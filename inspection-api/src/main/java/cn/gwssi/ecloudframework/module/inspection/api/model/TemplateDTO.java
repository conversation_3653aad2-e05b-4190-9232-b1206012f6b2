package cn.gwssi.ecloudframework.module.inspection.api.model;

import java.util.List;

public class TemplateDTO {
    /**
     * 主键
     */
    private String id;

    /**
     * 模板类型
     */
    private String category;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 基本信息
     */
    private String basicInfo;

    /**
     * 展示项
     */
    private String displayItem;

    /**
     * 是否展示结论，1：是，0：否
     */
    private Integer showResult;

    /**
     * 处置方式，手动转工单\自动转工单
     */
    private String dispose;

    /**
     * 是否支持上传，1：是，0：否
     */
    private Integer uploadFile;

    /**
     * 适用范围，通用\限定范围
     */
    private String scope;

    /**
     * 适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员
     */
    private String scopeObj;

    /**
     * 限定范围，从scopeObj里边提取出一些关键信息，方便根据查询一定范围内的模板。
     * [ { type:"role", value:"G_ROLE_ADMIN,GUEST" }, { type:"post", value:"UOMP_MANAGER,UOMP_TESTER" },
     * { type:"person", value:"914716947747700737,9147169477477007371" },
     * { type:"group", value:"914490040744148993,914490033109467137" } ]
     */
    private List<RangeDTO> scopeRange;


    /**
     * 关联的指标ID列表
     */
    private List<String> indicatorIds;

    private String createStartTime;

    private String createEndTime;

    private String createUser;

    private Integer state;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(String basicInfo) {
        this.basicInfo = basicInfo;
    }

    public String getDisplayItem() {
        return displayItem;
    }

    public void setDisplayItem(String displayItem) {
        this.displayItem = displayItem;
    }

    public Integer getShowResult() {
        return showResult;
    }

    public void setShowResult(Integer showResult) {
        this.showResult = showResult;
    }

    public String getDispose() {
        return dispose;
    }

    public void setDispose(String dispose) {
        this.dispose = dispose;
    }

    public Integer getUploadFile() {
        return uploadFile;
    }

    public void setUploadFile(Integer uploadFile) {
        this.uploadFile = uploadFile;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getScopeObj() {
        return scopeObj;
    }

    public void setScopeObj(String scopeObj) {
        this.scopeObj = scopeObj;
    }

    public List<String> getIndicatorIds() {
        return indicatorIds;
    }

    public void setIndicatorIds(List<String> indicatorIds) {
        this.indicatorIds = indicatorIds;
    }

    public List<RangeDTO> getScopeRange() {
        return scopeRange;
    }

    public void setScopeRange(List<RangeDTO> scopeRange) {
        this.scopeRange = scopeRange;
    }


    public String getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(String createStartTime) {
        this.createStartTime = createStartTime;
    }

    public String getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(String createEndTime) {
        this.createEndTime = createEndTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
