package cn.gwssi.ecloudframework.module.inspection.api.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PlanDTO extends BaseModel {
    /**
     * 主键
     */
    private String id;

    /**
     * 关联模板id
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 巡检类型，日巡检\周巡检\月巡检
     */
    private String category;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否节假日跳过，1：是，0：否
     */
    private Integer holidaySkip;

    /**
     * 任务接收人
     */
    private String taskReceiver;

    /**
     * 任务接收对象 1：人员；2：组织
     */
    private Integer taskRecevierObj;

    /**
     * 计划巡检时间
     */
    private String planTime;

    /**
     * 巡检状态
     */
    private String state;

    /**
     * 关联的巡检对象列表
     */
    private List<SubjectDTO> subjects;

    private Date startTimeBegin;

    private Date startTimeFinish;

    private Date endTimeBegin;

    private Date endTimeFinish;

    /**
     * 是否已发送过提醒,0:未发送 1:已发送
     */
    private String isSendNotice;
}
