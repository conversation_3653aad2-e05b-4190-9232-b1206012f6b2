package cn.gwssi.ecloudframework.module.inspection.api.model;

import lombok.Data;

import java.util.Date;

@Data
public class InspectionIndicatorDTO {

    /**
     * 主键
     */
    private String id;

    /**
     * 巡检指标
     */
    private String indicator;

    /**
     * 巡检方法
     */
    private String method;

    /**
     * 参考标准
     */
    private String standard;

    /**
     * 所属分类
     */
    private String category;

    /**
     * 结果种类，选项勾选\人工填写
     */
    private String resultType;

    /**
     * 启用状态，1：启用，0：停用
     */
    private Integer state;

    /**
     * 字典选择，是/否，正常/异常
     */
    private String dicType;

    private Date createTime;

    private String createUser;

    private String createBy;



    /**
     * 是否可编辑，非数据库字段
     */
    private String editable ;

    /**
     * 是否可删除，非数据库字段
     */
    private String deletable ;

    private String createStartTime;

    private String createEndTime;
}
