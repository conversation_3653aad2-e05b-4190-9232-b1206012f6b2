package cn.gwssi.ecloudframework.module.inspection.api.constant;

/**
 * 巡检模块常量类
 */
public interface InspectionConstants {
    /**
     * 巡检指标结果类型
     */
    enum IndicatorResultType {
        /**
         * 选项勾选
         */
        OPTION("选项勾选"),
        /**
         * 人工填写
         */
        MANUAL("人工填写");

        private final String text;

        IndicatorResultType(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 巡检指标状态
     */
    enum IndicatorState {
        /**
         * 启用
         */
        ENABLED(1),
        /**
         * 停用
         */
        DISABLED(0);

        private final int value;

        IndicatorState(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 巡检指标字典类型
     */
    enum IndicatorDicType {
        /**
         * 是/否
         */
        YES_NO("是/否"),
        /**
         * 正常/异常
         */
        NORMAL_ABNORMAL("正常/异常");

        private final String text;

        IndicatorDicType(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 巡检计划类型
     */
    enum PlanCategory {
        /**
         * 日巡检
         */
        DAILY("1"),
        /**
         * 周巡检
         */
        WEEKLY("2"),
        /**
         * 月巡检
         */
        MONTHLY("3");

        private final String text;

        PlanCategory(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 巡检计划状态
     */
    enum PlanState {
        /**
         * 暂存
         */
        DRAFT("1"),
        /**
         * 执行中
         */
        RUNNING("2"),
        /**
         * 已完成
         */
        FINISHED("3"),
        /**
         * 已终止
         */
        STOPPED("4");

        private final String text;

        PlanState(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 任务接收对象类型
     */
    enum TaskReceiverObjType {
        /**
         * 人员
         */
        PERSON(1),
        /**
         * 组织
         */
        ORG(2);

        private final int value;

        TaskReceiverObjType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 巡检任务状态
     */
    enum TaskState {
        /**
         * 待接收
         */
        PENDING("1"),
        /**
         * 处理中
         */
        PROCESSING("2"),
        /**
         * 已完成
         */
        COMPLETED("3");

        private final String text;

        TaskState(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 巡检任务类型
     */
    enum TaskType {
        /**
         * 计划中
         */
        PLANNED("1"),
        /**
         * 计划外
         */
        UNPLANNED("2");

        private final String text;

        TaskType(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 任务是否有效
     */
    enum TaskValid {
        /**
         * 有效
         */
        VALID(1),
        /**
         * 无效
         */
        INVALID(2);

        private final int value;

        TaskValid(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 巡检结论
     */
    enum InspectionResult {
        /**
         * 正常
         */
        NORMAL(1),
        /**
         * 异常
         */
        ABNORMAL(2);

        private final int text;

        InspectionResult(int text) {
            this.text = text;
        }

        public int getText() {
            return text;
        }
    }

    /**
     * 任务是否提交
     */
    enum TaskSubmit {
        /**
         * 有效
         */
        SAVE("1"),
        /**
         * 无效
         */
        SUBMIT("2");

        private final String value;

        TaskSubmit(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 模板状态
     */
    enum TemplateState {
        /**
         * 启用
         */
        ENABLED(1),
        /**
         * 停用
         */
        DISABLED(0);

        private final int value;

        TemplateState(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 模板处置方式
     */
    enum TemplateDispose {
        /**
         * 手动转工单
         */
        MANUAL("手动转工单"),
        /**
         * 自动转工单
         */
        AUTO("自动转工单");

        private final String text;

        TemplateDispose(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 模板适用范围
     */
    enum TemplateScope {
        /**
         * 通用
         */
        COMMON("通用"),
        /**
         * 限定范围
         */
        LIMITED("限定范围");

        private final String text;

        TemplateScope(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 模板适用范围对象
     */
    enum TemplateScopeObj {
        /**
         * 通用
         */
        ALL("all"),
        /**
         * 角色
         */
        ROLE("role"),
        /**
         * 岗位
         */
        POST("post"),
        /**
         * 组
         */
        GROUP("group"),
        /**
         * 人员
         */
        PERSON("person");

        private final String value;

        TemplateScopeObj(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
