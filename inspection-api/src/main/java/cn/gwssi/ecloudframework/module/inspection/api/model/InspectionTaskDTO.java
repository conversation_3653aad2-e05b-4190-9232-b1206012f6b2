package cn.gwssi.ecloudframework.module.inspection.api.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 巡检任务实体类
 */
@Data
public class InspectionTaskDTO extends BaseModel {

    /**
     * 查询类型，1为待处理，2为已完成
     */
    private String tab;

    /**
     * 主键
     */
    private String id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 计划id
     */
    private String planId;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 巡检类型
     */
    private String category;

    /**
     * 任务接收人
     */
    private String taskReceiver;

    /**
     * 计划巡检时间
     */
    private Date taskTime;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 巡检人
     */
    private String receiver;

    private String receiverName;

    /**
     * 巡检结论，正常\异常
     */
    private String result;

    /**
     * 完成状态，待接收\处理中\已完成
     */
    private String state;

    /**
     * 是否有效，1：是，2：否
     */
    private Integer enabled;

    /**
     * 任务类型，计划中\计划外
     */
    private String tType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 巡检对象，json
     */
    private String subjectJson;

    /**
     * 附件，json
     */
    private String files;

    /**
     * 工单id
     */
    private String orderId;

    /**
     * 实例id
     */
    private String instId;

    /**
     * 实例状态
     */
    private String instStatus;

    /**
     * 工单号
     */
    private String instOrderNo;

    private String taskTimeStart;
    private String taskTimeEnd;
    private Date planEndTime;
    //任务创建者或巡检人所在组织id，仅定时任务用
    private String orgId;
    //任务创建者或巡检人的名称，仅定时任务用
    private String orgUserName;

    /**
     * 以下为巡检记录查询使用
     */
    // 模板名称
    private String nameTemp;
    // 模板类型
    private String categoryTemp;
    private String finishTimeStart;
    private String finishTimeEnd;
    // 工单处理状态，1为待修复，2为已修复
    private String statusInst;
    // 关键字，当日巡检使用
    private String keyword;

    /**
     * 任务保存时使用，1为暂存，2为提交
     */
    private String isSubmit;

    /**
     * 关联的模板
     */
    private TemplateDTO template;

    /**
     * 关联的巡检单明细列表
     */
    private List<FormItemDTO> formItems;

    /**
     * 关联的工单
     */
    private List<TaskInstDTO> taskInsts;

    /**
     * 是否已发送过提醒,0:未发送 1:发送了未接收提醒 2:发送了录入提醒
     */
    private String isSendNotice;
}
