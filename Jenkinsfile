pipeline {
    agent {
      label 'compile-x86'
    }

    environment {
        _v = "v1.1.${BUILD_NUMBER}"
    }

    tools {
        maven 'apache-maven-3.5.4'
        jdk 'jdk1.8.0_201'
    }

    stages {
        stage('CheckOut') {
            steps {
                git branch: "${BRANCH_NAME}", credentialsId: 'git', url: "${GIT_URL}"
            }
        }

        stage('Deploy') {
            steps{
                sh """
                    echo ${_v}
                    mvn versions:set -DnewVersion=${_v}
                    mvn versions:commit
                    mvn clean install dependency:copy-dependencies -U -Pproduction -Dmaven.test.failure.ignore
                    mvn deploy
                """

            }
        }

        stage('Tag') {
            steps{
                 withCredentials([usernamePassword(credentialsId:"git",
                                          usernameVariable: "GIT_USERNAME",
                                          passwordVariable: "GIT_PASSWORD")]) {
                    sh """
                        git config --local credential.helper "!p() { echo username=\\$GIT_USERNAME; echo password=\\$GIT_PASSWORD; }; p"
                        git config --global user.name "liboyang"
                        git config --global user.email "<EMAIL>"
                        git tag -a -m '"Release ${_v}"' "${_v}"
                        git push origin "${_v}"
                    """
                }
            }
        }
    }
/*    post {
        always {
            script {
                sh 'set'
                def specificCause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                uid = (specificCause) ? "${specificCause.userId[0]}" : "system";
                _desc =  Jenkins.instance.getItemByFullName(env.JOB_NAME).getDescription()
            }
        }
        success {
            dingtalk (
                robot: 'dd5ad46e-ec02-490c-adfe-a928e8e2965e',
                type: 'ACTION_CARD',
                title: '你有新的消息，请注意查收',
                text: [
                    "![screenshot](http://code.e.cloud:9080/ecloud/ecloud-framework/uploads/0b8661b1f99f0918d0b71e64ec5132db/ci.jpeg)",
                    "### ${GITLAB_PROJECT_DESCRIPTION}发布${_v}版本！",
                    "> 此次更新包含${GITLAB_COMMIT_COUNT}个commit",
                    "* 模块名称：${GITLAB_PROJECT_NAME}",
                    "* 分支：${GIT_BRANCH}",
                    "* 流水线编号：${BUILD_NUMBER}",
                    "* [构建日志](${BUILD_URL})",
                    "* [GitLab](${GITLAB_PROJECT_GIT_HTTP_URL})"
                ]
            )
        }
        failure {
            dingtalk (
                    robot: 'dd5ad46e-ec02-490c-adfe-a928e8e2965e',
                    type: 'MARKDOWN',
                    title: "${GITLAB_PROJECT_DESCRIPTION}构建失败",
                    text: [
                            "### ${GITLAB_PROJECT_DESCRIPTION} ${_v}版本部署失败",
                            "* 失败阶段：${STAGE_NAME}",
                            "* [详细信息](${BUILD_URL})"
                    ]
            )
        }
    }*/
}
