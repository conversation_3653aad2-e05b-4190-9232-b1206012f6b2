pipeline {

    agent {
      label 'compile-x86'
    }

    environment {
        repo = "registry.e.cloud:8082"

        tag = "${BRANCH_NAME}.${BUILD_NUMBER}"
        imageName = "ecloud/newgwuomp-backend:${tag}"
    }

    tools {
        maven 'apache-maven-3.5.4'
        jdk 'jdk1.8.0_201'
    }

    stages {
        stage('CheckOut') {
            steps {
                script {
                  git branch: "${BRANCH_NAME}", credentialsId: 'git', url: 'http://code.e.cloud:9080/ecloud/newgwuomp-backend.git'
                }
            }
        }
        stage('Compile') {
            steps {
                sh 'mvn clean package -U -Dmaven.test.failure.ignore'
            }
        }
        stage('Image') {
            steps{
                script {
                    docker.withRegistry("http://${repo}","registry-hub-credentials") {
                        docker.build("${imageName}",'-f Dockerfile .').push()
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'develop'){
                        sh "/home/<USER>/agent/tools/k --kubeconfig /home/<USER>/agent/tools/.conf set image -n newgwuomp-dev deployment/newgwuomp-backend newgwuomp-backend=${repo}/${imageName}"
                    }else if (env.BRANCH_NAME == 'master'){
                        sh "/home/<USER>/agent/tools/k --kubeconfig /home/<USER>/agent/tools/.conf set image -n newgwuomp-sit deployment/newgwuomp-backend newgwuomp-backend=${repo}/${imageName}"
                    }

                }
            }
        }
    }
}
