pipeline {
    agent {
      label 'compile-x86'
    }
   
    environment {
        _v = "v1.3.${BUILD_NUMBER}"
    }

    tools {
        maven 'apache-maven-3.5.4'
        jdk 'jdk1.8.0_201'
    }

    stages {
        stage('CheckOut') {
            steps {
                git branch: "${BRANCH_NAME}", credentialsId: 'git', url: "${GIT_URL}"
            }
        }

        stage('Deploy') {
            steps{
                sh """
                    echo ${_v}
                    mvn versions:set -DnewVersion=${_v}
                    mvn versions:commit
                    mvn clean install dependency:copy-dependencies -U -Pproduction -Dmaven.test.failure.ignore
                    mvn deploy
                """
                
            }
        }
        
        stage('Tag') {
            steps{
                 withCredentials([usernamePassword(credentialsId:"git",
                                          usernameVariable: "GIT_USERNAME", 
                                          passwordVariable: "GIT_PASSWORD")]) {
                    sh """
                        git config --local credential.helper "!p() { echo username=\\$GIT_USERNAME; echo password=\\$GIT_PASSWORD; }; p"
                        git config --global user.name "liboyang"
                        git config --global user.email "<EMAIL>"
                        git tag -a -m '"Release ${_v}"' "${_v}"
                        git push origin "${_v}"
                    """
                }
            }
        }
    }
}