<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskInstDao">
    
    <select id="get" resultType="java.lang.String">
        select id from i_task_inst
        WHERE task_id = #{taskId}
            and inst_id = #{instId}
    </select>
    
    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.TaskInst">
        INSERT INTO i_task_inst
        (id, task_id, inst_id, order_no, status, create_user, create_by, create_time, update_user, update_by, update_time)
        VALUES
        (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{instId,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.TaskInst">
        UPDATE i_task_inst
        <set>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE task_id = #{taskId}
            and inst_id = #{instId}
    </update>
</mapper>
