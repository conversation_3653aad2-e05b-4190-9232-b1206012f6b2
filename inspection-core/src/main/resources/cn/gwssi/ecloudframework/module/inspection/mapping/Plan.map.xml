<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionPlanDao">
    <resultMap id="Plan" type="cn.gwssi.ecloudframework.module.inspection.core.model.Plan">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="templateName" column="template_name" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="holidaySkip" column="holiday_skip" jdbcType="INTEGER"/>
        <result property="taskReceiver" column="task_receiver" jdbcType="VARCHAR"/>
        <result property="taskRecevierObj" column="task_recevier_obj" jdbcType="INTEGER"/>
        <result property="planTime" column="plan_time" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isSendNotice" column="is_send_notice" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.Plan">
        INSERT INTO i_plan
        (id, template_id, name,  description, category, start_time, end_time, holiday_skip, task_receiver, task_recevier_obj, plan_time, state,
        create_user, create_by, create_time, update_user, update_by, update_time, is_send_notice)
        VALUES
        (#{id,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{ description,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{holidaySkip,jdbcType=INTEGER},
        #{taskReceiver,jdbcType=VARCHAR}, #{taskRecevierObj,jdbcType=INTEGER}, #{planTime,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isSendNotice,jdbcType=VARCHAR})
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.Plan">
        UPDATE i_plan
        <set>
            <if test="templateId != null and templateId != ''">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test=" description != null and  description != ''">
                 description = #{ description,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != ''">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="holidaySkip != null">
                holiday_skip = #{holidaySkip,jdbcType=INTEGER},
            </if>
            <if test="taskReceiver != null and taskReceiver != ''">
                task_receiver = #{taskReceiver,jdbcType=VARCHAR},
            </if>
            <if test="taskRecevierObj != null">
                task_recevier_obj = #{taskRecevierObj,jdbcType=INTEGER},
            </if>
            <if test="planTime != null and planTime != ''">
                plan_time = #{planTime,jdbcType=VARCHAR},
            </if>
            <if test="state != null and state != ''">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isSendNotice != null and isSendNotice != ''">
                is_send_notice = #{isSendNotice,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM i_plan WHERE id = #{id}
    </delete>

    <select id="get" parameterType="java.lang.String" resultMap="Plan">
        SELECT p.*, t.name as template_name FROM i_plan p
            left join i_template t on p.template_id = t.id
                 WHERE p.id = #{id}
    </select>

    <select id="list" parameterType="cn.gwssi.ecloudframework.module.inspection.api.model.PlanDTO" resultMap="Plan">
        SELECT p.*, t.name as template_name FROM i_plan p
        left join i_template t on p.template_id = t.id
        <where>
            <if test="name != null and name != ''">
                AND p.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="description != null and description != ''">
                AND p.description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="category != null and category != ''">
                AND p.category = #{category}
            </if>
            <if test="templateName != null and templateName != ''">
                AND t.name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="state != null and state != ''">
                AND p.state = #{state}
            </if>
            <if test="startTimeBegin != null">
                and p.start_time <![CDATA[ >= ]]> #{startTimeBegin}
            </if>
            <if test="startTimeFinish != null">
                and p.start_time <![CDATA[ <= ]]> #{startTimeFinish}
            </if>
            <if test="endTimeBegin != null">
                and p.end_time <![CDATA[ >= ]]> #{endTimeBegin}
            </if>
            <if test="endTimeFinish != null">
                and p.end_time <![CDATA[ <= ]]> #{endTimeFinish}
            </if>
            <if test="createBy != null and createBy != ''">
                AND p.create_by = #{createBy}
            </if>
            <if test="isSendNotice != null and isSendNotice != ''">
                AND p.is_send_notice = #{isSendNotice}
            </if>
        </where>
        ORDER BY p.update_time DESC
    </select>

    <select id="getPlansByTemplateId" parameterType="java.lang.String" resultMap="Plan">
        SELECT p.* FROM i_plan p
        WHERE p.template_id = #{templateId}
        ORDER BY p.update_time DESC
    </select>
</mapper>
