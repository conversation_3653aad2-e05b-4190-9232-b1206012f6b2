<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionIndicatorDao">
    <resultMap id="Indicator" type="cn.gwssi.ecloudframework.module.inspection.core.model.InspectionIndicator">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="indicator" column="indicator" jdbcType="VARCHAR"/>
        <result property="method" column="method" jdbcType="VARCHAR"/>
        <result property="standard" column="standard" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="resultType" column="result_type" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="dicType" column="dic_type" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="editable" column="editable" jdbcType="BOOLEAN"/>
        <result property="deletable" column="deletable" jdbcType="BOOLEAN"/>
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.InspectionIndicator">
        INSERT INTO i_indicator
        (id, indicator, method, standard, category, result_type, state, dic_type, create_user, create_by, create_time, update_user, update_by, update_time)
        VALUES
        (#{id,jdbcType=VARCHAR}, #{indicator,jdbcType=VARCHAR}, #{method,jdbcType=VARCHAR}, #{standard,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR}, #{resultType,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{dicType,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.InspectionIndicator">
        UPDATE i_indicator
        <set>
            <if test="indicator != null and indicator != ''">
                indicator = #{indicator,jdbcType=VARCHAR},
            </if>
            <if test="method != null and method != ''">
                method = #{method,jdbcType=VARCHAR},
            </if>
            <if test="standard != null and standard != ''">
                standard = #{standard,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != ''">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="resultType != null and resultType != ''">
                result_type = #{resultType,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
            <if test="dicType != null and dicType != ''">
                dic_type = #{dicType,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM i_indicator WHERE id = #{id}
    </delete>

    <select id="get" parameterType="java.lang.String" resultMap="Indicator">
        SELECT * FROM i_indicator WHERE id = #{id}
    </select>

    <select id="getIndicatorListWithStatus" parameterType="java.util.Map" resultMap="Indicator">
        SELECT
        i.*,
        /* 可删除状态: 没有被模板引用的可以删除 */
        CASE WHEN (SELECT COUNT(1) FROM i_template_indicator_relation tir WHERE tir.indicator_id = i.id) = 0
        THEN 1 ELSE 0
        END AS deletable,
        /* 可编辑状态: 没有录入结果的可以编辑 */
        CASE WHEN (SELECT COUNT(1) FROM i_form_item ift WHERE ift.indicator_id = i.id) = 0 THEN 1 else 0

        END AS editable
        FROM
        i_indicator i
        <where>
            <if test="indicator != null and indicator != ''" >
                AND i.indicator LIKE CONCAT('%', #{indicator}, '%')
            </if>
            <if test="category != null and category != ''">
                AND i.category = #{category}
            </if>
            <if test="state != null ">
                AND i.state = #{state}
            </if>
            <if test="standard != null and standard != '' ">
                AND i.standard LIKE CONCAT('%', #{standard}, '%')
            </if>
            <if test="method != null and method != '' ">
                AND i.method LIKE CONCAT('%', #{method}, '%')
            </if>

            <if test="createStartTime != null and createStartTime != ''">
                and i.create_time <![CDATA[ >= ]]> #{createStartTime}
            </if>
            <if test="createEndTime != null and createEndTime != ''">
                and i.create_time <![CDATA[ <= ]]> #{createEndTime}
            </if>

        </where>
        ORDER BY i.create_time DESC
    </select>

    <select id="getIndicatorsByTemplateId" parameterType="java.lang.String" resultMap="Indicator">
        SELECT
            i.*,
            /* 可删除状态: 没有被模板引用的可以删除 */
            CASE WHEN (SELECT COUNT(1) FROM i_template_indicator_relation tir WHERE tir.indicator_id = i.id) = 0
                 THEN 1 ELSE 0
            END AS deletable,
            /* 可编辑状态: 没有被模板引用的可以编辑，或者被模板引用但模板没有被任务引用的可以编辑 */
            CASE WHEN (SELECT COUNT(1) FROM i_template_indicator_relation tir WHERE tir.indicator_id = i.id) = 0 THEN 1
                 WHEN (SELECT COUNT(1)
                       FROM i_template_indicator_relation tir
                       JOIN i_template t ON tir.template_id = t.id
                       JOIN i_task task ON t.id = task.template_id
                       WHERE tir.indicator_id = i.id) = 0 THEN 1
                 ELSE 0
            END AS editable
        FROM
            i_indicator i
        JOIN i_template_indicator_relation tir ON i.id = tir.indicator_id
        WHERE
            tir.template_id = #{templateId}
        ORDER BY i.create_time DESC
    </select>
    
    <select id="getIndicatorListByTemplateId" resultMap="Indicator">
        select i.id FROM i_indicator i
        left join i_template_indicator_relation r on i.id = r.indicator_id
        where r.template_id = #{templateId}
    </select>
</mapper>
