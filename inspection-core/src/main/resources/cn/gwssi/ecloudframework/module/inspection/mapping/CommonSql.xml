<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inspectCommonSql">

    <!-- 通用sql映射 -->
    <sql id="sysdate_to_char">
        to_char(sysdate(), 'yyyy-MM-dd')
    </sql>
    <sql id="sysdate_to_char" databaseId="mysql">
        date_format(sysdate(),'%Y-%m-%d')
    </sql>
    
    <sql id="group_concat">
        wm_concat(
    </sql>
    <sql id="group_concat" databaseId="mysql">
        group_concat(
    </sql>

</mapper>
