<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTemplateDao">
    <resultMap id="Template" type="cn.gwssi.ecloudframework.module.inspection.core.model.Template">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="basicInfo" column="basic_info" jdbcType="VARCHAR"/>
        <result property="displayItem" column="display_item" jdbcType="VARCHAR"/>
        <result property="showResult" column="show_result" jdbcType="INTEGER"/>
        <result property="dispose" column="dispose" jdbcType="VARCHAR"/>
        <result property="uploadFile" column="upload_file" jdbcType="INTEGER"/>
        <result property="scope" column="scope" jdbcType="VARCHAR"/>
        <result property="scopeObj" column="scope_obj" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="showDeleteButton" column="show_delete_button" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.Template">
        INSERT INTO i_template
        (id, category, name, basic_info, display_item, show_result, dispose, upload_file, scope, scope_obj, state,
        create_user, create_by, create_time, update_user, update_by, update_time)
        VALUES
        (#{id,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{basicInfo,jdbcType=VARCHAR},
        #{displayItem,jdbcType=VARCHAR}, #{showResult,jdbcType=INTEGER}, #{dispose,jdbcType=VARCHAR}, #{uploadFile,jdbcType=INTEGER},
        #{scope,jdbcType=VARCHAR}, #{scopeObj,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER},
        #{createUser,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.inspection.core.model.Template">
        UPDATE i_template
        <set>
            <if test="category != null and category != ''">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="basicInfo != null and basicInfo != ''">
                basic_info = #{basicInfo,jdbcType=VARCHAR},
            </if>
            <if test="displayItem != null and displayItem != ''">
                display_item = #{displayItem,jdbcType=VARCHAR},
            </if>
            <if test="showResult != null">
                show_result = #{showResult,jdbcType=INTEGER},
            </if>
            <if test="dispose != null and dispose != ''">
                dispose = #{dispose,jdbcType=VARCHAR},
            </if>
            <if test="uploadFile != null">
                upload_file = #{uploadFile,jdbcType=INTEGER},
            </if>
            <if test="scope != null and scope != ''">
                scope = #{scope,jdbcType=VARCHAR},
            </if>
            <if test="scopeObj != null and scopeObj != ''">
                scope_obj = #{scopeObj,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM i_template WHERE id = #{id}
    </delete>

    <select id="get" parameterType="java.lang.String" resultMap="Template">
        SELECT * FROM i_template WHERE id = #{id}
    </select>

    <select id="list" resultMap="Template">
        SELECT DISTINCT t.*,
        CASE
            WHEN EXISTS (
                SELECT 1 FROM i_plan p
                WHERE t.id = p.template_id
            ) THEN 0
            ELSE 1
        END AS show_delete_button
        FROM i_template t
        JOIN i_template_auth p ON t.id = p.template_id
        <where>
            <if test="template.name != null and template.name != ''">
                AND t.name LIKE CONCAT('%', #{template.name}, '%')
            </if>
            <if test="template.category != null and template.category != ''">
                AND t.category = #{template.category}
            </if>
            <if test="template.createUser != null and template.createUser !=''">
                AND t.create_user LIKE CONCAT('%', #{template.createUser}, '%')
            </if>

            <if test="template.state != null">
                AND t.state = #{template.state}
            </if>
            <if test="template.createStartTime != null and template.createStartTime != ''">
                and t.create_time <![CDATA[ >= ]]> #{template.createStartTime}
            </if>
            <if test="template.createEndTime != null and template.createEndTime != ''">
                and t.create_time <![CDATA[ <= ]]> #{template.createEndTime}
            </if>
            AND
            (
                p.scope_obj = 'all' OR
                (p.scope_obj = 'person' AND p.scope_id = #{userId})
                <if test = "roleIds != null and roleIds.size() > 0">
                  OR (p.scope_obj = 'role' and p.scope_id IN
                  <foreach collection="roleIds" item = "roleId" open="(" separator="," close=")">
                      #{roleId}
                  </foreach>
                  )
                </if>
                <if test = "positionIds != null and positionIds.size() > 0">
                    OR (p.scope_obj = 'post' and p.scope_id IN
                    <foreach collection="positionIds" item = "positionId" open="(" separator="," close=")">
                        #{positionId}
                    </foreach>
                    )
                </if>
                <if test = "groupIds != null and groupIds.size() > 0">
                    OR (
                        <foreach collection="groupIds" item = "groupId"  separator="OR" open="" close="">
                            p.scope_obj = 'group' and p.scope_id LIKE CONCAT(#{groupId}, '%')
                        </foreach>
                    )
                </if>
            )
        </where>
        ORDER BY t.create_time DESC
    </select>

    <select id="getTemplatesByIndicatorId" parameterType="java.lang.String" resultMap="Template">
        SELECT t.* FROM i_template t
        JOIN i_template_indicator_relation tir ON t.id = tir.template_id
        WHERE tir.indicator_id = #{indicatorId}
        ORDER BY t.create_time DESC
    </select>
</mapper>
