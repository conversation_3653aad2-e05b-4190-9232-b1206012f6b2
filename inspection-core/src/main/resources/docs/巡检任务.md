# 巡检任务接口文档

## 获取巡检任务列表

**URL:** `/module/inspection/task/list`

**Method:** POST

**描述:** 根据查询条件获取巡检任务列表

**请求参数:**

| 参数名           | 类型 | 必填 | 描述             |
|---------------| ---- | ---- |----------------|
| tab           | String | 否 | 页签，待处理为1，已完成为2 |
| name          | String | 否 | 任务名称（支持模糊查询）   |
| state         | String | 否 | 完成状态           |
| receiver      | String | 否 | 巡检人            |
| taskTimeStart | String | 否 | 巡检计划时间（开始）     |
| taskTimeEnd   | String | 否 | 巡检计划时间（结束）     |
| pageNo        | Integer | 否 | 页码             |
| pageSize      | Integer | 否 | 每页大小           |

**统计页面，图表联动的参数：**

| 参数名           | 类型 | 必填 | 描述                 |
|---------------| ---- | ---- |--------------------|
| state           | String | 否 | 任务完成：未完成 1,2 已完成 3 |
| statusInst          | String | 否 | 故障修复：未修复1，已修复2     |
| categoryTemp         | String | 否 | 模板类型               |

**返回结果:**

```json
{
  "rows": [
    {
      "id": "任务ID",
      "name": "任务名称",
      "planId": "计划ID",
      "templateId": "模板ID",
      "category": "巡检类型",
      "taskReceiver": "任务接收人",
      "taskTime": "2023-01-01 00:00:00",
      "receiveTime": "2023-01-01 01:00:00",
      "finishTime": "2023-01-01 02:00:00",
      "receiver": "巡检人",
      "receiverName": "巡检人姓名",
      "result": "巡检结论",
      "state": "完成状态",
      "enabled": 1,
      "tType": "任务类型",
      "comment": "备注",
      "subjectJson": "巡检对象JSON",
      "instOrderNo": "工单号",
      "instId": "实例ID",
      "taskInsts": [{
        "instId": "实例ID",
        "orderNo": "工单编号",
        "status": "实例状态"
      }]
    }
  ],
  "total": 1
}
```

## 获取所有巡检任务

**URL:** `/module/inspection/task/listAll`

**Method:** POST

**描述:** 根据查询条件获取巡检任务列表

**请求参数:**

| 参数名           | 类型 | 必填 | 描述           |
|---------------| ---- |----|--------------|
| planId        | String | 是  | 计划ID         |
| pageNo        | Integer | 否  | 页码           |
| pageSize      | Integer | 否  | 每页大小         |

**返回结果:**

```json
{
  "rows": [
    {
      "id": "任务ID",
      "name": "任务名称",
      "planId": "计划ID",
      "templateId": "模板ID",
      "category": "巡检类型",
      "taskReceiver": "任务接收人",
      "taskTime": "2023-01-01 00:00:00",
      "receiveTime": "2023-01-01 01:00:00",
      "finishTime": "2023-01-01 02:00:00",
      "receiver": "巡检人",
      "receiverName": "巡检人姓名",
      "result": "巡检结论",
      "state": "完成状态",
      "enabled": 1,
      "tType": "任务类型",
      "comment": "备注",
      "subjectJson": "巡检对象JSON",
      "instOrderNo": "工单号",
      "instId": "实例ID",
      "taskInsts": [{
        "instId": "实例ID",
        "orderNo": "工单编号",
        "status": "实例状态"
      }]
    }
  ],
  "total": 1
}
```

## 获取巡检任务详情

**URL:** `/module/inspection/task/get`

**Method:** GET

**描述:** 根据ID获取巡检任务详情及关联的巡检单明细

**请求参数:**

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 是 | 巡检任务ID |

**返回结果:**

```json
{
  "result": {
    "id": "任务ID",
    "name": "任务名称",
    "planId": "计划ID",
    "templateId": "模板ID",
    "category": "巡检类型",
    "taskReceiver": "任务接收人",
    "taskTime": "2023-01-01 00:00:00",
    "receiveTime": "2023-01-01 01:00:00",
    "finishTime": "2023-01-01 02:00:00",
    "receiver": "巡检人",
    "result": "巡检结论",
    "state": "完成状态",
    "enabled": 1,
    "tType": "任务类型",
    "comment": "备注",
    "subjectJson": "巡检对象JSON",
    "orderId": "工单ID",
    "instId": "实例ID",
    "formItems": [
      {
        "id": "巡检单明细ID1",
        "taskId": "任务ID",
        "indicatorId": "指标ID1",
        "displayItem": "展示项1",
        "subjectName": "巡检对象名称1",
        "result": "结果1",
        "comment": "备注1",
        "indicator": {
          "indicator": "巡检指标",
          "method": "巡检方法",
          "standard": "参考标准",
          "category": "所属分类"
        }
      },
      {
        "id": "巡检单明细ID2",
        "taskId": "任务ID",
        "indicatorId": "指标ID2",
        "displayItem": "展示项2",
        "subjectName": "巡检对象名称2",
        "result": "结果2",
        "comment": "备注2",
        "indicator": {
          "indicator": "巡检指标",
          "method": "巡检方法",
          "standard": "参考标准",
          "category": "所属分类"
        }
      }
    ]
  },
  "code": 0,
  "message": "操作成功"
}
```

## 巡检结果录入

**URL:** `/module/inspection/task/save`

**Method:** POST

**描述:** 巡检结果录入

**请求参数:**

```json
{
  "id": "任务ID",
  "result": "巡检结论",
  "comment": "备注",
  "isSubmit": "1为暂存，2为提交",
  "formItems": [
    {
      "id": "id1",
      "result": "结果1",
      "comment": "备注1"
    },
    {
      "id": "id2",
      "result": "结果2",
      "comment": "备注2"
    }
  ]
}
```

## 计划外巡检

**URL:** `/module/inspection/task/saveTask`

**Method:** POST

**描述:** 计划外巡检

**请求参数:**

```json
{
  "name": "任务名称",
  "taskTime": "计划巡检时间",
  "templateId": "巡检模板id",
  "subjectJson": "巡检对象"
}
```

**返回结果:**

```json
{
  "result": "创建成功",
  "code": 0,
  "message": "操作成功"
}
```

## 获取巡检人

**URL:** `/module/inspection/task/getInspectors`

**Method:** GET

**描述:** 获取可选的巡检人列表

**请求参数:** 无

**返回结果:**

```json
{
  "rows": [
    {
      "id": "用户ID1",
      "fullname": "用户姓名1"
    },
    {
      "id": "用户ID2",
      "fullname": "用户姓名2"
    }
  ],
  "code": 0,
  "message": "操作成功"
}
```

## 接收巡检任务

**URL:** `/module/inspection/task/receive`

**Method:** POST

**描述:** 接收指定的巡检任务

**请求参数:**

| 参数名 | 类型 | 必填 | 描述            |
| ------ | ---- | ---- |---------------|
| id | String | 是 | 巡检任务ID |
| receiver | String | 是 | 接收人           |

**返回结果:**

```json
{
  "result": "接收成功",
  "code": 0,
  "message": "操作成功"
}
```

## 设置任务无效

**URL:** `/module/inspection/task/setInvalid`

**Method:** POST

**描述:** 设置指定的巡检任务为无效

**请求参数:**

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 是 | 巡检任务ID，多个用逗号分隔 |

**返回结果:**

```json
{
  "result": "设置成功",
  "code": 0,
  "message": "操作成功"
}
```

## 指定巡检人

**URL:** `/module/inspection/task/assignInspector`

**Method:** POST

**描述:** 指定巡检任务的巡检人

**请求参数:**

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| id | String | 是 | 巡检任务ID，多个用逗号分隔 |
| receiver | String | 是 | 巡检人 |

**返回结果:**

```json
{
  "result": "指定成功",
  "code": 0,
  "message": "操作成功"
}
```

## 获取巡检统计数据

**URL:** `/module/inspection/task/statistics`

**Method:** POST

**描述:** 获取巡检任务的统计数据

**请求参数:** body 形式传参

| 参数名           | 类型       | 必填 | 描述           |
|---------------|----------|----|--------------|
| keyword       | String | 否  | 关键字          |
| taskTimeStart | String   | 否  | 计划巡检时间，开始    |
| taskTimeEnd   | String   | 否  | 计划巡检时间，结束    |
| result         | String   | 否  | 巡检结论，字典值     |

**返回结果:**

```txt
{
  "result": {
    "indicator_inst": {
      "2025-05-01": 10
    }, // 故障及修复情况
    "task_completed": 20, // 巡检完成数量
    "task_uncompleted": 30, // 巡检未完成数量
    "inst_completed": 40, // 故障修复数量
    "inst_uncompleted": 50, // 故障未修复数量
    "inst_template_category": {
        "应用巡检": 10
    } // 故障分布
  },
  "code": 0,
  "message": "操作成功"
}
```

## 当日巡检

**URL:** `/module/inspection/task/today/statistics`

**Method:** GET

**描述:** 获取当日巡检的统计数据

**请求参数:** 无

**返回结果:**

```txt
{
  "data": {
    "err_today": 20,//故障-总量
    "err_increase": "12.5%",//故障-较昨日
    "task_unfinished": 9,//任务-未完成
    "task_pending": 4,//任务-待接收
    "task_processing": 5,//任务-处理中
    "inst_processing": 2,//故障-待处理
    "inst_processing_level_1": 1,//故障-待处理-一级
    "inst_processing_level_2": 0,//故障-待处理-二级
    "inst_processing_level_3": 1,//故障-待处理-三级
    "inst_completed": 1,//故障-已处理
    "inst_completed_per": "4%",//故障-已处理-百分比
    "inst_repeated": 1,//故障-重复性
    "inst_repeated_count": 3,//故障-重复次数
  },
  "code": 200,
  "isOk": true  
}
```

## 当日巡检任务

**URL:** `/module/inspection/task/today/list`

**Method:** GET

**描述:** 获取当日巡检任务

**请求参数:** 

| 参数名   | 类型 | 必填 | 描述                 |
|-------| ---- |----|--------------------|
| state  | String | 是  | 状态，1为待接收，2为处理中，空为全部 |
| pageSize | String | 是  | 分页条件，每页大小          |
| pageNo  | String | 是  | 分页条件，当前页码          |
| orderBy | String | 是  | 排序条件，默认传 endTime   |
| sort  | String | 是  | 排序条件，默认传 asc       |


**返回结果:**

```json
{
  "rows": [
    {
      "id": "id",
      "name": "标题",
      "endTime": "计划完成时间",
      "receiverName": "负责人",
      "tel": "联系电话"
    }
  ],
  "total": 1
}
```

## 当日巡检事件工单

**URL:** `/module/inspection/task/inst/list`

**Method:** GET

**描述:** 获取当日巡检事件工单

**请求参数:**  

| 参数名   | 类型 | 必填 | 描述                  |
|-------| ---- |----|---------------------|
| date  | String | 否  | 日期，空为全部             |
| pageSize | String | 是  | 分页条件，每页大小           |
| pageNo  | String | 是  | 分页条件，当前页码           |
| orderBy | String | 是  | 排序条件，默认传 createTime |
| sort  | String | 是  | 排序条件，默认传 desc       |

**返回结果:**

```json
{
  "rows": [
    {
      "instId": "实例id",
      "title": "标题",
      "level": "工单级别，对应字典",
      "assignee": "当前处理人",
      "tel": "联系电话",
      "status": "工单状态，对应字典",
      "createTime": "创建时间",
      "duration": "处理时长"
    }
  ],
  "total": 1
}
```
## 当日巡检事件工单详情

**URL:** `/module/inspection/task/inst/detail`

**Method:** GET

**描述:** 获取当日巡检事件工单

**请求参数:**

| 参数名      | 类型 | 必填 | 描述                  |
|----------| ---- |----|---------------------|
| instId   | String | 是  | 实例id                |

**返回结果:**

```json
{
  "rows": [
    {
      "title": "标题",
      "orderNo": "工单编号",
      "applicant": "请求人",
      "tel": "联系电话",
      "orderDesc": "描述"
    }
  ],
  "total": 1
}
```