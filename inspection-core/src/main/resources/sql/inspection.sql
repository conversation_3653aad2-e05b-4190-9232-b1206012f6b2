drop table if exists i_form_item;

drop table if exists i_indicator;

drop table if exists i_plan;

drop table if exists i_subject;

drop table if exists i_task;

drop table if exists i_template;

drop table if exists i_template_auth;

drop table if exists i_template_indicator_relation;

/*==============================================================*/
/* Table: i_form_item                                           */
/*==============================================================*/
create table i_form_item
(
   id                   varchar(64) not null,
   task_id              varchar(64),
   indicator_id         varchar(64),
   display_item         varchar(64),
   subject_name         varchar(64),
   result               varchar(64),
   comment              varchar(1000),
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_form_item comment '巡检单明细表';

/*==============================================================*/
/* Table: i_indicator                                           */
/*==============================================================*/
create table i_indicator
(
   id                   varchar(64) not null comment '主键',
   indicator            varchar(500) comment '巡检指标',
   method               varchar(500) comment '巡检方法',
   standard             varchar(500) comment '参考标准',
   category             varchar(64) comment '所属分类',
   result_type          varchar(64) comment '结果种类，选项勾选\人工填写',
   state                int comment '启用状态，1：启用，0：停用',
   dic_type             varchar(64) comment '字典选择，是/否，正常/异常',
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_indicator comment '巡检指标表';

/*==============================================================*/
/* Table: i_plan                                                */
/*==============================================================*/
create table i_plan
(
   id                   varchar(64) not null comment '主键',
   template_id          varchar(64) comment '关联模板id',
   name                 varchar(255) comment '计划名称',
   description          text comment '描述',
   category             varchar(64) comment '巡检类型，日巡检\周巡检\月巡检',
   start_time           timestamp comment '开始时间',
   end_time             timestamp comment '结束时间',
   holiday_skip         int comment '是否节假日跳过，1：是，0：否',
   task_receiver        text comment '任务接收人',
   task_recevier_obj    int comment '任务接收对象 0：人员；1：组织',
   plan_time            varchar(500) comment '计划巡检时间',
   state                varchar(64) comment '巡检状态',
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_plan comment '巡检计划表';

/*==============================================================*/
/* Table: i_subject                                             */
/*==============================================================*/
create table i_subject
(
   id                   varchar(64) not null,
   subject_type         varchar(255),
   subject_name         varchar(255),
   plan_id              varchar(64),
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_subject comment '巡检对象表（计划中）';

/*==============================================================*/
/* Table: i_task                                                */
/*==============================================================*/
create table i_task
(
   id                   varchar(64) not null comment '主键',
   name                 varchar(255) comment '任务名称',
   plan_id              varchar(64) comment '计划id',
   template_id          varchar(64) comment '模板id',
   category             varchar(64) comment '巡检类型',
   task_receiver        varchar(64) comment '任务接收的组织机构',
   task_time            timestamp comment '计划巡检时间',
   receive_time         timestamp comment '接收时间',
   finish_time          timestamp comment '完成时间',
   receiver             varchar(64) comment '巡检人',
   receiverName         varchar(64) comment '巡检人姓名',
   result               varchar(64) comment '巡检结论，正常\异常',
   state                varchar(64) comment '完成状态，待接收\处理中\已完成',
   enabled              int comment '是否有效，1：是，2：否',
   t_type               varchar(64) comment '任务类型，计划中\计划外',
   comment              varchar(500) comment '备注',
   subject_json         longtext comment '巡检对象，json',
   files                longtext comment '附件，json',
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_task comment '巡检任务表';

/*==============================================================*/
/* Table: i_template                                            */
/*==============================================================*/
create table i_template
(
   id                   varchar(64) not null comment '主键',
   category             varchar(64) comment '模板类型',
   name                 varchar(64) comment '模板名称',
   basic_info           varchar(64) comment '基本信息',
   display_item         varchar(64) comment '展示项',
   show_result          int comment '是否展示结论，1：是，0：否',
   dispose              varchar(64) comment '处置方式，手动转工单\自动转工单',
   upload_file          int comment '是否支持上传，1：是，0：否',
   scope                varchar(2048) comment '适用范围，通用\限定范围',
   scope_obj            text comment '适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员',
   state                int comment '状态，启用\停用',
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_template comment '模板表';

/*==============================================================*/
/* Table: i_template_auth                                       */
/*==============================================================*/
create table i_template_auth
(
   id                   varchar(64) primary key not null comment '主键',
   template_id          varchar(64),
   scope_obj            varchar(64) comment '适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员',
   scope_id             varchar(64),
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间'
);

alter table i_template_auth comment '模板权限';

/*==============================================================*/
/* Table: i_template_indicator_relation                         */
/*==============================================================*/
create table i_template_indicator_relation
(
   id                   varchar(64) not null,
   template_id          varchar(64),
   indicator_id         varchar(64),
   create_user          varchar(256) comment '创建人',
   create_by            varchar(64) comment '创建人ID',
   create_time          timestamp comment '创建时间',
   update_user          varchar(256) comment '修改人',
   update_by            varchar(64) comment '修改人ID',
   update_time          timestamp comment '修改时间',
   primary key (id)
);

alter table i_template_indicator_relation comment '模板与指标关系表';

/*==============================================================*/
/* Table: i_task_inst                                           */
/*==============================================================*/
create table i_task_inst
(
    id                   varchar(64) not null,
    task_id              varchar(64),
    inst_id              varchar(64),
    order_no             varchar(64) comment '工单号',
    status               varchar(64) comment '实例状态',
    create_user          varchar(256) comment '创建人',
    create_by            varchar(64) comment '创建人ID',
    create_time          timestamp comment '创建时间',
    update_user          varchar(256) comment '修改人',
    update_by            varchar(64) comment '修改人ID',
    update_time          timestamp comment '修改时间',
    primary key (id)
);

alter table i_template_indicator_relation comment '任务与实例关系表';