package cn.gwssi.ecloudframework.module.inspection.core.schedule;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@JobHandler(value = "finishedPlanJob")
@Component
public class FinishedPlanJob extends IJobHandler {

    @Resource
    private TaskStatusChecker taskStatusChecker;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        taskStatusChecker.checkPlanStatus();
        return IJobHandler.SUCCESS;
    }
}
