package cn.gwssi.ecloudframework.module.inspection.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.inspection.api.model.RangeDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.TemplateDTO;
import cn.gwssi.ecloudframework.module.inspection.core.model.Template;
import cn.gwssi.ecloudframework.module.inspection.core.model.TemplateIndicatorRelation;

import java.util.List;

/**
 * 巡检模板管理接口
 */
public interface InspectionTemplateManager extends Manager<String, Template> {
    /**
     * 获取巡检模板列表
     * @param pageQuery 分页查询参数
     * @param template 巡检模板查询条件
     * @return 分页结果
     */
    PageResult<List<Template>> list(PageQuery pageQuery, TemplateDTO template);

    /**
     * 保存模板及其关联的指标
     * @param template 模板
     * @param indicatorIds 指标ID列表
     * @return 保存的模板
     */
    Template saveTemplateWithIndicators(Template template, List<String> indicatorIds, List<RangeDTO> rangeDTOList);

    /**
     * 获取模板关联的指标关系列表
     * @param templateId 模板ID
     * @return 指标关系列表
     */
    List<TemplateIndicatorRelation> getTemplateIndicatorRelations(String templateId);

    /**
     * 更新模板状态
     * @param templateId 模板ID
     * @param state 状态值
     * @return 更新后的模板
     */
    Template updateState(String templateId, Integer state);

    /**
     * 根据指标ID获取关联的模板列表
     * @param indicatorId 指标ID
     * @return 模板列表
     */
    List<Template> getTemplatesByIndicatorId(String indicatorId);

    /**
     * 根据ID删除模板
     * @param id
     */
    void deleteById(String id);
}
