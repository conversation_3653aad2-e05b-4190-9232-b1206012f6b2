package cn.gwssi.ecloudframework.module.inspection.script;

import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskInstDao;
import cn.gwssi.ecloudframework.module.inspection.core.model.TaskInst;
import cn.gwssi.ecloudframework.sys.api.groovy.IScript;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class InspectionScript implements IScript {

    @Resource
    private InspectionTaskInstDao taskInstDao;

    // 废了，用下面的
    @Deprecated
    public void initTaskInst(TaskInst taskInst) {
        taskInstDao.create(taskInst);
    }

    public void updateTaskInst(TaskInst taskInst) {
        String existInst = taskInstDao.get(taskInst);
        if (StringUtils.isEmpty(existInst)) {
            taskInstDao.create(taskInst);
        } else {
            taskInstDao.update(taskInst);
        }
    }

}
