package cn.gwssi.ecloudframework.module.inspection.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.inspection.api.model.RangeDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.TemplateDTO;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTemplateAuthDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTemplateDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.TemplateIndicatorRelationDao;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionTemplateManager;
import cn.gwssi.ecloudframework.module.inspection.core.model.Plan;
import cn.gwssi.ecloudframework.module.inspection.core.model.Template;
import cn.gwssi.ecloudframework.module.inspection.core.model.TemplateAuth;
import cn.gwssi.ecloudframework.module.inspection.core.model.TemplateIndicatorRelation;
import cn.gwssi.ecloudframework.org.api.constant.GroupTypeConstant;
import cn.gwssi.ecloudframework.org.api.model.IGroup;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.service.GroupService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡检模板管理实现类
 */
@Service
public class InspectionTemplateManagerImpl extends BaseManager<String, Template> implements InspectionTemplateManager {

    public static String SCOPE_ALL = "all";

    @Resource
    GroupService groupService;

    @Resource
    private InspectionTemplateDao inspectionTemplateDao;

    @Resource
    private TemplateIndicatorRelationDao templateIndicatorRelationDao;

    @Resource
    private InspectionTemplateAuthDao inspectionTemplateAuthDao;

    @Resource
    private  InspectionPlanManagerImpl inspectionPlanManager;

    @Override
    @Transactional
    public Template saveTemplateWithIndicators(Template template, List<String> indicatorIds, List<RangeDTO> scopeRangeList) {
        // 保存模板
        if (template.getId() == null || template.getId().isEmpty()) {
            template.setCreateUser(ContextUtil.getCurrentUserName());
            if (template.getState() == null){
                template.setState(1);
            }
            inspectionTemplateDao.create(template);
        } else {
            inspectionTemplateDao.update(template);
            // 删除原有关联
            templateIndicatorRelationDao.removeByTemplateId(template.getId());

            inspectionTemplateAuthDao.removeByTemplateId(template.getId());
        }

        // 保存指标关联
        if (indicatorIds != null && !indicatorIds.isEmpty()) {
            for (String indicatorId : indicatorIds) {
                TemplateIndicatorRelation relation = new TemplateIndicatorRelation();
                relation.setTemplateId(template.getId());
                relation.setIndicatorId(indicatorId);
                templateIndicatorRelationDao.create(relation);
            }
        }

        // 保存权限关联
        if (scopeRangeList != null ) {
            if (!scopeRangeList.isEmpty()) {
                List<TemplateAuth> templateAuthList = new ArrayList<>();
                for (RangeDTO rangeDTO : scopeRangeList) {
                    TemplateAuth templateAuth = new TemplateAuth();
                    templateAuth.setTemplateId(template.getId());
                    templateAuth.setScopeObj(rangeDTO.getType());
                    String values = rangeDTO.getValue();
                    if (StringUtils.isNotEmpty(values)) {
                        for (String value : values.split(",")) {
                            templateAuth.setScopeId(value);
                            templateAuthList.add(templateAuth);
                        }
                    }
                }
                inspectionTemplateAuthDao.insertBatch(templateAuthList);
            } else {
                TemplateAuth templateAuth = new TemplateAuth();
                templateAuth.setTemplateId(template.getId());
                templateAuth.setScopeObj(SCOPE_ALL);
                inspectionTemplateAuthDao.create(templateAuth);
            }

        }
        return template;
    }

    @Override
    public PageResult<List<Template>> list(PageQuery pageQuery, TemplateDTO template) {
        IUser curUser = ContextUtil.getCurrentUser();
        List<String> rolesAlias = ContextUtil.getCurrentUser().getRoles().stream()
                .map(IUserRole::getAlias).collect(Collectors.toList());
        List<String> grpList = new ArrayList<>();
        List<String> positionList = new ArrayList<>();
        List<? extends IGroup> postIns = groupService.getGroupsByGroupTypeUserId(GroupTypeConstant.POST.key(), curUser.getUserId());
        if (!CollectionUtils.isEmpty(postIns)) {
            positionList = postIns.stream().map(IGroup::getGroupCode).collect(Collectors.toList());
        }

        List<? extends IGroup> grpIns = groupService.getGroupsByGroupTypeUserId(GroupTypeConstant.ORG.key(), curUser.getUserId());
        if (!CollectionUtils.isEmpty(grpIns)) {
            grpList = grpIns.stream().map(IGroup::getPath).collect(Collectors.toList());
        }
        PageHelperUtils.startPageAndOrderBy(pageQuery, "");
        List<Template> list = inspectionTemplateDao.list(template, curUser.getUserId(), rolesAlias, grpList, positionList);
        PageResult pageResult = new PageResult(list);
        pageResult.setRows(list);
        return pageResult;
    }

    @Override
    public List<TemplateIndicatorRelation> getTemplateIndicatorRelations(String templateId) {
        TemplateIndicatorRelation query = new TemplateIndicatorRelation();
        query.setTemplateId(templateId);
        return templateIndicatorRelationDao.getRelationList(query);
    }

    @Override
    public Template updateState(String templateId, Integer state) {
        Template template = inspectionTemplateDao.get(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        template.setState(state);
        inspectionTemplateDao.update(template);

        return template;
    }

    @Override
    public List<Template> getTemplatesByIndicatorId(String indicatorId) {
        return inspectionTemplateDao.getTemplatesByIndicatorId(indicatorId);
    }

    @Override
    public void deleteById(String id) {
        List<Plan> planList = inspectionPlanManager.getPlansByTemplateId(id);
        if (CollectionUtils.isEmpty(planList)){
            inspectionTemplateDao.remove(id);
        }else {
            throw new BusinessException("该模板有关联计划，不能删除");
        }
    }

}
