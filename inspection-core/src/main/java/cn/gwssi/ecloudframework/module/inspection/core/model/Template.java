package cn.gwssi.ecloudframework.module.inspection.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.List;

/**
 * 巡检模板实体类
 */
@Data
public class Template extends BaseModel {
    /**
     * 主键
     */
    private String id;

    /**
     * 模板类型
     */
    private String category;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 基本信息
     */
    private String basicInfo;

    /**
     * 展示项
     */
    private String displayItem;

    /**
     * 是否展示结论，1：是，0：否
     */
    private Integer showResult;

    /**
     * 处置方式，手动转工单\自动转工单
     */
    private String dispose;

    /**
     * 是否支持上传，1：是，0：否
     */
    private Integer uploadFile;

    /**
     * 适用范围，通用\限定范围
     */
    private String scope;

    /**
     * 适用范围对象；all 通用；role 角色 post 岗位  group 组 person 人员
     */
    private String scopeObj;

    /**
     * 状态，启用\停用
     */
    private Integer state;

    /**
     * 关联的指标ID列表
     */
    private List<String> indicatorIds;

    /**
     * 关联的指标列表
     */
    private List<InspectionIndicator> indicators;


    /**
     * 是否可以删除
     */
    private String showDeleteButton;

}
