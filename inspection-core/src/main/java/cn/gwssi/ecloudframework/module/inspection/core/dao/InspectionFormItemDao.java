package cn.gwssi.ecloudframework.module.inspection.core.dao;


import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.inspection.api.model.FormItemDTO;
import cn.gwssi.ecloudframework.module.inspection.core.model.FormItem;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

/**
 * 巡检单明细DAO接口
 */
@MapperScan
public interface InspectionFormItemDao extends BaseDao<String, FormItemDTO> {

    /**
     * 根据查询条件获取巡检单明细列表
     * @param taskId 任务id
     * @return 巡检单明细列表
     */
    List<FormItemDTO> list(@Param("taskId") String taskId);

    /**
     * 根据查询条件获取错误的巡检项
     * @param taskId 任务id
     * @param subjectName 巡检对象
     * @return 错误的巡检项列表
     */
    List<FormItemDTO> listWithError(@Param("taskId") String taskId, @Param("subjectName") String subjectName);

    /**
     * 批量创建任务指标
     * @param indicators 指标列表
     */
    void insertBatch(@Param("indicators") List<FormItem> indicators);

    /**
     * 统计异常指标数量，只统计今天和昨天的
     * @return 异常数量
     */
    List<Map<String, Object>> countErr(Map<String, Object> params);

    /**
     * 统计异常指标数量，按指标和对象统计
     * @return 异常数量
     */
    List<Map<String, Object>> countErrRepeated(Map<String, Object> params);
}
