package cn.gwssi.ecloudframework.module.inspection.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO;
import cn.gwssi.ecloudframework.org.api.model.IUser;

import java.util.List;
import java.util.Map;

/**
 * 巡检任务管理接口
 */
public interface InspectionTaskManager extends Manager<String, InspectionTaskDTO> {

    /**
     * 获取巡检任务列表
     * @param pageQuery 分页查询参数
     * @param task 巡检任务查询条件
     * @return 分页结果
     */
    PageResult<InspectionTaskDTO> list(PageQuery pageQuery, InspectionTaskDTO task);

    /**
     * 获取所有巡检任务
     * @param pageQuery 分页查询参数
     * @param planId 计划id
     * @return 巡检任务列表
     */
    PageResult<InspectionTaskDTO> listAll(PageQuery pageQuery, String planId);

    /**
     * 设置任务无效
     * @param taskId 任务ID
     */
    void setTaskInvalid(String taskId);

    /**
     * 获取巡检人
     * @return 巡检人列表
     */
    List<IUser> getInspectors();

    /**
     * 指定巡检人
     * @param taskId 任务ID
     * @param receiver 巡检人
     * @return 设置结果
     */
    void assignInspector(String taskId, String receiver);

    /**
     * 接收任务
     * @param taskId 任务ID
     * @param receiver 接收人
     */
    void receiveTask(String taskId, String receiver);

    /**
     * 保存任务及其关联的巡检单明细
     * @param task 任务
     */
    void save(InspectionTaskDTO task);

    /**
     * 保存计划外任务
     * @param task 任务
     */
    void saveTask(InspectionTaskDTO task);

    /**
     * 获取有异常的任务
     * @param taskId 巡检任务id
     * @param subjectName 巡检对象
     * @return 异常任务
     */
    InspectionTaskDTO getErrorTask(String taskId, String subjectName);

    /**
     * 统计巡检数据
     * @return 统计结果
     */
    Map<String, Object> getStatistics(Map<String, String> params);

    /**
     * 统计巡检数据
     * @return 统计结果
     */
    Map<String, Object> getTodayStatistics();

    /**
     * 获取当日巡检任务
     * @param state 任务状态
     * @return 当日巡检任务
     */
    List<Map<String, Object>> listToday(PageQuery pageQuery, String state);

    /**
     * 获取事件工单
     * @param pageQuery 分页参数
     * @param date 日期
     * @return 事件工单
     */
    List<Map<String, Object>> listInst(PageQuery pageQuery, String date);

    /**
     * 根据 instId 获取工单详情
     * @param instId 实例id
     * @return 工单详情
     */
    Map<String, Object> getWorkOrder(String instId);

}
