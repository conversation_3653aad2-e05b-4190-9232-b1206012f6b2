package cn.gwssi.ecloudframework.module.inspection.core.manager;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.module.inspection.api.model.PlanDTO;
import cn.gwssi.ecloudframework.module.inspection.core.model.Plan;
import cn.gwssi.ecloudframework.module.inspection.core.model.Subject;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionTask;

import java.util.List;

/**
 * 巡检计划管理接口
 */
public interface InspectionPlanManager extends Manager<String, Plan> {
    /**
     * 获取巡检计划列表
     * @param pageQuery 分页查询参数
     * @param plan 巡检计划查询条件
     * @return 分页结果
     */
    PageResult<Plan> list(PageQuery pageQuery, PlanDTO plan);

    /**
     * 保存计划及其关联的巡检对象，并生成相应的任务
     * @param plan 计划
     * @param subjects 巡检对象列表
     * @return 保存的计划
     */
    Plan savePlanWithSubjects(Plan plan, List<Subject> subjects);

    /**
     * 获取计划关联的巡检对象列表
     * @param planId 计划ID
     * @return 巡检对象列表
     */
    List<Subject> getPlanSubjects(String planId);

    /**
     * 启动计划，更新状态并生成后续任务
     * @param planId 计划ID
     * @param containsToday 是否包含今天
     * @return 启动后的计划
     */
    Plan startPlan(String planId, boolean containsToday);

    /**
     * 终止计划，更新状态并删除后续未接受的任务
     * @param planId 计划ID
     * @return 删除的任务数量
     */
    int stopPlan(String planId);

    /**
     * 根据模板ID获取关联的计划列表
     * @param templateId 模板ID
     * @return 计划列表
     */
    List<Plan> getPlansByTemplateId(String templateId);

    /**
     * 根据计划生成任务
     * @param plan 计划
     * @return 生成的任务列表
     */
    List<InspectionTask> generateTasksByPlan(Plan plan);

    /**
     * 批量创建任务
     * @param tasks 任务列表
     */
    void insertBatch(List<InspectionTask> tasks);
}
