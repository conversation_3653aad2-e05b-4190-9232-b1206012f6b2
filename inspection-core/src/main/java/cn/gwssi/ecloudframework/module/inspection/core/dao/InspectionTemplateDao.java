package cn.gwssi.ecloudframework.module.inspection.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.inspection.api.model.TemplateDTO;
import cn.gwssi.ecloudframework.module.inspection.core.model.Template;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 巡检模板DAO接口
 */
@MapperScan
public interface InspectionTemplateDao extends BaseDao<String, Template> {

    /**
     *
     * @param template
     * @param userId 当前登录用户的ID
     * @param roleList 当前登录用户的角色列表
     * @param grpList 当前用户所在分组列表
     * @param postList 当前用户岗位列表
     * @return
     */
    List<Template> list(@Param("template") TemplateDTO template, @Param("userId") String userId,
                        @Param("roleIds") List<String> roleList, @Param("groupIds") List<String> grpList,
                        @Param("positionIds") List<String> postList);

    /**
     * 根据指标ID获取关联的模板列表
     * @param indicatorId 指标ID
     * @return 模板列表
     */
    List<Template> getTemplatesByIndicatorId(String indicatorId);
}
