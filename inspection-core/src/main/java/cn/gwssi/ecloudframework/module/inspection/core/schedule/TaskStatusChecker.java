package cn.gwssi.ecloudframework.module.inspection.core.schedule;

import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants;
import cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.PlanDTO;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionPlanDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskDao;
import cn.gwssi.ecloudframework.module.inspection.core.model.Plan;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.service.UserService;
import cn.gwssi.ecloudframework.sys.api.jms.model.DefaultJmsDTO;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.ecloudframework.sys.api.jms.producer.JmsProducer;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 巡检任务状态检查器
 * 用于定时检查任务状态，关闭没有任务的计划等
 */
@Component
public class TaskStatusChecker {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskStatusChecker.class);

    @Resource
    private InspectionTaskDao inspectionTaskDao;

    @Resource
    private InspectionPlanDao inspectionPlanDao;

    @Resource
    private JmsProducer jmsProducer;

    @Resource
    private UserService userService ;

    /**
     * 检查计划状态
     * 检查所有执行中的计划，如果计划结束日期超过当前日期则完成该计划
     */
    public void checkPlanStatus() {
        LOGGER.info("开始检查巡检计划状态...");

        // 查询所有有效的巡检计划
        PlanDTO queryPlan = new PlanDTO();
        queryPlan.setState(InspectionConstants.PlanState.RUNNING.getText());
        List<Plan> activePlans = inspectionPlanDao.list(queryPlan);
        Date current = new Date();

        for (Plan plan : activePlans) {
            // 计划结束日期超过当前日期
            if (current.after(plan.getEndTime())) {
                plan.setState(InspectionConstants.PlanState.FINISHED.getText());
                inspectionPlanDao.update(plan);
                // 将关联的未完成计划任务设置成无效
                inspectionTaskDao.setDisableByPlanId(plan.getId());
            }
        }

        LOGGER.info("巡检计划状态检查完成");
    }

    /**
     * 检查任务状态
     * 未及时接收或完成的任务需要发送站内信进行通知
     */
    public void checkTaskStatus() {
        LOGGER.info("开始检查巡检任务状态...");

        // 获取发送人信息
        IUser iUser = userService.getUserByAccount("user") ;

        // 任务接收通知
        // 默认1小时
        long notifyLimitTime = 3600 * 1000;
        String notifyTime = SysPropertyUtil.getByAlias("INSPECT_EXPIRE_NOTIFY_TIME");
        if (StringUtils.isNotBlank(notifyTime)) {
            notifyLimitTime = Integer.parseInt(notifyTime) * 60 * 1000;
        }
        Date notifyExpireTime = new Date(new Date().getTime()+ notifyLimitTime);
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 查询所有快到巡检日期且待接收的任务
        List<InspectionTaskDTO> pendingTasks = inspectionTaskDao.getPendingByTaskTime(notifyExpireTime);

        for (InspectionTaskDTO task : pendingTasks) {
            List<SysIdentity> receivers = new ArrayList<>();
            SysIdentity sysIdentity = new DefaultIdentity() ;
            sysIdentity.setId(task.getCreateBy());
            sysIdentity.setName(task.getOrgUserName());
            sysIdentity.setType("user");
            sysIdentity.setOrgId(task.getOrgId());
            receivers.add(sysIdentity) ;

            String msg = String.join("",  task.getName(), "仍未有运维人员进行接收，请及时提醒相关人员进行处理。");
            Map<String, Object> extendVars = new HashMap<>() ;
            extendVars.put("type", "通知") ;
            extendVars.put("title", msg) ;
            extendVars.put("head", "通知") ;
            extendVars.put("megType", "inner") ;
            NotifyMessage notifyMessage = new NotifyMessage("巡检任务未接收通知", msg, iUser, receivers, "巡检通知", extendVars);
            DefaultJmsDTO jmsDTO = new DefaultJmsDTO("inner", notifyMessage);
            jmsProducer.sendToQueue(jmsDTO);
            task.setIsSendNotice("1");
            inspectionTaskDao.update(task);
        }

        // 任务录入完成通知
        // 默认10分钟
        long inputLimitTime = 1800 * 1000;
        String inputTime = SysPropertyUtil.getByAlias("INSPECT_EXPIRE_INPUT_TIME");
        if (StringUtils.isNotBlank(inputTime)) {
            inputLimitTime = Integer.parseInt(inputTime) * 60 * 1000;
        }
        Date inputExpireTime = new Date(new Date().getTime()+ inputLimitTime);

        // 查询所有快到巡检日期且执行中的任务
        List<InspectionTaskDTO> processingTasks = inspectionTaskDao.getProcessingByTaskTime(inputExpireTime);

        for (InspectionTaskDTO task : processingTasks) {
            List<SysIdentity> receivers = new ArrayList<>();
            SysIdentity sysIdentity = new DefaultIdentity() ;
            sysIdentity.setId(task.getReceiver());
            sysIdentity.setName(task.getOrgUserName());
            sysIdentity.setType("user");
            sysIdentity.setOrgId(task.getOrgId());
            receivers.add(sysIdentity) ;

            String msg = String.join("", task.getName(), "需在【", sf.format(task.getTaskTime()), "】前完成，请尽快处理。");
            Map<String, Object> extendVars = new HashMap<>() ;
            extendVars.put("type", "通知") ;
            extendVars.put("title", msg) ;
            extendVars.put("detailId",task.getId());
            extendVars.put("head", "通用") ;
            extendVars.put("megType", "inner") ;
            extendVars.put("requestMethod", "inspe_task") ;
            NotifyMessage notifyMessage = new NotifyMessage("巡检任务未完成通知", msg, iUser, receivers, "巡检通知", extendVars);
            DefaultJmsDTO jmsDTO = new DefaultJmsDTO("inner", notifyMessage);
            jmsProducer.sendToQueue(jmsDTO);
            task.setIsSendNotice("2");
            inspectionTaskDao.update(task);
        }

        // 计划结束通知
        // 默认1小时
        long planEndNotifyTime = 3600 * 1000;
        String planEndTime = SysPropertyUtil.getByAlias("INSPECT_PLAN_END_NOTIFY_TIME");
        if (StringUtils.isNotBlank(notifyTime)) {
            planEndNotifyTime = Integer.parseInt(planEndTime) * 60 * 1000;
        }
        Date planExpireTime = new Date(new Date().getTime()+ planEndNotifyTime);
        // 查询快结束的巡检计划
        PlanDTO queryPlan = new PlanDTO();
        queryPlan.setEndTimeFinish(planExpireTime);
        queryPlan.setIsSendNotice("0");
        List<Plan> activePlans = inspectionPlanDao.list(queryPlan);

        for (Plan plan : activePlans) {
            List<SysIdentity> receivers = new ArrayList<>();
            SysIdentity sysIdentity = new DefaultIdentity() ;
            sysIdentity.setId(plan.getCreateBy());
            sysIdentity.setName(plan.getCreateUser());
            sysIdentity.setType("user");
            receivers.add(sysIdentity) ;

            String msg = String.join("", "【", plan.getName(), "】于[", sf.format(plan.getEndTime()), "]结束，请知悉。");
            Map<String, Object> extendVars = new HashMap<>() ;
            extendVars.put("type", "通知") ;
            extendVars.put("title", msg) ;
            extendVars.put("head", "通知") ;
            extendVars.put("megType", "inner") ;
            NotifyMessage notifyMessage = new NotifyMessage("巡检计划提醒", msg, iUser, receivers, "巡检通知", extendVars);
            DefaultJmsDTO jmsDTO = new DefaultJmsDTO("inner", notifyMessage);
            jmsProducer.sendToQueue(jmsDTO);
            plan.setIsSendNotice("1");
            inspectionPlanDao.update(plan);
        }

        LOGGER.info("巡检任务状态检查完成");
    }
}
