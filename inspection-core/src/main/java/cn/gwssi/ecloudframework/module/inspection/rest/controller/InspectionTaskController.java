package cn.gwssi.ecloudframework.module.inspection.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionTaskManager;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 巡检任务控制器
 */
@RestController
@RequestMapping("/module/inspection/task")
public class InspectionTaskController {
    @Resource
    private InspectionTaskManager inspectionTaskManager;

    /**
     * 获取巡检任务列表
     * @param pageQuery 分页查询参数
     * @param task 巡检任务查询条件
     * @return 巡检任务列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public PageResult<InspectionTaskDTO> list(PageQuery pageQuery, @RequestBody InspectionTaskDTO task) {
        return inspectionTaskManager.list(pageQuery, task);
    }

    /**
     * 获取所有巡检任务
     * @param pageQuery 分页查询参数
     * @param planId 计划id
     * @return 巡检任务列表
     */
    @RequestMapping("/listAll")
    @ResponseBody
    public PageResult<InspectionTaskDTO> listAll(PageQuery pageQuery, @RequestParam String planId) {
        return inspectionTaskManager.listAll(pageQuery, planId);
    }

    /**
     * 获取巡检任务详情
     * @param id 巡检任务ID
     * @return 巡检任务详情
     */
    @RequestMapping("/get")
    public ResultMsg<InspectionTaskDTO> get(@RequestParam("id") String id) {
        InspectionTaskDTO task = inspectionTaskManager.get(id);
        return new ResultMsg<>(task);
    }

    /**
     * 设置任务无效
     * @param id 巡检任务ID
     * @return 设置结果
     */
    @RequestMapping("/setInvalid")
    @CatchErr
    public ResultMsg<String> setInvalid(@RequestParam("id") String id) {
        inspectionTaskManager.setTaskInvalid(id);
        return ResultMsg.SUCCESS("设置成功");
    }

    /**
     * 获取巡检人
     * @return 指定结果
     */
    @RequestMapping("/getInspectors")
    @CatchErr
    public ResultMsg getInspectors() {
        List<IUser> users = inspectionTaskManager.getInspectors();
        return new ResultMsg<>(users);
    }

    /**
     * 指定巡检人
     * @param id 巡检任务ID
     * @param receiver 巡检人
     * @return 指定结果
     */
    @RequestMapping("/assignInspector")
    @CatchErr
    public ResultMsg<String> assignInspector(@RequestParam("id") String id, @RequestParam("receiver") String receiver) {
        inspectionTaskManager.assignInspector(id, receiver);
        return ResultMsg.SUCCESS("指定成功");
    }

    /**
     * 接收巡检任务
     * @param id 巡检任务ID
     * @param receiver 接收人
     * @return 接收结果
     */
    @RequestMapping("/receive")
    @CatchErr
    public ResultMsg<String> receive(@RequestParam("id") String id, @RequestParam("receiver") String receiver) {
        inspectionTaskManager.receiveTask(id, receiver);
        return ResultMsg.SUCCESS("接收成功");
    }

    /**
     * 巡检结果录入
     * @param task 巡检任务
     * @return 保存结果
     */
    @RequestMapping("/save")
    @CatchErr
    public ResultMsg<String> save(@RequestBody InspectionTaskDTO task) {
        inspectionTaskManager.save(task);
        return ResultMsg.SUCCESS("保存成功");
    }

    /**
     * 计划外任务
     * @param task 巡检任务
     * @return 保存结果
     */
    @RequestMapping("/saveTask")
    @CatchErr
    public ResultMsg<String> saveTask(@RequestBody InspectionTaskDTO task) {
        inspectionTaskManager.saveTask(task);
        return ResultMsg.SUCCESS("保存成功");
    }

    /**
     * 获取有异常的任务
     * @param taskId 巡检任务id
     * @param subjectName 巡检对象
     * @return 异常任务
     */
    @RequestMapping("/getErrorTask")
    @CatchErr
    public ResultMsg<InspectionTaskDTO> getErrorTask(@RequestParam("taskId") String taskId, @RequestParam("subjectName") String subjectName) {
        InspectionTaskDTO result = inspectionTaskManager.getErrorTask(taskId, subjectName);
        return new ResultMsg<>(result);
    }

    /**
     * 获取巡检统计数据
     * @return 统计数据
     */
    @RequestMapping("/statistics")
    @ResponseBody
    public ResultMsg<Map<String, Object>> getStatistics(@RequestBody Map<String, String> params) {
        Map<String, Object> statistics = inspectionTaskManager.getStatistics(params);
        return ResultMsg.SUCCESS(statistics);
    }

    /**
     * 获取当日巡检统计数据
     * @return 当日巡检统计数据
     */
    @RequestMapping("/today/statistics")
    @ResponseBody
    public ResultMsg<Map<String, Object>> getTodayStatistics() {
        Map<String, Object> statistics = inspectionTaskManager.getTodayStatistics();
        return ResultMsg.SUCCESS(statistics);
    }

    /**
     * 获取当日巡检任务
     * @param state 任务状态
     * @return 当日巡检任务
     */
    @RequestMapping("/today/list")
    @ResponseBody
    public PageResult<Map<String, Object>> listToday(PageQuery pageQuery, @RequestParam("state") String state) {
        List<Map<String, Object>> list = inspectionTaskManager.listToday(pageQuery, state);
        return new PageResult<>(list);
    }

    /**
     * 获取事件工单
     * @param date 日期
     * @return 事件工单
     */
    @RequestMapping("/inst/list")
    @ResponseBody
    public PageResult<List<Map<String, Object>>> listInst(PageQuery pageQuery, @RequestParam("date") String date) {
        List<Map<String, Object>> list = inspectionTaskManager.listInst(pageQuery, date);
        return new PageResult(list);
    }

    /**
     * 根据 instId 获取工单详情
     * @param instId 实例id
     * @return 工单详情
     */
    @RequestMapping("/inst/detail")
    @ResponseBody
    public ResultMsg<Map<String, Object>> getWorkOrder(@RequestParam("instId") String instId) {
        Map<String, Object> detail = inspectionTaskManager.getWorkOrder(instId);
        return ResultMsg.SUCCESS(detail);
    }

}
