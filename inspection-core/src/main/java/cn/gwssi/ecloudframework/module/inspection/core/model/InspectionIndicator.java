package cn.gwssi.ecloudframework.module.inspection.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants.IndicatorDicType;
import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants.IndicatorResultType;
import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants.IndicatorState;
import lombok.Data;

/**
 * 巡检指标实体类
 */
@Data
public class InspectionIndicator extends BaseModel {
    /**
     * 主键
     */
    private String id;

    /**
     * 巡检指标
     */
    private String indicator;

    /**
     * 巡检方法
     */
    private String method;

    /**
     * 参考标准
     */
    private String standard;

    /**
     * 所属分类
     */
    private String category;

    /**
     * 结果种类，选项勾选\人工填写
     */
    private String resultType;

    /**
     * 启用状态，1：启用，0：停用
     */
    private Integer state;

    /**
     * 字典选择，是/否，正常/异常
     */
    private String dicType;


    /**
     * 是否可编辑，非数据库字段
     */
    private String editable ;

    /**
     * 是否可删除，非数据库字段
     */
    private String deletable ;

    /**
     * 获取结果种类枚举
     * @return 结果种类枚举
     */
    public IndicatorResultType getResultTypeEnum() {
        if (resultType == null) {
            return null;
        }
        for (IndicatorResultType type : IndicatorResultType.values()) {
            if (type.getText().equals(resultType)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 设置结果种类枚举
     * @param resultType 结果种类枚举
     */
    public void setResultTypeEnum(IndicatorResultType resultType) {
        if (resultType != null) {
            this.resultType = resultType.getText();
        }
    }

    /**
     * 获取启用状态枚举
     * @return 启用状态枚举
     */
    public IndicatorState getStateEnum() {
        if (state == null) {
            return null;
        }
        for (IndicatorState s : IndicatorState.values()) {
            if (s.getValue() == state) {
                return s;
            }
        }
        return null;
    }

    /**
     * 设置启用状态枚举
     * @param state 启用状态枚举
     */
    public void setStateEnum(IndicatorState state) {
        if (state != null) {
            this.state = state.getValue();
        }
    }

    /**
     * 获取字典选择枚举
     * @return 字典选择枚举
     */
    public IndicatorDicType getDicTypeEnum() {
        if (dicType == null) {
            return null;
        }
        for (IndicatorDicType type : IndicatorDicType.values()) {
            if (type.getText().equals(dicType)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 设置字典选择枚举
     * @param dicType 字典选择枚举
     */
    public void setDicTypeEnum(IndicatorDicType dicType) {
        if (dicType != null) {
            this.dicType = dicType.getText();
        }
    }

}
