package cn.gwssi.ecloudframework.module.inspection.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.module.inspection.api.model.RangeDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.TemplateDTO;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionIndicatorManager;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionTemplateManager;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionIndicator;
import cn.gwssi.ecloudframework.module.inspection.core.model.Template;
import cn.gwssi.ecloudframework.module.inspection.core.model.TemplateIndicatorRelation;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 巡检模板控制器
 */
@RestController
@RequestMapping("/module/inspection/template")
public class InspectionTemplateController extends ControllerTools {
    @Resource
    private InspectionTemplateManager inspectionTemplateManager;

    @Resource
    private InspectionIndicatorManager inspectionIndicatorManager;

    /**
     * 获取巡检模板列表
     * @param pageQuery 分页查询参数
     * @param template 巡检模板查询条件
     * @return 巡检模板列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public PageResult<List<Template>> list(PageQuery pageQuery, @RequestBody TemplateDTO template) {
        return inspectionTemplateManager.list(pageQuery, template);
    }

    /**
     * 获取巡检模板详情
     * @param id 巡检模板ID
     * @return 巡检模板详情
     */
    @RequestMapping("/get/{id}")
    public ResultMsg<Template> get(@PathVariable("id") String id) {
        Template template = inspectionTemplateManager.get(id);
        if (ObjectUtil.isEmpty(template)){
            throw new BusinessException("未找到对应模板");
        }
        List<TemplateIndicatorRelation> relations = inspectionTemplateManager.getTemplateIndicatorRelations(id);

        List<String> indicatorIds = new ArrayList<>();
        List<InspectionIndicator> indicators = new ArrayList<>();

        for (TemplateIndicatorRelation relation : relations) {
            indicatorIds.add(relation.getIndicatorId());
            InspectionIndicator indicator = inspectionIndicatorManager.get(relation.getIndicatorId());
            if (indicator != null) {
                indicators.add(indicator);
            }
        }

        template.setIndicatorIds(indicatorIds);
        template.setIndicators(indicators);

        return getSuccessResult(template);
    }

    /**
     * 保存巡检模板
     * @param templateDTO 巡检模板
     * @return 保存结果
     */
    @RequestMapping("/save")
    @CatchErr
    public ResultMsg<Template> save(@RequestBody TemplateDTO templateDTO) {
        List<String> indicatorIds = templateDTO.getIndicatorIds();

        List<RangeDTO> scopeRange = templateDTO.getScopeRange();

        Template template = inspectionTemplateManager.saveTemplateWithIndicators(BeanUtil.toBean(templateDTO, Template.class),
                indicatorIds, scopeRange);

        return getSuccessResult(template,"保存成功");
    }

    /**
     * 删除巡检模板
     * @param id 巡检模板ID
     * @return 删除结果
     */
    @RequestMapping("/remove/{id}")
    @CatchErr
    public ResultMsg<String> remove(@PathVariable("id") String id) {
        inspectionTemplateManager.deleteById(id);
        return getSuccessResult("删除成功");
    }

    /**
     * 更新巡检模板状态
     * @param id 巡检模板ID
     * @param state 状态值（1：启用，0：停用）
     * @return 更新结果
     */
    @RequestMapping("/updateState/{id}/{state}")
    @CatchErr
    public ResultMsg<String> updateState(@PathVariable("id") String id, @PathVariable("state") Integer state) {
        try {
            Template template = inspectionTemplateManager.updateState(id, state);
            String message = state == 1 ? "启用成功" : "停用成功";
            return getSuccessResult(message);
        } catch (Exception e) {
            return getWarnResult("更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 根据指标ID获取关联的模板列表
     * @param indicatorId 指标ID
     * @return 模板列表
     */
    @RequestMapping("/getByIndicatorId/{indicatorId}")
    @ResponseBody
    public ResultMsg<List<Template>> getByIndicatorId(@PathVariable("indicatorId") String indicatorId) {
        List<Template> templates = inspectionTemplateManager.getTemplatesByIndicatorId(indicatorId);
        return getSuccessResult(templates);
    }
}
