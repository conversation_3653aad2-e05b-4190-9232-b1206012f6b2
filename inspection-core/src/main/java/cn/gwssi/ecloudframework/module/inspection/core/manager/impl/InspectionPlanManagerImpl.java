package cn.gwssi.ecloudframework.module.inspection.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.inspection.api.constant.InspectionConstants;
import cn.gwssi.ecloudframework.module.inspection.api.model.PlanDTO;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionPlanDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTaskDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.InspectionTemplateDao;
import cn.gwssi.ecloudframework.module.inspection.core.dao.SubjectDao;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionPlanManager;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionTask;
import cn.gwssi.ecloudframework.module.inspection.core.model.Plan;
import cn.gwssi.ecloudframework.module.inspection.core.model.Subject;
import cn.gwssi.ecloudframework.module.inspection.core.model.Template;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.sys.core.dao.HolidayConfDao;
import cn.gwssi.ecloudframework.sys.core.model.HolidayConf;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.ecloudframework.sys.util.CurrentContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 巡检计划管理实现类
 */
@Service
public class InspectionPlanManagerImpl extends BaseManager<String, Plan> implements InspectionPlanManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(InspectionPlanManagerImpl.class);

    @Resource
    private InspectionPlanDao inspectionPlanDao;

    @Resource
    private SubjectDao subjectDao;

    @Resource
    private InspectionTaskDao inspectionTaskDao;

    @Resource
    private InspectionTemplateDao inspectionTemplateDao;

    @Resource
    private HolidayConfDao holidayConfDao;

    @Override
    public PageResult<Plan> list(PageQuery pageQuery, PlanDTO plan) {
        IUser currentUser = ContextUtil.getCurrentUser();
        List<? extends IUserRole> roles = currentUser.getRoles();
        boolean isLeader = roles.stream().anyMatch(item -> "G_ROLE_INSPECT_LEADER".equals(item.getAlias()));
        if (isLeader) {
            plan.setCreateBy(currentUser.getUserId());
        }
        PageHelperUtils.startPageAndOrderBy(pageQuery, "p.state asc, p.end_time desc");
        List<Plan> list = inspectionPlanDao.list(plan);
        return new PageResult<>(list);
    }

    @Override
    @Transactional
    public Plan savePlanWithSubjects(Plan plan, List<Subject> subjects) {
        try {
            Plan oldPlan;
            // 保存计划
            if (plan.getId() == null || plan.getId().isEmpty()) {
                if (!InspectionConstants.PlanState.DRAFT.getText().equals(plan.getState())) {
                    if (plan.getStartTime().after(plan.getEndTime())) {
                        throw new RuntimeException("计划结束时间不能小于计划开始时间");
                    }
                    Template template = inspectionTemplateDao.get(plan.getTemplateId());
                    if (template == null) {
                        throw new RuntimeException("巡检模板不存在");
                    }
                }

                plan.setCreateUser(ContextUtil.getCurrentUserName());
                plan.setIsSendNotice("0");
                inspectionPlanDao.create(plan);
                // 保存巡检对象
                if (subjects != null && !subjects.isEmpty()) {
                    for (Subject subject : subjects) {
                        subject.setPlanId(plan.getId());
                        subjectDao.create(subject);
                    }
                }
                oldPlan = plan;
            } else {
                oldPlan = inspectionPlanDao.get(plan.getId());
                // 只有暂存状态会变更巡检对象
                if (InspectionConstants.PlanState.DRAFT.getText().equals(plan.getState())) {
                    // 删除原有巡检对象
                    subjectDao.deleteByPlan(plan.getId());
                    // 保存新的巡检对象
                    if (subjects != null && !subjects.isEmpty()) {
                        for (Subject subject : subjects) {
                            subject.setPlanId(plan.getId());
                            subjectDao.create(subject);
                        }
                    }
                } else {
                    if (plan.getStartTime().after(plan.getEndTime())) {
                        throw new RuntimeException("计划结束时间不能小于开始时间");
                    }
                    Template template = inspectionTemplateDao.get(plan.getTemplateId());
                    if (template == null) {
                        throw new RuntimeException("巡检模板不存在");
                    }
                }
                inspectionPlanDao.update(plan);
            }

            // 执行中状态生成巡检任务
            if (InspectionConstants.PlanState.RUNNING.getText().equals(plan.getState())) {
                // 编辑运行中的计划，计划结束日期在旧的计划结束日期之后，需要生成延长日期的任务
                if (plan.getEndTime().after(oldPlan.getEndTime())) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(oldPlan.getEndTime());
                    calendar.add(Calendar.DAY_OF_YEAR, 1);
                    Date newDate = calendar.getTime();
                    // 重置编辑后的计划开始时间，为了生成延长日期的任务
                    plan.setStartTime(newDate);
                    List<InspectionTask> tasks = generateTasksByPlan(plan);
                    if (tasks != null && !tasks.isEmpty()) {
                        insertBatch(tasks);
                    }
                    //生成完新的任务后，计划开始时间重置成原来的开始时间
                    plan.setStartTime(oldPlan.getStartTime());
                } else if (plan.getEndTime().before(oldPlan.getEndTime())) {
                    //计划结束时间小于之前的计划结束时间，需要删除这段时间内的任务
                    inspectionTaskDao.deleteByPlanIdAndTaskTime(plan.getId(), plan.getEndTime());
                } else {
                    //计划结束时间未修改，如果已经生成了任务，则不再重复生成
                    int count = inspectionTaskDao.count(plan.getId());
                    if (count <= 0) {
                        List<InspectionTask> tasks = generateTasksByPlan(plan);
                        if (tasks != null && !tasks.isEmpty()) {
                            insertBatch(tasks);
                        }
                    }
                }
            }
            return plan;
        } catch (Exception e) {
            LOGGER.error("保存计划失败", e);
            throw new RuntimeException("保存失败：" + e.getMessage());
        }
    }

    @Override
    public List<Subject> getPlanSubjects(String planId) {
        Subject query = new Subject();
        query.setPlanId(planId);
        return subjectDao.getSubjectList(query);
    }

    @Override
    @Transactional
    public Plan startPlan(String planId, boolean containsToday) {
        try {
            Plan plan = inspectionPlanDao.get(planId);
            if (plan == null) {
                throw new RuntimeException("计划不存在");
            }

            if (plan.getEndTime().before(new Date())) {
                throw new RuntimeException("计划结束日期小于当前日期");
            }

            // 更新计划状态为执行中
            plan.setState(InspectionConstants.PlanState.RUNNING.getText());
            inspectionPlanDao.update(plan);

            Date restartTime = new Date();
            if (!containsToday) {
                LocalDate currentDate = LocalDate.now().plusDays(1);
                restartTime = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
            // 失效的任务设置成有效
            inspectionTaskDao.updateUnreceivedTasksByPlanIdAndTime(planId, InspectionConstants.TaskValid.VALID.getValue(), restartTime);

            return plan;
        } catch (Exception e) {
            LOGGER.error("启动计划失败", e);
            throw new RuntimeException("启动失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int stopPlan(String planId) {
        try {
            Plan plan = inspectionPlanDao.get(planId);
            if (plan == null) {
                throw new RuntimeException("计划不存在");
            }

            // 更新计划状态为已终止
            plan.setState(InspectionConstants.PlanState.STOPPED.getText());
            inspectionPlanDao.update(plan);

            // 将未接收的任务设置成无效
            int count = inspectionTaskDao.updateUnreceivedTasksByPlanId(planId, InspectionConstants.TaskValid.INVALID.getValue());

            return count;
        } catch (Exception e) {
            LOGGER.error("终止计划失败", e);
            throw new RuntimeException("终止计划失败：" + e.getMessage());
        }
    }

    @Override
    public List<Plan> getPlansByTemplateId(String templateId) {
        return inspectionPlanDao.getPlansByTemplateId(templateId);
    }

    @Override
    public List<InspectionTask> generateTasksByPlan(Plan plan) {
        if (plan == null || StringUtils.isEmpty(plan.getPlanTime())) {
            return Collections.emptyList();
        }

        Template template = inspectionTemplateDao.get(plan.getTemplateId());
        if (template == null) {
            return Collections.emptyList();
        }

        List<InspectionTask> tasks = new ArrayList<>();

        try {
            // 解析计划时间
            String planTime = plan.getPlanTime();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = plan.getStartTime();
            Date endDate = plan.getEndTime();
            Calendar calendar = Calendar.getInstance();

            // 获取节假日列表（如果需要跳过节假日）
            Set<String> holidays = new HashSet<>();
            if (plan.getHolidaySkip() != null && plan.getHolidaySkip() == 1) {
                // 这里应该从数据库查询节假日列表
                List<HolidayConf> holidayConfs = holidayConfDao.query();
                holidayConfs.stream().forEach(item -> addHolidays(holidays, item.getStartDay(), item.getEndDay()));
            }

            // 根据巡检类型生成任务
            if (InspectionConstants.PlanCategory.DAILY.getText().equals(plan.getCategory())) {
                // 日巡检，每天多个任务
                calendar.setTime(startDate);
                while (!calendar.getTime().after(endDate)) {
                    Date currentDate = calendar.getTime();
                    String dateStr = dateFormat.format(currentDate);

                    // 如果需要跳过节假日且当前日期是节假日，则跳过
                    if (plan.getHolidaySkip() != null && plan.getHolidaySkip() == 1 && holidays.contains(dateStr)) {
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                        continue;
                    }

                    // 多个任务时间
                    Set<String> planTimes = Arrays.stream(planTime.split(",")).collect(Collectors.toSet());

                    planTimes.forEach(item -> {
                        // 创建任务
                        InspectionTask task = createTasksForDate(plan, template, currentDate, item);
                        if (Objects.nonNull(task)) {
                            tasks.add(task);
                        }
                    });

                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                }
            } else if (InspectionConstants.PlanCategory.WEEKLY.getText().equals(plan.getCategory())) {
                // 周巡检，每周多个任务
                // 解析周几和时间，格式如：1#09:00,19:00|2#09:00,13:00,19:00
                String[] fields = planTime.split("\\|");
                Arrays.stream(fields).forEach(item -> {
                    String[] parts = item.split("#");
                    String weekDays = parts[0]; // 如：1
                    Set<String> timeStr = Arrays.stream(parts[1].split(",")).collect(Collectors.toSet());

                    Set<Integer> weekDaySet = Arrays.stream(weekDays.split(","))
                            .map(Integer::parseInt)
                            .collect(Collectors.toSet());

                    calendar.setTime(startDate);
                    while (!calendar.getTime().after(endDate)) {
                        Date currentDate = calendar.getTime();
                        String dateStr = dateFormat.format(currentDate);
                        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                        // 转换为我们的周几格式（1-7，周一到周日）
                        dayOfWeek = dayOfWeek == 1 ? 7 : dayOfWeek - 1;

                        // 如果是指定的周几且不是节假日（或不需要跳过节假日）
                        if (weekDaySet.contains(dayOfWeek) &&
                                (plan.getHolidaySkip() == null || plan.getHolidaySkip() != 1 || !holidays.contains(dateStr))) {
                            // 创建任务
                            timeStr.forEach(time -> {
                                InspectionTask task = createTasksForDate(plan, template, currentDate, time);
                                if (Objects.nonNull(task)) {
                                    tasks.add(task);
                                }
                            });
                        }

                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                    }
                });
            } else if (InspectionConstants.PlanCategory.MONTHLY.getText().equals(plan.getCategory())) {
                // 月巡检，每月多个任务
                // 解析日期和时间，格式如：1#09:00,19:00|2#09:00,13:00,19:00 或 lastDayOfMonth#09:00,19:00
                String[] fields = planTime.split("\\|");
                Arrays.stream(fields).forEach(item -> {
                    String[] parts = item.split("#");
                    String dayStr = parts[0]; // 如：21 或 lastDayOfMonth
                    Set<String> timeStr = Arrays.stream(parts[1].split(",")).collect(Collectors.toSet());

                    boolean isLastDay = "lastDayOfMonth".equals(dayStr);
                    int specificDay = isLastDay ? -1 : Integer.parseInt(dayStr);

                    calendar.setTime(startDate);
                    while (!calendar.getTime().after(endDate)) {
                        // 如果是指定日期，设置为当月的指定日期
                        if (!isLastDay) {
                            calendar.set(Calendar.DAY_OF_MONTH, Math.min(specificDay, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)));
                        } else {
                            // 如果是月末，设置为当月最后一天
                            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                        }

                        Date currentDate = calendar.getTime();
                        String dateStr = dateFormat.format(currentDate);

                        // 如果当前日期在范围内且不是节假日（或不需要跳过节假日）
                        if (!currentDate.before(startDate) && !currentDate.after(endDate) &&
                                (plan.getHolidaySkip() == null || plan.getHolidaySkip() != 1 || !holidays.contains(dateStr))) {
                            // 创建任务
                            timeStr.forEach(time -> {
                                InspectionTask task = createTasksForDate(plan, template, currentDate, time);
                                if (Objects.nonNull(task)) {
                                    tasks.add(task);
                                }
                            });
                        }

                        // 移动到下一个月的第一天
                        calendar.add(Calendar.MONTH, 1);
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("生成任务失败", e);
            throw new RuntimeException("生成计划任务失败", e);
        }

        return tasks;
    }

    /**
     * 为指定日期创建任务
     */
    private InspectionTask createTasksForDate(Plan plan, Template template, Date date, String timeStr) {
        InspectionTask task = new InspectionTask();

        try {
            // 解析时间
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
            Date time = timeFormat.parse(timeStr);

            // 组合日期和时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            Calendar timeCalendar = Calendar.getInstance();
            timeCalendar.setTime(time);
            calendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
            calendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            Date taskTime = calendar.getTime();

            // 将巡检对象转成json保存到任务里
            ObjectMapper objectMapper = new ObjectMapper();
            String subjectJson = objectMapper.writeValueAsString(plan.getSubjects());

            String type;
            if (InspectionConstants.PlanCategory.MONTHLY.getText().equals(plan.getCategory())) {
                type = "月巡检";
            } else if (InspectionConstants.PlanCategory.WEEKLY.getText().equals(plan.getCategory())) {
                type = "周巡检";
            } else {
                type = "日巡检";
            }
            String name = String.join("", "【", type, "】", plan.getName(), "[", new SimpleDateFormat("yyyyMMdd").format(date), "]");
            task.setName(name);
            task.setPlanId(plan.getId());
            task.setTemplateId(plan.getTemplateId());
            task.setCategory(plan.getCategory());
            task.setTaskReceiver(plan.getTaskReceiver());
            task.setTaskTime(taskTime);
            task.setState(InspectionConstants.TaskState.PENDING.getText());
            task.setEnabled(InspectionConstants.TaskValid.VALID.getValue());
            task.setTType(InspectionConstants.TaskType.PLANNED.getText());
            task.setSubjectJson(subjectJson);
            task.setCreateUser(ContextUtil.getCurrentUserName());
            task.setIsSendNotice("0");
        } catch (Exception e) {
            LOGGER.error("为指定日期创建计划任务失败", e);
            throw new RuntimeException("为指定日期创建计划任务失败", e);
        }
        return task;
    }

    @Override
    public void insertBatch(List<InspectionTask> tasks) {
        if (tasks != null && !tasks.isEmpty()) {
            inspectionTaskDao.insertBatch(tasks);
        }
    }

    private static void addHolidays(Set<String> holidays, Date startDate, Date endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate current = start;
        while (!current.isAfter(end)) {
            holidays.add(current.format(formatter));
            current = current.plusDays(1);
        }
    }
}
