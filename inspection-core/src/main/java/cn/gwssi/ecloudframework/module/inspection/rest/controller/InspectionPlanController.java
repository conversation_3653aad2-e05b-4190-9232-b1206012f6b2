package cn.gwssi.ecloudframework.module.inspection.rest.controller;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.inspection.api.model.PlanDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.SubjectDTO;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionPlanManager;
import cn.gwssi.ecloudframework.module.inspection.core.manager.InspectionTaskManager;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionTask;
import cn.gwssi.ecloudframework.module.inspection.core.model.Plan;
import cn.gwssi.ecloudframework.module.inspection.core.model.Subject;
import cn.gwssi.ecloudframework.module.inspection.core.schedule.TaskStatusChecker;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 巡检计划控制器
 */
@RestController
@RequestMapping("/module/inspection/plan")
public class InspectionPlanController {
    @Resource
    private InspectionPlanManager inspectionPlanManager;

    @Resource
    private TaskStatusChecker taskStatusChecker;

    /**
     * 获取巡检计划列表
     * @param pageQuery 分页查询参数
     * @param plan 巡检计划查询条件
     * @return 巡检计划列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public PageResult<Plan> list(PageQuery pageQuery, @RequestBody PlanDTO plan) {
        return inspectionPlanManager.list(pageQuery, plan);
    }

    /**
     * 获取巡检计划详情
     * @param id 巡检计划ID
     * @return 巡检计划详情
     */
    @RequestMapping("/get/{id}")
    public ResultMsg<PlanDTO> get(@PathVariable("id") String id) {
        Plan plan = inspectionPlanManager.get(id);
        PlanDTO planDTO = BeanCopierUtils.transformBean(plan, PlanDTO.class);

        List<Subject> subjects = inspectionPlanManager.getPlanSubjects(id);

        planDTO.setSubjects(BeanCopierUtils.transformList(subjects, SubjectDTO.class));

        return ResultMsg.SUCCESS(planDTO);
    }

    /**
     * 保存巡检计划
     * @param plan 巡检计划
     * @return 保存结果
     */
    @RequestMapping("/save")
    @CatchErr
    public ResultMsg<String> save(@RequestBody Plan plan) {
        List<Subject> subjects = plan.getSubjects();
        Plan savedPlan = inspectionPlanManager.savePlanWithSubjects(plan, subjects);
        return ResultMsg.SUCCESS(savedPlan.getId());
    }

    /**
     * 删除巡检计划
     * @param id 巡检计划ID
     * @return 删除结果
     */
    @RequestMapping("/remove/{id}")
    @CatchErr
    public ResultMsg<String> remove(@PathVariable("id") String id) {
        inspectionPlanManager.remove(id);
        return ResultMsg.SUCCESS("删除成功");
    }

    /**
     * 启动巡检计划
     * @param id 巡检计划ID
     * @return 启动结果
     */
    @RequestMapping("/start/{id}")
    @CatchErr
    public ResultMsg<String> start(@PathVariable("id") String id, @RequestParam("containsToday") boolean containsToday) {
        Plan plan = inspectionPlanManager.startPlan(id, containsToday);
        return ResultMsg.SUCCESS("启动成功");
    }

    /**
     * 终止巡检计划
     * @param id 巡检计划ID
     * @return 终止结果
     */
    @RequestMapping("/stop/{id}")
    @CatchErr
    public ResultMsg<String> stop(@PathVariable("id") String id) {
        int count = inspectionPlanManager.stopPlan(id);
        return ResultMsg.SUCCESS("终止成功");
    }

    /**
     * 根据模板ID获取关联的计划列表
     * @param templateId 模板ID
     * @return 计划列表
     */
    @RequestMapping("/getByTemplateId/{templateId}")
    @ResponseBody
    public ResultMsg<List<Plan>> getByTemplateId(@PathVariable("templateId") String templateId) {
        List<Plan> plans = inspectionPlanManager.getPlansByTemplateId(templateId);
        return ResultMsg.SUCCESS(plans);
    }

    @PostMapping("/test/planJob")
    public ResultMsg<String> testPlanJob() {
        taskStatusChecker.checkPlanStatus();
        return ResultMsg.SUCCESS("plan检查执行成功");
    }

    @PostMapping("/test/taskJob")
    public ResultMsg<String> testTaskJob() {
        taskStatusChecker.checkTaskStatus();
        return ResultMsg.SUCCESS("task检查执行成功");
    }
}
