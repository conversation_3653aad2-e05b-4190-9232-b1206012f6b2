package cn.gwssi.ecloudframework.module.inspection.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 巡检任务实体类
 */
@Data
public class InspectionTask extends BaseModel {
    /**
     * 主键
     */
    private String id;

    /**
     * 列表页面查询哪个tab，1：待处理，2：已完成
     */
    private String tab;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 计划id
     */
    private String planId;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 展示项
     */
    private String displayItem;

    /**
     * 巡检类型
     */
    private String category;

    /**
     * 任务接收人
     */
    private String taskReceiver;

    /**
     * 计划巡检时间
     */
    private Date taskTime;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 巡检人
     */
    private String receiver;

    /**
     * 巡检人姓名
     */
    private String receiverName;

    /**
     * 巡检结论，正常\异常
     */
    private String result;

    /**
     * 完成状态，待接收\处理中\已完成
     */
    private String state;

    /**
     * 是否有效，1：是，2：否
     */
    private Integer enabled;

    /**
     * 任务类型，计划中\计划外
     */
    private String tType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 巡检对象，json
     */
    private String subjectJson;

    /**
     * 附件，json
     */
    private String files;

    /**
     * 工单id
     */
    private String orderId;

    /**
     * 实例id
     */
    private String instId;

    /**
     * 关联的巡检单明细列表
     */
    private List<FormItem> formItems;

    /**
     * 是否已发送过提醒,0:未发送 1:发送了未接收提醒 2:发送了录入提醒
     */
    private String isSendNotice;
}
