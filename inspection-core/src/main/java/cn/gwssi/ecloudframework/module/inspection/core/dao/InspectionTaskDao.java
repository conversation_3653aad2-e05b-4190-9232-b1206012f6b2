package cn.gwssi.ecloudframework.module.inspection.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.inspection.api.model.InspectionTaskDTO;
import cn.gwssi.ecloudframework.module.inspection.api.model.TaskInstDTO;
import cn.gwssi.ecloudframework.module.inspection.core.model.InspectionTask;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 巡检任务DAO接口
 */
@MapperScan
public interface InspectionTaskDao extends BaseDao<String, InspectionTaskDTO> {

    /**
     * 获取巡检任务列表
     * @param task 查询参数
     * @return 巡检任务列表
     */
    List<InspectionTaskDTO> list(@Param("task") InspectionTaskDTO task, @Param("isLeader") boolean isLeader, @Param("orgId") String orgId, @Param("userId") String userId);

    /**
     * 获取所有巡检任务
     * @param planId 计划id
     * @return 巡检任务列表
     */
    List<InspectionTaskDTO> listAll(@Param("planId") String planId);

    /**
     * 获取所有巡检任务产生的工单
     * @param taskIds 任务id集合
     * @return 巡检任务列表产生的工单
     */
    List<TaskInstDTO> listInstByTaskId(@Param("taskIds") List<String> taskIds);

    /**
     * 获取今日任务
     * @param params 任务状态
     * @return 今日任务
     */
    List<Map<String, Object>> listToday(Map<String, Object> params);

    /**
     * 获取任务关联的工单
     * @param params 任务日期
     * @return 关联工单
     */
    List<Map<String, Object>> listInst(Map<String, Object> params);

    /**
     * 批量创建任务
     * @param tasks 任务列表
     */
    void insertBatch(@Param("tasks") List<InspectionTask> tasks);

    /**
     * 批量修改任务
     * @param ids 任务id
     * @param task 任务
     */
    void updateBatch(@Param("ids") String[] ids, @Param("task") InspectionTask task);

    /**
     * 获取巡检人
     * @param orgId 当前组织机构id
     */
    List<IUser> getInspectors(@Param("orgId") String orgId, @Param("role") String role);

    /**
     * 根据任务id获取模板
     * @param taskId 任务id
     */
    InspectionTaskDTO getWithTemplate(@Param("taskId") String taskId);

    /**
     * 根据计划ID将未接收的任务设置成有效无效
     * @param planId 计划ID
     * @param enabled 是否有效
     * @return 更新数量
     */
    int updateUnreceivedTasksByPlanId(@Param("planId") String planId, @Param("enabled") int enabled);

    int updateUnreceivedTasksByPlanIdAndTime(@Param("planId") String planId, @Param("enabled") int enabled, @Param("restartTime") Date restartTime);

    int count(String planId);

    List<InspectionTaskDTO> getPendingByTaskTime(@Param("expireTime") Date expireTime);

    List<InspectionTaskDTO> getProcessingByTaskTime(@Param("expireTime") Date expireTime);

    void setDisableByPlanId(String planId);

    void deleteByPlanIdAndTaskTime(@Param("planId") String planId, @Param("taskTime") Date taskTime);

    /**
     * 按照状态统计数量
     * @return 统计数据
     */
    List<Map<String, Object>> countStateAll(Map<String, String> params);

    /**
     * 按照实例状态、模板类型统计数量
     * @return 统计数据
     */
    List<Map<String, Object>> countInstAll(Map<String, String> params);

    /**
     * 按照日期统计指标、实例状态的数量
     * @return 统计数据
     */
    List<Map<String, Object>> countIndicatorAndInst(Map<String, String> params);

    /**
     * 按照状态统计数量
     * @return 统计数据
     */
    List<Map<String, Object>> countState(Map<String, Object> params);

    /**
     * 统计实例数量
     * @return 统计数据
     */
    List<Map<String, Object>> countInstState(Map<String, Object> params);

    /**
     * 根据 instId 获取工单详情
     * @param instId 实例id
     * @return 工单详情
     */
    Map<String, Object> getWorkOrder(@Param("instId") String instId);

    /**
     * 根据 instId 获取工单意见
     * @param instId 实例id
     * @return 工单意见
     */
    List<Map<String, Object>> getInstOpinion(@Param("instId") String instId);

}
