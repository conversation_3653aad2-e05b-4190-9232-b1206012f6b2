package cn.gwssi.ecloudframework.module.efficiency.api.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.List;

/**
 * 资源效能规则DTO
 */
@Data
public class ResourceEfficiencyDTO extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * CPU低阈值
     */
    private Integer cpuLowThreshold;

    /**
     * CPU高阈值
     */
    private Integer cpuHighThreshold;

    /**
     * 内存低阈值
     */
    private Integer memoryLowThreshold;

    /**
     * 内存高阈值
     */
    private Integer memoryHighThreshold;

    /**
     * 磁盘低阈值
     */
    private Integer diskLowThreshold;

    /**
     * 磁盘高阈值
     */
    private Integer diskHighThreshold;

    /**
     * 有效业务段开始时间
     */
    private String startTime;

    /**
     * 有效业务段结束时间
     */
    private String endTime;

    /**
     * 任意高负荷阈值累计占比
     */
    private Integer highRate;

    /**
     * 任意低负荷阈值累计占比
     */
    private Integer lowRate;

    /**
     * 服务器群组（JSON格式）
     */
    private String serverGroups;

    /**
     * 服务器群组列表（用于前端展示）
     */
    private List<String> serverGroupList;

    /**
     * 关联的服务器数量
     */
    private Integer serverCount;

    /**
     * 规则状态（用于查询条件）
     */
    private Integer status;
}
