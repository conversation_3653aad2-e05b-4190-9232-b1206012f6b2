package cn.gwssi.ecloudframework.module.efficiency.api.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 资源能效结果DTO
 */
@Data
public class ResourceResultDTO extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 关系表ID
     */
    private String relationId;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * 关联应用名称
     */
    private String applicationName;

    /**
     * 关联应用ID
     */
    private String applicationId;

    /**
     * CPU是否是高负荷
     */
    private String cpuHighThreshold;

    /**
     * CPU是否是低负荷
     */
    private String cpuLowThreshold;

    /**
     * 内存是否是高负荷
     */
    private String memoryHighThreshold;

    /**
     * 内存是否是低负荷
     */
    private String memoryLowThreshold;

    /**
     * 磁盘是否是高负荷
     */
    private String diskHighThreshold;

    /**
     * 磁盘是否是低负荷
     */
    private String diskLowThreshold;

    /**
     * CPU高负荷累计天数
     */
    private Integer cpuDays;

    /**
     * 内存高负荷累计天数
     */
    private Integer memoryDays;

    /**
     * 磁盘高负荷累计天数
     */
    private Integer diskDays;

    /**
     * 数据处理时间
     */
    private Date processDate;

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 规则名称（用于查询显示）
     */
    private String ruleName;

    /**
     * 综合负荷状态（用于前端显示）
     */
    private String overallStatus;

    /**
     * 建议操作（用于前端显示）
     */
    private String recommendation;
}
