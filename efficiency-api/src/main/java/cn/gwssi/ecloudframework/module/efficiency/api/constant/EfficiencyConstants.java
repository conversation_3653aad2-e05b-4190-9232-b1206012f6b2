package cn.gwssi.ecloudframework.module.efficiency.api.constant;

/**
 * 系统能效模块常量类
 */
public interface EfficiencyConstants {
    
    /**
     * 资源类型
     */
    enum ResourceType {
        /**
         * 物理机
         */
        PHYSICAL("物理机"),
        /**
         * 容器
         */
        CONTAINER("容器");

        private final String text;

        ResourceType(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 负荷状态
     */
    enum LoadStatus {
        /**
         * 高负荷
         */
        HIGH("高负荷"),
        /**
         * 低负荷
         */
        LOW("低负荷"),
        /**
         * 正常
         */
        NORMAL("正常");

        private final String text;

        LoadStatus(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 阈值判断结果
     */
    enum ThresholdResult {
        /**
         * 是
         */
        YES("是"),
        /**
         * 否
         */
        NO("否");

        private final String text;

        ThresholdResult(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 当日状态
     */
    enum SameDayStatus {
        /**
         * 正常
         */
        NORMAL("正常"),
        /**
         * 异常
         */
        ABNORMAL("异常"),
        /**
         * 待处理
         */
        PENDING("待处理");

        private final String text;

        SameDayStatus(String text) {
            this.text = text;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 规则状态
     */
    enum RuleStatus {
        /**
         * 启用
         */
        ENABLED(1),
        /**
         * 停用
         */
        DISABLED(0);

        private final int value;

        RuleStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 默认采集频率（分钟）
     */
    int DEFAULT_COLLECTION_FREQ = 5;

    /**
     * 默认时间格式
     */
    String DEFAULT_TIME_FORMAT = "HH:mm";

    /**
     * 默认日期格式
     */
    String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 默认日期时间格式
     */
    String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 百分比转换基数
     */
    int PERCENTAGE_BASE = 100;

    /**
     * 浮点数精度
     */
    int FLOAT_PRECISION = 2;
}
