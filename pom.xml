<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.gwssi.ecloud</groupId>
    <version>0.1-SNAPSHOT</version>
    <artifactId>ecloud-module-efficiency</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>efficiency-api</module>
        <module>efficiency-core</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <ecloud-module.version>0.1-SNAPSHOT</ecloud-module.version>
    </properties>


    <!-- deploy 服务器 -->
    <distributionManagement>
        <repository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>beacon-snapshot</id>
            <name>beacon-snapshot</name>
            <url>http://nexus.e.cloud:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>beacon-central</id>
            <name>beacon-central</name>
            <url>http://nexus.e.cloud:8081/repository/maven-central/</url>
        </repository>
        <repository>
            <id>beacon-snapshot</id>
            <name>beacon-snapshot</name>
            <url>http://nexus.e.cloud:8081/repository/maven-snapshots/</url>
        </repository>
    </repositories>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>ecloud-module</artifactId>
                <version>${ecloud-module.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>efficiency-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>ecloud-goffice-common</artifactId>
                <version>${ecloud-module.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>component-j2cache</artifactId>
                <version>${ecloud-module.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <charset>${project.build.sourceEncoding}</charset>
                        <docencoding>${project.build.sourceEncoding}</docencoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
