<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.gwssi.ecloud</groupId>
        <artifactId>ecloud-springboot</artifactId>
        <version>0.3-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>newgwuomp-backend</artifactId>
    <packaging>pom</packaging>
    <name>newgwuomp-backend</name>
    <modules>
        <module>project-service</module>
    </modules>

    <properties>
        <module-dependencies.version>newgwuomp-dev-SNAPSHOT</module-dependencies.version>
        <swagger.version>1.5.10</swagger.version>
        <lombok.version>1.18.24</lombok.version>
    </properties>

    <!-- deploy 服务器 -->
    <distributionManagement>
        <repository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>beacon-snapshot</id>
            <name>beacon-snapshot</name>
            <url>http://nexus.e.cloud:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>beacon-central</id>
            <name>beacon-central</name>
            <url>http://nexus.e.cloud:8081/repository/maven-central/</url>
        </repository>
        <repository>
            <id>beacon-snapshot</id>
            <name>beacon-snapshot</name>
            <url>http://nexus.e.cloud:8081/repository/maven-snapshots/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </pluginRepository>
    </pluginRepositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>ecloud-module-dependencies</artifactId>
                <version>${module-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>ecloud-framework</artifactId>
                <version>${ecloud-main.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>org-core</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>org-api</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>base-rest</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>base-db</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>sys-api</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>wf-api</artifactId>
                <version>${ecloud-main.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>demo-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.gwssi.ecloud</groupId>
                <artifactId>restclient</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>css-slw</groupId>
                <artifactId>platform</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>css-slw</groupId>
                <artifactId>base</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.9</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
</project>
