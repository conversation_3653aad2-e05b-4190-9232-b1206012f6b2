<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompOrgGroupMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompOrgGroup">
    <!--@mbg.generated-->
    <!--@Table uomp_org_group-->
    <id column="ID_" jdbcType="VARCHAR" property="id" />
    <result column="NAME_" jdbcType="VARCHAR" property="name" />
    <result column="PARENT_ID_" jdbcType="VARCHAR" property="parentId" />
    <result column="SN_" jdbcType="INTEGER" property="sn" />
    <result column="CODE_" jdbcType="VARCHAR" property="code" />
    <result column="TYPE_" jdbcType="VARCHAR" property="type" />
    <result column="DESC_" jdbcType="VARCHAR" property="desc" />
    <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_BY_" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_TIME_" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_BY_" jdbcType="VARCHAR" property="updateBy" />
    <result column="PATH_" jdbcType="VARCHAR" property="path" />
    <result column="SIMPLE_" jdbcType="VARCHAR" property="simple" />
    <result column="STATUS_" jdbcType="INTEGER" property="status" />
    <result column="SHOW_NAME_" jdbcType="VARCHAR" property="showName" />
    <result column="VIRTUAL_" jdbcType="INTEGER" property="virtual" />
    <result column="HISTORY_NAME_" jdbcType="VARCHAR" property="historyName" />
    <result column="PATH_NAME_" jdbcType="VARCHAR" property="pathName" />
    <result column="MCODE_" jdbcType="VARCHAR" property="mcode" />
    <result column="RESP_NAME" jdbcType="VARCHAR" property="respName" />
    <result column="RESP_ID" jdbcType="VARCHAR" property="respId" />
    <result column="ORG_GROUP_ID" jdbcType="VARCHAR" property="orgGroupId" />
  </resultMap>

  <resultMap id="GroupUomp" type="cn.gwssi.ecloud.staffpool.core.model.GroupUomp">
    <id property="id" column="id_" jdbcType="VARCHAR"/>
    <result property="name" column="name_" jdbcType="VARCHAR"/>
    <result property="parentId" column="parent_id_" jdbcType="VARCHAR"/>
    <result property="code" column="code_" jdbcType="VARCHAR"/>
    <result property="type" column="type_" jdbcType="VARCHAR"/>
    <result property="desc" column="desc_" jdbcType="VARCHAR"/>
    <result property="path" column="path_" jdbcType="VARCHAR"/>
    <result property="sn" column="sn_" jdbcType="INTEGER"/>
    <result property="createTime" column="create_time_" jdbcType="TIMESTAMP"/>
    <result property="createBy" column="create_by_" jdbcType="VARCHAR"/>
    <result property="updateTime" column="update_time_" jdbcType="TIMESTAMP"/>
    <result property="updateBy" column="update_by_" jdbcType="VARCHAR"/>
    <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
    <result property="simple" column="simple_" jdbcType="VARCHAR"/>
    <result property="status" column="status_" jdbcType="INTEGER"/>
    <result property="respName" column="resp_name" jdbcType="VARCHAR"/>
    <result property="respId" column="resp_id" jdbcType="VARCHAR"/>
    <result property="orgGroupId" column="org_group_id" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="tableName">
    uomp_org_group
  </sql>

  <sql id="forUpdate">
    <if test="name != null">
      name_ = #{name,jdbcType=VARCHAR},
    </if>
    <if test="parentId != null">
      parent_id_ = #{parentId,jdbcType=VARCHAR},
    </if>
    <if test="code != null">
      code_ = #{code,jdbcType=VARCHAR},
    </if>
    <if test="type != null">
      type_ = #{type,jdbcType=VARCHAR},
    </if>
    <if test="desc != null">
      desc_ = #{desc,jdbcType=VARCHAR},
    </if>
    <if test="path != null">
      path_ = #{path,jdbcType=VARCHAR},
    </if>
    <if test="sn != null">
      sn_ = #{sn,jdbcType=INTEGER},
    </if>
    <if test="simple != null">
      simple_ = #{simple,jdbcType=VARCHAR},
    </if>
    <if test="status != null">
      status_ = #{status,jdbcType=INTEGER},
    </if>
    <if test="updateBy != null">
      update_by_ = #{updateBy,jdbcType=VARCHAR},
    </if>
    <if test="updateTime != null">
      update_time_ = #{updateTime,jdbcType=TIMESTAMP},
    </if>
  </sql>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID_, NAME_, PARENT_ID_, SN_, CODE_, TYPE_, DESC_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_,
    UPDATE_BY_, PATH_, SIMPLE_, STATUS_, SHOW_NAME_, VIRTUAL_, HISTORY_NAME_, PATH_NAME_,
    MCODE_, RESP_NAME, RESP_ID, ORG_GROUP_ID
  </sql>

  <select id="get" parameterType="java.lang.String" resultMap="GroupUomp">
    SELECT * FROM
    <include refid="tableName"/>
    WHERE
    id_=#{id}
  </select>

  <select id="query" parameterType="java.util.Map" resultMap="GroupUomp">
    SELECT
    <if test="resultType==null">
      distinct torg.*
    </if>
    <if test="resultType=='forTree'">
      torg.id_,torg.name_,torg.parent_id_,torg.path_,torg.sn_, torg.org_group_id,torg.resp_name, torg.resp_id
    </if>
    <if test="resultType=='onlyGroupId'">
      distinct torg.id_,torg.sn_
    </if>
    <if test="resultType=='onlyGroupName'">
      distinct torg.id_,torg.name_,torg.sn_
    </if>
    <if test="resultType=='withUserNum'">
      distinct torg.*,groupUserNum.num as userNum
    </if>
    FROM
    <include refid="tableName"/>
    torg
    <if test="userId != null">
      inner join org_relation trelation on torg.id_ = trelation.group_id_
      and trelation.status_=1
      and trelation.type_='groupUser' and trelation.user_id_ = #{userId}
    </if>
    <if test="orgIds!=null">
      <if test="orgHasChild==true">
        inner join ORG_GROUP torgM on (torg.PATH_ like concat(torgM.PATH_,'%') and torgM.id_ in
        <if test="null != orgIds and orgIds.size > 0">
          <foreach collection="orgIds" item="orgId" index="index" open="(" close=")" separator=",">
            #{orgId}
          </foreach>)
        </if>
        <if test="null != orgIds and orgIds.size == 0">
          (''))
        </if>
        <if test="null != noHasChildOrgIds">
          or torg.id_ in
          <if test="null != noHasChildOrgIds and noHasChildOrgIds.size > 0">
            <foreach collection="noHasChildOrgIds" item="orgId" index="index" open="(" close=")"
                     separator=",">
              #{orgId}
            </foreach>
          </if>
          <if test="null != noHasChildOrgIds and noHasChildOrgIds.size == 0">
            ('')
          </if>
        </if>
      </if>
    </if>
    <if test="orgCodes!=null">
      <if test="orgHasChild==true">
        inner join ORG_GROUP torgMC on torg.PATH_ like concat(torgMC.PATH_,'%') and torgMC.code_ in
        <if test="null != orgCodes and orgCodes.size > 0">
          <foreach collection="orgCodes" item="orgCode" index="index" open="(" close=")" separator=",">
            #{orgCode}
          </foreach>
        </if>
        <if test="null != orgCodes and orgCodes.size == 0">
          ('')
        </if>
      </if>
    </if>
    <if test="resultType=='withUserNum'">
      LEFT JOIN (
      SELECT
      zz.ID_,
      count(1) AS num
      FROM
      (
      SELECT distinct
      z.ID_,
      c.ID_ AS user_id_
      FROM
      ORG_GROUP z,
      ORG_GROUP a,
      ORG_RELATION b,
      ORG_USER c
      WHERE
      a.path_ LIKE concat(z.path_, '%')
      AND a.ID_ = b.GROUP_ID_
      AND b.USER_ID_ = c.ID_
      AND c.ACTIVE_STATUS_ = '1'
      AND c.TYPE_ = '1'
      ) zz GROUP BY zz.ID_
      ) groupUserNum ON torg.ID_ = groupUserNum.ID_
    </if>
    <where>
      <if test="orgIds!=null">
        <if test="null==orgHasChild||orgHasChild==false">
          and torg.id_ in
          <if test="null != orgIds and orgIds.size > 0">
            <foreach collection="orgIds" item="orgId" index="index" open="(" close=")" separator=",">
              #{orgId}
            </foreach>
          </if>
          <if test="null != orgIds and orgIds.size == 0">
            ('')
          </if>
        </if>
      </if>
      <if test="orgCodes!=null">
        <if test="null==orgHasChild||orgHasChild==false">
          and torg.code_ in
          <if test="null != orgCodes and orgCodes.size > 0">
            <foreach collection="orgCodes" item="orgCode" index="index" open="(" close=")" separator=",">
              #{orgCode}
            </foreach>
          </if>
          <if test="null != orgCodes and orgCodes.size == 0">
            ('')
          </if>
        </if>
      </if>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
    <if test="orderBySql==null">
      ORDER BY sn_ ASC
    </if>
  </select>

  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.model.GroupUomp">
    INSERT INTO
    <include refid="tableName"/>
    (id_,name_,parent_id_,code_,type_,desc_,path_,sn_,create_time_,create_by_,update_time_,update_by_,simple_,status_,show_name_,virtual_,history_name_,path_name_,mcode_,resp_name,resp_id,org_group_id)
    VALUES
    (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
    #{type,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, #{sn,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
    #{updateBy,jdbcType=VARCHAR},#{simple,jdbcType=VARCHAR},#{status,jdbcType=INTEGER},#{showName,jdbcType=VARCHAR},
    #{virtual,jdbcType=INTEGER},#{historyName,jdbcType=VARCHAR},#{pathName,jdbcType=VARCHAR},#{mcode,jdbcType=VARCHAR},#{respName,jdbcType=VARCHAR},#{respId,jdbcType=VARCHAR},#{orgGroupId,jdbcType=VARCHAR})
  </insert>

  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.model.GroupUomp">
    UPDATE
    <include refid="tableName"/>
    SET
    name_=#{name,jdbcType=VARCHAR},
    parent_id_=#{parentId,jdbcType=VARCHAR},
    code_=#{code,jdbcType=VARCHAR},
    type_=#{type,jdbcType=VARCHAR},
    desc_=#{desc,jdbcType=VARCHAR},
    path_=#{path,jdbcType=VARCHAR},
    sn_=#{sn,jdbcType=INTEGER},
    simple_=#{simple,jdbcType=VARCHAR},
    show_name_ = #{showName,jdbcType=VARCHAR},
    status_=#{status,jdbcType=INTEGER},
    virtual_ = #{virtual,jdbcType=INTEGER},
    history_name_ = #{historyName,jdbcType=VARCHAR},
    mcode_ = #{mcode,jdbcType=VARCHAR},
    update_by_=#{updateBy,jdbcType=VARCHAR},
    update_time_=#{updateTime,jdbcType=TIMESTAMP},
    resp_name=#{respName,jdbcType=VARCHAR},
    resp_id=#{respId,jdbcType=VARCHAR},
    org_group_id=#{orgGroupId,jdbcType=VARCHAR}
    WHERE
    id_=#{id}
  </update>

  <update id="chageOrder" parameterType="cn.gwssi.ecloudframework.org.core.model.Group">
    UPDATE
    uomp_org_group
    SET
    sn_=#{sn,jdbcType=INTEGER}
    WHERE
    id_=#{id}
  </update>

  <select id="getByCode" parameterType="map" resultMap="GroupUomp">
    SELECT * FROM
    <include refid="tableName"/>
    WHERE
    code_=#{code}
    <if test="excludeId!=null">
      and id_ != #{excludeId}
    </if>
  </select>

  <select id="getAllSelective" resultMap="GroupUomp">
      select id_,
             name_,
             parent_id_,
             code_,
             type_,
             desc_,
             path_,
             sn_,
             create_time_,
             create_by_,
             update_time_,
             update_by_,
             simple_,
             status_,
             org_group_id,
             resp_name,
             resp_id
      from uomp_org_group
  </select>

  <delete id="remove">
    delete from uomp_org_group
    where ID_ = #{id,jdbcType=VARCHAR}
  </delete>

  <select id="getChildByPath" parameterType="java.lang.String" resultMap="GroupUomp">
    SELECT * FROM
    <include refid="tableName"/>
    WHERE
    path_ like #{path}
  </select>

<!--auto generated by MybatisCodeHelper on 2024-06-06-->
  <update id="updateById">
    update uomp_org_group
    <set>
      <if test="updated.name != null">
        NAME_ = #{updated.name,jdbcType=VARCHAR},
      </if>
      <if test="updated.parentId != null">
        PARENT_ID_ = #{updated.parentId,jdbcType=VARCHAR},
      </if>
      <if test="updated.sn != null">
        SN_ = #{updated.sn,jdbcType=INTEGER},
      </if>
      <if test="updated.code != null">
        CODE_ = #{updated.code,jdbcType=VARCHAR},
      </if>
      <if test="updated.type != null">
        TYPE_ = #{updated.type,jdbcType=VARCHAR},
      </if>
      <if test="updated.desc != null">
        DESC_ = #{updated.desc,jdbcType=VARCHAR},
      </if>
      <if test="updated.createTime != null">
        CREATE_TIME_ = #{updated.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.createBy != null">
        CREATE_BY_ = #{updated.createBy,jdbcType=VARCHAR},
      </if>
      <if test="updated.updateTime != null">
        UPDATE_TIME_ = #{updated.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updateBy != null">
        UPDATE_BY_ = #{updated.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updated.path != null">
        PATH_ = #{updated.path,jdbcType=VARCHAR},
      </if>
      <if test="updated.simple != null">
        SIMPLE_ = #{updated.simple,jdbcType=VARCHAR},
      </if>
      <if test="updated.status != null">
        STATUS_ = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.showName != null">
        SHOW_NAME_ = #{updated.showName,jdbcType=VARCHAR},
      </if>
      <if test="updated.virtual != null">
        VIRTUAL_ = #{updated.virtual,jdbcType=INTEGER},
      </if>
      <if test="updated.historyName != null">
        HISTORY_NAME_ = #{updated.historyName,jdbcType=VARCHAR},
      </if>
      <if test="updated.pathName != null">
        PATH_NAME_ = #{updated.pathName,jdbcType=VARCHAR},
      </if>
      <if test="updated.mcode != null">
        MCODE_ = #{updated.mcode,jdbcType=VARCHAR},
      </if>
      <if test="updated.respName != null">
        RESP_NAME = #{updated.respName,jdbcType=VARCHAR},
      </if>
      <if test="updated.respId != null">
        RESP_ID = #{updated.respId,jdbcType=VARCHAR},
      </if>
      <if test="updated.orgGroupId != null">
        ORG_GROUP_ID = #{updated.orgGroupId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID_=#{id,jdbcType=VARCHAR}
  </update>

  <sql id="selectVirtual" databaseId="mysql">
        desc_ as 'desc',
        virtual_     as 'virtual',
  </sql>
  <sql id="selectVirtual">
        desc_ as "desc",
        virtual_     as "virtual",
  </sql>

  <select id="getAll" resultType="cn.gwssi.ecloud.staffpool.core.model.GroupUomp">
      select id_          as id,
             name_        as name,
             parent_id_   as parentId,
             code_        as code,
             type_        as type,
            path_ as path,
             sn_          as sn,
             <include refid="selectVirtual"/>
             create_time_ as createTime,
             create_by_   as createBy,
             update_time_ as updateTime,
             update_by_   as updateBy,
             simple_      as simple,
             status_      as status,
             org_group_id as orgGroupId,
             resp_name    as respName,
             resp_id      as respId
      from
      <include refid="tableName"/>
      where 1=1
        <if test="virtual != null and virtual != ''">
          and VIRTUAL_ = #{virtual}
        </if>
        <if test="status != null and status != ''">
          and STATUS_ = #{status}
        </if>
    order by sn_
  </select>

  <select id="getByPath" resultType="java.lang.String">
    select ID_ from uomp_org_group uog where path_ like concat('%', #{orgGroupId}, '%')
  </select>

  <select id="selectIdByType" resultType="java.lang.String">
    select ID_ from uomp_org_group
    <where>
      <if test="equal != null and equal == 1">
        and TYPE_ = #{type}
      </if>
      <if test="equal != null and equal == 2">
        and TYPE_ != #{type}
      </if>
    </where>
  </select>

  <select id="getOrderGroup" resultType="cn.gwssi.ecloud.staffpool.core.model.OrderGroup">
    select org_group_id          as id,
    name_        as name,
    org_group_id   as orgId,
    org_group_id        as code,
    'org'        as type
    from
    <include refid="tableName"/>
    where STATUS_ = '1'

  </select>
</mapper>
