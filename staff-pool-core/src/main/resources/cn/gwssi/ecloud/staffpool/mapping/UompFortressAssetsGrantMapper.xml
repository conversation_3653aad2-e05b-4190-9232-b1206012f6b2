<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompFortressAssetsGrantMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssetsGrant">
    <!--@mbg.generated-->
    <!--@Table uomp_fortress_assets_grant-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FORT_ID" jdbcType="VARCHAR" property="fortId" />
    <result column="FORT_ASSET_ID" jdbcType="VARCHAR" property="fortAssetId" />
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
    <result column="IP_ADDR" jdbcType="VARCHAR" property="ipAddr" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="FORT_USER_ID" jdbcType="VARCHAR" property="fortUserId" />
    <result column="PERMISSION_ID" jdbcType="VARCHAR" property="permissionId" />
    <result column="PERMISSION" jdbcType="VARCHAR" property="permission" />
    <result column="BEGIN_TIME" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FORT_ID, FORT_ASSET_ID, RESOURCE_ID, IP_ADDR, USER_ID, FORT_USER_ID, PERMISSION_ID, 
    PERMISSION, BEGIN_TIME, END_TIME, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
    DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_fortress_assets_grant
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_fortress_assets_grant
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssetsGrant">
    <!--@mbg.generated-->
    insert into uomp_fortress_assets_grant (ID, FORT_ID, FORT_ASSET_ID, 
      RESOURCE_ID, IP_ADDR, USER_ID, 
      FORT_USER_ID, PERMISSION_ID, PERMISSION, 
      BEGIN_TIME, END_TIME, REMARK, 
      CREATE_BY, CREATE_TIME, UPDATE_BY, 
      UPDATE_TIME, DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{fortId,jdbcType=VARCHAR}, #{fortAssetId,jdbcType=VARCHAR}, 
      #{resourceId,jdbcType=VARCHAR}, #{ipAddr,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{fortUserId,jdbcType=VARCHAR}, #{permissionId,jdbcType=VARCHAR}, #{permission,jdbcType=VARCHAR}, 
      #{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssetsGrant">
    <!--@mbg.generated-->
    insert into uomp_fortress_assets_grant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="fortId != null">
        FORT_ID,
      </if>
      <if test="fortAssetId != null">
        FORT_ASSET_ID,
      </if>
      <if test="resourceId != null">
        RESOURCE_ID,
      </if>
      <if test="ipAddr != null">
        IP_ADDR,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="fortUserId != null">
        FORT_USER_ID,
      </if>
      <if test="permissionId != null">
        PERMISSION_ID,
      </if>
      <if test="permission != null">
        PERMISSION,
      </if>
      <if test="beginTime != null">
        BEGIN_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fortId != null">
        #{fortId,jdbcType=VARCHAR},
      </if>
      <if test="fortAssetId != null">
        #{fortAssetId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="ipAddr != null">
        #{ipAddr,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="fortUserId != null">
        #{fortUserId,jdbcType=VARCHAR},
      </if>
      <if test="permissionId != null">
        #{permissionId,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssetsGrant">
    <!--@mbg.generated-->
    update uomp_fortress_assets_grant
    set FORT_ID = #{fortId,jdbcType=VARCHAR},
      FORT_ASSET_ID = #{fortAssetId,jdbcType=VARCHAR},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      IP_ADDR = #{ipAddr,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      FORT_USER_ID = #{fortUserId,jdbcType=VARCHAR},
      PERMISSION_ID = #{permissionId,jdbcType=VARCHAR},
      PERMISSION = #{permission,jdbcType=VARCHAR},
      BEGIN_TIME = #{beginTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectInfo" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessAuthPermDTO">
       select id,permission,permission_id,begin_time,end_time,to_char(create_time,'yyyymmddhh24mmss') as create_time
        from uomp_fortress_assets_grant
        where user_id = #{userId} and resource_id = #{resourceId}
        and del_flag = '0'
  </select>
  <update id="updateDeletetStatus">
    update uomp_fortress_assets_grant set del_flag = '1',update_time = sysdate(),update_by = #{userId} where id = #{id}
  </update>
  <update id="updatePermission">
    update uomp_fortress_assets_grant set permission = #{permission},update_time = sysdate(),update_by = #{userId} where id = #{id}
  </update>
  <insert id="insertInfo" parameterType="java.util.Map">
      insert into uomp_fortress_assets_grant (id, fort_id, fort_asset_id, resource_id, ip_addr, user_id,
      fort_user_id, permission_id, permission, begin_time, end_time, create_by, create_time, update_by, update_time, del_flag)
      values(#{param.uuid}, #{param.fort_id}, #{param.fort_asset_id}, #{param.resource_id}, #{param.ip_addr}, #{param.user_id}, #{param.fort_user_id},
      #{param.permission_id}, #{param.permission}, #{param.empower_begin_time}, #{param.empower_end_time},
      #{param.user_id}, sysdate(), #{param.user_id}, sysdate(), '0')
  </insert>

  <select id="selectOutPermissionByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.OutPermissionBean">
      select '堡垒机'                                as outSysType,
             f.FORT_TYPE                             as fortType,
             f.FORT_NAME                             as fortName,
             g.PERMISSION                            as permission,
             g.FORT_ASSET_ID                         as fortAssetId,
             g.BEGIN_TIME                            as beginTime,
             g.END_TIME                              as endTime,
             concat(r.RESOURCE_NAME, '-', g.IP_ADDR) as resourceName
      from UOMP_FORTRESS_ASSETS_GRANT g
               left join UOMP_FORTRESS f on g.FORT_ID = f.ID
               left join CMDB_COMM_RESOURCE r on r.ID = g.RESOURCE_ID
      where g.USER_ID = #{userId}
        and g.DEL_FLAG = '0'
  </select>

  <select id="countByUserId" resultType="java.lang.Integer">
      select count(*) as num
      from UOMP_FORTRESS_ASSETS_GRANT g
               left join UOMP_FORTRESS f on g.FORT_ID = f.ID
               left join CMDB_COMM_RESOURCE r on r.ID = g.RESOURCE_ID
      where g.USER_ID = #{userId}
        and g.DEL_FLAG = '0'
  </select>
</mapper>