<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompAdmissionApplicationMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication">
        <!--@mbg.generated-->
        <!--@Table uomp_admission_application-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="APPLY_CODE" jdbcType="VARCHAR" property="applyCode"/>
        <result column="APPLY_TITLE" jdbcType="VARCHAR" property="applyTitle"/>
        <result column="APPLY_USER_ID" jdbcType="VARCHAR" property="applyUserId"/>
        <result column="APPLY_USER_NAME" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="APPLY_MATTER" jdbcType="VARCHAR" property="applyMatter"/>
        <result column="MANAGER_COMMENT" jdbcType="VARCHAR" property="managerComment"/>
        <result column="LEADER_COMMENT" jdbcType="VARCHAR" property="leaderComment"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        APPLY_CODE,
        APPLY_TITLE,
        APPLY_USER_ID,
        APPLY_USER_NAME,
        APPLY_TIME,
        APPLY_MATTER,
        MANAGER_COMMENT,
        LEADER_COMMENT,
        INST_ID,
        APPLY_STATUS,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG
    </sql>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication">
        <!--@mbg.generated-->
        insert into uomp_admission_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="applyCode != null">
                APPLY_CODE,
            </if>
            <if test="applyTitle != null">
                APPLY_TITLE,
            </if>
            <if test="applyUserId != null">
                APPLY_USER_ID,
            </if>
            <if test="applyUserName != null">
                APPLY_USER_NAME,
            </if>
            <if test="applyTime != null">
                APPLY_TIME,
            </if>
            <if test="applyMatter != null">
                APPLY_MATTER,
            </if>
            <if test="managerComment != null">
                MANAGER_COMMENT,
            </if>
            <if test="leaderComment != null">
                LEADER_COMMENT,
            </if>
            <if test="instId != null">
                INST_ID,
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyCode != null">
                #{applyCode,jdbcType=VARCHAR},
            </if>
            <if test="applyTitle != null">
                #{applyTitle,jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                #{applyUserId,jdbcType=VARCHAR},
            </if>
            <if test="applyUserName != null">
                #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyMatter != null">
                #{applyMatter,jdbcType=VARCHAR},
            </if>
            <if test="managerComment != null">
                #{managerComment,jdbcType=VARCHAR},
            </if>
            <if test="leaderComment != null">
                #{leaderComment,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2024-05-25-->
    <update id="updateById">
        update uomp_admission_application
        <set>
            <if test="id != null">
                ID = #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyCode != null">
                APPLY_CODE = #{applyCode,jdbcType=VARCHAR},
            </if>
            <if test="applyTitle != null">
                APPLY_TITLE = #{applyTitle,jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                APPLY_USER_ID = #{applyUserId,jdbcType=VARCHAR},
            </if>
            <if test="applyUserName != null">
                APPLY_USER_NAME = #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyMatter != null">
                APPLY_MATTER = #{applyMatter,jdbcType=VARCHAR},
            </if>
            <if test="managerComment != null">
                MANAGER_COMMENT = #{managerComment,jdbcType=VARCHAR},
            </if>
            <if test="leaderComment != null">
                LEADER_COMMENT = #{leaderComment,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                INST_ID = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS = #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-05-28-->
    <select id="selectAllById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uomp_admission_application
        where ID = #{id,jdbcType=VARCHAR}
    </select>
</mapper>