<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompContractManagementMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement">
    <!--@mbg.generated-->
    <!--@Table uomp_contract_management-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode" />
    <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName" />
    <result column="SIGNING_DATE" jdbcType="TIMESTAMP" property="signingDate" />
    <result column="CONTRACT_TYPE" jdbcType="VARCHAR" property="contractType" />
    <result column="CONTRACT_STATUS" jdbcType="VARCHAR" property="contractStatus" />
    <result column="PARTY_A_NAME" jdbcType="VARCHAR" property="partyAName" />
    <result column="PARTY_A_PRINCIPAL_NAME" jdbcType="VARCHAR" property="partyAPrincipalName" />
    <result column="PARTY_B_NAME" jdbcType="VARCHAR" property="partyBName" />
    <result column="PARTY_B_ID" jdbcType="VARCHAR" property="partyBId" />
    <result column="PARTY_B_PRINCIPAL_NAME" jdbcType="VARCHAR" property="partyBPrincipalName" />
    <result column="PARTY_C_NAME" jdbcType="VARCHAR" property="partyCName" />
    <result column="PARTY_C_PRINCIPAL_NAME" jdbcType="VARCHAR" property="partyCPrincipalName" />
    <result column="CONTRACT_CONTENT" jdbcType="VARCHAR" property="contractContent" />
    <result column="PROJECT_MANAGEMENT_ID" jdbcType="VARCHAR" property="projectManagementId" />
    <result column="PROJECT_MANAGEMENT_NAME" jdbcType="VARCHAR" property="projectManagementName" />
    <result column="DUTY_DEPART_ID" jdbcType="VARCHAR" property="dutyDepartId" />
    <result column="DUTY_DEPART_NAME" jdbcType="VARCHAR" property="dutyDepartName" />
    <result column="PAY_TIME" jdbcType="TIMESTAMP" property="payTime" />
    <result column="PAY_STATUS" jdbcType="VARCHAR" property="payStatus" />
    <result column="SERVICE_LEVEL_ID" jdbcType="VARCHAR" property="serviceLevelId" />
    <result column="SERVICE_LEVEL_NAME" jdbcType="VARCHAR" property="serviceLevelName" />
    <result column="QUALITY_BEGIN_DAY" jdbcType="TIMESTAMP" property="qualityBeginDay" />
    <result column="QUALITY_END_DAY" jdbcType="TIMESTAMP" property="qualityEndDay" />
    <result column="QUALITY_CONTENT" jdbcType="VARCHAR" property="qualityContent" />
    <result column="PARTY_A_PRINCIPAL_TEL" jdbcType="VARCHAR" property="partyAPrincipalTel" />
    <result column="PARTY_A_MANAGER" jdbcType="VARCHAR" property="partyAManager" />
    <result column="PARTY_A_MANAGER_TEL" jdbcType="VARCHAR" property="partyAManagerTel" />
    <result column="PARTY_B_PRINCIPAL_TEL" jdbcType="VARCHAR" property="partyBPrincipalTel" />
    <result column="PARTY_B_MANAGER" jdbcType="VARCHAR" property="partyBManager" />
    <result column="PARTY_B_MANAGER_TEL" jdbcType="VARCHAR" property="partyBManagerTel" />
    <result column="SALES_MANAGER" jdbcType="VARCHAR" property="salesManager" />
    <result column="SALES_MANAGER_TEL" jdbcType="VARCHAR" property="salesManagerTel" />
    <result column="PRE_SALES_MANAGER" jdbcType="VARCHAR" property="preSalesManager" />
    <result column="PRE_SALES_MANAGER_TEL" jdbcType="VARCHAR" property="preSalesManagerTel" />
    <result column="PARTY_C_PERSON" jdbcType="VARCHAR" property="partyCPerson" />
    <result column="PARTY_C_PERSON_TEL" jdbcType="VARCHAR" property="partyCPersonTel" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="ENTRY_ID" jdbcType="VARCHAR" property="entryId" />
    <result column="ENTRY_NAME" jdbcType="VARCHAR" property="entryName" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="contract_amount" jdbcType="FLOAT" property="contractAmount"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_CODE, CONTRACT_NAME, SIGNING_DATE, CONTRACT_TYPE, CONTRACT_STATUS, PARTY_A_NAME,
    PARTY_A_PRINCIPAL_NAME, PARTY_B_NAME, PARTY_B_PRINCIPAL_NAME, PARTY_C_NAME, PARTY_C_PRINCIPAL_NAME,
    CONTRACT_CONTENT, PROJECT_MANAGEMENT_ID, PROJECT_MANAGEMENT_NAME, DUTY_DEPART_ID,
    DUTY_DEPART_NAME, PAY_TIME, PAY_STATUS, SERVICE_LEVEL_ID, SERVICE_LEVEL_NAME, QUALITY_BEGIN_DAY,
    QUALITY_END_DAY, QUALITY_CONTENT, PARTY_A_PRINCIPAL_TEL, PARTY_A_MANAGER, PARTY_A_MANAGER_TEL,
    PARTY_B_PRINCIPAL_TEL, PARTY_B_MANAGER, PARTY_B_MANAGER_TEL, SALES_MANAGER, SALES_MANAGER_TEL, PRE_SALES_MANAGER,PRE_SALES_MANAGER_TEL,
    PARTY_C_PERSON, PARTY_C_PERSON_TEL, REMARK, ENTRY_ID, ENTRY_NAME, CREATE_BY, CREATE_TIME,
    CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG,PARTY_B_ID,contract_amount
  </sql>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement">
    <!--@mbg.generated-->
    insert into uomp_contract_management (ID, CONTRACT_CODE, CONTRACT_NAME,
    SIGNING_DATE, CONTRACT_TYPE, CONTRACT_STATUS,
    PARTY_A_NAME, PARTY_A_PRINCIPAL_NAME, PARTY_B_ID,PARTY_B_NAME,
    PARTY_B_PRINCIPAL_NAME, PARTY_C_NAME, PARTY_C_PRINCIPAL_NAME,
    CONTRACT_CONTENT, PROJECT_MANAGEMENT_ID, PROJECT_MANAGEMENT_NAME,
    DUTY_DEPART_ID, DUTY_DEPART_NAME, PAY_TIME,
    PAY_STATUS, SERVICE_LEVEL_ID, SERVICE_LEVEL_NAME,
    QUALITY_BEGIN_DAY, QUALITY_END_DAY, QUALITY_CONTENT,
    PARTY_A_PRINCIPAL_TEL, PARTY_A_MANAGER, PARTY_A_MANAGER_TEL,
    PARTY_B_PRINCIPAL_TEL, PARTY_B_MANAGER, PARTY_B_MANAGER_TEL,
    SALES_MANAGER,SALES_MANAGER_TEL, PRE_SALES_MANAGER, PRE_SALES_MANAGER_TEL,PARTY_C_PERSON,
    PARTY_C_PERSON_TEL, REMARK, ENTRY_ID,
    ENTRY_NAME, CREATE_BY, CREATE_TIME,
    CREATE_ORG_ID,DEL_FLAG,contract_amount)
    values (#{id,jdbcType=VARCHAR}, #{contractCode,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR},
    #{signingDate,jdbcType=TIMESTAMP}, #{contractType,jdbcType=VARCHAR}, #{contractStatus,jdbcType=VARCHAR},
    #{partyAName,jdbcType=VARCHAR}, #{partyAPrincipalName,jdbcType=VARCHAR}, #{partyBId,jdbcType=VARCHAR}, #{partyBName,jdbcType=VARCHAR},
    #{partyBPrincipalName,jdbcType=VARCHAR}, #{partyCName,jdbcType=VARCHAR}, #{partyCPrincipalName,jdbcType=VARCHAR},
    #{contractContent,jdbcType=VARCHAR}, #{projectManagementId,jdbcType=VARCHAR}, #{projectManagementName,jdbcType=VARCHAR},
    #{dutyDepartId,jdbcType=VARCHAR}, #{dutyDepartName,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP},
    #{payStatus,jdbcType=VARCHAR}, #{serviceLevelId,jdbcType=VARCHAR}, #{serviceLevelName,jdbcType=VARCHAR},
    #{qualityBeginDay,jdbcType=TIMESTAMP}, #{qualityEndDay,jdbcType=TIMESTAMP}, #{qualityContent,jdbcType=VARCHAR},
    #{partyAPrincipalTel,jdbcType=VARCHAR}, #{partyAManager,jdbcType=VARCHAR}, #{partyAManagerTel,jdbcType=VARCHAR},
    #{partyBPrincipalTel,jdbcType=VARCHAR}, #{partyBManager,jdbcType=VARCHAR}, #{partyBManagerTel,jdbcType=VARCHAR},
    #{salesManager,jdbcType=VARCHAR}, #{salesManagerTel,jdbcType=VARCHAR}, #{preSalesManager,jdbcType=VARCHAR}, #{preSalesManagerTel,jdbcType=VARCHAR},
    #{partyCPerson,jdbcType=VARCHAR},
    #{partyCPersonTel,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{entryId,jdbcType=VARCHAR},
    #{entryName,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{createOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR},#{contractAmount,jdbcType=FLOAT})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement">
    <!--@mbg.generated-->
    insert into uomp_contract_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="contractCode != null">
        CONTRACT_CODE,
      </if>
      <if test="contractName != null">
        CONTRACT_NAME,
      </if>
      <if test="signingDate != null">
        SIGNING_DATE,
      </if>
      <if test="contractType != null">
        CONTRACT_TYPE,
      </if>
      <if test="contractStatus != null">
        CONTRACT_STATUS,
      </if>
      <if test="partyAName != null">
        PARTY_A_NAME,
      </if>
      <if test="partyAPrincipalName != null">
        PARTY_A_PRINCIPAL_NAME,
      </if>
      <if test="partyBId != null">
        PARTY_B_ID,
      </if>
      <if test="partyBName != null">
        PARTY_B_NAME,
      </if>
      <if test="partyBPrincipalName != null">
        PARTY_B_PRINCIPAL_NAME,
      </if>
      <if test="partyCName != null">
        PARTY_C_NAME,
      </if>
      <if test="partyCPrincipalName != null">
        PARTY_C_PRINCIPAL_NAME,
      </if>
      <if test="contractContent != null">
        CONTRACT_CONTENT,
      </if>
      <if test="projectManagementId != null and projectManagementId != ''">
        PROJECT_MANAGEMENT_ID,
      </if>
      <if test="projectManagementName != null and projectManagementName != ''">
        PROJECT_MANAGEMENT_NAME,
      </if>
      <if test="dutyDepartId != null">
        DUTY_DEPART_ID,
      </if>
      <if test="dutyDepartName != null">
        DUTY_DEPART_NAME,
      </if>
      <if test="payTime != null">
        PAY_TIME,
      </if>
      <if test="payStatus != null">
        PAY_STATUS,
      </if>
      <if test="serviceLevelId != null">
        SERVICE_LEVEL_ID,
      </if>
      <if test="serviceLevelName != null">
        SERVICE_LEVEL_NAME,
      </if>
      <if test="qualityBeginDay != null">
        QUALITY_BEGIN_DAY,
      </if>
      <if test="qualityEndDay != null">
        QUALITY_END_DAY,
      </if>
      <if test="qualityContent != null">
        QUALITY_CONTENT,
      </if>
      <if test="partyAPrincipalTel != null">
        PARTY_A_PRINCIPAL_TEL,
      </if>
      <if test="partyAManager != null">
        PARTY_A_MANAGER,
      </if>
      <if test="partyAManagerTel != null">
        PARTY_A_MANAGER_TEL,
      </if>
      <if test="partyBPrincipalTel != null">
        PARTY_B_PRINCIPAL_TEL,
      </if>
      <if test="partyBManager != null">
        PARTY_B_MANAGER,
      </if>
      <if test="partyBManagerTel != null">
        PARTY_B_MANAGER_TEL,
      </if>
      <if test="salesManager != null">
        SALES_MANAGER,
      </if>
      <if test="salesManagerTel != null">
        SALES_MANAGER_TEL,
      </if>
      <if test="preSalesManager != null">
        PRE_SALES_MANAGER,
      </if>
      <if test="preSalesManagerTel != null">
        PRE_SALES_MANAGER_TEL,
      </if>
      <if test="partyCPerson != null">
        PARTY_C_PERSON,
      </if>
      <if test="partyCPersonTel != null">
        PARTY_C_PERSON_TEL,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="entryId != null">
        ENTRY_ID,
      </if>
      <if test="entryName != null">
        ENTRY_NAME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="contractAmount != null">
        contract_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="signingDate != null">
        #{signingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="partyAName != null">
        #{partyAName,jdbcType=VARCHAR},
      </if>
      <if test="partyAPrincipalName != null">
        #{partyAPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="partyBId != null">
        #{partyBId,jdbcType=VARCHAR},
      </if>
      <if test="partyBName != null">
        #{partyBName,jdbcType=VARCHAR},
      </if>
      <if test="partyBPrincipalName != null">
        #{partyBPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="partyCName != null">
        #{partyCName,jdbcType=VARCHAR},
      </if>
      <if test="partyCPrincipalName != null">
        #{partyCPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="contractContent != null">
        #{contractContent,jdbcType=VARCHAR},
      </if>
      <if test="projectManagementId != null and projectManagementId != ''">
        #{projectManagementId,jdbcType=VARCHAR},
      </if>
      <if test="projectManagementName != null and projectManagementName != ''">
        #{projectManagementName,jdbcType=VARCHAR},
      </if>
      <if test="dutyDepartId != null">
        #{dutyDepartId,jdbcType=VARCHAR},
      </if>
      <if test="dutyDepartName != null">
        #{dutyDepartName,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="serviceLevelId != null">
        #{serviceLevelId,jdbcType=VARCHAR},
      </if>
      <if test="serviceLevelName != null">
        #{serviceLevelName,jdbcType=VARCHAR},
      </if>
      <if test="qualityBeginDay != null">
        #{qualityBeginDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityEndDay != null">
        #{qualityEndDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityContent != null">
        #{qualityContent,jdbcType=VARCHAR},
      </if>
      <if test="partyAPrincipalTel != null">
        #{partyAPrincipalTel,jdbcType=VARCHAR},
      </if>
      <if test="partyAManager != null">
        #{partyAManager,jdbcType=VARCHAR},
      </if>
      <if test="partyAManagerTel != null">
        #{partyAManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="partyBPrincipalTel != null">
        #{partyBPrincipalTel,jdbcType=VARCHAR},
      </if>
      <if test="partyBManager != null">
        #{partyBManager,jdbcType=VARCHAR},
      </if>
      <if test="partyBManagerTel != null">
        #{partyBManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="salesManager != null">
        #{salesManager,jdbcType=VARCHAR},
      </if>
      <if test="salesManagerTel != null">
        #{salesManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="preSalesManager != null">
        #{preSalesManager,jdbcType=VARCHAR},
      </if>
      <if test="preSalesManagerTel != null">
        #{preSalesManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="partyCPerson != null">
        #{partyCPerson,jdbcType=VARCHAR},
      </if>
      <if test="partyCPersonTel != null">
        #{partyCPersonTel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="entryId != null">
        #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="entryName != null">
        #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="contractAmount != null">
        #{contractAmount,jdbcType=FLOAT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement">
    <!--@mbg.generated-->
    update uomp_contract_management
    <set>
      <if test="contractCode != null">
        CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        CONTRACT_NAME = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="signingDate != null">
        SIGNING_DATE = #{signingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        CONTRACT_TYPE = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        CONTRACT_STATUS = #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="partyAName != null">
        PARTY_A_NAME = #{partyAName,jdbcType=VARCHAR},
      </if>
      <if test="partyAPrincipalName != null">
        PARTY_A_PRINCIPAL_NAME = #{partyAPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="partyBId != null">
        PARTY_B_ID = #{partyBId,jdbcType=VARCHAR},
      </if>
      <if test="partyBName != null">
        PARTY_B_NAME = #{partyBName,jdbcType=VARCHAR},
      </if>
      <if test="partyBPrincipalName != null">
        PARTY_B_PRINCIPAL_NAME = #{partyBPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="partyCName != null">
        PARTY_C_NAME = #{partyCName,jdbcType=VARCHAR},
      </if>
      <if test="partyCPrincipalName != null">
        PARTY_C_PRINCIPAL_NAME = #{partyCPrincipalName,jdbcType=VARCHAR},
      </if>
      <if test="contractContent != null">
        CONTRACT_CONTENT = #{contractContent,jdbcType=VARCHAR},
      </if>
      <if test="projectManagementId != null and projectManagementId != ''">
        PROJECT_MANAGEMENT_ID = #{projectManagementId,jdbcType=VARCHAR},
      </if>
      <if test="projectManagementName != null and projectManagementName != ''">
        PROJECT_MANAGEMENT_NAME = #{projectManagementName,jdbcType=VARCHAR},
      </if>
      <if test="dutyDepartId != null">
        DUTY_DEPART_ID = #{dutyDepartId,jdbcType=VARCHAR},
      </if>
      <if test="dutyDepartName != null">
        DUTY_DEPART_NAME = #{dutyDepartName,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        PAY_TIME = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payStatus != null">
        PAY_STATUS = #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="serviceLevelId != null">
        SERVICE_LEVEL_ID = #{serviceLevelId,jdbcType=VARCHAR},
      </if>
      <if test="serviceLevelName != null">
        SERVICE_LEVEL_NAME = #{serviceLevelName,jdbcType=VARCHAR},
      </if>
      <if test="qualityBeginDay != null">
        QUALITY_BEGIN_DAY = #{qualityBeginDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityEndDay != null">
        QUALITY_END_DAY = #{qualityEndDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityContent != null">
        QUALITY_CONTENT = #{qualityContent,jdbcType=VARCHAR},
      </if>
      <if test="partyAPrincipalTel != null">
        PARTY_A_PRINCIPAL_TEL = #{partyAPrincipalTel,jdbcType=VARCHAR},
      </if>
      <if test="partyAManager != null">
        PARTY_A_MANAGER = #{partyAManager,jdbcType=VARCHAR},
      </if>
      <if test="partyAManagerTel != null">
        PARTY_A_MANAGER_TEL = #{partyAManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="partyBPrincipalTel != null">
        PARTY_B_PRINCIPAL_TEL = #{partyBPrincipalTel,jdbcType=VARCHAR},
      </if>
      <if test="partyBManager != null">
        PARTY_B_MANAGER = #{partyBManager,jdbcType=VARCHAR},
      </if>
      <if test="partyBManagerTel != null">
        PARTY_B_MANAGER_TEL = #{partyBManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="salesManager != null">
        SALES_MANAGER = #{salesManager,jdbcType=VARCHAR},
      </if>
      <if test="salesManagerTel != null">
        SALES_MANAGER_TEL = #{salesManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="preSalesManager != null">
        PRE_SALES_MANAGER = #{preSalesManager,jdbcType=VARCHAR},
      </if>
      <if test="preSalesManagerTel != null">
        PRE_SALES_MANAGER_TEL = #{preSalesManagerTel,jdbcType=VARCHAR},
      </if>
      <if test="partyCPerson != null">
        PARTY_C_PERSON = #{partyCPerson,jdbcType=VARCHAR},
      </if>
      <if test="partyCPersonTel != null">
        PARTY_C_PERSON_TEL = #{partyCPersonTel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="entryId != null">
        ENTRY_ID = #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="entryName != null">
        ENTRY_NAME = #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="contractAmount != null">
        contract_amount = #{contractAmount,jdbcType=FLOAT},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement">
    <!--@mbg.generated-->
    update uomp_contract_management
    set CONTRACT_CODE = #{contractCode,jdbcType=VARCHAR},
    CONTRACT_NAME = #{contractName,jdbcType=VARCHAR},
    SIGNING_DATE = #{signingDate,jdbcType=TIMESTAMP},
    CONTRACT_TYPE = #{contractType,jdbcType=VARCHAR},
    CONTRACT_STATUS = #{contractStatus,jdbcType=VARCHAR},
    PARTY_A_NAME = #{partyAName,jdbcType=VARCHAR},
    PARTY_A_PRINCIPAL_NAME = #{partyAPrincipalName,jdbcType=VARCHAR},
    PARTY_B_ID = #{partyBId,jdbcType=VARCHAR},
    PARTY_B_NAME = #{partyBName,jdbcType=VARCHAR},
    PARTY_B_PRINCIPAL_NAME = #{partyBPrincipalName,jdbcType=VARCHAR},
    PARTY_C_NAME = #{partyCName,jdbcType=VARCHAR},
    PARTY_C_PRINCIPAL_NAME = #{partyCPrincipalName,jdbcType=VARCHAR},
    CONTRACT_CONTENT = #{contractContent,jdbcType=VARCHAR},
    PROJECT_MANAGEMENT_ID = #{projectManagementId,jdbcType=VARCHAR},
    PROJECT_MANAGEMENT_NAME = #{projectManagementName,jdbcType=VARCHAR},
    DUTY_DEPART_ID = #{dutyDepartId,jdbcType=VARCHAR},
    DUTY_DEPART_NAME = #{dutyDepartName,jdbcType=VARCHAR},
    PAY_TIME = #{payTime,jdbcType=TIMESTAMP},
    PAY_STATUS = #{payStatus,jdbcType=VARCHAR},
    SERVICE_LEVEL_ID = #{serviceLevelId,jdbcType=VARCHAR},
    SERVICE_LEVEL_NAME = #{serviceLevelName,jdbcType=VARCHAR},
    QUALITY_BEGIN_DAY = #{qualityBeginDay,jdbcType=TIMESTAMP},
    QUALITY_END_DAY = #{qualityEndDay,jdbcType=TIMESTAMP},
    QUALITY_CONTENT = #{qualityContent,jdbcType=VARCHAR},
    PARTY_A_PRINCIPAL_TEL = #{partyAPrincipalTel,jdbcType=VARCHAR},
    PARTY_A_MANAGER = #{partyAManager,jdbcType=VARCHAR},
    PARTY_A_MANAGER_TEL = #{partyAManagerTel,jdbcType=VARCHAR},
    PARTY_B_PRINCIPAL_TEL = #{partyBPrincipalTel,jdbcType=VARCHAR},
    PARTY_B_MANAGER = #{partyBManager,jdbcType=VARCHAR},
    PARTY_B_MANAGER_TEL = #{partyBManagerTel,jdbcType=VARCHAR},
    SALES_MANAGER = #{salesManager,jdbcType=VARCHAR},
    SALES_MANAGER_TEL = #{salesManagerTel,jdbcType=VARCHAR},
    PRE_SALES_MANAGER = #{preSalesManager,jdbcType=VARCHAR},
    PRE_SALES_MANAGER_TEL = #{preSalesManagerTel,jdbcType=VARCHAR},
    PARTY_C_PERSON = #{partyCPerson,jdbcType=VARCHAR},
    PARTY_C_PERSON_TEL = #{partyCPersonTel,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    ENTRY_ID = #{entryId,jdbcType=VARCHAR},
    ENTRY_NAME = #{entryName,jdbcType=VARCHAR},
    CREATE_BY = #{createBy,jdbcType=VARCHAR},
    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
    CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
    UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
    contract_amount = #{contractAmount,jdbcType=FLOAT}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectInfoByContractCode" resultMap="BaseResultMap">
    select * from UOMP_CONTRACT_MANAGEMENT where CONTRACT_CODE = #{contractCode} and DEL_FLAG = '0'
  </select>
  <select id="selectInfoById" resultMap="BaseResultMap">
    select * from UOMP_CONTRACT_MANAGEMENT where DEL_FLAG = '0' and ID = #{id}
  </select>
  <!-- 项目里面的查询： 不使用了-->
  <select id="getRealtionContractList" resultMap="BaseResultMap">
    select
    a.*
    from
    UOMP_PROJECT_RELATION r
    left join UOMP_CONTRACT_MANAGEMENT a on r.RELATION_ID = a.ID and a.DEL_FLAG = '0'
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="query" resultMap="BaseResultMap">
    select * from UOMP_CONTRACT_MANAGEMENT
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="get" resultMap="BaseResultMap">
    select * from UOMP_CONTRACT_MANAGEMENT where ID = #{id} and DEL_FLAG = '0'
  </select>
  <!-- 项目里面的查询： 不使用了-->
  <select id="getUnbindingContractList" resultMap="BaseResultMap">
    select
    *
    from UOMP_CONTRACT_MANAGEMENT
    where DEL_FLAG = '0'
    and CONTRACT_STATUS != '1'
    and ID not in
    (select
    RELATION_ID
    from UOMP_PROJECT_RELATION
    where DEL_FLAG = '0' and RELATION_TYPE = 'contract' and PROJECT_MANAGEMENT_ID = #{projectManagementId})
    <if test="contractName != null and contractName != ''">
      and CONTRACT_NAME like concat('%',#{contractName},'%')
      or CONTRACT_CODE like concat('%',#{contractName},'%')
    </if>
    order by CREATE_TIME desc
  </select>

  <select id="getIdAndNameList" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
    select
      id,
      sla_name as name
    from uomp_sla
    where del_flag = '0' and SLA_STATE = '1'
    order by create_time desc
  </select>
  <select id="bindingContractListBySystemId" resultMap="BaseResultMap">
    select
    ucm.*
    from uomp_contract_management ucm
    inner join uomp_application_system_relation uasr on uasr.RELATION_ID = ucm.ID and uasr.DEL_FLAG = '0'
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
      and ucm.QUALITY_END_DAY is not NULL
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="unbindingContractList" resultMap="BaseResultMap">
    select
    *
    from uomp_contract_management
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
      and PROJECT_MANAGEMENT_ID is null and PROJECT_MANAGEMENT_NAME is null
      and ID not in (select RELATION_ID from uomp_application_system_relation where DEL_FLAG = '0')
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="getContractTop5" resultType="cn.gwssi.ecloud.staffpool.dto.SupplierContractDTO">
    select
      uucm.ID as id, uucm.CONTRACT_NAME as name,rucm.supplierId,rucm.supplierName, rucm.qualityEndDay
    from uomp_contract_management uucm
           inner join (
      select
        ucm.PARTY_B_ID as supplierId,
        usm.SUPPLIER_NAME as supplierName,
        MAX(ucm.QUALITY_END_DAY) as qualityEndDay
      from uomp_contract_management ucm
             inner join uomp_supplier_management usm on usm.ID = ucm.PARTY_B_ID and usm.DEL_FLAG = '0'
      where ucm.DEL_FLAG = '0'
        and ucm.QUALITY_END_DAY > now()
        and ucm.CONTRACT_STATUS not in ('1', '5')
      group by ucm.PARTY_B_ID
      order by qualityEndDay asc limit 5
    ) rucm on rucm.supplierId = uucm.PARTY_B_ID and uucm.QUALITY_END_DAY = rucm.qualityEndDay
    order by rucm.qualityEndDay asc
  </select>

  <select id="selectAllByPartyBId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from uomp_contract_management
    where DEL_FLAG = '0' and PARTY_B_ID = #{partyBId} and CONTRACT_STATUS not in ('1', '5')
  </select>

  <select id="getContractNoticeList" resultMap="BaseResultMap" databaseId='mysql'>
    select
    <include refid="Base_Column_List"/>
    from uomp_contract_management
    where
    CONTRACT_STATUS = '3'
    and DEL_FLAG = '0'
    and QUALITY_END_DAY is not null
    and QUALITY_END_DAY &lt;= (now() + interval #{day} day)
    and QUALITY_END_DAY > now()
  </select>



  <select id="getContractNoticeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from uomp_contract_management
    where
    CONTRACT_STATUS = '3'
    and DEL_FLAG = '0'
    and QUALITY_END_DAY is not null
    and QUALITY_END_DAY &lt;= (CURRENT_DATE + interval #{day} day)
    and QUALITY_END_DAY > CURRENT_DATE
  </select>

</mapper>
