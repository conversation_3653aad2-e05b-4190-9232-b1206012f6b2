<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompContractResourceMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompContractResource">
    <!--@mbg.generated-->
    <!--@Table uomp_contract_resource-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONTRACT_MANAGEMENT_ID" jdbcType="VARCHAR" property="contractManagementId" />
    <result column="C_INST_ID" jdbcType="VARCHAR" property="cInstId" />
    <result column="CI_NAME" jdbcType="VARCHAR" property="ciName" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="BRAND_MODEL" jdbcType="VARCHAR" property="brandModel" />
    <result column="CPU_FRAMEWORK" jdbcType="VARCHAR" property="cpuFramework" />
    <result column="USEDS" jdbcType="VARCHAR" property="useds" />
    <result column="OS" jdbcType="VARCHAR" property="os" />
    <result column="OS_VERSION" jdbcType="VARCHAR" property="osVersion" />
    <result column="MACHINE_ROOM" jdbcType="VARCHAR" property="machineRoom" />
    <result column="CABINET" jdbcType="VARCHAR" property="cabinet" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_MANAGEMENT_ID, C_INST_ID, CI_NAME, BRAND, BRAND_MODEL, CPU_FRAMEWORK, 
    USEDS, OS, OS_VERSION, MACHINE_ROOM, CABINET
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_contract_resource
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_contract_resource
    where ID = #{id,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByContractId" parameterType="java.lang.String">
      delete from uomp_contract_resource
      where CONTRACT_MANAGEMENT_ID = #{contractId,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByIdList">
    delete from uomp_contract_resource
    where id in
    <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="query" resultMap="BaseResultMap">
    select * from uomp_contract_resource
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <insert id="insert" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractResource">
    <!--@mbg.generated-->
    insert into uomp_contract_resource (ID, CONTRACT_MANAGEMENT_ID, C_INST_ID,
      CI_NAME, BRAND, BRAND_MODEL, 
      CPU_FRAMEWORK, USEDS, OS, 
      OS_VERSION, MACHINE_ROOM, CABINET
      )
    values (#{id,jdbcType=VARCHAR}, #{contractManagementId,jdbcType=VARCHAR}, #{cInstId,jdbcType=VARCHAR}, 
      #{ciName,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{brandModel,jdbcType=VARCHAR}, 
      #{cpuFramework,jdbcType=VARCHAR}, #{useds,jdbcType=VARCHAR}, #{os,jdbcType=VARCHAR}, 
      #{osVersion,jdbcType=VARCHAR}, #{machineRoom,jdbcType=VARCHAR}, #{cabinet,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractResource">
    <!--@mbg.generated-->
    insert into uomp_contract_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="contractManagementId != null">
        CONTRACT_MANAGEMENT_ID,
      </if>
      <if test="cInstId != null">
        C_INST_ID,
      </if>
      <if test="ciName != null">
        CI_NAME,
      </if>
      <if test="brand != null">
        BRAND,
      </if>
      <if test="brandModel != null">
        BRAND_MODEL,
      </if>
      <if test="cpuFramework != null">
        CPU_FRAMEWORK,
      </if>
      <if test="useds != null">
        USEDS,
      </if>
      <if test="os != null">
        OS,
      </if>
      <if test="osVersion != null">
        OS_VERSION,
      </if>
      <if test="machineRoom != null">
        MACHINE_ROOM,
      </if>
      <if test="cabinet != null">
        CABINET,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractManagementId != null">
        #{contractManagementId,jdbcType=VARCHAR},
      </if>
      <if test="cInstId != null">
        #{cInstId,jdbcType=VARCHAR},
      </if>
      <if test="ciName != null">
        #{ciName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="brandModel != null">
        #{brandModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuFramework != null">
        #{cpuFramework,jdbcType=VARCHAR},
      </if>
      <if test="useds != null">
        #{useds,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        #{os,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null">
        #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="machineRoom != null">
        #{machineRoom,jdbcType=VARCHAR},
      </if>
      <if test="cabinet != null">
        #{cabinet,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch">
    insert into uomp_contract_resource (ID, CONTRACT_MANAGEMENT_ID, C_INST_ID,
      CI_NAME, BRAND, BRAND_MODEL,
      CPU_FRAMEWORK, USEDS, OS,
      OS_VERSION, MACHINE_ROOM, CABINET
      )
    values
    <foreach collection="infoList" index="index" item="item" separator=",">
     (#{item.id,jdbcType=VARCHAR}, #{item.contractManagementId,jdbcType=VARCHAR}, #{item.cInstId,jdbcType=VARCHAR},
      #{item.ciName,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.brandModel,jdbcType=VARCHAR},
      #{item.cpuFramework,jdbcType=VARCHAR}, #{item.useds,jdbcType=VARCHAR}, #{item.os,jdbcType=VARCHAR},
      #{item.osVersion,jdbcType=VARCHAR}, #{item.machineRoom,jdbcType=VARCHAR}, #{item.cabinet,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractResource">
    <!--@mbg.generated-->
    update uomp_contract_resource
    <set>
      <if test="contractManagementId != null">
        CONTRACT_MANAGEMENT_ID = #{contractManagementId,jdbcType=VARCHAR},
      </if>
      <if test="cInstId != null">
        C_INST_ID = #{cInstId,jdbcType=VARCHAR},
      </if>
      <if test="ciName != null">
        CI_NAME = #{ciName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        BRAND = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="brandModel != null">
        BRAND_MODEL = #{brandModel,jdbcType=VARCHAR},
      </if>
      <if test="cpuFramework != null">
        CPU_FRAMEWORK = #{cpuFramework,jdbcType=VARCHAR},
      </if>
      <if test="useds != null">
        USEDS = #{useds,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        OS = #{os,jdbcType=VARCHAR},
      </if>
      <if test="osVersion != null">
        OS_VERSION = #{osVersion,jdbcType=VARCHAR},
      </if>
      <if test="machineRoom != null">
        MACHINE_ROOM = #{machineRoom,jdbcType=VARCHAR},
      </if>
      <if test="cabinet != null">
        CABINET = #{cabinet,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractResource">
    <!--@mbg.generated-->
    update uomp_contract_resource
    set CONTRACT_MANAGEMENT_ID = #{contractManagementId,jdbcType=VARCHAR},
      C_INST_ID = #{cInstId,jdbcType=VARCHAR},
      CI_NAME = #{ciName,jdbcType=VARCHAR},
      BRAND = #{brand,jdbcType=VARCHAR},
      BRAND_MODEL = #{brandModel,jdbcType=VARCHAR},
      CPU_FRAMEWORK = #{cpuFramework,jdbcType=VARCHAR},
      USEDS = #{useds,jdbcType=VARCHAR},
      OS = #{os,jdbcType=VARCHAR},
      OS_VERSION = #{osVersion,jdbcType=VARCHAR},
      MACHINE_ROOM = #{machineRoom,jdbcType=VARCHAR},
      CABINET = #{cabinet,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateBatchByCmIdAndCiId">
    <foreach collection="infoList" item="item" separator=";">
      update uomp_contract_resource
      set
      CI_NAME = #{item.ciName,jdbcType=VARCHAR},
      BRAND = #{item.brand,jdbcType=VARCHAR},
      BRAND_MODEL = #{item.brandModel,jdbcType=VARCHAR},
      CPU_FRAMEWORK = #{item.cpuFramework,jdbcType=VARCHAR},
      USEDS = #{item.useds,jdbcType=VARCHAR},
      OS = #{item.os,jdbcType=VARCHAR},
      OS_VERSION = #{item.osVersion,jdbcType=VARCHAR},
      MACHINE_ROOM = #{item.machineRoom,jdbcType=VARCHAR},
      CABINET = #{item.cabinet,jdbcType=VARCHAR}
      where CONTRACT_MANAGEMENT_ID = #{item.contractManagementId,jdbcType=VARCHAR}
      and C_INST_ID = #{item.cInstId,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="getResourceListByContractId" resultMap="BaseResultMap">
     select * from uomp_contract_resource
     where CONTRACT_MANAGEMENT_ID = #{contractId}
  </select>

    <select id="selectListByCmIdAndCiId" resultType="java.lang.String" parameterType="java.util.List">
        select
            C_INST_ID
        from uomp_contract_resource
        where (CONTRACT_MANAGEMENT_ID, C_INST_ID) in
        <foreach collection="paramList" item="item" index="index" open="(" separator="," close=")">
            (#{item.contractManagementId}, #{item.cInstId})
        </foreach>
    </select>
</mapper>