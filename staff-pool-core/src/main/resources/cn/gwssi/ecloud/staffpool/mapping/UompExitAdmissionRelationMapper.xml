<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompExitAdmissionRelationMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompExitAdmissionRelation">
        <!--@mbg.generated-->
        <!--@Table uomp_exit_admission_relation-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="APPLY_PERSON_ID" jdbcType="VARCHAR" property="applyPersonId"/>
        <result column="EXIT_APPLY_ID" jdbcType="VARCHAR" property="exitApplyId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        APPLY_PERSON_ID,
        EXIT_APPLY_ID,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
        DEL_FLAG
    </sql>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompExitAdmissionRelation">
        <!--@mbg.generated-->
        insert into uomp_exit_admission_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="applyPersonId != null">
                APPLY_PERSON_ID,
            </if>
            <if test="exitApplyId != null">
                EXIT_APPLY_ID,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyPersonId != null">
                #{applyPersonId,jdbcType=VARCHAR},
            </if>
            <if test="exitApplyId != null">
                #{exitApplyId,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectExitApplyIdByApplyPedrsonId" resultType="java.lang.String">
        select distinct EXIT_APPLY_ID exitApplyId
        from UOMP_EXIT_ADMISSION_RELATION
        where DEL_FLAG = '0'
          and APPLY_PERSON_ID = #{applyPersonId}
    </select>

    <select id="selectApplyPersonIdByExitId" resultType="java.lang.String">
        select APPLY_PERSON_ID
        from UOMP_EXIT_ADMISSION_RELATION
        where EXIT_APPLY_ID = #{exitId}
    </select>
</mapper>