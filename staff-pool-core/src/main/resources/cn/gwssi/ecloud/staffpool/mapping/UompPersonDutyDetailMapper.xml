<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonDutyDetailMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail">
        <!--@mbg.generated-->
        <!--@Table uomp_person_duty_detail-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DUTY_ID" jdbcType="VARCHAR" property="dutyId"/>
        <result column="PERSON_ID" jdbcType="VARCHAR" property="personId"/>
        <result column="DATE" jdbcType="VARCHAR" property="date"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>

    <sql id="dateKeyword" databaseId="mysql">
        `DATE`
    </sql>
    <sql id="dateKeyword">
        "DATE"
    </sql>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, DUTY_ID, PERSON_ID, <include refid="dateKeyword"/>, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>


    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_person_duty_detail
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from uomp_person_duty_detail
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail">
        <!--@mbg.generated-->
        insert into uomp_person_duty_detail (ID, DUTY_ID, PERSON_ID,
        <include refid="dateKeyword"/>, CREATE_BY, CREATE_TIME,
        UPDATE_BY, UPDATE_TIME, DEL_FLAG
        )
        values (#{id,jdbcType=VARCHAR}, #{dutyId,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR},
        #{date,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail">
        <!--@mbg.generated-->
        insert into uomp_person_duty_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="dutyId != null">
                DUTY_ID,
            </if>
            <if test="personId != null">
                PERSON_ID,
            </if>
            <if test="date != null">
                <include refid="dateKeyword"/>,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="dutyId != null">
                #{dutyId,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                #{personId,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail">
        <!--@mbg.generated-->
        update uomp_person_duty_detail
        <set>
            <if test="dutyId != null">
                DUTY_ID = #{dutyId,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                PERSON_ID = #{personId,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                <include refid="dateKeyword"/> = #{date,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail">
        <!--@mbg.generated-->
        update uomp_person_duty_detail
        set DUTY_ID = #{dutyId,jdbcType=VARCHAR},
        PERSON_ID = #{personId,jdbcType=VARCHAR},
        <include refid="dateKeyword"/> = #{date,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="removeByDutyId">
        delete
        from uomp_person_duty_detail
        where duty_id = #{dutyId}
    </delete>
    <select id="query" resultMap="BaseResultMap">
        select *
        from uomp_person_duty_detail
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
    <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="ORG_GROUP_NAME" jdbcType="VARCHAR" property="orgGroupName"/>
        <result column="ENTRY_STATUS" jdbcType="VARCHAR" property="entryStatus"/>
        <result column="SERVICE_LOCATION" jdbcType="VARCHAR" property="serviceLocation"/>
    </resultMap>
    <select id="selectByDutyId" parameterType="java.util.Map" resultMap="BasePersonMap">
        select i.ID, i.PERSON_NAME, i.ORG_GROUP_NAME, i.TEL, i.ENTRY_STATUS, p.SERVICE_LOCATION
        from uomp_person_duty_detail dd
                 left join uomp_person_info i on dd.person_id = i.id
                 left join uomp_admission_person p on p.person_id = i.id
        where dd.DUTY_ID = #{dutyId}
        group by i.ID
    </select>
    <select id="selectByMonth" resultType="cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto">
        select dd.date, i.PERSON_NAME as personName, i.ORG_GROUP_NAME as orgGroupName, i.TEL as tel
        from uomp_person_duty_detail dd
                 left join uomp_person_info i on dd.person_id = i.id
                 join uomp_org_group o  on i.ORG_GROUP_ID = o.ID_
        where dd.date like concat('%', #{month}, '%') and dd.del_flag = '0'
        <if test="orgId != '' and orgId != null">
            and o.path_  like concat('%', #{orgId}, '%')
        </if>
    </select>
</mapper>
