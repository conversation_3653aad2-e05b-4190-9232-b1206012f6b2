<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonEducationalMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="EDUCATION_BEGIN_TIME" jdbcType="VARCHAR" property="educationBeginTime" />
    <result column="EDUCATION_END_TIME" jdbcType="VARCHAR" property="educationEndTime" />
    <result column="SCHOOL" jdbcType="VARCHAR" property="school" />
    <result column="MAJOR" jdbcType="VARCHAR" property="major" />
    <result column="EDUCATION_BACKGROUND" jdbcType="VARCHAR" property="educationBackground" />
    <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="EDUCATION_FORM" jdbcType="VARCHAR" property="educationForm" />
    <result column="CERTIFICATE_NUM" jdbcType="VARCHAR" property="certificateNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, EDUCATION_BEGIN_TIME, EDUCATION_END_TIME, SCHOOL, MAJOR, EDUCATION_BACKGROUND,
    FILE_INFO, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
    DEL_FLAG, EDUCATION_FORM, CERTIFICATE_NUM
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_educational
    where ID = #{id,jdbcType=VARCHAR}
  </select>
    <select id="query" resultMap="BaseResultMap">
      select *
      from uomp_person_educational
      <where>
        <if test="whereSql != null">
          ${whereSql}
        </if>
      </where>
      <if test="orderBySql != null">
        ORDER BY ${orderBySql}
      </if>
    </select>
    <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_educational
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPersonId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_educational
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    insert into uomp_person_educational (ID, PERSON_ID, EDUCATION_BEGIN_TIME,
      EDUCATION_END_TIME, SCHOOL, MAJOR,
      EDUCATION_BACKGROUND, FILE_INFO, CREATE_BY,
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG,
      EDUCATION_FORM, CERTIFICATE_NUM)
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{educationBeginTime,jdbcType=VARCHAR},
      #{educationEndTime,jdbcType=VARCHAR}, #{school,jdbcType=VARCHAR}, #{major,jdbcType=VARCHAR},
      #{educationBackground,jdbcType=VARCHAR}, #{fileInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR},
      #{educationForm,jdbcType=VARCHAR}, #{certificateNum,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    insert into uomp_person_educational
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="educationBeginTime != null">
        EDUCATION_BEGIN_TIME,
      </if>
      <if test="educationEndTime != null">
        EDUCATION_END_TIME,
      </if>
      <if test="school != null">
        SCHOOL,
      </if>
      <if test="major != null">
        MAJOR,
      </if>
      <if test="educationBackground != null">
        EDUCATION_BACKGROUND,
      </if>
      <if test="fileInfo != null">
        FILE_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="educationForm != null">
        EDUCATION_FORM,
      </if>
      <if test="certificateNum != null">
        CERTIFICATE_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="educationBeginTime != null">
        #{educationBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="educationEndTime != null">
        #{educationEndTime,jdbcType=VARCHAR},
      </if>
      <if test="school != null">
        #{school,jdbcType=VARCHAR},
      </if>
      <if test="major != null">
        #{major,jdbcType=VARCHAR},
      </if>
      <if test="educationBackground != null">
        #{educationBackground,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="educationForm != null">
        #{educationForm,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        #{certificateNum,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    update uomp_person_educational
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="educationBeginTime != null">
        EDUCATION_BEGIN_TIME = #{educationBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="educationEndTime != null">
        EDUCATION_END_TIME = #{educationEndTime,jdbcType=VARCHAR},
      </if>
      <if test="school != null">
        SCHOOL = #{school,jdbcType=VARCHAR},
      </if>
      <if test="major != null">
        MAJOR = #{major,jdbcType=VARCHAR},
      </if>
      <if test="educationBackground != null">
        EDUCATION_BACKGROUND = #{educationBackground,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="educationForm != null">
        EDUCATION_FORM = #{educationForm,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        CERTIFICATE_NUM = #{certificateNum,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    update uomp_person_educational
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      EDUCATION_BEGIN_TIME = #{educationBeginTime,jdbcType=VARCHAR},
      EDUCATION_END_TIME = #{educationEndTime,jdbcType=VARCHAR},
      SCHOOL = #{school,jdbcType=VARCHAR},
      MAJOR = #{major,jdbcType=VARCHAR},
      EDUCATION_BACKGROUND = #{educationBackground,jdbcType=VARCHAR},
      FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      EDUCATION_FORM = #{educationForm,jdbcType=VARCHAR},
      CERTIFICATE_NUM = #{certificateNum,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPersonId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational">
    <!--@mbg.generated-->
    update uomp_person_educational
    <set>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="educationForm != null">
        EDUCATION_FORM = #{educationForm,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        CERTIFICATE_NUM = #{certificateNum,jdbcType=VARCHAR},
      </if>
    </set>
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </update>

  <update id="updateByPersonIds" >
      update uomp_person_educational set
      DEL_FLAG = '1',
      UPDATE_ORG_ID = #{orgId},
      where PERSON_ID in
      <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
  </update>

  <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonEducationalDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="EDUCATION_BEGIN_TIME" jdbcType="VARCHAR" property="educationBeginTime" />
    <result column="EDUCATION_END_TIME" jdbcType="VARCHAR" property="educationEndTime" />
    <result column="SCHOOL" jdbcType="VARCHAR" property="school" />
    <result column="MAJOR" jdbcType="VARCHAR" property="major" />
    <result column="EDUCATION_BACKGROUND" jdbcType="VARCHAR" property="educationBackground" />
    <result column="EDUCATION_FORM" jdbcType="VARCHAR" property="educationForm" />
    <result column="CERTIFICATE_NUM" jdbcType="VARCHAR" property="certificateNum" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard" />
  </resultMap>
<select id="selectByPersonIds" resultMap="BasePersonMap">
  select
    a.ID,
    a.PERSON_ID,
    a.EDUCATION_BEGIN_TIME,
    a.EDUCATION_END_TIME,
    a.SCHOOL,
    a.MAJOR,
    a.EDUCATION_BACKGROUND,
    b.PERSON_NAME,
    b.PERSON_CARD,
    a.EDUCATION_FORM,
    a.CERTIFICATE_NUM
  from
    UOMP_PERSON_EDUCATIONAL a
      left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
  where a.DEL_FLAG = '0' and b.DEL_FLAG ='0' and a.PERSON_ID in
  <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
    #{item}
  </foreach>
  order by a.PERSON_ID desc,a.CREATE_TIME desc
</select>
</mapper>
