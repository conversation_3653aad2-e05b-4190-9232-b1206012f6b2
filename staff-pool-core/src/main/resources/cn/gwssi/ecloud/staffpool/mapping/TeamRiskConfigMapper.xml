<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.TeamRiskConfigMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="name_" jdbcType="VARCHAR" property="name" />
        <result column="type_" jdbcType="VARCHAR" property="type" />
        <result column="factor_" jdbcType="VARCHAR" property="factor" />
        <result column="level_" jdbcType="VARCHAR" property="level" />
        <result column="rule_" jdbcType="VARCHAR" property="rule" />
        <result column="status_" jdbcType="VARCHAR" property="status" />
        <result column="del_flag" jdbcType="VARCHAR" property="delFlag" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig">
        insert into uomp_team_risk_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name_,
            </if>
            <if test="type != null">
                type_,
            </if>
            <if test="factor != null">
                factor_,
            </if>
            <if test="level != null">
                level_,
            </if>
            <if test="rule != null">
                rule_,
            </if>
            <if test="status != null">
                status_,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="factor != null">
                #{factor,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="rule != null">
                #{rule,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig">
        update uomp_team_risk_config
        <set>
            <if test="name != null">
                name_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type_ = #{type,jdbcType=VARCHAR},
            </if>
            <if test="factor != null">
                factor_ = #{factor,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level_ = #{level,jdbcType=VARCHAR},
            </if>
            <if test="rule != null">
                rule_ = #{rule,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status_ = #{status,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from uomp_team_risk_config
        <where>
            del_flag = '0'
            <if test="whereSql != null">
              and ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        <if test="orderBySql == null">
            ORDER BY id asc
        </if>
    </select>

    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT * FROM uomp_team_risk_config
        WHERE id = #{id}
    </select>
    <select id="getAll" resultMap="BaseResultMap">
        SELECT * FROM uomp_team_risk_config
        WHERE 1 = 1
    </select>

    <update id="remove" parameterType="java.lang.String">
        update uomp_team_risk_config set del_flag = '1'
        where id = #{id}
    </update>
</mapper>