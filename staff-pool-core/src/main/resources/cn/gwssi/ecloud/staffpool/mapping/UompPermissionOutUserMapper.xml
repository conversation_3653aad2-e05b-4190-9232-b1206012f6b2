<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPermissionOutUserMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutUser">
    <!--@mbg.generated-->
    <!--@Table uomp_permission_out_user-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OUT_BASE_ID" jdbcType="VARCHAR" property="outBaseId" />
    <result column="EMPOWER_USER_IDS" jdbcType="VARCHAR" property="empowerUserIds" />
    <result column="EMPOWER_USER_JSON" jdbcType="VARCHAR" property="empowerUserJson" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, OUT_BASE_ID, EMPOWER_USER_IDS, EMPOWER_USER_JSON, CREATE_BY, CREATE_TIME, UPDATE_BY,
    UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_permission_out_user
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_permission_out_user
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutUser">
    <!--@mbg.generated-->
    insert into uomp_permission_out_user (ID, OUT_BASE_ID, EMPOWER_USER_IDS,
      EMPOWER_USER_JSON, CREATE_BY, CREATE_TIME,
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{outBaseId,jdbcType=VARCHAR}, #{empowerUserIds,jdbcType=VARCHAR},
      #{empowerUserJson,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutUser">
    <!--@mbg.generated-->
    insert into uomp_permission_out_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="outBaseId != null">
        OUT_BASE_ID,
      </if>
      <if test="empowerUserIds != null">
        EMPOWER_USER_IDS,
      </if>
      <if test="empowerUserJson != null">
        EMPOWER_USER_JSON,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="outBaseId != null">
        #{outBaseId,jdbcType=VARCHAR},
      </if>
      <if test="empowerUserIds != null">
        #{empowerUserIds,jdbcType=VARCHAR},
      </if>
      <if test="empowerUserJson != null">
        #{empowerUserJson,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutUser">
    <!--@mbg.generated-->
    update uomp_permission_out_user
    set OUT_BASE_ID = #{outBaseId,jdbcType=VARCHAR},
      EMPOWER_USER_IDS = #{empowerUserIds,jdbcType=VARCHAR},
      EMPOWER_USER_JSON = #{empowerUserJson,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
