<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompExitAcceptInfoMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompExitAcceptInfo">
    <!--@mbg.generated-->
    <!--@Table uomp_exit_accept_info-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="EXIT_APPLY_ID" jdbcType="VARCHAR" property="exitApplyId" />
    <result column="ENGAGEMENT_PROJECT_ID" jdbcType="VARCHAR" property="engagementProjectId" />
    <result column="ENGAGEMENT_PROJECT_NAME" jdbcType="VARCHAR" property="engagementProjectName" />
    <result column="ACCEPT_USER_ID" jdbcType="VARCHAR" property="acceptUserId" />
    <result column="ACCEPT_USER_NAME" jdbcType="VARCHAR" property="acceptUserName" />
    <result column="ACCEPT_CONTEXT" jdbcType="VARCHAR" property="acceptContext" />
    <result column="ACCEPT_CONTEXT_FILE" jdbcType="VARCHAR" property="acceptContextFile" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, EXIT_APPLY_ID, ENGAGEMENT_PROJECT_ID, ENGAGEMENT_PROJECT_NAME, ACCEPT_USER_ID,
    ACCEPT_USER_NAME, ACCEPT_CONTEXT, ACCEPT_CONTEXT_FILE, CREATE_BY, CREATE_TIME, UPDATE_BY,
    UPDATE_TIME, DEL_FLAG
  </sql>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompExitAcceptInfo">
    <!--@mbg.generated-->
    insert into uomp_exit_accept_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="exitApplyId != null">
        EXIT_APPLY_ID,
      </if>
      <if test="engagementProjectId != null">
        ENGAGEMENT_PROJECT_ID,
      </if>
      <if test="engagementProjectName != null">
        ENGAGEMENT_PROJECT_NAME,
      </if>
      <if test="acceptUserId != null">
        ACCEPT_USER_ID,
      </if>
      <if test="acceptUserName != null">
        ACCEPT_USER_NAME,
      </if>
      <if test="acceptContext != null">
        ACCEPT_CONTEXT,
      </if>
      <if test="acceptContextFile != null">
        ACCEPT_CONTEXT_FILE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="exitApplyId != null">
        #{exitApplyId,jdbcType=VARCHAR},
      </if>
      <if test="engagementProjectId != null">
        #{engagementProjectId,jdbcType=VARCHAR},
      </if>
      <if test="engagementProjectName != null">
        #{engagementProjectName,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserId != null">
        #{acceptUserId,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserName != null">
        #{acceptUserName,jdbcType=VARCHAR},
      </if>
      <if test="acceptContext != null">
        #{acceptContext,jdbcType=VARCHAR},
      </if>
      <if test="acceptContextFile != null">
        #{acceptContextFile,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectAllByExitApplyId" resultType="cn.gwssi.ecloud.staffpool.dto.AcceptListBean">
      select ID as accept_id,
             ENGAGEMENT_PROJECT_NAME as engagementProjectName,
             ACCEPT_USER_NAME as acceptUserName,
             ACCEPT_CONTEXT as acceptContext,
             ACCEPT_CONTEXT_FILE as acceptContextFileJson
      from UOMP_EXIT_ACCEPT_INFO
      where EXIT_APPLY_ID = #{exitApplyId}
        and DEL_FLAG = '0'
  </select>

  <select id="selectInfoById" resultType="cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO">
    select r.ORG_USER_ID  as id,
           r.PERSON_NAME  as name,
           g.ORG_GROUP_ID as orgId,
           'user'         as type
    from UOMP_EXIT_ACCEPT_INFO a
           left join uomp_person_info r on a.ACCEPT_USER_ID = r.ID
           left join uomp_org_group g on r.ORG_GROUP_ID = g.ID_
    where a.EXIT_APPLY_ID = #{id}
      and a.DEL_FLAG = '0'
    order by a.CREATE_TIME
  </select>

  <select id="selectAccountByExitApplyId" resultType="java.lang.String">
      select r.ACCOUNT
      from UOMP_EXIT_ACCEPT_INFO a
               left join uomp_person_info r on a.ACCEPT_USER_ID = r.ID
      where r.ACCOUNT is not null
        and r.IS_ACCOUNT = '1'
        and a.EXIT_APPLY_ID = #{exitApplyId}
  </select>
</mapper>
