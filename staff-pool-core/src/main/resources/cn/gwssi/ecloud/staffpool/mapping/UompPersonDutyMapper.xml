<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonDutyMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty">
        <!--@mbg.generated-->
        <!--@Table uomp_person_duty-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="DATE" jdbcType="VARCHAR" property="date"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>

    <sql id="dateKeyword" databaseId="mysql">
        `DATE`
    </sql>
    <sql id="dateKeyword">
        "DATE"
    </sql>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, <include refid="dateKeyword"/>, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_person_duty
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from uomp_person_duty
        where ID = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty">
        <!--@mbg.generated-->
        insert into uomp_person_duty (ID, <include refid="dateKeyword"/>, CREATE_BY,
        CREATE_TIME, UPDATE_BY, UPDATE_TIME,
        DEL_FLAG)
        values (#{id,jdbcType=VARCHAR}, #{date,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{delFlag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty">
        <!--@mbg.generated-->
        insert into uomp_person_duty
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="date != null">
                <include refid="dateKeyword"/>,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty">
        <!--@mbg.generated-->
        update uomp_person_duty
        <set>
            <if test="date != null">
                <include refid="dateKeyword"/> = #{date,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty">
        <!--@mbg.generated-->
        update uomp_person_duty
        set <include refid="dateKeyword"/> = #{date,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select *
        from uomp_person_duty
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
    <select id="getDutyList" resultType="cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto">
        select d.id, i.PERSON_NAME as personName, i.tel, d.date, d.CREATE_BY as createBy, d.CREATE_TIME as createTime
        from (select id, date, create_time, create_by, del_flag
        from uomp_person_duty
        <where>
            <if test="startDate != null and endDate != null">
                date &gt;= #{startDate} and date &lt;= #{endDate}
            </if>
            <if test="createBys != null and createBys.size() > 0">
                and create_by in
                <foreach item="createBy" collection="createBys" open="(" separator="," close=")">
                    #{createBy}
                </foreach>
            </if>
        </where>
        order by date desc
        limit #{pageSize} offset #{pageNo}) d
        left join uomp_person_duty_detail dd on d.id = dd.duty_id
        <if test="null != personIds and personIds.length > 0">
            and dd.person_id in
            <foreach collection="personIds" item="personId" index="index" open="(" close=")" separator=",">
                #{personId}
            </foreach>
        </if>
        left join uomp_person_info i on dd.person_id = i.id
        WHERE dd.DEL_FLAG = '0'
        AND d.DEL_FLAG = '0'
        order by d.date desc
    </select>

    <select id="getDutyListTotal" resultType="Integer">
        select count(distinct d.id)
        from (select id, date, create_time, create_by, del_flag
        from uomp_person_duty
        <where>
            <if test="startDate != null and endDate != null">
                date &gt;= #{startDate} and date &lt;= #{endDate}
            </if>
            <if test="createBys != null and createBys.size() > 0">
                and create_by in
                <foreach item="createBy" collection="createBys" open="(" separator="," close=")">
                    #{createBy}
                </foreach>
            </if>
        </where>) d
        left join uomp_person_duty_detail dd on d.id = dd.duty_id
        <if test="null != personIds and personIds.length > 0">
            and dd.person_id in
            <foreach collection="personIds" item="personId" index="index" open="(" close=")" separator=",">
                #{personId}
            </foreach>
        </if>
        left join uomp_person_info i on dd.person_id = i.id
        WHERE dd.DEL_FLAG = '0'
        AND d.DEL_FLAG = '0'
    </select>

    <select id="selectByDateAndPersonId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto">
        select d.id, i.PERSON_NAME as personName, d.date
        from uomp_person_duty d
        left join uomp_person_duty_detail dd on d.id = dd.duty_id
        left join uomp_person_info i on dd.person_id = i.id
        WHERE dd.DEL_FLAG = '0'
        AND d.DEL_FLAG = '0'
        <if test="null != personIds and personIds.length > 0">
            and dd.person_id in
            <foreach collection="personIds" item="personId" index="index" open="(" close=")" separator=",">
                #{personId}
            </foreach>
        </if>
        <if test="null != dates and dates.length > 0">
            and d.date in
            <foreach collection="dates" item="date" index="index" open="(" close=")" separator=",">
                #{date}
            </foreach>
        </if>
        order by d.date desc
    </select>
</mapper>
