<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompDesensitizationMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization">
    <!--@mbg.generated-->
    <!--@Table uomp_desensitization-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DES_OBJ_CODE" jdbcType="VARCHAR" property="desObjCode" />
    <result column="DES_FIELD_CODE" jdbcType="VARCHAR" property="desFieldCode" />
    <result column="DES_RULE_MODE" jdbcType="VARCHAR" property="desRuleMode" />
    <result column="DES_RULE_JSON" jdbcType="VARCHAR" property="desRuleJson" />
    <result column="DES_RULE_REGX" jdbcType="VARCHAR" property="desRuleRegx" />
    <result column="SENSITIVE_WORDS" jdbcType="VARCHAR" property="sensitiveWords" />
    <result column="SENSITIVE_REPLACE_WORDS" jdbcType="VARCHAR" property="sensitiveReplaceWords" />
    <result column="PLAINTEXT_ROLE_JSON" jdbcType="VARCHAR" property="plaintextRoleJson" />
    <result column="PLAINTEXT_POST_JSON" jdbcType="VARCHAR" property="plaintextPostJson" />
    <result column="PLAINTEXT_USER_JSON" jdbcType="VARCHAR" property="plaintextUserJson" />
    <result column="IS_CREATER" jdbcType="VARCHAR" property="isCreater" />
    <result column="IS_OPERATOR" jdbcType="VARCHAR" property="isOperator" />
    <result column="OPERATOR_MODE" jdbcType="VARCHAR" property="operatorMode" />
    <result column="OPERATOR_SCRIPT" jdbcType="VARCHAR" property="operatorScript" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="IS_OVERALL_ENABLE" jdbcType="VARCHAR" property="isOverallEnable" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DES_OBJ_CODE, DES_FIELD_CODE, DES_RULE_MODE, DES_RULE_JSON, DES_RULE_REGX, SENSITIVE_WORDS,
    SENSITIVE_REPLACE_WORDS, PLAINTEXT_ROLE_JSON, PLAINTEXT_POST_JSON, PLAINTEXT_USER_JSON,
    IS_CREATER, IS_OPERATOR, OPERATOR_MODE, OPERATOR_SCRIPT, IS_ENABLE, IS_OVERALL_ENABLE,
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_desensitization
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_desensitization
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization">
    <!--@mbg.generated-->
    insert into uomp_desensitization (ID, DES_OBJ_CODE, DES_FIELD_CODE,
      DES_RULE_MODE, DES_RULE_JSON, DES_RULE_REGX,
      SENSITIVE_WORDS, SENSITIVE_REPLACE_WORDS,
      PLAINTEXT_ROLE_JSON, PLAINTEXT_POST_JSON,
      PLAINTEXT_USER_JSON, IS_CREATER, IS_OPERATOR,
      OPERATOR_MODE, OPERATOR_SCRIPT, IS_ENABLE,
      IS_OVERALL_ENABLE, CREATE_BY, CREATE_TIME,
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{desObjCode,jdbcType=VARCHAR}, #{desFieldCode,jdbcType=VARCHAR},
      #{desRuleMode,jdbcType=VARCHAR}, #{desRuleJson,jdbcType=VARCHAR}, #{desRuleRegx,jdbcType=VARCHAR},
      #{sensitiveWords,jdbcType=VARCHAR}, #{sensitiveReplaceWords,jdbcType=VARCHAR},
      #{plaintextRoleJson,jdbcType=VARCHAR}, #{plaintextPostJson,jdbcType=VARCHAR},
      #{plaintextUserJson,jdbcType=VARCHAR}, #{isCreater,jdbcType=VARCHAR}, #{isOperator,jdbcType=VARCHAR},
      #{operatorMode,jdbcType=VARCHAR}, #{operatorScript,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR},
      #{isOverallEnable,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization">
    <!--@mbg.generated-->
    insert into uomp_desensitization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="desObjCode != null">
        DES_OBJ_CODE,
      </if>
      <if test="desFieldCode != null">
        DES_FIELD_CODE,
      </if>
      <if test="desRuleMode != null">
        DES_RULE_MODE,
      </if>
      <if test="desRuleJson != null">
        DES_RULE_JSON,
      </if>
      <if test="desRuleRegx != null">
        DES_RULE_REGX,
      </if>
      <if test="sensitiveWords != null">
        SENSITIVE_WORDS,
      </if>
      <if test="sensitiveReplaceWords != null">
        SENSITIVE_REPLACE_WORDS,
      </if>
      <if test="plaintextRoleJson != null">
        PLAINTEXT_ROLE_JSON,
      </if>
      <if test="plaintextPostJson != null">
        PLAINTEXT_POST_JSON,
      </if>
      <if test="plaintextUserJson != null">
        PLAINTEXT_USER_JSON,
      </if>
      <if test="isCreater != null">
        IS_CREATER,
      </if>
      <if test="isOperator != null">
        IS_OPERATOR,
      </if>
      <if test="operatorMode != null">
        OPERATOR_MODE,
      </if>
      <if test="operatorScript != null">
        OPERATOR_SCRIPT,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isOverallEnable != null">
        IS_OVERALL_ENABLE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="desObjCode != null">
        #{desObjCode,jdbcType=VARCHAR},
      </if>
      <if test="desFieldCode != null">
        #{desFieldCode,jdbcType=VARCHAR},
      </if>
      <if test="desRuleMode != null">
        #{desRuleMode,jdbcType=VARCHAR},
      </if>
      <if test="desRuleJson != null">
        #{desRuleJson,jdbcType=VARCHAR},
      </if>
      <if test="desRuleRegx != null">
        #{desRuleRegx,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveWords != null">
        #{sensitiveWords,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveReplaceWords != null">
        #{sensitiveReplaceWords,jdbcType=VARCHAR},
      </if>
      <if test="plaintextRoleJson != null">
        #{plaintextRoleJson,jdbcType=VARCHAR},
      </if>
      <if test="plaintextPostJson != null">
        #{plaintextPostJson,jdbcType=VARCHAR},
      </if>
      <if test="plaintextUserJson != null">
        #{plaintextUserJson,jdbcType=VARCHAR},
      </if>
      <if test="isCreater != null">
        #{isCreater,jdbcType=VARCHAR},
      </if>
      <if test="isOperator != null">
        #{isOperator,jdbcType=VARCHAR},
      </if>
      <if test="operatorMode != null">
        #{operatorMode,jdbcType=VARCHAR},
      </if>
      <if test="operatorScript != null">
        #{operatorScript,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isOverallEnable != null">
        #{isOverallEnable,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization">
    <!--@mbg.generated-->
    update uomp_desensitization
    set DES_OBJ_CODE = #{desObjCode,jdbcType=VARCHAR},
      DES_FIELD_CODE = #{desFieldCode,jdbcType=VARCHAR},
      DES_RULE_MODE = #{desRuleMode,jdbcType=VARCHAR},
      DES_RULE_JSON = #{desRuleJson,jdbcType=VARCHAR},
      DES_RULE_REGX = #{desRuleRegx,jdbcType=VARCHAR},
      SENSITIVE_WORDS = #{sensitiveWords,jdbcType=VARCHAR},
      SENSITIVE_REPLACE_WORDS = #{sensitiveReplaceWords,jdbcType=VARCHAR},
      PLAINTEXT_ROLE_JSON = #{plaintextRoleJson,jdbcType=VARCHAR},
      PLAINTEXT_POST_JSON = #{plaintextPostJson,jdbcType=VARCHAR},
      PLAINTEXT_USER_JSON = #{plaintextUserJson,jdbcType=VARCHAR},
      IS_CREATER = #{isCreater,jdbcType=VARCHAR},
      IS_OPERATOR = #{isOperator,jdbcType=VARCHAR},
      OPERATOR_MODE = #{operatorMode,jdbcType=VARCHAR},
      OPERATOR_SCRIPT = #{operatorScript,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
      IS_OVERALL_ENABLE = #{isOverallEnable,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectAllEnable" resultType="java.lang.String">
      select is_overall_enable from UOMP_DESENSITIZATION where del_flag = '0' limit 0,1
  </select>
  <select id="selectAllConfig" resultMap="BaseResultMap">
    select * from UOMP_DESENSITIZATION
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="selectWz" resultType="java.lang.Integer">
    <if test="sqlStr != null and sqlStr != ''">
      #{sqlStr}
    </if>
  </select>
</mapper>
