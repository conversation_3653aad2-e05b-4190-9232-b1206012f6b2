<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompSupplierManagementMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement">
    <!--@mbg.generated-->
    <!--@Table uomp_supplier_management-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="CREDIT_CODE" jdbcType="VARCHAR" property="creditCode" />
    <result column="SHORT_NAME" jdbcType="VARCHAR" property="shortName" />
    <result column="TEL" jdbcType="VARCHAR" property="tel" />
    <result column="RESP_NAME" jdbcType="VARCHAR" property="respName" />
    <result column="RESP_TEL" jdbcType="VARCHAR" property="respTel" />
    <result column="SUPPLIER_TYPE" jdbcType="VARCHAR" property="supplierType" />
    <result column="SUPPLIER_STATUS" jdbcType="VARCHAR" property="supplierStatus" />
    <result column="REGISTER_PROVINCE" jdbcType="VARCHAR" property="registerProvince" />
    <result column="REGISTER_CITY" jdbcType="VARCHAR" property="registerCity" />
    <result column="REGISTER_REGIN" jdbcType="VARCHAR" property="registerRegin" />
    <result column="REGISTER_ADDRESS" jdbcType="VARCHAR" property="registerAddress" />
    <result column="CONTACT_ADDRESS" jdbcType="VARCHAR" property="contactAddress" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="ENTRY_TIME" jdbcType="TIMESTAMP" property="entryTime" />
    <result column="ENTRY_ID" jdbcType="VARCHAR" property="entryId" />
    <result column="ENTRY_NAME" jdbcType="VARCHAR" property="entryName" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="USED_NAME" jdbcType="VARCHAR" property="usedName" />
    <result column="START_COOPERATION_TIME" jdbcType="TIMESTAMP" property="startCooperationTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SUPPLIER_NAME, CREDIT_CODE, SHORT_NAME, TEL, RESP_NAME, RESP_TEL, SUPPLIER_TYPE,
    SUPPLIER_STATUS, REGISTER_PROVINCE, REGISTER_CITY, REGISTER_REGIN, REGISTER_ADDRESS,
    CONTACT_ADDRESS, REMARK, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME,
    UPDATE_ORG_ID, DEL_FLAG, ENTRY_TIME, ENTRY_ID, ENTRY_NAME, GROUP_ID, USED_NAME,START_COOPERATION_TIME
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_supplier_management
    where ID = #{id,jdbcType=VARCHAR} and DEL_FLAG = '0'
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_supplier_management
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement">
    <!--@mbg.generated-->
    insert into uomp_supplier_management (ID, SUPPLIER_NAME, CREDIT_CODE,
    SHORT_NAME, TEL, RESP_NAME,
    RESP_TEL, SUPPLIER_TYPE, SUPPLIER_STATUS,
    REGISTER_PROVINCE, REGISTER_CITY, REGISTER_REGIN,
    REGISTER_ADDRESS, CONTACT_ADDRESS, REMARK,
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID,
    UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
    DEL_FLAG, ENTRY_TIME, ENTRY_ID,
    ENTRY_NAME, GROUP_ID, USED_NAME,START_COOPERATION_TIME
    )
    values (#{id,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{creditCode,jdbcType=VARCHAR},
    #{shortName,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, #{respName,jdbcType=VARCHAR},
    #{respTel,jdbcType=VARCHAR}, #{supplierType,jdbcType=VARCHAR}, #{supplierStatus,jdbcType=VARCHAR},
    #{registerProvince,jdbcType=VARCHAR}, #{registerCity,jdbcType=VARCHAR}, #{registerRegin,jdbcType=VARCHAR},
    #{registerAddress,jdbcType=VARCHAR}, #{contactAddress,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
    #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR},
    #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR},
    #{delFlag,jdbcType=VARCHAR}, #{entryTime,jdbcType=TIMESTAMP}, #{entryId,jdbcType=VARCHAR},
    #{entryName,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{usedName,jdbcType=VARCHAR}, #{startCooperationTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement">
    <!--@mbg.generated-->
    insert into uomp_supplier_management
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="creditCode != null">
        CREDIT_CODE,
      </if>
      <if test="shortName != null">
        SHORT_NAME,
      </if>
      <if test="tel != null">
        TEL,
      </if>
      <if test="respName != null">
        RESP_NAME,
      </if>
      <if test="respTel != null">
        RESP_TEL,
      </if>
      <if test="supplierType != null">
        SUPPLIER_TYPE,
      </if>
      <if test="supplierStatus != null">
        SUPPLIER_STATUS,
      </if>
      <if test="registerProvince != null">
        REGISTER_PROVINCE,
      </if>
      <if test="registerCity != null">
        REGISTER_CITY,
      </if>
      <if test="registerRegin != null">
        REGISTER_REGIN,
      </if>
      <if test="registerAddress != null">
        REGISTER_ADDRESS,
      </if>
      <if test="contactAddress != null">
        CONTACT_ADDRESS,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="entryTime != null">
        ENTRY_TIME,
      </if>
      <if test="entryId != null">
        ENTRY_ID,
      </if>
      <if test="entryName != null">
        ENTRY_NAME,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="usedName != null">
        USED_NAME,
      </if>
      <if test="startCooperationTime != null">
        START_COOPERATION_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="creditCode != null">
        #{creditCode,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="respName != null">
        #{respName,jdbcType=VARCHAR},
      </if>
      <if test="respTel != null">
        #{respTel,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=VARCHAR},
      </if>
      <if test="supplierStatus != null">
        #{supplierStatus,jdbcType=VARCHAR},
      </if>
      <if test="registerProvince != null">
        #{registerProvince,jdbcType=VARCHAR},
      </if>
      <if test="registerCity != null">
        #{registerCity,jdbcType=VARCHAR},
      </if>
      <if test="registerRegin != null">
        #{registerRegin,jdbcType=VARCHAR},
      </if>
      <if test="registerAddress != null">
        #{registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null">
        #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="entryTime != null">
        #{entryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="entryId != null">
        #{entryId,jdbcType=VARCHAR},
      </if>
      <if test="entryName != null">
        #{entryName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="usedName != null">
        #{usedName,jdbcType=VARCHAR},
      </if>
      <if test="startCooperationTime != null">
        #{startCooperationTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement">
    <!--@mbg.generated-->
    update uomp_supplier_management
    set SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},
      SHORT_NAME = #{shortName,jdbcType=VARCHAR},
      TEL = #{tel,jdbcType=VARCHAR},
      RESP_NAME = #{respName,jdbcType=VARCHAR},
      RESP_TEL = #{respTel,jdbcType=VARCHAR},
      SUPPLIER_TYPE = #{supplierType,jdbcType=VARCHAR},
      SUPPLIER_STATUS = #{supplierStatus,jdbcType=VARCHAR},
      REGISTER_PROVINCE = #{registerProvince,jdbcType=VARCHAR},
      REGISTER_CITY = #{registerCity,jdbcType=VARCHAR},
      REGISTER_REGIN = #{registerRegin,jdbcType=VARCHAR},
      REGISTER_ADDRESS = #{registerAddress,jdbcType=VARCHAR},
      CONTACT_ADDRESS = #{contactAddress,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      ENTRY_TIME = #{entryTime,jdbcType=TIMESTAMP},
      ENTRY_ID = #{entryId,jdbcType=VARCHAR},
      ENTRY_NAME = #{entryName,jdbcType=VARCHAR},
      GROUP_ID = #{groupId,jdbcType=VARCHAR},
      USED_NAME = #{usedName,jdbcType=VARCHAR},
      START_COOPERATION_TIME = #{startCooperationTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateById" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement">
    update uomp_supplier_management
    <trim prefix="SET" suffixOverrides=",">
      <if test="supplierName != null and supplierName != ''">SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},</if>
      <if test="creditCode != null and supplierName != ''">CREDIT_CODE = #{creditCode,jdbcType=VARCHAR},</if>
      <if test="shortName != null">SHORT_NAME = #{shortName,jdbcType=VARCHAR},</if>
      <if test="tel != null and tel != ''">TEL = #{tel,jdbcType=VARCHAR},</if>
      <if test="respName != null and respName != ''">RESP_NAME = #{respName,jdbcType=VARCHAR},</if>
      <if test="respTel != null and respTel != ''">RESP_TEL = #{respTel,jdbcType=VARCHAR},</if>
      <if test="supplierType != null and supplierType != ''">SUPPLIER_TYPE = #{supplierType,jdbcType=VARCHAR},</if>
      <if test="supplierStatus != null and supplierStatus != ''">SUPPLIER_STATUS = #{supplierStatus,jdbcType=VARCHAR},</if>
      <if test="registerAddress != null">REGISTER_ADDRESS = #{registerAddress,jdbcType=VARCHAR},</if>
      <if test="contactAddress != null">CONTACT_ADDRESS = #{contactAddress,jdbcType=VARCHAR},</if>
      <if test="remark != null">REMARK = #{remark,jdbcType=VARCHAR},</if>
      <if test="groupId != null">GROUP_ID = #{groupId,jdbcType=VARCHAR},</if>
      <if test="delFlag != null">DEL_FLAG = #{delFlag,jdbcType=VARCHAR},</if>
      <if test="usedName != null">USED_NAME = #{usedName,jdbcType=VARCHAR},</if>
      <if test="startCooperationTime != null">      START_COOPERATION_TIME = #{startCooperationTime,jdbcType=TIMESTAMP},</if>
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    </trim>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="supplierServiceYears" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO">
    select TIMESTAMPDIFF(YEAR, START_COOPERATION_TIME, date_format(now(), '%Y-%m-%d %H:%i:%S')) as num, usm.SUPPLIER_NAME as name
    FROM uomp_supplier_management usm
    where START_COOPERATION_TIME  is not null and DEL_FLAG ='0'
  </select>

  <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from uomp_supplier_management
      <where>
        <if test="whereSql!=null">
          ${whereSql}
        </if>
      </where>
      <if test="orderBySql!=null">
        ORDER BY ${orderBySql}
      </if>
  </select>

<!--  <select id="selectCreditCodeById" resultMap="BaseResultMap">-->
<!--    select CREDIT_CODE,SUPPLIER_NAME,GROUP_ID from UOMP_SUPPLIER_MANAGEMENT where ID = #{id} and DEL_FLAG = '0'-->
<!--  </select>-->
  <select id="selectIdByCreditCode" resultMap="BaseResultMap">
    select ID from UOMP_SUPPLIER_MANAGEMENT where CREDIT_CODE = #{credit_code} and DEL_FLAG = '0'
  </select>
  <select id="selectIdBySupplierName" resultMap="BaseResultMap">
    select ID from UOMP_SUPPLIER_MANAGEMENT where SUPPLIER_NAME = #{supplier_name} and DEL_FLAG = '0'
  </select>
  <select id="selectSupplierIdByGroupId" resultType="java.lang.String">
    select id from UOMP_SUPPLIER_MANAGEMENT where GROUP_ID = #{orgId} limit 1
  </select>
  <select id="pageSupplierToProject" resultType="cn.gwssi.ecloud.staffpool.dto.UompProjectSupplierListDTO">
      select a.ID,a.SUPPLIER_NAME as supplierName,a.CREDIT_CODE as creditCode,a.SHORT_NAME as shortName,a.SUPPLIER_STATUS as supplierStatus,b.NAME_ as supplierStatusName
      from UOMP_SUPPLIER_MANAGEMENT a
      left join SYS_DATA_DICT b on a.SUPPLIER_STATUS = b.KEY_ and b.DICT_KEY_ = 'UOMP_SUPPLIER_STATE'
      <where>
        <if test="whereSql!=null">
          ${whereSql}
        </if>
        <if test="supplierName !=null and supplierName != ''">
          and instr(a.SUPPLIER_NAME, #{supplierName})>0
        </if>
      </where>
      <if test="orderBySql!=null">
        ORDER BY ${orderBySql}
      </if>
  </select>

  <select id="selectIdAndSupplierNameBySupplierStatus"
          resultType="cn.gwssi.ecloud.staffpool.dto.SupplierManagementDTO">
    select ID as id, SUPPLIER_NAME as supplierName
    from UOMP_SUPPLIER_MANAGEMENT
    where SUPPLIER_STATUS = '1'
      AND DEL_FLAG = '0'
  </select>

  <select id="selectSupplierTypeBySupplierStatus" resultType="cn.gwssi.ecloud.staffpool.dto.SupplierManagementDTO">
    select distinct SUPPLIER_TYPE as supplierType
    from UOMP_SUPPLIER_MANAGEMENT
    where SUPPLIER_STATUS = '1'
      AND DEL_FLAG = '0'
  </select>

  <select id="selectAllBySupplierType" resultType="cn.gwssi.ecloud.staffpool.dto.SupplierManagementDTO">
    select ID as id, SUPPLIER_NAME as supplierName, SUPPLIER_TYPE as supplierType
    from UOMP_SUPPLIER_MANAGEMENT
    where SUPPLIER_STATUS = '1'
    AND DEL_FLAG = '0'
    and supplier_type like concat('%', #{types}, '%')
  </select>

  <select id="selectIdByWorkingCompany" resultType="java.lang.String">
      select ID
      from UOMP_SUPPLIER_MANAGEMENT
      where DEL_FLAG = '0'
        and SUPPLIER_STATUS = '1'
        and SUPPLIER_NAME = #{workingCompany} limit 1
  </select>

  <select id="selectGroupIdByWorkingCompany" resultType="java.lang.String">
      select GROUP_ID
      from UOMP_SUPPLIER_MANAGEMENT
      where DEL_FLAG = '0'
        and SUPPLIER_STATUS = '1'
        and SUPPLIER_NAME = #{workingCompany} limit 1
  </select>
</mapper>