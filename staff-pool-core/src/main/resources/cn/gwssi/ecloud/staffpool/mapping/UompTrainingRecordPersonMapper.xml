<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTrainingRecordPersonMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson">
    <!--@mbg.generated-->
    <!--@Table uomp_training_record_person-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TRAINING_RECORD_ID" jdbcType="VARCHAR" property="trainingRecordId" />
    <result column="TRAINING_PERSON_ID" jdbcType="VARCHAR" property="trainingPersonId" />
    <result column="TRAINING_PERSON_NAME" jdbcType="VARCHAR" property="trainingPersonName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRAINING_RECORD_ID, TRAINING_PERSON_ID, TRAINING_PERSON_NAME
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_training_record_person
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_training_record_person
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
    <delete id="deleteByRecordId">
      delete from uomp_training_record_person
      where TRAINING_RECORD_ID = #{id}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson">
    <!--@mbg.generated-->
    insert into uomp_training_record_person (ID, TRAINING_RECORD_ID, TRAINING_PERSON_ID,
    TRAINING_PERSON_NAME)
    values (#{id,jdbcType=VARCHAR}, #{trainingRecordId,jdbcType=VARCHAR}, #{trainingPersonId,jdbcType=VARCHAR},
    #{trainingPersonName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson">
    <!--@mbg.generated-->
    insert into uomp_training_record_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="trainingRecordId != null">
        TRAINING_RECORD_ID,
      </if>
      <if test="trainingPersonId != null">
        TRAINING_PERSON_ID,
      </if>
      <if test="trainingPersonName != null">
        TRAINING_PERSON_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="trainingRecordId != null">
        #{trainingRecordId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonId != null">
        #{trainingPersonId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonName != null">
        #{trainingPersonName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson">
    <!--@mbg.generated-->
    update uomp_training_record_person
    <set>
      <if test="trainingRecordId != null">
        TRAINING_RECORD_ID = #{trainingRecordId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonId != null">
        TRAINING_PERSON_ID = #{trainingPersonId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonName != null">
        TRAINING_PERSON_NAME = #{trainingPersonName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson">
    <!--@mbg.generated-->
    update uomp_training_record_person
    set TRAINING_RECORD_ID = #{trainingRecordId,jdbcType=VARCHAR},
    TRAINING_PERSON_ID = #{trainingPersonId,jdbcType=VARCHAR},
    TRAINING_PERSON_NAME = #{trainingPersonName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <!--  //按照培训开始日期倒叙，取前3个-->
  <select id="selectAllByLimit3" resultType="cn.gwssi.ecloud.staffpool.dto.TrainingDTO">
    select TRAINING_NAME as trainingName,
           TRAINING_BEGIN_TIME as trainingTime,
           SIGN_IN_NUM as signInNum
    from UOMP_TRAINING_RECORD
    order by TRAINING_BEGIN_TIME desc
  </select>
</mapper>
