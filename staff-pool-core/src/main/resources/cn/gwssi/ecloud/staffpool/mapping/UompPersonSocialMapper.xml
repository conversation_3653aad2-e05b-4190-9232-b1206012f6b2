<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonSocialMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    <!--@Table uomp_person_social-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="RELA_NAME" jdbcType="VARCHAR" property="relaName" />
    <result column="RELA_AGE" jdbcType="VARCHAR" property="relaAge" />
    <result column="NATIONAL" jdbcType="VARCHAR" property="national" />
    <result column="POLITICS_STATUS" jdbcType="VARCHAR" property="politicsStatus" />
    <result column="RELATION_WITH_MYSELF" jdbcType="VARCHAR" property="relationWithMyself" />
    <result column="RELA_TEL" jdbcType="VARCHAR" property="relaTel" />
    <result column="RELA_ADDRESS" jdbcType="VARCHAR" property="relaAddress" />
    <result column="RELA_POST" jdbcType="VARCHAR" property="relaPost" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, RELA_NAME, RELA_AGE, `NATIONAL`, POLITICS_STATUS, RELATION_WITH_MYSELF, 
    RELA_TEL, RELA_ADDRESS, RELA_POST, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, 
    UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_person_social
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="query" resultMap="BaseResultMap">
    select *
    from uomp_person_social
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_social
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPersonId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_social
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    insert into uomp_person_social (ID, PERSON_ID, RELA_NAME, 
      RELA_AGE, `NATIONAL`, POLITICS_STATUS, 
      RELATION_WITH_MYSELF, RELA_TEL, RELA_ADDRESS, 
      RELA_POST, CREATE_BY, CREATE_TIME, 
      CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
      UPDATE_ORG_ID, DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{relaName,jdbcType=VARCHAR}, 
      #{relaAge,jdbcType=VARCHAR}, #{national,jdbcType=VARCHAR}, #{politicsStatus,jdbcType=VARCHAR}, 
      #{relationWithMyself,jdbcType=VARCHAR}, #{relaTel,jdbcType=VARCHAR}, #{relaAddress,jdbcType=VARCHAR}, 
      #{relaPost,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    insert into uomp_person_social
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="relaName != null">
        RELA_NAME,
      </if>
      <if test="relaAge != null">
        RELA_AGE,
      </if>
      <if test="national != null">
        `NATIONAL`,
      </if>
      <if test="politicsStatus != null">
        POLITICS_STATUS,
      </if>
      <if test="relationWithMyself != null">
        RELATION_WITH_MYSELF,
      </if>
      <if test="relaTel != null">
        RELA_TEL,
      </if>
      <if test="relaAddress != null">
        RELA_ADDRESS,
      </if>
      <if test="relaPost != null">
        RELA_POST,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="relaName != null">
        #{relaName,jdbcType=VARCHAR},
      </if>
      <if test="relaAge != null">
        #{relaAge,jdbcType=VARCHAR},
      </if>
      <if test="national != null">
        #{national,jdbcType=VARCHAR},
      </if>
      <if test="politicsStatus != null">
        #{politicsStatus,jdbcType=VARCHAR},
      </if>
      <if test="relationWithMyself != null">
        #{relationWithMyself,jdbcType=VARCHAR},
      </if>
      <if test="relaTel != null">
        #{relaTel,jdbcType=VARCHAR},
      </if>
      <if test="relaAddress != null">
        #{relaAddress,jdbcType=VARCHAR},
      </if>
      <if test="relaPost != null">
        #{relaPost,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    update uomp_person_social
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="relaName != null">
        RELA_NAME = #{relaName,jdbcType=VARCHAR},
      </if>
      <if test="relaAge != null">
        RELA_AGE = #{relaAge,jdbcType=VARCHAR},
      </if>
      <if test="national != null">
        `NATIONAL` = #{national,jdbcType=VARCHAR},
      </if>
      <if test="politicsStatus != null">
        POLITICS_STATUS = #{politicsStatus,jdbcType=VARCHAR},
      </if>
      <if test="relationWithMyself != null">
        RELATION_WITH_MYSELF = #{relationWithMyself,jdbcType=VARCHAR},
      </if>
      <if test="relaTel != null">
        RELA_TEL = #{relaTel,jdbcType=VARCHAR},
      </if>
      <if test="relaAddress != null">
        RELA_ADDRESS = #{relaAddress,jdbcType=VARCHAR},
      </if>
      <if test="relaPost != null">
        RELA_POST = #{relaPost,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    update uomp_person_social
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      RELA_NAME = #{relaName,jdbcType=VARCHAR},
      RELA_AGE = #{relaAge,jdbcType=VARCHAR},
      `NATIONAL` = #{national,jdbcType=VARCHAR},
      POLITICS_STATUS = #{politicsStatus,jdbcType=VARCHAR},
      RELATION_WITH_MYSELF = #{relationWithMyself,jdbcType=VARCHAR},
      RELA_TEL = #{relaTel,jdbcType=VARCHAR},
      RELA_ADDRESS = #{relaAddress,jdbcType=VARCHAR},
      RELA_POST = #{relaPost,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateByPersonId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial">
    <!--@mbg.generated-->
    update uomp_person_social
    <set>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </update>
    <update id="updateByPersonIds">
      update uomp_person_social set
      DEL_FLAG = '1',
      UPDATE_ORG_ID = #{orgId},
      where PERSON_ID in
      <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </update>
  <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonSocialDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="RELA_NAME" jdbcType="VARCHAR" property="relaName" />
    <result column="RELA_AGE" jdbcType="VARCHAR" property="relaAge" />
    <result column="NATIONAL" jdbcType="VARCHAR" property="national" />
    <result column="POLITICS_STATUS" jdbcType="VARCHAR" property="politicsStatus" />
    <result column="RELATION_WITH_MYSELF" jdbcType="VARCHAR" property="relationWithMyself" />
    <result column="RELA_TEL" jdbcType="VARCHAR" property="relaTel" />
    <result column="RELA_ADDRESS" jdbcType="VARCHAR" property="relaAddress" />
    <result column="RELA_POST" jdbcType="VARCHAR" property="relaPost" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard" />
  </resultMap>
  <select id="selectByPersonIds" resultMap="BasePersonMap">
    select
    a.ID,
    a.PERSON_ID,
    a.RELA_NAME,
    a.RELA_AGE,
    a.NATIONAL,
    a.POLITICS_STATUS,
    a.RELATION_WITH_MYSELF,
    a.RELA_TEL,
    a.RELA_ADDRESS,
    a.RELA_POST,
    b.PERSON_NAME,
    b.PERSON_CARD
    from
    UOMP_PERSON_SOCIAL a
    left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
    where a.DEL_FLAG = '0' and b.DEL_FLAG ='0' and a.PERSON_ID in
    <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by a.PERSON_ID desc,a.CREATE_TIME desc
  </select>

</mapper>