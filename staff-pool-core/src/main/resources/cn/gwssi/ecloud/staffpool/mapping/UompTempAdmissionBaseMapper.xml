<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTempAdmissionBaseMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmissionBase">
    <!--@mbg.generated-->
    <!--@Table uomp_temp_admission_base-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLY_CODE" jdbcType="VARCHAR" property="applyCode" />
    <result column="APPLY_TITLE" jdbcType="VARCHAR" property="applyTitle" />
    <result column="DEST_CLERK_ID" jdbcType="VARCHAR" property="destClerkId" />
    <result column="DEST_CLERK_NAME" jdbcType="VARCHAR" property="destClerkName" />
    <result column="PLAN_VISIT_TIME" jdbcType="TIMESTAMP" property="planVisitTime" />
    <result column="JOB_CONTENT" jdbcType="VARCHAR" property="jobContent" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
    <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="ACCEPT_NAME" jdbcType="VARCHAR" property="acceptName" />
    <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="ENGAGEMENT_PROJECT" jdbcType="TIMESTAMP" property="engagementProject" />
    <result column="ENGAGEMENT_PROJECT_ID" jdbcType="VARCHAR" property="engagementProjectId" />
    <result column="ENGAGEMENT_PROJECT_JSON" jdbcType="VARCHAR" property="engagementProjectJson" />
    <result column="FILING_STATUS" jdbcType="VARCHAR" property="filingStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_CODE, APPLY_TITLE, DEST_CLERK_ID, DEST_CLERK_NAME, PLAN_VISIT_TIME, JOB_CONTENT, 
    INST_ID, APPLY_STATUS, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
    UPDATE_ORG_ID, DEL_FLAG, ACCEPT_NAME, APPLY_TIME, ENGAGEMENT_PROJECT, ENGAGEMENT_PROJECT_ID, ENGAGEMENT_PROJECT_JSON, FILING_STATUS
  </sql>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmissionBase">
    <!--@mbg.generated-->
    insert into uomp_temp_admission_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applyCode != null">
        APPLY_CODE,
      </if>
      <if test="applyTitle != null">
        APPLY_TITLE,
      </if>
      <if test="destClerkId != null">
        DEST_CLERK_ID,
      </if>
      <if test="destClerkName != null">
        DEST_CLERK_NAME,
      </if>
      <if test="planVisitTime != null">
        PLAN_VISIT_TIME,
      </if>
      <if test="jobContent != null">
        JOB_CONTENT,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
      <if test="applyStatus != null">
        APPLY_STATUS,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="acceptName != null">
        ACCEPT_NAME,
      </if>
      <if test="applyTime != null">
        APPLY_TIME,
      </if>
      <if test="engagementProject != null">
        ENGAGEMENT_PROJECT,
      </if>
      <if test="engagementProjectId != null">
        ENGAGEMENT_PROJECT_ID,
      </if>
      <if test="engagementProjectJson != null">
        ENGAGEMENT_PROJECT_JSON,
      </if>
      <if test="filingStatus != null">
        FILING_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applyCode != null">
        #{applyCode,jdbcType=VARCHAR},
      </if>
      <if test="applyTitle != null">
        #{applyTitle,jdbcType=VARCHAR},
      </if>
      <if test="destClerkId != null">
        #{destClerkId,jdbcType=VARCHAR},
      </if>
      <if test="destClerkName != null">
        #{destClerkName,jdbcType=VARCHAR},
      </if>
      <if test="planVisitTime != null">
        #{planVisitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="jobContent != null">
        #{jobContent,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="acceptName != null">
        #{acceptName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="engagementProject != null">
        #{engagementProject,jdbcType=TIMESTAMP},
      </if>
      <if test="engagementProjectId != null">
        #{engagementProjectId,jdbcType=VARCHAR},
      </if>
      <if test="engagementProjectJson != null">
        #{engagementProjectJson,jdbcType=VARCHAR},
      </if>
      <if test="filingStatus != null">
        #{filingStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>