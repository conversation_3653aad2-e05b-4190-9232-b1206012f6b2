<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgTeamMapper">
    <sql id="baseColumn">
        id_,name_,sn_,create_time_,create_by_,update_time_,update_by_,desc_
    </sql>

    <select id="selectOrgGroupTypeList" resultType="java.lang.String">
        select TYPE_ as orgType from ORG_GROUP where ID_ = #{orgId}
    </select>

</mapper>