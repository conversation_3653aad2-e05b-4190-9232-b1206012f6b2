<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompReportFileMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
    <!--@mbg.generated-->
    <!--@Table uomp_report_file-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize" />
    <result column="UPLOAD_TIME" jdbcType="VARCHAR" property="uploadTime" />
    <result column="UPLOADER_ID" jdbcType="VARCHAR" property="uploaderId" />
    <result column="UPLOADER_NAME" jdbcType="VARCHAR" property="uploaderName" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, REPORT_ID, FILE_ID, FILE_NAME, FILE_SIZE, UPLOAD_TIME, UPLOADER_ID, UPLOADER_NAME, 
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_report_file
    where ID = #{id,jdbcType=VARCHAR} and DEL_FLAG = ''
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_report_file
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
    <!--@mbg.generated-->
    insert into uomp_report_file (ID, REPORT_ID, FILE_ID, 
      FILE_NAME, FILE_SIZE, UPLOAD_TIME, 
      UPLOADER_ID, UPLOADER_NAME, CREATE_BY, 
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, 
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileSize,jdbcType=VARCHAR}, #{uploadTime,jdbcType=VARCHAR}, 
      #{uploaderId,jdbcType=VARCHAR}, #{uploaderName,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
    <!--@mbg.generated-->
    insert into uomp_report_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="reportId != null">
        REPORT_ID,
      </if>
      <if test="fileId != null">
        FILE_ID,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="fileSize != null">
        FILE_SIZE,
      </if>
      <if test="uploadTime != null">
        UPLOAD_TIME,
      </if>
      <if test="uploaderId != null">
        UPLOADER_ID,
      </if>
      <if test="uploaderName != null">
        UPLOADER_NAME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null">
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=VARCHAR},
      </if>
      <if test="uploaderId != null">
        #{uploaderId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderName != null">
        #{uploaderName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
    <!--@mbg.generated-->
    update uomp_report_file
    <set>
      <if test="reportId != null">
        REPORT_ID = #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        FILE_ID = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        FILE_SIZE = #{fileSize,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        UPLOAD_TIME = #{uploadTime,jdbcType=VARCHAR},
      </if>
      <if test="uploaderId != null">
        UPLOADER_ID = #{uploaderId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderName != null">
        UPLOADER_NAME = #{uploaderName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
    <!--@mbg.generated-->
    update uomp_report_file
    set REPORT_ID = #{reportId,jdbcType=VARCHAR},
      FILE_ID = #{fileId,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      FILE_SIZE = #{fileSize,jdbcType=VARCHAR},
      UPLOAD_TIME = #{uploadTime,jdbcType=VARCHAR},
      UPLOADER_ID = #{uploaderId,jdbcType=VARCHAR},
      UPLOADER_NAME = #{uploaderName,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_report_file
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <update id="updateDelFlag" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReportFile">
     update uomp_report_file
     set UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
     where REPORT_ID = #{reportId,jdbcType=VARCHAR}
  </update>
</mapper>