<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPermissionOutBaseMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutBase">
    <!--@mbg.generated-->
    <!--@Table uomp_permission_out_base-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLY_ID" jdbcType="VARCHAR" property="applyId" />
    <result column="OUT_APPLY_SYS_TYPE" jdbcType="VARCHAR" property="outApplySysType" />
    <result column="OUT_APPLY_SYS_ID" jdbcType="VARCHAR" property="outApplySysId" />
    <result column="OUT_APPLY_SYS_NAME" jdbcType="VARCHAR" property="outApplySysName" />
    <result column="OUT_APPLY_SYS_JSON" jdbcType="VARCHAR" property="outApplySysJson" />
    <result column="OUT_APPLY_TITLE" jdbcType="VARCHAR" property="outApplyTitle" />
    <result column="OUT_APPLY_USER_ID" jdbcType="VARCHAR" property="outApplyUserId" />
    <result column="OUT_APPLY_USER_NAME" jdbcType="VARCHAR" property="outApplyUserName" />
    <result column="OUT_APPLY_TIME" jdbcType="TIMESTAMP" property="outApplyTime" />
    <result column="OUT_APPLY_MATTER" jdbcType="VARCHAR" property="outApplyMatter" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_ID, OUT_APPLY_SYS_TYPE, OUT_APPLY_SYS_ID, OUT_APPLY_SYS_NAME, OUT_APPLY_SYS_JSON, 
    OUT_APPLY_TITLE, OUT_APPLY_USER_ID, OUT_APPLY_USER_NAME, OUT_APPLY_TIME, OUT_APPLY_MATTER, 
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_permission_out_base
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_permission_out_base
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutBase">
    <!--@mbg.generated-->
    insert into uomp_permission_out_base (ID, APPLY_ID, OUT_APPLY_SYS_TYPE, 
      OUT_APPLY_SYS_ID, OUT_APPLY_SYS_NAME, OUT_APPLY_SYS_JSON, 
      OUT_APPLY_TITLE, OUT_APPLY_USER_ID, OUT_APPLY_USER_NAME, 
      OUT_APPLY_TIME, OUT_APPLY_MATTER, CREATE_BY, 
      CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
      DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{applyId,jdbcType=VARCHAR}, #{outApplySysType,jdbcType=VARCHAR}, 
      #{outApplySysId,jdbcType=VARCHAR}, #{outApplySysName,jdbcType=VARCHAR}, #{outApplySysJson,jdbcType=VARCHAR}, 
      #{outApplyTitle,jdbcType=VARCHAR}, #{outApplyUserId,jdbcType=VARCHAR}, #{outApplyUserName,jdbcType=VARCHAR}, 
      #{outApplyTime,jdbcType=TIMESTAMP}, #{outApplyMatter,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutBase">
    <!--@mbg.generated-->
    insert into uomp_permission_out_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applyId != null">
        APPLY_ID,
      </if>
      <if test="outApplySysType != null">
        OUT_APPLY_SYS_TYPE,
      </if>
      <if test="outApplySysId != null">
        OUT_APPLY_SYS_ID,
      </if>
      <if test="outApplySysName != null">
        OUT_APPLY_SYS_NAME,
      </if>
      <if test="outApplySysJson != null">
        OUT_APPLY_SYS_JSON,
      </if>
      <if test="outApplyTitle != null">
        OUT_APPLY_TITLE,
      </if>
      <if test="outApplyUserId != null">
        OUT_APPLY_USER_ID,
      </if>
      <if test="outApplyUserName != null">
        OUT_APPLY_USER_NAME,
      </if>
      <if test="outApplyTime != null">
        OUT_APPLY_TIME,
      </if>
      <if test="outApplyMatter != null">
        OUT_APPLY_MATTER,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=VARCHAR},
      </if>
      <if test="outApplySysType != null">
        #{outApplySysType,jdbcType=VARCHAR},
      </if>
      <if test="outApplySysId != null">
        #{outApplySysId,jdbcType=VARCHAR},
      </if>
      <if test="outApplySysName != null">
        #{outApplySysName,jdbcType=VARCHAR},
      </if>
      <if test="outApplySysJson != null">
        #{outApplySysJson,jdbcType=VARCHAR},
      </if>
      <if test="outApplyTitle != null">
        #{outApplyTitle,jdbcType=VARCHAR},
      </if>
      <if test="outApplyUserId != null">
        #{outApplyUserId,jdbcType=VARCHAR},
      </if>
      <if test="outApplyUserName != null">
        #{outApplyUserName,jdbcType=VARCHAR},
      </if>
      <if test="outApplyTime != null">
        #{outApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outApplyMatter != null">
        #{outApplyMatter,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutBase">
    <!--@mbg.generated-->
    update uomp_permission_out_base
    set APPLY_ID = #{applyId,jdbcType=VARCHAR},
      OUT_APPLY_SYS_TYPE = #{outApplySysType,jdbcType=VARCHAR},
      OUT_APPLY_SYS_ID = #{outApplySysId,jdbcType=VARCHAR},
      OUT_APPLY_SYS_NAME = #{outApplySysName,jdbcType=VARCHAR},
      OUT_APPLY_SYS_JSON = #{outApplySysJson,jdbcType=VARCHAR},
      OUT_APPLY_TITLE = #{outApplyTitle,jdbcType=VARCHAR},
      OUT_APPLY_USER_ID = #{outApplyUserId,jdbcType=VARCHAR},
      OUT_APPLY_USER_NAME = #{outApplyUserName,jdbcType=VARCHAR},
      OUT_APPLY_TIME = #{outApplyTime,jdbcType=TIMESTAMP},
      OUT_APPLY_MATTER = #{outApplyMatter,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>