<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.TeamRiskMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="config_id" jdbcType="VARCHAR" property="configId"/>
        <result column="name_" jdbcType="VARCHAR" property="configId"/>
        <result column="risk_level" jdbcType="VARCHAR" property="level"/>
        <result column="change_level" jdbcType="VARCHAR" property="changeLevel"/>
        <result column="reason_" jdbcType="VARCHAR" property="reason"/>
        <result column="attach_" jdbcType="VARCHAR" property="attach"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk">
        insert into uomp_team_risk
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="level != null">
                risk_level,
            </if>
            <if test="changeLevel != null">
                change_level,
            </if>
            <if test="reason != null">
                reason_,
            </if>
            <if test="attach != null">
                attach_,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="changeLevel != null">
                #{changeLevel,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="attach != null">
                #{attach,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="createRiskHis" parameterType="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk">
        insert into uomp_team_risk_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="level != null">
                risk_level,
            </if>
            <if test="changeLevel != null">
                change_level,
            </if>
            <if test="reason != null">
                reason_,
            </if>
            <if test="attach != null">
                attach_,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="changeLevel != null">
                #{changeLevel,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="attach != null">
                #{attach,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        utr.id,
        utr.user_id,
        utr.config_id,
        conf.name_,
        utr.risk_level,
        utr.change_level,
        utr.reason_,
        utr.attach_,
        utr.update_user,
        utr.update_time,
        utr.update_by
        from uomp_team_risk utr
        left join uomp_team_risk_config conf on conf.id = utr.config_id
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        <if test="orderBySql == null">
            ORDER BY utr.id asc
        </if>
    </select>


    <select id="queryRiskList" resultType="cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskVO">
        select
        upi.id as id,
        upi.PERSON_NAME as userName,
        upi.org_user_id as userId,
        group_concat(CONCAT(utr.config_id,'-',utr.change_level) as configIds,
        upi.WORKING_COMPANY as serviceProvider,
        upi.ORG_GROUP_NAME as teamName
        from uomp_person_info upi
        inner join uomp_team_risk utr on utr.user_id = upi.id
        where 1=1
        <if test="whereSql != null">
            and ${whereSql}
        </if>
        <if test="queryLevel != null and queryLevel == 'high'">
            and utr.change_level = '1'}
        </if>
        <if test="queryLevel != null and queryLevel == 'med'">
            and utr.change_level = '2'
            and upi.id not in (select user_id from uomp_team_risk where change_level = '1')
        </if>
        <if test="queryLevel != null and queryLevel == 'low'">
            and upi.id not in (select user_id from uomp_team_risk where change_level = '1' and change_level = '2')
        </if>
        group by upi.id
    </select>
    <select id="queryRiskHisList" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        utr.id,
        utr.user_id,
        utr.config_id,
        conf.name_,
        utr.risk_level,
        utr.change_level,
        utr.reason_,
        utr.attach_,
        utr.create_user,
        utr.create_time,
        utr.create_by
        from uomp_team_risk_his utr
        left join uomp_team_risk_config conf on conf.id = utr.config_id
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        <if test="orderBySql == null">
            ORDER BY utr.id asc
        </if>
    </select>
</mapper>