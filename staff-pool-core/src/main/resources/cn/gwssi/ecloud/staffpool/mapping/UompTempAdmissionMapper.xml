<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTempAdmissionMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission">
        <!--@mbg.generated-->
        <!--@Table uomp_temp_admission-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="PLAN_VISIT_TIME" jdbcType="TIMESTAMP" property="planVisitTime"/>
        <result column="REAL_VISIT_TIME" jdbcType="TIMESTAMP" property="realVisitTime"/>
        <result column="DEST_CLERK_ID" jdbcType="VARCHAR" property="destClerkId"/>
        <result column="DEST_CLERK_NAME" jdbcType="VARCHAR" property="destClerkName"/>
        <result column="EXIT_TIME" jdbcType="TIMESTAMP" property="exitTime"/>
        <result column="JOB_CONTENT" jdbcType="VARCHAR" property="jobContent"/>
        <result column="MANAGER_COMMENT" jdbcType="VARCHAR" property="managerComment"/>
        <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="APPLY_CODE" jdbcType="VARCHAR" property="applyCode"/>
        <result column="APPLY_TITLE" jdbcType="VARCHAR" property="applyTitle"/>
        <result column="BLACKLIST" jdbcType="VARCHAR" property="blacklist"/>
        <result column="BLACKLIST_REASON" jdbcType="VARCHAR" property="blacklistReason"/>
        <result column="BASE_ID" jdbcType="VARCHAR" property="baseId"/>
        <result column="ACCEPT_NAME" jdbcType="VARCHAR" property="acceptName"/>
        <result column="SEX" jdbcType="VARCHAR" property="sex"/>
        <result column="APPLICAT_DUTY" jdbcType="VARCHAR" property="applicatDuty"/>
        <result column="ENGAGEMENT_PROJECT" jdbcType="TIMESTAMP" property="engagementProject"/>
        <result column="ENGAGEMENT_PROJECT_ID" jdbcType="VARCHAR" property="engagementProjectId"/>
        <result column="ENGAGEMENT_PROJECT_JSON" jdbcType="VARCHAR" property="engagementProjectJson"/>
        <result column="FILING_STATUS" jdbcType="VARCHAR" property="filingStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        PERSON_NAME,
        PERSON_CARD,
        TEL,
        WORKING_COMPANY,
        PLAN_VISIT_TIME,
        REAL_VISIT_TIME,
        DEST_CLERK_ID,
        DEST_CLERK_NAME,
        EXIT_TIME,
        JOB_CONTENT,
        MANAGER_COMMENT,
        APPLY_STATUS,
        INST_ID,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG,
        APPLY_CODE,
        APPLY_TITLE,
        BLACKLIST,
        BLACKLIST_REASON,
        BASE_ID,
        ACCEPT_NAME,
        SEX,
        APPLICAT_DUTY,
        ENGAGEMENT_PROJECT,
        ENGAGEMENT_PROJECT_ID,
        ENGAGEMENT_PROJECT_JSON,
        FILING_STATUS
    </sql>
    <update id="updateByPersonNameAndCard">
        update uomp_temp_admission
        <set>
            BLACKLIST        = #{blacklist},
            BLACKLIST_REASON = #{blacklistReason},
        </set>
        where PERSON_NAME = #{personName}
          and PERSON_CARD = #{personCard}
    </update>
    <update id="update">
        update uomp_temp_admission
        <set>
            <if test="personName != null and personName != ''">
                PERSON_NAME = #{personName},
            </if>
            <if test="personCard != null and personCard != ''">
                PERSON_CARD = #{personCard},
            </if>
            <if test="tel != null and tel != ''">
                TEL = #{tel},
            </if>
            <if test="workingCompany != null and workingCompany != ''">
                WORKING_COMPANY = #{workingCompany},
            </if>
            <if test="planVisitTime != null">
                PLAN_VISIT_TIME = #{planVisitTime},
            </if>
            <if test="realVisitTime != null">
                REAL_VISIT_TIME = #{realVisitTime},
            </if>
            <if test="destClerkId != null and destClerkId != ''">
                DEST_CLERK_ID = #{destClerkId},
            </if>
            <if test="destClerkName != null and destClerkName != ''">
                DEST_CLERK_NAME = #{destClerkName},
            </if>
            <if test="exitTime != null">
                EXIT_TIME = #{exitTime},
            </if>
            <if test="jobContent != null and jobContent != ''">
                JOB_CONTENT = #{jobContent},
            </if>
            <if test="managerComment != null and managerComment != ''">
                MANAGER_COMMENT = #{managerComment},
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                APPLY_STATUS = #{applyStatus},
            </if>
            <if test="instId != null and instId != ''">
                INST_ID = #{instId},
            </if>
            <if test="createBy != null and createBy != ''">
                CREATE_BY = #{createBy},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime},
            </if>
            <if test="createOrgId != null and createOrgId != ''">
                CREATE_ORG_ID = #{createOrgId},
            </if>
            <if test="updateBy != null and updateBy != ''">
                UPDATE_BY = #{updateBy},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime},
            </if>
            <if test="updateOrgId != null and updateOrgId != ''">
                UPDATE_ORG_ID = #{updateOrgId},
            </if>
            <if test="delFlag != null and delFlag != ''">
                DEL_FLAG = #{delFlag},
            </if>
            <if test="applyCode != null and applyCode != ''">
                APPLY_CODE = #{applyCode},
            </if>
            <if test="applyTitle != null and applyTitle != ''">
                APPLY_TITLE = #{applyTitle},
            </if>
            <if test="blacklist != null and blacklist != ''">
                BLACKLIST = #{blacklist},
            </if>
            <if test="blacklistReason != null and blacklistReason != ''">
                BLACKLIST_REASON = #{blacklistReason},
            </if>
            <if test="baseId != null and baseId != ''">
                BASE_ID = #{baseId},
            </if>
            <if test="acceptName != null and acceptName != ''">
                ACCEPT_NAME = #{acceptName},
            </if>
            <if test="sex != null and sex != ''">
                SEX = #{sex},
            </if>
            <if test="applicatDuty != null and applicatDuty != ''">
                APPLICAT_DUTY = #{applicatDuty},
            </if>
            <if test="engagementProject != null and applicatDuty != ''">
                ENGAGEMENT_PROJECT = #{engagementProject},
            </if>
            <if test="engagementProjectId != null and applicatDuty != ''">
                ENGAGEMENT_PROJECT_ID = #{engagementProjectId},
            </if>
            <if test="engagementProjectJson != null and applicatDuty != ''">
                ENGAGEMENT_PROJECT_JSON = #{engagementProjectJson},
            </if>
            <if test="filingStatus != null and applicatDuty != ''">
                FILING_STATUS = #{filingStatus},
            </if>
        </set>
        where ID = #{id}
    </update>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_temp_admission
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="countByRealVisitTime" resultType="java.lang.Integer">
        select count(ID) as num
        from UOMP_TEMP_ADMISSION
        where DEL_FLAG = '0'
          and APPLY_STATUS = '2'
          and REAL_VISIT_TIME like concat(#{time}, '%')
    </select>

    <select id="selectAllBySelective" resultType="cn.gwssi.ecloud.staffpool.dto.TempApplyDTO">
        select
            ID                      as id,
            PERSON_NAME             as personName,
            PERSON_CARD             as personCard,
            TEL                     as tel,
            WORKING_COMPANY         as workingCompany,
            REAL_VISIT_TIME         as realVisitTime,
            DEST_CLERK_ID           as destClerkId,
            DEST_CLERK_NAME         as destClerkName,
            EXIT_TIME               as exitTime,
            APPLY_CODE              as applyCode,
            JOB_CONTENT             as jobContent,
            ACCEPT_NAME             as acceptName,
            APPLICAT_DUTY           as applicatDuty,
            APPLY_STATUS            as applyStatus,
            CREATE_TIME             as createTime,
            FILING_STATUS           as filingStatus,
            ENGAGEMENT_PROJECT      as engagementProject,
            ENGAGEMENT_PROJECT_ID   as engagementProjectId,
            ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
            CREATE_BY               as createBy,
            CREATE_TIME             as createTimes
        from UOMP_TEMP_ADMISSION uta
        where uta.ID IN (
            select max(id)
            from
            (
            select a.ID                      as id,
                   a.PERSON_NAME             as personName,
                   a.PERSON_CARD             as personCard,
                   a.TEL                     as tel,
                   a.WORKING_COMPANY         as workingCompany,
                   a.REAL_VISIT_TIME         as realVisitTime,
                   a.DEST_CLERK_ID           as destClerkId,
                   a.DEST_CLERK_NAME         as destClerkName,
                   a.EXIT_TIME               as exitTime,
                   a.APPLY_CODE              as applyCode,
                   a.JOB_CONTENT             as jobContent,
                   a.ACCEPT_NAME             as acceptName,
                   a.APPLICAT_DUTY           as applicatDuty,
                   a.APPLY_STATUS            as applyStatus,
                   a.CREATE_TIME             as createTime,
                   a.FILING_STATUS           as filingStatus,
                   a.ENGAGEMENT_PROJECT      as engagementProject,
                   a.ENGAGEMENT_PROJECT_ID   as engagementProjectId,
                   a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
                   a.CREATE_BY               as createBy
            from UOMP_TEMP_ADMISSION a
                     left join UOMP_TEMP_ADMISSION_BASE b on a.BASE_ID = b.ID and b.DEL_FLAG = '0'
                     inner join bpm_instance bi on b.INST_ID = bi.id_ and bi.status_ != 'discard'
            where a.DEL_FLAG = '0'
              and a.BASE_ID is not null
            <if test="personName != null and personName != ''">
                and a.PERSON_NAME like concat('%', #{personName}, '%')
            </if>
            <if test="acceptName != null and acceptName != ''">
                and a.ACCEPT_NAME like concat('%', #{acceptName}, '%')
            </if>
            <if test="workingCompanyList != null and workingCompanyList.size() > 0">
                and a.WORKING_COMPANY_ID in
                <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="realVisitTimeBegin != null and realVisitTimeBegin != ''">
                and date_format(a.REAL_VISIT_TIME, '%Y-%m-%d %H:%i') >= #{realVisitTimeBegin}
            </if>
            <if test="realVisitTimeEnd != null and realVisitTimeEnd != ''">
                and date_format(a.REAL_VISIT_TIME, '%Y-%m-%d %H:%i') &lt;= #{realVisitTimeEnd}
            </if>
            <if test="ifSupplier == 1">
                and a.WORKING_COMPANY_ID = #{supplierId}
            </if>
            <if test="involvedProjectList != null and involvedProjectList.size() > 0">
                and a.ENGAGEMENT_PROJECT_ID in
                <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="filingStatus != null and filingStatus != ''">
                and a.FILING_STATUS = #{filingStatus}
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                and a.APPLY_STATUS = #{applyStatus}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and a.CREATE_BY = #{createdBy}
            </if>
            union all
            select a.ID                      as id,
                   a.PERSON_NAME             as personName,
                   a.PERSON_CARD             as personCard,
                   a.TEL                     as tel,
                   a.WORKING_COMPANY         as workingCompany,
                   a.REAL_VISIT_TIME         as realVisitTime,
                   a.DEST_CLERK_ID           as destClerkId,
                   a.DEST_CLERK_NAME         as destClerkName,
                   a.EXIT_TIME               as exitTime,
                   a.APPLY_CODE              as applyCode,
                   a.JOB_CONTENT             as jobContent,
                   a.ACCEPT_NAME             as acceptName,
                   a.APPLICAT_DUTY           as applicatDuty,
                   a.APPLY_STATUS            as applyStatus,
                   a.CREATE_TIME             as createTime,
                   a.FILING_STATUS           as filingStatus,
                   a.ENGAGEMENT_PROJECT      as engagementProject,
                   a.ENGAGEMENT_PROJECT_ID   as engagementProjectId,
                   a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
                   a.CREATE_BY               as createBy
            from UOMP_TEMP_ADMISSION a
            where a.DEL_FLAG = '0'
              and a.BASE_ID is null
            <if test="personName != null and personName != ''">
                and a.PERSON_NAME like concat('%', #{personName}, '%')
            </if>
            <if test="acceptName != null and acceptName != ''">
                and a.ACCEPT_NAME like concat('%', #{acceptName}, '%')
            </if>
            <if test="workingCompanyList != null and workingCompanyList.size() > 0">
                and a.WORKING_COMPANY_ID in
                <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="realVisitTimeBegin != null and realVisitTimeBegin != ''">
                and date_format(a.REAL_VISIT_TIME, '%Y-%m-%d %H:%i') >= #{realVisitTimeBegin}
            </if>
            <if test="realVisitTimeEnd != null and realVisitTimeEnd != ''">
                and date_format(a.REAL_VISIT_TIME, '%Y-%m-%d %H:%i') &lt;= #{realVisitTimeEnd}
            </if>
            <if test="ifSupplier == 1">
                and a.WORKING_COMPANY_ID = #{supplierId}
            </if>
            <if test="involvedProjectList != null and involvedProjectList.size() > 0">
                and a.ENGAGEMENT_PROJECT_ID in
                <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="filingStatus != null and filingStatus != ''">
                and a.FILING_STATUS = #{filingStatus}
            </if>
            <if test="applyStatus != null and applyStatus != ''">
                and a.APPLY_STATUS = #{applyStatus}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and a.CREATE_BY = #{createdBy}
            </if>
            ) as unionTemp
            where ((unionTemp.applyStatus != '0') or
                   (unionTemp.applyStatus = '0' and unionTemp.createBy = #{createdByStatus}))
            group by personCard,
                     personName
        )
        order by createTimes desc
    </select>

    <select id="selectInfoByName" resultType="cn.gwssi.ecloud.staffpool.dto.InfoByNameDTO">
        select PERSON_NAME     as personName,
               PERSON_CARD     as personCard,
               TEL             as tel,
               WORKING_COMPANY as workingCompany,
               SEX             as sex
        from UOMP_TEMP_ADMISSION
        where DEL_FLAG = '0'
          and APPLY_STATUS = '2'
          and BLACKLIST = '0'
          and PERSON_NAME = #{personName}
        order by CREATE_TIME desc
        limit 1
    </select>

    <select id="coutByPersonCard" resultType="java.lang.Integer">
        select COUNT(ID) as count
        from UOMP_TEMP_ADMISSION
        where DEL_FLAG = '0'
          and APPLY_STATUS = '2'
          and BLACKLIST = '1'
          and PERSON_CARD = #{personCard}
    </select>

    <select id="selectAllById" resultType="cn.gwssi.ecloud.staffpool.dto.UompTempAdmissionInfoDTO">
        select U0.ID              as id,
               U0.PERSON_NAME     as personName,
               U0.PERSON_CARD     as personCard,
               U0.TEL             as tel,
               U0.WORKING_COMPANY as workingCompany,
               U0.PLAN_VISIT_TIME as planVisitTime,
               U0.REAL_VISIT_TIME as realVisitTime,
               U0.DEST_CLERK_NAME as destClerkName,
               U0.EXIT_TIME       as exitTime,
               U0.JOB_CONTENT     as jobContent,
               U0.ACCEPT_NAME     as acceptName,
               U0.CREATE_BY       as createBy,
               b.INST_ID          as instId,
               U0.SEX             as sex,
               U0.APPLICAT_DUTY   as applicatDuty
        from UOMP_TEMP_ADMISSION as U0
                 left join UOMP_TEMP_ADMISSION_BASE b
                           on U0.BASE_ID = b.ID
        where U0.id = #{id}
    </select>

    <select id="selectPersonById" resultType="cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission">
        select PERSON_NAME as personName, PERSON_CARD as personCard
        from UOMP_TEMP_ADMISSION
        where ID = #{id}
    </select>

    <update id="updateBlacklistAndBlacklistReasonByPersonNameAndPersonCard">
        update UOMP_TEMP_ADMISSION
        set BLACKLIST        = '1',
            BLACKLIST_REASON = #{reason}
        where PERSON_NAME = #{personName}
          and PERSON_CARD = #{personCard}
    </update>

    <select id="selectAllByPersonCardOrPersonName" resultType="cn.gwssi.ecloud.staffpool.dto.TempApplyDTO">
        select *
        from (
        select a.ID                      as id,
               a.PERSON_NAME             as personName,
               a.PERSON_CARD             as personCard,
               a.TEL                     as tel,
               a.WORKING_COMPANY         as workingCompany,
               a.PLAN_VISIT_TIME         as planVisitTime,
               a.REAL_VISIT_TIME         as realVisitTime,
               a.DEST_CLERK_ID           as destClerkId,
               a.DEST_CLERK_NAME         as destClerkName,
               a.EXIT_TIME               as exitTime,
               a.JOB_CONTENT             as jobContent,
               a.APPLY_CODE              as applyCode,
               a.BLACKLIST               as blacklist,
               a.ACCEPT_NAME             as acceptName,
               a.ENGAGEMENT_PROJECT      as engagementProject,
               a.ENGAGEMENT_PROJECT_ID   as engagementProjectId,
               a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
               a.FILING_STATUS           as filingStatus,
               b.APPLY_TITLE             as applyTitle,
               a.APPLICAT_DUTY           as applicatDuty,
               a.SEX                     as sex,
               case
                   when b.APPLY_STATUS is null then a.APPLY_STATUS
                   else b.APPLY_STATUS
                   end                   as applyStatus,
               case
                   when b.INST_ID is null then a.INST_ID
                   else b.INST_ID
                   end                   as instId,
               a.CREATE_TIME             as createTime,
               a.CREATE_BY               as createBy
        from UOMP_TEMP_ADMISSION a
                 left join UOMP_TEMP_ADMISSION_BASE b on a.BASE_ID = b.ID and b.DEL_FLAG = '0'
                 inner join bpm_instance bi on b.INST_ID = bi.id_ and bi.status_ != 'discard'
        where a.DEL_FLAG = '0'
          and a.BASE_ID is not null
        <if test="personName != null and personName != ''">
            and PERSON_NAME = #{personName}
        </if>
        <if test="personCard != null and personCard != ''">
            and PERSON_CARD = #{personCard}
        </if>
        union all
        select a.ID                      as id,
               a.PERSON_NAME             as personName,
               a.PERSON_CARD             as personCard,
               a.TEL                     as tel,
               a.WORKING_COMPANY         as workingCompany,
               a.PLAN_VISIT_TIME         as planVisitTime,
               a.REAL_VISIT_TIME         as realVisitTime,
               a.DEST_CLERK_ID           as destClerkId,
               a.DEST_CLERK_NAME         as destClerkName,
               a.EXIT_TIME               as exitTime,
               a.JOB_CONTENT             as jobContent,
               a.APPLY_CODE              as applyCode,
               a.BLACKLIST               as blacklist,
               a.ACCEPT_NAME             as acceptName,
               a.ENGAGEMENT_PROJECT      as engagementProject,
               a.ENGAGEMENT_PROJECT_ID   as engagementProjectId,
               a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
               a.FILING_STATUS           as filingStatus,
               ''                        as applyTitle,
               a.APPLICAT_DUTY           as applicatDuty,
               a.SEX                     as sex,
               a.APPLY_STATUS            as applyStatus,
               ''                        as instId,
               a.CREATE_TIME             as createTime,
               a.CREATE_BY               as createBy
        from UOMP_TEMP_ADMISSION a
        where a.DEL_FLAG = '0'
          and a.BASE_ID is null
        <if test="personName != null and personName != ''">
            and PERSON_NAME = #{personName}
        </if>
        <if test="personCard != null and personCard != ''">
            and PERSON_CARD = #{personCard}
        </if>
        ) as unionTemp
        where ((unionTemp.applyStatus != '0') or (unionTemp.applyStatus = '0' and unionTemp.createBy = #{createBy}))
        order by createTime desc
    </select>

    <select id="selectPersonCardAndPersonNameById" resultType="cn.gwssi.ecloud.staffpool.dto.TempApplyDTO">
        select PERSON_CARD as personCard, PERSON_NAME as personName
        from UOMP_TEMP_ADMISSION
        where ID = #{id}
    </select>

    <select id="selectAllBySelectiveExport" resultType="cn.gwssi.ecloud.staffpool.dto.TempApplyDTO">
        select *
        from (select a.ID                 as id,
                     a.PERSON_NAME        as personName,
                     a.PERSON_CARD        as personCard,
                     a.TEL                as tel,
                     a.WORKING_COMPANY    as workingCompany,
                     a.WORKING_COMPANY_ID as workingCompanyId,
                     a.REAL_VISIT_TIME    as realVisitTime,
                     a.DEST_CLERK_ID      as destClerkId,
                     a.DEST_CLERK_NAME    as destClerkName,
                     a.EXIT_TIME          as exitTime,
                     a.APPLY_CODE         as applyCode,
                     a.JOB_CONTENT        as jobContent,
                     a.ACCEPT_NAME        as acceptName,
                     a.APPLICAT_DUTY      as applicatDuty,
                     a.APPLY_STATUS       as applyStatus,
                     a.CREATE_TIME        as createTime,
                     a.CREATE_BY          as createBy,
                     a.CREATE_ORG_ID      as createOrgId
              from UOMP_TEMP_ADMISSION a
                       left join uomp_temp_admission_base b on b.ID = a.BASE_ID
                       inner join bpm_instance bi on b.INST_ID = bi.id_ and bi.status_ != 'discard'
              where a.DEL_FLAG = '0'
              union all
              select a.ID                 as id,
                     a.PERSON_NAME        as personName,
                     a.PERSON_CARD        as personCard,
                     a.TEL                as tel,
                     a.WORKING_COMPANY    as workingCompany,
                     a.WORKING_COMPANY_ID as workingCompanyId,
                     a.REAL_VISIT_TIME    as realVisitTime,
                     a.DEST_CLERK_ID      as destClerkId,
                     a.DEST_CLERK_NAME    as destClerkName,
                     a.EXIT_TIME          as exitTime,
                     a.APPLY_CODE         as applyCode,
                     a.JOB_CONTENT        as jobContent,
                     a.ACCEPT_NAME        as acceptName,
                     a.APPLICAT_DUTY      as applicatDuty,
                     a.APPLY_STATUS       as applyStatus,
                     a.CREATE_TIME        as createTime,
                     a.CREATE_BY          as createBy,
                     a.CREATE_ORG_ID      as createOrgId
              from UOMP_TEMP_ADMISSION a
              where a.DEL_FLAG = '0'
                and a.BASE_ID is null) as unionTemp
        <where>
            <if test="personName != null and personName != ''">
                and personName like concat('%', #{personName}, '%')
            </if>
            <if test="acceptName != null and acceptName != ''">
                and acceptName like concat('%', #{acceptName}, '%')
            </if>
            <if test="workingCompany != null and workingCompany != ''">
                and workingCompanyId like concat('%', #{workingCompany}, '%')
            </if>
            <if test="realVisitTimeBegin != null and realVisitTimeBegin != ''">
                and date_format(realVisitTime, '%Y-%m-%d %H:%i') >= #{realVisitTimeBegin}
            </if>
            <if test="realVisitTimeEnd != null and realVisitTimeEnd != ''">
                and date_format(realVisitTime, '%Y-%m-%d %H:%i') &lt;= #{realVisitTimeEnd}
            </if>
            <if test="ifSupplier == 1">
                and createOrgId = #{orgId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and createBy = #{createdBy}
            </if>
        </where>
        order by createTime desc
    </select>

    <select id="selectIdByAll" resultType="java.lang.String">
        select ID
        from UOMP_TEMP_ADMISSION
        where PERSON_NAME = #{personName}
          and PERSON_CARD = #{personCard}
          and TEL = #{tel}
          and WORKING_COMPANY = #{workingCompany}
          and date_format(REAL_VISIT_TIME, '%Y-%m-%d %H:%i') = #{planVisitTime}
          and date_format(REAL_VISIT_TIME, '%Y-%m-%d %H:%i') = #{realVisitTime}
          and ACCEPT_NAME = #{acceptName}
          and EXIT_TIME = #{exitTime}
          and JOB_CONTENT = #{jobContent}
          and DEL_FLAG = '0'
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-06-06-->
    <insert id="insertSelective">
        INSERT INTO uomp_temp_admission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="personName != null">
                PERSON_NAME,
            </if>
            <if test="personCard != null">
                PERSON_CARD,
            </if>
            <if test="tel != null">
                TEL,
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY,
            </if>
            <if test="planVisitTime != null">
                PLAN_VISIT_TIME,
            </if>
            <if test="realVisitTime != null">
                REAL_VISIT_TIME,
            </if>
            <if test="destClerkId != null">
                DEST_CLERK_ID,
            </if>
            <if test="destClerkName != null">
                DEST_CLERK_NAME,
            </if>
            <if test="exitTime != null">
                EXIT_TIME,
            </if>
            <if test="jobContent != null">
                JOB_CONTENT,
            </if>
            <if test="managerComment != null">
                MANAGER_COMMENT,
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS,
            </if>
            <if test="instId != null">
                INST_ID,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
            <if test="applyCode != null">
                APPLY_CODE,
            </if>
            <if test="applyTitle != null">
                APPLY_TITLE,
            </if>
            <if test="blacklist != null">
                BLACKLIST,
            </if>
            <if test="blacklistReason != null">
                BLACKLIST_REASON,
            </if>
            <if test="baseId != null">
                BASE_ID,
            </if>
            <if test="acceptName != null">
                ACCEPT_NAME,
            </if>
            <if test="sex != null">
                SEX,
            </if>
            <if test="applicatDuty != null">
                APPLICAT_DUTY,
            </if>
            <if test="engagementProject != null">
                ENGAGEMENT_PROJECT,
            </if>
            <if test="engagementProjectId != null">
                ENGAGEMENT_PROJECT_ID,
            </if>
            <if test="engagementProjectJson != null">
                ENGAGEMENT_PROJECT_JSON,
            </if>
            <if test="filingStatus != null">
                FILING_STATUS,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="planVisitTime != null">
                #{planVisitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="realVisitTime != null">
                #{realVisitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="destClerkId != null">
                #{destClerkId,jdbcType=VARCHAR},
            </if>
            <if test="destClerkName != null">
                #{destClerkName,jdbcType=VARCHAR},
            </if>
            <if test="exitTime != null">
                #{exitTime,jdbcType=TIMESTAMP},
            </if>
            <if test="jobContent != null">
                #{jobContent,jdbcType=VARCHAR},
            </if>
            <if test="managerComment != null">
                #{managerComment,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="applyCode != null">
                #{applyCode,jdbcType=VARCHAR},
            </if>
            <if test="applyTitle != null">
                #{applyTitle,jdbcType=VARCHAR},
            </if>
            <if test="blacklist != null">
                #{blacklist,jdbcType=VARCHAR},
            </if>
            <if test="blacklistReason != null">
                #{blacklistReason,jdbcType=VARCHAR},
            </if>
            <if test="baseId != null">
                #{baseId,jdbcType=VARCHAR},
            </if>
            <if test="acceptName != null">
                #{acceptName,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="applicatDuty != null">
                #{applicatDuty,jdbcType=VARCHAR},
            </if>
            <if test="engagementProject != null">
                #{engagementProject,jdbcType=TIMESTAMP},
            </if>
            <if test="engagementProjectId != null">
                #{engagementProjectId,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectJson != null">
                #{engagementProjectJson,jdbcType=VARCHAR},
            </if>
            <if test="filingStatus != null">
                #{filingStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select
            id="countByRealVisitTimeGroupByRealVisitTime" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO">
        select date_format(REAL_VISIT_TIME, '%Y-%m') as name,
               COUNT(1)                              as num
        from uomp_temp_admission
        where DEL_FLAG = '0'
          and REAL_VISIT_TIME is not null
        <if test="realVisitTimeBegin != null and realVisitTimeBegin != ''">
            and date_format(REAL_VISIT_TIME, '%Y-%m') >= #{realVisitTimeBegin}
        </if>
        <if test="realVisitTimeEnd != null and realVisitTimeEnd != ''">
            and date_format(REAL_VISIT_TIME, '%Y-%m') &lt;= #{realVisitTimeEnd}
        </if>
        group by date_format(REAL_VISIT_TIME, '%Y-%m')
        order by name
    </select>

    <select id="countByrealVisitTime" resultType="java.lang.Integer">
        select
            count(1)
        from
            (
                select
        PERSON_CARD
                from
                    uomp_temp_admission
                where
                    REAL_VISIT_TIME >= #{realVisitTimeStart}
                  and REAL_VISIT_TIME &lt;= #{realVisitTimeEnd}
        and APPLY_STATUS = '2'
        and DEL_FLAG = '0'
        group by
        PERSON_CARD ) as b

    </select>
</mapper>
