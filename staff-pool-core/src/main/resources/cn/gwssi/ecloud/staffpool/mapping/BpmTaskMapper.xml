<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.BpmTaskMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.BpmTask">
    <!--@mbg.generated-->
    <!--@Table bpm_task-->
    <id column="id_" jdbcType="VARCHAR" property="id" />
    <result column="name_" jdbcType="VARCHAR" property="name" />
    <result column="subject_" jdbcType="VARCHAR" property="subject" />
    <result column="inst_id_" jdbcType="VARCHAR" property="instId" />
    <result column="task_id_" jdbcType="VARCHAR" property="taskId" />
    <result column="act_inst_id_" jdbcType="VARCHAR" property="actInstId" />
    <result column="act_execution_id_" jdbcType="VARCHAR" property="actExecutionId" />
    <result column="node_id_" jdbcType="VARCHAR" property="nodeId" />
    <result column="def_id_" jdbcType="VARCHAR" property="defId" />
    <result column="assignee_id_" jdbcType="VARCHAR" property="assigneeId" />
    <result column="assignee_names_" jdbcType="VARCHAR" property="assigneeNames" />
    <result column="status_" jdbcType="VARCHAR" property="status" />
    <result column="priority_" jdbcType="INTEGER" property="priority" />
    <result column="due_time_" jdbcType="TIMESTAMP" property="dueTime" />
    <result column="task_type_" jdbcType="VARCHAR" property="taskType" />
    <result column="parent_id_" jdbcType="VARCHAR" property="parentId" />
    <result column="type_id_" jdbcType="VARCHAR" property="typeId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by_" jdbcType="VARCHAR" property="createBy" />
    <result column="support_mobile_" jdbcType="INTEGER" property="supportMobile" />
    <result column="back_node_" jdbcType="VARCHAR" property="backNode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_, name_, subject_, inst_id_, task_id_, act_inst_id_, act_execution_id_, node_id_,
    def_id_, assignee_id_, assignee_names_, status_, priority_, due_time_, task_type_,
    parent_id_, type_id_, create_time_, create_by_, support_mobile_, back_node_
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from bpm_task
    where id_ = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from bpm_task
    where id_ = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.gwssi.ecloud.staffpool.core.entity.BpmTask">
    <!--@mbg.generated-->
    insert into bpm_task (id_, name_, subject_,
      inst_id_, task_id_, act_inst_id_,
      act_execution_id_, node_id_, def_id_,
      assignee_id_, assignee_names_, status_,
      priority_, due_time_, task_type_,
      parent_id_, type_id_, create_time_,
      create_by_, support_mobile_, back_node_
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{subject,jdbcType=VARCHAR},
      #{instId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{actInstId,jdbcType=VARCHAR},
      #{actExecutionId,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, #{defId,jdbcType=VARCHAR},
      #{assigneeId,jdbcType=VARCHAR}, #{assigneeNames,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
      #{priority,jdbcType=INTEGER}, #{dueTime,jdbcType=TIMESTAMP}, #{taskType,jdbcType=VARCHAR},
      #{parentId,jdbcType=VARCHAR}, #{typeId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR}, #{supportMobile,jdbcType=INTEGER}, #{backNode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.BpmTask">
    <!--@mbg.generated-->
    insert into bpm_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id_,
      </if>
      <if test="name != null">
        name_,
      </if>
      <if test="subject != null">
        subject_,
      </if>
      <if test="instId != null">
        inst_id_,
      </if>
      <if test="taskId != null">
        task_id_,
      </if>
      <if test="actInstId != null">
        act_inst_id_,
      </if>
      <if test="actExecutionId != null">
        act_execution_id_,
      </if>
      <if test="nodeId != null">
        node_id_,
      </if>
      <if test="defId != null">
        def_id_,
      </if>
      <if test="assigneeId != null">
        assignee_id_,
      </if>
      <if test="assigneeNames != null">
        assignee_names_,
      </if>
      <if test="status != null">
        status_,
      </if>
      <if test="priority != null">
        priority_,
      </if>
      <if test="dueTime != null">
        due_time_,
      </if>
      <if test="taskType != null">
        task_type_,
      </if>
      <if test="parentId != null">
        parent_id_,
      </if>
      <if test="typeId != null">
        type_id_,
      </if>
      <if test="createTime != null">
        create_time_,
      </if>
      <if test="createBy != null">
        create_by_,
      </if>
      <if test="supportMobile != null">
        support_mobile_,
      </if>
      <if test="backNode != null">
        back_node_,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="actInstId != null">
        #{actInstId,jdbcType=VARCHAR},
      </if>
      <if test="actExecutionId != null">
        #{actExecutionId,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="defId != null">
        #{defId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeId != null">
        #{assigneeId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeNames != null">
        #{assigneeNames,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="dueTime != null">
        #{dueTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="supportMobile != null">
        #{supportMobile,jdbcType=INTEGER},
      </if>
      <if test="backNode != null">
        #{backNode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.BpmTask">
    <!--@mbg.generated-->
    update bpm_task
    <set>
      <if test="name != null">
        name_ = #{name,jdbcType=VARCHAR},
      </if>
      <if test="subject != null">
        subject_ = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        inst_id_ = #{instId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id_ = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="actInstId != null">
        act_inst_id_ = #{actInstId,jdbcType=VARCHAR},
      </if>
      <if test="actExecutionId != null">
        act_execution_id_ = #{actExecutionId,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        node_id_ = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="defId != null">
        def_id_ = #{defId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeId != null">
        assignee_id_ = #{assigneeId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeNames != null">
        assignee_names_ = #{assigneeNames,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status_ = #{status,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority_ = #{priority,jdbcType=INTEGER},
      </if>
      <if test="dueTime != null">
        due_time_ = #{dueTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskType != null">
        task_type_ = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id_ = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="typeId != null">
        type_id_ = #{typeId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time_ = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by_ = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="supportMobile != null">
        support_mobile_ = #{supportMobile,jdbcType=INTEGER},
      </if>
      <if test="backNode != null">
        back_node_ = #{backNode,jdbcType=VARCHAR},
      </if>
    </set>
    where id_ = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.BpmTask">
    <!--@mbg.generated-->
    update bpm_task
    set name_ = #{name,jdbcType=VARCHAR},
      subject_ = #{subject,jdbcType=VARCHAR},
      inst_id_ = #{instId,jdbcType=VARCHAR},
      task_id_ = #{taskId,jdbcType=VARCHAR},
      act_inst_id_ = #{actInstId,jdbcType=VARCHAR},
      act_execution_id_ = #{actExecutionId,jdbcType=VARCHAR},
      node_id_ = #{nodeId,jdbcType=VARCHAR},
      def_id_ = #{defId,jdbcType=VARCHAR},
      assignee_id_ = #{assigneeId,jdbcType=VARCHAR},
      assignee_names_ = #{assigneeNames,jdbcType=VARCHAR},
      status_ = #{status,jdbcType=VARCHAR},
      priority_ = #{priority,jdbcType=INTEGER},
      due_time_ = #{dueTime,jdbcType=TIMESTAMP},
      task_type_ = #{taskType,jdbcType=VARCHAR},
      parent_id_ = #{parentId,jdbcType=VARCHAR},
      type_id_ = #{typeId,jdbcType=VARCHAR},
      create_time_ = #{createTime,jdbcType=TIMESTAMP},
      create_by_ = #{createBy,jdbcType=VARCHAR},
      support_mobile_ = #{supportMobile,jdbcType=INTEGER},
      back_node_ = #{backNode,jdbcType=VARCHAR}
    where id_ = #{id,jdbcType=VARCHAR}
  </update>

  <select id="countBySelective" resultType="java.lang.Integer">
    SELECT
    count(task.id_)
    FROM bpm_task task
    LEFT JOIN bpm_task_identitylink linkd on task.id_ = linkd.task_id_
    LEFT JOIN bpm_instance instance on task.inst_id_ = instance.id_
    WHERE task.node_id_ not in('DOC_ONE')
    and ((task.assignee_id_ = '0' and linkd.permission_code_ in
    <foreach collection="userRights" index="index" item="permissionCode" open="(" separator="," close=")">
      #{permissionCode}
    </foreach> )
    or (task.assignee_id_ = #{userId} AND (task.STATUS_ != 'LOCK' OR (task.STATUS_ = 'LOCK' and (linkd.IDENTITY_ = #{userId} or linkd.TYPE_ != 'user') ))))
    and task.assignee_id_ is not null
    AND instance.is_test_data_ = 0
    and linkd.TYPE_ = 'user'
  </select>
  <resultMap id="BaseTodoMap" type="cn.gwssi.ecloud.staffpool.dto.BpmTaskDto">
    <!--@mbg.generated-->
    <!--@Table bpm_task-->
    <id column="id_" jdbcType="VARCHAR" property="id" />
    <result column="name_" jdbcType="VARCHAR" property="name" />
    <result column="subject_" jdbcType="VARCHAR" property="subject" />
    <result column="inst_id_" jdbcType="VARCHAR" property="instId" />
    <result column="task_id_" jdbcType="VARCHAR" property="taskId" />
    <result column="act_inst_id_" jdbcType="VARCHAR" property="actInstId" />
    <result column="act_execution_id_" jdbcType="VARCHAR" property="actExecutionId" />
    <result column="node_id_" jdbcType="VARCHAR" property="nodeId" />
    <result column="def_id_" jdbcType="VARCHAR" property="defId" />
    <result column="assignee_id_" jdbcType="VARCHAR" property="assigneeId" />
    <result column="assignee_names_" jdbcType="VARCHAR" property="assigneeNames" />
    <result column="status_" jdbcType="VARCHAR" property="status" />
    <result column="priority_" jdbcType="INTEGER" property="priority" />
    <result column="due_time_" jdbcType="TIMESTAMP" property="dueTime" />
    <result column="task_type_" jdbcType="VARCHAR" property="taskType" />
    <result column="parent_id_" jdbcType="VARCHAR" property="parentId" />
    <result column="type_id_" jdbcType="VARCHAR" property="typeId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by_" jdbcType="VARCHAR" property="createBy" />
    <result column="support_mobile_" jdbcType="INTEGER" property="supportMobile" />
    <result column="back_node_" jdbcType="VARCHAR" property="backNode" />
    <result column="creator_" jdbcType="VARCHAR" property="creator" />
    <result column="report_id" jdbcType="VARCHAR" property="reportId" />
    <result column="inst_status_" jdbcType="VARCHAR" property="instStatus" />
    <result column="inst_create_time_" jdbcType="VARCHAR" property="instCreateTime" />
    <result column="inst_name_" jdbcType="VARCHAR" property="instName" />
    <result column="check_status_" jdbcType="VARCHAR" property="checkStatus" />
    <result column="check_time_" jdbcType="VARCHAR" property="checkTime" />
    <result column="node_type_key_" jdbcType="VARCHAR" property="nodeTypeKey" />
    <result column="node_type_name_" jdbcType="VARCHAR" property="nodeTypeName" />
    <result column="link_id_" jdbcType="VARCHAR" property="linkId" />
    <result column="link_task_type_" jdbcType="VARCHAR" property="linkTaskType" />

  </resultMap>
  <select id="selectTodoList" resultMap="BaseTodoMap">
    SELECT
    task.id_, task.name_, task.subject_, task.inst_id_, task.id_ task_id_,
    task.act_inst_id_, task.act_execution_id_, task.node_id_, task.def_id_,
    task.assignee_id_, task.status_,
    task.priority_, task.due_time_, task.task_type_, task.parent_id_, task.type_id_,
    task.create_time_, task.create_by_, task.support_mobile_, task.back_node_,
    opinion.creator_,
    instance.STATUS_ inst_status_,
    instance.create_time_ inst_create_time_,
    instance.def_name_ inst_name_,
    linkd.check_status_,linkd.check_time_,
    node.KEY_ node_type_key_,
    node.NAME_ node_type_name_,
    linkd.id_ link_id_,linkd.task_type_ link_task_type_
    FROM bpm_task task
    LEFT JOIN bpm_task_identitylink linkd on task.id_ = linkd.task_id_
    LEFT JOIN BPM_TASK_OPINION opinion on opinion.task_id_ =  task.id_
    LEFT JOIN bpm_instance instance on task.inst_id_ = instance.id_
    LEFT JOIN SYS_TREE_NODE node on task.TYPE_ID_ = node.ID_
    <where>
      task.node_id_ not in('DOC_ONE')
      and ((task.assignee_id_ = '0' and linkd.permission_code_ in
    <foreach collection="userRights" index="index" item="permissionCode" open="(" separator="," close=")">
        #{permissionCode}
    </foreach> )
      or (task.assignee_id_ = #{userId} AND (task.STATUS_ != 'LOCK' OR (task.STATUS_ = 'LOCK' and (linkd.IDENTITY_ = #{userId} or linkd.TYPE_ != 'user') ))))
      and task.assignee_id_ is not null
      AND instance.is_test_data_ = 0
      <if test="appTodoType == '1'">
        and instance.top_def_key_ in ('UOMP_FLOW_EVENT','UOMP_FLOW_ISSUES','UOMP_FLOW_ALTER','UOMP_FLOW_EVENT_PATENT','UOMP_FLOW_ISSUES_PATENT','UOMP_FLOW_ALTER_PATENT')
      </if>
      <if test="intimeStart!=null and intimeStart.trim()!=''">
        and task.CREATE_TIME_ &gt;= #{intimeStart}
      </if>
      <if test="intimeEnd!=null and intimeEnd.trim()!=''">
        and task.CREATE_TIME_ &lt;= #{intimeEnd}
      </if>
      <if test="subject!=null and subject.trim()!=''">
        and instr(task.subject_, trim(#{subject}))>0
      </if>
    <if test="createStartTime != null and createStartTime.trim()!=''">
      and instance.create_time_ &gt;=#{createStartTime}
    </if>
      <if test="createEndTime != null and createEndTime.trim()!=''">
        and instance.create_time_ &lt;=#{createEndTime}
      </if>
      <if test="defIds!=null and defIds.length > 0">
        and instance.def_id_ in
        <foreach collection="defIds" index="index" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
    <if test="sort !=null and orderBy.equals('instCreateTime')">
      order by instance.create_time_ ${sort}
    </if>
    <if test="sort !=null and orderBy.equals('createTime')">
      order by task.create_time_ ${sort}
    </if>
    limit #{pageSize} offset #{pageNo}
  </select>
  <select id="selectTodoListTotal" resultType="Integer">
    SELECT
    count(*)
    FROM bpm_task task
    LEFT JOIN bpm_task_identitylink linkd on task.id_ = linkd.task_id_
    LEFT JOIN BPM_TASK_OPINION opinion on opinion.task_id_ =  task.id_
    LEFT JOIN bpm_instance instance on task.inst_id_ = instance.id_
    LEFT JOIN SYS_TREE_NODE node on task.TYPE_ID_ = node.ID_
    <where>
      task.node_id_ not in('DOC_ONE')
      and ((task.assignee_id_ = '0' and linkd.permission_code_ in
      <foreach collection="userRights" index="index" item="permissionCode" open="(" separator="," close=")">
        #{permissionCode}
      </foreach> )
      or (task.assignee_id_ = #{userId} AND (task.STATUS_ != 'LOCK' OR (task.STATUS_ = 'LOCK' and (linkd.IDENTITY_ = #{userId} or linkd.TYPE_ != 'user') ))))
      and task.assignee_id_ is not null
      AND instance.is_test_data_ = 0
      <if test="appTodoType == '1'">
        and instance.top_def_key_ in ('UOMP_FLOW_EVENT','UOMP_FLOW_ISSUES','UOMP_FLOW_ALTER','UOMP_FLOW_EVENT_PATENT','UOMP_FLOW_ISSUES_PATENT','UOMP_FLOW_ALTER_PATENT')
      </if>
      <if test="intimeStart!=null and intimeStart.trim()!=''">
        and task.CREATE_TIME_ &gt;= str_to_date('${intimeStart} 00:00:00','yyyy-MM-dd hh24:mi:ss')
      </if>
      <if test="intimeEnd!=null and intimeEnd.trim()!=''">
        and task.CREATE_TIME_ &lt;= str_to_date('${intimeEnd} 23:59:59','yyyy-MM-dd hh24:mi:ss')
      </if>
      <if test="subject!=null and subject.trim()!=''">
        and instr(task.subject_, trim(#{subject}))>0
      </if>
      <if test="createStartTime != null and createStartTime.trim()!=''">
        and instance.create_time_ &gt;=#{createStartTime}
      </if>
      <if test="createEndTime != null and createEndTime.trim()!=''">
        and instance.create_time_ &lt;=#{createEndTime}
      </if>
      <if test="defIds!=null and defIds.length > 0">
        and instance.def_id_ in
        <foreach collection="defIds" index="index" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>
  <select id="selectInstList" resultType="cn.gwssi.ecloud.staffpool.dto.BpmTaskDto">
    select
    OO.TASK_ID_ taskId,
    OO.INST_ID_ id,
    OO.ID_ opinionId,
    OO.inst_id_ instId,
    OO.creator_ creator,
    node.NAME_ nodeTypeName,
    node.KEY_ nodeTypeKey,
    def.NAME_ defName,
    instO.SUBJECT_ subject,
    instO.def_name_ instName,
    instO.STATUS_ status,
    instO.CREATE_TIME_ as instCreateTime,
    OO.task_key_ nodeId,
    OO.CREATE_TIME_ createTime,
    OO.APPROVE_TIME_ approveTime,
    OO.STATUS_ approveStatus,
    OO.TASK_NAME_ nodeName,
    OO.APPROVER_NAME_ as approverName
    from BPM_TASK_OPINION OO
    inner join (select max(O.id_) id_
    from BPM_TASK_OPINION O
    inner join BPM_INSTANCE inst on O.INST_ID_ = inst.ID_
    left join BPM_USER_AGENCY_LOG agent on agent.task_id_ = o.task_id_
    left join BPM_USER_AGENCY_CONFIG config on config.id_ = agent.config_id_
    WHERE
    inst.is_test_data_ = 0
    and O.status_ not in ('skip','end','awaiting_check')
    and O.APPROVER_ is not null
    AND (O.APPROVER_ = #{userId} OR CONFIG.CONFIG_USER_ID_ = #{userId})
    group by inst.TOP_INST_ID_) groupId on groupId.id_ = OO.id_
    INNER JOIN BPM_INSTANCE instO ON OO.INST_ID_ = instO.ID_
    INNER JOIN BPM_DEFINITION def on instO.TOP_DEF_KEY_ = def.KEY_ and def.IS_MAIN_ = 'Y'
    LEFT JOIN SYS_TREE_NODE node on node.ID_ = def.TYPE_ID_
    where 1=1
    <if test="intimeStart!=null and intimeStart.trim()!=''">
      and OO.CREATE_TIME_ &gt;= #{intimeStart}
    </if>
    <if test="intimeEnd!=null and intimeEnd.trim()!=''">
      and OO.CREATE_TIME_ &lt;= #{intimeEnd}
    </if>
    <if test="subject!=null and subject.trim()!=''">
      and instr(instO.subject_, trim(#{subject}))>0
    </if>
    <if test="appTodoType == '1'">
      and instO.top_def_key_ in ('UOMP_FLOW_EVENT','UOMP_FLOW_ISSUES','UOMP_FLOW_ALTER','UOMP_FLOW_EVENT_PATENT','UOMP_FLOW_ISSUES_PATENT','UOMP_FLOW_ALTER_PATENT')
    </if>
    <if test="defIds!=null and defIds.length > 0">
      and instO.def_id_ in
      <foreach collection="defIds" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    <if test="sort != null and orderBy.equals('createTime')">
      ORDER BY OO.CREATE_TIME_ ${sort}
    </if>
    <if test="sort != null and orderBy.equals('approveTime')">
      ORDER BY OO.APPROVE_TIME_ ${sort}
    </if>
    limit #{pageSize} offset #{pageNo}
  </select>
  <select id="selectInstListTotal" resultType="Integer">
    select
    count(*)
    from BPM_TASK_OPINION OO
    inner join (select max(O.id_) id_
    from BPM_TASK_OPINION O
    inner join BPM_INSTANCE inst on O.INST_ID_ = inst.ID_
    left join BPM_USER_AGENCY_LOG agent on agent.task_id_ = o.task_id_
    left join BPM_USER_AGENCY_CONFIG config on config.id_ = agent.config_id_
    WHERE
    inst.is_test_data_ = 0
    and O.status_ not in ('skip','end','awaiting_check')
    and ifnull(O.opinion_,'/')!='开始节点跳过'
    and O.APPROVER_ is not null
    AND (O.APPROVER_ = #{userId} OR CONFIG.CONFIG_USER_ID_ = #{userId})
    group by inst.TOP_INST_ID_) groupId on groupId.id_ = OO.id_
    INNER JOIN BPM_INSTANCE instO ON OO.INST_ID_ = instO.ID_
    INNER JOIN BPM_DEFINITION def on instO.TOP_DEF_KEY_ = def.KEY_ and def.IS_MAIN_ = 'Y'
    LEFT JOIN SYS_TREE_NODE node on node.ID_ = def.TYPE_ID_
    where 1=1
    <if test="intimeStart!=null and intimeStart.trim()!=''">
      and OO.CREATE_TIME_ &gt;= str_to_date('${intimeStart} 00:00:00','yyyy-MM-dd hh24:mi:ss')
    </if>
    <if test="intimeEnd!=null and intimeEnd.trim()!=''">
      and OO.CREATE_TIME_ &lt;= str_to_date('${intimeEnd} 23:59:59','yyyy-MM-dd hh24:mi:ss')
    </if>
    <if test="subject!=null and subject.trim()!=''">
      and instr(instO.subject_, trim(#{subject}))>0
    </if>
    <if test="appTodoType == '1'">
      and instO.top_def_key_ in ('UOMP_FLOW_EVENT','UOMP_FLOW_ISSUES','UOMP_FLOW_ALTER','UOMP_FLOW_EVENT_PATENT','UOMP_FLOW_ISSUES_PATENT','UOMP_FLOW_ALTER_PATENT')
    </if>
    <if test="defIds!=null and defIds.length > 0">
      and instO.def_id_ in
      <foreach collection="defIds" index="index" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>
  <select id="selectApprove" resultType="cn.gwssi.ecloud.staffpool.dto.LabelDTO">
    select name_ label,id_ value from BPM_DEFINITION where is_main_='Y'
    and (key_ like'UOMP%' or key_ like'CMDB%') and name_ not like'%fb' order by name_;
  </select>
</mapper>
