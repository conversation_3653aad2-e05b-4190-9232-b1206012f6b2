<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTeamMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTeam">
    <!--@mbg.generated-->
    <!--@Table uomp_team-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="ORGID" jdbcType="VARCHAR" property="orgid" />
    <result column="TEAM_TYPE" jdbcType="VARCHAR" property="teamType" />
    <result column="DESCRIBE" jdbcType="VARCHAR" property="describe" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="TEAM_LEADER" jdbcType="VARCHAR" property="teamLeader" />
    <result column="TEAM_LEADER_ID" jdbcType="VARCHAR" property="teamLeaderId" />
    <result column="TEAM_RELATIONS" jdbcType="VARCHAR" property="teamRelations" />
    <result column="GROUP_LEADERS" jdbcType="VARCHAR" property="groupLeaders" />
    <result column="GROUP_LEADER_IDS" jdbcType="VARCHAR" property="groupLeaderIds" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, `NAME`, ORGID, TEAM_TYPE, `DESCRIBE`, `STATUS`, CREATE_BY, UPDATE_BY, CREATE_TIME, 
    UPDATE_TIME, DEL_FLAG, ORG_NAME, TEAM_LEADER, TEAM_LEADER_ID, TEAM_RELATIONS, GROUP_LEADERS, 
    GROUP_LEADER_IDS
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_team
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_team
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTeam">
    <!--@mbg.generated-->
    insert into uomp_team (ID, `NAME`, ORGID, 
      TEAM_TYPE, `DESCRIBE`, `STATUS`, 
      CREATE_BY, UPDATE_BY, CREATE_TIME, 
      UPDATE_TIME, DEL_FLAG, ORG_NAME, 
      TEAM_LEADER, TEAM_LEADER_ID, TEAM_RELATIONS, 
      GROUP_LEADERS, GROUP_LEADER_IDS)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{orgid,jdbcType=VARCHAR}, 
      #{teamType,jdbcType=VARCHAR}, #{describe,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{teamLeader,jdbcType=VARCHAR}, #{teamLeaderId,jdbcType=VARCHAR}, #{teamRelations,jdbcType=VARCHAR}, 
      #{groupLeaders,jdbcType=VARCHAR}, #{groupLeaderIds,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTeam">
    <!--@mbg.generated-->
    insert into uomp_team
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="orgid != null">
        ORGID,
      </if>
      <if test="teamType != null">
        TEAM_TYPE,
      </if>
      <if test="describe != null">
        `DESCRIBE`,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="teamLeader != null">
        TEAM_LEADER,
      </if>
      <if test="teamLeaderId != null">
        TEAM_LEADER_ID,
      </if>
      <if test="teamRelations != null">
        TEAM_RELATIONS,
      </if>
      <if test="groupLeaders != null">
        GROUP_LEADERS,
      </if>
      <if test="groupLeaderIds != null">
        GROUP_LEADER_IDS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="orgid != null">
        #{orgid,jdbcType=VARCHAR},
      </if>
      <if test="teamType != null">
        #{teamType,jdbcType=VARCHAR},
      </if>
      <if test="describe != null">
        #{describe,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="teamLeader != null">
        #{teamLeader,jdbcType=VARCHAR},
      </if>
      <if test="teamLeaderId != null">
        #{teamLeaderId,jdbcType=VARCHAR},
      </if>
      <if test="teamRelations != null">
        #{teamRelations,jdbcType=VARCHAR},
      </if>
      <if test="groupLeaders != null">
        #{groupLeaders,jdbcType=VARCHAR},
      </if>
      <if test="groupLeaderIds != null">
        #{groupLeaderIds,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTeam">
    <!--@mbg.generated-->
    update uomp_team
    set `NAME` = #{name,jdbcType=VARCHAR},
      ORGID = #{orgid,jdbcType=VARCHAR},
      TEAM_TYPE = #{teamType,jdbcType=VARCHAR},
      `DESCRIBE` = #{describe,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      TEAM_LEADER = #{teamLeader,jdbcType=VARCHAR},
      TEAM_LEADER_ID = #{teamLeaderId,jdbcType=VARCHAR},
      TEAM_RELATIONS = #{teamRelations,jdbcType=VARCHAR},
      GROUP_LEADERS = #{groupLeaders,jdbcType=VARCHAR},
      GROUP_LEADER_IDS = #{groupLeaderIds,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" resultMap="BaseResultMap">
    select
    *
    from UOMP_TEAM
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>

  <select id="selectAllByStatus" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
    select
      ID as id,NAME as name
    from UOMP_TEAM
    where DEL_FLAG = '0' AND STATUS = '1'
    order by CREATE_TIME desc
  </select>
</mapper>