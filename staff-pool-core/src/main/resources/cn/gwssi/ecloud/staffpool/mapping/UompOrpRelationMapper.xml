<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompOrpRelationMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompOrpRelation">
    <!--@mbg.generated-->
    <!--@Table uomp_orp_relation-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="RELATIION_TYPE" jdbcType="VARCHAR" property="relatiionType" />
    <result column="RELATION_ID" jdbcType="VARCHAR" property="relationId" />
    <result column="ORG_TYPE" jdbcType="VARCHAR" property="orgType" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, RELATIION_TYPE, RELATION_ID, ORG_TYPE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
    DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_orp_relation
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_orp_relation
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompOrpRelation">
    <!--@mbg.generated-->
    insert into uomp_orp_relation (ID, RELATIION_TYPE, RELATION_ID, 
      ORG_TYPE, CREATE_BY, CREATE_TIME, 
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{relatiionType,jdbcType=VARCHAR}, #{relationId,jdbcType=VARCHAR}, 
      #{orgType,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompOrpRelation">
    <!--@mbg.generated-->
    insert into uomp_orp_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="relatiionType != null">
        RELATIION_TYPE,
      </if>
      <if test="relationId != null">
        RELATION_ID,
      </if>
      <if test="orgType != null">
        ORG_TYPE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="relatiionType != null">
        #{relatiionType,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompOrpRelation">
    <!--@mbg.generated-->
    update uomp_orp_relation
    set RELATIION_TYPE = #{relatiionType,jdbcType=VARCHAR},
      RELATION_ID = #{relationId,jdbcType=VARCHAR},
      ORG_TYPE = #{orgType,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" resultMap="BaseResultMap">
    select * from uomp_orp_relation
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>