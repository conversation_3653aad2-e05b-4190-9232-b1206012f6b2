<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPermissionOutDetailsMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails">
    <!--@mbg.generated-->
    <!--@Table uomp_permission_out_details-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OUT_USER_ID" jdbcType="VARCHAR" property="outUserId" />
    <result column="OUT_APPLY_TYPE" jdbcType="VARCHAR" property="outApplyType" />
    <result column="EMPOWER_RESOURCE_IDS" jdbcType="VARCHAR" property="empowerResourceIds" />
    <result column="EMPOWER_RESOURCE_JSON" jdbcType="VARCHAR" property="empowerResourceJson" />
    <result column="PERMISSION" jdbcType="VARCHAR" property="permission" />
    <result column="EMPOWER_BEGIN_TIME" jdbcType="VARCHAR" property="empowerBeginTime" />
    <result column="EMPOWER_END_TIME" jdbcType="VARCHAR" property="empowerEndTime" />
    <result column="AUTHORIZATION_STATUS" jdbcType="VARCHAR" property="authorizationStatus" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, OUT_USER_ID, OUT_APPLY_TYPE, EMPOWER_RESOURCE_IDS, EMPOWER_RESOURCE_JSON, PERMISSION,
    EMPOWER_BEGIN_TIME, EMPOWER_END_TIME, AUTHORIZATION_STATUS, CREATE_BY, CREATE_TIME,
    UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_permission_out_details
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_permission_out_details
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails">
    <!--@mbg.generated-->
    insert into uomp_permission_out_details (ID, OUT_USER_ID, OUT_APPLY_TYPE,
      EMPOWER_RESOURCE_IDS, EMPOWER_RESOURCE_JSON,
      PERMISSION, EMPOWER_BEGIN_TIME, EMPOWER_END_TIME,
      AUTHORIZATION_STATUS, CREATE_BY, CREATE_TIME,
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{outUserId,jdbcType=VARCHAR}, #{outApplyType,jdbcType=VARCHAR},
      #{empowerResourceIds,jdbcType=VARCHAR}, #{empowerResourceJson,jdbcType=VARCHAR},
      #{permission,jdbcType=VARCHAR}, #{empowerBeginTime,jdbcType=VARCHAR}, #{empowerEndTime,jdbcType=VARCHAR},
      #{authorizationStatus,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails">
    <!--@mbg.generated-->
    insert into uomp_permission_out_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="outUserId != null">
        OUT_USER_ID,
      </if>
      <if test="outApplyType != null">
        OUT_APPLY_TYPE,
      </if>
      <if test="empowerResourceIds != null">
        EMPOWER_RESOURCE_IDS,
      </if>
      <if test="empowerResourceJson != null">
        EMPOWER_RESOURCE_JSON,
      </if>
      <if test="permission != null">
        PERMISSION,
      </if>
      <if test="empowerBeginTime != null">
        EMPOWER_BEGIN_TIME,
      </if>
      <if test="empowerEndTime != null">
        EMPOWER_END_TIME,
      </if>
      <if test="authorizationStatus != null">
        AUTHORIZATION_STATUS,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="outUserId != null">
        #{outUserId,jdbcType=VARCHAR},
      </if>
      <if test="outApplyType != null">
        #{outApplyType,jdbcType=VARCHAR},
      </if>
      <if test="empowerResourceIds != null">
        #{empowerResourceIds,jdbcType=VARCHAR},
      </if>
      <if test="empowerResourceJson != null">
        #{empowerResourceJson,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="empowerBeginTime != null">
        #{empowerBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="empowerEndTime != null">
        #{empowerEndTime,jdbcType=VARCHAR},
      </if>
      <if test="authorizationStatus != null">
        #{authorizationStatus,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails">
    <!--@mbg.generated-->
    update uomp_permission_out_details
    set OUT_USER_ID = #{outUserId,jdbcType=VARCHAR},
      OUT_APPLY_TYPE = #{outApplyType,jdbcType=VARCHAR},
      EMPOWER_RESOURCE_IDS = #{empowerResourceIds,jdbcType=VARCHAR},
      EMPOWER_RESOURCE_JSON = #{empowerResourceJson,jdbcType=VARCHAR},
      PERMISSION = #{permission,jdbcType=VARCHAR},
      EMPOWER_BEGIN_TIME = #{empowerBeginTime,jdbcType=VARCHAR},
      EMPOWER_END_TIME = #{empowerEndTime,jdbcType=VARCHAR},
      AUTHORIZATION_STATUS = #{authorizationStatus,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectOutInfoById" resultType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails">
      select
          d.ID,
          d.AUTHORIZATION_STATUS,
          d.EMPOWER_BEGIN_TIME,
          d.EMPOWER_END_TIME,
          d.OUT_APPLY_TYPE,
          d.EMPOWER_RESOURCE_IDS,
          d.PERMISSION,
          u.EMPOWER_USER_IDS as OUT_USER_ID
      from UOMP_PERMISSION_OUT_DETAILS d
      left join UOMP_PERMISSION_OUT_USER u on d.OUT_USER_ID = u.ID
      where d.ID = #{outDetailId}
  </select>
  <update id="updateAuthorizationStatusById">
    update UOMP_PERMISSION_OUT_DETAILS set AUTHORIZATION_STATUS = '1'
    <if test="resultList != null and resultList.size > 0">
      where ID in
      <foreach collection="resultList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </update>
</mapper>
