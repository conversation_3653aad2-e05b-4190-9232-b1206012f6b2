<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonConfigMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig">
        <!--@mbg.generated-->
        <!--@Table uomp_person_config-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONFIG_INFO" jdbcType="VARCHAR" property="configInfo"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="CONFIG_TYPE" jdbcType="VARCHAR" property="configType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CONFIG_INFO, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG, CONFIG_TYPE
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_person_config
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <select id="query" resultMap="BaseResultMap">
        select *
        from uomp_person_config
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from uomp_person_config
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig">
        <!--@mbg.generated-->
        insert into uomp_person_config (ID, CONFIG_INFO, CREATE_BY,
        CREATE_TIME, UPDATE_BY, UPDATE_TIME,
        DEL_FLAG, CONFIG_TYPE)
        values (#{id,jdbcType=VARCHAR}, #{configInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{delFlag,jdbcType=VARCHAR}, #{configType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig">
        <!--@mbg.generated-->
        insert into uomp_person_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="configInfo != null">
                CONFIG_INFO,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
            <if test="configType != null">
                CONFIG_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configInfo != null">
                #{configInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                #{configType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig">
        <!--@mbg.generated-->
        update uomp_person_config
        <set>
            <if test="configInfo != null">
                CONFIG_INFO = #{configInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                CONFIG_TYPE = #{configType,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig">
        <!--@mbg.generated-->
        update uomp_person_config
        set CONFIG_INFO = #{configInfo,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
        CONFIG_TYPE = #{configType,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getConfigInfo" resultMap="BaseResultMap">
        select
        ID,
        CONFIG_INFO
        FROM UOMP_PERSON_CONFIG
        where DEL_FLAG = '0'
        <if test="configType !=null and configType.trim() !=''">
            and CONFIG_TYPE = #{configType}
        </if>
        ORDER BY CREATE_TIME DESC
        LIMIT 0,1
    </select>
</mapper>