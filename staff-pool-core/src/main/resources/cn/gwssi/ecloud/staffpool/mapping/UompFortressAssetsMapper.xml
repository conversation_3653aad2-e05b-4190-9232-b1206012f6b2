<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompFortressAssetsMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssets">
    <!--@mbg.generated-->
    <!--@Table uomp_fortress_assets-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FORT_ID" jdbcType="VARCHAR" property="fortId" />
    <result column="FORT_ASSET_ID" jdbcType="VARCHAR" property="fortAssetId" />
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
    <result column="IP_ADDR" jdbcType="VARCHAR" property="ipAddr" />
    <result column="SYNC_STATUS" jdbcType="VARCHAR" property="syncStatus" />
    <result column="API_EXPLAIN" jdbcType="VARCHAR" property="apiExplain" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FORT_ID, FORT_ASSET_ID, RESOURCE_ID, IP_ADDR, SYNC_STATUS, API_EXPLAIN, CREATE_BY, 
    CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_fortress_assets
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_fortress_assets
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssets">
    <!--@mbg.generated-->
    insert into uomp_fortress_assets (ID, FORT_ID, FORT_ASSET_ID, 
      RESOURCE_ID, IP_ADDR, SYNC_STATUS, 
      API_EXPLAIN, CREATE_BY, CREATE_TIME, 
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{fortId,jdbcType=VARCHAR}, #{fortAssetId,jdbcType=VARCHAR}, 
      #{resourceId,jdbcType=VARCHAR}, #{ipAddr,jdbcType=VARCHAR}, #{syncStatus,jdbcType=VARCHAR}, 
      #{apiExplain,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssets">
    <!--@mbg.generated-->
    insert into uomp_fortress_assets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="fortId != null">
        FORT_ID,
      </if>
      <if test="fortAssetId != null">
        FORT_ASSET_ID,
      </if>
      <if test="resourceId != null">
        RESOURCE_ID,
      </if>
      <if test="ipAddr != null">
        IP_ADDR,
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS,
      </if>
      <if test="apiExplain != null">
        API_EXPLAIN,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fortId != null">
        #{fortId,jdbcType=VARCHAR},
      </if>
      <if test="fortAssetId != null">
        #{fortAssetId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="ipAddr != null">
        #{ipAddr,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=VARCHAR},
      </if>
      <if test="apiExplain != null">
        #{apiExplain,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssets">
    <!--@mbg.generated-->
    update uomp_fortress_assets
    set FORT_ID = #{fortId,jdbcType=VARCHAR},
      FORT_ASSET_ID = #{fortAssetId,jdbcType=VARCHAR},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      IP_ADDR = #{ipAddr,jdbcType=VARCHAR},
      SYNC_STATUS = #{syncStatus,jdbcType=VARCHAR},
      API_EXPLAIN = #{apiExplain,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectInfoByFortAssetId" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessResourceDTO">
      select resource_id as resourceId,ip_addr as ipAddr from uomp_fortress_assets where fort_asset_id = #{fortAssetId} limit 1
  </select>
</mapper>