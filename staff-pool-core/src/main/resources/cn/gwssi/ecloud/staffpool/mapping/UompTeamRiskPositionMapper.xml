<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTeamRiskPositionMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTeamRiskPosition">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="PERMISSION" jdbcType="VARCHAR" property="permission" />
        <result column="TECHNICAL_LEVEL" jdbcType="VARCHAR" property="technicalLevel" />
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTeamRiskPosition">
        insert into uomp_team_risk_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="permission != null">
                PERMISSION,
            </if>
            <if test="technicalLevel != null">
                TECHNICAL_LEVEL,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="permission != null">
                #{permission,jdbcType=VARCHAR},
            </if>
            <if test="technicalLevel != null">
                #{technicalLevel,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTeamRiskPosition">
        update uomp_team_risk_position
        <set>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="permission != null">
                PERMISSION = #{permission,jdbcType=VARCHAR},
            </if>
            <if test="technicalLevel != null">
                TECHNICAL_LEVEL = #{technicalLevel,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from uomp_team_risk_position
        <where>
            1 = 1
            <if test="whereSql != null">
              and ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        <if test="orderBySql == null">
            ORDER BY CREATE_TIME desc
        </if>
    </select>

    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT * FROM uomp_team_risk_position
        WHERE id = #{id}
    </select>

    <delete id="remove" parameterType="java.lang.String">
        delete from uomp_team_risk_position
        where id = #{id}
    </delete>
</mapper>
