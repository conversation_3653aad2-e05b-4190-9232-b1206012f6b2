<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPartyInfoMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPartyInfo">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONTRACT_MANAGEMENT_ID" jdbcType="VARCHAR" property="contractManagementId"/>
        <result column="PARTY_INFO" jdbcType="VARCHAR" property="partyInfo"/>
        <result column="PARTY_NAME" jdbcType="VARCHAR" property="partyName"/>
        <result column="PARTY_TEL" jdbcType="VARCHAR" property="partyTel"/>
        <result column="PARTY_MANAGER_NAME" jdbcType="VARCHAR" property="partyManagerName"/>
        <result column="PARTY_MANAGER_TEL" jdbcType="VARCHAR" property="partyManagerTel"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        *
        from uomp_party_info
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">

        delete from uomp_party_info
        where contract_management_id = #{contractManagementId,jdbcType=VARCHAR}
    </delete>

    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPartyInfo">

        insert into uomp_party_info (ID, contract_management_id,party_info,party_name,party_tel,party_manager_name,party_manager_tel,CREATE_BY,
        CREATE_TIME, UPDATE_BY, UPDATE_TIME)
        values (#{id,jdbcType=VARCHAR}, #{contractManagementId,jdbcType=VARCHAR},#{partyInfo,jdbcType=VARCHAR},#{partyName,jdbcType=VARCHAR},
        #{partyTel,jdbcType=VARCHAR},#{partyManagerName,jdbcType=VARCHAR},#{partyManagerTel,jdbcType=VARCHAR},#{createBy,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>


    <select id="query" parameterType="java.lang.String" resultMap="BaseResultMap">
        select *
        from uomp_party_info where contract_management_id = #{contractManagementId,jdbcType=VARCHAR}

    </select>


</mapper>
