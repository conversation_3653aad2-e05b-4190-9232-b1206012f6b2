<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoHistoryMapper">

  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoHistory">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
    <result column="PERSON_INFO" jdbcType="VARCHAR" property="personInfo" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, PERSON_ID, PERSON_INFO, INST_ID
  </sql>

  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from uomp_person_all_info_history
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <delete id="remove" parameterType="java.lang.String">
    delete from uomp_person_all_info_history
    where ID = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoHistory">
    insert into uomp_person_all_info_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="personInfo != null">
        PERSON_INFO,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="personInfo != null">
        #{personInfo,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoHistory">
    update uomp_person_all_info_history
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="personInfo != null">
        PERSON_INFO = #{personInfo,jdbcType=VARCHAR},
      </if>
      <if test="educationalInfo != null">
        INST_ID = #{instId,jdbcType=VARCHAR},
      </if>

    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getByInstId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from uomp_person_all_info_history
    where INST_ID = #{instId,jdbcType=VARCHAR}
  </select>

</mapper>