<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgPostMapper">
    <sql id="baseColumn">
        id_,name_,code_,is_civil_servant_,desc_,level_,create_time_,create_by_,update_time_,update_by_,type_,org_id_
    </sql>
    <select id="getPostByOrgType" resultType="cn.gwssi.ecloud.staffpool.dto.OrgPostTypeDTO">
        select
        tpost.ID_,
        tpost.NAME_,
        tpost.CODE_,
        tpost.DESC_
        from org_post tpost
        where tpost.TYPE_ not in ('4','5')
        and ID_ in(select RELATION_ID from UOMP_ORP_RELATION where DEL_FLAG = '0' and RELATIION_TYPE = 'post' and ORG_TYPE = #{orgType})
        <if test="name != null and name != ''">
            and instr(tpost.NAME_,#{name}) >0
        </if>
        order by tpost.id_ DESC
    </select>
    <select id="selectPositionsByUserId" resultType="java.lang.String" databaseId='mysql'>
        select
        group_concat(distinct tpost.name_) as existing_positions
        from
        org_post tpost
        inner join org_relation trelation on tpost.id_ = trelation.group_id_
        and trelation.type_='postUser'
        and trelation.status_ =1
        and trelation.user_id_ =#{userId}
        <!-- 查询所有岗位 where tpost.TYPE_ not in('4','5') -->
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectPositionsByUserId" resultType="java.lang.String">
        select
        wm_concat(distinct tpost.name_) as existing_positions
        from
        org_post tpost
        inner join org_relation trelation on tpost.id_ = trelation.group_id_
        and trelation.type_='postUser'
        and trelation.status_ =1
        and trelation.user_id_ =#{userId}
        <!-- 查询所有岗位 where tpost.TYPE_ not in('4','5') -->
        ORDER BY tpost.create_time_ desc
    </select>

    <select id="selectPositionsByAccount" resultType="java.lang.String" databaseId='mysql'>
        select
        group_concat(distinct tpost.name_) as existing_positions
        from
        org_post tpost
        inner join org_relation trelation on tpost.id_ = trelation.group_id_
        inner join org_user tuser ON tuser.id_ = trelation.user_id_
        and trelation.type_='postUser'
        and trelation.status_ =1
        and tuser.account_ =#{account}
        <!-- 查询所有岗位 where tpost.TYPE_ not in('4','5') -->
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectPositionsByAccount" resultType="java.lang.String">
        select
        wm_concat(distinct tpost.name_) as existing_positions
        from
        org_post tpost
        inner join org_relation trelation on tpost.id_ = trelation.group_id_
        inner join org_user tuser ON tuser.id_ = trelation.user_id_
        and trelation.type_='postUser'
        and trelation.status_ =1
        and tuser.account_ =#{account}
        <!-- 查询所有岗位 where tpost.TYPE_ not in('4','5') -->
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectOwerPostByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO">
        select distinct tt.* from (
            select
                opost.ID_ as id,
                opost.NAME_ as name,
                opost.CODE_ as code,
                opost.DESC_ as description
            from
            org_post opost
            inner join org_relation orelation on opost.id_ = orelation.group_id_
            <where>
                <if test="whereSql!=null">
                    ${whereSql}
                </if>
            </where>
            <if test="orderBySql!=null">
                ORDER BY ${orderBySql}
            </if>
        ) tt
    </select>
    <select id="selectOwerNoPostByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO">
        select
        tpost.ID_ as id,
        tpost.NAME_ as name,
        tpost.CODE_ as code,
        tpost.DESC_ as description
       from
        org_post tpost
       where tpost.id_ not in (
        select trelation.group_id_ from org_relation trelation where trelation.user_id_ = #{userId}
        and trelation.group_id_ is not null
       )
       and tpost.TYPE_ not in('4','5')
       and tpost.ID_ in(select RELATION_ID from UOMP_ORP_RELATION where DEL_FLAG = '0' and RELATIION_TYPE = 'post' and ORG_TYPE = #{orgType})
       ORDER BY tpost.create_time_ desc
    </select>
    <select id="query" resultType="cn.gwssi.ecloud.staffpool.core.entity.OrgPost">
        select
        id_ as id,
        name_ as name,
        code_ as code,
        is_civil_servant_ as isCivilServant,
        desc_ as description,
        level_ as level,
        create_time_ as createTime,
        create_by_ as createBy,
        update_time_ as updateTime,
        update_by_ as updateBy,
        type_ as type,
        org_id_ as orgId
        from org_post
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="selectNameByUserIdAndTypeIn" resultType="java.lang.String" databaseId='mysql'>
        select GROUP_CONCAT(distinct tpost.NAME_) as name
        from org_post tpost
                 inner join org_relation trelation
                            on tpost.id_ = trelation.group_id_ and trelation.type_ = 'postUser' and
                               trelation.status_ = 1 and trelation.user_id_ = #{userId}
        where tpost.TYPE_ in ('4', '5')
          and exists (SELECT 1 FROM UOMP_TEAM t WHERE t.ID = tpost.ID_ and t.STATUS = '1')
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectNameByUserIdAndTypeIn" resultType="java.lang.String">
        select wm_concat(distinct tpost.NAME_) as name
        from org_post tpost
        inner join org_relation trelation
        on tpost.id_ = trelation.group_id_ and trelation.type_ = 'postUser' and
        trelation.status_ = 1 and trelation.user_id_ = #{userId}
        where tpost.TYPE_ in ('4', '5')
        and exists (SELECT 1 FROM UOMP_TEAM t WHERE t.ID = tpost.ID_ and t.STATUS = '1')
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectNameByUserIdAndTypeNotIn" resultType="java.lang.String" databaseId='mysql'>
        select GROUP_CONCAT(distinct tpost.NAME_) as name
        from org_post tpost
                 inner join org_relation trelation
                            on tpost.id_ = trelation.group_id_ and trelation.type_ = 'postUser' and
                               trelation.status_ = 1 and trelation.user_id_ = #{userId}
<!--        where tpost.TYPE_ not in ('4', '5')  暂时取消限定逻辑 todo 20240617-->
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectNameByUserIdAndTypeNotIn" resultType="java.lang.String">
        select wm_concat(distinct tpost.NAME_) as name
        from org_post tpost
        inner join org_relation trelation
        on tpost.id_ = trelation.group_id_ and trelation.type_ = 'postUser' and
        trelation.status_ = 1 and trelation.user_id_ = #{userId}
        <!--        where tpost.TYPE_ not in ('4', '5')  暂时取消限定逻辑 todo 20240617-->
        ORDER BY tpost.create_time_ desc
    </select>
    <select id="selectAllByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.OrgPostDTO">
        select tpost.ID_         as id,
               tpost.NAME_       as name,
               trelation.ORG_ID_ as orgId
        from org_post tpost
                 inner join org_relation trelation on tpost.id_ = trelation.group_id_
            and trelation.type_ = 'postUser' and trelation.status_ = 1 and trelation.user_id_ = #{userId}
        ORDER BY tpost.create_time_ desc
    </select>

    <select id="countByPostKey" resultType="java.lang.Integer">
        select count(*) as num
        from org_post tpost
                 inner join org_relation trelation
                            on tpost.id_ = trelation.group_id_ and trelation.type_ = 'postUser' and
                               trelation.status_ = 1
        where tpost.TYPE_ not in ('4', '5')
          and tpost.CODE_ = #{postKey}
    </select>
    <select id="queryAllPost" resultType="cn.gwssi.ecloud.staffpool.core.entity.OrgPost">
        select * from org_post ORDER BY ID_ DESC
    </select>
</mapper>
