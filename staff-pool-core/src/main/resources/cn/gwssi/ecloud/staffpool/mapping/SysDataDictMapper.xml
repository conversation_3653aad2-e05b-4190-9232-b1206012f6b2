<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.SysDataDict">
        <!--@mbg.generated-->
        <!--@Table sys_data_dict-->
        <id column="id_" jdbcType="VARCHAR" property="id"/>
        <result column="parent_id_" jdbcType="VARCHAR" property="parentId"/>
        <result column="key_" jdbcType="VARCHAR" property="key"/>
        <result column="name_" jdbcType="VARCHAR" property="name"/>
        <result column="dict_key_" jdbcType="VARCHAR" property="dictKey"/>
        <result column="type_id_" jdbcType="VARCHAR" property="typeId"/>
        <result column="sn_" jdbcType="INTEGER" property="sn"/>
        <result column="dict_type_" jdbcType="VARCHAR" property="dictType"/>
        <result column="delete_flag_" jdbcType="VARCHAR" property="deleteFlag"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_by_" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_time_" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_by_" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id_,
        parent_id_,
        key_,
        name_,
        dict_key_,
        type_id_,
        sn_,
        dict_type_,
        delete_flag_,
        create_time_,
        create_by_,
        update_time_,
        update_by_
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_data_dict
        where id_ = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectSupplier" resultMap="BaseResultMap">
        select KEY_, NAME_ as name
        from SYS_DATA_DICT
        where DICT_KEY_ = 'G_ORG_TYPE'
          and Name_ = '供应商' limit 0,1
    </select>

    <select id="selectPatent" resultType="cn.gwssi.ecloud.staffpool.dto.FilePatentDto">
        select node.KEY_  as FILE_TYPE,
               node.NAME_ as FILE_TYPE_NAME,
               node.DESC_ as FILE_MEMO,
               node.SN_   as SN
        from SYS_TREE_NODE node
                 left join SYS_TREE tree on node.TREE_ID_ = tree.ID_
        where tree.KEY_ = 'PATENT_SUPPLIER_FILE_TYPE'
        order by node.SN_
    </select>
    <select id="selectNameByPostKey" resultType="java.lang.String">
        select NAME_ as name
        from SYS_DATA_DICT
        where DICT_KEY_ = 'UOMP_POST_TITLE'
        and KEY_ = #{postKey}
        and PARENT_ID_ is not null
    </select>
    <select id="selectSubListByUompEducation" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
        select KEY_ as id,NAME_ as name
        from SYS_DATA_DICT
        where DICT_KEY_ = #{dictKey} and PARENT_ID_ is not null
    </select>
    <select id="selectSubListByDictKey" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
        select KEY_ as id,NAME_ as name
        from SYS_DATA_DICT
        where DICT_KEY_ = #{dictKey} and PARENT_ID_ is not null
        order by id asc
    </select>

    <select id="getKeyByName" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO"  databaseId='mysql'>
        select group_concat(a.name_) name from (
        select name_ from sys_data_dict where dict_key_ = #{dictKey}
        and key_ in
        <foreach collection="keyList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and dict_type_ = 'node' order by sn_
        ) as a
    </select>

    <select id="getKeyByName" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
        select wm_concat(a.name_) name from (
        select name_ from sys_data_dict where dict_key_ = #{dictKey}
        and key_ in
        <foreach collection="keyList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and dict_type_ = 'node' order by sn_
        ) as a
    </select>

    <select id="selectKeyByDictkeyAndName" resultType="java.lang.String">
        select KEY_
        from SYS_DATA_DICT
        where DICT_KEY_ = #{dictKey}
          and NAME_ = #{name}
          and PARENT_ID_ is not null
        limit 1
    </select>

    <select id="selectKeyByDictkeyAndKey" resultType="java.lang.String">
        select KEY_
        from SYS_DATA_DICT
        where DICT_KEY_ = #{dictKey}
        and KEY_ = #{key}
        and PARENT_ID_ is not null
        limit 1
    </select>

    <select id="selectKeyByDictkeyAndNameAndParentId" resultType="java.lang.String">
        select KEY_
        from SYS_DATA_DICT
        where DICT_KEY_ = #{dictKey}
          and NAME_ = #{name}
          and PARENT_ID_ = #{id}
    </select>

    <select id="selectParentIdByDictKeyAndKey" resultType="java.lang.String">
        select PARENT_ID_
        from sys_data_dict
        where dict_type_ = 'node'
          and dict_key_ = #{dictKey}
          and key_ = #{key}
    </select>

    <select id="selectNameByDictKeyAndKey" resultType="java.lang.String">
        select name_
        from sys_data_dict
        where dict_type_ = 'node'
        and dict_key_ = #{dictKey}
        and key_ = #{key}
    </select>

    <select id="selectKeyAndParentIdById" resultType="java.util.Map">
        select KEY_ as keyName, PARENT_ID_ as parentId
        from sys_data_dict
        where ID_ = #{parentId} and dict_type_ = 'node'
    </select>

    <select id="selectAllByDictKeys" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_data_dict
        where dict_key_ = #{dictKey} and dict_type_ = 'node' and delete_flag_ = '0'
    </select>
</mapper>
