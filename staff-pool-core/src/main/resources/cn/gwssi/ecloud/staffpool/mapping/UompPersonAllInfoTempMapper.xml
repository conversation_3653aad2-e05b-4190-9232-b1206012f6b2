<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoTempMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp">
    <!--@mbg.generated-->
    <!--@Table uomp_person_all_info_temp-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="PERSON_INFO" jdbcType="VARCHAR" property="personInfo" />
    <result column="EDUCATIONAL_INFO" jdbcType="VARCHAR" property="educationalInfo" />
    <result column="JOB_INFO" jdbcType="VARCHAR" property="jobInfo" />
    <result column="TECH_INFO" jdbcType="VARCHAR" property="techInfo" />
    <result column="SOCIAL_INFO" jdbcType="VARCHAR" property="socialInfo" />
    <result column="NO_CRIME_INFO" jdbcType="VARCHAR" property="noCrimeInfo" />
    <result column="ABROAD_INFO" jdbcType="VARCHAR" property="abroadInfo" />
    <result column="ENRTY_EXIT_INFO" jdbcType="VARCHAR" property="enrtyExitInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, PERSON_INFO, EDUCATIONAL_INFO, JOB_INFO, TECH_INFO, SOCIAL_INFO, NO_CRIME_INFO,
    ABROAD_INFO, ENRTY_EXIT_INFO, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_all_info_temp
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_all_info_temp
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp">
    <!--@mbg.generated-->
    insert into uomp_person_all_info_temp (ID, PERSON_ID, PERSON_INFO,
      EDUCATIONAL_INFO, JOB_INFO, TECH_INFO,
      SOCIAL_INFO, NO_CRIME_INFO, ABROAD_INFO,
      ENRTY_EXIT_INFO, CREATE_BY, CREATE_TIME,
      UPDATE_BY, UPDATE_TIME, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{personInfo,jdbcType=VARCHAR},
      #{educationalInfo,jdbcType=VARCHAR}, #{jobInfo,jdbcType=VARCHAR}, #{techInfo,jdbcType=VARCHAR},
      #{socialInfo,jdbcType=VARCHAR}, #{noCrimeInfo,jdbcType=VARCHAR}, #{abroadInfo,jdbcType=VARCHAR},
      #{enrtyExitInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp">
    <!--@mbg.generated-->
    insert into uomp_person_all_info_temp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="personInfo != null">
        PERSON_INFO,
      </if>
      <if test="educationalInfo != null">
        EDUCATIONAL_INFO,
      </if>
      <if test="jobInfo != null">
        JOB_INFO,
      </if>
      <if test="techInfo != null">
        TECH_INFO,
      </if>
      <if test="socialInfo != null">
        SOCIAL_INFO,
      </if>
      <if test="noCrimeInfo != null">
        NO_CRIME_INFO,
      </if>
      <if test="abroadInfo != null">
        ABROAD_INFO,
      </if>
      <if test="enrtyExitInfo != null">
        ENRTY_EXIT_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="personInfo != null">
        #{personInfo,jdbcType=VARCHAR},
      </if>
      <if test="educationalInfo != null">
        #{educationalInfo,jdbcType=VARCHAR},
      </if>
      <if test="jobInfo != null">
        #{jobInfo,jdbcType=VARCHAR},
      </if>
      <if test="techInfo != null">
        #{techInfo,jdbcType=VARCHAR},
      </if>
      <if test="socialInfo != null">
        #{socialInfo,jdbcType=VARCHAR},
      </if>
      <if test="noCrimeInfo != null">
        #{noCrimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="abroadInfo != null">
        #{abroadInfo,jdbcType=VARCHAR},
      </if>
      <if test="enrtyExitInfo != null">
        #{enrtyExitInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp">
    <!--@mbg.generated-->
    update uomp_person_all_info_temp
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="personInfo != null">
        PERSON_INFO = #{personInfo,jdbcType=VARCHAR},
      </if>
      <if test="educationalInfo != null">
        EDUCATIONAL_INFO = #{educationalInfo,jdbcType=VARCHAR},
      </if>
      <if test="jobInfo != null">
        JOB_INFO = #{jobInfo,jdbcType=VARCHAR},
      </if>
      <if test="techInfo != null">
        TECH_INFO = #{techInfo,jdbcType=VARCHAR},
      </if>
      <if test="socialInfo != null">
        SOCIAL_INFO = #{socialInfo,jdbcType=VARCHAR},
      </if>
      <if test="noCrimeInfo != null">
        NO_CRIME_INFO = #{noCrimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="abroadInfo != null">
        ABROAD_INFO = #{abroadInfo,jdbcType=VARCHAR},
      </if>
      <if test="enrtyExitInfo != null">
        ENRTY_EXIT_INFO = #{enrtyExitInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp">
    <!--@mbg.generated-->
    update uomp_person_all_info_temp
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      PERSON_INFO = #{personInfo,jdbcType=VARCHAR},
      EDUCATIONAL_INFO = #{educationalInfo,jdbcType=VARCHAR},
      JOB_INFO = #{jobInfo,jdbcType=VARCHAR},
      TECH_INFO = #{techInfo,jdbcType=VARCHAR},
      SOCIAL_INFO = #{socialInfo,jdbcType=VARCHAR},
      NO_CRIME_INFO = #{noCrimeInfo,jdbcType=VARCHAR},
      ABROAD_INFO = #{abroadInfo,jdbcType=VARCHAR},
      ENRTY_EXIT_INFO = #{enrtyExitInfo,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getByPeronId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_all_info_temp
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
    order by CREATE_TIME limit 1
  </select>
</mapper>
