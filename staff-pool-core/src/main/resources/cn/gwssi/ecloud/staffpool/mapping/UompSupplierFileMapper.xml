<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompSupplierFileMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    <!--@mbg.generated-->
    <!--@Table uomp_supplier_file-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SUPPLIER_MANAGEMENT_ID" jdbcType="VARCHAR" property="supplierManagementId" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="UPLOADER_ID" jdbcType="VARCHAR" property="uploaderId" />
    <result column="UPLOADER_NAME" jdbcType="VARCHAR" property="uploaderName" />
    <result column="UPLOAD_TIME" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="FILE_TYPE" jdbcType="VARCHAR" property="fileType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SUPPLIER_MANAGEMENT_ID, FILE_NAME, FILE_ID, UPLOADER_ID, UPLOADER_NAME, UPLOAD_TIME, 
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG, 
    FILE_TYPE
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_supplier_file
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_supplier_file
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    <!--@mbg.generated-->
    insert into uomp_supplier_file (ID, SUPPLIER_MANAGEMENT_ID, FILE_NAME, 
      FILE_ID, UPLOADER_ID, UPLOADER_NAME, 
      UPLOAD_TIME, CREATE_BY, CREATE_TIME, 
      CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
      UPDATE_ORG_ID, DEL_FLAG, FILE_TYPE
      )
    values (#{id,jdbcType=VARCHAR}, #{supplierManagementId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileId,jdbcType=VARCHAR}, #{uploaderId,jdbcType=VARCHAR}, #{uploaderName,jdbcType=VARCHAR}, 
      #{uploadTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    <!--@mbg.generated-->
    insert into uomp_supplier_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="supplierManagementId != null">
        SUPPLIER_MANAGEMENT_ID,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="fileId != null">
        FILE_ID,
      </if>
      <if test="uploaderId != null">
        UPLOADER_ID,
      </if>
      <if test="uploaderName != null">
        UPLOADER_NAME,
      </if>
      <if test="uploadTime != null">
        UPLOAD_TIME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="supplierManagementId != null">
        #{supplierManagementId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderId != null">
        #{uploaderId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderName != null">
        #{uploaderName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    <!--@mbg.generated-->
    update uomp_supplier_file
    set SUPPLIER_MANAGEMENT_ID = #{supplierManagementId,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      FILE_ID = #{fileId,jdbcType=VARCHAR},
      UPLOADER_ID = #{uploaderId,jdbcType=VARCHAR},
      UPLOADER_NAME = #{uploaderName,jdbcType=VARCHAR},
      UPLOAD_TIME = #{uploadTime,jdbcType=TIMESTAMP},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      FILE_TYPE = #{fileType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateById" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    update uomp_supplier_file
    <trim prefix="SET" suffixOverrides=",">
      <if test="fileName != null">FILE_NAME = #{fileName,jdbcType=VARCHAR},</if>
      <if test="fileId != null">FILE_ID = #{fileId,jdbcType=VARCHAR},</if>
      <if test="delFlag != null">DEL_FLAG = #{delFlag,jdbcType=VARCHAR},</if>
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    </trim>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBySupplierManagementId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile">
    update uomp_supplier_file
    <trim prefix="SET" suffixOverrides=",">
      <if test="delFlag != null">DEL_FLAG = #{delFlag,jdbcType=VARCHAR},</if>
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    </trim>
    where SUPPLIER_MANAGEMENT_ID = #{supplierManagementId,jdbcType=VARCHAR}
  </update>
  <delete id="removeBySupplierManagementId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_supplier_file
    where SUPPLIER_MANAGEMENT_ID = #{supplierManagementId,jdbcType=VARCHAR}
  </delete>
  <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UOMP_SUPPLIER_FILE
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="getFileList" resultType="cn.gwssi.ecloud.staffpool.dto.UompSupplierFileDto">
    select
    ID,
    SUPPLIER_MANAGEMENT_ID,
    FILE_NAME,
    FILE_ID,
    UPLOAD_TIME,
    UPLOADER_NAME
    from UOMP_SUPPLIER_FILE
    where DEL_FLAG = '0' and SUPPLIER_MANAGEMENT_ID = #{supplierManagementId}
    <if test="fileName != null and fileName != ''">
      and FILE_NAME like concat('%',#{fileName},'%')
    </if>
  </select>

  <select id="getFileListPatent" resultType="cn.gwssi.ecloud.staffpool.dto.SupplierFile">
    select
    ID,
    SUPPLIER_MANAGEMENT_ID,
    FILE_NAME,
    FILE_ID,
    FILE_TYPE
    from UOMP_SUPPLIER_FILE
    where DEL_FLAG = '0' and SUPPLIER_MANAGEMENT_ID = #{supplierManagementId} and FILE_TYPE = #{fileType}
    order by CREATE_TIME desc
  </select>
</mapper>