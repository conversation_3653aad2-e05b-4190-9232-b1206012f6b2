<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgRelationMapper">
    <select id="countByUserIdAndRole" resultType="java.lang.Integer">
        SELECT count(0)
        FROM ORG_RELATION r,
             ORG_ROLE o
        WHERE r.USER_ID_ = #{userId}
          AND r.TYPE_ = 'userRole'
          AND r.GROUP_ID_ = o.ID_
          AND o.ALIAS_ = #{alias}
    </select>
    <select id="selectOwerListByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.OrgPostBaseDTO">
        select distinct *
        from (select tpost.ID_   as id,
                     tpost.NAME_ as name,
                     tpost.CODE_ as code
              from org_post tpost
                       inner join org_relation trelation on tpost.id_ = trelation.group_id_
                  and trelation.type_ = 'postUser'
                  and trelation.status_ = 1
                  and trelation.user_id_ = #{userId}) as t
    </select>
    <select id="selectGroupByUserId" resultType="java.lang.String" databaseId='mysql'>
        select group_concat(distinct b.NAME_) AS postName
        from org_relation a
                 inner join org_post b on a.group_id_ = b.id_ and a.TYPE_ = 'postUser' and b.TYPE_ in ('4', '5')
        where a.USER_ID_ = #{userId}
          and exists (SELECT 1 FROM UOMP_TEAM t WHERE t.ID = b.ID_ and t.STATUS = '1')
    </select>

    <select id="selectGroupByUserId" resultType="java.lang.String">
        select wm_concat(distinct b.NAME_) AS postName
        from org_relation a
        inner join org_post b on a.group_id_ = b.id_ and a.TYPE_ = 'postUser' and b.TYPE_ in ('4', '5')
        where a.USER_ID_ = #{userId}
        and exists (SELECT 1 FROM UOMP_TEAM t WHERE t.ID = b.ID_ and t.STATUS = '1')
    </select>
    <select id="selectGroupIdByUserId" resultType="java.lang.String">
        select r.GROUP_ID_
        from ORG_RELATION r
        where r.USER_ID_ = #{userId}
          and r.TYPE_ = 'groupUser'
          and IS_MASTER_ = '1'
    </select>

    <select id="selectNumByStatus" resultType="cn.gwssi.ecloud.staffpool.dto.ManagerDTO">
        select a.ID               as id,
               a.NAME             as name,
               count(r.ID_)       as num,
               a.GROUP_LEADER_IDS as groupLeaderIds
        from UOMP_TEAM a
                 left join ORG_RELATION r on a.ID = r.GROUP_ID_
        where a.STATUS = '1'
          and DEL_FLAG = '0'
        group by a.ID, a.NAME, a.GROUP_LEADER_IDS
    </select>

    <select id="selectUserInfoByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO"  databaseId='mysql'>
        SELECT
        id_ as id,
        fullname_ as name,
        'user' as type,
        org_id as orgId
        FROM
        USER_GROUP_ORG
        WHERE
        id_ in (SELECT SUBSTRING_INDEX(
                       SUBSTRING_INDEX(
                                group_concat(DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX(t.group_leader_ids, ',', numbers.n), ',', -1)),
                               ',',
                               numbers.n
                       ),
                       ',',
                       -1
               ) AS leader_id
        FROM ( SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4  UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8  UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
               UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23  UNION ALL SELECT 24 UNION ALL SELECT 25 UNION ALL SELECT 26 UNION ALL SELECT 27  UNION ALL SELECT 28 UNION ALL SELECT 29 UNION ALL SELECT 30 UNION ALL SELECT 31 UNION ALL SELECT 32 UNION ALL SELECT 33 UNION ALL SELECT 34 UNION ALL SELECT 35 UNION ALL SELECT 36 UNION ALL SELECT 37 UNION ALL SELECT 38 UNION ALL SELECT 39
               UNION ALL SELECT 40 UNION ALL SELECT 41 UNION ALL SELECT 42  UNION ALL SELECT 43 UNION ALL SELECT 44 UNION ALL SELECT 45 UNION ALL SELECT 46  UNION ALL SELECT 47 UNION ALL SELECT 48 UNION ALL SELECT 49 UNION ALL SELECT 50 UNION ALL SELECT 51 UNION ALL SELECT 52 UNION ALL SELECT 53 UNION ALL SELECT 54 UNION ALL SELECT 55 UNION ALL SELECT 56 UNION ALL SELECT 57 UNION ALL SELECT 58
               UNION ALL SELECT 59 UNION ALL SELECT 60 UNION ALL SELECT 61  UNION ALL SELECT 62 UNION ALL SELECT 63 UNION ALL SELECT 64 UNION ALL SELECT 65  UNION ALL SELECT 66 UNION ALL SELECT 67 UNION ALL SELECT 68 UNION ALL SELECT 69 UNION ALL SELECT 70 UNION ALL SELECT 71 UNION ALL SELECT 72 UNION ALL SELECT 73 UNION ALL SELECT 74 UNION ALL SELECT 75 UNION ALL SELECT 76 UNION ALL SELECT 77
               UNION ALL SELECT 78 UNION ALL SELECT 79 UNION ALL SELECT 80  UNION ALL SELECT 81 UNION ALL SELECT 82 UNION ALL SELECT 83 UNION ALL SELECT 84  UNION ALL SELECT 85 UNION ALL SELECT 86 UNION ALL SELECT 87 UNION ALL SELECT 88 UNION ALL SELECT 89 UNION ALL SELECT 90 UNION ALL SELECT 91 UNION ALL SELECT 92 UNION ALL SELECT 93 UNION ALL SELECT 94 UNION ALL SELECT 95 UNION ALL SELECT 96) numbers
                 INNER JOIN (SELECT group_concat(group_leader_ids) AS group_leader_ids
                             FROM UOMP_TEAM
                             WHERE id IN (SELECT DISTINCT group_id_
                                          FROM ORG_RELATION o
                                                   INNER JOIN uomp_team t ON o.group_id_ = t.id
                                          WHERE t.STATUS = '1'
                                            AND t.del_flag = '0'
                                            AND o.type_ = 'postUser'
                                            AND user_id_ = #{userId})) t
                            ON CHAR_LENGTH(t.group_leader_ids) - CHAR_LENGTH(REPLACE(t.group_leader_ids, ',', '')) >=
                               numbers.n - 1
        WHERE TRIM(BOTH ',' FROM SUBSTRING_INDEX(SUBSTRING_INDEX(t.group_leader_ids, ',', numbers.n), ',', -1))
        &lt;> '' GROUP BY numbers.n )
    </select>

    <select id="selectUserInfoByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO" >
        SELECT
        id_ as id,
        fullname_ as name,
        'user' as type,
        org_id as orgId
        FROM
        USER_GROUP_ORG
        WHERE
        id_ in (SELECT SUBSTRING_INDEX(
        SUBSTRING_INDEX(
        wm_concat(DISTINCT SUBSTRING_INDEX(SUBSTRING_INDEX(t.group_leader_ids, ',', numbers.n), ',', -1)),
        ',',
        numbers.n
        ),
        ',',
        -1
        ) AS leader_id
        FROM ( SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4  UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8  UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20
        UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23  UNION ALL SELECT 24 UNION ALL SELECT 25 UNION ALL SELECT 26 UNION ALL SELECT 27  UNION ALL SELECT 28 UNION ALL SELECT 29 UNION ALL SELECT 30 UNION ALL SELECT 31 UNION ALL SELECT 32 UNION ALL SELECT 33 UNION ALL SELECT 34 UNION ALL SELECT 35 UNION ALL SELECT 36 UNION ALL SELECT 37 UNION ALL SELECT 38 UNION ALL SELECT 39
        UNION ALL SELECT 40 UNION ALL SELECT 41 UNION ALL SELECT 42  UNION ALL SELECT 43 UNION ALL SELECT 44 UNION ALL SELECT 45 UNION ALL SELECT 46  UNION ALL SELECT 47 UNION ALL SELECT 48 UNION ALL SELECT 49 UNION ALL SELECT 50 UNION ALL SELECT 51 UNION ALL SELECT 52 UNION ALL SELECT 53 UNION ALL SELECT 54 UNION ALL SELECT 55 UNION ALL SELECT 56 UNION ALL SELECT 57 UNION ALL SELECT 58
        UNION ALL SELECT 59 UNION ALL SELECT 60 UNION ALL SELECT 61  UNION ALL SELECT 62 UNION ALL SELECT 63 UNION ALL SELECT 64 UNION ALL SELECT 65  UNION ALL SELECT 66 UNION ALL SELECT 67 UNION ALL SELECT 68 UNION ALL SELECT 69 UNION ALL SELECT 70 UNION ALL SELECT 71 UNION ALL SELECT 72 UNION ALL SELECT 73 UNION ALL SELECT 74 UNION ALL SELECT 75 UNION ALL SELECT 76 UNION ALL SELECT 77
        UNION ALL SELECT 78 UNION ALL SELECT 79 UNION ALL SELECT 80  UNION ALL SELECT 81 UNION ALL SELECT 82 UNION ALL SELECT 83 UNION ALL SELECT 84  UNION ALL SELECT 85 UNION ALL SELECT 86 UNION ALL SELECT 87 UNION ALL SELECT 88 UNION ALL SELECT 89 UNION ALL SELECT 90 UNION ALL SELECT 91 UNION ALL SELECT 92 UNION ALL SELECT 93 UNION ALL SELECT 94 UNION ALL SELECT 95 UNION ALL SELECT 96) numbers
        INNER JOIN (SELECT wm_concat(group_leader_ids) AS group_leader_ids
        FROM UOMP_TEAM
        WHERE id IN (SELECT DISTINCT group_id_
        FROM ORG_RELATION o
        INNER JOIN uomp_team t ON o.group_id_ = t.id
        WHERE t.STATUS = '1'
        AND t.del_flag = '0'
        AND o.type_ = 'postUser'
        AND user_id_ = #{userId})) t
        ON CHAR_LENGTH(t.group_leader_ids) - CHAR_LENGTH(REPLACE(t.group_leader_ids, ',', '')) >=
        numbers.n - 1
        WHERE TRIM(BOTH ',' FROM SUBSTRING_INDEX(SUBSTRING_INDEX(t.group_leader_ids, ',', numbers.n), ',', -1))
        &lt;> '' GROUP BY numbers.n )
    </select>


    <select id="selectNoOwerPostList" resultType="cn.gwssi.ecloud.staffpool.dto.PostDTO" databaseId='mysql'>
        select
            opost.id_ as id,
            opost.name_ as name,
            opost.desc_ as `desc`
        from org_post opost
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="selectNoOwerPostList" resultType="cn.gwssi.ecloud.staffpool.dto.PostDTO">
        select
        opost.id_ as id,
        opost.name_ as name,
        opost.desc_ as "desc"
        from org_post opost
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>
</mapper>
