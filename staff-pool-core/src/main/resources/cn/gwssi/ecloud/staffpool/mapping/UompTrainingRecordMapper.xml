<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTrainingRecordMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TRAINING_NAME" jdbcType="VARCHAR" property="trainingName" />
    <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
    <result column="TRAINING_MODE" jdbcType="VARCHAR" property="trainingMode" />
    <result column="CONFERENCE_NUM" jdbcType="VARCHAR" property="conferenceNum" />
    <result column="TRAINING_BEGIN_TIME" jdbcType="TIMESTAMP" property="trainingBeginTime" />
    <result column="TRAINING_END_TIME" jdbcType="TIMESTAMP" property="trainingEndTime" />
    <result column="TRAINING_TEACHER" jdbcType="VARCHAR" property="trainingTeacher" />
    <result column="TRAINING_PLAN_ID" jdbcType="VARCHAR" property="trainingPlanId" />
    <result column="TRAINING_CONTENT" jdbcType="VARCHAR" property="trainingContent" />
    <result column="TRAINING_SITE" jdbcType="VARCHAR" property="trainingSite" />
    <result column="SIGN_IN_NUM" jdbcType="VARCHAR" property="signInNum" />
    <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="TRAINING_PLAN_NAME" jdbcType="VARCHAR" property="trainingPlanName" />
    <result column="TRAINING_DURATION" jdbcType="VARCHAR" property="trainingDuration" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRAINING_NAME, PROJECT_NAME, TRAINING_MODE, CONFERENCE_NUM, TRAINING_BEGIN_TIME,
    TRAINING_END_TIME, TRAINING_TEACHER, TRAINING_PLAN_ID, TRAINING_CONTENT, TRAINING_SITE,
    SIGN_IN_NUM, FILE_INFO, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME,
    UPDATE_ORG_ID, DEL_FLAG, TRAINING_PLAN_NAME, TRAINING_DURATION
  </sql>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord">
    <!--@mbg.generated-->
    insert into uomp_training_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="trainingName != null">
        TRAINING_NAME,
      </if>
      <if test="projectName != null">
        PROJECT_NAME,
      </if>
      <if test="trainingMode != null">
        TRAINING_MODE,
      </if>
      <if test="conferenceNum != null">
        CONFERENCE_NUM,
      </if>
      <if test="trainingBeginTime != null">
        TRAINING_BEGIN_TIME,
      </if>
      <if test="trainingEndTime != null">
        TRAINING_END_TIME,
      </if>
      <if test="trainingTeacher != null">
        TRAINING_TEACHER,
      </if>
      <if test="trainingPlanId != null">
        TRAINING_PLAN_ID,
      </if>
      <if test="trainingContent != null">
        TRAINING_CONTENT,
      </if>
      <if test="trainingSite != null">
        TRAINING_SITE,
      </if>
      <if test="signInNum != null">
        SIGN_IN_NUM,
      </if>
      <if test="fileInfo != null">
        FILE_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="trainingPlanName != null">
        TRAINING_PLAN_NAME,
      </if>
      <if test="trainingDuration != null">
        TRAINING_DURATION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="trainingName != null">
        #{trainingName,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="trainingMode != null">
        #{trainingMode,jdbcType=VARCHAR},
      </if>
      <if test="conferenceNum != null">
        #{conferenceNum,jdbcType=VARCHAR},
      </if>
      <if test="trainingBeginTime != null">
        #{trainingBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainingEndTime != null">
        #{trainingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainingTeacher != null">
        #{trainingTeacher,jdbcType=VARCHAR},
      </if>
      <if test="trainingPlanId != null">
        #{trainingPlanId,jdbcType=VARCHAR},
      </if>
      <if test="trainingContent != null">
        #{trainingContent,jdbcType=VARCHAR},
      </if>
      <if test="trainingSite != null">
        #{trainingSite,jdbcType=VARCHAR},
      </if>
      <if test="signInNum != null">
        #{signInNum,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="trainingPlanName != null">
        #{trainingPlanName,jdbcType=VARCHAR},
      </if>
      <if test="trainingDuration != null">
        #{trainingDuration,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectTrainingRecordByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    b.ID,
    b.TRAINING_NAME,
    b.TRAINING_MODE,
    b.TRAINING_TEACHER,
    b.TRAINING_CONTENT,
    b.TRAINING_SITE,
    b.TRAINING_BEGIN_TIME,
    b.TRAINING_END_TIME
    from UOMP_TRAINING_RECORD_PERSON a
    left join UOMP_TRAINING_RECORD b on a.TRAINING_RECORD_ID = b.ID
    where b.DEL_FLAG = '0'
    and a.TRAINING_PERSON_ID = #{userId}
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord">
    <!--@mbg.generated-->
    update gwuomp_dev.uomp_training_record
    <set>
      <if test="trainingName != null">
        TRAINING_NAME = #{trainingName,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="trainingMode != null">
        TRAINING_MODE = #{trainingMode,jdbcType=VARCHAR},
      </if>
      <if test="conferenceNum != null">
        CONFERENCE_NUM = #{conferenceNum,jdbcType=VARCHAR},
      </if>
      <if test="trainingBeginTime != null">
        TRAINING_BEGIN_TIME = #{trainingBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainingEndTime != null">
        TRAINING_END_TIME = #{trainingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trainingTeacher != null">
        TRAINING_TEACHER = #{trainingTeacher,jdbcType=VARCHAR},
      </if>
      <if test="trainingPlanId != null">
        TRAINING_PLAN_ID = #{trainingPlanId,jdbcType=VARCHAR},
      </if>
      <if test="trainingContent != null">
        TRAINING_CONTENT = #{trainingContent,jdbcType=VARCHAR},
      </if>
      <if test="trainingSite != null">
        TRAINING_SITE = #{trainingSite,jdbcType=VARCHAR},
      </if>
      <if test="signInNum != null">
        SIGN_IN_NUM = #{signInNum,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="trainingPlanName != null">
        TRAINING_PLAN_NAME = #{trainingPlanName,jdbcType=VARCHAR},
      </if>
      <if test="trainingDuration != null">
        TRAINING_DURATION = #{trainingDuration,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    b.ID,
    b.TRAINING_NAME,
    b.TRAINING_MODE,
    b.TRAINING_TEACHER,
    b.TRAINING_CONTENT,
    b.TRAINING_SITE,
    b.TRAINING_BEGIN_TIME,
    b.TRAINING_END_TIME
    from UOMP_TRAINING_RECORD_PERSON a
    left join UOMP_TRAINING_RECORD b on a.TRAINING_RECORD_ID = b.ID
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>
