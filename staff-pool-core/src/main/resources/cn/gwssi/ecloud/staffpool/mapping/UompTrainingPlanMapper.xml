<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTrainingPlanMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan">
        <!--@mbg.generated-->
        <!--@Table uomp_training_plan-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="TRAINING_PLAN_NAME" jdbcType="VARCHAR" property="trainingPlanName"/>
        <result column="TRAINING_PURPOSE" jdbcType="VARCHAR" property="trainingPurpose"/>
        <result column="TRAINING_THEME" jdbcType="VARCHAR" property="trainingTheme"/>
        <result column="TRAINING_RESOURCE" jdbcType="VARCHAR" property="trainingResource"/>
        <result column="TRAINING_OBJECT" jdbcType="VARCHAR" property="trainingObject"/>
        <result column="TRAINING_MODE" jdbcType="VARCHAR" property="trainingMode"/>
        <result column="EVALUATION_MODE" jdbcType="VARCHAR" property="evaluationMode"/>
        <result column="TRAINING_BEGIN_TIME" jdbcType="TIMESTAMP" property="trainingBeginTime"/>
        <result column="TRAINING_END_TIME" jdbcType="TIMESTAMP" property="trainingEndTime"/>
        <result column="TRAINING_CONTENT" jdbcType="VARCHAR" property="trainingContent"/>
        <result column="ONLINE_ID" jdbcType="VARCHAR" property="onlineId"/>
        <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, TRAINING_PLAN_NAME, TRAINING_PURPOSE, TRAINING_THEME, TRAINING_RESOURCE, TRAINING_OBJECT,
        TRAINING_MODE, EVALUATION_MODE, TRAINING_BEGIN_TIME, TRAINING_END_TIME, TRAINING_CONTENT,
        ONLINE_ID, FILE_INFO, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_training_plan
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from uomp_training_plan
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan">
        <!--@mbg.generated-->
        insert into uomp_training_plan (ID, TRAINING_PLAN_NAME, TRAINING_PURPOSE,
        TRAINING_THEME, TRAINING_RESOURCE, TRAINING_OBJECT,
        TRAINING_MODE, EVALUATION_MODE, TRAINING_BEGIN_TIME,
        TRAINING_END_TIME, TRAINING_CONTENT, ONLINE_ID,
        FILE_INFO, CREATE_BY, CREATE_TIME,
        UPDATE_BY, UPDATE_TIME, DEL_FLAG
        )
        values (#{id,jdbcType=VARCHAR}, #{trainingPlanName,jdbcType=VARCHAR}, #{trainingPurpose,jdbcType=VARCHAR},
        #{trainingTheme,jdbcType=VARCHAR}, #{trainingResource,jdbcType=VARCHAR}, #{trainingObject,jdbcType=VARCHAR},
        #{trainingMode,jdbcType=VARCHAR}, #{evaluationMode,jdbcType=VARCHAR}, #{trainingBeginTime,jdbcType=TIMESTAMP},
        #{trainingEndTime,jdbcType=TIMESTAMP}, #{trainingContent,jdbcType=VARCHAR}, #{onlineId,jdbcType=VARCHAR},
        #{fileInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan">
        <!--@mbg.generated-->
        insert into uomp_training_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="trainingPlanName != null">
                TRAINING_PLAN_NAME,
            </if>
            <if test="trainingPurpose != null">
                TRAINING_PURPOSE,
            </if>
            <if test="trainingTheme != null">
                TRAINING_THEME,
            </if>
            <if test="trainingResource != null">
                TRAINING_RESOURCE,
            </if>
            <if test="trainingObject != null">
                TRAINING_OBJECT,
            </if>
            <if test="trainingMode != null">
                TRAINING_MODE,
            </if>
            <if test="evaluationMode != null">
                EVALUATION_MODE,
            </if>
            <if test="trainingBeginTime != null">
                TRAINING_BEGIN_TIME,
            </if>
            <if test="trainingEndTime != null">
                TRAINING_END_TIME,
            </if>
            <if test="trainingContent != null">
                TRAINING_CONTENT,
            </if>
            <if test="onlineId != null">
                ONLINE_ID,
            </if>
            <if test="fileInfo != null">
                FILE_INFO,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="trainingPlanName != null">
                #{trainingPlanName,jdbcType=VARCHAR},
            </if>
            <if test="trainingPurpose != null">
                #{trainingPurpose,jdbcType=VARCHAR},
            </if>
            <if test="trainingTheme != null">
                #{trainingTheme,jdbcType=VARCHAR},
            </if>
            <if test="trainingResource != null">
                #{trainingResource,jdbcType=VARCHAR},
            </if>
            <if test="trainingObject != null">
                #{trainingObject,jdbcType=VARCHAR},
            </if>
            <if test="trainingMode != null">
                #{trainingMode,jdbcType=VARCHAR},
            </if>
            <if test="evaluationMode != null">
                #{evaluationMode,jdbcType=VARCHAR},
            </if>
            <if test="trainingBeginTime != null">
                #{trainingBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trainingEndTime != null">
                #{trainingEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trainingContent != null">
                #{trainingContent,jdbcType=VARCHAR},
            </if>
            <if test="onlineId != null">
                #{onlineId,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan">
        <!--@mbg.generated-->
        update uomp_training_plan
        <set>
            <if test="trainingPlanName != null">
                TRAINING_PLAN_NAME = #{trainingPlanName,jdbcType=VARCHAR},
            </if>
            <if test="trainingPurpose != null">
                TRAINING_PURPOSE = #{trainingPurpose,jdbcType=VARCHAR},
            </if>
            <if test="trainingTheme != null">
                TRAINING_THEME = #{trainingTheme,jdbcType=VARCHAR},
            </if>
            <if test="trainingResource != null">
                TRAINING_RESOURCE = #{trainingResource,jdbcType=VARCHAR},
            </if>
            <if test="trainingObject != null">
                TRAINING_OBJECT = #{trainingObject,jdbcType=VARCHAR},
            </if>
            <if test="trainingMode != null">
                TRAINING_MODE = #{trainingMode,jdbcType=VARCHAR},
            </if>
            <if test="evaluationMode != null">
                EVALUATION_MODE = #{evaluationMode,jdbcType=VARCHAR},
            </if>
            <if test="trainingBeginTime != null">
                TRAINING_BEGIN_TIME = #{trainingBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trainingEndTime != null">
                TRAINING_END_TIME = #{trainingEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trainingContent != null">
                TRAINING_CONTENT = #{trainingContent,jdbcType=VARCHAR},
            </if>
            <if test="onlineId != null">
                ONLINE_ID = #{onlineId,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan">
        <!--@mbg.generated-->
        update uomp_training_plan
        set TRAINING_PLAN_NAME = #{trainingPlanName,jdbcType=VARCHAR},
        TRAINING_PURPOSE = #{trainingPurpose,jdbcType=VARCHAR},
        TRAINING_THEME = #{trainingTheme,jdbcType=VARCHAR},
        TRAINING_RESOURCE = #{trainingResource,jdbcType=VARCHAR},
        TRAINING_OBJECT = #{trainingObject,jdbcType=VARCHAR},
        TRAINING_MODE = #{trainingMode,jdbcType=VARCHAR},
        EVALUATION_MODE = #{evaluationMode,jdbcType=VARCHAR},
        TRAINING_BEGIN_TIME = #{trainingBeginTime,jdbcType=TIMESTAMP},
        TRAINING_END_TIME = #{trainingEndTime,jdbcType=TIMESTAMP},
        TRAINING_CONTENT = #{trainingContent,jdbcType=VARCHAR},
        ONLINE_ID = #{onlineId,jdbcType=VARCHAR},
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select *
        from uomp_training_plan
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
</mapper>
