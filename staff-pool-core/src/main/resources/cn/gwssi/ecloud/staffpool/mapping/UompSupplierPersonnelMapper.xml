<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompSupplierPersonnelMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel">
    <!--@mbg.generated-->
    <!--@Table uomp_supplier_personnel-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SUPPLIER_MANAGEMENT_ID" jdbcType="VARCHAR" property="supplierManagementId" />
    <result column="PERSONNEL_ID" jdbcType="VARCHAR" property="personnelId" />
    <result column="PERSONNEL_NAME" jdbcType="VARCHAR" property="personnelName" />
    <result column="PERSONNEL_POST" jdbcType="VARCHAR" property="personnelPost" />
    <result column="PERSONNEL_TYPE" jdbcType="VARCHAR" property="personnelType" />
    <result column="PERSONNEL_TEL" jdbcType="VARCHAR" property="personnelTel" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SUPPLIER_MANAGEMENT_ID, PERSONNEL_ID, PERSONNEL_NAME, PERSONNEL_POST, PERSONNEL_TYPE, 
    PERSONNEL_TEL, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, 
    DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_supplier_personnel
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_supplier_personnel
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel">
    <!--@mbg.generated-->
    insert into uomp_supplier_personnel (ID, SUPPLIER_MANAGEMENT_ID, PERSONNEL_ID, 
      PERSONNEL_NAME, PERSONNEL_POST, PERSONNEL_TYPE, 
      PERSONNEL_TEL, CREATE_BY, CREATE_TIME, 
      CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
      UPDATE_ORG_ID, DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{supplierManagementId,jdbcType=VARCHAR}, #{personnelId,jdbcType=VARCHAR}, 
      #{personnelName,jdbcType=VARCHAR}, #{personnelPost,jdbcType=VARCHAR}, #{personnelType,jdbcType=VARCHAR}, 
      #{personnelTel,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel">
    <!--@mbg.generated-->
    insert into uomp_supplier_personnel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="supplierManagementId != null">
        SUPPLIER_MANAGEMENT_ID,
      </if>
      <if test="personnelId != null">
        PERSONNEL_ID,
      </if>
      <if test="personnelName != null">
        PERSONNEL_NAME,
      </if>
      <if test="personnelPost != null">
        PERSONNEL_POST,
      </if>
      <if test="personnelType != null">
        PERSONNEL_TYPE,
      </if>
      <if test="personnelTel != null">
        PERSONNEL_TEL,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="supplierManagementId != null">
        #{supplierManagementId,jdbcType=VARCHAR},
      </if>
      <if test="personnelId != null">
        #{personnelId,jdbcType=VARCHAR},
      </if>
      <if test="personnelName != null">
        #{personnelName,jdbcType=VARCHAR},
      </if>
      <if test="personnelPost != null">
        #{personnelPost,jdbcType=VARCHAR},
      </if>
      <if test="personnelType != null">
        #{personnelType,jdbcType=VARCHAR},
      </if>
      <if test="personnelTel != null">
        #{personnelTel,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel">
    <!--@mbg.generated-->
    update uomp_supplier_personnel
    set SUPPLIER_MANAGEMENT_ID = #{supplierManagementId,jdbcType=VARCHAR},
      PERSONNEL_ID = #{personnelId,jdbcType=VARCHAR},
      PERSONNEL_NAME = #{personnelName,jdbcType=VARCHAR},
      PERSONNEL_POST = #{personnelPost,jdbcType=VARCHAR},
      PERSONNEL_TYPE = #{personnelType,jdbcType=VARCHAR},
      PERSONNEL_TEL = #{personnelTel,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateById" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel">
    update uomp_supplier_personnel
    <trim prefix="SET" suffixOverrides=",">
      <if test="personnelName != null">PERSONNEL_NAME = #{personnelName,jdbcType=VARCHAR},</if>
      <if test="personnelType != null">PERSONNEL_TYPE = #{personnelType,jdbcType=VARCHAR},</if>
      <if test="personnelTel != null">PERSONNEL_TEL = #{personnelTel,jdbcType=VARCHAR},</if>
      <if test="delFlag != null">DEL_FLAG = #{delFlag,jdbcType=VARCHAR},</if>
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    </trim>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>