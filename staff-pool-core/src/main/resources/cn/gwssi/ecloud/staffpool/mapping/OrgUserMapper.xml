<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgUserMapper">
    <sql id="baseColumn">
        id_,
        fullname_,
        account_,
        password_,
        email_,
        mobile_,
        weixin_,
        address_,
        photo_,
        sex_,
        from_,
        status_,
        create_time_,
        create_by_,
        update_time_,
        update_by_,
        sn_,
        telephone_,
        active_status_,
        secret_level_,
        type_,
        desc_,
        login_name_
    </sql>

    <resultMap id="UserBaseMap" type="cn.gwssi.ecloudframework.org.core.model.User">
        <id property="id" column="id_" jdbcType="VARCHAR"/>
        <result property="fullname" column="fullname_" jdbcType="VARCHAR"/>
        <result property="account" column="account_" jdbcType="VARCHAR"/>
        <result property="password" column="password_" jdbcType="VARCHAR"/>
        <result property="email" column="email_" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile_" jdbcType="VARCHAR"/>
        <result property="weixin" column="weixin_" jdbcType="VARCHAR"/>
        <result property="openid" column="openid_" jdbcType="VARCHAR"/>
        <result property="address" column="address_" jdbcType="VARCHAR"/>
        <result property="photo" column="photo_" jdbcType="VARCHAR"/>
        <result property="sex" column="sex_" jdbcType="VARCHAR"/>
        <result property="from" column="from_" jdbcType="VARCHAR"/>
        <result property="status" column="status_" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time_" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by_" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time_" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by_" jdbcType="VARCHAR"/>
        <result property="sn" column="sn_" jdbcType="INTEGER"/>
        <result property="telephone" column="telephone_" jdbcType="VARCHAR"/>
        <result property="activeStatus" column="active_status_" jdbcType="INTEGER"/>
        <result property="secretLevel" column="secret_level_" jdbcType="INTEGER"/>
        <result property="type" column="type_" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getUserList" resultMap="UserBaseMap">
        select DISTINCT ou.* from ORG_USER ou
        inner join uomp_person_info upi on ou.account_ = upi.ACCOUNT and upi.ACCOUNT is not null
        inner join org_relation orelation on orelation.user_id_ = ou.id_ and orelation.type_ = 'userRole'
        INNER join org_role orole on orole.id_ = orelation.group_id_
        WHERE orole.alias_ in ('G_ROLE_CUSTOMER_LEADER','G_ROLE_MANAGER')
    </select>
    <select id="selectOrgUserByAccount" resultType="java.lang.String">
        select id_ AS org_user_id
        from ORG_USER
        where ACCOUNT_ = #{accountNum} limit 1
    </select>
    <update id="updateStatusById">
        update
            ORG_USER
        set ACTIVE_STATUS_ = 1,
            STATUS_        = 1
        where ID_ = #{id}
    </update>
    <select id="selectUserStatus" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionPersonListDTO" databaseId='mysql'>
        select user_id                    as userId,
               max(user_name)             as userName,
               max(user_account)          as userAccount,
               group_concat(group_id)     as groupId,
               group_concat(group_name)   as groupName,
               group_concat(org_pathname) as orgPathName
        from (
        select upi.ID         as user_id,
               tuser.FULLNAME_   as user_name,
               tuser.ACCOUNT_    as user_account,
               tgroup.id_        as group_id,
               tgroup.name_      as group_name,
               tgroup.path_name_ as org_pathname
        from uomp_person_info upi
        inner join org_user tuser ON tuser.id_ = upi.ORG_USER_ID
        inner join uomp_org_group tgroup on upi.ORG_GROUP_ID = tgroup.id_
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        ) groupUser
        group by groupUser.user_id
    </select>
    <select id="selectUserStatus" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionPersonListDTO">
        select user_id                    as userId,
        max(user_name)             as userName,
        max(user_account)          as userAccount,
        wm_concat(group_id)     as groupId,
        wm_concat(group_name)   as groupName,
        wm_concat(org_pathname) as orgPathName
        from (
        select upi.ID         as user_id,
        tuser.FULLNAME_   as user_name,
        tuser.ACCOUNT_    as user_account,
        tgroup.id_        as group_id,
        tgroup.name_      as group_name,
        tgroup.path_name_ as org_pathname
        from uomp_person_info upi
        inner join org_user tuser ON tuser.id_ = upi.ORG_USER_ID
        inner join uomp_org_group tgroup on upi.ORG_GROUP_ID = tgroup.id_
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
        ) groupUser
        group by groupUser.user_id
    </select>
    <select id="selectListByGroupId" resultType="cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO">
        select ID_        as id,
               NAME_      as name,
               PARENT_ID_ as parentId,
               PATH_      as path
        from ORG_GROUP
        WHERE STATUS_ = 1
          and ID_ = #{groupId} limit 1
    </select>
    <select id="selectAllByStatus" resultType="cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO">
        select ID_        as id,
               NAME_      as name,
               PARENT_ID_ as parentId,
               PATH_      as path
        from ORG_GROUP
        WHERE STATUS_ = 1
    </select>
    <update id="updateStatusById1">
        update
            ORG_USER
        set STATUS_ = #{status}
        where ID_ = #{id}
    </update>
    <select id="selectFullNameById" resultType="java.lang.String">
        select fullname_
        from org_user
        where id_ = #{id}
    </select>

    <select id="selectUserInfoByAccount" resultType="java.lang.String">
        select id_
        from org_user
        where account_ = #{account}
    </select>

    <select id="selectUserInfoByIds" resultType="cn.gwssi.ecloud.staffpool.dto.OrgUserBaseDTO">
        select id_ as userId, account_ as account, fullname_ as name, email_ as email
        from org_user where id_ in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllBypersonCard" resultType="java.lang.String">
        select a.ID_ as userId
        from org_user a
                 inner join UOMP_PERSON_INFO b on
                    a.ACCOUNT_ = b.account
                and b.DEL_FLAG = '0'
        where b.PERSON_CARD = #{personCard}
    </select>

    <select id="countByAccount" resultType="java.lang.Integer">
        select count(1)
        from ORG_USER
        where ACCOUNT_ = #{account}
          AND STATUS_ = 1
          and ACTIVE_STATUS_ = 1
    </select>

    <select id="selectIdByPersonCard" resultType="java.lang.String">
        select ID_
        from org_user
        where ACCOUNT_ = (select ACCOUNT from UOMP_PERSON_INFO where DEL_FLAG = '0' and PERSON_CARD = #{personCard})
    </select>

    <update id="updateStatus">
        update org_user
        set status_ = #{status}
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
    </update>
</mapper>
