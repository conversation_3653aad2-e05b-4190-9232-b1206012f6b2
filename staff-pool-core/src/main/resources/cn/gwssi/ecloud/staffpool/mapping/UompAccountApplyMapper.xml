<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompAccountApplyMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply">
    <!--@mbg.generated-->
    <!--@Table uomp_account_apply-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="APPLY_PERSON" jdbcType="VARCHAR" property="applyPerson" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="APPLY_ITEM" jdbcType="VARCHAR" property="applyItem" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TITLE, APPLY_TIME, APPLY_PERSON, `STATUS`, APPLY_ITEM, DEL_FLAG, CREATE_BY, CREATE_TIME, 
    CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, INST_ID
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_account_apply
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_account_apply
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply">
    <!--@mbg.generated-->
    insert into uomp_account_apply (ID, TITLE, APPLY_TIME, 
      APPLY_PERSON, `STATUS`, APPLY_ITEM, 
      DEL_FLAG, CREATE_BY, CREATE_TIME, 
      CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
      UPDATE_ORG_ID, INST_ID)
    values (#{id,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{applyPerson,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{applyItem,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOrgId,jdbcType=VARCHAR}, #{instId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply">
    <!--@mbg.generated-->
    insert into uomp_account_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="title != null">
        TITLE,
      </if>
      <if test="applyTime != null">
        APPLY_TIME,
      </if>
      <if test="applyPerson != null">
        APPLY_PERSON,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="applyItem != null">
        APPLY_ITEM,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyPerson != null">
        #{applyPerson,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="applyItem != null">
        #{applyItem,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply">
    <!--@mbg.generated-->
    update uomp_account_apply
    set TITLE = #{title,jdbcType=VARCHAR},
      APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      APPLY_PERSON = #{applyPerson,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=VARCHAR},
      APPLY_ITEM = #{applyItem,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      INST_ID = #{instId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <!-- 账号申请列表查询sql -->
  <select id="accountNumList" resultType="cn.gwssi.ecloud.staffpool.dto.UompAccountApplyDTO">
    SELECT
    uaa.ID as id,
    uaa.TITLE as title,
    uaa.APPLY_TIME as applyTime,
    uaa.APPLY_PERSON as applyPerson,
    uaa.STATUS as status,
    uaa.INST_ID as instId
    FROM
    UOMP_ACCOUNT_APPLY uaa
    INNER JOIN bpm_instance bi ON uaa.INST_ID = bi.id_ AND bi.status_ != 'discard'
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>

  <select id="selectInfoOneByPersonId" resultType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply">
      select
          b.INST_ID as instId,
          b.CREATE_BY as createBy
      from
          UOMP_ACCOUNT_APPLY_PERSON a
      inner join UOMP_ACCOUNT_APPLY b on a.APPLY_ID = b.ID
      where
          a.PERSON_ID = #{personId}
      order by a.CREATE_TIME desc
      limit 1
  </select>
</mapper>