<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompFortressMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompFortress">
    <!--@mbg.generated-->
    <!--@Table uomp_fortress-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NODE_ID" jdbcType="VARCHAR" property="nodeId" />
    <result column="FORT_TYPE" jdbcType="VARCHAR" property="fortType" />
    <result column="FORT_NAME" jdbcType="VARCHAR" property="fortName" />
    <result column="FORT_KEY" jdbcType="VARCHAR" property="fortKey" />
    <result column="FORT_VERSION" jdbcType="VARCHAR" property="fortVersion" />
    <result column="SERVICE_URL" jdbcType="VARCHAR" property="serviceUrl" />
    <result column="FORT_STATE" jdbcType="VARCHAR" property="fortState" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, NODE_ID, FORT_TYPE, FORT_NAME, FORT_KEY, FORT_VERSION, SERVICE_URL, FORT_STATE, 
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_fortress
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_fortress
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortress">
    <!--@mbg.generated-->
    insert into uomp_fortress (ID, NODE_ID, FORT_TYPE, 
      FORT_NAME, FORT_KEY, FORT_VERSION, 
      SERVICE_URL, FORT_STATE, CREATE_BY, 
      CREATE_TIME, UPDATE_BY, UPDATE_TIME, 
      DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, #{fortType,jdbcType=VARCHAR}, 
      #{fortName,jdbcType=VARCHAR}, #{fortKey,jdbcType=VARCHAR}, #{fortVersion,jdbcType=VARCHAR}, 
      #{serviceUrl,jdbcType=VARCHAR}, #{fortState,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortress">
    <!--@mbg.generated-->
    insert into uomp_fortress
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="nodeId != null">
        NODE_ID,
      </if>
      <if test="fortType != null">
        FORT_TYPE,
      </if>
      <if test="fortName != null">
        FORT_NAME,
      </if>
      <if test="fortKey != null">
        FORT_KEY,
      </if>
      <if test="fortVersion != null">
        FORT_VERSION,
      </if>
      <if test="serviceUrl != null">
        SERVICE_URL,
      </if>
      <if test="fortState != null">
        FORT_STATE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="fortType != null">
        #{fortType,jdbcType=VARCHAR},
      </if>
      <if test="fortName != null">
        #{fortName,jdbcType=VARCHAR},
      </if>
      <if test="fortKey != null">
        #{fortKey,jdbcType=VARCHAR},
      </if>
      <if test="fortVersion != null">
        #{fortVersion,jdbcType=VARCHAR},
      </if>
      <if test="serviceUrl != null">
        #{serviceUrl,jdbcType=VARCHAR},
      </if>
      <if test="fortState != null">
        #{fortState,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortress">
    <!--@mbg.generated-->
    update uomp_fortress
    set NODE_ID = #{nodeId,jdbcType=VARCHAR},
      FORT_TYPE = #{fortType,jdbcType=VARCHAR},
      FORT_NAME = #{fortName,jdbcType=VARCHAR},
      FORT_KEY = #{fortKey,jdbcType=VARCHAR},
      FORT_VERSION = #{fortVersion,jdbcType=VARCHAR},
      SERVICE_URL = #{serviceUrl,jdbcType=VARCHAR},
      FORT_STATE = #{fortState,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectInfoById" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessDTO">
      select
      f.id as fortId,
      f.service_url as serviceUrl,
      a.auth_url as authUrl,
      a.auth_account as authAccount,
      a.auth_password as authPassword
      from UOMP_FORTRESS f
      inner join uomp_fortress_api a on a.FORT_ID = f.ID and a.DEL_FLAG = '0'
      where f.FORT_STATE = '1' and f.del_flag = '0'
      and f.id = #{fortId} limit 1
  </select>
  <select id="selectAll" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessDTO">
      select
      f.id as fortId,
      f.service_url as serviceUrl,
      a.auth_url as authUrl,
      a.auth_account as authAccount,
      a.auth_password as authPassword
      from UOMP_FORTRESS f
      inner join uomp_fortress_api a on a.FORT_ID = f.ID and a.DEL_FLAG = '0'
      where f.FORT_STATE = '1' and f.del_flag = '0'
  </select>
</mapper>