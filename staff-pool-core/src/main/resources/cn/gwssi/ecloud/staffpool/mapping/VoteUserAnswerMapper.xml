<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.VoteUserAnswerMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer">
    <!--@mbg.generated-->
    <!--@Table vote_user_answer-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="VOTE_ID" jdbcType="VARCHAR" property="voteId" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="ANSWER" jdbcType="VARCHAR" property="answer" />
    <result column="ANSWER_STATUS" jdbcType="VARCHAR" property="answerStatus" />
    <result column="ATTACH_STATUS" jdbcType="VARCHAR" property="attachStatus" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="SJ_ORG_ID" jdbcType="VARCHAR" property="sjOrgId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REVIEW_RESULT" jdbcType="VARCHAR" property="reviewResult" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VOTE_ID, USER_ID, ANSWER, ANSWER_STATUS, ATTACH_STATUS, TITLE, SJ_ORG_ID, CREATE_TIME,
    UPDATE_TIME, REVIEW_RESULT
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from vote_user_answer
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from vote_user_answer
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer">
    <!--@mbg.generated-->
    insert into vote_user_answer (ID, VOTE_ID, USER_ID,
      ANSWER, ANSWER_STATUS, ATTACH_STATUS,
      TITLE, SJ_ORG_ID, CREATE_TIME,
      UPDATE_TIME, REVIEW_RESULT)
    values (#{id,jdbcType=VARCHAR}, #{voteId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{answer,jdbcType=VARCHAR}, #{answerStatus,jdbcType=VARCHAR}, #{attachStatus,jdbcType=VARCHAR},
      #{title,jdbcType=VARCHAR}, #{sjOrgId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{reviewResult,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer">
    <!--@mbg.generated-->
    insert into vote_user_answer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="voteId != null">
        VOTE_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="answer != null">
        ANSWER,
      </if>
      <if test="answerStatus != null">
        ANSWER_STATUS,
      </if>
      <if test="attachStatus != null">
        ATTACH_STATUS,
      </if>
      <if test="title != null">
        TITLE,
      </if>
      <if test="sjOrgId != null">
        SJ_ORG_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="reviewResult != null">
        REVIEW_RESULT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="voteId != null">
        #{voteId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=VARCHAR},
      </if>
      <if test="answerStatus != null">
        #{answerStatus,jdbcType=VARCHAR},
      </if>
      <if test="attachStatus != null">
        #{attachStatus,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="sjOrgId != null">
        #{sjOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewResult != null">
        #{reviewResult,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer">
    <!--@mbg.generated-->
    update vote_user_answer
    <set>
      <if test="voteId != null">
        VOTE_ID = #{voteId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="answer != null">
        ANSWER = #{answer,jdbcType=VARCHAR},
      </if>
      <if test="answerStatus != null">
        ANSWER_STATUS = #{answerStatus,jdbcType=VARCHAR},
      </if>
      <if test="attachStatus != null">
        ATTACH_STATUS = #{attachStatus,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="sjOrgId != null">
        SJ_ORG_ID = #{sjOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewResult != null">
        REVIEW_RESULT = #{reviewResult,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer">
    <!--@mbg.generated-->
    update vote_user_answer
    set VOTE_ID = #{voteId,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      ANSWER = #{answer,jdbcType=VARCHAR},
      ANSWER_STATUS = #{answerStatus,jdbcType=VARCHAR},
      ATTACH_STATUS = #{attachStatus,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      SJ_ORG_ID = #{sjOrgId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REVIEW_RESULT = #{reviewResult,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
