<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompApplicationSystemManagementMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement">
        <!--@mbg.generated-->
        <!--@Table uomp_application_system_management-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="APPLICATION_SYSTEM_NAME" jdbcType="VARCHAR" property="applicationSystemName"/>
        <result column="DEPART_ID" jdbcType="VARCHAR" property="departId"/>
        <result column="DEPART_NAME" jdbcType="VARCHAR" property="departName"/>
        <result column="PRINCIPAL_NAME" jdbcType="VARCHAR" property="principalName"/>
        <result column="PRINCIPAL_ID" jdbcType="VARCHAR" property="principalId"/>
        <result column="IS_HEART" jdbcType="VARCHAR" property="isHeart"/>
        <result column="ONLINE_TIME" jdbcType="TIMESTAMP" property="onlineTime"/>
        <result column="SYSTEM_STATUS" jdbcType="VARCHAR" property="systemStatus"/>
        <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId"/>
        <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName"/>
        <result column="SUPPLIER_DEPART_NAME" jdbcType="VARCHAR" property="supplierDepartName"/>
        <result column="SUPPLIER_TEL" jdbcType="VARCHAR" property="supplierTel"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        APPLICATION_SYSTEM_NAME,
        DEPART_ID,
        DEPART_NAME,
        PRINCIPAL_NAME,
        PRINCIPAL_ID,
        IS_HEART,
        ONLINE_TIME,
        SYSTEM_STATUS,
        SUPPLIER_ID,
        SUPPLIER_NAME,
        SUPPLIER_DEPART_NAME,
        SUPPLIER_TEL,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG
    </sql>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from uomp_application_system_management
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement">
        <!--@mbg.generated-->
        insert into uomp_application_system_management (ID,
                                                        APPLICATION_SYSTEM_NAME, DEPART_ID, DEPART_NAME,
                                                        PRINCIPAL_NAME, PRINCIPAL_ID, IS_HEART,
                                                        ONLINE_TIME, SYSTEM_STATUS, SUPPLIER_ID,
                                                        SUPPLIER_NAME, SUPPLIER_DEPART_NAME, SUPPLIER_TEL,
                                                        CREATE_BY, CREATE_TIME, CREATE_ORG_ID,
                                                        UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
                                                        DEL_FLAG)
        values (#{id,jdbcType=VARCHAR},
                #{applicationSystemName,jdbcType=VARCHAR}, #{departId,jdbcType=VARCHAR}, #{departName,jdbcType=VARCHAR},
                #{principalName,jdbcType=VARCHAR}, #{principalId,jdbcType=VARCHAR}, #{isHeart,jdbcType=VARCHAR},
                #{onlineTime,jdbcType=TIMESTAMP}, #{systemStatus,jdbcType=VARCHAR}, #{supplierId,jdbcType=VARCHAR},
                #{supplierName,jdbcType=VARCHAR}, #{supplierDepartName,jdbcType=VARCHAR},
                #{supplierTel,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR},
                #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR},
                #{delFlag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement">
        <!--@mbg.generated-->
        insert into uomp_application_system_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="applicationSystemName != null">
                APPLICATION_SYSTEM_NAME,
            </if>
            <if test="departId != null">
                DEPART_ID,
            </if>
            <if test="departName != null">
                DEPART_NAME,
            </if>
            <if test="principalName != null">
                PRINCIPAL_NAME,
            </if>
            <if test="principalId != null">
                PRINCIPAL_ID,
            </if>
            <if test="isHeart != null">
                IS_HEART,
            </if>
            <if test="onlineTime != null">
                ONLINE_TIME,
            </if>
            <if test="systemStatus != null">
                SYSTEM_STATUS,
            </if>
            <if test="supplierId != null">
                SUPPLIER_ID,
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME,
            </if>
            <if test="supplierDepartName != null">
                SUPPLIER_DEPART_NAME,
            </if>
            <if test="supplierTel != null">
                SUPPLIER_TEL,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="applicationSystemName != null">
                #{applicationSystemName,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                #{departId,jdbcType=VARCHAR},
            </if>
            <if test="departName != null">
                #{departName,jdbcType=VARCHAR},
            </if>
            <if test="principalName != null">
                #{principalName,jdbcType=VARCHAR},
            </if>
            <if test="principalId != null">
                #{principalId,jdbcType=VARCHAR},
            </if>
            <if test="isHeart != null">
                #{isHeart,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="systemStatus != null">
                #{systemStatus,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierDepartName != null">
                #{supplierDepartName,jdbcType=VARCHAR},
            </if>
            <if test="supplierTel != null">
                #{supplierTel,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement">
        <!--@mbg.generated-->
        update uomp_application_system_management
        <set>
            <if test="applicationSystemName != null">
                APPLICATION_SYSTEM_NAME = #{applicationSystemName,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                DEPART_ID = #{departId,jdbcType=VARCHAR},
            </if>
            <if test="departName != null">
                DEPART_NAME = #{departName,jdbcType=VARCHAR},
            </if>
            <if test="principalName != null">
                PRINCIPAL_NAME = #{principalName,jdbcType=VARCHAR},
            </if>
            <if test="principalId != null">
                PRINCIPAL_ID = #{principalId,jdbcType=VARCHAR},
            </if>
            <if test="isHeart != null">
                IS_HEART = #{isHeart,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                ONLINE_TIME = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="systemStatus != null">
                SYSTEM_STATUS = #{systemStatus,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                SUPPLIER_ID = #{supplierId,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierDepartName != null">
                SUPPLIER_DEPART_NAME = #{supplierDepartName,jdbcType=VARCHAR},
            </if>
            <if test="supplierTel != null">
                SUPPLIER_TEL = #{supplierTel,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement">
        <!--@mbg.generated-->
        update uomp_application_system_management
        set APPLICATION_SYSTEM_NAME = #{applicationSystemName,jdbcType=VARCHAR},
            DEPART_ID               = #{departId,jdbcType=VARCHAR},
            DEPART_NAME             = #{departName,jdbcType=VARCHAR},
            PRINCIPAL_NAME          = #{principalName,jdbcType=VARCHAR},
            PRINCIPAL_ID            = #{principalId,jdbcType=VARCHAR},
            IS_HEART                = #{isHeart,jdbcType=VARCHAR},
            ONLINE_TIME             = #{onlineTime,jdbcType=TIMESTAMP},
            SYSTEM_STATUS           = #{systemStatus,jdbcType=VARCHAR},
            SUPPLIER_ID             = #{supplierId,jdbcType=VARCHAR},
            SUPPLIER_NAME           = #{supplierName,jdbcType=VARCHAR},
            SUPPLIER_DEPART_NAME    = #{supplierDepartName,jdbcType=VARCHAR},
            SUPPLIER_TEL            = #{supplierTel,jdbcType=VARCHAR},
            CREATE_BY               = #{createBy,jdbcType=VARCHAR},
            CREATE_TIME             = #{createTime,jdbcType=TIMESTAMP},
            CREATE_ORG_ID           = #{createOrgId,jdbcType=VARCHAR},
            UPDATE_BY               = #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME             = #{updateTime,jdbcType=TIMESTAMP},
            UPDATE_ORG_ID           = #{updateOrgId,jdbcType=VARCHAR},
            DEL_FLAG                = #{delFlag,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select s.*
        from uomp_application_system_management s
        <if test="supplierName != null and supplierName != ''">
        inner join uomp_supplier_management m ON s.SUPPLIER_ID = m.id and m.del_flag = '0'
        </if>
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
    <select id="get" resultMap="BaseResultMap">
        select *
        from uomp_application_system_management
        where id = #{id}
          and del_flag = '0'
    </select>

    <select id="selectIdAndNameByAll" resultType="cn.gwssi.ecloud.staffpool.dto.ProjectBySupplierDTO">
        select ID as id, APPLICATION_SYSTEM_NAME as projectName
        from uomp_application_system_management
        where SYSTEM_STATUS = '0'
          and DEL_FLAG = '0'
        order by CREATE_TIME desc
    </select>

    <select id="selectIdByApplicationSystemName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UOMP_APPLICATION_SYSTEM_MANAGEMENT
        where DEL_FLAG = '0'
        and SYSTEM_STATUS = '0' and APPLICATION_SYSTEM_NAME = #{applicationSystemName} limit 1
    </select>

    <!--  查询 除了 非启动状态的项目数据-->
    <select id="selectAllByRelationId" resultType="cn.gwssi.ecloud.staffpool.dto.ProjectBySupplierDTO">
        select t.ID                      as id,
        t.APPLICATION_SYSTEM_NAME as projectName,
        t.DEPART_ID               as departId,
        t.DEPART_NAME             as departName
        from UOMP_APPLICATION_SYSTEM_MANAGEMENT t
        where t.DEL_FLAG = '0' and t.SYSTEM_STATUS = '0'
        <if test="projectName != null and projectName != ''">
            and t.APPLICATION_SYSTEM_NAME like concat('%', #{projectName}, '%')
        </if>
        <if test="supplierId != null and supplierId != ''">
            and t.SUPPLIER_ID = #{supplierId}
        </if>
        order by t.CREATE_TIME desc
    </select>
</mapper>
