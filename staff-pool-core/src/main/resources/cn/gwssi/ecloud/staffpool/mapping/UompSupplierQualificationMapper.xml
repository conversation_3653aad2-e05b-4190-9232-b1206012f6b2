<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompSupplierQualificationMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification">
    <!--@mbg.generated-->
    <!--@Table uomp_supplier_qualification-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SUPPLIER_MANAGEMENT_ID" jdbcType="VARCHAR" property="supplierManagementId" />
    <result column="CERTIFICATE_NAME" jdbcType="VARCHAR" property="certificateName" />
    <result column="CERTIFICATE_NUM" jdbcType="VARCHAR" property="certificateNum" />
    <result column="ISSUING_AUTHORITY" jdbcType="VARCHAR" property="issuingAuthority" />
    <result column="ISSUING_DATE" jdbcType="VARCHAR" property="issuingDate" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
    <result column="FILES" jdbcType="VARCHAR" property="files" />
    <result column="STANDARDS" jdbcType="VARCHAR" property="standards" />
    <result column="STATEMENT" jdbcType="VARCHAR" property="statement" />
    <result column="BUSINESS_AREA" jdbcType="VARCHAR" property="businessArea" />
    <result column="ASSESSMENT_LEVEL" jdbcType="VARCHAR" property="assessmentLevel" />
    <result column="BUSINESS_LINE" jdbcType="VARCHAR" property="businessLine" />
    <result column="QUALIFICATION_LEVEL" jdbcType="VARCHAR" property="qualificationLevel" />
    <result column="TRIAL_AREA" jdbcType="VARCHAR" property="trialArea" />
    <result column="CERTIFICATION_SUB_ITEM" jdbcType="VARCHAR" property="certificationSubItem" />
    <result column="OTHER_NAME" jdbcType="VARCHAR" property="otherName" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SUPPLIER_MANAGEMENT_ID, CERTIFICATE_NAME, CERTIFICATE_NUM, ISSUING_AUTHORITY,
    ISSUING_DATE, END_TIME, FILES, STANDARDS, `STATEMENT`, BUSINESS_AREA, ASSESSMENT_LEVEL,
    BUSINESS_LINE, QUALIFICATION_LEVEL, TRIAL_AREA, CERTIFICATION_SUB_ITEM, OTHER_NAME,
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_supplier_qualification
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_supplier_qualification
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification">
    <!--@mbg.generated-->
    insert into uomp_supplier_qualification (ID, SUPPLIER_MANAGEMENT_ID, CERTIFICATE_NAME,
      CERTIFICATE_NUM, ISSUING_AUTHORITY, ISSUING_DATE,
      END_TIME, FILES, STANDARDS,
      `STATEMENT`, BUSINESS_AREA, ASSESSMENT_LEVEL,
      BUSINESS_LINE, QUALIFICATION_LEVEL, TRIAL_AREA,
      CERTIFICATION_SUB_ITEM, OTHER_NAME, CREATE_BY,
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{supplierManagementId,jdbcType=VARCHAR}, #{certificateName,jdbcType=VARCHAR},
      #{certificateNum,jdbcType=VARCHAR}, #{issuingAuthority,jdbcType=VARCHAR}, #{issuingDate,jdbcType=VARCHAR},
      #{endTime,jdbcType=VARCHAR}, #{files,jdbcType=VARCHAR}, #{standards,jdbcType=VARCHAR},
      #{statement,jdbcType=VARCHAR}, #{businessArea,jdbcType=VARCHAR}, #{assessmentLevel,jdbcType=VARCHAR},
      #{businessLine,jdbcType=VARCHAR}, #{qualificationLevel,jdbcType=VARCHAR}, #{trialArea,jdbcType=VARCHAR},
      #{certificationSubItem,jdbcType=VARCHAR}, #{otherName,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification">
    <!--@mbg.generated-->
    insert into uomp_supplier_qualification
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="supplierManagementId != null">
        SUPPLIER_MANAGEMENT_ID,
      </if>
      <if test="certificateName != null">
        CERTIFICATE_NAME,
      </if>
      <if test="certificateNum != null">
        CERTIFICATE_NUM,
      </if>
      <if test="issuingAuthority != null">
        ISSUING_AUTHORITY,
      </if>
      <if test="issuingDate != null">
        ISSUING_DATE,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="files != null">
        FILES,
      </if>
      <if test="standards != null">
        STANDARDS,
      </if>
      <if test="statement != null">
        `STATEMENT`,
      </if>
      <if test="businessArea != null">
        BUSINESS_AREA,
      </if>
      <if test="assessmentLevel != null">
        ASSESSMENT_LEVEL,
      </if>
      <if test="businessLine != null">
        BUSINESS_LINE,
      </if>
      <if test="qualificationLevel != null">
        QUALIFICATION_LEVEL,
      </if>
      <if test="trialArea != null">
        TRIAL_AREA,
      </if>
      <if test="certificationSubItem != null">
        CERTIFICATION_SUB_ITEM,
      </if>
      <if test="otherName != null">
        OTHER_NAME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="supplierManagementId != null">
        #{supplierManagementId,jdbcType=VARCHAR},
      </if>
      <if test="certificateName != null">
        #{certificateName,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        #{certificateNum,jdbcType=VARCHAR},
      </if>
      <if test="issuingAuthority != null">
        #{issuingAuthority,jdbcType=VARCHAR},
      </if>
      <if test="issuingDate != null">
        #{issuingDate,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="files != null">
        #{files,jdbcType=VARCHAR},
      </if>
      <if test="standards != null">
        #{standards,jdbcType=VARCHAR},
      </if>
      <if test="statement != null">
        #{statement,jdbcType=VARCHAR},
      </if>
      <if test="businessArea != null">
        #{businessArea,jdbcType=VARCHAR},
      </if>
      <if test="assessmentLevel != null">
        #{assessmentLevel,jdbcType=VARCHAR},
      </if>
      <if test="businessLine != null">
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="qualificationLevel != null">
        #{qualificationLevel,jdbcType=VARCHAR},
      </if>
      <if test="trialArea != null">
        #{trialArea,jdbcType=VARCHAR},
      </if>
      <if test="certificationSubItem != null">
        #{certificationSubItem,jdbcType=VARCHAR},
      </if>
      <if test="otherName != null">
        #{otherName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification">
    <!--@mbg.generated-->
    update uomp_supplier_qualification
    set SUPPLIER_MANAGEMENT_ID = #{supplierManagementId,jdbcType=VARCHAR},
      CERTIFICATE_NAME = #{certificateName,jdbcType=VARCHAR},
      CERTIFICATE_NUM = #{certificateNum,jdbcType=VARCHAR},
      ISSUING_AUTHORITY = #{issuingAuthority,jdbcType=VARCHAR},
      ISSUING_DATE = #{issuingDate,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=VARCHAR},
      FILES = #{files,jdbcType=VARCHAR},
      STANDARDS = #{standards,jdbcType=VARCHAR},
      `STATEMENT` = #{statement,jdbcType=VARCHAR},
      BUSINESS_AREA = #{businessArea,jdbcType=VARCHAR},
      ASSESSMENT_LEVEL = #{assessmentLevel,jdbcType=VARCHAR},
      BUSINESS_LINE = #{businessLine,jdbcType=VARCHAR},
      QUALIFICATION_LEVEL = #{qualificationLevel,jdbcType=VARCHAR},
      TRIAL_AREA = #{trialArea,jdbcType=VARCHAR},
      CERTIFICATION_SUB_ITEM = #{certificationSubItem,jdbcType=VARCHAR},
      OTHER_NAME = #{otherName,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from UOMP_SUPPLIER_QUALIFICATION
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="getQualificationList" resultType="cn.gwssi.ecloud.staffpool.dto.UompSupplierQualificationDto">
    select
    ID,
    SUPPLIER_MANAGEMENT_ID,
    CERTIFICATE_NAME,
    CERTIFICATE_NUM,
    ISSUING_AUTHORITY,
    ISSUING_DATE,
    END_TIME,
    FILES,
    STANDARDS,
    STATEMENT,
    BUSINESS_AREA,
    ASSESSMENT_LEVEL,
    BUSINESS_LINE,
    QUALIFICATION_LEVEL,
    TRIAL_AREA,
    CERTIFICATION_SUB_ITEM,
    OTHER_NAME
    from UOMP_SUPPLIER_QUALIFICATION
    where DEL_FLAG = '0' and SUPPLIER_MANAGEMENT_ID = #{supplierManagementId}
    order by CREATE_TIME desc
    </select>

  <update id="updateById" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification">
    update UOMP_SUPPLIER_QUALIFICATION
    <trim prefix="SET" suffixOverrides=",">
      <if test="certificateName !=null and certificateName !=''">
        CERTIFICATE_NAME = #{certificateName},
      </if>
      <if test="certificateNum !=null and certificateNum !=''">
        CERTIFICATE_NUM = #{certificateNum},
      </if>
      <if test="issuingAuthority !=null and issuingAuthority !=''">
        ISSUING_AUTHORITY = #{issuingAuthority},
      </if>
      <if test="issuingDate !=null and issuingDate !=''">
        ISSUING_DATE = #{issuingDate},
      </if>
      <if test="endTime !=null and endTime !=''">
        END_TIME = #{endTime},
      </if>
      <if test="files !=null and files !=''">
        FILES = #{files},
      </if>
      <if test="standards !=null and standards !=''">
        STANDARDS = #{standards},
      </if>
      <if test="statement !=null and statement !=''">
        STATEMENT = #{statement},
      </if>
      <if test="statement == null or statement ==''">
        STATEMENT = null,
      </if>
      <if test="businessArea !=null and businessArea !=''">
        BUSINESS_AREA = #{businessArea},
      </if>
      <if test="assessmentLevel !=null and assessmentLevel !=''">
        ASSESSMENT_LEVEL = #{assessmentLevel},
      </if>
      <if test="businessLine !=null and businessLine !=''">
        BUSINESS_LINE = #{businessLine},
      </if>
      <if test="qualificationLevel !=null and qualificationLevel !=''">
        QUALIFICATION_LEVEL = #{qualificationLevel},
      </if>
      <if test="trialArea !=null and trialArea !=''">
        TRIAL_AREA = #{trialArea},
      </if>
      <if test="certificationSubItem !=null and certificationSubItem !=''">
        CERTIFICATION_SUB_ITEM = #{certificationSubItem},
      </if>
      <if test="otherName !=null and otherName !=''">
        OTHER_NAME = #{otherName},
      </if>
      <if test="delFlag != null">DEL_FLAG = #{delFlag,jdbcType=VARCHAR},</if>
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
    </trim>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
