<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompFortressApiMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompFortressApi">
    <!--@mbg.generated-->
    <!--@Table uomp_fortress_api-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="FORT_ID" jdbcType="VARCHAR" property="fortId" />
    <result column="AUTH_WAY" jdbcType="VARCHAR" property="authWay" />
    <result column="AUTH_ACCOUNT" jdbcType="VARCHAR" property="authAccount" />
    <result column="AUTH_PASSWORD" jdbcType="VARCHAR" property="authPassword" />
    <result column="AUTH_URL" jdbcType="VARCHAR" property="authUrl" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, FORT_ID, AUTH_WAY, AUTH_ACCOUNT, AUTH_PASSWORD, AUTH_URL, CREATE_BY, CREATE_TIME, 
    UPDATE_BY, UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_fortress_api
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_fortress_api
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressApi">
    <!--@mbg.generated-->
    insert into uomp_fortress_api (ID, FORT_ID, AUTH_WAY, 
      AUTH_ACCOUNT, AUTH_PASSWORD, AUTH_URL, 
      CREATE_BY, CREATE_TIME, UPDATE_BY, 
      UPDATE_TIME, DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{fortId,jdbcType=VARCHAR}, #{authWay,jdbcType=VARCHAR}, 
      #{authAccount,jdbcType=VARCHAR}, #{authPassword,jdbcType=VARCHAR}, #{authUrl,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressApi">
    <!--@mbg.generated-->
    insert into uomp_fortress_api
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="fortId != null">
        FORT_ID,
      </if>
      <if test="authWay != null">
        AUTH_WAY,
      </if>
      <if test="authAccount != null">
        AUTH_ACCOUNT,
      </if>
      <if test="authPassword != null">
        AUTH_PASSWORD,
      </if>
      <if test="authUrl != null">
        AUTH_URL,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fortId != null">
        #{fortId,jdbcType=VARCHAR},
      </if>
      <if test="authWay != null">
        #{authWay,jdbcType=VARCHAR},
      </if>
      <if test="authAccount != null">
        #{authAccount,jdbcType=VARCHAR},
      </if>
      <if test="authPassword != null">
        #{authPassword,jdbcType=VARCHAR},
      </if>
      <if test="authUrl != null">
        #{authUrl,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompFortressApi">
    <!--@mbg.generated-->
    update uomp_fortress_api
    set FORT_ID = #{fortId,jdbcType=VARCHAR},
      AUTH_WAY = #{authWay,jdbcType=VARCHAR},
      AUTH_ACCOUNT = #{authAccount,jdbcType=VARCHAR},
      AUTH_PASSWORD = #{authPassword,jdbcType=VARCHAR},
      AUTH_URL = #{authUrl,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>