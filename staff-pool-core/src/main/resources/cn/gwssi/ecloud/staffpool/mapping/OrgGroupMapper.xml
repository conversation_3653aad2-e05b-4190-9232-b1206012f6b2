<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgGroupMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.OrgGroup">
    <!--@mbg.generated-->
    <!--@Table org_group-->
    <result column="id_" jdbcType="VARCHAR" property="id" />
    <result column="name_" jdbcType="VARCHAR" property="name" />
    <result column="parent_id_" jdbcType="VARCHAR" property="parentId" />
    <result column="sn_" jdbcType="INTEGER" property="sn" />
    <result column="code_" jdbcType="VARCHAR" property="code" />
    <result column="type_" jdbcType="VARCHAR" property="type" />
    <result column="desc_" jdbcType="VARCHAR" property="desc" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by_" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time_" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by_" jdbcType="VARCHAR" property="updateBy" />
    <result column="path_" jdbcType="VARCHAR" property="path" />
    <result column="simple_" jdbcType="VARCHAR" property="simple" />
    <result column="status_" jdbcType="INTEGER" property="status" />
    <result column="show_name_" jdbcType="VARCHAR" property="showName" />
    <result column="virtual_" jdbcType="INTEGER" property="virtual" />
    <result column="history_name_" jdbcType="VARCHAR" property="historyName" />
    <result column="path_name_" jdbcType="VARCHAR" property="pathName" />
    <result column="mcode_" jdbcType="VARCHAR" property="mcode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_, name_, parent_id_, sn_, code_, type_, desc_, create_time_, create_by_, update_time_,
    update_by_, path_, simple_, status_, show_name_, virtual_, history_name_, path_name_,
    mcode_
  </sql>
  <update id="updateById" parameterType="cn.gwssi.ecloud.staffpool.core.entity.OrgGroup">
    update
    ORG_GROUP
    set
    NAME_ = #{name}, CODE_ = #{code}
    where ID_ = #{id}
  </update>
  <select id="selectSn" resultMap="BaseResultMap">
    select max(SN_) from ORG_GROUP where (PARENT_ID_ is null or PARENT_ID_ = '0')
  </select>
</mapper>
