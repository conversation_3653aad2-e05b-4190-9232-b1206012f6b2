<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonJobMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    <!--@Table uomp_person_job-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="JOB_BEGIN_TIME" jdbcType="VARCHAR" property="jobBeginTime" />
    <result column="JOB_END_TIME" jdbcType="VARCHAR" property="jobEndTime" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="JOB_POSITION" jdbcType="VARCHAR" property="jobPosition" />
    <result column="JOB_DESCRIBE" jdbcType="VARCHAR" property="jobDescribe" />
    <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, JOB_BEGIN_TIME, JOB_END_TIME, COMPANY_NAME, JOB_POSITION, JOB_DESCRIBE,
    FILE_INFO, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
    DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_job
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="query" resultMap="BaseResultMap">
    select *
    from uomp_person_job
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
    <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_job
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPersonId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_job
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    insert into uomp_person_job (ID, PERSON_ID, JOB_BEGIN_TIME,
      JOB_END_TIME, COMPANY_NAME, JOB_POSITION,
      JOB_DESCRIBE, FILE_INFO, CREATE_BY,
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{jobBeginTime,jdbcType=VARCHAR},
      #{jobEndTime,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{jobPosition,jdbcType=VARCHAR},
      #{jobDescribe,jdbcType=VARCHAR}, #{fileInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    insert into uomp_person_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="jobBeginTime != null">
        JOB_BEGIN_TIME,
      </if>
      <if test="jobEndTime != null">
        JOB_END_TIME,
      </if>
      <if test="companyName != null">
        COMPANY_NAME,
      </if>
      <if test="jobPosition != null">
        JOB_POSITION,
      </if>
      <if test="jobDescribe != null">
        JOB_DESCRIBE,
      </if>
      <if test="fileInfo != null">
        FILE_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="jobBeginTime != null">
        #{jobBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="jobEndTime != null">
        #{jobEndTime,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="jobPosition != null">
        #{jobPosition,jdbcType=VARCHAR},
      </if>
      <if test="jobDescribe != null">
        #{jobDescribe,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    update uomp_person_job
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="jobBeginTime != null">
        JOB_BEGIN_TIME = #{jobBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="jobEndTime != null">
        JOB_END_TIME = #{jobEndTime,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="jobPosition != null">
        JOB_POSITION = #{jobPosition,jdbcType=VARCHAR},
      </if>
      <if test="jobDescribe != null">
        JOB_DESCRIBE = #{jobDescribe,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    update uomp_person_job
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      JOB_BEGIN_TIME = #{jobBeginTime,jdbcType=VARCHAR},
      JOB_END_TIME = #{jobEndTime,jdbcType=VARCHAR},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      JOB_POSITION = #{jobPosition,jdbcType=VARCHAR},
      JOB_DESCRIBE = #{jobDescribe,jdbcType=VARCHAR},
      FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPersonId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob">
    <!--@mbg.generated-->
    update uomp_person_job
    <set>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </update>
    <update id="updateByPersonIds">
      update uomp_person_job set
      DEL_FLAG = '1',
      UPDATE_ORG_ID = #{orgId},
      where PERSON_ID in
      <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </update>
  <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonJobDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="JOB_BEGIN_TIME" jdbcType="VARCHAR" property="jobBeginTime" />
    <result column="JOB_END_TIME" jdbcType="VARCHAR" property="jobEndTime" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="JOB_POSITION" jdbcType="VARCHAR" property="jobPosition" />
    <result column="JOB_DESCRIBE" jdbcType="VARCHAR" property="jobDescribe" />
    <result column="CRATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard" />
  </resultMap>
  <select id="selectByPersonIds" resultMap="BasePersonMap">
    select
      a.ID,
      a.PERSON_ID,
      a.JOB_BEGIN_TIME,
      a.JOB_END_TIME,
      a.COMPANY_NAME,
      a.JOB_POSITION,
      a.JOB_DESCRIBE,
      b.PERSON_NAME,
      b.PERSON_CARD
    from
      UOMP_PERSON_JOB a
        left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
    where a.DEL_FLAG = '0' and b.DEL_FLAG ='0' and a.PERSON_ID in
    <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by a.PERSON_ID desc,a.CREATE_TIME desc
  </select>
</mapper>
