<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompTrainingPlanPersonMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson">
    <!--@mbg.generated-->
    <!--@Table uomp_training_plan_person-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TRAINING_PLAN_ID" jdbcType="VARCHAR" property="trainingPlanId" />
    <result column="TRAINING_PERSON_ID" jdbcType="VARCHAR" property="trainingPersonId" />
    <result column="TRAINING_PERSON_NAME" jdbcType="VARCHAR" property="trainingPersonName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRAINING_PLAN_ID, TRAINING_PERSON_ID, TRAINING_PERSON_NAME
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_training_plan_person
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_training_plan_person
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPlanId">
    delete from uomp_training_plan_person
    where TRAINING_PLAN_ID = #{id}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson">
    <!--@mbg.generated-->
    insert into uomp_training_plan_person (ID, TRAINING_PLAN_ID, TRAINING_PERSON_ID,
      TRAINING_PERSON_NAME)
    values (#{id,jdbcType=VARCHAR}, #{trainingPlanId,jdbcType=VARCHAR}, #{trainingPersonId,jdbcType=VARCHAR},
      #{trainingPersonName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson">
    <!--@mbg.generated-->
    insert into uomp_training_plan_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="trainingPlanId != null">
        TRAINING_PLAN_ID,
      </if>
      <if test="trainingPersonId != null">
        TRAINING_PERSON_ID,
      </if>
      <if test="trainingPersonName != null">
        TRAINING_PERSON_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="trainingPlanId != null">
        #{trainingPlanId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonId != null">
        #{trainingPersonId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonName != null">
        #{trainingPersonName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson">
    <!--@mbg.generated-->
    update uomp_training_plan_person
    <set>
      <if test="trainingPlanId != null">
        TRAINING_PLAN_ID = #{trainingPlanId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonId != null">
        TRAINING_PERSON_ID = #{trainingPersonId,jdbcType=VARCHAR},
      </if>
      <if test="trainingPersonName != null">
        TRAINING_PERSON_NAME = #{trainingPersonName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson">
    <!--@mbg.generated-->
    update uomp_training_plan_person
    set TRAINING_PLAN_ID = #{trainingPlanId,jdbcType=VARCHAR},
      TRAINING_PERSON_ID = #{trainingPersonId,jdbcType=VARCHAR},
      TRAINING_PERSON_NAME = #{trainingPersonName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="query" resultMap="BaseResultMap">
    select *
    from uomp_training_plan_person
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>
