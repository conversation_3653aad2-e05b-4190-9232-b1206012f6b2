<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompReportMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompReport">
    <!--@mbg.generated-->
    <!--@Table uomp_report-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REPORT_NAME" jdbcType="VARCHAR" property="reportName" />
    <result column="REPORT_CODE" jdbcType="VARCHAR" property="reportCode" />
    <result column="REPORT_TYPE" jdbcType="VARCHAR" property="reportType" />
    <result column="REPORT_BEGIN" jdbcType="TIMESTAMP" property="reportBegin" />
    <result column="REPORT_END" jdbcType="TIMESTAMP" property="reportEnd" />
    <result column="APPLICATION_SYSTEM_MANAGEMENT_ID" jdbcType="VARCHAR" property="applicationSystemManagementId" />
    <result column="APPLICATION_SYSTEM_NAME" jdbcType="VARCHAR" property="applicationSystemName" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="UPLOAD_TIME" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="UPLOADER_ID" jdbcType="VARCHAR" property="uploaderId" />
    <result column="UPLOADER_NAME" jdbcType="VARCHAR" property="uploaderName" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, REPORT_NAME, REPORT_CODE, REPORT_TYPE, REPORT_BEGIN, REPORT_END, APPLICATION_SYSTEM_MANAGEMENT_ID,
    APPLICATION_SYSTEM_NAME, SUPPLIER_NAME, UPLOAD_TIME, UPLOADER_ID, UPLOADER_NAME, 
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_report
    where ID = #{id,jdbcType=VARCHAR} and DEL_FLAG = '0'
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_report
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReport">
    <!--@mbg.generated-->
    insert into uomp_report (ID, REPORT_NAME,
      REPORT_CODE, REPORT_TYPE, REPORT_BEGIN, 
      REPORT_END, APPLICATION_SYSTEM_MANAGEMENT_ID, 
      APPLICATION_SYSTEM_NAME, SUPPLIER_NAME, UPLOAD_TIME, 
      UPLOADER_ID, UPLOADER_NAME, CREATE_BY, 
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, 
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{reportName,jdbcType=VARCHAR},
      #{reportCode,jdbcType=VARCHAR}, #{reportType,jdbcType=VARCHAR}, #{reportBegin,jdbcType=TIMESTAMP}, 
      #{reportEnd,jdbcType=TIMESTAMP}, #{applicationSystemManagementId,jdbcType=VARCHAR}, 
      #{applicationSystemName,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{uploadTime,jdbcType=TIMESTAMP}, 
      #{uploaderId,jdbcType=VARCHAR}, #{uploaderName,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReport">
    <!--@mbg.generated-->
    insert into uomp_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="reportName != null">
        REPORT_NAME,
      </if>
      <if test="reportCode != null">
        REPORT_CODE,
      </if>
      <if test="reportType != null">
        REPORT_TYPE,
      </if>
      <if test="reportBegin != null">
        REPORT_BEGIN,
      </if>
      <if test="reportEnd != null">
        REPORT_END,
      </if>
      <if test="applicationSystemManagementId != null">
        APPLICATION_SYSTEM_MANAGEMENT_ID,
      </if>
      <if test="applicationSystemName != null">
        APPLICATION_SYSTEM_NAME,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="uploadTime != null">
        UPLOAD_TIME,
      </if>
      <if test="uploaderId != null">
        UPLOADER_ID,
      </if>
      <if test="uploaderName != null">
        UPLOADER_NAME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="reportName != null">
        #{reportName,jdbcType=VARCHAR},
      </if>
      <if test="reportCode != null">
        #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null">
        #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="reportBegin != null">
        #{reportBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="reportEnd != null">
        #{reportEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="applicationSystemManagementId != null">
        #{applicationSystemManagementId,jdbcType=VARCHAR},
      </if>
      <if test="applicationSystemName != null">
        #{applicationSystemName,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uploaderId != null">
        #{uploaderId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderName != null">
        #{uploaderName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReport">
    <!--@mbg.generated-->
    update uomp_report
    <set>
      <if test="reportName != null">
        REPORT_NAME = #{reportName,jdbcType=VARCHAR},
      </if>
      <if test="reportCode != null">
        REPORT_CODE = #{reportCode,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null">
        REPORT_TYPE = #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="reportBegin != null">
        REPORT_BEGIN = #{reportBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="reportEnd != null">
        REPORT_END = #{reportEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="applicationSystemManagementId != null">
        APPLICATION_SYSTEM_MANAGEMENT_ID = #{applicationSystemManagementId,jdbcType=VARCHAR},
      </if>
      <if test="applicationSystemName != null">
        APPLICATION_SYSTEM_NAME = #{applicationSystemName,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        UPLOAD_TIME = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uploaderId != null">
        UPLOADER_ID = #{uploaderId,jdbcType=VARCHAR},
      </if>
      <if test="uploaderName != null">
        UPLOADER_NAME = #{uploaderName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompReport">
    <!--@mbg.generated-->
    update uomp_report
    set REPORT_NAME = #{reportName,jdbcType=VARCHAR},
      REPORT_CODE = #{reportCode,jdbcType=VARCHAR},
      REPORT_TYPE = #{reportType,jdbcType=VARCHAR},
      REPORT_BEGIN = #{reportBegin,jdbcType=TIMESTAMP},
      REPORT_END = #{reportEnd,jdbcType=TIMESTAMP},
      APPLICATION_SYSTEM_MANAGEMENT_ID = #{applicationSystemManagementId,jdbcType=VARCHAR},
      APPLICATION_SYSTEM_NAME = #{applicationSystemName,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      UPLOAD_TIME = #{uploadTime,jdbcType=TIMESTAMP},
      UPLOADER_ID = #{uploaderId,jdbcType=VARCHAR},
      UPLOADER_NAME = #{uploaderName,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="query" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_report
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>