<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompWorkOrdersMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
    <!--@mbg.generated-->
    <!--@Table uomp_work_order-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REQUEST_ID" jdbcType="VARCHAR" property="requestId" />
    <result column="ORDER_TYPE" jdbcType="VARCHAR" property="orderType" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="ORDER_LEVEL" jdbcType="VARCHAR" property="orderLevel" />
    <result column="ORDER_TITLE" jdbcType="VARCHAR" property="orderTitle" />
    <result column="ORDER_STATE" jdbcType="VARCHAR" property="orderState" />
    <result column="SERVICE_TYPE" jdbcType="VARCHAR" property="serviceType" />
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
    <result column="HANDLER" jdbcType="VARCHAR" property="handler" />
    <result column="HANDLER_ID" jdbcType="VARCHAR" property="handlerId" />
    <result column="HANDLER_GROUP" jdbcType="VARCHAR" property="handlerGroup" />
    <result column="HANDLER_GROUP_ID" jdbcType="VARCHAR" property="handlerGroupId" />
    <result column="APPLICANT" jdbcType="VARCHAR" property="applicant" />
    <result column="APPLICANT_TEL" jdbcType="VARCHAR" property="applicantTel" />
    <result column="LINK_START_TIME" jdbcType="TIMESTAMP" property="linkStartTime" />
    <result column="FLOW_START_TIME" jdbcType="TIMESTAMP" property="flowStartTime" />
    <result column="LINK_OVER_TIME" jdbcType="TIMESTAMP" property="linkOverTime" />
    <result column="FLOW_OVER_TIME" jdbcType="TIMESTAMP" property="flowOverTime" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
    <result column="FINISH_TIME" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="IF_PAUSE" jdbcType="DECIMAL" property="ifPause" />
    <result column="IF_FEEDBACK" jdbcType="VARCHAR" property="ifFeedback" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="REGISTRANT" jdbcType="VARCHAR" property="registrant" />
    <result column="REGISTRANT_ID" jdbcType="VARCHAR" property="registrantId" />
    <result column="IF_SPLIT_CHILD" jdbcType="VARCHAR" property="ifSplitChild" />
    <result column="TIME_LIMIT" jdbcType="DECIMAL" property="timeLimit" />
    <result column="PAUSE_TIME" jdbcType="TIMESTAMP" property="pauseTime" />
    <result column="PAUSE_SUM" jdbcType="DECIMAL" property="pauseSum" />
    <result column="REMIND_LIMIT" jdbcType="DECIMAL" property="remindLimit" />
    <result column="PARENT_INST_ID" jdbcType="VARCHAR" property="parentInstId" />
    <result column="PROJECT_ID" jdbcType="VARCHAR" property="projectId" />
    <result column="IF_VIP" jdbcType="VARCHAR" property="ifVip" />
    <result column="MODIFY_RECORD" jdbcType="VARCHAR" property="modifyRecord" />
    <result column="RESTART" jdbcType="DECIMAL" property="restart" />
    <result column="BUS_ID" jdbcType="VARCHAR" property="busId" />
    <result column="IF_FROM_API" jdbcType="VARCHAR" property="ifFromApi" />
    <result column="RESPONSE_STATUS" jdbcType="VARCHAR" property="responseStatus" />
    <result column="ORDER_KIND" jdbcType="VARCHAR" property="orderKind" />
    <result column="APPLICANT_ORG_NAME" jdbcType="VARCHAR" property="applicantOrgName" />
    <result column="APPLICANT_ORG_ID" jdbcType="VARCHAR" property="applicantOrgId" />
    <result column="ORDER_DESC" jdbcType="VARCHAR" property="orderDesc" />
    <result column="APPLICANT_ROOM" jdbcType="VARCHAR" property="applicantRoom" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, REQUEST_ID, ORDER_TYPE, ORDER_NO, ORDER_LEVEL, ORDER_TITLE, ORDER_STATE, SERVICE_TYPE, 
    RESOURCE_ID, `HANDLER`, HANDLER_ID, HANDLER_GROUP, HANDLER_GROUP_ID, APPLICANT, APPLICANT_TEL, 
    LINK_START_TIME, FLOW_START_TIME, LINK_OVER_TIME, FLOW_OVER_TIME, INST_ID, FINISH_TIME, 
    IF_PAUSE, IF_FEEDBACK, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
    DEL_FLAG, REGISTRANT, REGISTRANT_ID, IF_SPLIT_CHILD, TIME_LIMIT, PAUSE_TIME, PAUSE_SUM, 
    REMIND_LIMIT, PARENT_INST_ID, PROJECT_ID, IF_VIP, MODIFY_RECORD, `RESTART`, BUS_ID, 
    IF_FROM_API, RESPONSE_STATUS, ORDER_KIND, APPLICANT_ORG_NAME, APPLICANT_ORG_ID, ORDER_DESC, 
    APPLICANT_ROOM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_work_order
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_work_order
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
    <!--@mbg.generated-->
    insert into uomp_work_order (ID, REQUEST_ID, ORDER_TYPE, 
      ORDER_NO, ORDER_LEVEL, ORDER_TITLE, 
      ORDER_STATE, SERVICE_TYPE, RESOURCE_ID, 
      `HANDLER`, HANDLER_ID, HANDLER_GROUP, 
      HANDLER_GROUP_ID, APPLICANT, APPLICANT_TEL, 
      LINK_START_TIME, FLOW_START_TIME, LINK_OVER_TIME, 
      FLOW_OVER_TIME, INST_ID, FINISH_TIME, 
      IF_PAUSE, IF_FEEDBACK, CREATE_BY, 
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, 
      UPDATE_TIME, DEL_FLAG, REGISTRANT, 
      REGISTRANT_ID, IF_SPLIT_CHILD, TIME_LIMIT, 
      PAUSE_TIME, PAUSE_SUM, REMIND_LIMIT, 
      PARENT_INST_ID, PROJECT_ID, IF_VIP, 
      MODIFY_RECORD, `RESTART`, BUS_ID, 
      IF_FROM_API, RESPONSE_STATUS, ORDER_KIND, 
      APPLICANT_ORG_NAME, APPLICANT_ORG_ID, ORDER_DESC, 
      APPLICANT_ROOM)
    values (#{id,jdbcType=VARCHAR}, #{requestId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderLevel,jdbcType=VARCHAR}, #{orderTitle,jdbcType=VARCHAR}, 
      #{orderState,jdbcType=VARCHAR}, #{serviceType,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, 
      #{handler,jdbcType=VARCHAR}, #{handlerId,jdbcType=VARCHAR}, #{handlerGroup,jdbcType=VARCHAR}, 
      #{handlerGroupId,jdbcType=VARCHAR}, #{applicant,jdbcType=VARCHAR}, #{applicantTel,jdbcType=VARCHAR}, 
      #{linkStartTime,jdbcType=TIMESTAMP}, #{flowStartTime,jdbcType=TIMESTAMP}, #{linkOverTime,jdbcType=TIMESTAMP}, 
      #{flowOverTime,jdbcType=TIMESTAMP}, #{instId,jdbcType=VARCHAR}, #{finishTime,jdbcType=TIMESTAMP}, 
      #{ifPause,jdbcType=DECIMAL}, #{ifFeedback,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}, #{registrant,jdbcType=VARCHAR}, 
      #{registrantId,jdbcType=VARCHAR}, #{ifSplitChild,jdbcType=VARCHAR}, #{timeLimit,jdbcType=DECIMAL}, 
      #{pauseTime,jdbcType=TIMESTAMP}, #{pauseSum,jdbcType=DECIMAL}, #{remindLimit,jdbcType=DECIMAL}, 
      #{parentInstId,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{ifVip,jdbcType=VARCHAR}, 
      #{modifyRecord,jdbcType=VARCHAR}, #{restart,jdbcType=DECIMAL}, #{busId,jdbcType=VARCHAR}, 
      #{ifFromApi,jdbcType=VARCHAR}, #{responseStatus,jdbcType=VARCHAR}, #{orderKind,jdbcType=VARCHAR}, 
      #{applicantOrgName,jdbcType=VARCHAR}, #{applicantOrgId,jdbcType=VARCHAR}, #{orderDesc,jdbcType=VARCHAR}, 
      #{applicantRoom,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
    <!--@mbg.generated-->
    insert into uomp_work_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="requestId != null">
        REQUEST_ID,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="orderLevel != null">
        ORDER_LEVEL,
      </if>
      <if test="orderTitle != null">
        ORDER_TITLE,
      </if>
      <if test="orderState != null">
        ORDER_STATE,
      </if>
      <if test="serviceType != null">
        SERVICE_TYPE,
      </if>
      <if test="resourceId != null">
        RESOURCE_ID,
      </if>
      <if test="handler != null">
        `HANDLER`,
      </if>
      <if test="handlerId != null">
        HANDLER_ID,
      </if>
      <if test="handlerGroup != null">
        HANDLER_GROUP,
      </if>
      <if test="handlerGroupId != null">
        HANDLER_GROUP_ID,
      </if>
      <if test="applicant != null">
        APPLICANT,
      </if>
      <if test="applicantTel != null">
        APPLICANT_TEL,
      </if>
      <if test="linkStartTime != null">
        LINK_START_TIME,
      </if>
      <if test="flowStartTime != null">
        FLOW_START_TIME,
      </if>
      <if test="linkOverTime != null">
        LINK_OVER_TIME,
      </if>
      <if test="flowOverTime != null">
        FLOW_OVER_TIME,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
      <if test="finishTime != null">
        FINISH_TIME,
      </if>
      <if test="ifPause != null">
        IF_PAUSE,
      </if>
      <if test="ifFeedback != null">
        IF_FEEDBACK,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="registrant != null">
        REGISTRANT,
      </if>
      <if test="registrantId != null">
        REGISTRANT_ID,
      </if>
      <if test="ifSplitChild != null">
        IF_SPLIT_CHILD,
      </if>
      <if test="timeLimit != null">
        TIME_LIMIT,
      </if>
      <if test="pauseTime != null">
        PAUSE_TIME,
      </if>
      <if test="pauseSum != null">
        PAUSE_SUM,
      </if>
      <if test="remindLimit != null">
        REMIND_LIMIT,
      </if>
      <if test="parentInstId != null">
        PARENT_INST_ID,
      </if>
      <if test="projectId != null">
        PROJECT_ID,
      </if>
      <if test="ifVip != null">
        IF_VIP,
      </if>
      <if test="modifyRecord != null">
        MODIFY_RECORD,
      </if>
      <if test="restart != null">
        `RESTART`,
      </if>
      <if test="busId != null">
        BUS_ID,
      </if>
      <if test="ifFromApi != null">
        IF_FROM_API,
      </if>
      <if test="responseStatus != null">
        RESPONSE_STATUS,
      </if>
      <if test="orderKind != null">
        ORDER_KIND,
      </if>
      <if test="applicantOrgName != null">
        APPLICANT_ORG_NAME,
      </if>
      <if test="applicantOrgId != null">
        APPLICANT_ORG_ID,
      </if>
      <if test="orderDesc != null">
        ORDER_DESC,
      </if>
      <if test="applicantRoom != null">
        APPLICANT_ROOM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderLevel != null">
        #{orderLevel,jdbcType=VARCHAR},
      </if>
      <if test="orderTitle != null">
        #{orderTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderState != null">
        #{orderState,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="handler != null">
        #{handler,jdbcType=VARCHAR},
      </if>
      <if test="handlerId != null">
        #{handlerId,jdbcType=VARCHAR},
      </if>
      <if test="handlerGroup != null">
        #{handlerGroup,jdbcType=VARCHAR},
      </if>
      <if test="handlerGroupId != null">
        #{handlerGroupId,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="applicantTel != null">
        #{applicantTel,jdbcType=VARCHAR},
      </if>
      <if test="linkStartTime != null">
        #{linkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowStartTime != null">
        #{flowStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkOverTime != null">
        #{linkOverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowOverTime != null">
        #{flowOverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifPause != null">
        #{ifPause,jdbcType=DECIMAL},
      </if>
      <if test="ifFeedback != null">
        #{ifFeedback,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="registrant != null">
        #{registrant,jdbcType=VARCHAR},
      </if>
      <if test="registrantId != null">
        #{registrantId,jdbcType=VARCHAR},
      </if>
      <if test="ifSplitChild != null">
        #{ifSplitChild,jdbcType=VARCHAR},
      </if>
      <if test="timeLimit != null">
        #{timeLimit,jdbcType=DECIMAL},
      </if>
      <if test="pauseTime != null">
        #{pauseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pauseSum != null">
        #{pauseSum,jdbcType=DECIMAL},
      </if>
      <if test="remindLimit != null">
        #{remindLimit,jdbcType=DECIMAL},
      </if>
      <if test="parentInstId != null">
        #{parentInstId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="ifVip != null">
        #{ifVip,jdbcType=VARCHAR},
      </if>
      <if test="modifyRecord != null">
        #{modifyRecord,jdbcType=VARCHAR},
      </if>
      <if test="restart != null">
        #{restart,jdbcType=DECIMAL},
      </if>
      <if test="busId != null">
        #{busId,jdbcType=VARCHAR},
      </if>
      <if test="ifFromApi != null">
        #{ifFromApi,jdbcType=VARCHAR},
      </if>
      <if test="responseStatus != null">
        #{responseStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderKind != null">
        #{orderKind,jdbcType=VARCHAR},
      </if>
      <if test="applicantOrgName != null">
        #{applicantOrgName,jdbcType=VARCHAR},
      </if>
      <if test="applicantOrgId != null">
        #{applicantOrgId,jdbcType=VARCHAR},
      </if>
      <if test="orderDesc != null">
        #{orderDesc,jdbcType=VARCHAR},
      </if>
      <if test="applicantRoom != null">
        #{applicantRoom,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
    <!--@mbg.generated-->
    update uomp_work_order
    <set>
      <if test="requestId != null">
        REQUEST_ID = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderLevel != null">
        ORDER_LEVEL = #{orderLevel,jdbcType=VARCHAR},
      </if>
      <if test="orderTitle != null">
        ORDER_TITLE = #{orderTitle,jdbcType=VARCHAR},
      </if>
      <if test="orderState != null">
        ORDER_STATE = #{orderState,jdbcType=VARCHAR},
      </if>
      <if test="serviceType != null">
        SERVICE_TYPE = #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="handler != null">
        `HANDLER` = #{handler,jdbcType=VARCHAR},
      </if>
      <if test="handlerId != null">
        HANDLER_ID = #{handlerId,jdbcType=VARCHAR},
      </if>
      <if test="handlerGroup != null">
        HANDLER_GROUP = #{handlerGroup,jdbcType=VARCHAR},
      </if>
      <if test="handlerGroupId != null">
        HANDLER_GROUP_ID = #{handlerGroupId,jdbcType=VARCHAR},
      </if>
      <if test="applicant != null">
        APPLICANT = #{applicant,jdbcType=VARCHAR},
      </if>
      <if test="applicantTel != null">
        APPLICANT_TEL = #{applicantTel,jdbcType=VARCHAR},
      </if>
      <if test="linkStartTime != null">
        LINK_START_TIME = #{linkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowStartTime != null">
        FLOW_START_TIME = #{flowStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkOverTime != null">
        LINK_OVER_TIME = #{linkOverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flowOverTime != null">
        FLOW_OVER_TIME = #{flowOverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="instId != null">
        INST_ID = #{instId,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ifPause != null">
        IF_PAUSE = #{ifPause,jdbcType=DECIMAL},
      </if>
      <if test="ifFeedback != null">
        IF_FEEDBACK = #{ifFeedback,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="registrant != null">
        REGISTRANT = #{registrant,jdbcType=VARCHAR},
      </if>
      <if test="registrantId != null">
        REGISTRANT_ID = #{registrantId,jdbcType=VARCHAR},
      </if>
      <if test="ifSplitChild != null">
        IF_SPLIT_CHILD = #{ifSplitChild,jdbcType=VARCHAR},
      </if>
      <if test="timeLimit != null">
        TIME_LIMIT = #{timeLimit,jdbcType=DECIMAL},
      </if>
      <if test="pauseTime != null">
        PAUSE_TIME = #{pauseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pauseSum != null">
        PAUSE_SUM = #{pauseSum,jdbcType=DECIMAL},
      </if>
      <if test="remindLimit != null">
        REMIND_LIMIT = #{remindLimit,jdbcType=DECIMAL},
      </if>
      <if test="parentInstId != null">
        PARENT_INST_ID = #{parentInstId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        PROJECT_ID = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="ifVip != null">
        IF_VIP = #{ifVip,jdbcType=VARCHAR},
      </if>
      <if test="modifyRecord != null">
        MODIFY_RECORD = #{modifyRecord,jdbcType=VARCHAR},
      </if>
      <if test="restart != null">
        `RESTART` = #{restart,jdbcType=DECIMAL},
      </if>
      <if test="busId != null">
        BUS_ID = #{busId,jdbcType=VARCHAR},
      </if>
      <if test="ifFromApi != null">
        IF_FROM_API = #{ifFromApi,jdbcType=VARCHAR},
      </if>
      <if test="responseStatus != null">
        RESPONSE_STATUS = #{responseStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderKind != null">
        ORDER_KIND = #{orderKind,jdbcType=VARCHAR},
      </if>
      <if test="applicantOrgName != null">
        APPLICANT_ORG_NAME = #{applicantOrgName,jdbcType=VARCHAR},
      </if>
      <if test="applicantOrgId != null">
        APPLICANT_ORG_ID = #{applicantOrgId,jdbcType=VARCHAR},
      </if>
      <if test="orderDesc != null">
        ORDER_DESC = #{orderDesc,jdbcType=VARCHAR},
      </if>
      <if test="applicantRoom != null">
        APPLICANT_ROOM = #{applicantRoom,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
    <!--@mbg.generated-->
    update uomp_work_order
    set REQUEST_ID = #{requestId,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      ORDER_LEVEL = #{orderLevel,jdbcType=VARCHAR},
      ORDER_TITLE = #{orderTitle,jdbcType=VARCHAR},
      ORDER_STATE = #{orderState,jdbcType=VARCHAR},
      SERVICE_TYPE = #{serviceType,jdbcType=VARCHAR},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      `HANDLER` = #{handler,jdbcType=VARCHAR},
      HANDLER_ID = #{handlerId,jdbcType=VARCHAR},
      HANDLER_GROUP = #{handlerGroup,jdbcType=VARCHAR},
      HANDLER_GROUP_ID = #{handlerGroupId,jdbcType=VARCHAR},
      APPLICANT = #{applicant,jdbcType=VARCHAR},
      APPLICANT_TEL = #{applicantTel,jdbcType=VARCHAR},
      LINK_START_TIME = #{linkStartTime,jdbcType=TIMESTAMP},
      FLOW_START_TIME = #{flowStartTime,jdbcType=TIMESTAMP},
      LINK_OVER_TIME = #{linkOverTime,jdbcType=TIMESTAMP},
      FLOW_OVER_TIME = #{flowOverTime,jdbcType=TIMESTAMP},
      INST_ID = #{instId,jdbcType=VARCHAR},
      FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP},
      IF_PAUSE = #{ifPause,jdbcType=DECIMAL},
      IF_FEEDBACK = #{ifFeedback,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      REGISTRANT = #{registrant,jdbcType=VARCHAR},
      REGISTRANT_ID = #{registrantId,jdbcType=VARCHAR},
      IF_SPLIT_CHILD = #{ifSplitChild,jdbcType=VARCHAR},
      TIME_LIMIT = #{timeLimit,jdbcType=DECIMAL},
      PAUSE_TIME = #{pauseTime,jdbcType=TIMESTAMP},
      PAUSE_SUM = #{pauseSum,jdbcType=DECIMAL},
      REMIND_LIMIT = #{remindLimit,jdbcType=DECIMAL},
      PARENT_INST_ID = #{parentInstId,jdbcType=VARCHAR},
      PROJECT_ID = #{projectId,jdbcType=VARCHAR},
      IF_VIP = #{ifVip,jdbcType=VARCHAR},
      MODIFY_RECORD = #{modifyRecord,jdbcType=VARCHAR},
      `RESTART` = #{restart,jdbcType=DECIMAL},
      BUS_ID = #{busId,jdbcType=VARCHAR},
      IF_FROM_API = #{ifFromApi,jdbcType=VARCHAR},
      RESPONSE_STATUS = #{responseStatus,jdbcType=VARCHAR},
      ORDER_KIND = #{orderKind,jdbcType=VARCHAR},
      APPLICANT_ORG_NAME = #{applicantOrgName,jdbcType=VARCHAR},
      APPLICANT_ORG_ID = #{applicantOrgId,jdbcType=VARCHAR},
      ORDER_DESC = #{orderDesc,jdbcType=VARCHAR},
      APPLICANT_ROOM = #{applicantRoom,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getWorkOrderList" resultType="cn.gwssi.ecloud.staffpool.core.entity.UompWorkOrder">
        select
          id,
          request_id as requestId,
          order_level as order_level,
          order_type as orderType,
          case
              when order_state = '0' then '暂存'
              when order_state = '1' then '处理中'
              when order_state = '2' then '搁置中'
              when order_state = '5' then '处理完成'
              when order_state = '9' then '已关闭'
              when ORDER_STATE = '3' then '未指派'
              when ORDER_STATE = '4' then '已取消'
              else order_state end as orderState,
          order_no as orderNo,
          order_title as orderTitle,
          ifnull(handler,handler_group) as handler,
          create_time as createTime,
          inst_id as instId
        from uomp_work_order
        <where>
          <if test="whereSql!=null">
            ${whereSql}
          </if>
          and instr(project_id ,#{projectManagementId})>0
          <if test="orderTitle != null">
            and instr(order_title, #{orderTitle})>0
          </if>
          <if test="orderNo != null">
            and instr(order_no, #{orderNo})>0
          </if>
        </where>
        <if test="orderBySql!=null">
          ORDER BY ${orderBySql}
        </if>
  </select>
</mapper>