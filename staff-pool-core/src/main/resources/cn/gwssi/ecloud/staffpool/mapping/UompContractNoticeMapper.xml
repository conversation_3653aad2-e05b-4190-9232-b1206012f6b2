<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompContractNoticeMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice">
    <!--@mbg.generated-->
    <!--@Table uomp_contract_notice-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONTRACT_MANAGEMENT_ID" jdbcType="VARCHAR" property="contractManagementId" />
    <result column="NOTICE_NUM" jdbcType="INTEGER" property="noticeNum" />
    <result column="QUALITY_BEGIN_DAY" jdbcType="TIMESTAMP" property="qualityBeginDay" />
    <result column="QUALITY_END_DAY" jdbcType="TIMESTAMP" property="qualityEndDay" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONTRACT_MANAGEMENT_ID, NOTICE_NUM, QUALITY_BEGIN_DAY, QUALITY_END_DAY, CREATE_TIME, 
    UPDATE_TIME, DEL_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_contract_notice
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_contract_notice
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice">
    <!--@mbg.generated-->
    insert into uomp_contract_notice (ID, CONTRACT_MANAGEMENT_ID, NOTICE_NUM,
      QUALITY_BEGIN_DAY, QUALITY_END_DAY, CREATE_TIME, 
      UPDATE_TIME, DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{contractManagementId,jdbcType=VARCHAR}, #{noticeNum,jdbcType=SMALLINT}, 
      #{qualityBeginDay,jdbcType=TIMESTAMP}, #{qualityEndDay,jdbcType=TIMESTAMP}, now(),
      now(), #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice">
    <!--@mbg.generated-->
    insert into uomp_contract_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="contractManagementId != null">
        CONTRACT_MANAGEMENT_ID,
      </if>
      <if test="noticeNum != null">
        NOTICE_NUM,
      </if>
      <if test="qualityBeginDay != null">
        QUALITY_BEGIN_DAY,
      </if>
      <if test="qualityEndDay != null">
        QUALITY_END_DAY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractManagementId != null">
        #{contractManagementId,jdbcType=VARCHAR},
      </if>
      <if test="noticeNum != null">
        #{noticeNum,jdbcType=SMALLINT},
      </if>
      <if test="qualityBeginDay != null">
        #{qualityBeginDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityEndDay != null">
        #{qualityEndDay,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice">
    <!--@mbg.generated-->
    update uomp_contract_notice
    <set>
      <if test="contractManagementId != null">
        CONTRACT_MANAGEMENT_ID = #{contractManagementId,jdbcType=VARCHAR},
      </if>
      <if test="noticeNum != null">
        NOTICE_NUM = #{noticeNum,jdbcType=SMALLINT},
      </if>
      <if test="qualityBeginDay != null">
        QUALITY_BEGIN_DAY = #{qualityBeginDay,jdbcType=TIMESTAMP},
      </if>
      <if test="qualityEndDay != null">
        QUALITY_END_DAY = #{qualityEndDay,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice">
    <!--@mbg.generated-->
    update uomp_contract_notice
    set CONTRACT_MANAGEMENT_ID = #{contractManagementId,jdbcType=VARCHAR},
      NOTICE_NUM = #{noticeNum,jdbcType=SMALLINT},
      QUALITY_BEGIN_DAY = #{qualityBeginDay,jdbcType=TIMESTAMP},
      QUALITY_END_DAY = #{qualityEndDay,jdbcType=TIMESTAMP},
      UPDATE_TIME = now()
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getListByContractIds" resultMap="BaseResultMap">
     select * from uomp_contract_notice
     where DEL_FLAG = '0'
     and CONTRACT_MANAGEMENT_ID in
    <foreach collection="contractIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>