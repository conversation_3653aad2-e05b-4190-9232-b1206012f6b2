<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.CmdbCommResourceMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource">
    <!--@mbg.generated-->
    <!--@Table cmdb_comm_resource-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
    <result column="BASELINE_ID" jdbcType="VARCHAR" property="baselineId" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="RESOURCE_NO" jdbcType="VARCHAR" property="resourceNo" />
    <result column="RESOURCE_NAME" jdbcType="VARCHAR" property="resourceName" />
    <result column="DEVICE_MODEL" jdbcType="VARCHAR" property="deviceModel" />
    <result column="RESOURCE_STATE" jdbcType="VARCHAR" property="resourceState" />
    <result column="PRODUCER" jdbcType="VARCHAR" property="producer" />
    <result column="DEVICE_DUTYOR" jdbcType="VARCHAR" property="deviceDutyor" />
    <result column="TEND_DATE_START" jdbcType="DATE" property="tendDateStart" />
    <result column="TEND_DATE_END" jdbcType="DATE" property="tendDateEnd" />
    <result column="DEVICE_UNIT_NAME" jdbcType="VARCHAR" property="deviceUnitName" />
    <result column="DEVICE_UNIT_ID" jdbcType="VARCHAR" property="deviceUnitId" />
    <result column="SUPPLIER_ID" jdbcType="VARCHAR" property="supplierId" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId" />
    <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName" />
    <result column="PROJECT_ID" jdbcType="VARCHAR" property="projectId" />
    <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
    <result column="DEVICE_STATE" jdbcType="VARCHAR" property="deviceState" />
    <result column="IP_ADDR" jdbcType="VARCHAR" property="ipAddr" />
    <result column="SUBMIT_TIME" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="AUDIT_STATE" jdbcType="VARCHAR" property="auditState" />
    <result column="AUDIT_RESULT" jdbcType="VARCHAR" property="auditResult" />
    <result column="AUDIT_END_TIME" jdbcType="TIMESTAMP" property="auditEndTime" />
    <result column="AUDITER" jdbcType="VARCHAR" property="auditer" />
    <result column="AUDITERID" jdbcType="VARCHAR" property="auditerid" />
    <result column="IF_NOTICE" jdbcType="VARCHAR" property="ifNotice" />
    <result column="NOTICE_TIME" jdbcType="TIMESTAMP" property="noticeTime" />
    <result column="FILE_STR" jdbcType="VARCHAR" property="fileStr" />
    <result column="AUDIT_OPIN" jdbcType="VARCHAR" property="auditOpin" />
    <result column="DEVICE_PRICE" jdbcType="DECIMAL" property="devicePrice" />
    <result column="OTHER_COLS" jdbcType="VARCHAR" property="otherCols" />
    <result column="METADATA1" jdbcType="VARCHAR" property="metadata1" />
    <result column="METADATA2" jdbcType="VARCHAR" property="metadata2" />
    <result column="METADATA3" jdbcType="VARCHAR" property="metadata3" />
    <result column="METADATA4" jdbcType="VARCHAR" property="metadata4" />
    <result column="METADATA5" jdbcType="VARCHAR" property="metadata5" />
    <result column="EXTENDS1" jdbcType="VARCHAR" property="extends1" />
    <result column="EXTENDS2" jdbcType="VARCHAR" property="extends2" />
    <result column="EXTENDS3" jdbcType="VARCHAR" property="extends3" />
    <result column="EXTENDS4" jdbcType="VARCHAR" property="extends4" />
    <result column="EXTENDS5" jdbcType="VARCHAR" property="extends5" />
    <result column="EXTENDS6" jdbcType="VARCHAR" property="extends6" />
    <result column="EXTENDS7" jdbcType="VARCHAR" property="extends7" />
    <result column="EXTENDS8" jdbcType="VARCHAR" property="extends8" />
    <result column="EXTENDS9" jdbcType="VARCHAR" property="extends9" />
    <result column="EXTENDS10" jdbcType="VARCHAR" property="extends10" />
    <result column="EXTENDS11" jdbcType="VARCHAR" property="extends11" />
    <result column="EXTENDS12" jdbcType="VARCHAR" property="extends12" />
    <result column="EXTENDS13" jdbcType="VARCHAR" property="extends13" />
    <result column="EXTENDS14" jdbcType="VARCHAR" property="extends14" />
    <result column="EXTENDS15" jdbcType="VARCHAR" property="extends15" />
    <result column="EXTENDS16" jdbcType="VARCHAR" property="extends16" />
    <result column="EXTENDS17" jdbcType="VARCHAR" property="extends17" />
    <result column="EXTENDS18" jdbcType="VARCHAR" property="extends18" />
    <result column="EXTENDS19" jdbcType="VARCHAR" property="extends19" />
    <result column="EXTENDS20" jdbcType="VARCHAR" property="extends20" />
    <result column="EXTENDS_DATE1" jdbcType="DATE" property="extendsDate1" />
    <result column="EXTENDS_DATE2" jdbcType="DATE" property="extendsDate2" />
    <result column="EXTENDS_DATE3" jdbcType="DATE" property="extendsDate3" />
    <result column="EXTENDS_DATE4" jdbcType="DATE" property="extendsDate4" />
    <result column="EXTENDS_DATE5" jdbcType="DATE" property="extendsDate5" />
    <result column="EXTENDS_INT1" jdbcType="DECIMAL" property="extendsInt1" />
    <result column="EXTENDS_INT2" jdbcType="DECIMAL" property="extendsInt2" />
    <result column="EXTENDS_INT3" jdbcType="DECIMAL" property="extendsInt3" />
    <result column="EXTENDS_INT4" jdbcType="DECIMAL" property="extendsInt4" />
    <result column="EXTENDS_INT5" jdbcType="DECIMAL" property="extendsInt5" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="MAC_ADDR" jdbcType="VARCHAR" property="macAddr" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INST_ID, BASELINE_ID, GROUP_ID, MODEL_ID, RESOURCE_NO, RESOURCE_NAME, DEVICE_MODEL,
    RESOURCE_STATE, PRODUCER, DEVICE_DUTYOR, TEND_DATE_START, TEND_DATE_END, DEVICE_UNIT_NAME,
    DEVICE_UNIT_ID, SUPPLIER_ID, SUPPLIER_NAME, CONTRACT_ID, CONTRACT_NAME, PROJECT_ID,
    PROJECT_NAME, DEVICE_STATE, IP_ADDR, SUBMIT_TIME, AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME,
    AUDITER, AUDITERID, IF_NOTICE, NOTICE_TIME, FILE_STR, AUDIT_OPIN, DEVICE_PRICE, OTHER_COLS,
    METADATA1, METADATA2, METADATA3, METADATA4, METADATA5, EXTENDS1, EXTENDS2, EXTENDS3,
    EXTENDS4, EXTENDS5, EXTENDS6, EXTENDS7, EXTENDS8, EXTENDS9, EXTENDS10, EXTENDS11,
    EXTENDS12, EXTENDS13, EXTENDS14, EXTENDS15, EXTENDS16, EXTENDS17, EXTENDS18, EXTENDS19,
    EXTENDS20, EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3, EXTENDS_DATE4, EXTENDS_DATE5,
    EXTENDS_INT1, EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4, EXTENDS_INT5, CREATE_BY,
    UPDATE_BY, CREATE_TIME, UPDATE_TIME, DEL_FLAG, MAC_ADDR, ORDER_NO
  </sql>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource">
    <!--@mbg.generated-->
    insert into cmdb_comm_resource (ID, INST_ID, BASELINE_ID,
      GROUP_ID, MODEL_ID, RESOURCE_NO,
      RESOURCE_NAME, DEVICE_MODEL, RESOURCE_STATE,
      PRODUCER, DEVICE_DUTYOR, TEND_DATE_START,
      TEND_DATE_END, DEVICE_UNIT_NAME, DEVICE_UNIT_ID,
      SUPPLIER_ID, SUPPLIER_NAME, CONTRACT_ID,
      CONTRACT_NAME, PROJECT_ID, PROJECT_NAME,
      DEVICE_STATE, IP_ADDR, SUBMIT_TIME,
      AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME,
      AUDITER, AUDITERID, IF_NOTICE,
      NOTICE_TIME, FILE_STR, AUDIT_OPIN,
      DEVICE_PRICE, OTHER_COLS, METADATA1,
      METADATA2, METADATA3, METADATA4,
      METADATA5, EXTENDS1, EXTENDS2,
      EXTENDS3, EXTENDS4, EXTENDS5,
      EXTENDS6, EXTENDS7, EXTENDS8,
      EXTENDS9, EXTENDS10, EXTENDS11,
      EXTENDS12, EXTENDS13, EXTENDS14,
      EXTENDS15, EXTENDS16, EXTENDS17,
      EXTENDS18, EXTENDS19, EXTENDS20,
      EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3,
      EXTENDS_DATE4, EXTENDS_DATE5, EXTENDS_INT1,
      EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4,
      EXTENDS_INT5, CREATE_BY, UPDATE_BY,
      CREATE_TIME, UPDATE_TIME, DEL_FLAG,
      MAC_ADDR, ORDER_NO)
    values (#{id,jdbcType=VARCHAR}, #{instId,jdbcType=VARCHAR}, #{baselineId,jdbcType=VARCHAR},
      #{groupId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{resourceNo,jdbcType=VARCHAR},
      #{resourceName,jdbcType=VARCHAR}, #{deviceModel,jdbcType=VARCHAR}, #{resourceState,jdbcType=VARCHAR},
      #{producer,jdbcType=VARCHAR}, #{deviceDutyor,jdbcType=VARCHAR}, #{tendDateStart,jdbcType=DATE},
      #{tendDateEnd,jdbcType=DATE}, #{deviceUnitName,jdbcType=VARCHAR}, #{deviceUnitId,jdbcType=VARCHAR},
      #{supplierId,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{contractId,jdbcType=VARCHAR},
      #{contractName,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR},
      #{deviceState,jdbcType=VARCHAR}, #{ipAddr,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP},
      #{auditState,jdbcType=VARCHAR}, #{auditResult,jdbcType=VARCHAR}, #{auditEndTime,jdbcType=TIMESTAMP},
      #{auditer,jdbcType=VARCHAR}, #{auditerid,jdbcType=VARCHAR}, #{ifNotice,jdbcType=VARCHAR},
      #{noticeTime,jdbcType=TIMESTAMP}, #{fileStr,jdbcType=VARCHAR}, #{auditOpin,jdbcType=VARCHAR},
      #{devicePrice,jdbcType=DECIMAL}, #{otherCols,jdbcType=VARCHAR}, #{metadata1,jdbcType=VARCHAR},
      #{metadata2,jdbcType=VARCHAR}, #{metadata3,jdbcType=VARCHAR}, #{metadata4,jdbcType=VARCHAR},
      #{metadata5,jdbcType=VARCHAR}, #{extends1,jdbcType=VARCHAR}, #{extends2,jdbcType=VARCHAR},
      #{extends3,jdbcType=VARCHAR}, #{extends4,jdbcType=VARCHAR}, #{extends5,jdbcType=VARCHAR},
      #{extends6,jdbcType=VARCHAR}, #{extends7,jdbcType=VARCHAR}, #{extends8,jdbcType=VARCHAR},
      #{extends9,jdbcType=VARCHAR}, #{extends10,jdbcType=VARCHAR}, #{extends11,jdbcType=VARCHAR},
      #{extends12,jdbcType=VARCHAR}, #{extends13,jdbcType=VARCHAR}, #{extends14,jdbcType=VARCHAR},
      #{extends15,jdbcType=VARCHAR}, #{extends16,jdbcType=VARCHAR}, #{extends17,jdbcType=VARCHAR},
      #{extends18,jdbcType=VARCHAR}, #{extends19,jdbcType=VARCHAR}, #{extends20,jdbcType=VARCHAR},
      #{extendsDate1,jdbcType=DATE}, #{extendsDate2,jdbcType=DATE}, #{extendsDate3,jdbcType=DATE},
      #{extendsDate4,jdbcType=DATE}, #{extendsDate5,jdbcType=DATE}, #{extendsInt1,jdbcType=DECIMAL},
      #{extendsInt2,jdbcType=DECIMAL}, #{extendsInt3,jdbcType=DECIMAL}, #{extendsInt4,jdbcType=DECIMAL},
      #{extendsInt5,jdbcType=DECIMAL}, #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR},
      #{macAddr,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource">
    <!--@mbg.generated-->
    insert into cmdb_comm_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
      <if test="baselineId != null">
        BASELINE_ID,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="modelId != null">
        MODEL_ID,
      </if>
      <if test="resourceNo != null">
        RESOURCE_NO,
      </if>
      <if test="resourceName != null">
        RESOURCE_NAME,
      </if>
      <if test="deviceModel != null">
        DEVICE_MODEL,
      </if>
      <if test="resourceState != null">
        RESOURCE_STATE,
      </if>
      <if test="producer != null">
        PRODUCER,
      </if>
      <if test="deviceDutyor != null">
        DEVICE_DUTYOR,
      </if>
      <if test="tendDateStart != null">
        TEND_DATE_START,
      </if>
      <if test="tendDateEnd != null">
        TEND_DATE_END,
      </if>
      <if test="deviceUnitName != null">
        DEVICE_UNIT_NAME,
      </if>
      <if test="deviceUnitId != null">
        DEVICE_UNIT_ID,
      </if>
      <if test="supplierId != null">
        SUPPLIER_ID,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="contractId != null">
        CONTRACT_ID,
      </if>
      <if test="contractName != null">
        CONTRACT_NAME,
      </if>
      <if test="projectId != null">
        PROJECT_ID,
      </if>
      <if test="projectName != null">
        PROJECT_NAME,
      </if>
      <if test="deviceState != null">
        DEVICE_STATE,
      </if>
      <if test="ipAddr != null">
        IP_ADDR,
      </if>
      <if test="submitTime != null">
        SUBMIT_TIME,
      </if>
      <if test="auditState != null">
        AUDIT_STATE,
      </if>
      <if test="auditResult != null">
        AUDIT_RESULT,
      </if>
      <if test="auditEndTime != null">
        AUDIT_END_TIME,
      </if>
      <if test="auditer != null">
        AUDITER,
      </if>
      <if test="auditerid != null">
        AUDITERID,
      </if>
      <if test="ifNotice != null">
        IF_NOTICE,
      </if>
      <if test="noticeTime != null">
        NOTICE_TIME,
      </if>
      <if test="fileStr != null">
        FILE_STR,
      </if>
      <if test="auditOpin != null">
        AUDIT_OPIN,
      </if>
      <if test="devicePrice != null">
        DEVICE_PRICE,
      </if>
      <if test="otherCols != null">
        OTHER_COLS,
      </if>
      <if test="metadata1 != null">
        METADATA1,
      </if>
      <if test="metadata2 != null">
        METADATA2,
      </if>
      <if test="metadata3 != null">
        METADATA3,
      </if>
      <if test="metadata4 != null">
        METADATA4,
      </if>
      <if test="metadata5 != null">
        METADATA5,
      </if>
      <if test="extends1 != null">
        EXTENDS1,
      </if>
      <if test="extends2 != null">
        EXTENDS2,
      </if>
      <if test="extends3 != null">
        EXTENDS3,
      </if>
      <if test="extends4 != null">
        EXTENDS4,
      </if>
      <if test="extends5 != null">
        EXTENDS5,
      </if>
      <if test="extends6 != null">
        EXTENDS6,
      </if>
      <if test="extends7 != null">
        EXTENDS7,
      </if>
      <if test="extends8 != null">
        EXTENDS8,
      </if>
      <if test="extends9 != null">
        EXTENDS9,
      </if>
      <if test="extends10 != null">
        EXTENDS10,
      </if>
      <if test="extends11 != null">
        EXTENDS11,
      </if>
      <if test="extends12 != null">
        EXTENDS12,
      </if>
      <if test="extends13 != null">
        EXTENDS13,
      </if>
      <if test="extends14 != null">
        EXTENDS14,
      </if>
      <if test="extends15 != null">
        EXTENDS15,
      </if>
      <if test="extends16 != null">
        EXTENDS16,
      </if>
      <if test="extends17 != null">
        EXTENDS17,
      </if>
      <if test="extends18 != null">
        EXTENDS18,
      </if>
      <if test="extends19 != null">
        EXTENDS19,
      </if>
      <if test="extends20 != null">
        EXTENDS20,
      </if>
      <if test="extendsDate1 != null">
        EXTENDS_DATE1,
      </if>
      <if test="extendsDate2 != null">
        EXTENDS_DATE2,
      </if>
      <if test="extendsDate3 != null">
        EXTENDS_DATE3,
      </if>
      <if test="extendsDate4 != null">
        EXTENDS_DATE4,
      </if>
      <if test="extendsDate5 != null">
        EXTENDS_DATE5,
      </if>
      <if test="extendsInt1 != null">
        EXTENDS_INT1,
      </if>
      <if test="extendsInt2 != null">
        EXTENDS_INT2,
      </if>
      <if test="extendsInt3 != null">
        EXTENDS_INT3,
      </if>
      <if test="extendsInt4 != null">
        EXTENDS_INT4,
      </if>
      <if test="extendsInt5 != null">
        EXTENDS_INT5,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="macAddr != null">
        MAC_ADDR,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
      <if test="baselineId != null">
        #{baselineId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="resourceNo != null">
        #{resourceNo,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceModel != null">
        #{deviceModel,jdbcType=VARCHAR},
      </if>
      <if test="resourceState != null">
        #{resourceState,jdbcType=VARCHAR},
      </if>
      <if test="producer != null">
        #{producer,jdbcType=VARCHAR},
      </if>
      <if test="deviceDutyor != null">
        #{deviceDutyor,jdbcType=VARCHAR},
      </if>
      <if test="tendDateStart != null">
        #{tendDateStart,jdbcType=DATE},
      </if>
      <if test="tendDateEnd != null">
        #{tendDateEnd,jdbcType=DATE},
      </if>
      <if test="deviceUnitName != null">
        #{deviceUnitName,jdbcType=VARCHAR},
      </if>
      <if test="deviceUnitId != null">
        #{deviceUnitId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="deviceState != null">
        #{deviceState,jdbcType=VARCHAR},
      </if>
      <if test="ipAddr != null">
        #{ipAddr,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditEndTime != null">
        #{auditEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditer != null">
        #{auditer,jdbcType=VARCHAR},
      </if>
      <if test="auditerid != null">
        #{auditerid,jdbcType=VARCHAR},
      </if>
      <if test="ifNotice != null">
        #{ifNotice,jdbcType=VARCHAR},
      </if>
      <if test="noticeTime != null">
        #{noticeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileStr != null">
        #{fileStr,jdbcType=VARCHAR},
      </if>
      <if test="auditOpin != null">
        #{auditOpin,jdbcType=VARCHAR},
      </if>
      <if test="devicePrice != null">
        #{devicePrice,jdbcType=DECIMAL},
      </if>
      <if test="otherCols != null">
        #{otherCols,jdbcType=VARCHAR},
      </if>
      <if test="metadata1 != null">
        #{metadata1,jdbcType=VARCHAR},
      </if>
      <if test="metadata2 != null">
        #{metadata2,jdbcType=VARCHAR},
      </if>
      <if test="metadata3 != null">
        #{metadata3,jdbcType=VARCHAR},
      </if>
      <if test="metadata4 != null">
        #{metadata4,jdbcType=VARCHAR},
      </if>
      <if test="metadata5 != null">
        #{metadata5,jdbcType=VARCHAR},
      </if>
      <if test="extends1 != null">
        #{extends1,jdbcType=VARCHAR},
      </if>
      <if test="extends2 != null">
        #{extends2,jdbcType=VARCHAR},
      </if>
      <if test="extends3 != null">
        #{extends3,jdbcType=VARCHAR},
      </if>
      <if test="extends4 != null">
        #{extends4,jdbcType=VARCHAR},
      </if>
      <if test="extends5 != null">
        #{extends5,jdbcType=VARCHAR},
      </if>
      <if test="extends6 != null">
        #{extends6,jdbcType=VARCHAR},
      </if>
      <if test="extends7 != null">
        #{extends7,jdbcType=VARCHAR},
      </if>
      <if test="extends8 != null">
        #{extends8,jdbcType=VARCHAR},
      </if>
      <if test="extends9 != null">
        #{extends9,jdbcType=VARCHAR},
      </if>
      <if test="extends10 != null">
        #{extends10,jdbcType=VARCHAR},
      </if>
      <if test="extends11 != null">
        #{extends11,jdbcType=VARCHAR},
      </if>
      <if test="extends12 != null">
        #{extends12,jdbcType=VARCHAR},
      </if>
      <if test="extends13 != null">
        #{extends13,jdbcType=VARCHAR},
      </if>
      <if test="extends14 != null">
        #{extends14,jdbcType=VARCHAR},
      </if>
      <if test="extends15 != null">
        #{extends15,jdbcType=VARCHAR},
      </if>
      <if test="extends16 != null">
        #{extends16,jdbcType=VARCHAR},
      </if>
      <if test="extends17 != null">
        #{extends17,jdbcType=VARCHAR},
      </if>
      <if test="extends18 != null">
        #{extends18,jdbcType=VARCHAR},
      </if>
      <if test="extends19 != null">
        #{extends19,jdbcType=VARCHAR},
      </if>
      <if test="extends20 != null">
        #{extends20,jdbcType=VARCHAR},
      </if>
      <if test="extendsDate1 != null">
        #{extendsDate1,jdbcType=DATE},
      </if>
      <if test="extendsDate2 != null">
        #{extendsDate2,jdbcType=DATE},
      </if>
      <if test="extendsDate3 != null">
        #{extendsDate3,jdbcType=DATE},
      </if>
      <if test="extendsDate4 != null">
        #{extendsDate4,jdbcType=DATE},
      </if>
      <if test="extendsDate5 != null">
        #{extendsDate5,jdbcType=DATE},
      </if>
      <if test="extendsInt1 != null">
        #{extendsInt1,jdbcType=DECIMAL},
      </if>
      <if test="extendsInt2 != null">
        #{extendsInt2,jdbcType=DECIMAL},
      </if>
      <if test="extendsInt3 != null">
        #{extendsInt3,jdbcType=DECIMAL},
      </if>
      <if test="extendsInt4 != null">
        #{extendsInt4,jdbcType=DECIMAL},
      </if>
      <if test="extendsInt5 != null">
        #{extendsInt5,jdbcType=DECIMAL},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="macAddr != null">
        #{macAddr,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getConfList" parameterType="java.util.Map" resultMap="BaseResultMap">
    select
    t.id,
    t.inst_id,
    t.baseline_id,
    t.resource_no,
    t.resource_name,
    t.device_model, t.resource_state, t.tend_date_start,t.tend_date_end,
    t.device_unit_name as device_unit, t.device_unit_id device_unitid,
    t.device_state,t.device_dutyor,
    b.NAME as type
    from CMDB_COMM_RESOURCE t
    left join CMDB_TABLE_BASE_CONFIG b on t.BASELINE_ID = b.BASELINE_ID
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
  <select id="selectInfoByContractId" resultMap="BaseResultMap">
        select
        t.*
        from CMDB_COMM_RESOURCE t
        left join CMDB_TABLE_BASE_CONFIG b on t.BASELINE_ID = b.BASELINE_ID
        <where>
          <if test="whereSql!=null">
            ${whereSql}
          </if>
        </where>
        <if test="orderBySql!=null">
          ORDER BY ${orderBySql}
        </if>
  </select>

  <select id="selectPropertyList" resultType="cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO">
      select
        t.id as id,
        t.baseline_id as baselineId,
        t.resource_no as resourceNo,
        t.resource_name as resourceName,
        t.device_model as deviceModel,
        t.resource_state as resourceState,
        t.tend_date_start as tendDateStart,
        t.tend_date_end as tendDateEnd,
        t.device_unit_name as deviceUnit,
        t.device_unit_id as deviceUnitId,
        t.device_state as deviceState,
        b.NAME as `type`
      from CMDB_COMM_RESOURCE t
      left join CMDB_TABLE_BASE_CONFIG b on t.BASELINE_ID = b.BASELINE_ID
      <where>
        <if test="whereSql!=null">
          ${whereSql}
        </if>
      </where>
      <if test="orderBySql!=null">
        ORDER BY ${orderBySql}
      </if>
  </select>
</mapper>
