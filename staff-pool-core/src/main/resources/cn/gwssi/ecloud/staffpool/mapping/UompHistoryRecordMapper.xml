<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompHistoryRecordMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord">
    <!--@mbg.generated-->
    <!--@Table uomp_history_record-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BIZ_ID" jdbcType="VARCHAR" property="bizId" />
    <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType" />
    <result column="OPERATOR_ID" jdbcType="VARCHAR" property="operatorId" />
    <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
    <result column="OPERATOR_MESSAGE" jdbcType="VARCHAR" property="operatorMessage" />
    <result column="OPERATOR_REASON" jdbcType="VARCHAR" property="operatorReason" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="OPERATOR_TIME" jdbcType="TIMESTAMP" property="operatorTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, BIZ_ID, BIZ_TYPE, OPERATOR_ID, OPERATOR_NAME, OPERATOR_MESSAGE, OPERATOR_REASON, 
    CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG, OPERATOR_TIME
  </sql>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord">
    <!--@mbg.generated-->
    insert into uomp_history_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="bizId != null">
        BIZ_ID,
      </if>
      <if test="bizType != null">
        BIZ_TYPE,
      </if>
      <if test="operatorId != null">
        OPERATOR_ID,
      </if>
      <if test="operatorName != null">
        OPERATOR_NAME,
      </if>
      <if test="operatorMessage != null">
        OPERATOR_MESSAGE,
      </if>
      <if test="operatorReason != null">
        OPERATOR_REASON,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="operatorTime != null">
        OPERATOR_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorMessage != null">
        #{operatorMessage,jdbcType=VARCHAR},
      </if>
      <if test="operatorReason != null">
        #{operatorReason,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="operatorTime != null">
        #{operatorTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <resultMap id="BaseBlackMap" type="cn.gwssi.ecloud.staffpool.dto.UompBlackDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_info-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <id column="BIZ_ID" jdbcType="VARCHAR" property="bizId" />
    <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
    <result column="OPERATOR_MESSAGE" jdbcType="VARCHAR" property="operatorMessage" />
    <result column="OPERATOR_REASON" jdbcType="VARCHAR" property="operatorReason" />
    <result column="OPERATOR_TIME" jdbcType="TIMESTAMP" property="operatorTime" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
    <result column="PERSON_SEX" jdbcType="VARCHAR" property="personSex"/>
    <result column="PERSON_BIRTHDAY" jdbcType="VARCHAR" property="personBirthday"/>
    <result column="TEL" jdbcType="VARCHAR" property="tel"/>
    <result column="EDUCATION" jdbcType="VARCHAR" property="education"/>
    <result column="POST" jdbcType="VARCHAR" property="post"/>
    <result column="MAJOR" jdbcType="VARCHAR" property="major"/>
    <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
    <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate"/>
  </resultMap>

  <select id="getBlackHistoryList" resultMap="BaseBlackMap">
    select f.* from (
    select
    a.ID,
    a.BIZ_ID,
    a.OPERATOR_NAME,
    a.OPERATOR_MESSAGE,
    a.OPERATOR_REASON,
    a.OPERATOR_TIME,
    b.PERSON_NAME,
    b.PERSON_SEX,
    b.WORKING_COMPANY,
    b.POST,
    b.EDUCATION,
    b.MAJOR,
    b.TEL,
    b.PERSON_BIRTHDAY,
    b.ENTRY_DATE
    from
    UOMP_HISTORY_RECORD a
    inner join UOMP_PERSON_INFO b on a.BIZ_ID = b.ID
    where a.DEL_FLAG = '0' and a.BIZ_TYPE = '0'
    union all
    select
    a.ID,
    a.BIZ_ID,
    a.OPERATOR_NAME,
    a.OPERATOR_MESSAGE,
    a.OPERATOR_REASON,
    a.OPERATOR_TIME,
    b.PERSON_NAME,
    '' as PERSON_SEX,
    b.WORKING_COMPANY,
    '' as POST,
    '' as EDUCATION,
    '' as MAJOR,
    b.TEL,
    '' as PERSON_BIRTHDAY,
    '' as ENTRY_DATE
    from
    UOMP_HISTORY_RECORD a
    inner join UOMP_TEMP_ADMISSION b on a.BIZ_ID = b.ID
    where a.DEL_FLAG = '0' and a.BIZ_TYPE = '0'
    ) f
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>