<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonInfoMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo">
        <!--@mbg.generated-->
        <!--@Table uomp_person_info-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard"/>
        <result column="PERSON_SEX" jdbcType="VARCHAR" property="personSex"/>
        <result column="PERSON_BIRTHDAY" jdbcType="VARCHAR" property="personBirthday"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="EDUCATION" jdbcType="VARCHAR" property="education"/>
        <result column="POST" jdbcType="VARCHAR" property="post"/>
        <result column="MAJOR" jdbcType="VARCHAR" property="major"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="BLACKLIST" jdbcType="VARCHAR" property="blacklist"/>
        <result column="BLACKLIST_REASON" jdbcType="VARCHAR" property="blacklistReason"/>
        <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate"/>
        <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo"/>
        <result column="TRIAL_STATUS" jdbcType="VARCHAR" property="trialStatus"/>
        <result column="BACKGROUND_STATUS" jdbcType="VARCHAR" property="backgroundStatus"/>
        <result column="ID_TYPE" jdbcType="VARCHAR" property="idType"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="IS_ACCOUNT" jdbcType="VARCHAR" property="isAccount"/>
        <result column="ACCOUNT" jdbcType="VARCHAR" property="account"/>
        <result column="WORKING_COMPANY_ID" jdbcType="VARCHAR" property="workingCompanyId"/>
        <result column="TECHNICAL_DIRECTION" jdbcType="VARCHAR" property="technicalDirection"/>
        <result column="REG_PERMANENT_RESIDENCE" jdbcType="VARCHAR" property="regPermanentResidence"/>
        <result column="NATIONALITY" jdbcType="VARCHAR" property="nationality"/>
        <result column="POLITICS_STATUS" jdbcType="VARCHAR" property="politicsStatus"/>
        <result column="NATION" jdbcType="VARCHAR" property="nation"/>
        <result column="REG_PROVINCE" jdbcType="VARCHAR" property="regProvince"/>
        <result column="REG_CITY" jdbcType="VARCHAR" property="regCity"/>
        <result column="REG_REGION" jdbcType="VARCHAR" property="regRegion"/>
        <result column="AUDIT_DATE" jdbcType="TIMESTAMP" property="auditDate"/>
        <result column="ORG_GROUP_ID" jdbcType="VARCHAR" property="orgGroupId"/>
        <result column="ORG_GROUP_NAME" jdbcType="VARCHAR" property="orgGroupName"/>
        <result column="UPDATING" jdbcType="VARCHAR" property="updating"/>
        <result column="ORG_USER_ID" jdbcType="VARCHAR" property="orgUserId"/>
        <result column="ENTRY_STATUS" jdbcType="VARCHAR" property="entryStatus"/>
        <result column="DIMISSION" jdbcType="VARCHAR" property="dimission"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        PERSON_NAME,
        PERSON_CARD,
        PERSON_SEX,
        PERSON_BIRTHDAY,
        TEL,
        ADDRESS,
        EDUCATION,
        POST,
        MAJOR,
        WORKING_COMPANY,
        REMARK,
        INST_ID,
        BLACKLIST,
        BLACKLIST_REASON,
        ENTRY_DATE,
        FILE_INFO,
        TRIAL_STATUS,
        BACKGROUND_STATUS,
        ID_TYPE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG,
        IS_ACCOUNT,
        ACCOUNT,
        WORKING_COMPANY_ID,
        TECHNICAL_DIRECTION,
        REG_PERMANENT_RESIDENCE,
        NATIONALITY,
        POLITICS_STATUS,
        NATION,
        REG_PROVINCE,
        REG_CITY,
        REG_REGION,
        AUDIT_DATE,
        ORG_GROUP_ID,
        ORG_GROUP_NAME,
        UPDATING,
        ORG_USER_ID,
        ENTRY_STATUS,
        DIMISSION,
        EMAIL
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_person_info
        where ID = #{id,jdbcType=VARCHAR} and DEL_FLAG = '0'
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from uomp_person_info
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo">
        <!--@mbg.generated-->
        insert into uomp_person_info (ID, PERSON_NAME, PERSON_CARD,
                                      PERSON_SEX, PERSON_BIRTHDAY, TEL,
                                      ADDRESS, EDUCATION, POST,
                                      MAJOR, WORKING_COMPANY, REMARK,
                                      INST_ID, BLACKLIST, BLACKLIST_REASON,
                                      ENTRY_DATE, FILE_INFO, TRIAL_STATUS,
                                      BACKGROUND_STATUS, ID_TYPE, CREATE_BY,
                                      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
                                      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG,
                                      IS_ACCOUNT, ACCOUNT, WORKING_COMPANY_ID,
                                      TECHNICAL_DIRECTION, REG_PERMANENT_RESIDENCE,
                                      NATIONALITY, POLITICS_STATUS, NATION,
                                      REG_PROVINCE, REG_CITY, REG_REGION,AUDIT_DATE,ORG_GROUP_ID,ORG_GROUP_NAME,UPDATING,ORG_USER_ID,ENTRY_STATUS,DIMISSION,EMAIL)
        values (#{id,jdbcType=VARCHAR}, #{personName,jdbcType=VARCHAR}, #{personCard,jdbcType=VARCHAR},
                #{personSex,jdbcType=VARCHAR}, #{personBirthday,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR}, #{education,jdbcType=VARCHAR}, #{post,jdbcType=VARCHAR},
                #{major,jdbcType=VARCHAR}, #{workingCompany,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{instId,jdbcType=VARCHAR}, #{blacklist,jdbcType=VARCHAR}, #{blacklistReason,jdbcType=VARCHAR},
                #{entryDate,jdbcType=VARCHAR}, #{fileInfo,jdbcType=VARCHAR}, #{trialStatus,jdbcType=VARCHAR},
                #{backgroundStatus,jdbcType=VARCHAR}, #{idType,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR},
                #{isAccount,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{workingCompanyId,jdbcType=VARCHAR},
                #{technicalDirection,jdbcType=VARCHAR}, #{regPermanentResidence,jdbcType=VARCHAR},
                #{nationality,jdbcType=VARCHAR}, #{politicsStatus,jdbcType=VARCHAR}, #{nation,jdbcType=VARCHAR},
                #{regProvince,jdbcType=VARCHAR}, #{regCity,jdbcType=VARCHAR}, #{regRegion,jdbcType=VARCHAR},
                #{auditDate,jdbcType=TIMESTAMP}, #{orgGroupId,jdbcType=VARCHAR}, #{orgGroupName,jdbcType=VARCHAR},
                #{updating,jdbcType=VARCHAR},#{orgUserId,jdbcType=VARCHAR},#{entryStatus,jdbcType=VARCHAR},#{dimission,jdbcType=VARCHAR},
                #{email,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo">
        <!--@mbg.generated-->
        insert into uomp_person_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="personName != null">
                PERSON_NAME,
            </if>
            <if test="personCard != null">
                PERSON_CARD,
            </if>
            <if test="personSex != null">
                PERSON_SEX,
            </if>
            <if test="personBirthday != null">
                PERSON_BIRTHDAY,
            </if>
            <if test="tel != null">
                TEL,
            </if>
            <if test="address != null">
                ADDRESS,
            </if>
            <if test="education != null">
                EDUCATION,
            </if>
            <if test="post != null">
                POST,
            </if>
            <if test="major != null">
                MAJOR,
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="instId != null">
                INST_ID,
            </if>
            <if test="blacklist != null">
                BLACKLIST,
            </if>
            <if test="blacklistReason != null">
                BLACKLIST_REASON,
            </if>
            <if test="entryDate != null">
                ENTRY_DATE,
            </if>
            <if test="fileInfo != null">
                FILE_INFO,
            </if>
            <if test="trialStatus != null">
                TRIAL_STATUS,
            </if>
            <if test="backgroundStatus != null">
                BACKGROUND_STATUS,
            </if>
            <if test="idType != null">
                ID_TYPE,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
            <if test="isAccount != null">
                IS_ACCOUNT,
            </if>
            <if test="account != null">
                ACCOUNT,
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID,
            </if>
            <if test="technicalDirection != null">
                TECHNICAL_DIRECTION,
            </if>
            <if test="regPermanentResidence != null">
                REG_PERMANENT_RESIDENCE,
            </if>
            <if test="nationality != null">
                NATIONALITY,
            </if>
            <if test="politicsStatus != null">
                POLITICS_STATUS,
            </if>
            <if test="nation != null">
                NATION,
            </if>
            <if test="regProvince != null">
                REG_PROVINCE,
            </if>
            <if test="regCity != null">
                REG_CITY,
            </if>
            <if test="regRegion != null">
                REG_REGION,
            </if>
            <if test="auditDate != null">
                AUDIT_DATE,
            </if>
            <if test="orgGroupId != null">
                ORG_GROUP_ID,
            </if>
            <if test="orgGroupName != null">
                ORG_GROUP_NAME,
            </if>
            <if test="updating != null">
                UPDATING,
            </if>
            <if test="orgUserId != null">
                ORG_USER_ID,
            </if>
            <if test="entryStatus != null">
                ENTRY_STATUS,
            </if>
            <if test="dimission != null">
                DIMISSION,
            </if>
            <if test="email != null">
                EMAIL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="personSex != null">
                #{personSex,jdbcType=VARCHAR},
            </if>
            <if test="personBirthday != null">
                #{personBirthday,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                #{education,jdbcType=VARCHAR},
            </if>
            <if test="post != null">
                #{post,jdbcType=VARCHAR},
            </if>
            <if test="major != null">
                #{major,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="blacklist != null">
                #{blacklist,jdbcType=VARCHAR},
            </if>
            <if test="blacklistReason != null">
                #{blacklistReason,jdbcType=VARCHAR},
            </if>
            <if test="entryDate != null">
                #{entryDate,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="trialStatus != null">
                #{trialStatus,jdbcType=VARCHAR},
            </if>
            <if test="backgroundStatus != null">
                #{backgroundStatus,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="isAccount != null">
                #{isAccount,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="technicalDirection != null">
                #{technicalDirection,jdbcType=VARCHAR},
            </if>
            <if test="regPermanentResidence != null">
                #{regPermanentResidence,jdbcType=VARCHAR},
            </if>
            <if test="nationality != null">
                #{nationality,jdbcType=VARCHAR},
            </if>
            <if test="politicsStatus != null">
                #{politicsStatus,jdbcType=VARCHAR},
            </if>
            <if test="nation != null">
                #{nation,jdbcType=VARCHAR},
            </if>
            <if test="regProvince != null">
                #{regProvince,jdbcType=VARCHAR},
            </if>
            <if test="regCity != null">
                #{regCity,jdbcType=VARCHAR},
            </if>
            <if test="regRegion != null">
                #{regRegion,jdbcType=VARCHAR},
            </if>
            <if test="auditDate != null">
                #{auditDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orgGroupId != null">
                #{orgGroupId,jdbcType=VARCHAR},
            </if>
            <if test="orgGroupName != null">
                #{orgGroupName,jdbcType=VARCHAR},
            </if>
            <if test="updating != null">
                #{updating,jdbcType=VARCHAR},
            </if>
            <if test="orgUserId != null">
                #{orgUserId,jdbcType=VARCHAR},
            </if>
            <if test="entryStatus != null">
                #{entryStatus,jdbcType=VARCHAR},
            </if>
            <if test="dimission != null">
                #{dimission,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo">
        <!--@mbg.generated-->
        update uomp_person_info
        set PERSON_NAME = #{personName,jdbcType=VARCHAR},
        PERSON_CARD = #{personCard,jdbcType=VARCHAR},
        PERSON_SEX = #{personSex,jdbcType=VARCHAR},
        PERSON_BIRTHDAY = #{personBirthday,jdbcType=VARCHAR},
        TEL = #{tel,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR},
        EDUCATION = #{education,jdbcType=VARCHAR},
        POST = #{post,jdbcType=VARCHAR},
        MAJOR = #{major,jdbcType=VARCHAR},
        WORKING_COMPANY = #{workingCompany,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        INST_ID = #{instId,jdbcType=VARCHAR},
        BLACKLIST = #{blacklist,jdbcType=VARCHAR},
        BLACKLIST_REASON = #{blacklistReason,jdbcType=VARCHAR},
        ENTRY_DATE = #{entryDate,jdbcType=VARCHAR},
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
        TRIAL_STATUS = #{trialStatus,jdbcType=VARCHAR},
        BACKGROUND_STATUS = #{backgroundStatus,jdbcType=VARCHAR},
        ID_TYPE = #{idType,jdbcType=VARCHAR},
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
        IS_ACCOUNT = #{isAccount,jdbcType=VARCHAR},
        ACCOUNT = #{account,jdbcType=VARCHAR},
        WORKING_COMPANY_ID = #{workingCompanyId,jdbcType=VARCHAR},
        TECHNICAL_DIRECTION = #{technicalDirection,jdbcType=VARCHAR},
        REG_PERMANENT_RESIDENCE = #{regPermanentResidence,jdbcType=VARCHAR},
        NATIONALITY = #{nationality,jdbcType=VARCHAR},
        POLITICS_STATUS = #{politicsStatus,jdbcType=VARCHAR},
        NATION = #{nation,jdbcType=VARCHAR},
        REG_PROVINCE = #{regProvince,jdbcType=VARCHAR},
        REG_CITY = #{regCity,jdbcType=VARCHAR},
        REG_REGION = #{regRegion,jdbcType=VARCHAR},
        AUDIT_DATE = #{auditDate,jdbcType=TIMESTAMP},
        ORG_GROUP_ID = #{orgGroupId,jdbcType=VARCHAR},
        ORG_GROUP_NAME = #{orgGroupName,jdbcType=VARCHAR},
        UPDATING = #{updating,jdbcType=VARCHAR},
        ORG_USER_ID = #{orgUserId,jdbcType=VARCHAR},
        ENTRY_STATUS = #{entryStatus,jdbcType=VARCHAR},
        DIMISSION = #{dimission,jdbcType=VARCHAR},
        EMAIL = #{email,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByIds">
        update UOMP_PERSON_INFO set
        DEL_FLAG = '1',
        UPDATE_ORG_ID = #{orgId},

        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo">
        <!--@mbg.generated-->
        update uomp_person_info
        <set>
            <if test="personName != null">
                PERSON_NAME = #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                PERSON_CARD = #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="personSex != null">
                PERSON_SEX = #{personSex,jdbcType=VARCHAR},
            </if>
            <if test="personBirthday != null">
                PERSON_BIRTHDAY = #{personBirthday,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                TEL = #{tel,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                EDUCATION = #{education,jdbcType=VARCHAR},
            </if>
            <if test="post != null">
                POST = #{post,jdbcType=VARCHAR},
            </if>
            <if test="major != null">
                MAJOR = #{major,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY = #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                INST_ID = #{instId,jdbcType=VARCHAR},
            </if>
            <if test="blacklist != null">
                BLACKLIST = #{blacklist,jdbcType=VARCHAR},
            </if>
            <if test="blacklistReason != null">
                BLACKLIST_REASON = #{blacklistReason,jdbcType=VARCHAR},
            </if>
            <if test="entryDate != null">
                ENTRY_DATE = #{entryDate,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="trialStatus != null">
                TRIAL_STATUS = #{trialStatus,jdbcType=VARCHAR},
            </if>
            <if test="backgroundStatus != null">
                BACKGROUND_STATUS = #{backgroundStatus,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                ID_TYPE = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="isAccount != null">
                IS_ACCOUNT = #{isAccount,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                ACCOUNT = #{account,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID = #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="technicalDirection != null">
                TECHNICAL_DIRECTION = #{technicalDirection,jdbcType=VARCHAR},
            </if>
            <if test="regPermanentResidence != null">
                REG_PERMANENT_RESIDENCE = #{regPermanentResidence,jdbcType=VARCHAR},
            </if>
            <if test="nationality != null">
                NATIONALITY = #{nationality,jdbcType=VARCHAR},
            </if>
            <if test="politicsStatus != null">
                POLITICS_STATUS = #{politicsStatus,jdbcType=VARCHAR},
            </if>
            <if test="nation != null">
                NATION = #{nation,jdbcType=VARCHAR},
            </if>
            <if test="regProvince != null">
                REG_PROVINCE = #{regProvince,jdbcType=VARCHAR},
            </if>
            <if test="regCity != null">
                REG_CITY = #{regCity,jdbcType=VARCHAR},
            </if>
            <if test="regRegion != null">
                REG_REGION = #{regRegion,jdbcType=VARCHAR},
            </if>
            <if test="auditDate != null">
                AUDIT_DATE = #{auditDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orgGroupId != null">
                ORG_GROUP_ID = #{orgGroupId,jdbcType=VARCHAR},
            </if>
            <if test="orgGroupName != null">
                ORG_GROUP_NAME = #{orgGroupName,jdbcType=VARCHAR},
            </if>
            <if test="updating != null">
                UPDATING = #{updating,jdbcType=VARCHAR},
            </if>
            <if test="orgUserId != null">
                ORG_USER_ID =  #{orgUserId,jdbcType=VARCHAR},
            </if>
            <if test="entryStatus != null">
                ENTRY_STATUS = #{entryStatus,jdbcType=VARCHAR},
            </if>
            <if test="dimission != null">
                DIMISSION = #{dimission,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="query" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UOMP_PERSON_INFO
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
    <select id="selectInfoOneByPersonId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPersonInfoDTO">
        select a.PERSON_NAME        as personName,
               a.TEL                as tel,
               a.ADDRESS            as address,
               a.POST               as post,
               a.PERSON_SEX         as personSex,
               a.WORKING_COMPANY_ID as workingCompanyId,
               a.ORG_GROUP_ID       as groupId,
               b.SUPPLIER_NAME      as supplierName,
               c.ORG_GROUP_ID       as orgGroupId
        from UOMP_PERSON_INFO a
        left join UOMP_SUPPLIER_MANAGEMENT b on a.WORKING_COMPANY_ID = b.ID and b.DEL_FLAG = '0'
        left join UOMP_ORG_GROUP c on a.ORG_GROUP_ID = c.ID_
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
    </select>
    <select id="selectTaskIdByInstId" resultType="cn.gwssi.ecloud.staffpool.dto.BaseInstDTO">
        select task.task_id_ as taskId,
               linkd.id_     as linkId
        from bpm_task task
        left join bpm_task_identitylink linkd on task.id_ = linkd.task_id_
        where task.INST_ID_ = #{instId} limit 1
    </select>
    <!--  账号申请人员列表 -->
    <select id="selectNoAccountPeronList" resultType="cn.gwssi.ecloud.staffpool.dto.UompPersonInfoAndAccountDTO">
        select
            upi.ID                  as id,
            upi.PERSON_NAME         as personName,
            upi.PERSON_CARD         as personCard,
            upi.ENTRY_DATE          as entryDate,
            upi.WORKING_COMPANY     as workingCompany,
            upi.TECHNICAL_DIRECTION as technicalDirection,
            upi.TEL                 as mobile,
            upi.PERSON_SEX          as sex,
            upi.ADDRESS             as address,
            uog.ORG_GROUP_ID        as orgGroupId,
            uog.NAME_				as orgGroupName
        from UOMP_PERSON_INFO upi
        left join uomp_org_group uog on upi.ORG_GROUP_ID = uog.ID_
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <!--  查询非黑名单，且审核通过的人员数据，2023-12-20背调不合格的也不能申请入场-->
    <select id="selectAllByPersonSelet" resultType="cn.gwssi.ecloud.staffpool.dto.PersonSeletDTO">
        select a.ID                  as id,
               a.PERSON_NAME         as personName,
               a.PERSON_CARD         as personCard,
               a.WORKING_COMPANY     as workingCompany,
               a.WORKING_COMPANY_ID  as workingCompanyId,
               a.EDUCATION           as education,
               a.TEL                 as tel,
               a.TECHNICAL_DIRECTION as technicalDirection,
               a.ENTRY_DATE          as entryDate,
               a.WORKING_COMPANY_ID  as supplierId,
               a.ORG_GROUP_ID        as orgGroupId,
               a.ORG_GROUP_NAME      as orgGroupName,
               a.PERSON_SEX          as personSex
        from UOMP_PERSON_INFO a
        where DEL_FLAG = '0'
          and BLACKLIST = '0'
          and TRIAL_STATUS = '2'
          and BACKGROUND_STATUS != '2'
        <if test="ifSupplier == 1">
            and WORKING_COMPANY_ID = #{supplierId}
        </if>
        order by CREATE_TIME desc
    </select>

    <select id="selectAllByAdmissionPersonId" resultType="cn.gwssi.ecloud.staffpool.dto.AcceptPersonSelectDTO">
        select a.ID              as id,
               a.PERSON_NAME     as personName,
               a.WORKING_COMPANY as workingCompany,
               a.ACCOUNT         as account
        from UOMP_PERSON_INFO a
        where a.DEL_FLAG = '0'
          and a.TRIAL_STATUS = '2'
          and a.PERSON_CARD in (select PERSON_CARD
                                from UOMP_ADMISSION_PERSON
                                where MAINTENANCE_GROUP_ID = (select MAINTENANCE_GROUP_ID
                                                              from UOMP_ADMISSION_PERSON
                                                              where id = #{admissionPersonId})
                                  and APPLY_STATUS = '2'
                                  and id != #{admissionPersonId}
                                group by PERSON_CARD)
        order by a.CREATE_TIME desc
    </select>

    <select id="selectAccountByPersonCard" resultType="java.lang.String">
        select ACCOUNT
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and PERSON_CARD = #{personCard}
    </select>

    <select id="countByBackgroundStatus" resultType="cn.gwssi.ecloud.staffpool.dto.KeyWordNumDTO">
        select count(ID)         as num,
               BACKGROUND_STATUS as keyWord
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and BACKGROUND_STATUS in ('1', '2')
        group by BACKGROUND_STATUS
        order by BACKGROUND_STATUS
    </select>

    <select id="countPassPersonByBackgroundStatus" resultType="cn.gwssi.ecloud.staffpool.dto.KeyWordNumDTO">
        select count(ID) as num,
               BACKGROUND_STATUS as keyWord
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
          and BLACKLIST = '0'
		  and DIMISSION = '0'
          and BACKGROUND_STATUS in ('0', '1', '2')
        group by BACKGROUND_STATUS
        order by BACKGROUND_STATUS asc
    </select>

    <select id="countPassPersonByBackgroundStatusAndWorkingCompanyId" resultType="cn.gwssi.ecloud.staffpool.dto.KeyWordNumDTO">
        select count(ID) as num,
               BACKGROUND_STATUS as keyWord
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
          and BLACKLIST = '0'
		  and DIMISSION = '0'
          and BACKGROUND_STATUS in ('0', '1', '2')
          and WORKING_COMPANY_ID = #{workingCompanyId}
        group by BACKGROUND_STATUS
        order by BACKGROUND_STATUS asc
    </select>

    <select id="countByTrialStatus" resultType="cn.gwssi.ecloud.staffpool.dto.KeyWordNumDTO">
        select count(ID) as num,
               EDUCATION as keyWord
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
        group by EDUCATION
        order by EDUCATION
    </select>

    <select id="selectPersonBirthdayByTrialStatus" resultType="java.lang.String">
        select PERSON_BIRTHDAY
        from UOMP_PERSON_INFO
        where DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
    </select>

    <select id="countByWorkingCompanyIdAndTechnicalDirection" resultType="java.lang.Integer">
        select count(ID) as count
        from
        UOMP_PERSON_INFO
        where
        DEL_FLAG = '0'
        and TRIAL_STATUS = '2'
        and WORKING_COMPANY_ID = #{id}
        and TECHNICAL_DIRECTION = #{key}
    </select>

    <select id="selectPersonByWorkingCompany" resultMap="BaseResultMap">
        select ID, PERSON_NAME,ORG_GROUP_ID, ORG_GROUP_NAME
        from
        UOMP_PERSON_INFO
        where
        DEL_FLAG = '0'
        and TRIAL_STATUS = '2'
        and BLACKLIST = '0'
		and DIMISSION = '0'
		and BACKGROUND_STATUS != '2'
        and WORKING_COMPANY_ID = #{id}
    </select>

    <select id="selectUserId" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
        select
            u.ID_ as id
        from
            UOMP_PERSON_INFO a
                left join ORG_USER u on a.ACCOUNT = u.ACCOUNT_
        where a.ID = #{id} and a.DEL_FLAG = '0'
        limit 1
    </select>

    <select id="selectBlack" resultMap="BaseBlackMap">
        select
            a.ID,
            a.OPERATOR_NAME,
            a.OPERATOR_MESSAGE,
            a.OPERATOR_REASON,
            a.OPERATOR_TIME,
            b.PERSON_NAME,
            b.PERSON_SEX,
            b.WORKING_COMPANY,
            b.POST,
            b.EDUCATION,
            b.MAJOR,
            b.TEL,
            b.PERSON_BIRTHDAY,
            b.ENTRY_DATE
        from
            UOMP_HISTORY_RECORD a
                left join UOMP_PERSON_INFO b on a.BIZ_ID = b.ID
        where a.DEL_FLAG = '0' and a.BIZ_TYPE = '0'
          and a.BIZ_ID = #{id}
    </select>
    <resultMap id="BaseBlackMap" type="cn.gwssi.ecloud.staffpool.dto.UompBlackDto">
        <!--@mbg.generated-->
        <!--@Table uomp_person_info-->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
        <result column="OPERATOR_MESSAGE" jdbcType="VARCHAR" property="operatorMessage" />
        <result column="OPERATOR_REASON" jdbcType="VARCHAR" property="operatorReason" />
        <result column="OPERATOR_TIME" jdbcType="TIMESTAMP" property="operatorTime" />
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="PERSON_SEX" jdbcType="VARCHAR" property="personSex"/>
        <result column="PERSON_BIRTHDAY" jdbcType="VARCHAR" property="personBirthday"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="EDUCATION" jdbcType="VARCHAR" property="education"/>
        <result column="POST" jdbcType="VARCHAR" property="post"/>
        <result column="MAJOR" jdbcType="VARCHAR" property="major"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="ORG_GROUP_NAME" jdbcType="VARCHAR" property="orgGroupName"/>
        <result column="TECHNICAL_DIRECTION" jdbcType="VARCHAR" property="technicalDirection"/>
        <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate"/>
        <result column="TRIAL_STATUS" jdbcType="VARCHAR" property="trialStatus"/>
        <result column="BLACKLIST" jdbcType="VARCHAR" property="blacklist"/>
        <result column="BLACKLIST_REASON" jdbcType="VARCHAR" property="blacklistReason"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="getBlackList" resultMap="BaseBlackMap">
        select f.* from (
        select
        ID,
        PERSON_NAME,
        PERSON_SEX,
        WORKING_COMPANY,
        ORG_GROUP_NAME,
        TECHNICAL_DIRECTION,
        POST,
        EDUCATION,
        MAJOR,
        TEL,
        PERSON_BIRTHDAY,
        ENTRY_DATE,
        TRIAL_STATUS,
        BLACKLIST,
        BLACKLIST_REASON,
        INST_ID,
        '0' as type,
        CREATE_TIME,
        UPDATE_TIME
        from
        UOMP_PERSON_INFO
        where DEL_FLAG = '0' and BLACKLIST = '1'

        union all

        select
        ID,
        PERSON_NAME,
        '',
        WORKING_COMPANY,
        '',
        '',
        '',
        '',
        '',
        TEL,
        '',
        '',
        '',
        BLACKLIST,
        BLACKLIST_REASON,
        INST_ID,
        '1' as type,
        CREATE_TIME,
        UPDATE_TIME
        from UOMP_TEMP_ADMISSION
        where ID in (select MAX(ID) as ID from UOMP_TEMP_ADMISSION WHERE BLACKLIST = '1' and DEL_FLAG = '0' and APPLY_STATUS = '2' GROUP by PERSON_NAME,PERSON_CARD)
        ) f
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="selectAllByPersonCardAndTrialStatus" resultType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UOMP_PERSON_INFO
        where PERSON_CARD = #{personCard}
          and DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
    </select>

    <select id="selectIdAndAccountByPersonNameAndPersonCard" resultType="java.lang.String">
        select ID as id
        from UOMP_PERSON_INFO
        where PERSON_NAME = #{personName}
          AND PERSON_CARD = #{personCard}
          and DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
    </select>

    <select id="selectOneByPersonNameAndPersonCard" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from UOMP_PERSON_INFO
        where PERSON_NAME = #{personName}
          AND PERSON_CARD = #{personCard}
          and DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
        limit 1
    </select>
    <select id="countByOrg" resultType="java.lang.Integer">
        select count(ID) as count
        from
            UOMP_PERSON_INFO
        where
            DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
          and ORG_GROUP_ID in (select ID_ from uomp_org_group uog where path_ like concat('%', #{orgGroupId}, '%'))
          and BACKGROUND_STATUS != '2'
    </select>
    <select id="getWorkingCompanyTotalAndPass" resultType="cn.gwssi.ecloud.staffpool.dto.PersonCompanyDTO">
        select
            upi.WORKING_COMPANY as name,
            count(*) as total,
            sum(case when upi.BACKGROUND_STATUS = '1' then 1 else 0 end) as num
        from uomp_person_info upi
        inner join (select ID,SUPPLIER_NAME from uomp_supplier_management where DEL_FLAG = '0' and SUPPLIER_STATUS = '1') usm on usm.id = upi.WORKING_COMPANY_ID
        where upi.DEL_FLAG = '0'
        and upi.TRIAL_STATUS = '2'
        and upi.BACKGROUND_STATUS in ('1', '2')
        and upi.BLACKLIST = '0'
		and upi.DIMISSION = '0'
        group by upi.WORKING_COMPANY_ID
    </select>

    <select id="getWorkingCompanyNoPass" resultType="cn.gwssi.ecloud.staffpool.dto.WorkAndPersonDTO">
        select
            usm.ID as workId,
            usm.SUPPLIER_NAME as workName,
            upi.ID as personId,
            upi.PERSON_NAME as personName
        from uomp_person_info upi
        inner join (select ID,SUPPLIER_NAME from uomp_supplier_management where DEL_FLAG = '0' and SUPPLIER_STATUS = '1') usm on usm.id = upi.WORKING_COMPANY_ID
        where upi.DEL_FLAG = '0'
        and upi.TRIAL_STATUS = '2'
        and upi.BACKGROUND_STATUS = '2'
        and upi.BLACKLIST = '0'
		and upi.DIMISSION = '0'
    </select>

    <select id="selectAllByOrg" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        UOMP_PERSON_INFO
        where
        DEL_FLAG = '0'
        and BLACKLIST = '0'
        and TRIAL_STATUS = '2'
        and DIMISSION = '0'
        and BACKGROUND_STATUS != '2'
        and ORG_GROUP_ID in (select ID_ from uomp_org_group uog where path_ like concat('%', #{orgGroupId}, '%') and STATUS_ = '1' )
    </select>

    <select id="selectAllByOrgStatusAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        UOMP_PERSON_INFO
        where
        DEL_FLAG = '0'
        and ORG_GROUP_ID in (select ID_ from uomp_org_group uog where path_ like concat('%', #{orgGroupId}, '%'))
    </select>

    <select id="countByTechnicalDirection" resultType="java.util.Map">
        select count(1), TECHNICAL_DIRECTION
        from uomp_person_info
        where DEL_FLAG = '0'
          and TRIAL_STATUS = '2'
          and BACKGROUND_STATUS = '1'
        group by TECHNICAL_DIRECTION
    </select>

    <select id="selectAllByOrgGroupId" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_person_info
        where DEL_FLAG = '0'
        and TRIAL_STATUS = '2'
        and BACKGROUND_STATUS != '2'
        and BLACKLIST = '0'
        and DIMISSION = '0'
        <if test="technicalDirection != null and technicalDirection != ''">
            and TECHNICAL_DIRECTION = #{technicalDirection}
        </if>
        and ORG_GROUP_ID in
        <foreach collection="orgGroupIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countByEdu" resultType="cn.gwssi.ecloud.staffpool.dto.KeyWordNumDTO">
        select
            EDUCATION as keyWord,
            count(1) as num
        from
            uomp_person_info
        where
            DEL_FLAG = '0'
            and TRIAL_STATUS = '2'
            and DIMISSION = '0'
			and BLACKLIST = '0'
			and BACKGROUND_STATUS != '2'
            and EDUCATION != ''
        group by
            EDUCATION
    </select>

    <select id="countByEntry" resultType="java.lang.String">
        select
           ENTRY_DATE
        from
            uomp_person_info
        where
            DEL_FLAG = '0'
			and TRIAL_STATUS = '2'
			and DIMISSION = '0'
            and BLACKLIST = '0'
			and BACKGROUND_STATUS != '2'
            and ENTRY_DATE != ''
        order by ENTRY_DATE desc
    </select>

    <select id="getOrgUserIdByUserId" resultType="java.lang.String">
        select
            ou.id_ as orgUserId
        from
            uomp_person_info upi
        inner join org_user ou on ou.account_ = upi.ACCOUNT
        where
            upi.ID = #{userId}
    </select>

    <select id="getPersonInTime" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO" databaseId="mysql">
        select TIMESTAMPDIFF(YEAR, ENTRY_DATE, date_format(now(), '%Y-%m-%d %H:%i:%S')) as name, count(*) as num
        FROM uomp_person_info
        where ENTRY_DATE is not null and DEL_FLAG ='0' and TRIAL_STATUS = '2'
        and BLACKLIST = '0'
        and DIMISSION = '0'
        and BACKGROUND_STATUS != '2'
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
        group by name
    </select>
    <select id="getPersonInTime" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO" >
        select TIMESTAMPDIFF(YEAR, ENTRY_DATE, date_format(now(), '%Y-%m-%d %H:%i:%S')) as name, count(*) as num
        FROM uomp_person_info
        where ENTRY_DATE is not null and DEL_FLAG ='0' and TRIAL_STATUS = '2'
        and BLACKLIST = '0'
        and DIMISSION = '0'
        and BACKGROUND_STATUS != '2'
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
        group by TIMESTAMPDIFF(YEAR, ENTRY_DATE, date_format(now(), '%Y-%m-%d %H:%i:%S'))
    </select>

    <select id="getAccountPassList" resultType="cn.gwssi.ecloud.staffpool.dto.AccountPersonDTO">
         select
             upi.PERSON_NAME as personName,
             ou.account_ as account,
             upi.ID as userId,
             ou.id_ as orgUserId,
             upi.ORG_GROUP_ID as groupId,
             upi.ORG_GROUP_NAME as groupName,
             upi.WORKING_COMPANY as workingCompany,
             upi.WORKING_COMPANY_ID as workingCompanyId,
             upi.ENTRY_STATUS as entryStatus,
             ou.status_ as status,
             upi.TEL as tel,
             upi.EMAIL as email,
             uog.ORG_GROUP_ID as orgGroupId
         from
         uomp_person_info upi
         inner join org_user ou on upi.ORG_USER_ID = ou.id_
         inner join uomp_org_group uog on uog.ID_ = upi.ORG_GROUP_ID
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>

<!--auto generated by MybatisCodeHelper on 2024-06-27-->
    <update id="updateEntryStatusByPersonCard">
        update uomp_person_info
        set ENTRY_STATUS=#{updatedEntryStatus,jdbcType=VARCHAR}
        where PERSON_CARD=#{personCard,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2024-06-28-->
    <select id="selectOneByIdOrPersonCard" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uomp_person_info
        where ID=#{id,jdbcType=VARCHAR} or PERSON_CARD=#{personCard,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-07-05-->
    <update id="updateEntryStatusByIdOrPersonCard">
        update uomp_person_info
        set ENTRY_STATUS=#{updatedEntryStatus,jdbcType=VARCHAR}
        where ID=#{id,jdbcType=VARCHAR} or PERSON_CARD=#{personCard,jdbcType=VARCHAR}
    </update>

    <resultMap id="BaseContactMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO">
        <!--@mbg.generated-->
        <!--@Table uomp_person_info-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard"/>
        <result column="PERSON_SEX" jdbcType="VARCHAR" property="personSex"/>
        <result column="PERSON_BIRTHDAY" jdbcType="VARCHAR" property="personBirthday"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="EDUCATION" jdbcType="VARCHAR" property="education"/>
        <result column="POST" jdbcType="VARCHAR" property="post"/>
        <result column="MAJOR" jdbcType="VARCHAR" property="major"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="BLACKLIST" jdbcType="VARCHAR" property="blacklist"/>
        <result column="BLACKLIST_REASON" jdbcType="VARCHAR" property="blacklistReason"/>
        <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate"/>
        <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo"/>
        <result column="TRIAL_STATUS" jdbcType="VARCHAR" property="trialStatus"/>
        <result column="BACKGROUND_STATUS" jdbcType="VARCHAR" property="backgroundStatus"/>
        <result column="ID_TYPE" jdbcType="VARCHAR" property="idType"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="IS_ACCOUNT" jdbcType="VARCHAR" property="isAccount"/>
        <result column="ACCOUNT" jdbcType="VARCHAR" property="account"/>
        <result column="WORKING_COMPANY_ID" jdbcType="VARCHAR" property="workingCompanyId"/>
        <result column="TECHNICAL_DIRECTION" jdbcType="VARCHAR" property="technicalDirection"/>
        <result column="REG_PERMANENT_RESIDENCE" jdbcType="VARCHAR" property="regPermanentResidence"/>
        <result column="NATIONALITY" jdbcType="VARCHAR" property="nationality"/>
        <result column="POLITICS_STATUS" jdbcType="VARCHAR" property="politicsStatus"/>
        <result column="NATION" jdbcType="VARCHAR" property="nation"/>
        <result column="REG_PROVINCE" jdbcType="VARCHAR" property="regProvince"/>
        <result column="REG_CITY" jdbcType="VARCHAR" property="regCity"/>
        <result column="REG_REGION" jdbcType="VARCHAR" property="regRegion"/>
        <result column="AUDIT_DATE" jdbcType="TIMESTAMP" property="auditDate"/>
        <result column="ORG_GROUP_ID" jdbcType="VARCHAR" property="orgGroupId"/>
        <result column="ORG_GROUP_NAME" jdbcType="VARCHAR" property="orgGroupName"/>
        <result column="UPDATING" jdbcType="VARCHAR" property="updating"/>
        <result column="ORG_USER_ID" jdbcType="VARCHAR" property="orgUserId"/>
        <result column="ENTRY_STATUS" jdbcType="VARCHAR" property="entryStatus"/>
        <result column="DIMISSION" jdbcType="VARCHAR" property="dimission"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="SERVICE_LOCATION" jdbcType="VARCHAR" property="serviceLocation"/>
    </resultMap>
    <select id="selectContacts" parameterType="java.util.Map" resultMap="BaseContactMap">
        select i.*, p.SERVICE_LOCATION
        from UOMP_PERSON_INFO i
        left join (select SERVICE_LOCATION, person_id from uomp_admission_person where OUT_TIME is null and OUT_APPLY_STATUS = 0 group by person_id) p on p.person_id = i.id
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="countByTrialStatusAndBlacklistAndBackgroundStatusNotAndDimissionAndEntryStatus" resultType="java.lang.Integer">
        select count(*) from uomp_person_info
        where TRIAL_STATUS = #{trialStatus} and BLACKLIST = #{blacklist} and BACKGROUND_STATUS != #{backgroundStatus} and DIMISSION = #{dimission}
        <if test="entryStatus != null and entryStatus != ''">
            and ENTRY_STATUS = #{entryStatus}
        </if>
    </select>

    <select id="selectAllByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uomp_person_info
        where ORG_USER_ID = #{userId}
    </select>
    <update id="updatePersonCardById" parameterType="java.lang.String">
        update uomp_person_info set PERSON_CARD =#{personCard} where id = #{id}
    </update>
</mapper>
