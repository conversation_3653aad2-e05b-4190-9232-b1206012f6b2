<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonNoCrimeMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    <!--@Table uomp_person_no_crime-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="PROOF_NUMBER" jdbcType="VARCHAR" property="proofNumber" />
    <result column="QUERY_BEGIN_TIME" jdbcType="VARCHAR" property="queryBeginTime" />
    <result column="QUERY_END_TIME" jdbcType="VARCHAR" property="queryEndTime" />
    <result column="PROVIDE_UNIT" jdbcType="VARCHAR" property="provideUnit" />
    <result column="INDATE" jdbcType="VARCHAR" property="indate" />
    <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, PROOF_NUMBER, QUERY_BEGIN_TIME, QUERY_END_TIME, PROVIDE_UNIT, INDATE,
    FILE_INFO, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
    DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_no_crime
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="query" resultMap="BaseResultMap">
    select *
    from uomp_person_no_crime
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
    <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_no_crime
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPersonId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_no_crime
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    insert into uomp_person_no_crime (ID, PERSON_ID, PROOF_NUMBER,
      QUERY_BEGIN_TIME, QUERY_END_TIME, PROVIDE_UNIT,
      INDATE, FILE_INFO, CREATE_BY,
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{proofNumber,jdbcType=VARCHAR},
      #{queryBeginTime,jdbcType=VARCHAR}, #{queryEndTime,jdbcType=VARCHAR}, #{provideUnit,jdbcType=VARCHAR},
      #{indate,jdbcType=VARCHAR}, #{fileInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    insert into uomp_person_no_crime
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="proofNumber != null">
        PROOF_NUMBER,
      </if>
      <if test="queryBeginTime != null">
        QUERY_BEGIN_TIME,
      </if>
      <if test="queryEndTime != null">
        QUERY_END_TIME,
      </if>
      <if test="provideUnit != null">
        PROVIDE_UNIT,
      </if>
      <if test="indate != null">
        INDATE,
      </if>
      <if test="fileInfo != null">
        FILE_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="proofNumber != null">
        #{proofNumber,jdbcType=VARCHAR},
      </if>
      <if test="queryBeginTime != null">
        #{queryBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="queryEndTime != null">
        #{queryEndTime,jdbcType=VARCHAR},
      </if>
      <if test="provideUnit != null">
        #{provideUnit,jdbcType=VARCHAR},
      </if>
      <if test="indate != null">
        #{indate,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    update uomp_person_no_crime
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="proofNumber != null">
        PROOF_NUMBER = #{proofNumber,jdbcType=VARCHAR},
      </if>
      <if test="queryBeginTime != null">
        QUERY_BEGIN_TIME = #{queryBeginTime,jdbcType=VARCHAR},
      </if>
      <if test="queryEndTime != null">
        QUERY_END_TIME = #{queryEndTime,jdbcType=VARCHAR},
      </if>
      <if test="provideUnit != null">
        PROVIDE_UNIT = #{provideUnit,jdbcType=VARCHAR},
      </if>
      <if test="indate != null">
        INDATE = #{indate,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    update uomp_person_no_crime
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      PROOF_NUMBER = #{proofNumber,jdbcType=VARCHAR},
      QUERY_BEGIN_TIME = #{queryBeginTime,jdbcType=VARCHAR},
      QUERY_END_TIME = #{queryEndTime,jdbcType=VARCHAR},
      PROVIDE_UNIT = #{provideUnit,jdbcType=VARCHAR},
      INDATE = #{indate,jdbcType=VARCHAR},
      FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPersonId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime">
    <!--@mbg.generated-->
    update uomp_person_no_crime
    <set>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </update>
    <update id="updateByPersonIds">
      update uomp_person_no_crime set
      DEL_FLAG = '1',
      UPDATE_ORG_ID = #{orgId},
      where PERSON_ID in
      <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </update>
  <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonNoCrimeDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="PROOF_NUMBER" jdbcType="VARCHAR" property="proofNumber" />
    <result column="QUERY_BEGIN_TIME" jdbcType="VARCHAR" property="queryBeginTime" />
    <result column="QUERY_END_TIME" jdbcType="VARCHAR" property="queryEndTime" />
    <result column="PROVIDE_UNIT" jdbcType="VARCHAR" property="provideUnit" />
    <result column="INDATE" jdbcType="VARCHAR" property="indate" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard" />
  </resultMap>
  <select id="selectByPersonIds" resultMap="BasePersonMap">
    select
      a.ID,
      a.PERSON_ID,
      a.PROOF_NUMBER,
      a.QUERY_BEGIN_TIME,
      a.QUERY_END_TIME,
      a.PROVIDE_UNIT,
      a.INDATE,
      b.PERSON_NAME,
      b.PERSON_CARD
    from
      UOMP_PERSON_NO_CRIME a
        left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
    where a.DEL_FLAG = '0' and b.DEL_FLAG ='0' and a.PERSON_ID in
    <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by a.PERSON_ID desc,a.CREATE_TIME desc
  </select>
</mapper>
