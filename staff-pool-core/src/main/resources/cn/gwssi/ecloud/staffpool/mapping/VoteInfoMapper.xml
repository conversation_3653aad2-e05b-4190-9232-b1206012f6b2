<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.VoteInfoMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.VoteInfo">
        <!--@mbg.generated-->
        <!--@Table vote_info-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="STATE" jdbcType="VARCHAR" property="state"/>
        <result column="RELEASE_TIME" jdbcType="TIMESTAMP" property="releaseTime"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="SURVEY_SCOPE" jdbcType="VARCHAR" property="surveyScope"/>
        <result column="ANONYMITY" jdbcType="VARCHAR" property="anonymity"/>
        <result column="RELEASE_PEOPLE_ID" jdbcType="VARCHAR" property="releasePeopleId"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="COLLECTED" jdbcType="VARCHAR" property="collected"/>
        <result column="EXPLAIN_" jdbcType="VARCHAR" property="explain"/>
        <result column="RELEASE_PEOPLE_NAME" jdbcType="VARCHAR" property="releasePeopleName"/>
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="TRAINING_CONTENT" jdbcType="VARCHAR" property="trainingContent"/>
        <result column="TRAINING_SITE" jdbcType="VARCHAR" property="trainingSite"/>
        <result column="TRAINING_DATE" jdbcType="VARCHAR" property="trainingDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, TITLE, `STATE`, RELEASE_TIME, START_TIME, END_TIME, SURVEY_SCOPE, ANONYMITY,
        RELEASE_PEOPLE_ID, `STATUS`, COLLECTED, EXPLAIN_, RELEASE_PEOPLE_NAME, `TYPE`, PROJECT_NAME,
        TRAINING_CONTENT, TRAINING_SITE, TRAINING_DATE
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from vote_info
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from vote_info
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteInfo">
        <!--@mbg.generated-->
        insert into vote_info (ID, TITLE, `STATE`,
        RELEASE_TIME, START_TIME, END_TIME,
        SURVEY_SCOPE, ANONYMITY, RELEASE_PEOPLE_ID,
        `STATUS`, COLLECTED, EXPLAIN_,
        RELEASE_PEOPLE_NAME, `TYPE`, PROJECT_NAME,
        TRAINING_CONTENT, TRAINING_SITE, TRAINING_DATE
        )
        values (#{id,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
        #{releaseTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
        #{surveyScope,jdbcType=VARCHAR}, #{anonymity,jdbcType=VARCHAR}, #{releasePeopleId,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}, #{collected,jdbcType=VARCHAR}, #{explain,jdbcType=VARCHAR},
        #{releasePeopleName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR},
        #{trainingContent,jdbcType=VARCHAR}, #{trainingSite,jdbcType=VARCHAR}, #{trainingDate,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteInfo">
        <!--@mbg.generated-->
        insert into vote_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="title != null">
                TITLE,
            </if>
            <if test="state != null">
                `STATE`,
            </if>
            <if test="releaseTime != null">
                RELEASE_TIME,
            </if>
            <if test="startTime != null">
                START_TIME,
            </if>
            <if test="endTime != null">
                END_TIME,
            </if>
            <if test="surveyScope != null">
                SURVEY_SCOPE,
            </if>
            <if test="anonymity != null">
                ANONYMITY,
            </if>
            <if test="releasePeopleId != null">
                RELEASE_PEOPLE_ID,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="collected != null">
                COLLECTED,
            </if>
            <if test="explain != null">
                EXPLAIN_,
            </if>
            <if test="releasePeopleName != null">
                RELEASE_PEOPLE_NAME,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="projectName != null">
                PROJECT_NAME,
            </if>
            <if test="trainingContent != null">
                TRAINING_CONTENT,
            </if>
            <if test="trainingSite != null">
                TRAINING_SITE,
            </if>
            <if test="trainingDate != null">
                TRAINING_DATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="releaseTime != null">
                #{releaseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyScope != null">
                #{surveyScope,jdbcType=VARCHAR},
            </if>
            <if test="anonymity != null">
                #{anonymity,jdbcType=VARCHAR},
            </if>
            <if test="releasePeopleId != null">
                #{releasePeopleId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="collected != null">
                #{collected,jdbcType=VARCHAR},
            </if>
            <if test="explain != null">
                #{explain,jdbcType=VARCHAR},
            </if>
            <if test="releasePeopleName != null">
                #{releasePeopleName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="trainingContent != null">
                #{trainingContent,jdbcType=VARCHAR},
            </if>
            <if test="trainingSite != null">
                #{trainingSite,jdbcType=VARCHAR},
            </if>
            <if test="trainingDate != null">
                #{trainingDate,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteInfo">
        <!--@mbg.generated-->
        update vote_info
        <set>
            <if test="title != null">
                TITLE = #{title,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `STATE` = #{state,jdbcType=VARCHAR},
            </if>
            <if test="releaseTime != null">
                RELEASE_TIME = #{releaseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                START_TIME = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyScope != null">
                SURVEY_SCOPE = #{surveyScope,jdbcType=VARCHAR},
            </if>
            <if test="anonymity != null">
                ANONYMITY = #{anonymity,jdbcType=VARCHAR},
            </if>
            <if test="releasePeopleId != null">
                RELEASE_PEOPLE_ID = #{releasePeopleId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="collected != null">
                COLLECTED = #{collected,jdbcType=VARCHAR},
            </if>
            <if test="explain != null">
                EXPLAIN_ = #{explain,jdbcType=VARCHAR},
            </if>
            <if test="releasePeopleName != null">
                RELEASE_PEOPLE_NAME = #{releasePeopleName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="trainingContent != null">
                TRAINING_CONTENT = #{trainingContent,jdbcType=VARCHAR},
            </if>
            <if test="trainingSite != null">
                TRAINING_SITE = #{trainingSite,jdbcType=VARCHAR},
            </if>
            <if test="trainingDate != null">
                TRAINING_DATE = #{trainingDate,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.VoteInfo">
        <!--@mbg.generated-->
        update vote_info
        set TITLE = #{title,jdbcType=VARCHAR},
        `STATE` = #{state,jdbcType=VARCHAR},
        RELEASE_TIME = #{releaseTime,jdbcType=TIMESTAMP},
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
        SURVEY_SCOPE = #{surveyScope,jdbcType=VARCHAR},
        ANONYMITY = #{anonymity,jdbcType=VARCHAR},
        RELEASE_PEOPLE_ID = #{releasePeopleId,jdbcType=VARCHAR},
        `STATUS` = #{status,jdbcType=VARCHAR},
        COLLECTED = #{collected,jdbcType=VARCHAR},
        EXPLAIN_ = #{explain,jdbcType=VARCHAR},
        RELEASE_PEOPLE_NAME = #{releasePeopleName,jdbcType=VARCHAR},
        `TYPE` = #{type,jdbcType=VARCHAR},
        PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
        TRAINING_CONTENT = #{trainingContent,jdbcType=VARCHAR},
        TRAINING_SITE = #{trainingSite,jdbcType=VARCHAR},
        TRAINING_DATE = #{trainingDate,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectVoteInfoByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto">
        select vote.title           title,
               answer.update_time   fillTime,
               answer.review_result reviewResult,
               answer.id            answerId,
               vote.id              voteId
        from VOTE_USER_ANSWER answer
                 left join VOTE_INFO vote on answer.vote_id = vote.id
        where vote.type = '3'
          and answer.answer_status = '1'
          and answer.user_id = #{userId}
        order by update_time desc
    </select>

    <select id="selectVoteInfo" resultType="cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto">
        select vote.title           title,
               answer.update_time   fillTime,
               answer.review_result reviewResult,
               answer.id            answerId,
               vote.id              voteId
        from VOTE_USER_ANSWER answer
                 left join VOTE_INFO vote on answer.vote_id = vote.id
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>
</mapper>
