<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPersonTechnologyMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    <!--@Table uomp_person_technology-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="GET_TIME" jdbcType="VARCHAR" property="getTime" />
    <result column="QUALIFTY_NAME" jdbcType="VARCHAR" property="qualiftyName" />
    <result column="QUALIFTY_TYPE" jdbcType="VARCHAR" property="qualiftyType" />
    <result column="CERTIFICATION_BODY" jdbcType="VARCHAR" property="certificationBody" />
    <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="START_TIME" jdbcType="VARCHAR" property="startTime" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PERSON_ID, GET_TIME, QUALIFTY_NAME, QUALIFTY_TYPE, CERTIFICATION_BODY, FILE_INFO,
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG,
    START_TIME, END_TIME
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_person_technology
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="query" resultMap="BaseResultMap">
    select *
    from uomp_person_technology
    <where>
      <if test="whereSql != null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql != null">
      ORDER BY ${orderBySql}
    </if>
  </select>
    <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_technology
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByPersonId" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_person_technology
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    insert into uomp_person_technology (ID, PERSON_ID, GET_TIME,
      QUALIFTY_NAME, QUALIFTY_TYPE, CERTIFICATION_BODY,
      FILE_INFO, CREATE_BY, CREATE_TIME,
      CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME,
      UPDATE_ORG_ID, DEL_FLAG, START_TIME,
      END_TIME)
    values (#{id,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR}, #{getTime,jdbcType=VARCHAR},
      #{qualiftyName,jdbcType=VARCHAR}, #{qualiftyType,jdbcType=VARCHAR}, #{certificationBody,jdbcType=VARCHAR},
      #{fileInfo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}, #{startTime,jdbcType=VARCHAR},
      #{endTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    insert into uomp_person_technology
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="getTime != null">
        GET_TIME,
      </if>
      <if test="qualiftyName != null">
        QUALIFTY_NAME,
      </if>
      <if test="qualiftyType != null">
        QUALIFTY_TYPE,
      </if>
      <if test="certificationBody != null">
        CERTIFICATION_BODY,
      </if>
      <if test="fileInfo != null">
        FILE_INFO,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="getTime != null">
        #{getTime,jdbcType=VARCHAR},
      </if>
      <if test="qualiftyName != null">
        #{qualiftyName,jdbcType=VARCHAR},
      </if>
      <if test="qualiftyType != null">
        #{qualiftyType,jdbcType=VARCHAR},
      </if>
      <if test="certificationBody != null">
        #{certificationBody,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    update uomp_person_technology
    <set>
      <if test="personId != null">
        PERSON_ID = #{personId,jdbcType=VARCHAR},
      </if>
      <if test="getTime != null">
        GET_TIME = #{getTime,jdbcType=VARCHAR},
      </if>
      <if test="qualiftyName != null">
        QUALIFTY_NAME = #{qualiftyName,jdbcType=VARCHAR},
      </if>
      <if test="qualiftyType != null">
        QUALIFTY_TYPE = #{qualiftyType,jdbcType=VARCHAR},
      </if>
      <if test="certificationBody != null">
        CERTIFICATION_BODY = #{certificationBody,jdbcType=VARCHAR},
      </if>
      <if test="fileInfo != null">
        FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    update uomp_person_technology
    set PERSON_ID = #{personId,jdbcType=VARCHAR},
      GET_TIME = #{getTime,jdbcType=VARCHAR},
      QUALIFTY_NAME = #{qualiftyName,jdbcType=VARCHAR},
      QUALIFTY_TYPE = #{qualiftyType,jdbcType=VARCHAR},
      CERTIFICATION_BODY = #{certificationBody,jdbcType=VARCHAR},
      FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      START_TIME = #{startTime,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPersonId" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology">
    <!--@mbg.generated-->
    update uomp_person_technology
    <set>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where PERSON_ID = #{personId,jdbcType=VARCHAR}
  </update>
    <update id="updateByPersonIds">
      update uomp_person_technology set
      DEL_FLAG = '1',
      UPDATE_ORG_ID = #{orgId},
      where PERSON_ID in
      <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </update>
  <resultMap id="BasePersonMap" type="cn.gwssi.ecloud.staffpool.dto.UompPersonTechnologyDto">
    <!--@mbg.generated-->
    <!--@Table uomp_person_educational-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="GET_TIME" jdbcType="VARCHAR" property="getTime" />
    <result column="QUALIFTY_NAME" jdbcType="VARCHAR" property="qualiftyName" />
    <result column="QUALIFTY_TYPE" jdbcType="VARCHAR" property="qualiftyType" />
    <result column="CERTIFICATION_BODY" jdbcType="VARCHAR" property="certificationBody" />
    <result column="START_TIME" jdbcType="VARCHAR" property="startTime" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard" />
  </resultMap>
  <select id="selectByPersonIds" resultMap="BasePersonMap">
    select
    a.ID,
    a.PERSON_ID,
    a.GET_TIME,
    a.QUALIFTY_NAME,
    a.QUALIFTY_TYPE,
    a.CERTIFICATION_BODY,
    b.PERSON_NAME,
    b.PERSON_CARD,
    a.START_TIME,
    a.END_TIME
    from
    UOMP_PERSON_TECHNOLOGY a
    left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
    where a.DEL_FLAG = '0' and b.DEL_FLAG ='0' and a.PERSON_ID in
    <foreach collection="personIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by a.PERSON_ID desc,a.CREATE_TIME desc
  </select>

</mapper>
