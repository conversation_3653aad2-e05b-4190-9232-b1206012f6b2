<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompAdmissionPersonMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson">
        <!--@mbg.generated-->
        <!--@Table uomp_admission_person-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="APPLY_ID" jdbcType="VARCHAR" property="applyId"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard"/>
        <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate"/>
        <result column="EDUCATION" jdbcType="VARCHAR" property="education"/>
        <result column="TECHNICAL_POST" jdbcType="VARCHAR" property="technicalPost"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="ENGAGEMENT_PROJECT" jdbcType="VARCHAR" property="engagementProject"/>
        <result column="SCORE" jdbcType="VARCHAR" property="score"/>
        <result column="INTERVIEW_RESULT" jdbcType="VARCHAR" property="interviewResult"/>
        <result column="MAINTENANCE_GROUP_ID" jdbcType="VARCHAR" property="maintenanceGroupId"/>
        <result column="MAINTENANCE_GROUP_NAME" jdbcType="VARCHAR" property="maintenanceGroupName"/>
        <result column="POST_ID" jdbcType="VARCHAR" property="postId"/>
        <result column="POST_NAME" jdbcType="VARCHAR" property="postName"/>
        <result column="JOB_CONTENT" jdbcType="VARCHAR" property="jobContent"/>
        <result column="IS_EXPORT" jdbcType="VARCHAR" property="isExport"/>
        <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus"/>
        <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime"/>
        <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime"/>
        <result column="OUT_APPLY_STATUS" jdbcType="VARCHAR" property="outApplyStatus"/>
        <result column="FILE_INFO" jdbcType="VARCHAR" property="fileInfo"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="POST_FILE_INFO" jdbcType="VARCHAR" property="postFileInfo"/>
        <result column="SERVIC_TYPE" jdbcType="VARCHAR" property="servicType"/>
        <result column="SERVICE_LOCATION" jdbcType="VARCHAR" property="serviceLocation"/>
        <result column="IS_CLASSIFIED_POSITION" jdbcType="VARCHAR" property="isClassifiedPosition"/>
        <result column="TECHNICAL_DIRECTION" jdbcType="VARCHAR" property="technicalDirection"/>
        <result column="ENGAGEMENT_PROJECT_ID" jdbcType="VARCHAR" property="engagementProjectId"/>
        <result column="ENGAGEMENT_PROJECT_JSON" jdbcType="VARCHAR" property="engagementProjectJson"/>
        <result column="MAINTENANCE_GROUP_JSON" jdbcType="VARCHAR" property="maintenanceGroupJson"/>
        <result column="PLAN_VISIT_TIME" jdbcType="VARCHAR" property="planVisitTime"/>
        <result column="PLAN_OUT_TIME" jdbcType="VARCHAR" property="planOutTime"/>
        <result column="PERSON_ROLE" jdbcType="VARCHAR" property="personRole"/>
        <result column="PERSON_ROLE_ID" jdbcType="VARCHAR" property="personRoleId"/>
        <result column="PERSON_ROLE_JSON" jdbcType="VARCHAR" property="personRoleJson"/>
        <result column="DEPART_NAME" jdbcType="VARCHAR" property="departName"/>
        <result column="DEPART_ID" jdbcType="VARCHAR" property="departId"/>
        <result column="DEPART_JSON" jdbcType="VARCHAR" property="departJson"/>
        <result column="POSITION_TYPE" jdbcType="VARCHAR" property="positionType"/>
        <result column="CLASSIFIED_POSITION" jdbcType="VARCHAR" property="classifiedPosition"/>
        <result column="POST_JSON" jdbcType="VARCHAR" property="postJson"/>
        <result column="WORKING_COMPANY_ID" jdbcType="VARCHAR" property="workingCompanyId"/>
        <result column="WORKING_COMPANY_JSON" jdbcType="VARCHAR" property="workingCompanyJson"/>
        <result column="PERSON_ID" jdbcType="VARCHAR" property="personId"/>
        <result column="SEX" jdbcType="VARCHAR" property="sex"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        APPLY_ID,
        PERSON_NAME,
        PERSON_CARD,
        ENTRY_DATE,
        EDUCATION,
        TECHNICAL_POST,
        WORKING_COMPANY,
        TEL,
        ENGAGEMENT_PROJECT,
        SCORE,
        INTERVIEW_RESULT,
        MAINTENANCE_GROUP_ID,
        MAINTENANCE_GROUP_NAME,
        POST_ID,
        POST_NAME,
        JOB_CONTENT,
        IS_EXPORT,
        APPLY_STATUS,
        IN_TIME,
        OUT_TIME,
        OUT_APPLY_STATUS,
        FILE_INFO,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG,
        POST_FILE_INFO,
        SERVIC_TYPE,
        SERVICE_LOCATION,
        IS_CLASSIFIED_POSITION,
        TECHNICAL_DIRECTION,
        ENGAGEMENT_PROJECT_ID,
        ENGAGEMENT_PROJECT_JSON,
        MAINTENANCE_GROUP_JSON,
        PLAN_VISIT_TIME,
        PLAN_OUT_TIME,
        PERSON_ROLE,
        PERSON_ROLE_ID,
        PERSON_ROLE_JSON,
        DEPART_NAME,
        DEPART_ID,
        DEPART_JSON,
        POSITION_TYPE,
        CLASSIFIED_POSITION,
        POST_JSON,
        WORKING_COMPANY_ID,
        WORKING_COMPANY_JSON,
        PERSON_ID,
        SEX
    </sql>
    <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from uomp_admission_person
        where ID = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="remove" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from uomp_admission_person
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson">
        <!--@mbg.generated-->
        insert into uomp_admission_person (ID, APPLY_ID, PERSON_NAME,
                                           PERSON_CARD, ENTRY_DATE, EDUCATION,
                                           TECHNICAL_POST, WORKING_COMPANY, TEL,
                                           ENGAGEMENT_PROJECT, SCORE, INTERVIEW_RESULT,
                                           MAINTENANCE_GROUP_ID, MAINTENANCE_GROUP_NAME,
                                           POST_ID, POST_NAME, JOB_CONTENT,
                                           IS_EXPORT, APPLY_STATUS, IN_TIME,
                                           OUT_TIME, OUT_APPLY_STATUS, FILE_INFO,
                                           CREATE_BY, CREATE_TIME, CREATE_ORG_ID,
                                           UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID,
                                           DEL_FLAG, POST_FILE_INFO, SERVIC_TYPE,
                                           SERVICE_LOCATION, IS_CLASSIFIED_POSITION,
                                           TECHNICAL_DIRECTION, ENGAGEMENT_PROJECT_ID,
                                           ENGAGEMENT_PROJECT_JSON, MAINTENANCE_GROUP_JSON, PLAN_VISIT_TIME,
                                           PLAN_OUT_TIME, PERSON_ROLE, PERSON_ROLE_ID, PERSON_ROLE_JSON,
                                           DEPART_NAME, DEPART_ID, DEPART_JSON, POSITION_TYPE,
                                           CLASSIFIED_POSITION, POST_JSON, WORKING_COMPANY_ID, WORKING_COMPANY_JSON,
                                           PERSON_ID, SEX)
        values (#{id,jdbcType=VARCHAR}, #{applyId,jdbcType=VARCHAR}, #{personName,jdbcType=VARCHAR},
                #{personCard,jdbcType=VARCHAR}, #{entryDate,jdbcType=VARCHAR}, #{education,jdbcType=VARCHAR},
                #{technicalPost,jdbcType=VARCHAR}, #{workingCompany,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR},
                #{engagementProject,jdbcType=VARCHAR}, #{score,jdbcType=VARCHAR}, #{interviewResult,jdbcType=VARCHAR},
                #{maintenanceGroupId,jdbcType=VARCHAR}, #{maintenanceGroupName,jdbcType=VARCHAR},
                #{postId,jdbcType=VARCHAR}, #{postName,jdbcType=VARCHAR}, #{jobContent,jdbcType=VARCHAR},
                #{isExport,jdbcType=VARCHAR}, #{applyStatus,jdbcType=VARCHAR}, #{inTime,jdbcType=TIMESTAMP},
                #{outTime,jdbcType=TIMESTAMP}, #{outApplyStatus,jdbcType=VARCHAR}, #{fileInfo,jdbcType=VARCHAR},
                #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR},
                #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR},
                #{delFlag,jdbcType=VARCHAR}, #{postFileInfo,jdbcType=VARCHAR}, #{servicType,jdbcType=VARCHAR},
                #{serviceLocation,jdbcType=VARCHAR}, #{isClassifiedPosition,jdbcType=VARCHAR},
                #{technicalDirection,jdbcType=VARCHAR}, #{engagementProjectId,jdbcType=VARCHAR},
                #{engagementProjectJson,jdbcType=VARCHAR}, #{maintenanceGroupJson,jdbcType=VARCHAR},
                #{planVisitTime,jdbcType=VARCHAR}
                   , #{planOutTime,jdbcType=VARCHAR}, #{personRole,jdbcType=VARCHAR}, #{personRoleId,jdbcType=VARCHAR}
                   , #{personRoleJson,jdbcType=VARCHAR}, #{departName,jdbcType=VARCHAR}, #{departId,jdbcType=VARCHAR}
                   , #{departJson,jdbcType=VARCHAR}, #{positionType,jdbcType=VARCHAR},
                #{classifiedPosition,jdbcType=VARCHAR}
                   , #{postJson,jdbcType=VARCHAR}, #{workingCompanyId,jdbcType=VARCHAR},
                #{workingCompanyJson,jdbcType=VARCHAR}
                   , #{personId,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson">
        <!--@mbg.generated-->
        insert into uomp_admission_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="applyId != null">
                APPLY_ID,
            </if>
            <if test="personName != null">
                PERSON_NAME,
            </if>
            <if test="personCard != null">
                PERSON_CARD,
            </if>
            <if test="entryDate != null">
                ENTRY_DATE,
            </if>
            <if test="education != null">
                EDUCATION,
            </if>
            <if test="technicalPost != null">
                TECHNICAL_POST,
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY,
            </if>
            <if test="tel != null">
                TEL,
            </if>
            <if test="engagementProject != null">
                ENGAGEMENT_PROJECT,
            </if>
            <if test="score != null">
                SCORE,
            </if>
            <if test="interviewResult != null">
                INTERVIEW_RESULT,
            </if>
            <if test="maintenanceGroupId != null">
                MAINTENANCE_GROUP_ID,
            </if>
            <if test="maintenanceGroupName != null">
                MAINTENANCE_GROUP_NAME,
            </if>
            <if test="postId != null">
                POST_ID,
            </if>
            <if test="postName != null">
                POST_NAME,
            </if>
            <if test="jobContent != null">
                JOB_CONTENT,
            </if>
            <if test="isExport != null">
                IS_EXPORT,
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS,
            </if>
            <if test="inTime != null">
                IN_TIME,
            </if>
            <if test="outTime != null">
                OUT_TIME,
            </if>
            <if test="outApplyStatus != null">
                OUT_APPLY_STATUS,
            </if>
            <if test="fileInfo != null">
                FILE_INFO,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
            <if test="postFileInfo != null">
                POST_FILE_INFO,
            </if>
            <if test="servicType != null">
                SERVIC_TYPE,
            </if>
            <if test="serviceLocation != null">
                SERVICE_LOCATION,
            </if>
            <if test="isClassifiedPosition != null">
                IS_CLASSIFIED_POSITION,
            </if>
            <if test="technicalDirection != null">
                TECHNICAL_DIRECTION,
            </if>
            <if test="engagementProjectId != null">
                ENGAGEMENT_PROJECT_ID,
            </if>
            <if test="engagementProjectJson != null">
                ENGAGEMENT_PROJECT_JSON,
            </if>
            <if test="maintenanceGroupJson != null">
                MAINTENANCE_GROUP_JSON,
            </if>
            <if test="planVisitTime != null">
                PLAN_VISIT_TIME,
            </if>
            <if test="planOutTime != null">
                PLAN_OUT_TIME,
            </if>
            <if test="personRole != null">
                PERSON_ROLE,
            </if>
            <if test="personRoleId != null">
                PERSON_ROLE_ID,
            </if>
            <if test="personRoleJson != null">
                PERSON_ROLE_JSON,
            </if>
            <if test="departName != null">
                DEPART_NAME,
            </if>
            <if test="departId != null">
                DEPART_ID,
            </if>
            <if test="departJson != null">
                DEPART_JSON,
            </if>
            <if test="positionType != null">
                POSITION_TYPE,
            </if>
            <if test="classifiedPosition != null">
                CLASSIFIED_POSITION,
            </if>
            <if test="postJson != null">
                POST_JSON,
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID,
            </if>
            <if test="workingCompanyJson != null">
                WORKING_COMPANY_JSON,
            </if>
            <if test="personId != null">
                PERSON_ID,
            </if>
            <if test="sex != null">
                SEX,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyId != null">
                #{applyId,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="entryDate != null">
                #{entryDate,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                #{education,jdbcType=VARCHAR},
            </if>
            <if test="technicalPost != null">
                #{technicalPost,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="engagementProject != null">
                #{engagementProject,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=VARCHAR},
            </if>
            <if test="interviewResult != null">
                #{interviewResult,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupId != null">
                #{maintenanceGroupId,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupName != null">
                #{maintenanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                #{postId,jdbcType=VARCHAR},
            </if>
            <if test="postName != null">
                #{postName,jdbcType=VARCHAR},
            </if>
            <if test="jobContent != null">
                #{jobContent,jdbcType=VARCHAR},
            </if>
            <if test="isExport != null">
                #{isExport,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTime != null">
                #{inTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outTime != null">
                #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outApplyStatus != null">
                #{outApplyStatus,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="postFileInfo != null">
                #{postFileInfo,jdbcType=VARCHAR},
            </if>
            <if test="servicType != null">
                #{servicType,jdbcType=VARCHAR},
            </if>
            <if test="serviceLocation != null">
                #{serviceLocation,jdbcType=VARCHAR},
            </if>
            <if test="isClassifiedPosition != null">
                #{isClassifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="technicalDirection != null">
                #{technicalDirection,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectId != null">
                #{engagementProjectId,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectJson != null">
                #{engagementProjectJson,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupJson != null">
                #{maintenanceGroupJson,jdbcType=VARCHAR},
            </if>
            <if test="planVisitTime != null">
                #{planVisitTime,jdbcType=VARCHAR},
            </if>
            <if test="planOutTime != null">
                #{planOutTime,jdbcType=VARCHAR},
            </if>
            <if test="personRole != null">
                #{personRole,jdbcType=VARCHAR},
            </if>
            <if test="personRoleId != null">
                #{personRoleId,jdbcType=VARCHAR},
            </if>
            <if test="personRoleJson != null">
                #{personRoleJson,jdbcType=VARCHAR},
            </if>
            <if test="departName != null">
                #{departName,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                #{departId,jdbcType=VARCHAR},
            </if>
            <if test="departJson != null">
                #{departJson,jdbcType=VARCHAR},
            </if>
            <if test="positionType != null">
                #{positionType,jdbcType=VARCHAR},
            </if>
            <if test="classifiedPosition != null">
                #{classifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="postJson != null">
                #{postJson,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyJson != null">
                #{workingCompanyJson,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                #{personId,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson">
        <!--@mbg.generated-->
        update uomp_admission_person
        set APPLY_ID                = #{applyId,jdbcType=VARCHAR},
            PERSON_NAME             = #{personName,jdbcType=VARCHAR},
            PERSON_CARD             = #{personCard,jdbcType=VARCHAR},
            ENTRY_DATE              = #{entryDate,jdbcType=VARCHAR},
            EDUCATION               = #{education,jdbcType=VARCHAR},
            TECHNICAL_POST          = #{technicalPost,jdbcType=VARCHAR},
            WORKING_COMPANY         = #{workingCompany,jdbcType=VARCHAR},
            TEL                     = #{tel,jdbcType=VARCHAR},
            ENGAGEMENT_PROJECT      = #{engagementProject,jdbcType=VARCHAR},
            SCORE                   = #{score,jdbcType=VARCHAR},
            INTERVIEW_RESULT        = #{interviewResult,jdbcType=VARCHAR},
            MAINTENANCE_GROUP_ID    = #{maintenanceGroupId,jdbcType=VARCHAR},
            MAINTENANCE_GROUP_NAME  = #{maintenanceGroupName,jdbcType=VARCHAR},
            POST_ID                 = #{postId,jdbcType=VARCHAR},
            POST_NAME               = #{postName,jdbcType=VARCHAR},
            JOB_CONTENT             = #{jobContent,jdbcType=VARCHAR},
            IS_EXPORT               = #{isExport,jdbcType=VARCHAR},
            APPLY_STATUS            = #{applyStatus,jdbcType=VARCHAR},
            IN_TIME                 = #{inTime,jdbcType=TIMESTAMP},
            OUT_TIME                = #{outTime,jdbcType=TIMESTAMP},
            OUT_APPLY_STATUS        = #{outApplyStatus,jdbcType=VARCHAR},
            FILE_INFO               = #{fileInfo,jdbcType=VARCHAR},
            CREATE_BY               = #{createBy,jdbcType=VARCHAR},
            CREATE_TIME             = #{createTime,jdbcType=TIMESTAMP},
            CREATE_ORG_ID           = #{createOrgId,jdbcType=VARCHAR},
            UPDATE_BY               = #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME             = #{updateTime,jdbcType=TIMESTAMP},
            UPDATE_ORG_ID           = #{updateOrgId,jdbcType=VARCHAR},
            DEL_FLAG                = #{delFlag,jdbcType=VARCHAR},
            POST_FILE_INFO          = #{postFileInfo,jdbcType=VARCHAR},
            SERVIC_TYPE             = #{servicType,jdbcType=VARCHAR},
            SERVICE_LOCATION        = #{serviceLocation,jdbcType=VARCHAR},
            IS_CLASSIFIED_POSITION  = #{isClassifiedPosition,jdbcType=VARCHAR},
            TECHNICAL_DIRECTION     = #{technicalDirection,jdbcType=VARCHAR},
            ENGAGEMENT_PROJECT_ID   = #{engagementProjectId,jdbcType=VARCHAR},
            ENGAGEMENT_PROJECT_JSON = #{engagementProjectJson,jdbcType=VARCHAR},
            MAINTENANCE_GROUP_JSON  = #{maintenanceGroupJson,jdbcType=VARCHAR},
            PLAN_VISIT_TIME         = #{planVisitTime,jdbcType=VARCHAR},
            PLAN_OUT_TIME           = #{planOutTime,jdbcType=VARCHAR},
            PERSON_ROLE             = #{personRole,jdbcType=VARCHAR},
            PERSON_ROLE_ID          = #{personRoleId,jdbcType=VARCHAR},
            PERSON_ROLE_JSON        = #{personRoleJson,jdbcType=VARCHAR},
            DEPART_NAME             = #{departName,jdbcType=VARCHAR},
            DEPART_ID               = #{departId,jdbcType=VARCHAR},
            DEPART_JSON             = #{departJson,jdbcType=VARCHAR},
            POSITION_TYPE           = #{positionType,jdbcType=VARCHAR},
            CLASSIFIED_POSITION     = #{classifiedPosition,jdbcType=VARCHAR},
            POST_JSON               = #{postJson,jdbcType=VARCHAR},
            WORKING_COMPANY_ID      = #{workingCompanyId,jdbcType=VARCHAR},
            WORKING_COMPANY_JSON    = #{workingCompanyJson,jdbcType=VARCHAR},
            PERSON_ID               = #{personId,jdbcType=VARCHAR},
            SEX                     = #{sex,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="query" resultMap="BaseResultMap">
        select *
        from uomp_admission_person
        <where>
            <if test="whereSql != null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql != null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="selectAllByEntryPersonSelect" resultType="cn.gwssi.ecloud.staffpool.dto.EntryPersonSelectDTO">
        select a.ID                   as id,
               a.PERSON_ID            as personId,
               a.PERSON_NAME          as personName,
               a.PERSON_CARD          as personCard,
               a.TEL                  as tel,
               a.ENTRY_DATE           as entryDate,
               a.WORKING_COMPANY      as workingCompany,
               a.WORKING_COMPANY_ID   as workingCompanyId,
               a.WORKING_COMPANY_JSON as workingCompanyJson,
               b.TECHNICAL_DIRECTION  as postId
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD and b.DEL_FLAG = '0'
        where a.ID in (select MAX(ID)
                       from UOMP_ADMISSION_PERSON
                       where DEL_FLAG = '0'
                         and APPLY_STATUS = '2'
                         and OUT_APPLY_STATUS = '0'
                         and OUT_TIME is null
                       group by PERSON_CARD)
        <if test="ifSupplier == 1">
            and exists (select 1
                        from UOMP_PERSON_INFO
                        where UOMP_PERSON_INFO.PERSON_CARD = a.PERSON_CARD
                          and WORKING_COMPANY_ID = #{supplierId})
        </if>
        order by a.CREATE_TIME desc
    </select>

    <select id="selectPersonCardByApplyId" resultType="java.lang.String">
        select PERSON_CARD
        from UOMP_ADMISSION_PERSON
        where ID = #{id}
    </select>

    <!--  //校验该人员知否存在该项目的申请信息（且未退场） 排除审核不通过，或者草稿状态的
    //查出人员未退场的参与项目-->
    <select id="selectEngagementProjectIdByPersonCard" resultType="java.lang.String">
        select ENGAGEMENT_PROJECT_ID
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          AND PERSON_CARD = #{personCard}
          AND APPLY_STATUS not in ('0', '3')
          AND OUT_TIME IS NULL
    </select>

    <select id="countByPersonCard" resultType="java.lang.Integer">
        select count(*)
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          AND PERSON_CARD = #{personCard}
          AND OUT_APPLY_STATUS = '0'
          AND OUT_TIME IS NULL
          and APPLY_STATUS != '2'
    </select>

    <select id="countByInTime" resultType="java.lang.Integer">
        select count(ID) as num
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          and APPLY_STATUS = '2'
          and IN_TIME like concat(#{time}, '%')
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-05-25-->
    <update id="updateById">
        update uomp_admission_person
        <set>
            <if test="id != null">
                ID = #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyId != null">
                APPLY_ID = #{applyId,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                PERSON_NAME = #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                PERSON_CARD = #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="entryDate != null">
                ENTRY_DATE = #{entryDate,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                EDUCATION = #{education,jdbcType=VARCHAR},
            </if>
            <if test="technicalPost != null">
                TECHNICAL_POST = #{technicalPost,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY = #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                TEL = #{tel,jdbcType=VARCHAR},
            </if>
            <if test="engagementProject != null">
                ENGAGEMENT_PROJECT = #{engagementProject,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                SCORE = #{score,jdbcType=VARCHAR},
            </if>
            <if test="interviewResult != null">
                INTERVIEW_RESULT = #{interviewResult,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupId != null">
                MAINTENANCE_GROUP_ID = #{maintenanceGroupId,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupName != null">
                MAINTENANCE_GROUP_NAME = #{maintenanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                POST_ID = #{postId,jdbcType=VARCHAR},
            </if>
            <if test="postName != null">
                POST_NAME = #{postName,jdbcType=VARCHAR},
            </if>
            <if test="jobContent != null">
                JOB_CONTENT = #{jobContent,jdbcType=VARCHAR},
            </if>
            <if test="isExport != null">
                IS_EXPORT = #{isExport,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS = #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTime != null">
                IN_TIME = #{inTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outTime != null">
                OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outApplyStatus != null">
                OUT_APPLY_STATUS = #{outApplyStatus,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="postFileInfo != null">
                POST_FILE_INFO = #{postFileInfo,jdbcType=VARCHAR},
            </if>
            <if test="servicType != null">
                SERVIC_TYPE = #{servicType,jdbcType=VARCHAR},
            </if>
            <if test="serviceLocation != null">
                SERVICE_LOCATION = #{serviceLocation,jdbcType=VARCHAR},
            </if>
            <if test="isClassifiedPosition != null">
                IS_CLASSIFIED_POSITION = #{isClassifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="technicalDirection != null">
                TECHNICAL_DIRECTION = #{technicalDirection,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectId != null">
                ENGAGEMENT_PROJECT_ID = #{engagementProjectId,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectJson != null">
                ENGAGEMENT_PROJECT_JSON = #{engagementProjectJson,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupJson != null">
                MAINTENANCE_GROUP_JSON = #{maintenanceGroupJson,jdbcType=VARCHAR},
            </if>
            <if test="planVisitTime != null">
                PLAN_VISIT_TIME = #{planVisitTime,jdbcType=VARCHAR},
            </if>
            <if test="planOutTime != null">
                PLAN_OUT_TIME = #{planOutTime,jdbcType=VARCHAR},
            </if>
            <if test="personRole != null">
                PERSON_ROLE = #{personRole,jdbcType=VARCHAR},
            </if>
            <if test="personRoleId != null">
                PERSON_ROLE_ID = #{personRoleId,jdbcType=VARCHAR},
            </if>
            <if test="personRoleJson != null">
                PERSON_ROLE_JSON = #{personRoleJson,jdbcType=VARCHAR},
            </if>
            <if test="departName != null">
                DEPART_NAME = #{departName,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                DEPART_ID = #{departId,jdbcType=VARCHAR},
            </if>
            <if test="departJson != null">
                DEPART_JSON = #{departJson,jdbcType=VARCHAR},
            </if>
            <if test="positionType != null">
                POSITION_TYPE = #{positionType,jdbcType=VARCHAR},
            </if>
            <if test="classifiedPosition != null">
                CLASSIFIED_POSITION = #{classifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="postJson != null">
                POST_JSON = #{postJson,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID = #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyJson != null">
                WORKING_COMPANY_JSON = #{workingCompanyJson,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                PERSON_ID = #{personId,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                SEX = #{sex,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-05-25-->
    <update id="updateByApplyId">
        update uomp_admission_person
        <set>
            <if test="id != null">
                ID = #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyId != null">
                APPLY_ID = #{applyId,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                PERSON_NAME = #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                PERSON_CARD = #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="entryDate != null">
                ENTRY_DATE = #{entryDate,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                EDUCATION = #{education,jdbcType=VARCHAR},
            </if>
            <if test="technicalPost != null">
                TECHNICAL_POST = #{technicalPost,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY = #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                TEL = #{tel,jdbcType=VARCHAR},
            </if>
            <if test="engagementProject != null">
                ENGAGEMENT_PROJECT = #{engagementProject,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                SCORE = #{score,jdbcType=VARCHAR},
            </if>
            <if test="interviewResult != null">
                INTERVIEW_RESULT = #{interviewResult,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupId != null">
                MAINTENANCE_GROUP_ID = #{maintenanceGroupId,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupName != null">
                MAINTENANCE_GROUP_NAME = #{maintenanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                POST_ID = #{postId,jdbcType=VARCHAR},
            </if>
            <if test="postName != null">
                POST_NAME = #{postName,jdbcType=VARCHAR},
            </if>
            <if test="jobContent != null">
                JOB_CONTENT = #{jobContent,jdbcType=VARCHAR},
            </if>
            <if test="isExport != null">
                IS_EXPORT = #{isExport,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS = #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="inTime != null">
                IN_TIME = #{inTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outTime != null">
                OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outApplyStatus != null">
                OUT_APPLY_STATUS = #{outApplyStatus,jdbcType=VARCHAR},
            </if>
            <if test="fileInfo != null">
                FILE_INFO = #{fileInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="postFileInfo != null">
                POST_FILE_INFO = #{postFileInfo,jdbcType=VARCHAR},
            </if>
            <if test="servicType != null">
                SERVIC_TYPE = #{servicType,jdbcType=VARCHAR},
            </if>
            <if test="serviceLocation != null">
                SERVICE_LOCATION = #{serviceLocation,jdbcType=VARCHAR},
            </if>
            <if test="isClassifiedPosition != null">
                IS_CLASSIFIED_POSITION = #{isClassifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="technicalDirection != null">
                TECHNICAL_DIRECTION = #{technicalDirection,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectId != null">
                ENGAGEMENT_PROJECT_ID = #{engagementProjectId,jdbcType=VARCHAR},
            </if>
            <if test="engagementProjectJson != null">
                ENGAGEMENT_PROJECT_JSON = #{engagementProjectJson,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupJson != null">
                MAINTENANCE_GROUP_JSON = #{maintenanceGroupJson,jdbcType=VARCHAR},
            </if>
            <if test="planVisitTime != null">
                PLAN_VISIT_TIME = #{planVisitTime,jdbcType=VARCHAR},
            </if>
            <if test="planOutTime != null">
                PLAN_OUT_TIME = #{planOutTime,jdbcType=VARCHAR},
            </if>
            <if test="personRole != null">
                PERSON_ROLE = #{personRole,jdbcType=VARCHAR},
            </if>
            <if test="personRoleId != null">
                PERSON_ROLE_ID = #{personRoleId,jdbcType=VARCHAR},
            </if>
            <if test="personRoleJson != null">
                PERSON_ROLE_JSON = #{personRoleJson,jdbcType=VARCHAR},
            </if>
            <if test="departName != null">
                DEPART_NAME = #{departName,jdbcType=VARCHAR},
            </if>
            <if test="departId != null">
                DEPART_ID = #{departId,jdbcType=VARCHAR},
            </if>
            <if test="departJson != null">
                DEPART_JSON = #{departJson,jdbcType=VARCHAR},
            </if>
            <if test="positionType != null">
                POSITION_TYPE = #{positionType,jdbcType=VARCHAR},
            </if>
            <if test="classifiedPosition != null">
                CLASSIFIED_POSITION = #{classifiedPosition,jdbcType=VARCHAR},
            </if>
            <if test="postJson != null">
                POST_JSON = #{postJson,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID = #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyJson != null">
                WORKING_COMPANY_JSON = #{workingCompanyJson,jdbcType=VARCHAR},
            </if>
            <if test="personId != null">
                PERSON_ID = #{personId,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                SEX = #{sex,jdbcType=VARCHAR},
            </if>
        </set>
        where APPLY_ID = #{applyId,jdbcType=VARCHAR}
    </update>

    <select id="selectIdByPersonCard" resultType="java.lang.String">
        select ID
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          AND OUT_TIME IS NULL
          and APPLY_STATUS = '2'
          AND PERSON_CARD = #{personCard}
    </select>

    <select id="selectEngagementProjectByPersonCard" resultType="cn.gwssi.ecloud.staffpool.dto.ProjectListBean">
        select ENGAGEMENT_PROJECT as engagementProjectName, ENGAGEMENT_PROJECT_ID as engagementProjectId
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          AND OUT_TIME IS NULL
          and APPLY_STATUS = '2'
          AND PERSON_CARD = #{personCard}
    </select>

    <update id="updateOutApplyStatusByIdIn">
        update UOMP_ADMISSION_PERSON
        set OUT_APPLY_STATUS = '1'
        where id in
        <foreach item="id" collection="applyPersonIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateOutTimeByIdIn">
        update UOMP_ADMISSION_PERSON
        set OUT_TIME = #{dateTime}
        where id in
        <foreach item="id" collection="applyPersonIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectAllBySelective" parameterType="cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO"
            resultType="cn.gwssi.ecloud.staffpool.dto.PersonListDTO">
        select unionPerson2.* from (select *, max(createTime) as createTimes
        from (
        select a.ID                      as id,
               a.PERSON_ID               as personId,
               b.PERSON_NAME             as personName,
               b.PERSON_CARD             as personCard,
               b.TEL                     as tel,
               a.WORKING_COMPANY         as workingCompany,
               a.WORKING_COMPANY_ID      as workingCompanyId,
               a.WORKING_COMPANY_JSON    as workingCompanyJson,
               a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
               a.IN_TIME                 as inTime,
               a.OUT_TIME                as outTime,
               a.OUT_APPLY_STATUS        as outApplyStatus,
               case
                   when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                   when a.OUT_TIME is null then '0'
                   else '1'
                   end                   as entryStatus,
               a.APPLY_STATUS            as applyStatus,
               a.ENGAGEMENT_PROJECT      as engagementProject,
               a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
               a.APPLY_ID                as applyId,
               c.INST_ID                 as instId,
               a.PLAN_VISIT_TIME         as planVisitTime,
               a.PLAN_OUT_TIME           as planOutTime,
               a.PERSON_ROLE             as personRole,
               a.PERSON_ROLE_ID          as personRoleId,
               a.PERSON_ROLE_JSON        as personRoleJson,
               a.DEPART_ID               as departId,
               a.DEPART_NAME             as departName,
               a.DEPART_JSON             as departJson,
               a.POSITION_TYPE           as positionType,
               a.CLASSIFIED_POSITION     as classifiedPosition,
               a.POST_ID                 as postId,
               a.POST_NAME               as postName,
               a.POST_JSON               as postJson,
               a.EDUCATION               as education,
               a.TECHNICAL_DIRECTION     as technicalDirection,
               b.PERSON_SEX              as sex,
               a.CREATE_TIME             as createTime,
               a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
                 left join uomp_admission_application c on a.APPLY_ID = c.ID AND c.DEL_FLAG = '0'
                 inner join bpm_instance bi on c.INST_ID = bi.id_ and bi.status_ != 'discard'
        where a.DEL_FLAG = '0'
          and a.IS_EXPORT = '1'
        <if test="personName != null and personName != ''">
            and b.PERSON_NAME like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="postName != null and postName != ''">
            and b.POST like concat('%', #{postName}, '%')
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and a.WORKING_COMPANY_ID in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.ENGAGEMENT_PROJECT_ID in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and b.WORKING_COMPANY_ID = #{supplierId}
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == -1">
            and a.APPLY_STATUS != '2'
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 0">
            and a.APPLY_STATUS = '2'
            and a.OUT_TIME is null
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 1">
            and a.OUT_TIME is not null
        </if>
        <if test="personCard != null and personCard != ''">
            and b.PERSON_CARD = #{personCard}
        </if>
        <if test="applyStatus != null and applyStatus != ''">
            and a.APPLY_STATUS = #{applyStatus}
        </if>
        <if test="orgGroupIdList != null and orgGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="orgGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createdBy != null and createdBy != ''">
            and a.CREATE_BY = #{createdBy}
        </if>
        union all
        select a.ID                      as id,
               a.PERSON_ID               as personId,
               b.PERSON_NAME             as personName,
               b.PERSON_CARD             as personCard,
               b.TEL                     as tel,
               a.WORKING_COMPANY         as workingCompany,
               a.WORKING_COMPANY_ID      as workingCompanyId,
               a.WORKING_COMPANY_JSON    as workingCompanyJson,
               a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
               a.IN_TIME                 as inTime,
               a.OUT_TIME                as outTime,
               a.OUT_APPLY_STATUS        as outApplyStatus,
               case
                   when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                   when a.OUT_TIME is null then '0'
                   else '1'
                   end                   as entryStatus,
               a.APPLY_STATUS            as applyStatus,
               a.ENGAGEMENT_PROJECT      as engagementProject,
               a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
               a.APPLY_ID                as applyId,
               ''                        as instId,
               a.PLAN_VISIT_TIME         as planVisitTime,
               a.PLAN_OUT_TIME           as planOutTime,
               a.PERSON_ROLE             as personRole,
               a.PERSON_ROLE_ID          as personRoleId,
               a.PERSON_ROLE_JSON        as personRoleJson,
               a.DEPART_ID               as departId,
               a.DEPART_NAME             as departName,
               a.DEPART_JSON             as departJson,
               a.POSITION_TYPE           as positionType,
               a.CLASSIFIED_POSITION     as classifiedPosition,
               a.POST_ID                 as postId,
               b.POST                    as postName,
               a.POST_JSON               as postJson,
               a.EDUCATION               as education,
               a.TECHNICAL_DIRECTION     as technicalDirection,
               b.PERSON_SEX              as sex,
               a.CREATE_TIME             as createTime,
               a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        where a.DEL_FLAG = '0'
          and a.IS_EXPORT = '0'
        <if test="personName != null and personName != ''">
            and b.PERSON_NAME like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="postName != null and postName != ''">
            and b.POST like concat('%', #{postName}, '%')
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and a.WORKING_COMPANY_ID in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.ENGAGEMENT_PROJECT_ID in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and b.WORKING_COMPANY_ID = #{supplierId}
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == -1">
            and a.APPLY_STATUS != '2'
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 0">
            and a.APPLY_STATUS = '2'
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 1">
            and a.OUT_TIME is not null
        </if>
        <if test="personCard != null and personCard != ''">
            and b.PERSON_CARD = #{personCard}
        </if>
        <if test="applyStatus != null and applyStatus != ''">
            and a.APPLY_STATUS = #{applyStatus}
        </if>
        <if test="orgGroupIdList != null and orgGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="orgGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createdBy != null and createdBy != ''">
            and a.CREATE_BY = #{createdBy}
        </if>
        ) as unionPerson
        group by personCard) as unionPerson1
            inner join
        (select a.ID                      as id,
                a.PERSON_ID               as personId,
                b.PERSON_NAME             as personName,
                b.PERSON_CARD             as personCard,
                b.TEL                     as tel,
                a.WORKING_COMPANY         as workingCompany,
                a.WORKING_COMPANY_ID      as workingCompanyId,
                a.WORKING_COMPANY_JSON    as workingCompanyJson,
                a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
                a.IN_TIME                 as inTime,
                a.OUT_TIME                as outTime,
                a.OUT_APPLY_STATUS        as outApplyStatus,
                case
                    when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                    when a.OUT_TIME is null then '0'
                    else '1'
                    end                   as entryStatus,
                a.APPLY_STATUS            as applyStatus,
                a.ENGAGEMENT_PROJECT      as engagementProject,
                a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
                a.APPLY_ID                as applyId,
                c.INST_ID                 as instId,
                a.PLAN_VISIT_TIME         as planVisitTime,
                a.PLAN_OUT_TIME           as planOutTime,
                a.PERSON_ROLE             as personRole,
                a.PERSON_ROLE_ID          as personRoleId,
                a.PERSON_ROLE_JSON        as personRoleJson,
                a.DEPART_ID               as departId,
                a.DEPART_NAME             as departName,
                a.DEPART_JSON             as departJson,
                a.POSITION_TYPE           as positionType,
                a.CLASSIFIED_POSITION     as classifiedPosition,
                a.POST_ID                 as postId,
                b.POST                    as postName,
                a.POST_JSON               as postJson,
                a.EDUCATION               as education,
                a.TECHNICAL_DIRECTION     as technicalDirection,
                b.PERSON_SEX              as sex,
                a.CREATE_TIME             as createTime,
                a.CREATE_BY               as createBy
         from UOMP_ADMISSION_PERSON a
                  left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
                  left join uomp_admission_application c on a.APPLY_ID = c.ID AND c.DEL_FLAG = '0'
                  inner join bpm_instance bi on c.INST_ID = bi.id_ and bi.status_ != 'discard'
         where a.DEL_FLAG = '0'
           and a.IS_EXPORT = '1'
         union all
         select a.ID                      as id,
                a.PERSON_ID               as personId,
                b.PERSON_NAME             as personName,
                b.PERSON_CARD             as personCard,
                b.TEL                     as tel,
                a.WORKING_COMPANY         as workingCompany,
                a.WORKING_COMPANY_ID      as workingCompanyId,
                a.WORKING_COMPANY_JSON    as workingCompanyJson,
                a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
                a.IN_TIME                 as inTime,
                a.OUT_TIME                as outTime,
                a.OUT_APPLY_STATUS        as outApplyStatus,
                case
                    when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                    when a.OUT_TIME is null then '0'
                    else '1'
                    end                   as entryStatus,
                a.APPLY_STATUS            as applyStatus,
                a.ENGAGEMENT_PROJECT      as engagementProject,
                a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
                a.APPLY_ID                as applyId,
                ''                        as instId,
                a.PLAN_VISIT_TIME         as planVisitTime,
                a.PLAN_OUT_TIME           as planOutTime,
                a.PERSON_ROLE             as personRole,
                a.PERSON_ROLE_ID          as personRoleId,
                a.PERSON_ROLE_JSON        as personRoleJson,
                a.DEPART_ID               as departId,
                a.DEPART_NAME             as departName,
                a.DEPART_JSON             as departJson,
                a.POSITION_TYPE           as positionType,
                a.CLASSIFIED_POSITION     as classifiedPosition,
                a.POST_ID                 as postId,
                b.POST                    as postName,
                a.POST_JSON               as postJson,
                a.EDUCATION               as education,
                a.TECHNICAL_DIRECTION     as technicalDirection,
                b.PERSON_SEX              as sex,
                a.CREATE_TIME             as createTime,
                a.CREATE_BY               as createBy
         from UOMP_ADMISSION_PERSON a
                  left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
         where a.DEL_FLAG = '0'
           and a.IS_EXPORT = '0') as unionPerson2
        on unionPerson1.personCard = unionPerson2.personCard and unionPerson1.createTimes = unionPerson2.createTime
        where ((unionPerson2.applyStatus != '0') or
               (unionPerson2.applyStatus = '0' and unionPerson2.createBy = #{createdByStatus}))
        group by unionPerson2.personCard
        order by unionPerson2.createTime desc
    </select>

    <select id="selectAllBySelectivePage" parameterType="cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO"
            resultType="cn.gwssi.ecloud.staffpool.dto.PersonListDTO">
        select * from (
        select unionPerson2.* from (select *, max(createTime) as createTimes
        from (
        select a.ID                      as id,
        a.PERSON_ID               as personId,
        b.PERSON_NAME             as personName,
        b.PERSON_CARD             as personCard,
        b.TEL                     as tel,
        b.WORKING_COMPANY         as workingCompany,
        b.WORKING_COMPANY_ID      as workingCompanyId,
<!--        b.WORKING_COMPANY_JSON    as workingCompanyJson,-->
        a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
        a.IN_TIME                 as inTime,
        a.OUT_TIME                as outTime,
        a.OUT_APPLY_STATUS        as outApplyStatus,
        case
        when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
        when a.OUT_TIME is null then '0'
        else '1'
        end                   as entryStatus,
        a.APPLY_STATUS            as applyStatus,
        a.ENGAGEMENT_PROJECT      as engagementProject,
        a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
        a.APPLY_ID                as applyId,
        c.INST_ID                 as instId,
        a.PLAN_VISIT_TIME         as planVisitTime,
        a.PLAN_OUT_TIME           as planOutTime,
        a.PERSON_ROLE             as personRole,
        a.PERSON_ROLE_ID          as personRoleId,
        a.PERSON_ROLE_JSON        as personRoleJson,
        a.DEPART_ID               as departId,
        a.DEPART_NAME             as departName,
        a.DEPART_JSON             as departJson,
        a.POSITION_TYPE           as positionType,
        a.CLASSIFIED_POSITION     as classifiedPosition,
        a.POST_ID                 as postId,
        a.POST_NAME               as postName,
        a.POST_JSON               as postJson,
        a.EDUCATION               as education,
        a.TECHNICAL_DIRECTION     as technicalDirection,
        b.PERSON_SEX              as sex,
        a.CREATE_TIME             as createTime,
        a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
        left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        left join uomp_admission_application c on a.APPLY_ID = c.ID AND c.DEL_FLAG = '0'
        inner join bpm_instance bi on c.INST_ID = bi.id_ and bi.status_ != 'discard'
        where a.DEL_FLAG = '0'
        and a.IS_EXPORT = '1'
        <if test="personName != null and personName != ''">
            and b.PERSON_NAME like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="postName != null and postName != ''">
            and b.POST like concat('%', #{postName}, '%')
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and b.WORKING_COMPANY_ID in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.ENGAGEMENT_PROJECT_ID in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and b.WORKING_COMPANY_ID = #{supplierId}
        </if>
        <if test="personCard != null and personCard != ''">
            and b.PERSON_CARD = #{personCard}
        </if>
        <if test="applyStatus != null and applyStatus != ''">
            and a.APPLY_STATUS = #{applyStatus}
        </if>
        <if test="orgGroupIdList != null and orgGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="orgGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createdBy != null and createdBy != ''">
            and a.CREATE_BY = #{createdBy}
        </if>
        union all
        select a.ID                      as id,
        a.PERSON_ID               as personId,
        b.PERSON_NAME             as personName,
        b.PERSON_CARD             as personCard,
        b.TEL                     as tel,
        b.WORKING_COMPANY         as workingCompany,
        b.WORKING_COMPANY_ID      as workingCompanyId,
<!--        b.WORKING_COMPANY_JSON    as workingCompanyJson,-->
        a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
        a.IN_TIME                 as inTime,
        a.OUT_TIME                as outTime,
        a.OUT_APPLY_STATUS        as outApplyStatus,
        case
        when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
        when a.OUT_TIME is null then '0'
        else '1'
        end                   as entryStatus,
        a.APPLY_STATUS            as applyStatus,
        a.ENGAGEMENT_PROJECT      as engagementProject,
        a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
        a.APPLY_ID                as applyId,
        ''                        as instId,
        a.PLAN_VISIT_TIME         as planVisitTime,
        a.PLAN_OUT_TIME           as planOutTime,
        a.PERSON_ROLE             as personRole,
        a.PERSON_ROLE_ID          as personRoleId,
        a.PERSON_ROLE_JSON        as personRoleJson,
        a.DEPART_ID               as departId,
        a.DEPART_NAME             as departName,
        a.DEPART_JSON             as departJson,
        a.POSITION_TYPE           as positionType,
        a.CLASSIFIED_POSITION     as classifiedPosition,
        a.POST_ID                 as postId,
        b.POST                    as postName,
        a.POST_JSON               as postJson,
        a.EDUCATION               as education,
        a.TECHNICAL_DIRECTION     as technicalDirection,
        b.PERSON_SEX              as sex,
        a.CREATE_TIME             as createTime,
        a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
        left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        where a.DEL_FLAG = '0'
        and a.IS_EXPORT = '0'
        <if test="personName != null and personName != ''">
            and b.PERSON_NAME like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="postName != null and postName != ''">
            and b.POST like concat('%', #{postName}, '%')
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and b.WORKING_COMPANY_ID in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.ENGAGEMENT_PROJECT_ID in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and b.WORKING_COMPANY_ID = #{supplierId}
        </if>
        <if test="personCard != null and personCard != ''">
            and b.PERSON_CARD = #{personCard}
        </if>
        <if test="applyStatus != null and applyStatus != ''">
            and a.APPLY_STATUS = #{applyStatus}
        </if>
        <if test="orgGroupIdList != null and orgGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="orgGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="createdBy != null and createdBy != ''">
            and a.CREATE_BY = #{createdBy}
        </if>
        ) as unionPerson
        group by personCard) as unionPerson1
        inner join
        (select a.ID                      as id,
        a.PERSON_ID               as personId,
        b.PERSON_NAME             as personName,
        b.PERSON_CARD             as personCard,
        b.TEL                     as tel,
        b.WORKING_COMPANY         as workingCompany,
        b.WORKING_COMPANY_ID      as workingCompanyId,
<!--        b.WORKING_COMPANY_JSON    as workingCompanyJson,-->
        a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
        a.IN_TIME                 as inTime,
        a.OUT_TIME                as outTime,
        a.OUT_APPLY_STATUS        as outApplyStatus,
        case
        when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
        when a.OUT_TIME is null then '0'
        else '1'
        end                   as entryStatus,
        a.APPLY_STATUS            as applyStatus,
        a.ENGAGEMENT_PROJECT      as engagementProject,
        a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
        a.APPLY_ID                as applyId,
        c.INST_ID                 as instId,
        a.PLAN_VISIT_TIME         as planVisitTime,
        a.PLAN_OUT_TIME           as planOutTime,
        a.PERSON_ROLE             as personRole,
        a.PERSON_ROLE_ID          as personRoleId,
        a.PERSON_ROLE_JSON        as personRoleJson,
        a.DEPART_ID               as departId,
        a.DEPART_NAME             as departName,
        a.DEPART_JSON             as departJson,
        a.POSITION_TYPE           as positionType,
        a.CLASSIFIED_POSITION     as classifiedPosition,
        a.POST_ID                 as postId,
        b.POST                    as postName,
        a.POST_JSON               as postJson,
        a.EDUCATION               as education,
        a.TECHNICAL_DIRECTION     as technicalDirection,
        b.PERSON_SEX              as sex,
        a.CREATE_TIME             as createTime,
        a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
        left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        left join uomp_admission_application c on a.APPLY_ID = c.ID AND c.DEL_FLAG = '0'
        inner join bpm_instance bi on c.INST_ID = bi.id_ and bi.status_ != 'discard'
        where a.DEL_FLAG = '0'
        and a.IS_EXPORT = '1'
        union all
        select a.ID                      as id,
        a.PERSON_ID               as personId,
        b.PERSON_NAME             as personName,
        b.PERSON_CARD             as personCard,
        b.TEL                     as tel,
        b.WORKING_COMPANY         as workingCompany,
        b.WORKING_COMPANY_ID      as workingCompanyId,
<!--        b.WORKING_COMPANY_JSON    as workingCompanyJson,-->
        a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
        a.IN_TIME                 as inTime,
        a.OUT_TIME                as outTime,
        a.OUT_APPLY_STATUS        as outApplyStatus,
        case
        when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
        when a.OUT_TIME is null then '0'
        else '1'
        end                   as entryStatus,
        a.APPLY_STATUS            as applyStatus,
        a.ENGAGEMENT_PROJECT      as engagementProject,
        a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
        a.APPLY_ID                as applyId,
        ''                        as instId,
        a.PLAN_VISIT_TIME         as planVisitTime,
        a.PLAN_OUT_TIME           as planOutTime,
        a.PERSON_ROLE             as personRole,
        a.PERSON_ROLE_ID          as personRoleId,
        a.PERSON_ROLE_JSON        as personRoleJson,
        a.DEPART_ID               as departId,
        a.DEPART_NAME             as departName,
        a.DEPART_JSON             as departJson,
        a.POSITION_TYPE           as positionType,
        a.CLASSIFIED_POSITION     as classifiedPosition,
        a.POST_ID                 as postId,
        b.POST                    as postName,
        a.POST_JSON               as postJson,
        a.EDUCATION               as education,
        a.TECHNICAL_DIRECTION     as technicalDirection,
        b.PERSON_SEX              as sex,
        a.CREATE_TIME             as createTime,
        a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
        left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        where a.DEL_FLAG = '0'
        and a.IS_EXPORT = '0') as unionPerson2
        on unionPerson1.personCard = unionPerson2.personCard and unionPerson1.createTimes = unionPerson2.createTime
        where ((unionPerson2.applyStatus != '0') or
        (unionPerson2.applyStatus = '0' and unionPerson2.createBy = #{createdByStatus}))
        group by unionPerson2.personCard
        order by unionPerson2.createTime desc) as result
        <where>
            <if test="entryStatus != null and entryStatus != '' and entryStatus == -1">
                and result.applyStatus != '2'
            </if>
            <if test="entryStatus != null and entryStatus != '' and entryStatus == 0">
                and result.applyStatus = '2'
                and result.outTime is null
            </if>
            <if test="entryStatus != null and entryStatus != '' and entryStatus == 1">
                and result.outTime is not null
            </if>
        </where>
    </select>

    <select id="selectIdBySelective" resultType="java.lang.String">
        select MAX(a.ID) id
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_PERSON_INFO b on
            a.PERSON_CARD = b.PERSON_CARD
                AND b.DEL_FLAG = '0'
        where a.DEL_FLAG = '0'
          and a.APPLY_STATUS = '2'
        <if test="personName != null and personName != ''">
            and a.PERSON_NAME like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.MAINTENANCE_GROUP_ID in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="postName != null and postName != ''">
            and a.POST_NAME like concat('%', #{postName}, '%')
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and a.WORKING_COMPANY_ID in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.ENGAGEMENT_PROJECT_ID in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and b.WORKING_COMPANY_ID = #{supplierId}
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 0">
            and a.OUT_TIME is null
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 1">
            and a.OUT_TIME is not null
        </if>
        GROUP BY a.PERSON_CARD
    </select>

    <select id="selectAllById" resultType="cn.gwssi.ecloud.staffpool.dto.EntryApplyDTO">
        select a.ID                     as id,
               a.PERSON_ID              as personId,
               c.PERSON_NAME            as personName,
               c.PERSON_CARD            as personCard,
               a.ENTRY_DATE             as entryDate,
               a.EDUCATION              as education,
               a.TECHNICAL_POST         as technicalPost,
               a.WORKING_COMPANY        as workingCompany,
               a.WORKING_COMPANY_ID     as workingCompanyId,
               a.WORKING_COMPANY_JSON   as workingCompanyJson,
               c.TEL                    as tel,
               a.ENGAGEMENT_PROJECT     as engagementProject,
               a.SCORE                  as score,
               a.INTERVIEW_RESULT       as interviewResult,
               a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
               a.POST_NAME              as postName,
               a.JOB_CONTENT            as jobContent,
               a.FILE_INFO              as fileInfoJson,
               a.POST_FILE_INFO         as postFileInfoJson,
               b.APPLY_CODE             as applyCode,
               b.APPLY_TITLE            as applyTitle,
               b.APPLY_USER_NAME        as applyUserName,
               b.APPLY_TIME             as applyTime,
               b.APPLY_MATTER           as applyMatter,
               a.SERVIC_TYPE            as servicType,
               a.SERVICE_LOCATION       as serviceLocation,
               a.IS_CLASSIFIED_POSITION as isClassifiedPosition,
               a.TECHNICAL_DIRECTION    as technicalDirection,
               a.CREATE_BY              as createBy,
               b.INST_ID                as instId,
               a.PLAN_VISIT_TIME        as planVisitTime,
               a.PLAN_OUT_TIME          as planOutTime,
               a.PERSON_ROLE            as personRole,
               a.PERSON_ROLE_ID         as personRoleId,
               a.PERSON_ROLE_JSON       as personRoleJson,
               a.DEPART_ID              as departId,
               a.DEPART_NAME            as departName,
               a.DEPART_JSON            as departJson,
               a.POSITION_TYPE          as positionType,
               a.CLASSIFIED_POSITION    as classifiedPosition,
               a.POST_ID                as postId,
               a.POST_NAME              as postName,
               a.POST_JSON              as postJson,
               c.PERSON_SEX             as sex
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_ADMISSION_APPLICATION b on a.APPLY_ID = b.ID and b.DEL_FLAG = '0'
                 left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
        where a.ID = #{id}
          and a.DEL_FLAG = '0'
    </select>


    <select id="selectAllByPersonCard" resultType="cn.gwssi.ecloud.staffpool.dto.PersonInfoOverviewDTO">
        select *
        from (select e.ID                     as id,
                     e.OUT_APPLY_TITLE        as title,
                     '2'                      as type,
                     e.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     e.WORKING_COMPANY        as workingCompany,
                     e.WORKING_COMPANY_ID     as workingCompanyId,
                     e.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.IN_TIME                as inTime,
                     e.APPLY_STATUS           as applyStatus,
                     '1'                      as entryStatus,
                     e.INST_ID                as instId,
                     c.PERSON_SEX             as sex,
                     e.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy
              from UOMP_EXIT_APPLICATION e
                       left join UOMP_ADMISSION_PERSON a on e.APPLY_PERSON_ID = a.ID
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
                       inner join bpm_instance bi on e.INST_ID = bi.id_ and bi.status_ != 'discard'
              where e.DEL_FLAG = '0'
                and a.IS_EXPORT = '1'
              union all
              select e.ID                     as id,
                     e.OUT_APPLY_TITLE        as title,
                     '2'                      as type,
                     e.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     e.WORKING_COMPANY        as workingCompany,
                     e.WORKING_COMPANY_ID     as workingCompanyId,
                     e.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.IN_TIME                as inTime,
                     e.APPLY_STATUS           as applyStatus,
                     '1'                      as entryStatus,
                     ''                       as instId,
                     c.PERSON_SEX             as sex,
                     e.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy
              from UOMP_EXIT_APPLICATION e
                       left join UOMP_ADMISSION_PERSON a on e.APPLY_PERSON_ID = a.ID
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
              where e.DEL_FLAG = '0'
                and a.IS_EXPORT = '0'
              union all
              select a.ID                     as id,
                     b.APPLY_TITLE            as title,
                     '1'                      as type,
                     a.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     a.WORKING_COMPANY        as workingCompany,
                     a.WORKING_COMPANY_ID     as workingCompanyId,
                     a.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.IN_TIME                as inTime,
                     a.APPLY_STATUS           as applyStatus,
                     case
                         when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                         when a.OUT_TIME is null then '0'
                         else '1'
                         end                  as entryStatus,
                     b.INST_ID                as instId,
                     c.PERSON_SEX             as sex,
                     a.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy
              from UOMP_ADMISSION_PERSON a
                       left join UOMP_ADMISSION_APPLICATION b on a.APPLY_ID = b.id and b.DEL_FLAG = '0'
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
                       inner join bpm_instance bi on b.INST_ID = bi.id_ and bi.status_ != 'discard'
              where a.DEL_FLAG = '0'
                and a.IS_EXPORT = '1'
              union all
              select a.ID                     as id,
                     b.APPLY_TITLE            as title,
                     '1'                      as type,
                     a.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     a.WORKING_COMPANY        as workingCompany,
                     a.WORKING_COMPANY_ID     as workingCompanyId,
                     a.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.IN_TIME                as inTime,
                     a.APPLY_STATUS           as applyStatus,
                     case
                         when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                         when a.OUT_TIME is null then '0'
                         else '1'
                         end                  as entryStatus,
                     ''                       as instId,
                     c.PERSON_SEX             as sex,
                     a.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy
              from UOMP_ADMISSION_PERSON a
                       left join UOMP_ADMISSION_APPLICATION b on a.APPLY_ID = b.id and b.DEL_FLAG = '0'
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
              where a.DEL_FLAG = '0'
                and a.IS_EXPORT = '0') a
        where a.personCard = #{personCard}
          and ((a.applyStatus != '0') or (a.applyStatus = '0' and a.createBy = #{createBy}))
        order by a.createTime desc
    </select>

    <select id="selectAllBySelectiveExport" resultType="cn.gwssi.ecloud.staffpool.dto.PersonListDTO">
        select *
        from (select e.ID                     as id,
                     e.OUT_APPLY_TITLE        as title,
                     '2'                      as type,
                     e.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     e.WORKING_COMPANY        as workingCompany,
                     e.WORKING_COMPANY_ID     as workingCompanyId,
                     e.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_ID   as maintenanceGroupId,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT_ID  as engagementProjectId,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.TECHNICAL_DIRECTION    as technicalDirection,
                     a.SERVICE_LOCATION       as serviceLocation,
                     '1'                      as entryStatus,
                     a.IN_TIME                as inTime,
                     e.APPLY_STATUS           as applyStatus,
                     e.INST_ID                as instId,
                     e.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy,
                     c.PERSON_SEX             as sex,
                     a.DEL_FLAG               as delFlag
              from UOMP_EXIT_APPLICATION e
                       left join UOMP_ADMISSION_PERSON a on e.APPLY_PERSON_ID = a.ID
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
                       inner join bpm_instance bi on e.INST_ID = bi.id_ and bi.status_ != 'discard'
              where e.DEL_FLAG = '0'
                and e.INST_ID is not null
              union all
              select e.ID                     as id,
                     e.OUT_APPLY_TITLE        as title,
                     '2'                      as type,
                     e.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     e.WORKING_COMPANY        as workingCompany,
                     e.WORKING_COMPANY_ID     as workingCompanyId,
                     e.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_ID   as maintenanceGroupId,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT_ID  as engagementProjectId,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.TECHNICAL_DIRECTION    as technicalDirection,
                     a.SERVICE_LOCATION       as serviceLocation,
                     '1'                      as entryStatus,
                     a.IN_TIME                as inTime,
                     e.APPLY_STATUS           as applyStatus,
                     e.INST_ID                as instId,
                     e.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy,
                     c.PERSON_SEX             as sex,
                     a.DEL_FLAG               as delFlag
              from UOMP_EXIT_APPLICATION e
                       left join UOMP_ADMISSION_PERSON a on e.APPLY_PERSON_ID = a.ID
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
              where e.DEL_FLAG = '0'
                and e.INST_ID is null
              union all
              select a.ID                     as id,
                     b.APPLY_TITLE            as title,
                     '1'                      as type,
                     a.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     a.WORKING_COMPANY        as workingCompany,
                     a.WORKING_COMPANY_ID     as workingCompanyId,
                     a.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_ID   as maintenanceGroupId,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT_ID  as engagementProjectId,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.TECHNICAL_DIRECTION    as technicalDirection,
                     a.SERVICE_LOCATION       as serviceLocation,
                     case
                         when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                         when a.OUT_TIME is null then '0'
                         else '1'
                         end                  as entryStatus,
                     a.IN_TIME                as inTime,
                     a.APPLY_STATUS           as applyStatus,
                     b.INST_ID                as instId,
                     a.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy,
                     c.PERSON_SEX             as sex,
                     a.DEL_FLAG               as delFlag
              from UOMP_ADMISSION_PERSON a
                       left join UOMP_ADMISSION_APPLICATION b on a.APPLY_ID = b.id and b.DEL_FLAG = '0'
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
                       inner join bpm_instance bi on b.INST_ID = bi.id_ and bi.status_ != 'discard'
              where a.DEL_FLAG = '0'
                and a.IS_EXPORT = '1'
              union all
              select a.ID                     as id,
                     b.APPLY_TITLE            as title,
                     '1'                      as type,
                     a.OUT_TIME               as outTime,
                     a.PERSON_ID              as personId,
                     c.PERSON_NAME            as personName,
                     c.PERSON_CARD            as personCard,
                     c.TEL                    as tel,
                     a.WORKING_COMPANY        as workingCompany,
                     a.WORKING_COMPANY_ID     as workingCompanyId,
                     a.WORKING_COMPANY_JSON   as workingCompanyJson,
                     a.MAINTENANCE_GROUP_ID   as maintenanceGroupId,
                     a.MAINTENANCE_GROUP_NAME as maintenanceGroupName,
                     a.ENGAGEMENT_PROJECT_ID  as engagementProjectId,
                     a.ENGAGEMENT_PROJECT     as engagementProject,
                     a.TECHNICAL_DIRECTION    as technicalDirection,
                     a.SERVICE_LOCATION       as serviceLocation,
                     case
                         when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                         when a.OUT_TIME is null then '0'
                         else '1'
                         end                  as entryStatus,
                     a.IN_TIME                as inTime,
                     a.APPLY_STATUS           as applyStatus,
                     ''                       as instId,
                     a.CREATE_TIME            as createTime,
                     a.CREATE_BY              as createBy,
                     c.PERSON_SEX             as sex,
                     a.DEL_FLAG               as delFlag
              from UOMP_ADMISSION_PERSON a
                       left join UOMP_ADMISSION_APPLICATION b on a.APPLY_ID = b.id and b.DEL_FLAG = '0'
                       left join UOMP_PERSON_INFO c on a.PERSON_CARD = c.PERSON_CARD AND c.DEL_FLAG = '0'
              where a.DEL_FLAG = '0'
                and a.IS_EXPORT = '0') a
        where a.delFlag = '0'
        <if test="personName != null and personName != ''">
            and a.personName like concat('%', #{personName}, '%')
        </if>
        <if test="maintenanceGroupIdList != null and maintenanceGroupIdList.size() > 0">
            and a.maintenanceGroupId in
            <foreach item="id" collection="maintenanceGroupIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="workingCompanyList != null and workingCompanyList.size() > 0">
            and a.workingCompanyId in
            <foreach item="id" collection="workingCompanyList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="involvedProjectList != null and involvedProjectList.size() > 0">
            and a.engagementProjectId in
            <foreach item="id" collection="involvedProjectList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="inTimeBegin != null and inTimeBegin != ''">
            and date_format(a.inTime, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null and inTimeEnd != ''">
            and date_format(a.inTime, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="ifSupplier == 1">
            and a.workingCompanyId = #{supplierId}
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == -1">
            and a.applyStatus != '2'
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 0">
            and a.applyStatus = '2'
        </if>
        <if test="entryStatus != null and entryStatus != '' and entryStatus == 1">
            and a.outTime is not null
        </if>
        <if test="personCard != null and personCard != ''">
            and a.personCard = #{personCard}
        </if>
        <if test="applyStatus != null and applyStatus != ''">
            and a.applyStatus = #{applyStatus}
        </if>
        <if test="orgGroupId != null and orgGroupId != ''">
            and a.maintenanceGroupId = #{orgGroupId}
        </if>
        <if test="createdBy != null and createdBy != ''">
            and a.createBy = #{createdBy}
        </if>
        order by a.createTime desc
    </select>

    <select id="selectEngagementProjectIdByPersonCardAndApplyStatus" resultType="java.lang.String">
        select ENGAGEMENT_PROJECT_ID
        from UOMP_ADMISSION_PERSON
        where DEL_FLAG = '0'
          AND PERSON_CARD = #{personCard}
          AND APPLY_STATUS != '3'
          AND OUT_TIME IS NULL
    </select>

    <select id="countTotalPersonLocation" resultType="java.lang.Integer">
        select sum(num) as toal
        from (
            select
			    count(0) as num, a.SERVICE_LOCATION as name
			from (
			    SELECT PERSON_CARD, SERVICE_LOCATION FROM uomp_admission_person
			    WHERE DEL_FLAG = '0' and APPLY_STATUS = '2' and OUT_TIME is null
			    GROUP BY SERVICE_LOCATION , PERSON_CARD
		        ) a
			GROUP BY a.SERVICE_LOCATION) totalTable
    </select>

    <select id="getPersonInTime" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO" databaseId="mysql">
        select TIMESTAMPDIFF(YEAR, IN_TIME, date_format(now(), '%Y-%m-%d %H:%i:%S')) as name, count(*) as num
        FROM uomp_admission_person
        where IN_TIME is not null
          and DEL_FLAG = '0'
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
        group by name
    </select>

    <select id="getPersonInTime" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO">
        select TIMESTAMPDIFF(YEAR, IN_TIME, date_format(now(), '%Y-%m-%d %H:%i:%S')) as name, count(*) as num
        FROM uomp_admission_person
        where IN_TIME is not null
        and DEL_FLAG = '0'
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
        group by TIMESTAMPDIFF(YEAR, IN_TIME, date_format(now(), '%Y-%m-%d %H:%i:%S'))
    </select>

    <select id="countTotalByApplyStatus" resultType="java.lang.Integer">
        select count(1)
        from uomp_admission_person
        where APPLY_STATUS = '2'
          and DEL_FLAG = '0'
          and OUT_TIME is null
        <if test="inTimeBegin != null">
            and date_format(IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null">
            and date_format(IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
    </select>

    <select id="getPersonLocation" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO">
        select
		    count(0) as num, a.SERVICE_LOCATION as name
		from (
		SELECT PERSON_CARD, SERVICE_LOCATION FROM uomp_admission_person
		WHERE DEL_FLAG = '0' and APPLY_STATUS = '2' and OUT_TIME is null
		GROUP BY SERVICE_LOCATION , PERSON_CARD
		) a
		GROUP BY a.SERVICE_LOCATION
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-06-26-->
    <select id="selectOneByPersonIdAndPersonCard" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uomp_admission_person
        where APPLY_STATUS = '2'
          and OUT_APPLY_STATUS = '0'
          and DEL_FLAG = '0'
        <if test="personId != null and personId != ''">
            and PERSON_ID = #{personId,jdbcType=VARCHAR}
        </if>
        <if test="personCard != null and personCard != ''">
            and PERSON_CARD = #{personCard,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-06-27-->
    <select id="selectAllByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from uomp_admission_person
        where ID in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="countByInTimeRange" resultType="java.lang.Integer">
        select
            count(1)
        from
            (
                select
                    PERSON_CARD
                from
                    uomp_admission_person
                where
                    IN_TIME > #{inTimeStart}
                  and IN_TIME &lt; #{inTimeEnd}
        and APPLY_STATUS = '2'
        and OUT_APPLY_STATUS = '0'
        and OUT_TIME is null
        group by
        PERSON_CARD ) as b

    </select>

    <select id="selectAllByPersonCardIn" resultType="cn.gwssi.ecloud.staffpool.dto.PersonListDTO">
        select a.ID                      as id,
               a.PERSON_ID               as personId,
               b.PERSON_NAME             as personName,
               b.PERSON_CARD             as personCard,
               b.TEL                     as tel,
               a.WORKING_COMPANY         as workingCompany,
               a.WORKING_COMPANY_ID      as workingCompanyId,
               a.WORKING_COMPANY_JSON    as workingCompanyJson,
               a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
               a.IN_TIME                 as inTime,
               a.OUT_TIME                as outTime,
               a.OUT_APPLY_STATUS        as outApplyStatus,
               case
                   when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
                   when a.OUT_TIME is null then '0'
                   else '1'
                   end                   as entryStatus,
               a.APPLY_STATUS            as applyStatus,
               a.ENGAGEMENT_PROJECT      as engagementProject,
               a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
               a.APPLY_ID                as applyId,
               a.PLAN_VISIT_TIME         as planVisitTime,
               a.PLAN_OUT_TIME           as planOutTime,
               a.PERSON_ROLE             as personRole,
               a.PERSON_ROLE_ID          as personRoleId,
               a.PERSON_ROLE_JSON        as personRoleJson,
               a.DEPART_ID               as departId,
               a.DEPART_NAME             as departName,
               a.DEPART_JSON             as departJson,
               a.POSITION_TYPE           as positionType,
               a.CLASSIFIED_POSITION     as classifiedPosition,
               a.POST_ID                 as postId,
               a.POST_NAME               as postName,
               a.POST_JSON               as postJson,
               a.EDUCATION               as education,
               a.TECHNICAL_DIRECTION     as technicalDirection,
               b.PERSON_SEX              as sex,
               a.CREATE_TIME             as createTime,
               a.CREATE_BY               as createBy,
               a.SERVICE_LOCATION        as serviceLocation
        from UOMP_ADMISSION_PERSON a
                 left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        where b.PERSON_CARD in
        <foreach item="personCard" collection="personCardList" open="(" separator="," close=")">
            #{personCard}
        </foreach>
    </select>

    <select id="selectAllByPersonAdmissionIds" resultType="cn.gwssi.ecloud.staffpool.dto.PersonListDTO">
        select a.ID                      as id,
        a.PERSON_ID               as personId,
        b.PERSON_NAME             as personName,
        b.PERSON_CARD             as personCard,
        b.TEL                     as tel,
        a.WORKING_COMPANY         as workingCompany,
        a.WORKING_COMPANY_ID      as workingCompanyId,
        a.WORKING_COMPANY_JSON    as workingCompanyJson,
        a.MAINTENANCE_GROUP_NAME  as maintenanceGroupName,
        a.IN_TIME                 as inTime,
        a.OUT_TIME                as outTime,
        a.OUT_APPLY_STATUS        as outApplyStatus,
        case
        when a.APPLY_STATUS = '0' or a.APPLY_STATUS = '1' or a.APPLY_STATUS = '3' then '-1'
        when a.OUT_TIME is null then '0'
        else '1'
        end                   as entryStatus,
        a.APPLY_STATUS            as applyStatus,
        a.ENGAGEMENT_PROJECT      as engagementProject,
        a.ENGAGEMENT_PROJECT_JSON as engagementProjectJson,
        a.APPLY_ID                as applyId,
        a.PLAN_VISIT_TIME         as planVisitTime,
        a.PLAN_OUT_TIME           as planOutTime,
        a.PERSON_ROLE             as personRole,
        a.PERSON_ROLE_ID          as personRoleId,
        a.PERSON_ROLE_JSON        as personRoleJson,
        a.DEPART_ID               as departId,
        a.DEPART_NAME             as departName,
        a.DEPART_JSON             as departJson,
        a.POSITION_TYPE           as positionType,
        a.CLASSIFIED_POSITION     as classifiedPosition,
        a.POST_ID                 as postId,
        a.POST_NAME               as postName,
        a.POST_JSON               as postJson,
        a.EDUCATION               as education,
        a.TECHNICAL_DIRECTION     as technicalDirection,
        b.PERSON_SEX              as sex,
        a.CREATE_TIME             as createTime,
        a.CREATE_BY               as createBy
        from UOMP_ADMISSION_PERSON a
        left join UOMP_PERSON_INFO b on a.PERSON_CARD = b.PERSON_CARD AND b.DEL_FLAG = '0'
        where a.ID in
        <foreach item="id" collection="admissionIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getPersonInTimeNew" resultType="cn.gwssi.ecloud.staffpool.dto.PercentageDTO">
        select TIMESTAMPDIFF(YEAR, ENTRY_DATE, date_format(now(), '%Y-%m-%d %H:%i:%S')) as name, count(*) as num
		from(
		select PERSON_CARD,ENTRY_DATE from uomp_admission_person
		where DEL_FLAG = '0' and APPLY_STATUS = '2' and OUT_TIME is null
		group by PERSON_CARD) a
		group by TIMESTAMPDIFF(YEAR, ENTRY_DATE, date_format(now(), '%Y-%m-%d %H:%i:%S'))
    </select>



    <select id="countTotalByApplyStatusNew" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (
        select PERSON_CARD
        from uomp_admission_person
        where APPLY_STATUS = '2'
        and DEL_FLAG = '0'
        and OUT_TIME is null
        <if test="inTimeBegin != null">
            and date_format(IN_TIME, '%Y-%m-%d') >= #{inTimeBegin}
        </if>
        <if test="inTimeEnd != null">
            and date_format(IN_TIME, '%Y-%m-%d') &lt;= #{inTimeEnd}
        </if>
        <if test="workingCompanyId != null and workingCompanyId != ''">
            and WORKING_COMPANY_ID = #{workingCompanyId}
        </if>
        <if test="workingCompany != null and workingCompany != ''">
            and WORKING_COMPANY = #{workingCompany}
        </if>
        GROUP BY PERSON_CARD
        ) a
    </select>
    <update id="encryptPersonCard" parameterType="java.lang.String">
        update uomp_admission_person set person_card = #{personCard} where id = #{id}
    </update>
</mapper>
