<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.CmdbTableBaseConfigMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig">
    <!--@mbg.generated-->
    <!--@Table cmdb_table_base_config-->
    <id column="BASELINE_ID" jdbcType="VARCHAR" property="baselineId" />
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="KEY" jdbcType="VARCHAR" property="key" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="COMMENT" jdbcType="VARCHAR" property="comment" />
    <result column="DS_KEY" jdbcType="VARCHAR" property="dsKey" />
    <result column="DS_NAME" jdbcType="VARCHAR" property="dsName" />
    <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
    <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
    <result column="EXTERNAL" jdbcType="INTEGER" property="external" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="MODEL_TYPE" jdbcType="VARCHAR" property="modelType" />
    <result column="TEAM_NAME" jdbcType="VARCHAR" property="teamName" />
    <result column="MODEL_STATUS" jdbcType="VARCHAR" property="modelStatus" />
    <result column="TEAM_ID" jdbcType="VARCHAR" property="teamId" />
    <result column="BASELINE" jdbcType="VARCHAR" property="baseline" />
    <result column="BASELINE_STATUS" jdbcType="VARCHAR" property="baselineStatus" />
    <result column="BASELINE_AUDIT" jdbcType="VARCHAR" property="baselineAudit" />
    <result column="RELEASE_TIME" jdbcType="TIMESTAMP" property="releaseTime" />
    <result column="RELEASE_PERSION" jdbcType="VARCHAR" property="releasePersion" />
    <result column="RELEASE_PERSION_ID" jdbcType="VARCHAR" property="releasePersionId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="SN_" jdbcType="INTEGER" property="sn" />
    <result column="OPERATOR" jdbcType="VARCHAR" property="operator" />
    <result column="BASELINE_ID_PRE" jdbcType="VARCHAR" property="baselineIdPre" />
    <result column="MODEL_ICON" jdbcType="VARCHAR" property="modelIcon" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BASELINE_ID, MODEL_ID, `KEY`, `NAME`, `COMMENT`, DS_KEY, DS_NAME, GROUP_ID, GROUP_NAME, 
    `EXTERNAL`, ORG_ID, MODEL_TYPE, TEAM_NAME, MODEL_STATUS, TEAM_ID, BASELINE, BASELINE_STATUS, 
    BASELINE_AUDIT, RELEASE_TIME, RELEASE_PERSION, RELEASE_PERSION_ID, CREATE_TIME, CREATE_BY, 
    UPDATE_TIME, UPDATE_BY, DEL_FLAG, SN_, `OPERATOR`, BASELINE_ID_PRE, MODEL_ICON, ORDER_NO
  </sql>

  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig">
    <!--@mbg.generated-->
    insert into cmdb_table_base_config (BASELINE_ID, MODEL_ID, `KEY`, 
      `NAME`, `COMMENT`, DS_KEY, 
      DS_NAME, GROUP_ID, GROUP_NAME, 
      `EXTERNAL`, ORG_ID, MODEL_TYPE, 
      TEAM_NAME, MODEL_STATUS, TEAM_ID, 
      BASELINE, BASELINE_STATUS, BASELINE_AUDIT, 
      RELEASE_TIME, RELEASE_PERSION, RELEASE_PERSION_ID, 
      CREATE_TIME, CREATE_BY, UPDATE_TIME, 
      UPDATE_BY, DEL_FLAG, SN_, 
      `OPERATOR`, BASELINE_ID_PRE, MODEL_ICON, 
      ORDER_NO)
    values (#{baselineId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, #{dsKey,jdbcType=VARCHAR}, 
      #{dsName,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, 
      #{external,jdbcType=INTEGER}, #{orgId,jdbcType=VARCHAR}, #{modelType,jdbcType=VARCHAR}, 
      #{teamName,jdbcType=VARCHAR}, #{modelStatus,jdbcType=VARCHAR}, #{teamId,jdbcType=VARCHAR}, 
      #{baseline,jdbcType=VARCHAR}, #{baselineStatus,jdbcType=VARCHAR}, #{baselineAudit,jdbcType=VARCHAR}, 
      #{releaseTime,jdbcType=TIMESTAMP}, #{releasePersion,jdbcType=VARCHAR}, #{releasePersionId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}, #{sn,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{baselineIdPre,jdbcType=VARCHAR}, #{modelIcon,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig">
    <!--@mbg.generated-->
    insert into cmdb_table_base_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baselineId != null">
        BASELINE_ID,
      </if>
      <if test="modelId != null">
        MODEL_ID,
      </if>
      <if test="key != null">
        `KEY`,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="comment != null">
        `COMMENT`,
      </if>
      <if test="dsKey != null">
        DS_KEY,
      </if>
      <if test="dsName != null">
        DS_NAME,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="groupName != null">
        GROUP_NAME,
      </if>
      <if test="external != null">
        `EXTERNAL`,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="modelType != null">
        MODEL_TYPE,
      </if>
      <if test="teamName != null">
        TEAM_NAME,
      </if>
      <if test="modelStatus != null">
        MODEL_STATUS,
      </if>
      <if test="teamId != null">
        TEAM_ID,
      </if>
      <if test="baseline != null">
        BASELINE,
      </if>
      <if test="baselineStatus != null">
        BASELINE_STATUS,
      </if>
      <if test="baselineAudit != null">
        BASELINE_AUDIT,
      </if>
      <if test="releaseTime != null">
        RELEASE_TIME,
      </if>
      <if test="releasePersion != null">
        RELEASE_PERSION,
      </if>
      <if test="releasePersionId != null">
        RELEASE_PERSION_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="sn != null">
        SN_,
      </if>
      <if test="operator != null">
        `OPERATOR`,
      </if>
      <if test="baselineIdPre != null">
        BASELINE_ID_PRE,
      </if>
      <if test="modelIcon != null">
        MODEL_ICON,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baselineId != null">
        #{baselineId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="dsKey != null">
        #{dsKey,jdbcType=VARCHAR},
      </if>
      <if test="dsName != null">
        #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="external != null">
        #{external,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="teamName != null">
        #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="modelStatus != null">
        #{modelStatus,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=VARCHAR},
      </if>
      <if test="baseline != null">
        #{baseline,jdbcType=VARCHAR},
      </if>
      <if test="baselineStatus != null">
        #{baselineStatus,jdbcType=VARCHAR},
      </if>
      <if test="baselineAudit != null">
        #{baselineAudit,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null">
        #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releasePersion != null">
        #{releasePersion,jdbcType=VARCHAR},
      </if>
      <if test="releasePersionId != null">
        #{releasePersionId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="baselineIdPre != null">
        #{baselineIdPre,jdbcType=VARCHAR},
      </if>
      <if test="modelIcon != null">
        #{modelIcon,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig">
    <!--@mbg.generated-->
    update cmdb_table_base_config
    <set>
      <if test="modelId != null">
        MODEL_ID = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="key != null">
        `KEY` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `COMMENT` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="dsKey != null">
        DS_KEY = #{dsKey,jdbcType=VARCHAR},
      </if>
      <if test="dsName != null">
        DS_NAME = #{dsName,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="external != null">
        `EXTERNAL` = #{external,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        MODEL_TYPE = #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="teamName != null">
        TEAM_NAME = #{teamName,jdbcType=VARCHAR},
      </if>
      <if test="modelStatus != null">
        MODEL_STATUS = #{modelStatus,jdbcType=VARCHAR},
      </if>
      <if test="teamId != null">
        TEAM_ID = #{teamId,jdbcType=VARCHAR},
      </if>
      <if test="baseline != null">
        BASELINE = #{baseline,jdbcType=VARCHAR},
      </if>
      <if test="baselineStatus != null">
        BASELINE_STATUS = #{baselineStatus,jdbcType=VARCHAR},
      </if>
      <if test="baselineAudit != null">
        BASELINE_AUDIT = #{baselineAudit,jdbcType=VARCHAR},
      </if>
      <if test="releaseTime != null">
        RELEASE_TIME = #{releaseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="releasePersion != null">
        RELEASE_PERSION = #{releasePersion,jdbcType=VARCHAR},
      </if>
      <if test="releasePersionId != null">
        RELEASE_PERSION_ID = #{releasePersionId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        SN_ = #{sn,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        `OPERATOR` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="baselineIdPre != null">
        BASELINE_ID_PRE = #{baselineIdPre,jdbcType=VARCHAR},
      </if>
      <if test="modelIcon != null">
        MODEL_ICON = #{modelIcon,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where BASELINE_ID = #{baselineId,jdbcType=VARCHAR}
  </update>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig">
    <!--@mbg.generated-->
    update cmdb_table_base_config
    set MODEL_ID = #{modelId,jdbcType=VARCHAR},
      `KEY` = #{key,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      `COMMENT` = #{comment,jdbcType=VARCHAR},
      DS_KEY = #{dsKey,jdbcType=VARCHAR},
      DS_NAME = #{dsName,jdbcType=VARCHAR},
      GROUP_ID = #{groupId,jdbcType=VARCHAR},
      GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      `EXTERNAL` = #{external,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=VARCHAR},
      MODEL_TYPE = #{modelType,jdbcType=VARCHAR},
      TEAM_NAME = #{teamName,jdbcType=VARCHAR},
      MODEL_STATUS = #{modelStatus,jdbcType=VARCHAR},
      TEAM_ID = #{teamId,jdbcType=VARCHAR},
      BASELINE = #{baseline,jdbcType=VARCHAR},
      BASELINE_STATUS = #{baselineStatus,jdbcType=VARCHAR},
      BASELINE_AUDIT = #{baselineAudit,jdbcType=VARCHAR},
      RELEASE_TIME = #{releaseTime,jdbcType=TIMESTAMP},
      RELEASE_PERSION = #{releasePersion,jdbcType=VARCHAR},
      RELEASE_PERSION_ID = #{releasePersionId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      SN_ = #{sn,jdbcType=INTEGER},
      `OPERATOR` = #{operator,jdbcType=VARCHAR},
      BASELINE_ID_PRE = #{baselineIdPre,jdbcType=VARCHAR},
      MODEL_ICON = #{modelIcon,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    where BASELINE_ID = #{baselineId,jdbcType=VARCHAR}
  </update>
</mapper>