<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.OrgRoleMapper">
    <sql id="baseColumn">
        id_,name_,alias_,enabled_,description,create_time_,create_by_,update_time_,update_by_,type_id_,type_name_,sn_,org_id_
    </sql>
    <select id="getRoleByOrgType" resultType="cn.gwssi.ecloud.staffpool.dto.OrgRoleTypeDTO">
        select
        trole.ID_ as id,
        trole.NAME_ as name,
        trole.ALIAS_  as alias,
        trole.DESCRIPTION as `desc`
        from org_role trole
        where trole.enabled_ = 1
        and ID_ in(select RELATION_ID from UOMP_ORP_RELATION where DEL_FLAG = '0' and RELATIION_TYPE = 'role' and ORG_TYPE = #{orgType})
        <if test="name != null and name != ''">
            and instr(trole.NAME_,#{name}) >0
        </if>
        order by trole.id_ DESC
    </select>
    <select id="selectRoleByUserId" resultType="java.lang.String">
        select
        from
        org_role trole
        inner join org_relation trelation on trole.id_ = trelation.group_id_
        and trelation.type_='userRole'
        and trelation.status_ =1 and trelation.user_id_ = #{userId}
        ORDER BY trole.id_ DESC
    </select>

    <select id="selectRoleByAccount" resultType="java.lang.String" databaseId='mysql'>
        select
        group_concat(trole.name_) as existing_roles
        from
        org_role trole
        inner join org_relation trelation on trole.id_ = trelation.group_id_
        inner join org_user tuser ON tuser.id_ = trelation.user_id_
        and trelation.type_='userRole'
        and trelation.status_ =1 and tuser.account_ =#{account}
        ORDER BY trole.id_ DESC
    </select>
    <select id="selectRoleByAccount" resultType="java.lang.String">
        select
        wm_concat(trole.name_) as existing_roles
        from
        org_role trole
        inner join org_relation trelation on trole.id_ = trelation.group_id_
        inner join org_user tuser ON tuser.id_ = trelation.user_id_
        and trelation.type_='userRole'
        and trelation.status_ =1 and tuser.account_ =#{account}
        ORDER BY trole.id_ DESC
    </select>
    <select id="selectOwerRoleByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO">
        select DISTINCT tt.* from (
            select
                orole.id_ as id,
                orole.name_ as name,
                orole.alias_ as code,
                orole.description as description
            from
            org_role orole
            inner join org_relation orelation on orole.id_ = orelation.group_id_
            <where>
                <if test="whereSql!=null">
                    ${whereSql}
                </if>
            </where>
            <if test="orderBySql!=null">
                ORDER BY ${orderBySql}
            </if>
        ) tt
    </select>
    <select id="selectOwerNoRoleByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO">
            select
            trole.id_ as id,
            trole.name_ as name,
            trole.alias_ as code,
            trole.DESCRIPTION as description
            from
            org_role trole
            where  trole.id_ not in (
            select trelation.group_id_ from org_relation trelation where trelation.user_id_ = #{userId}
            and trelation.group_id_ is not null
            )
            and trole.enabled_ = '1'
            and trole.ID_ in(select RELATION_ID from UOMP_ORP_RELATION where DEL_FLAG = '0' and RELATIION_TYPE = 'role' and ORG_TYPE = #{orgType})
            ORDER BY trole.id_ DESC
    </select>

    <select id="query" resultType="cn.gwssi.ecloud.staffpool.core.entity.OrgRole">
        select
        id_ as id,
        name_ as name,
        alias_ as alias,
        enabled_ as enabled,
        description as description,
        type_id_ as typeId,
        create_time_ as createTime,
        create_by_ as createBy,
        update_time_ as updateTime,
        update_by_ as updateBy,
        type_name_ as typeName,
        sn_ as sn,
        org_id_ as orgId
        from org_role
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>

    <select id="selectNameByUserId" resultType="java.lang.String" databaseId='mysql'>
        select GROUP_CONCAT(trole.name_) as name
        from org_role trole
                 inner join org_relation trelation
                            on trole.id_ = trelation.group_id_ and trelation.type_ = 'userRole' and
                               trelation.status_ = 1 and trelation.user_id_ = #{userId}
        ORDER BY trole.id_ DESC
    </select>
    <select id="selectNameByUserId" resultType="java.lang.String">
        select wm_concat(trole.name_) as name
        from org_role trole
        inner join org_relation trelation
        on trole.id_ = trelation.group_id_ and trelation.type_ = 'userRole' and
        trelation.status_ = 1 and trelation.user_id_ = #{userId}
        ORDER BY trole.id_ DESC
    </select>

    <select id="selectBaseByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
        select trole.id_    as id,
               trole.alias_ as name
        from org_role trole
                 inner join org_relation trelation on trole.id_ = trelation.group_id_
            and trelation.type_ = 'userRole'
            and trelation.status_ = 1 and trelation.user_id_ = #{userId}
        ORDER BY trole.id_ DESC
    </select>

    <select id="getRoleByAlias" resultType="cn.gwssi.ecloud.staffpool.core.entity.OrgRole">
        select
           id_ as id,
           name_ as name,
           alias_ as alias,
           enabled_ as enabled
        from org_role
        where alias_ = #{alias}
    </select>

    <select id="getMrRoleId" resultType="java.lang.String">
        select
        id_ as id
        from org_role
        where alias_ in ('G_ROLE_GENERAL_USER')
    </select>
</mapper>
