<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.JumpUserMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.JumpUser">
    <!--@mbg.generated-->
    <!--@Table jump_user-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ACCOUNT_" jdbcType="VARCHAR" property="account" />
    <result column="JUMP_USER" jdbcType="VARCHAR" property="jumpUser" />
    <result column="JPASSWORD" jdbcType="VARCHAR" property="jpassword" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="SYNC_STATUS" jdbcType="VARCHAR" property="syncStatus" />
    <result column="JUMP_USER_ID" jdbcType="VARCHAR" property="jumpUserId" />
    <result column="SYSTEM_KEY" jdbcType="VARCHAR" property="systemKey" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="FLAG" jdbcType="VARCHAR" property="flag" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UOMP_USER_ID" jdbcType="VARCHAR" property="uompUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACCOUNT_, JUMP_USER, JPASSWORD, USER_NAME, SYNC_STATUS, JUMP_USER_ID, SYSTEM_KEY, 
    EMAIL, CREATE_TIME, FLAG, UPDATE_TIME, UOMP_USER_ID
  </sql>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.JumpUser">
    <!--@mbg.generated-->
    insert into jump_user (ID, ACCOUNT_, JUMP_USER, 
      JPASSWORD, USER_NAME, SYNC_STATUS, 
      JUMP_USER_ID, SYSTEM_KEY, EMAIL, 
      CREATE_TIME, FLAG, UPDATE_TIME, 
      UOMP_USER_ID)
    values (#{id,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{jumpUser,jdbcType=VARCHAR}, 
      #{jpassword,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{syncStatus,jdbcType=VARCHAR}, 
      #{jumpUserId,jdbcType=VARCHAR}, #{systemKey,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{flag,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{uompUserId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.JumpUser">
    <!--@mbg.generated-->
    insert into jump_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="account != null">
        ACCOUNT_,
      </if>
      <if test="jumpUser != null">
        JUMP_USER,
      </if>
      <if test="jpassword != null">
        JPASSWORD,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS,
      </if>
      <if test="jumpUserId != null">
        JUMP_USER_ID,
      </if>
      <if test="systemKey != null">
        SYSTEM_KEY,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="flag != null">
        FLAG,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="uompUserId != null">
        UOMP_USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="jumpUser != null">
        #{jumpUser,jdbcType=VARCHAR},
      </if>
      <if test="jpassword != null">
        #{jpassword,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=VARCHAR},
      </if>
      <if test="jumpUserId != null">
        #{jumpUserId,jdbcType=VARCHAR},
      </if>
      <if test="systemKey != null">
        #{systemKey,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="uompUserId != null">
        #{uompUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectInfoByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessUserDTO">
      select jump_user_id as fort_user_id,user_name from jump_user where uomp_user_id =  #{userId} and flag !='9' limit 1
  </select>
  <select id="selectListByUserIds" resultType="cn.gwssi.ecloud.staffpool.dto.UompFortessUserDTO">
    select jump_user_id as fortUserId,system_key as userName from jump_user where flag != '9'
    <if test="idList != null and idList.size > 0">
      and uomp_user_id in
      <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
  <update id="updateDeletetFlag">
    update jump_user set flag = '9',update_time =sysdate() where jump_user_id = #{userId}
  </update>
  <select id="selectJumpUserByUserId" resultType="java.lang.String">
    select jump_user_id from JUMP_USER where uomp_user_id = #{userId} and flag != '9' limit 1
  </select>
  <select id="selectUserIdByUser" resultType="java.lang.String">
    select JUMP_USER_ID from JUMP_USER where JUMP_USER=#{account}
  </select>
  <update id="updateJumpUser">
    update JUMP_USER set user_name=#{name},EMAIL=#{email},update_time = sysdate() where JUMP_USER_ID=#{id}
  </update>
</mapper>