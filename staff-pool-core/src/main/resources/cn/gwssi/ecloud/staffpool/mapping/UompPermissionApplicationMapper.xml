<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPermissionApplicationMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication">
    <!--@mbg.generated-->
    <!--@Table uomp_permission_application-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLY_CODE" jdbcType="VARCHAR" property="applyCode" />
    <result column="APPLY_TITLE" jdbcType="VARCHAR" property="applyTitle" />
    <result column="APPLY_USER_ID" jdbcType="VARCHAR" property="applyUserId" />
    <result column="APPLY_USER_NAME" jdbcType="VARCHAR" property="applyUserName" />
    <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="APPLY_MATTER" jdbcType="VARCHAR" property="applyMatter" />
    <result column="INST_ID" jdbcType="VARCHAR" property="instId" />
    <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_CODE, APPLY_TITLE, APPLY_USER_ID, APPLY_USER_NAME, APPLY_TIME, APPLY_MATTER, 
    INST_ID, APPLY_STATUS, CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, 
    UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_permission_application
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_permission_application
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication">
    <!--@mbg.generated-->
    insert into uomp_permission_application (ID, APPLY_CODE, APPLY_TITLE, 
      APPLY_USER_ID, APPLY_USER_NAME, APPLY_TIME, 
      APPLY_MATTER, INST_ID, APPLY_STATUS, 
      CREATE_BY, CREATE_TIME, CREATE_ORG_ID, 
      UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, 
      DEL_FLAG)
    values (#{id,jdbcType=VARCHAR}, #{applyCode,jdbcType=VARCHAR}, #{applyTitle,jdbcType=VARCHAR}, 
      #{applyUserId,jdbcType=VARCHAR}, #{applyUserName,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{applyMatter,jdbcType=VARCHAR}, #{instId,jdbcType=VARCHAR}, #{applyStatus,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, 
      #{delFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication">
    <!--@mbg.generated-->
    insert into uomp_permission_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applyCode != null">
        APPLY_CODE,
      </if>
      <if test="applyTitle != null">
        APPLY_TITLE,
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID,
      </if>
      <if test="applyUserName != null">
        APPLY_USER_NAME,
      </if>
      <if test="applyTime != null">
        APPLY_TIME,
      </if>
      <if test="applyMatter != null">
        APPLY_MATTER,
      </if>
      <if test="instId != null">
        INST_ID,
      </if>
      <if test="applyStatus != null">
        APPLY_STATUS,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applyCode != null">
        #{applyCode,jdbcType=VARCHAR},
      </if>
      <if test="applyTitle != null">
        #{applyTitle,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyMatter != null">
        #{applyMatter,jdbcType=VARCHAR},
      </if>
      <if test="instId != null">
        #{instId,jdbcType=VARCHAR},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication">
    <!--@mbg.generated-->
    update uomp_permission_application
    set APPLY_CODE = #{applyCode,jdbcType=VARCHAR},
      APPLY_TITLE = #{applyTitle,jdbcType=VARCHAR},
      APPLY_USER_ID = #{applyUserId,jdbcType=VARCHAR},
      APPLY_USER_NAME = #{applyUserName,jdbcType=VARCHAR},
      APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      APPLY_MATTER = #{applyMatter,jdbcType=VARCHAR},
      INST_ID = #{instId,jdbcType=VARCHAR},
      APPLY_STATUS = #{applyStatus,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="getList" resultType="cn.gwssi.ecloud.staffpool.dto.UompPermissionListDTO">
    select
    uaa.ID as id,
    uaa.APPLY_TITLE as applyTitle,
    uaa.APPLY_USER_NAME as applyUserName,
    uaa.APPLY_TIME as applyTime,
    uaa.APPLY_STATUS as applyStatus,
    uaa.INST_ID as instId
    from UOMP_PERMISSION_APPLICATION uaa
    inner join bpm_instance bi on uaa.INST_ID = bi.id_ and bi.status_ != 'discard'
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
    <if test="orderBySql!=null">
      ORDER BY ${orderBySql}
    </if>
  </select>
</mapper>