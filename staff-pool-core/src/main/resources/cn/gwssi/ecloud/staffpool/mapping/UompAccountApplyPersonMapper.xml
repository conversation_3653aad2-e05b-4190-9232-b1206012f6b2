<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompAccountApplyPersonMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson">
    <!--@mbg.generated-->
    <!--@Table uomp_account_apply_person-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLY_ID" jdbcType="VARCHAR" property="applyId" />
    <result column="PERSON_ID" jdbcType="VARCHAR" property="personId" />
    <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName" />
    <result column="ROLE" jdbcType="VARCHAR" property="role" />
    <result column="PERMISSION" jdbcType="VARCHAR" property="permission" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="ACCOUNT_NUM" jdbcType="VARCHAR" property="accountNum" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="ENTRY_DATE" jdbcType="VARCHAR" property="entryDate" />
    <result column="PERSON_JSON" jdbcType="VARCHAR" property="personJson" />
    <result column="AUTHORIZATION_STATUS" jdbcType="VARCHAR" property="authorizationStatus" />
    <result column="ORG_USER_ID" jdbcType="VARCHAR" property="orgUserId" />
    <result column="MAINTENANCE_GROUP_ID" jdbcType="VARCHAR" property="maintenanceGroupId" />
    <result column="MAINTENANCE_GROUP_JSON" jdbcType="VARCHAR" property="maintenanceGroupJson" />
    <result column="ENGAGEMENT_PROJECT_ID" jdbcType="VARCHAR" property="engagementProjectId" />
    <result column="ENGAGEMENT_PROJECT_JSON" jdbcType="VARCHAR" property="engagementProjectJson" />
  </resultMap>

  <sql id="roleKeyword" databaseId="mysql">
    `ROLE`
  </sql>
  <sql id="roleKeyword">
    "ROLE"
  </sql>

  <sql id="positionKeyword" databaseId="mysql">
    `POSITION`
  </sql>
  <sql id="positionKeyword">
    "POSITION"
  </sql>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_ID, PERSON_ID, PERSON_NAME,<include refid="roleKeyword"/> , PERMISSION, <include refid="positionKeyword"/>, ACCOUNT_NUM,
    CREATE_BY, CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, ENTRY_DATE,
    PERSON_JSON, AUTHORIZATION_STATUS, ORG_USER_ID, MAINTENANCE_GROUP_ID, MAINTENANCE_GROUP_JSON,
    ENGAGEMENT_PROJECT_ID, ENGAGEMENT_PROJECT_JSON
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_account_apply_person
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_account_apply_person
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson">
    <!--@mbg.generated-->
    insert into uomp_account_apply_person (ID, APPLY_ID, PERSON_ID,
      PERSON_NAME, <include refid="roleKeyword"/>, PERMISSION,
      <include refid="positionKeyword"/>, ACCOUNT_NUM, CREATE_BY,
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY,
      UPDATE_TIME, UPDATE_ORG_ID, ENTRY_DATE,
      PERSON_JSON, AUTHORIZATION_STATUS, ORG_USER_ID,
      MAINTENANCE_GROUP_ID, MAINTENANCE_GROUP_JSON,
      ENGAGEMENT_PROJECT_ID, ENGAGEMENT_PROJECT_JSON
      )
    values (#{id,jdbcType=VARCHAR}, #{applyId,jdbcType=VARCHAR}, #{personId,jdbcType=VARCHAR},
      #{personName,jdbcType=VARCHAR}, #{role,jdbcType=VARCHAR}, #{permission,jdbcType=VARCHAR},
      #{position,jdbcType=VARCHAR}, #{accountNum,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{entryDate,jdbcType=VARCHAR},
      #{personJson,jdbcType=VARCHAR}, #{authorizationStatus,jdbcType=VARCHAR}, #{orgUserId,jdbcType=VARCHAR},
      #{maintenanceGroupId,jdbcType=VARCHAR}, #{maintenanceGroupJson,jdbcType=VARCHAR},
      #{engagementProjectId,jdbcType=VARCHAR}, #{engagementProjectJson,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson">
    <!--@mbg.generated-->
    insert into uomp_account_apply_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applyId != null">
        APPLY_ID,
      </if>
      <if test="personId != null">
        PERSON_ID,
      </if>
      <if test="personName != null">
        PERSON_NAME,
      </if>
      <if test="role != null">
        <include refid="roleKeyword"/>,
      </if>
      <if test="permission != null">
        PERMISSION,
      </if>
      <if test="position != null">
        <include refid="positionKeyword"/>,
      </if>
      <if test="accountNum != null">
        ACCOUNT_NUM,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="entryDate != null">
        ENTRY_DATE,
      </if>
      <if test="personJson != null">
        PERSON_JSON,
      </if>
      <if test="authorizationStatus != null">
        AUTHORIZATION_STATUS,
      </if>
      <if test="orgUserId != null">
        ORG_USER_ID,
      </if>
      <if test="maintenanceGroupId != null">
        MAINTENANCE_GROUP_ID,
      </if>
      <if test="maintenanceGroupJson != null">
        MAINTENANCE_GROUP_JSON,
      </if>
      <if test="engagementProjectId != null">
        ENGAGEMENT_PROJECT_ID,
      </if>
      <if test="engagementProjectJson != null">
        ENGAGEMENT_PROJECT_JSON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=VARCHAR},
      </if>
      <if test="personId != null">
        #{personId,jdbcType=VARCHAR},
      </if>
      <if test="personName != null">
        #{personName,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="accountNum != null">
        #{accountNum,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="entryDate != null">
        #{entryDate,jdbcType=VARCHAR},
      </if>
      <if test="personJson != null">
        #{personJson,jdbcType=VARCHAR},
      </if>
      <if test="authorizationStatus != null">
        #{authorizationStatus,jdbcType=VARCHAR},
      </if>
      <if test="orgUserId != null">
        #{orgUserId,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceGroupId != null">
        #{maintenanceGroupId,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceGroupJson != null">
        #{maintenanceGroupJson,jdbcType=VARCHAR},
      </if>
      <if test="engagementProjectId != null">
        #{engagementProjectId,jdbcType=VARCHAR},
      </if>
      <if test="engagementProjectJson != null">
        #{engagementProjectJson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson">
    <!--@mbg.generated-->
    update uomp_account_apply_person
    set APPLY_ID = #{applyId,jdbcType=VARCHAR},
      PERSON_ID = #{personId,jdbcType=VARCHAR},
      PERSON_NAME = #{personName,jdbcType=VARCHAR},
      <include refid="roleKeyword"/> = #{role,jdbcType=VARCHAR},
      PERMISSION = #{permission,jdbcType=VARCHAR},
      <include refid="positionKeyword"/> = #{position,jdbcType=VARCHAR},
      ACCOUNT_NUM = #{accountNum,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      ENTRY_DATE = #{entryDate,jdbcType=VARCHAR},
      PERSON_JSON = #{personJson,jdbcType=VARCHAR},
      AUTHORIZATION_STATUS = #{authorizationStatus,jdbcType=VARCHAR},
      ORG_USER_ID = #{orgUserId,jdbcType=VARCHAR},
      MAINTENANCE_GROUP_ID = #{maintenanceGroupId,jdbcType=VARCHAR},
      MAINTENANCE_GROUP_JSON = #{maintenanceGroupJson,jdbcType=VARCHAR},
      ENGAGEMENT_PROJECT_ID = #{engagementProjectId,jdbcType=VARCHAR},
      ENGAGEMENT_PROJECT_JSON = #{engagementProjectJson,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByOrguserIdAccountNumId">
    update
      uomp_account_apply_person
    set
      ORG_USER_ID = #{orgUserId,jdbcType=VARCHAR},
      ACCOUNT_NUM = #{accountNum,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="upDateAuthorizationStatusByIds">
    update
      UOMP_ACCOUNT_APPLY_PERSON
    set
    AUTHORIZATION_STATUS = '1'
    where ID in
    <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>
  <!-- 账号授权 参数：申请账号人员行id -->
  <select id="selectApplyInfoById" resultType="cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson">
    select
      a.ORG_USER_ID as orgUserId,
      a.ROLE as role,
      a.POSITION as position,
      a.AUTHORIZATION_STATUS as authorizationStatus,
      c.ORG_GROUP_ID as maintenanceGroupId
    from
      UOMP_ACCOUNT_APPLY_PERSON a
    left join UOMP_PERSON_INFO b on a.PERSON_ID = b.ID
    left join UOMP_ORG_GROUP c on b.ORG_GROUP_ID = c.ID_
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
  </select>
  <select id="selectUserInfoByInstId" resultType="cn.gwssi.ecloud.staffpool.dto.UompAccountApplyPersonDTO">
    select u.id_ as user_id,i.account as account_num,p.id, p.person_name, role, permission, "position", account_num,
    p.create_by, p.create_time, p.create_org_id, p.update_by, p.update_time, p.update_org_id
    from uomp_account_apply_person p
    inner join  uomp_account_apply a on a.id = p.APPLY_ID
    inner join UOMP_PERSON_INFO i on i.ID = p.person_id
    left join org_user u on u.ACCOUNT_ = p.ACCOUNT_NUM
    where  a.inst_id = #{instId}
    and p.ROLE like '%JS_ROLE_USER%'
    and p.account_num not in (select account_ from jump_user where flag != '9')
  </select>
  <select id="selectAllByIds" resultType="cn.gwssi.ecloud.staffpool.dto.UompAccountApplyPersonDTO">
      select u.id_ as user_id,i.account as account_num,p.id, p.person_name, role, permission, "position", account_num,
      p.create_by, p.create_time, p.create_org_id, p.update_by, p.update_time, p.update_org_id
      from uomp_account_apply_person p
      inner join  uomp_account_apply a on a.id = p.APPLY_ID
      inner join UOMP_PERSON_INFO i on i.ID = p.person_id
      left join org_user u on u.ACCOUNT_ = p.ACCOUNT_NUM
      where  p.id in
      <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
      and p.ROLE like '%JS_ROLE_USER%'
      and p.account_num not in (select account_ from jump_user where flag != '9')
  </select>

  <!--<select id="selectSystemByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
      select uasm.ID as id, uasm.APPLICATION_SYSTEM_NAME as name from uomp_account_apply_person uaap
       inner join uomp_account_apply uaa on uaa.ID = uaap.APPLY_ID
       left join uomp_application_system_management uasm  on uasm.ID = uaap.ENGAGEMENT_PROJECT_ID
       where uaa.DEL_FLAG = '0'
       and uaap.PERSON_ID = #{userId}
       and uaa.STATUS = '2'
       order by uaap.CREATE_TIME desc limit 1
  </select>-->
  <select id="selectSystemByUserId" resultType="cn.gwssi.ecloud.staffpool.dto.BaseDTO">
    select uasm.ID as id, uasm.APPLICATION_SYSTEM_NAME as name from uomp_account_apply_person uaap
    left join uomp_application_system_management uasm  on uasm.ID = uaap.ENGAGEMENT_PROJECT_ID
    where uaap.PERSON_ID = #{userId}
    and uaap.AUTHORIZATION_STATUS = '1'
    order by uaap.CREATE_TIME desc limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2024-06-24-->
  <select id="selectAllByPersonId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from uomp_account_apply_person
    where PERSON_ID=#{personId,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2024-06-24-->
  <select id="selectOneByPersonId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from uomp_account_apply_person
    where PERSON_ID=#{personId,jdbcType=VARCHAR}
    limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2024-06-26-->
  <select id="selectAllByApplyIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from uomp_account_apply_person
    where APPLY_ID in
    <foreach item="item" index="index" collection="applyIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>
    <select id="selectInfoByPersonId"
            resultMap="BaseResultMap">
      select uaap.* from
      uomp_account_apply_person uaap
      left join uomp_account_apply uaa on uaap.APPLY_ID = uaa.ID and uaa.DEL_FLAG = '0' AND uaa.STATUS = '2'
      where uaap.PERSON_ID = #{userId} and ACCOUNT_NUM is not null
      order by uaap.CREATE_TIME desc
      limit 0,1
    </select>
</mapper>
