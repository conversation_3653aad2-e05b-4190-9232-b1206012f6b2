<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompApplicationSystemRelationMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    <!--@mbg.generated-->
    <!--@Table uomp_application_system_relation-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLICATION_SYSTEM_MANAGEMENT_ID" jdbcType="VARCHAR" property="applicationSystemManagementId" />
    <result column="RELATION_ID" jdbcType="VARCHAR" property="relationId" />
    <result column="RELATION_TYPE" jdbcType="VARCHAR" property="relationType" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLICATION_SYSTEM_MANAGEMENT_ID, RELATION_ID, RELATION_TYPE, CREATE_BY, CREATE_TIME, 
    CREATE_ORG_ID, UPDATE_BY, UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from uomp_application_system_relation
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_application_system_relation
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    <!--@mbg.generated-->
    insert into uomp_application_system_relation (ID, APPLICATION_SYSTEM_MANAGEMENT_ID, 
      RELATION_ID, RELATION_TYPE, CREATE_BY, 
      CREATE_TIME, CREATE_ORG_ID, UPDATE_BY, 
      UPDATE_TIME, UPDATE_ORG_ID, DEL_FLAG
      )
    values (#{id,jdbcType=VARCHAR}, #{applicationSystemManagementId,jdbcType=VARCHAR}, 
      #{relationId,jdbcType=VARCHAR}, #{relationType,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOrgId,jdbcType=VARCHAR}, #{delFlag,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    <!--@mbg.generated-->
    insert into uomp_application_system_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applicationSystemManagementId != null">
        APPLICATION_SYSTEM_MANAGEMENT_ID,
      </if>
      <if test="relationId != null">
        RELATION_ID,
      </if>
      <if test="relationType != null">
        RELATION_TYPE,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applicationSystemManagementId != null">
        #{applicationSystemManagementId,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    <!--@mbg.generated-->
    update uomp_application_system_relation
    <set>
      <if test="applicationSystemManagementId != null">
        APPLICATION_SYSTEM_MANAGEMENT_ID = #{applicationSystemManagementId,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        RELATION_ID = #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        RELATION_TYPE = #{relationType,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        CREATE_BY = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOrgId != null">
        UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    <!--@mbg.generated-->
    update uomp_application_system_relation
    set APPLICATION_SYSTEM_MANAGEMENT_ID = #{applicationSystemManagementId,jdbcType=VARCHAR},
      RELATION_ID = #{relationId,jdbcType=VARCHAR},
      RELATION_TYPE = #{relationType,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateDelFlag" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation">
    update uomp_application_system_relation
    set UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_ORG_ID = #{updateOrgId,jdbcType=VARCHAR},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
    where APPLICATION_SYSTEM_MANAGEMENT_ID = #{applicationSystemManagementId,jdbcType=VARCHAR}
  </update>
  <update id="updateDelFlagByIds">
    update uomp_application_system_relation
    set
      DEL_FLAG = '1'
    <where>
      <if test="whereSql!=null">
        ${whereSql}
      </if>
    </where>
  </update>
    <select id="query" resultMap="BaseResultMap">
        SELECT * from uomp_application_system_relation
        <where>
            <if test="whereSql!=null">
                ${whereSql}
            </if>
        </where>
        <if test="orderBySql!=null">
            ORDER BY ${orderBySql}
        </if>
    </select>
</mapper>