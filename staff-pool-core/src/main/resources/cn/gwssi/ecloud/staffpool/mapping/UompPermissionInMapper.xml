<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompPermissionInMapper">
  <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionIn">
    <!--@mbg.generated-->
    <!--@Table uomp_permission_in-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="APPLY_ID" jdbcType="VARCHAR" property="applyId" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="USER_JSON" jdbcType="VARCHAR" property="userJson" />
    <result column="EXISTING_ROLES" jdbcType="VARCHAR" property="existingRoles" />
    <result column="EXISTING_POSITIONS" jdbcType="VARCHAR" property="existingPositions" />
    <result column="MAINTENANCE_GROUP_NAME" jdbcType="VARCHAR" property="maintenanceGroupName" />
    <result column="APPLY_TYPE" jdbcType="VARCHAR" property="applyType" />
    <result column="ROLE" jdbcType="VARCHAR" property="role" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="AUTHORIZATION_STATUS" jdbcType="VARCHAR" property="authorizationStatus" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag" />
    <result column="SYSTEM_NAME" jdbcType="VARCHAR" property="systemName" />
  </resultMap>

  <sql id="roleKeyword" databaseId="mysql">
    `ROLE`
  </sql>
  <sql id="roleKeyword">
    "ROLE"
  </sql>

  <sql id="positionKeyword" databaseId="mysql">
    `POSITION`
  </sql>
  <sql id="positionKeyword">
    "POSITION"
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, APPLY_ID, USER_ID, USER_NAME, USER_JSON, EXISTING_ROLES, EXISTING_POSITIONS,
    MAINTENANCE_GROUP_NAME, APPLY_TYPE, <include refid="roleKeyword"/> , <include refid="positionKeyword"/> , AUTHORIZATION_STATUS, CREATE_BY,
    CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG, SYSTEM_NAME
  </sql>
  <select id="get" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from uomp_permission_in
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="remove" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from uomp_permission_in
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="create" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionIn">
    <!--@mbg.generated-->
    insert into uomp_permission_in (ID, APPLY_ID, USER_ID,
      USER_NAME, USER_JSON, EXISTING_ROLES,
      EXISTING_POSITIONS, MAINTENANCE_GROUP_NAME,
      APPLY_TYPE, <include refid="roleKeyword"/> , <include refid="positionKeyword"/> ,
      AUTHORIZATION_STATUS, CREATE_BY, CREATE_TIME,
      UPDATE_BY, UPDATE_TIME, DEL_FLAG,SYSTEM_NAME
      )
    values (#{id,jdbcType=VARCHAR}, #{applyId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
      #{userName,jdbcType=VARCHAR}, #{userJson,jdbcType=VARCHAR}, #{existingRoles,jdbcType=VARCHAR},
      #{existingPositions,jdbcType=VARCHAR}, #{maintenanceGroupName,jdbcType=VARCHAR},
      #{applyType,jdbcType=VARCHAR}, #{role,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR},
      #{authorizationStatus,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}, #{systemName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionIn">
    <!--@mbg.generated-->
    insert into uomp_permission_in
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="applyId != null">
        APPLY_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="userJson != null">
        USER_JSON,
      </if>
      <if test="existingRoles != null">
        EXISTING_ROLES,
      </if>
      <if test="existingPositions != null">
        EXISTING_POSITIONS,
      </if>
      <if test="maintenanceGroupName != null">
        MAINTENANCE_GROUP_NAME,
      </if>
      <if test="applyType != null">
        APPLY_TYPE,
      </if>
      <if test="role != null">
        <include refid="roleKeyword"/> ,
      </if>
      <if test="position != null">
        <include refid="positionKeyword"/> ,
      </if>
      <if test="authorizationStatus != null">
        AUTHORIZATION_STATUS,
      </if>
      <if test="createBy != null">
        CREATE_BY,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateBy != null">
        UPDATE_BY,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="delFlag != null">
        DEL_FLAG,
      </if>
      <if test="systemName != null">
        SYSTEM_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userJson != null">
        #{userJson,jdbcType=VARCHAR},
      </if>
      <if test="existingRoles != null">
        #{existingRoles,jdbcType=VARCHAR},
      </if>
      <if test="existingPositions != null">
        #{existingPositions,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceGroupName != null">
        #{maintenanceGroupName,jdbcType=VARCHAR},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="authorizationStatus != null">
        #{authorizationStatus,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="systemName != null">
        #{systemName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompPermissionIn">
    <!--@mbg.generated-->
    update uomp_permission_in
    set APPLY_ID = #{applyId,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      USER_JSON = #{userJson,jdbcType=VARCHAR},
      EXISTING_ROLES = #{existingRoles,jdbcType=VARCHAR},
      EXISTING_POSITIONS = #{existingPositions,jdbcType=VARCHAR},
      MAINTENANCE_GROUP_NAME = #{maintenanceGroupName,jdbcType=VARCHAR},
      APPLY_TYPE = #{applyType,jdbcType=VARCHAR},
      <include refid="roleKeyword"/>  = #{role,jdbcType=VARCHAR},
      <include refid="positionKeyword"/>  = #{position,jdbcType=VARCHAR},
      AUTHORIZATION_STATUS = #{authorizationStatus,jdbcType=VARCHAR},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
      SYSTEM_NAME = #{systemName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateAuthorizationStatus">
    update UOMP_PERMISSION_IN set AUTHORIZATION_STATUS = #{status} where ID = #{id}
  </update>
</mapper>
