<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloud.staffpool.core.dao.UompExitApplicationMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.ecloud.staffpool.core.entity.UompExitApplication">
        <!--@mbg.generated-->
        <!--@Table uomp_exit_application-->
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="APPLY_PERSON_ID" jdbcType="VARCHAR" property="applyPersonId"/>
        <result column="OUT_APPLY_CODE" jdbcType="VARCHAR" property="outApplyCode"/>
        <result column="PERSON_NAME" jdbcType="VARCHAR" property="personName"/>
        <result column="TEL" jdbcType="VARCHAR" property="tel"/>
        <result column="ONLINE_TIME" jdbcType="VARCHAR" property="onlineTime"/>
        <result column="MAINTENANCE_GROUP_ID" jdbcType="VARCHAR" property="maintenanceGroupId"/>
        <result column="MAINTENANCE_GROUP_NAME" jdbcType="VARCHAR" property="maintenanceGroupName"/>
        <result column="POST_ID" jdbcType="VARCHAR" property="postId"/>
        <result column="POST_NAME" jdbcType="VARCHAR" property="postName"/>
        <result column="WORKING_COMPANY" jdbcType="VARCHAR" property="workingCompany"/>
        <result column="JOB_ACCEPT_ID" jdbcType="VARCHAR" property="jobAcceptId"/>
        <result column="JOB_ACCEPT_NAME" jdbcType="VARCHAR" property="jobAcceptName"/>
        <result column="PLAN_OUT_TIME" jdbcType="TIMESTAMP" property="planOutTime"/>
        <result column="OUT_REASON" jdbcType="VARCHAR" property="outReason"/>
        <result column="OUT_REASON_FILE" jdbcType="VARCHAR" property="outReasonFile"/>
        <result column="JOB_HANDOVER_SITUATION" jdbcType="VARCHAR" property="jobHandoverSituation"/>
        <result column="JOB_HANDOVER_SITUATION_FILE" jdbcType="VARCHAR" property="jobHandoverSituationFile"/>
        <result column="MANAGER_COMMENT" jdbcType="VARCHAR" property="managerComment"/>
        <result column="LEADER_COMMENT" jdbcType="VARCHAR" property="leaderComment"/>
        <result column="IS_DELETE" jdbcType="VARCHAR" property="isDelete"/>
        <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime"/>
        <result column="APPLY_STATUS" jdbcType="VARCHAR" property="applyStatus"/>
        <result column="INST_ID" jdbcType="VARCHAR" property="instId"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UPDATE_ORG_ID" jdbcType="VARCHAR" property="updateOrgId"/>
        <result column="DEL_FLAG" jdbcType="VARCHAR" property="delFlag"/>
        <result column="OUT_APPLY_TITLE" jdbcType="VARCHAR" property="outApplyTitle"/>
        <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser"/>
        <result column="PERSON_CARD" jdbcType="VARCHAR" property="personCard"/>
        <result column="WORKING_COMPANY_ID" jdbcType="VARCHAR" property="workingCompanyId"/>
        <result column="WORKING_COMPANY_JSON" jdbcType="VARCHAR" property="workingCompanyJson"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        APPLY_PERSON_ID,
        OUT_APPLY_CODE,
        PERSON_NAME,
        TEL,
        ONLINE_TIME,
        MAINTENANCE_GROUP_ID,
        MAINTENANCE_GROUP_NAME,
        POST_ID,
        POST_NAME,
        WORKING_COMPANY,
        JOB_ACCEPT_ID,
        JOB_ACCEPT_NAME,
        PLAN_OUT_TIME,
        OUT_REASON,
        OUT_REASON_FILE,
        JOB_HANDOVER_SITUATION,
        JOB_HANDOVER_SITUATION_FILE,
        MANAGER_COMMENT,
        LEADER_COMMENT,
        IS_DELETE,
        OUT_TIME,
        APPLY_STATUS,
        INST_ID,
        CREATE_BY,
        CREATE_TIME,
        CREATE_ORG_ID,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_ORG_ID,
        DEL_FLAG,
        OUT_APPLY_TITLE,
        CREATE_USER,
        PERSON_CARD,
        WORKING_COMPANY_ID,
        WORKING_COMPANY_JSON
    </sql>
    <insert id="insertSelective" parameterType="cn.gwssi.ecloud.staffpool.core.entity.UompExitApplication">
        <!--@mbg.generated-->
        insert into uomp_exit_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="applyPersonId != null">
                APPLY_PERSON_ID,
            </if>
            <if test="outApplyCode != null">
                OUT_APPLY_CODE,
            </if>
            <if test="personName != null">
                PERSON_NAME,
            </if>
            <if test="tel != null">
                TEL,
            </if>
            <if test="onlineTime != null">
                ONLINE_TIME,
            </if>
            <if test="maintenanceGroupId != null">
                MAINTENANCE_GROUP_ID,
            </if>
            <if test="maintenanceGroupName != null">
                MAINTENANCE_GROUP_NAME,
            </if>
            <if test="postId != null">
                POST_ID,
            </if>
            <if test="postName != null">
                POST_NAME,
            </if>
            <if test="workingCompany != null">
                WORKING_COMPANY,
            </if>
            <if test="jobAcceptId != null">
                JOB_ACCEPT_ID,
            </if>
            <if test="jobAcceptName != null">
                JOB_ACCEPT_NAME,
            </if>
            <if test="planOutTime != null">
                PLAN_OUT_TIME,
            </if>
            <if test="outReason != null">
                OUT_REASON,
            </if>
            <if test="outReasonFile != null">
                OUT_REASON_FILE,
            </if>
            <if test="jobHandoverSituation != null">
                JOB_HANDOVER_SITUATION,
            </if>
            <if test="jobHandoverSituationFile != null">
                JOB_HANDOVER_SITUATION_FILE,
            </if>
            <if test="managerComment != null">
                MANAGER_COMMENT,
            </if>
            <if test="leaderComment != null">
                LEADER_COMMENT,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="outTime != null">
                OUT_TIME,
            </if>
            <if test="applyStatus != null">
                APPLY_STATUS,
            </if>
            <if test="instId != null">
                INST_ID,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createOrgId != null">
                CREATE_ORG_ID,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="updateOrgId != null">
                UPDATE_ORG_ID,
            </if>
            <if test="delFlag != null">
                DEL_FLAG,
            </if>
            <if test="outApplyTitle != null">
                OUT_APPLY_TITLE,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="personCard != null">
                PERSON_CARD,
            </if>
            <if test="workingCompanyId != null">
                WORKING_COMPANY_ID,
            </if>
            <if test="workingCompanyJson != null">
                WORKING_COMPANY_JSON,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="applyPersonId != null">
                #{applyPersonId,jdbcType=VARCHAR},
            </if>
            <if test="outApplyCode != null">
                #{outApplyCode,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="tel != null">
                #{tel,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupId != null">
                #{maintenanceGroupId,jdbcType=VARCHAR},
            </if>
            <if test="maintenanceGroupName != null">
                #{maintenanceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                #{postId,jdbcType=VARCHAR},
            </if>
            <if test="postName != null">
                #{postName,jdbcType=VARCHAR},
            </if>
            <if test="workingCompany != null">
                #{workingCompany,jdbcType=VARCHAR},
            </if>
            <if test="jobAcceptId != null">
                #{jobAcceptId,jdbcType=VARCHAR},
            </if>
            <if test="jobAcceptName != null">
                #{jobAcceptName,jdbcType=VARCHAR},
            </if>
            <if test="planOutTime != null">
                #{planOutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="outReason != null">
                #{outReason,jdbcType=VARCHAR},
            </if>
            <if test="outReasonFile != null">
                #{outReasonFile,jdbcType=VARCHAR},
            </if>
            <if test="jobHandoverSituation != null">
                #{jobHandoverSituation,jdbcType=VARCHAR},
            </if>
            <if test="jobHandoverSituationFile != null">
                #{jobHandoverSituationFile,jdbcType=VARCHAR},
            </if>
            <if test="managerComment != null">
                #{managerComment,jdbcType=VARCHAR},
            </if>
            <if test="leaderComment != null">
                #{leaderComment,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=VARCHAR},
            </if>
            <if test="outTime != null">
                #{outTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyStatus != null">
                #{applyStatus,jdbcType=VARCHAR},
            </if>
            <if test="instId != null">
                #{instId,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOrgId != null">
                #{createOrgId,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOrgId != null">
                #{updateOrgId,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=VARCHAR},
            </if>
            <if test="outApplyTitle != null">
                #{outApplyTitle,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="personCard != null">
                #{personCard,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyId != null">
                #{workingCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="workingCompanyJson != null">
                #{workingCompanyJson,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectAllById" resultType="cn.gwssi.ecloud.staffpool.dto.ExitApplyDTO">
        select ID                   as id,
               OUT_APPLY_CODE       as outApplyCode,
               PERSON_NAME          as personName,
               TEL                  as tel,
               ONLINE_TIME          as onlineTime,
               POST_NAME            as postName,
               WORKING_COMPANY      as workingCompany,
               WORKING_COMPANY_ID   as workingCompanyId,
               WORKING_COMPANY_JSON as workingCompanyJson,
               PLAN_OUT_TIME        as planOutTime,
               OUT_REASON           as outReason,
               OUT_REASON_FILE      as outReasonFileJson,
               IS_DELETE            as isDelete,
               OUT_TIME             as outTime,
               INST_ID              as instId,
               CREATE_BY            as createBy
        from UOMP_EXIT_APPLICATION
        where ID = #{exitId}
          and APPLY_STATUS = '2'
          and DEL_FLAG = '0'
    </select>

    <!--  //通过person_card 查询该人员是否有退场未完成的流程，有得话暂时不让入场-->
    <select id="countByPersonCard" resultType="java.lang.Integer">
        select count(*)
        from UOMP_EXIT_APPLICATION
        where DEL_FLAG = '0'
          AND PERSON_CARD = #{personCard}
          AND APPLY_STATUS != '2'
    </select>

    <select id="countByOutTime" resultType="java.lang.Integer">
        select count(ID) as num
        from UOMP_EXIT_APPLICATION
        where DEL_FLAG = '0'
          and APPLY_STATUS = '2'
          and OUT_TIME like concat(#{time}, '%')
    </select>

    <update id="updateIsDeleteById">
        update UOMP_EXIT_APPLICATION
        set IS_DELETE = '0'
        where ID = #{id}
    </update>

    <select id="countByApplyStatus" resultType="java.lang.Integer">
        select count(1)
        from UOMP_EXIT_APPLICATION
        where APPLY_STATUS = '2'
          and DEL_FLAG = '0'
        <if test="outTimeBegin != null and outTimeBegin != ''">
            and OUT_TIME >= #{outTimeBegin}
        </if>
        <if test="outTimeEnd != null and outTimeEnd != ''">
            and OUT_TIME &lt;= #{outTimeEnd}
        </if>
    </select>

    <select id="countByApplyStatusNew" resultType="java.lang.Integer">
        select count(*) from (
        select PERSON_CARD
        from UOMP_EXIT_APPLICATION
        where APPLY_STATUS = '2'
        and DEL_FLAG = '0'
        <if test="outTimeBegin != null and outTimeBegin != ''">
            and OUT_TIME >= #{outTimeBegin}
        </if>
        <if test="outTimeEnd != null and outTimeEnd != ''">
            and OUT_TIME &lt;= #{outTimeEnd}
        </if>
        group by PERSON_CARD) a
    </select>
</mapper>
