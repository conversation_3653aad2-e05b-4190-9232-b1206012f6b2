<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="staff-pool-1" author="wjw" failOnError="false">
        <createTable remarks="入场申请基础信息表" tableName="UOMP_ADMISSION_APPLICATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_CODE" remarks="编号" type="VARCHAR(64)"/>
            <column name="APPLY_TITLE" remarks="标题" type="VARCHAR(200)"/>
            <column name="APPLY_USER_ID" remarks="申请人id" type="VARCHAR(64)"/>
            <column name="APPLY_USER_NAME" remarks="申请人名称" type="VARCHAR(200)"/>
            <column name="APPLY_TIME" remarks="申请时间" type="timestamp"/>
            <column name="APPLY_MATTER" remarks="申请事项" type="VARCHAR(300)"/>
            <column name="MANAGER_COMMENT" remarks="运维经理意见" type="VARCHAR(900)"/>
            <column name="LEADER_COMMENT" remarks="甲方运维领导小组意见" type="VARCHAR(900)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="APPLY_STATUS" remarks="审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-2" author="wjw" failOnError="false">
        <createTable remarks="入场申请关联人员信息表" tableName="UOMP_ADMISSION_PERSON">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_ID" remarks="入场申请id" type="VARCHAR(64)"/>
            <column name="PERSON_NAME" remarks="姓名" type="VARCHAR(200)"/>
            <column name="PERSON_CARD" remarks="身份证号" type="VARCHAR(20)"/>
            <column name="ENTRY_DATE" remarks="入职时间" type="VARCHAR(20)"/>
            <column name="EDUCATION" remarks="学历" type="VARCHAR(30)"/>
            <column name="TECHNICAL_POST" remarks="职称" type="VARCHAR(60)"/>
            <column name="WORKING_COMPANY" remarks="所在公司" type="VARCHAR(300)"/>
            <column name="TEL" remarks="联系电话" type="VARCHAR(15)"/>
            <column name="ENGAGEMENT_PROJECT" remarks="应用系统" type="VARCHAR(3000)"/>
            <column name="SCORE" remarks="笔试成绩" type="VARCHAR(5)"/>
            <column name="INTERVIEW_RESULT" remarks="面谈结果" type="VARCHAR(100)"/>
            <column name="MAINTENANCE_GROUP_ID" remarks="进驻运维组id" type="VARCHAR(1000)"/>
            <column name="MAINTENANCE_GROUP_NAME" remarks="进驻运维组" type="VARCHAR(3000)"/>
            <column name="POST_ID" remarks="申请岗位id" type="VARCHAR(1000)"/>
            <column name="POST_NAME" remarks="申请岗位" type="VARCHAR(1000)"/>
            <column name="JOB_CONTENT" remarks="工作内容" type="VARCHAR(600)"/>
            <column name="IS_EXPORT" remarks="是否导入 0-是 1-不是" type="VARCHAR(1)"/>
            <column name="APPLY_STATUS" remarks="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="IN_TIME" remarks="入场时间" type="timestamp"/>
            <column name="OUT_TIME" remarks="退场时间" type="timestamp"/>
            <column name="OUT_APPLY_STATUS" remarks="退场申请状态 0-无申请 1-有申请" type="VARCHAR(1)"/>
            <column name="FILE_INFO" remarks="笔试面试相关附件" type="${clob.type}"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="POST_FILE_INFO" remarks="附件" type="TEXT"/>
            <column name="SERVIC_TYPE" remarks="服务类型" type="VARCHAR(2)"/>
            <column name="SERVICE_LOCATION" remarks="驻场服务地点" type="VARCHAR(50)"/>
            <column name="IS_CLASSIFIED_POSITION" remarks="是否涉密岗位" type="VARCHAR(1)"/>
            <column name="TECHNICAL_DIRECTION" remarks="技术方向" type="VARCHAR(10)"/>
            <column name="ENGAGEMENT_PROJECT_ID" remarks="应用系统id" type="VARCHAR(1000)"/>
            <column name="ENGAGEMENT_PROJECT_JSON" remarks="应用系统json" type="${clob.type}"/>
            <column name="MAINTENANCE_GROUP_JSON" remarks="进驻运维组json" type="${clob.type}"/>
            <column name="PLAN_VISIT_TIME" remarks="预计到访时间" type="timestamp"/>
            <column name="PLAN_OUT_TIME" remarks="计划退出时间" type="timestamp"/>
            <column name="PERSON_ROLE" remarks="角色" type="VARCHAR(100)"/>
            <column name="PERSON_ROLE_ID" remarks="角色 id" type="VARCHAR(100)"/>
            <column name="PERSON_ROLE_JSON" remarks="角色 JSON" type="VARCHAR(2000)"/>
            <column name="DEPART_ID" remarks="主管部门 id" type="VARCHAR(100)"/>
            <column name="DEPART_NAME" remarks="主管部门" type="VARCHAR(100)"/>
            <column name="DEPART_JSON" remarks="主管部门 JSON" type="VARCHAR(2000)"/>
            <column name="POSITION_TYPE" remarks="岗位类型" type="VARCHAR(100)"/>
            <column name="CLASSIFIED_POSITION" remarks="涉密岗位性质" type="VARCHAR(100)"/>
            <column name="POST_JSON" remarks="申请岗位 JSON" type="VARCHAR(2000)"/>
            <column name="WORKING_COMPANY_ID" remarks="所在公司id" type="VARCHAR(300)"/>
            <column name="WORKING_COMPANY_JSON" remarks="所在公司json" type="VARCHAR(300)"/>
            <column name="PERSON_ID" remarks="人员ID" type="VARCHAR(64)"/>
            <column defaultValue="0" name="SEX" remarks="0: 男,  1:女" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-3" author="wjw" failOnError="false">

        <createTable remarks="退场申请接收人数据表" tableName="UOMP_EXIT_ACCEPT_INFO">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="EXIT_APPLY_ID" remarks="退场申请id" type="VARCHAR(64)"/>
            <column name="ENGAGEMENT_PROJECT_ID" remarks="参与项目id" type="VARCHAR(64)"/>
            <column name="ENGAGEMENT_PROJECT_NAME" remarks="参与项目名称" type="VARCHAR(300)"/>
            <column name="ACCEPT_USER_ID" remarks="工作交接人id" type="VARCHAR(64)"/>
            <column name="ACCEPT_USER_NAME" remarks="工作交接人名称" type="VARCHAR(200)"/>
            <column name="ACCEPT_CONTEXT" remarks="工作交接内容" type="VARCHAR(1500)"/>
            <column name="ACCEPT_CONTEXT_FILE" remarks="工作交接附件" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-4" author="wjw" failOnError="false">

        <createTable remarks="退场申请入场申请关系表" tableName="UOMP_EXIT_ADMISSION_RELATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_PERSON_ID" remarks="入场申请人员id" type="VARCHAR(64)"/>
            <column name="EXIT_APPLY_ID" remarks="退场申请id" type="VARCHAR(64)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-5" author="wjw" failOnError="false">

        <createTable remarks="退场申请信息表" tableName="UOMP_EXIT_APPLICATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_PERSON_ID" remarks="退场申请人员id(原-入场申请人员id)" type="VARCHAR(64)"/>
            <column name="OUT_APPLY_CODE" remarks="编号" type="VARCHAR(64)"/>
            <column name="PERSON_NAME" remarks="姓名" type="VARCHAR(200)"/>
            <column name="TEL" remarks="联系电话" type="VARCHAR(15)"/>
            <column name="ONLINE_TIME" remarks="在岗年限" type="VARCHAR(20)"/>
            <column name="MAINTENANCE_GROUP_ID" remarks="进驻运维组id(已废弃)" type="VARCHAR(64)"/>
            <column name="MAINTENANCE_GROUP_NAME" remarks="进驻运维组(已废弃)" type="VARCHAR(100)"/>
            <column name="POST_ID" remarks="技术方向code(原-申请岗位id)" type="VARCHAR(1000)"/>
            <column name="POST_NAME" remarks="技术方向(原-申请岗位名称)" type="VARCHAR(1000)"/>
            <column name="WORKING_COMPANY" remarks="所在公司" type="VARCHAR(300)"/>
            <column name="JOB_ACCEPT_ID" remarks="工作接收人id(已废弃)" type="VARCHAR(64)"/>
            <column name="JOB_ACCEPT_NAME" remarks="工作接收人(已废弃)" type="VARCHAR(500)"/>
            <column name="PLAN_OUT_TIME" remarks="计划退出时间" type="timestamp"/>
            <column name="OUT_REASON" remarks="退出原因" type="VARCHAR(1500)"/>
            <column name="OUT_REASON_FILE" remarks="退场原因附件信息" type="TEXT"/>
            <column name="JOB_HANDOVER_SITUATION" remarks="工作交接情况(已废弃)" type="VARCHAR(1500)"/>
            <column name="JOB_HANDOVER_SITUATION_FILE" remarks="工作交接附件信息(已废弃)" type="TEXT"/>
            <column name="MANAGER_COMMENT" remarks="运维经理意见(已废弃)" type="VARCHAR(1500)"/>
            <column name="LEADER_COMMENT" remarks="甲方运维小组领导意见(已废弃)" type="VARCHAR(1500)"/>
            <column name="IS_DELETE" remarks="是否删除相关权限 0-是 1-否" type="VARCHAR(1)"/>
            <column name="OUT_TIME" remarks="实际退场时间" type="timestamp"/>
            <column name="APPLY_STATUS" remarks="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="OUT_APPLY_TITLE" remarks="标题" type="VARCHAR(60)"/>
            <column name="CREATE_USER" remarks="创建人名称" type="VARCHAR(200)"/>
            <column name="PERSON_CARD" remarks="身份证号" type="VARCHAR(32)"/>
            <column name="WORKING_COMPANY_ID" remarks="所在公司id" type="VARCHAR(300)"/>
            <column name="WORKING_COMPANY_JSON" remarks="所在公司json" type="VARCHAR(300)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-6" author="wjw" failOnError="false">

        <createTable remarks="人员库组织机构表" tableName="UOMP_ORG_GROUP">
            <column name="ID_" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="NAME_" remarks="名称" type="VARCHAR(300)">
                <constraints nullable="false"/>
            </column>
            <column name="PARENT_ID_" remarks="父ID" type="VARCHAR(64)"/>
            <column defaultValueNumeric="100" name="SN_" remarks="排序" type="INT"/>
            <column name="CODE_" remarks="编号" type="VARCHAR(300)">
                <constraints nullable="false"/>
            </column>
            <column name="TYPE_" remarks="类型：0集团，1公司，3部门" type="VARCHAR(192)"/>
            <column name="DESC_" remarks="描述" type="VARCHAR(1500)"/>
            <column name="CREATE_TIME_" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_BY_" remarks="创建人" type="VARCHAR(192)"/>
            <column name="UPDATE_TIME_" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_BY_" remarks="更新人" type="VARCHAR(192)"/>
            <column name="PATH_" remarks="机构路径" type="VARCHAR(4000)"/>
            <column name="SIMPLE_" remarks="简称" type="VARCHAR(60)"/>
            <column defaultValueNumeric="1" name="STATUS_" remarks="状态：0禁用，1正常" type="INT"/>
            <column name="SHOW_NAME_" remarks="显示名称" type="VARCHAR(1000)"/>
            <column name="VIRTUAL_" remarks="是否虚拟:0否,1是" type="INT"/>
            <column name="HISTORY_NAME_" remarks="曾用名" type="VARCHAR(2000)"/>
            <column name="PATH_NAME_" remarks="机构路径" type="VARCHAR(4000)"/>
            <column name="MCODE_" remarks="主编号" type="VARCHAR(300)"/>
            <column name="RESP_NAME" remarks="负责人" type="VARCHAR(300)"/>
            <column name="ORG_GROUP_ID" remarks="组织 id" type="VARCHAR(300)"/>
            <column name="RESP_ID" remarks="负责人 id" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-7" author="wjw" failOnError="false">

        <createTable remarks="临时入场申请表" tableName="UOMP_TEMP_ADMISSION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_NAME" remarks="姓名" type="VARCHAR(200)"/>
            <column name="PERSON_CARD" remarks="身份证号" type="VARCHAR(20)"/>
            <column name="TEL" remarks="联系方式" type="VARCHAR(15)"/>
            <column name="WORKING_COMPANY" remarks="就职公司" type="VARCHAR(300)"/>
            <column name="PLAN_VISIT_TIME" remarks="预计到访时间" type="timestamp"/>
            <column name="REAL_VISIT_TIME" remarks="实际到访时间" type="timestamp"/>
            <column name="DEST_CLERK_ID" remarks="申请人id（原-接待人id）" type="VARCHAR(64)"/>
            <column name="DEST_CLERK_NAME" remarks="申请人（原-接待人）" type="VARCHAR(200)"/>
            <column name="EXIT_TIME" remarks="离场时间" type="timestamp"/>
            <column name="JOB_CONTENT" remarks="工作内容" type="VARCHAR(3000)"/>
            <column name="MANAGER_COMMENT" remarks="运维经理意见" type="VARCHAR(1500)"/>
            <column name="APPLY_STATUS" remarks="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="APPLY_CODE" remarks="编号" type="VARCHAR(64)"/>
            <column name="APPLY_TITLE" remarks="标题" type="VARCHAR(60)"/>
            <column name="BLACKLIST" remarks="黑名单 0-非黑名单 1-是黑名单" type="VARCHAR(10)"/>
            <column name="BLACKLIST_REASON" remarks="加入黑名单原因" type="VARCHAR(300)"/>
            <column name="BASE_ID" remarks="申请基础表id" type="VARCHAR(64)"/>
            <column name="ACCEPT_NAME" remarks="接待人" type="VARCHAR(200)"/>
            <column defaultValue="0" name="SEX" remarks="0: 男,  1:女" type="VARCHAR(100)"/>
            <column name="APPLICAT_DUTY" remarks="职务" type="VARCHAR(100)"/>
            <column name="WORKING_COMPANY_JSON" remarks="就职公司 json" type="VARCHAR(300)"/>
            <column name="ENGAGEMENT_PROJECT_ID" remarks="应用系统id" type="VARCHAR(1000)"/>
            <column name="ENGAGEMENT_PROJECT_JSON" remarks="应用系统json" type="VARCHAR(4000)"/>
            <column name="FILING_STATUS" remarks="备案状态 0:未备案, 1:已备案" type="VARCHAR(100)"/>
            <column name="ENGAGEMENT_PROJECT" remarks="应用系统" type="VARCHAR(3000)"/>
            <column name="WORKING_COMPANY_ID" remarks="就职公司 id" type="VARCHAR(300)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-8" author="wjw" failOnError="false">

        <createTable remarks="临时入场申请基础信息表" tableName="UOMP_TEMP_ADMISSION_BASE">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_CODE" remarks="编号" type="VARCHAR(64)"/>
            <column name="APPLY_TITLE" remarks="标题" type="VARCHAR(200)"/>
            <column name="DEST_CLERK_ID" remarks="申请人id（原-接待人id）" type="VARCHAR(64)"/>
            <column name="DEST_CLERK_NAME" remarks="申请人（原-接待人）" type="VARCHAR(200)"/>
            <column name="PLAN_VISIT_TIME" remarks="预计到访时间" type="timestamp"/>
            <column name="JOB_CONTENT" remarks="工作内容" type="VARCHAR(3000)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="APPLY_STATUS" remarks="审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="ACCEPT_NAME" remarks="接待人" type="VARCHAR(200)"/>
            <column name="APPLY_TIME" remarks="申请时间" type="timestamp"/>
            <column name="ENGAGEMENT_PROJECT_ID" remarks="应用系统id" type="VARCHAR(1000)"/>
            <column name="ENGAGEMENT_PROJECT_JSON" remarks="应用系统json" type="VARCHAR(4000)"/>
            <column name="FILING_STATUS" remarks="备案状态 0:未备案, 1:已备案" type="VARCHAR(100)"/>
            <column name="ENGAGEMENT_PROJECT" remarks="应用系统" type="VARCHAR(3000)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-9" author="wjw" failOnError="false">

        <createTable remarks="人员账号申请" tableName="UOMP_ACCOUNT_APPLY">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TITLE" remarks="标题" type="VARCHAR(255)"/>
            <column name="APPLY_TIME" remarks="申请时间" type="timestamp"/>
            <column name="APPLY_PERSON" remarks="申请人" type="VARCHAR(64)"/>
            <column name="STATUS" remarks="审核状态 1:待审核 2审核通过 3审核不通过" type="VARCHAR(10)"/>
            <column name="APPLY_ITEM" remarks="申请事项" type="VARCHAR(1200)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-10" author="wjw" failOnError="false">

        <createTable remarks="账号申请人员表" tableName="UOMP_ACCOUNT_APPLY_PERSON">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_ID" remarks="申请表ID" type="VARCHAR(64)"/>
            <column name="PERSON_ID" remarks="人员ID" type="VARCHAR(64)"/>
            <column name="PERSON_NAME" remarks="人员名称" type="VARCHAR(200)"/>
            <column name="ROLE" remarks="角色" type="VARCHAR(6000)"/>
            <column name="PERMISSION" remarks="权限" type="VARCHAR(64)"/>
            <column name="POSITION" remarks="岗位" type="VARCHAR(6000)"/>
            <column name="ACCOUNT_NUM" remarks="账号" type="VARCHAR(100)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新人组织" type="VARCHAR(64)"/>
            <column name="ENTRY_DATE" remarks="入职时间" type="VARCHAR(20)"/>
            <column name="PERSON_JSON" type="${clob.type}"/>
            <column name="AUTHORIZATION_STATUS" remarks="授权状态 0-未授权 1-已授权" type="VARCHAR(64)"/>
            <column name="ORG_USER_ID" remarks="系统账号id" type="VARCHAR(64)"/>
            <column name="MAINTENANCE_GROUP_ID" remarks="进驻运维组id" type="VARCHAR(600)"/>
            <column name="MAINTENANCE_GROUP_JSON" remarks="进驻运维组json" type="${clob.type}"/>
            <column name="ENGAGEMENT_PROJECT_ID" remarks="参与项目id" type="VARCHAR(600)"/>
            <column name="ENGAGEMENT_PROJECT_JSON" remarks="参与项目json" type="${clob.type}"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-11" author="wjw" failOnError="false">

        <createTable remarks="应用系统信息表" tableName="UOMP_APPLICATION_SYSTEM_MANAGEMENT">
            <column name="ID" remarks="主键id" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLICATION_SYSTEM_NAME" remarks="应用系统名称" type="VARCHAR(300)"/>
            <column name="DEPART_ID" remarks="运维部门id" type="VARCHAR(64)"/>
            <column name="DEPART_NAME" remarks="运维部门名称" type="VARCHAR(200)"/>
            <column name="PRINCIPAL_NAME" remarks="负责人id" type="VARCHAR(100)"/>
            <column name="PRINCIPAL_ID" remarks="负责人名称" type="VARCHAR(64)"/>
            <column name="IS_HEART" remarks="是否核心应用（1：是   0：否）" type="VARCHAR(200)"/>
            <column name="ONLINE_TIME" remarks="上线时间" type="timestamp"/>
            <column name="SYSTEM_STATUS" remarks="状态（0：使用中  1已停用）" type="VARCHAR(2)"/>
            <column name="SUPPLIER_ID" remarks="服务商id" type="VARCHAR(64)"/>
            <column name="SUPPLIER_NAME" remarks="服务商名称" type="VARCHAR(200)"/>
            <column name="SUPPLIER_DEPART_NAME" remarks="服务商负责人" type="VARCHAR(100)"/>
            <column name="SUPPLIER_TEL" remarks="联系电话" type="VARCHAR(12)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建机构" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新机构" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="删除标识（0:正常  1：已删除）1" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-12" author="wjw" failOnError="false">

        <createTable remarks="应用系统关联表" tableName="UOMP_APPLICATION_SYSTEM_RELATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLICATION_SYSTEM_MANAGEMENT_ID" remarks="应用系统id" type="VARCHAR(64)"/>
            <column name="RELATION_ID" remarks="关联id" type="VARCHAR(64)"/>
            <column name="RELATION_TYPE" remarks="关联类型（contract:合同）" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建机构" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新机构" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="删除标识（0:正常  1：已删除）" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-13" author="wjw" failOnError="false">

        <createTable remarks="合同附件表" tableName="UOMP_CONTRACT_FILE">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CONTRACT_MANAGEMENT_ID" remarks="合同id" type="VARCHAR(64)"/>
            <column name="FILE_NAME" remarks="附件名称" type="VARCHAR(300)"/>
            <column name="FILE_ID" remarks="文件服务id" type="VARCHAR(300)"/>
            <column name="UPLOAD_TIME" remarks="上传时间" type="timestamp"/>
            <column name="UPLOADER_ID" remarks="上传人id" type="VARCHAR(64)"/>
            <column name="UPLOADER_NAME" remarks="上传人名称" type="VARCHAR(30)"/>
            <column name="CREATE_BY" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" type="timestamp"/>
            <column name="CREATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" type="timestamp"/>
            <column name="UPDATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-14" author="wjw" failOnError="false">

        <createTable remarks="合同管理基本信息表" tableName="UOMP_CONTRACT_MANAGEMENT">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CONTRACT_CODE" remarks="合同编号" type="VARCHAR(100)"/>
            <column name="CONTRACT_NAME" remarks="合同名称" type="VARCHAR(300)"/>
            <column name="SIGNING_DATE" remarks="签订日期" type="timestamp"/>
            <column name="CONTRACT_TYPE" remarks="合同类型" type="VARCHAR(10)"/>
            <column name="CONTRACT_STATUS" remarks="合同状态" type="VARCHAR(10)"/>
            <column name="PARTY_A_NAME" remarks="甲方名称" type="VARCHAR(300)"/>
            <column name="PARTY_A_PRINCIPAL_NAME" remarks="甲方负责人名称" type="VARCHAR(200)"/>
            <column name="PARTY_B_NAME" remarks="乙方名称" type="VARCHAR(300)"/>
            <column name="PARTY_B_PRINCIPAL_NAME" remarks="乙方负责人名称" type="VARCHAR(200)"/>
            <column name="PARTY_C_NAME" remarks="丙方名称" type="VARCHAR(300)"/>
            <column name="PARTY_C_PRINCIPAL_NAME" remarks="丙方负责人名称" type="VARCHAR(200)"/>
            <column name="CONTRACT_CONTENT" type="VARCHAR(6000)"/>
            <column name="PROJECT_MANAGEMENT_ID" remarks="合同应用系统id" type="VARCHAR(64)"/>
            <column name="PROJECT_MANAGEMENT_NAME" remarks="合同应用系统名称" type="VARCHAR(300)"/>
            <column name="DUTY_DEPART_ID" remarks="责任部门id" type="VARCHAR(64)"/>
            <column name="DUTY_DEPART_NAME" remarks="责任部门名称" type="VARCHAR(300)"/>
            <column name="PAY_TIME" remarks="付款日期" type="timestamp"/>
            <column name="PAY_STATUS" remarks="付款情况" type="VARCHAR(10)"/>
            <column name="SERVICE_LEVEL_ID" remarks="服务等级协议id" type="VARCHAR(64)"/>
            <column name="SERVICE_LEVEL_NAME" remarks="服务等级协议名称" type="VARCHAR(300)"/>
            <column name="QUALITY_BEGIN_DAY" remarks="质保/维保开始日" type="timestamp"/>
            <column name="QUALITY_END_DAY" remarks="质保/维保结束日" type="timestamp"/>
            <column name="QUALITY_CONTENT" remarks="质保内容" type="VARCHAR(4000)"/>
            <column name="PARTY_A_PRINCIPAL_TEL" remarks="甲方负责人电话" type="VARCHAR(20)"/>
            <column name="PARTY_A_MANAGER" remarks="甲方项目经理" type="VARCHAR(200)"/>
            <column name="PARTY_A_MANAGER_TEL" remarks="甲方项目经理电话" type="VARCHAR(20)"/>
            <column name="PARTY_B_PRINCIPAL_TEL" remarks="乙方负责人电话" type="VARCHAR(20)"/>
            <column name="PARTY_B_MANAGER" remarks="乙方项目经理" type="VARCHAR(200)"/>
            <column name="PARTY_B_MANAGER_TEL" remarks="乙方项目经理电话" type="VARCHAR(20)"/>
            <column name="SALES_MANAGER" remarks="销售经理" type="VARCHAR(200)"/>
            <column name="PRE_SALES_MANAGER" remarks="售前经理" type="VARCHAR(200)"/>
            <column name="PARTY_C_PERSON" remarks="丙方联系人" type="VARCHAR(200)"/>
            <column name="PARTY_C_PERSON_TEL" remarks="丙方联系人电话" type="VARCHAR(20)"/>
            <column name="REMARK" remarks="备注" type="VARCHAR(4000)"/>
            <column name="ENTRY_ID" remarks="录入人id" type="VARCHAR(64)"/>
            <column name="ENTRY_NAME" remarks="录入人名称" type="VARCHAR(200)"/>
            <column name="CREATE_BY" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" type="timestamp"/>
            <column name="CREATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" type="timestamp"/>
            <column name="UPDATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" type="VARCHAR(1)"/>
            <column name="PARTY_B_ID" remarks="服务商id" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-15" author="wjw" failOnError="false">

        <createTable remarks="人员权限申请基础表" tableName="UOMP_PERMISSION_APPLICATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_CODE" remarks="编号" type="VARCHAR(64)"/>
            <column name="APPLY_TITLE" remarks="标题" type="VARCHAR(200)"/>
            <column name="APPLY_USER_ID" remarks="申请人id" type="VARCHAR(64)"/>
            <column name="APPLY_USER_NAME" remarks="申请人名称" type="VARCHAR(200)"/>
            <column name="APPLY_TIME" remarks="申请时间" type="timestamp"/>
            <column name="APPLY_MATTER" remarks="申请事项" type="VARCHAR(300)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="APPLY_STATUS" remarks="审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-16" author="wjw" failOnError="false">

        <createTable remarks="人员权限申请内部权限表" tableName="UOMP_PERMISSION_IN">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_ID" remarks="申请表id" type="VARCHAR(64)"/>
            <column name="USER_ID" remarks="用户id" type="VARCHAR(64)"/>
            <column name="USER_NAME" remarks="用户名称" type="VARCHAR(300)"/>
            <column name="USER_JSON" remarks="用户json" type="TEXT"/>
            <column name="EXISTING_ROLES" remarks="已有角色" type="VARCHAR(1000)"/>
            <column name="EXISTING_POSITIONS" remarks="已有岗位" type="VARCHAR(1000)"/>
            <column name="MAINTENANCE_GROUP_NAME" remarks="所属业务组名称" type="VARCHAR(1000)"/>
            <column name="APPLY_TYPE" remarks="申请类型 0-申请分配权限 1-申请删除权限 2-申请启用权限" type="VARCHAR(10)"/>
            <column name="ROLE" remarks="角色" type="TEXT"/>
            <column name="POSITION" remarks="岗位" type="TEXT"/>
            <column name="AUTHORIZATION_STATUS" remarks="授权状态 0-未授权 1-已授权" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="删除标识 0-未删除 1-已删除" type="VARCHAR(1)"/>
            <column name="EXISTING_SYSTEM" remarks="已有系统" type="VARCHAR(1000)"/>
            <column name="SYSTEM_NAME" remarks="系统" type="TEXT"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-17" author="wjw" failOnError="false">

        <createTable remarks="人员权限申请外部权限基础表" tableName="UOMP_PERMISSION_OUT_BASE">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="APPLY_ID" remarks="申请表id" type="VARCHAR(64)"/>
            <column name="OUT_APPLY_SYS_TYPE" remarks="外部申请系统类型" type="VARCHAR(10)"/>
            <column name="OUT_APPLY_SYS_ID" remarks="外部申请系统id" type="VARCHAR(64)"/>
            <column name="OUT_APPLY_SYS_NAME" remarks="外部申请系统名称" type="VARCHAR(500)"/>
            <column name="OUT_APPLY_SYS_JSON" remarks="外部申请系统json" type="VARCHAR(2000)"/>
            <column name="OUT_APPLY_TITLE" remarks="外部申请标题" type="VARCHAR(500)"/>
            <column name="OUT_APPLY_USER_ID" remarks="外部申请人id" type="VARCHAR(64)"/>
            <column name="OUT_APPLY_USER_NAME" remarks="外部申请人名称" type="VARCHAR(200)"/>
            <column name="OUT_APPLY_TIME" remarks="外部申请时间" type="timestamp"/>
            <column name="OUT_APPLY_MATTER" remarks="外部申请事项" type="VARCHAR(300)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="删除标识 0-未删除 1-已删除" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-18" author="wjw" failOnError="false">

        <createTable remarks="人员权限申请外部权限详情表" tableName="UOMP_PERMISSION_OUT_DETAILS">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="OUT_USER_ID" remarks="用户表id" type="VARCHAR(64)"/>
            <column name="OUT_APPLY_TYPE" remarks="外部申请类型 0-申请分配权限 1-申请删除权限" type="VARCHAR(10)"/>
            <column name="EMPOWER_RESOURCE_IDS" remarks="授权资源id集合" type="VARCHAR(1000)"/>
            <column name="EMPOWER_RESOURCE_JSON" remarks="授权资源json" type="TEXT"/>
            <column name="PERMISSION" remarks="权限" type="VARCHAR(200)"/>
            <column name="EMPOWER_BEGIN_TIME" remarks="授权开始日期" type="VARCHAR(30)"/>
            <column name="EMPOWER_END_TIME" remarks="授权结束日期" type="VARCHAR(30)"/>
            <column name="AUTHORIZATION_STATUS" remarks="授权状态 0-未授权 1-已授权" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="删除标识 0-未删除 1-已删除" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-19" author="wjw" failOnError="false">

        <createTable remarks="人员权限申请外部用户表" tableName="UOMP_PERMISSION_OUT_USER">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="OUT_BASE_ID" remarks="外部申请表id" type="VARCHAR(64)"/>
            <column name="EMPOWER_USER_IDS" remarks="授权用户id集合" type="VARCHAR(1000)"/>
            <column name="EMPOWER_USER_JSON" remarks="授权用户json" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="删除标识 0-未删除 1-已删除" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-20" author="wjw" failOnError="false">

        <createTable remarks="服务报告信息表" tableName="UOMP_REPORT">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="REPORT_NAME" remarks="报告名称" type="VARCHAR(300)"/>
            <column name="REPORT_CODE" remarks="报告编码" type="VARCHAR(100)"/>
            <column name="REPORT_TYPE" remarks="报告类型" type="VARCHAR(1)"/>
            <column name="REPORT_BEGIN" remarks="报告开始时间" type="timestamp"/>
            <column name="REPORT_END" remarks="报告结束时间" type="timestamp"/>
            <column name="APPLICATION_SYSTEM_MANAGEMENT_ID" remarks="应用系统id" type="VARCHAR(64)"/>
            <column name="APPLICATION_SYSTEM_NAME" remarks="应用 系统名称" type="VARCHAR(300)"/>
            <column name="SUPPLIER_NAME" remarks="服务商" type="VARCHAR(200)"/>
            <column name="UPLOAD_TIME" remarks="上传时间" type="timestamp"/>
            <column name="UPLOADER_ID" remarks="上传人id" type="VARCHAR(64)"/>
            <column name="UPLOADER_NAME" remarks="上传人" type="VARCHAR(200)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建机构" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新机构" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="删除标识（0:正常  1：已删除）" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-21" author="wjw" failOnError="false">

        <createTable remarks="服务报告文件表" tableName="UOMP_REPORT_FILE">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="REPORT_ID" remarks="应用系统id" type="VARCHAR(64)"/>
            <column name="FILE_ID" remarks="文件id" type="VARCHAR(64)"/>
            <column name="FILE_NAME" remarks="文件名称" type="VARCHAR(300)"/>
            <column name="FILE_SIZE" remarks="文件大小" type="VARCHAR(100)"/>
            <column name="UPLOAD_TIME" remarks="上传时间" type="VARCHAR(64)"/>
            <column name="UPLOADER_ID" remarks="上传人id" type="VARCHAR(64)"/>
            <column name="UPLOADER_NAME" remarks="上传人" type="VARCHAR(200)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建机构" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新机构" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="删除标识（0:正常  1：已删除）" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-22" author="wjw" failOnError="false">

        <createTable remarks="出国证件信息表" tableName="UOMP_PERSON_ABROAD">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="CERTIFICATE_NAME" remarks="证件名称" type="VARCHAR(300)"/>
            <column name="CERTIFICATE_NUM" remarks="证件号码" type="VARCHAR(50)"/>
            <column name="ISSUE_AT" remarks="签发地" type="VARCHAR(300)"/>
            <column name="START_TIME" remarks="证件有效起始时间" type="VARCHAR(50)"/>
            <column name="END_TIME" remarks="证件有效终止时间" type="VARCHAR(50)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-23" author="wjw" failOnError="false">

        <createTable remarks="人员信息标签配置表" tableName="UOMP_PERSON_CONFIG">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CONFIG_INFO" remarks="配置信息" type="VARCHAR(3000)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="删除标识" type="VARCHAR(1)"/>
            <column name="CONFIG_TYPE" remarks="配置类型， 1-人员页签配置 ， 2-供应商页签配置" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-24" author="wjw" failOnError="false">

        <createTable remarks="人员值班表" tableName="UOMP_PERSON_DUTY">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="DATE" remarks="值班日期" type="VARCHAR(32)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column defaultValue="0" name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-25" author="wjw" failOnError="false">

        <createTable remarks="人员值班详情表" tableName="UOMP_PERSON_DUTY_DETAIL">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="DUTY_ID" remarks="值班id" type="VARCHAR(64)"/>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="DATE" remarks="值班日期" type="VARCHAR(32)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column defaultValue="0" name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-26" author="wjw" failOnError="false">

        <createTable remarks="教育背景信息表" tableName="UOMP_PERSON_EDUCATIONAL">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="EDUCATION_BEGIN_TIME" remarks="教育开始时间" type="VARCHAR(15)"/>
            <column name="EDUCATION_END_TIME" remarks="教育结束时间" type="VARCHAR(15)"/>
            <column name="SCHOOL" remarks="就读院校" type="VARCHAR(120)"/>
            <column name="MAJOR" remarks="就读专业" type="VARCHAR(120)"/>
            <column name="EDUCATION_BACKGROUND" remarks="学历" type="VARCHAR(120)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="EDUCATION_FORM" remarks="教育形式(是否统招)" type="VARCHAR(10)"/>
            <column name="CERTIFICATE_NUM" remarks="证书编号" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-27" author="wjw" failOnError="false">

        <createTable remarks="出入境情况表" tableName="UOMP_PERSON_ENTRY_EXIT">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="ENTRY_EXIT" remarks="出境/入境" type="VARCHAR(10)"/>
            <column name="ENTRY_EXIT_TIME" remarks="出入境时间" type="VARCHAR(50)"/>
            <column name="CERTIFICATE_NAME" remarks="证件名称" type="VARCHAR(300)"/>
            <column name="CERTIFICATE_NUM" remarks="证件号码" type="VARCHAR(50)"/>
            <column name="ENTRY_EXIT_PORTS" remarks="出入境口岸" type="VARCHAR(300)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-28" author="wjw" failOnError="false">

        <createTable remarks="人员管理基础信息表" tableName="UOMP_PERSON_INFO">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_NAME" remarks="姓名" type="VARCHAR(200)"/>
            <column name="PERSON_CARD" remarks="身份证号" type="VARCHAR(20)"/>
            <column name="PERSON_SEX" remarks="性别" type="VARCHAR(2)"/>
            <column name="PERSON_BIRTHDAY" remarks="出生年月" type="VARCHAR(15)"/>
            <column name="TEL" remarks="联系电话" type="VARCHAR(15)"/>
            <column name="ADDRESS" remarks="居住地址" type="VARCHAR(300)"/>
            <column name="EDUCATION" remarks="学历" type="VARCHAR(10)"/>
            <column name="POST" remarks="工作职位" type="VARCHAR(100)"/>
            <column name="MAJOR" remarks="专业" type="VARCHAR(120)"/>
            <column name="WORKING_COMPANY" remarks="就职公司" type="VARCHAR(300)"/>
            <column name="REMARK" remarks="备注" type="VARCHAR(3000)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column defaultValue="0" name="BLACKLIST" remarks="黑名单标识 0-非黑名单 1-黑名单" type="VARCHAR(10)"/>
            <column name="BLACKLIST_REASON" remarks="加入黑名单原因" type="VARCHAR(300)"/>
            <column name="ENTRY_DATE" remarks="入职日期" type="VARCHAR(20)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column defaultValue="0" name="TRIAL_STATUS" remarks="初审状态 0-暂存 1-审核中 2-审核通过 3-审核不通过" type="VARCHAR(10)"/>
            <column defaultValue="0" name="BACKGROUND_STATUS" remarks="背景调查状态 0-待审核 1-合格 2-不合格" type="VARCHAR(10)"/>
            <column name="ID_TYPE" remarks="证件类型(暂时冗余)" type="VARCHAR(10)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column defaultValue="0" name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column defaultValue="0" name="IS_ACCOUNT" remarks="是否分配账号 0-未分配 1-已分配" type="VARCHAR(10)"/>
            <column name="ACCOUNT" remarks="账号" type="VARCHAR(50)"/>
            <column name="WORKING_COMPANY_ID" remarks="就职公司id" type="VARCHAR(64)"/>
            <column name="TECHNICAL_DIRECTION" remarks="技术方向" type="VARCHAR(10)"/>
            <column name="REG_PERMANENT_RESIDENCE" remarks="户口所在地" type="VARCHAR(1000)"/>
            <column name="NATIONALITY" remarks="国籍" type="VARCHAR(300)"/>
            <column name="POLITICS_STATUS" remarks="政治面貌" type="VARCHAR(300)"/>
            <column name="NATION" remarks="民族" type="VARCHAR(200)"/>
            <column name="REG_PROVINCE" remarks="户口所在地-省" type="VARCHAR(20)"/>
            <column name="REG_CITY" remarks="户口所在地-市" type="VARCHAR(20)"/>
            <column name="REG_REGION" remarks="户口所在地-区/县" type="VARCHAR(20)"/>
            <column name="AUDIT_DATE" remarks="审核日期" type="timestamp"/>
            <column name="ORG_GROUP_ID" remarks="运维组织Id" type="VARCHAR(100)"/>
            <column name="ORG_GROUP_NAME" remarks="运维组织名称" type="VARCHAR(100)"/>
            <column defaultValue="0" name="UPDATING" remarks="是否更新中（1是 0 否）" type="VARCHAR(10)"/>
            <column name="ORG_USER_ID" remarks="系统用户Id" type="VARCHAR(64)"/>
            <column defaultValue="-1" name="ENTRY_STATUS" remarks="驻场服务状态" type="VARCHAR(10)"/>
            <column defaultValue="0" name="DIMISSION" remarks="是否离职" type="VARCHAR(10)"/>
            <column name="EMAIL" remarks="邮箱" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-29" author="wjw" failOnError="false">

        <createTable remarks="工作背景信息表" tableName="UOMP_PERSON_JOB">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="JOB_BEGIN_TIME" remarks="工作开始时间" type="VARCHAR(15)"/>
            <column name="JOB_END_TIME" remarks="工作结束时间" type="VARCHAR(15)"/>
            <column name="COMPANY_NAME" remarks="公司名称" type="VARCHAR(120)"/>
            <column name="JOB_POSITION" remarks="工作职位" type="VARCHAR(120)"/>
            <column name="JOB_DESCRIBE" remarks="工作描述" type="VARCHAR(900)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-30" author="wjw" failOnError="false">

        <createTable remarks="无犯罪记录信息表" tableName="UOMP_PERSON_NO_CRIME">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="PROOF_NUMBER" remarks="证明编号" type="VARCHAR(64)"/>
            <column name="QUERY_BEGIN_TIME" remarks="查询周期开始时间" type="VARCHAR(15)"/>
            <column name="QUERY_END_TIME" remarks="查询周期结束时间" type="VARCHAR(15)"/>
            <column name="PROVIDE_UNIT" remarks="出具单位" type="VARCHAR(120)"/>
            <column name="INDATE" remarks="有效期" type="VARCHAR(30)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-31" author="wjw" failOnError="false">

        <createTable remarks="社会关系信息表" tableName="UOMP_PERSON_SOCIAL">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="RELA_NAME" remarks="姓名" type="VARCHAR(200)"/>
            <column name="RELA_AGE" remarks="年龄" type="VARCHAR(5)"/>
            <column name="NATIONAL" remarks="国籍" type="VARCHAR(100)"/>
            <column name="POLITICS_STATUS" remarks="政治面貌" type="VARCHAR(30)"/>
            <column name="RELATION_WITH_MYSELF" remarks="与本人关系" type="VARCHAR(30)"/>
            <column name="RELA_TEL" remarks="联系方式" type="VARCHAR(15)"/>
            <column name="RELA_ADDRESS" remarks="居住地" type="VARCHAR(300)"/>
            <column name="RELA_POST" remarks="工作单位及职务" type="VARCHAR(300)"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-32" author="wjw" failOnError="false">


        <createTable remarks="技术资质信息表" tableName="UOMP_PERSON_TECHNOLOGY">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="GET_TIME" remarks="获取时间" type="VARCHAR(15)"/>
            <column name="QUALIFTY_NAME" remarks="资质名称" type="VARCHAR(60)"/>
            <column name="QUALIFTY_TYPE" remarks="资质类型" type="VARCHAR(10)"/>
            <column name="CERTIFICATION_BODY" remarks="颁证机构" type="VARCHAR(120)"/>
            <column name="FILE_INFO" remarks="附件信息" type="TEXT"/>
            <column name="CREATE_BY" remarks="创建人" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="修改时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="START_TIME" remarks="证书起始时间" type="VARCHAR(50)"/>
            <column name="END_TIME" remarks="证书终止时间" type="VARCHAR(50)"/>
        </createTable>
    </changeSet>


    <changeSet id="staff-pool-33" author="wjw" failOnError="false">

        <createTable remarks="供应商附件信息表" tableName="UOMP_SUPPLIER_FILE">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="SUPPLIER_MANAGEMENT_ID" remarks="供应商管理主键" type="VARCHAR(64)"/>
            <column name="FILE_NAME" remarks="附件名称" type="VARCHAR(300)"/>
            <column name="FILE_ID" remarks="文档服务id" type="VARCHAR(300)"/>
            <column name="UPLOADER_ID" remarks="上传人id" type="VARCHAR(64)"/>
            <column name="UPLOADER_NAME" remarks="上传人姓名" type="VARCHAR(200)"/>
            <column name="UPLOAD_TIME" remarks="上传时间" type="timestamp"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" type="timestamp"/>
            <column name="UPDATE_ORG_ID" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="FILE_TYPE" remarks="附件类型" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-34" author="wjw" failOnError="false">

        <createTable remarks="供应商管理基础信息表" tableName="UOMP_SUPPLIER_MANAGEMENT">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="SUPPLIER_NAME" remarks="供应商名称" type="VARCHAR(300)"/>
            <column name="CREDIT_CODE" remarks="统一社会信用代码" type="VARCHAR(20)"/>
            <column name="SHORT_NAME" remarks="简称" type="VARCHAR(200)"/>
            <column name="TEL" remarks="联系电话" type="VARCHAR(20)"/>
            <column name="RESP_NAME" remarks="负责人" type="VARCHAR(200)"/>
            <column name="RESP_TEL" remarks="负责人电话" type="VARCHAR(20)"/>
            <column name="SUPPLIER_TYPE" remarks="类型" type="VARCHAR(40)"/>
            <column name="SUPPLIER_STATUS" remarks="状态" type="VARCHAR(10)"/>
            <column name="REGISTER_PROVINCE" type="VARCHAR(10)"/>
            <column name="REGISTER_CITY" type="VARCHAR(10)"/>
            <column name="REGISTER_REGIN" type="VARCHAR(10)"/>
            <column name="REGISTER_ADDRESS" remarks="注册地址" type="VARCHAR(2000)"/>
            <column name="CONTACT_ADDRESS" remarks="联系地址" type="VARCHAR(2000)"/>
            <column name="REMARK" remarks="备注" type="VARCHAR(4000)"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人机构ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新人机构ID" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(1)"/>
            <column name="ENTRY_TIME" remarks="录入时间" type="timestamp"/>
            <column name="ENTRY_ID" remarks="录入人id" type="VARCHAR(64)"/>
            <column name="ENTRY_NAME" remarks="录入人名称" type="VARCHAR(200)"/>
            <column name="GROUP_ID" remarks="机构id" type="VARCHAR(64)"/>
            <column name="USED_NAME" remarks="曾用名" type="VARCHAR(2000)"/>
            <column name="START_COOPERATION_TIME" remarks="开始合作日期" type="timestamp"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-35" author="wjw" failOnError="false">

        <createTable remarks="供应商资质证书表" tableName="UOMP_SUPPLIER_QUALIFICATION">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="SUPPLIER_MANAGEMENT_ID" remarks="供应商管理主键" type="VARCHAR(64)"/>
            <column name="CERTIFICATE_NAME" remarks="证书名称" type="VARCHAR(300)"/>
            <column name="CERTIFICATE_NUM" remarks="证书编号" type="VARCHAR(100)"/>
            <column name="ISSUING_AUTHORITY" remarks="发证机构" type="VARCHAR(300)"/>
            <column name="ISSUING_DATE" remarks="发证日期" type="VARCHAR(100)"/>
            <column name="END_TIME" remarks="有效期至" type="VARCHAR(100)"/>
            <column name="FILES" remarks="证书附件" type="TEXT"/>
            <column name="STANDARDS" remarks="符合标准" type="VARCHAR(300)"/>
            <column name="STATEMENT" remarks="适用性声明" type="VARCHAR(300)"/>
            <column name="BUSINESS_AREA" remarks="业务领域" type="VARCHAR(600)"/>
            <column name="ASSESSMENT_LEVEL" remarks="评估等级" type="VARCHAR(30)"/>
            <column name="BUSINESS_LINE" remarks="业务种类" type="VARCHAR(300)"/>
            <column name="QUALIFICATION_LEVEL" remarks="资质等级" type="VARCHAR(30)"/>
            <column name="TRIAL_AREA" remarks="试用地域" type="VARCHAR(300)"/>
            <column name="CERTIFICATION_SUB_ITEM" remarks="认证分项" type="VARCHAR(600)"/>
            <column name="OTHER_NAME" remarks="其他证书名称" type="VARCHAR(300)"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人机构ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="更新人机构ID" type="VARCHAR(64)"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(2)"/>
        </createTable>

    </changeSet>


    <changeSet id="staff-pool-36" author="renjiahao" failOnError="false">

        <createTable remarks="人员信息子表" tableName="uomp_person_all_info_temp">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="PERSON_INFO" remarks="人员全部信息" type="${clob.type}"/>
            <column name="EDUCATIONAL_INFO" remarks="教育背景信息" type="${clob.type}"/>
            <column name="JOB_INFO" remarks="工作背景信息" type="${clob.type}"/>
            <column name="TECH_INFO" remarks="技术资质信息" type="${clob.type}"/>
            <column name="SOCIAL_INFO" remarks="社会关系信息" type="${clob.type}"/>
            <column name="NO_CRIME_INFO" remarks="无犯罪记录信息" type="${clob.type}"/>
            <column name="ABROAD_INFO" remarks="出国证件信息" type="${clob.type}"/>
            <column name="ENRTY_EXIT_INFO" remarks="出入境记录信息" type="${clob.type}"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(2)"/>
        </createTable>

    </changeSet>


    <changeSet id="staff-pool-37" author="renjiahao" failOnError="false">

        <createTable remarks="脱敏配置表" tableName="uomp_desensitization">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="DES_OBJ_CODE" remarks="脱敏对象code" type="VARCHAR(64)"/>
            <column name="DES_FIELD_CODE" remarks="脱敏字段code" type="VARCHAR(64)"/>
            <column name="DES_RULE_MODE" remarks="脱敏规则方式 0-普通 1-正则表达式" type="VARCHAR(2)"/>
            <column name="DES_RULE_JSON" remarks="脱敏规则json" type="${clob.type}"/>
            <column name="DES_RULE_REGX" remarks="脱敏规则正则表达式" type="VARCHAR(128)"/>
            <column name="SENSITIVE_WORDS" remarks="敏感词" type="VARCHAR(256)"/>
            <column name="SENSITIVE_REPLACE_WORDS" remarks="敏感词替换词" type="VARCHAR(32)"/>
            <column name="PLAINTEXT_ROLE_JSON" remarks="明文展示角色json" type="${clob.type}"/>
            <column name="PLAINTEXT_POST_JSON" remarks="明文展示岗位json" type="${clob.type}"/>
            <column name="PLAINTEXT_USER_JSON" remarks="经办人是否可见 0-是 1-否" type="${clob.type}"/>
            <column name="IS_CREATER" remarks="数据录入人是否可见 0-是 1-否" type="VARCHAR(2)"/>
            <column name="IS_OPERATOR" remarks="脱敏规则方式 0-普通 1-正则表达式" type="VARCHAR(2)"/>
            <column name="OPERATOR_MODE" remarks="经办人过滤方式 0-sql" type="VARCHAR(16)"/>
            <column name="OPERATOR_SCRIPT" remarks="经办人过滤sql" type="${clob.type}"/>
            <column name="IS_ENABLE" remarks="是否启用 0-是 1-否" type="VARCHAR(2)"/>
            <column name="IS_OVERALL_ENABLE" remarks="全局是否启用 0-是 1-否" type="VARCHAR(2)"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(2)"/>
        </createTable>

    </changeSet>


    <changeSet id="staff-pool-38" author="renjiahao" failOnError="false">

        <createTable remarks="操作记录表" tableName="uomp_history_record">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="BIZ_ID" remarks="业务主键" type="VARCHAR(64)"/>
            <column name="BIZ_TYPE" remarks="业务类型 0-黑名单" type="VARCHAR(64)"/>
            <column name="OPERATOR_ID" remarks="操作人ID" type="VARCHAR(64)"/>
            <column name="OPERATOR_NAME" remarks="操作人" type="VARCHAR(256)"/>
            <column name="OPERATOR_MESSAGE" remarks="操作信息" type="VARCHAR(1024)"/>
            <column name="OPERATOR_REASON" remarks="操作原因说明" type="VARCHAR(1024)"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="DEL_FLAG" remarks="逻辑删除标记 0-有效 1-无效" type="VARCHAR(2)"/>
            <column name="OPERATOR_TIME" remarks="更新时间" type="timestamp"/>
        </createTable>

    </changeSet>


    <changeSet id="staff-pool-39" author="renjiahao" failOnError="false">

        <createTable remarks="业务组" tableName="uomp_team">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="NAME" remarks="业务组名称" type="VARCHAR(512)"/>
            <column name="ORGID" remarks="归属单位" type="VARCHAR(64)"/>
            <column name="TEAM_TYPE" remarks="组类型" type="VARCHAR(32)"/>
            <column name="DESCRIBE" remarks="描述" type="VARCHAR(2048)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR(2)"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="修改人员ID" type="VARCHAR(32)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column defaultValue="0" name="DEL_FLAG" remarks="删除标志(0:无效，1:有效)" type="VARCHAR(1)"/>
            <column name="ORG_NAME" remarks="归属单位名称" type="VARCHAR(256)"/>
            <column name="TEAM_LEADER" remarks="组领导" type="VARCHAR(256)"/>
            <column name="TEAM_LEADER_ID" remarks="组领导ID" type="VARCHAR(32)"/>
            <column name="TEAM_RELATIONS" remarks="授权组" type="VARCHAR(4000)"/>
            <column name="GROUP_LEADERS" remarks="组长" type="VARCHAR(1024)"/>
            <column name="GROUP_LEADER_IDS" remarks="组长ids" type="VARCHAR(2048)"/>
        </createTable>

    </changeSet>
    <changeSet id="staff-pool-40" author="jinghaiyang" failOnError="false">

        <createTable remarks="培训记录参会人员信息表" tableName="uomp_training_record_person">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TRAINING_RECORD_ID" remarks="培训记录id" type="VARCHAR(64)"/>
            <column name="TRAINING_PERSON_ID" remarks="参会人员id" type="VARCHAR(64)"/>
            <column name="TRAINING_PERSON_NAME" remarks="参会人员名称" type="VARCHAR(300)"/>
        </createTable>

    </changeSet>

    <changeSet id="staff-pool-41" author="jinghaiyang" failOnError="false">
        <createTable remarks="" tableName="cmdb_comm_resource">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="INST_ID" remarks="" type="VARCHAR(64)"/>
            <column name="BASELINE_ID" remarks="" type="VARCHAR(64)"/>
            <column name="GROUP_ID" remarks="" type="VARCHAR(64)"/>
            <column name="MODEL_ID" remarks="" type="VARCHAR(64)"/>
            <column name="RESOURCE_NO" remarks="" type="VARCHAR(64)"/>
            <column name="RESOURCE_NAME" remarks="" type="VARCHAR(500)"/>
            <column name="DEVICE_MODEL" remarks="" type="VARCHAR(500)"/>
            <column name="RESOURCE_STATE" remarks="" type="VARCHAR(50)"/>
            <column name="PRODUCER" remarks="" type="VARCHAR(500)"/>
            <column name="DEVICE_DUTYOR" remarks="" type="VARCHAR(200)"/>
            <column name="TEND_DATE_START" remarks="" type="date"/>
            <column name="TEND_DATE_END" remarks="" type="date"/>
            <column name="DEVICE_UNIT_NAME" remarks="" type="VARCHAR(500)"/>
            <column name="DEVICE_UNIT_ID" remarks="" type="VARCHAR(500)"/>
            <column name="SUPPLIER_ID" remarks="" type="VARCHAR(64)"/>
            <column name="SUPPLIER_NAME" remarks="" type="VARCHAR(500)"/>
            <column name="CONTRACT_ID" remarks="" type="VARCHAR(64)"/>
            <column name="CONTRACT_NAME" remarks="" type="VARCHAR(500)"/>
            <column name="PROJECT_ID" remarks="" type="VARCHAR(64)"/>
            <column name="PROJECT_NAME" remarks="" type="VARCHAR(500)"/>
            <column name="DEVICE_STATE" remarks="" type="VARCHAR(50)"/>
            <column name="IP_ADDR" remarks="" type="VARCHAR(500)"/>
            <column name="SUBMIT_TIME" remarks="" type="timestamp"/>
            <column name="AUDIT_STATE" remarks="" type="VARCHAR(50)"/>
            <column name="AUDIT_RESULT" remarks="" type="VARCHAR(50)"/>
            <column name="AUDIT_END_TIME" remarks="" type="timestamp"/>
            <column name="AUDITER" remarks="" type="VARCHAR(500)"/>
            <column name="AUDITERID" remarks="" type="VARCHAR(200)"/>
            <column name="IF_NOTICE" remarks="" type="VARCHAR(2)"/>
            <column name="NOTICE_TIME" remarks="" type="timestamp"/>
            <column name="FILE_STR" remarks="" type="VARCHAR(2000)"/>
            <column name="AUDIT_OPIN" remarks="" type="VARCHAR(2000)"/>
            <column name="DEVICE_PRICE" remarks="" type="decimal(18, 4)"/>
            <column name="OTHER_COLS" remarks="" type="${clob.type}"/>
            <column name="METADATA1" remarks="" type="VARCHAR(2000)"/>
            <column name="METADATA2" remarks="" type="VARCHAR(2000)"/>
            <column name="METADATA3" remarks="" type="VARCHAR(2000)"/>
            <column name="METADATA4" remarks="" type="VARCHAR(2000)"/>
            <column name="METADATA5" remarks="" type="VARCHAR(2000)"/>
            <column name="EXTENDS1" remarks="" type="${clob.type}"/>
            <column name="EXTENDS2" remarks="" type="${clob.type}"/>
            <column name="EXTENDS3" remarks="" type="${clob.type}"/>
            <column name="EXTENDS4" remarks="" type="${clob.type}"/>
            <column name="EXTENDS5" remarks="" type="${clob.type}"/>
            <column name="EXTENDS6" remarks="" type="${clob.type}"/>
            <column name="EXTENDS7" remarks="" type="${clob.type}"/>
            <column name="EXTENDS8" remarks="" type="${clob.type}"/>
            <column name="EXTENDS9" remarks="" type="${clob.type}"/>
            <column name="EXTENDS10" remarks="" type="${clob.type}"/>
            <column name="EXTENDS11" remarks="" type="${clob.type}"/>
            <column name="EXTENDS12" remarks="" type="${clob.type}"/>
            <column name="EXTENDS13" remarks="" type="${clob.type}"/>
            <column name="EXTENDS14" remarks="" type="${clob.type}"/>
            <column name="EXTENDS15" remarks="" type="${clob.type}"/>
            <column name="EXTENDS16" remarks="" type="${clob.type}"/>
            <column name="EXTENDS17" remarks="" type="${clob.type}"/>
            <column name="EXTENDS18" remarks="" type="${clob.type}"/>
            <column name="EXTENDS19" remarks="" type="${clob.type}"/>
            <column name="EXTENDS20" remarks="" type="${clob.type}"/>
            <column name="EXTENDS_DATE1" remarks="" type="date"/>
            <column name="EXTENDS_DATE2" remarks="" type="date"/>
            <column name="EXTENDS_DATE3" remarks="" type="date"/>
            <column name="EXTENDS_DATE4" remarks="" type="date"/>
            <column name="EXTENDS_DATE5" remarks="" type="date"/>
            <column name="EXTENDS_INT1" remarks="" type="decimal(10, 0)"/>
            <column name="EXTENDS_INT2" remarks="" type="decimal(10, 0)"/>
            <column name="EXTENDS_INT3" remarks="" type="decimal(10, 0)"/>
            <column name="EXTENDS_INT4" remarks="" type="decimal(10, 0)"/>
            <column name="EXTENDS_INT5" remarks="" type="decimal(10, 0)"/>
            <column name="CREATE_BY" remarks="" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="" type="timestamp"/>
            <column name="UPDATE_TIME" remarks="" type="timestamp"/>
            <column name="DEL_FLAG" remarks="" type="VARCHAR(1)"/>
            <column name="MAC_ADDR" remarks="" type="VARCHAR(500)"/>
            <column name="ORDER_NO" remarks="" type="VARCHAR(200)"/>
        </createTable>
    </changeSet>


 <changeSet id="staff-pool-20250319-1" author="xieshi" failOnError="false">
        <createTable remarks="" tableName="cmdb_table_base_config">
            <column name="BASELINE_ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="MODEL_ID" remarks="" type="VARCHAR(64)"/>
            <column name="${key.colum}" remarks="" type="VARCHAR(64)"/>
            <column name="NAME" remarks="" type="VARCHAR(200)"/>
            <column name="${comment.colum}" remarks="" type="VARCHAR(2000)"/>
            <column name="DS_KEY" remarks="" type="VARCHAR(64)"/>
            <column name="DS_NAME" remarks="" type="VARCHAR(200)"/>
            <column name="GROUP_ID" remarks="" type="VARCHAR(64)"/>
            <column name="GROUP_NAME" remarks="" type="VARCHAR(200)"/>
            <column name="EXTERNAL" remarks="" type="int"/>
            <column name="ORG_ID" remarks="" type="VARCHAR(200)"/>
            <column name="MODEL_TYPE" remarks="" type="VARCHAR(1)"/>
            <column name="TEAM_NAME" remarks="" type="VARCHAR(4000)"/>
            <column name="MODEL_STATUS" remarks="" type="VARCHAR(1)"/>
            <column name="TEAM_ID" remarks="" type="VARCHAR(4000)"/>
            <column name="BASELINE" remarks="" type="VARCHAR(64)"/>
            <column name="BASELINE_STATUS" remarks="" type="VARCHAR(2)"/>
            <column name="BASELINE_AUDIT" remarks="" type="VARCHAR(64)"/>
            <column name="RELEASE_TIME" remarks="" type="timestamp"/>
            <column name="RELEASE_PERSION" remarks="" type="VARCHAR(200)"/>
            <column name="RELEASE_PERSION_ID" remarks="" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="" type="timestamp"/>
            <column name="CREATE_BY" remarks="" type="VARCHAR(200)"/>
            <column name="UPDATE_TIME" remarks="" type="timestamp"/>
            <column name="UPDATE_BY" remarks="" type="VARCHAR(200)"/>
            <column name="DEL_FLAG" remarks="" type="VARCHAR(1)"/>
            <column name="SN_" remarks="" type="int"/>
            <column name="OPERATOR" remarks="" type="VARCHAR(200)"/>
            <column name="BASELINE_ID_PRE" remarks="" type="VARCHAR(64)"/>
            <column name="MODEL_ICON" remarks="" type="VARCHAR(500)"/>
            <column name="ORDER_NO" remarks="" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>




    <changeSet id="staff-pool-43" author="jinghaiyang" failOnError="false">
        <createTable remarks="合同到期提醒表" tableName="uomp_contract_notice">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CONTRACT_MANAGEMENT_ID" remarks="合同id" type="VARCHAR(64)"/>
            <column defaultValue="0" name="NOTICE_NUM" remarks="提醒次数"  type="int"/>
            <column name="QUALITY_BEGIN_DAY" remarks="质保/维保开始日" type="timestamp"/>
            <column name="QUALITY_END_DAY" remarks="质保/维保开始日" type="timestamp"/>
            <column name="CREATE_TIME" remarks="" type="timestamp"/>
            <column name="UPDATE_TIME" remarks="" type="timestamp"/>
            <column defaultValue="0"  name="DEL_FLAG" remarks="删除标识（0 正常 1删除）" type="VARCHAR(2)"/>
        </createTable>
    </changeSet>



    <changeSet id="staff-pool-44" author="jinghaiyang" failOnError="false">
        <createTable remarks="合同关联资源表" tableName="uomp_contract_resource">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="CONTRACT_MANAGEMENT_ID" remarks="合同id" type="VARCHAR(64)"/>
            <column name="C_INST_ID" remarks="资源ID"  type="VARCHAR(64)"/>
            <column name="CI_NAME" remarks="资源名称" type="VARCHAR(256)"/>
            <column name="BRAND" remarks="品牌" type="VARCHAR(256)"/>
            <column name="BRAND_MODEL" remarks="型号" type="VARCHAR(256)"/>
            <column name="CPU_FRAMEWORK" remarks="cpu架构" type="VARCHAR(256)"/>
            <column name="USEDS" remarks="用途" type="VARCHAR(256)"/>
            <column name="OS" remarks="操作系统" type="VARCHAR(256)"/>
            <column name="OS_VERSION" remarks="操作系统版本" type="VARCHAR(256)"/>
            <column name="MACHINE_ROOM" remarks="所属机房" type="VARCHAR(1024)"/>
            <column name="CABINET" remarks="所属机柜" type="VARCHAR(1024)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-45" author="jinghaiyang" failOnError="false">

        <createTable remarks="培训记录表" tableName="uomp_training_record">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TRAINING_NAME" remarks="培训名称" type="VARCHAR(256)"/>
            <column name="PROJECT_NAME" remarks="项目名称" type="VARCHAR(512)"/>
            <column name="TRAINING_MODE" remarks="培训方式" type="VARCHAR(256)"/>
            <column name="CONFERENCE_NUM" remarks="会议号" type="VARCHAR(16)"/>
            <column name="TRAINING_BEGIN_TIME" remarks="培训开始日期" type="timestamp"/>
            <column name="TRAINING_END_TIME" remarks="培训结束日期" type="timestamp"/>
            <column name="TRAINING_TEACHER" remarks="培训讲师" type="VARCHAR(256)"/>
            <column name="TRAINING_PLAN_ID" remarks="培训计划id" type="VARCHAR(64)"/>
            <column name="TRAINING_CONTENT" remarks="培训内容" type="VARCHAR(3000)"/>
            <column name="TRAINING_SITE" remarks="培训地点" type="VARCHAR(256)"/>
            <column name="SIGN_IN_NUM" remarks="签到人数" type="VARCHAR(8)"/>
            <column name="FILE_INFO" remarks="附件信息json" type="${clob.type}"/>
            <column name="CREATE_BY" remarks="创建人ID" type="VARCHAR(64)"/>
            <column name="CREATE_TIME" remarks="创建时间" type="timestamp"/>
            <column name="CREATE_ORG_ID" remarks="创建人组织" type="VARCHAR(64)"/>
            <column name="UPDATE_BY" remarks="更新人ID" type="VARCHAR(64)"/>
            <column name="UPDATE_TIME" remarks="更新时间" type="timestamp"/>
            <column name="UPDATE_ORG_ID" remarks="修改人组织" type="VARCHAR(64)"/>
            <column defaultValue="0"  name="DEL_FLAG" remarks="删除标识（0 正常 1删除）" type="VARCHAR(2)"/>
            <column name="TRAINING_PLAN_NAME" remarks="培训计划名称" type="VARCHAR(300)"/>
            <column name="TRAINING_DURATION" remarks="培训时长" type="VARCHAR(16)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-46" author="jinghaiyang" failOnError="false">
        <createTable remarks="问卷信息" tableName="vote_info">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TITLE" remarks="标题" type="VARCHAR(4000)"/>
            <column name="STATE" remarks="状态" type="VARCHAR(4000)"/>
            <column name="RELEASE_TIME" remarks="发布日期" type="timestamp"/>
            <column name="START_TIME" remarks="开始时间" type="timestamp"/>
            <column name="END_TIME" remarks="结束时间" type="timestamp"/>
            <column name="SURVEY_SCOPE" remarks="调查范围" type="${clob.type}"/>
            <column name="ANONYMITY" remarks="匿名" type="VARCHAR(64)"/>
            <column name="RELEASE_PEOPLE_ID" remarks="发布人id" type="VARCHAR(64)"/>
            <column name="RELEASE_PEOPLE_NAME" remarks="发布人" type="VARCHAR(256)"/>
            <column name="STATUS" remarks="状态" type="VARCHAR(64)"/>
            <column name="TYPE" remarks="类型" type="VARCHAR(256)"/>
            <column name="COLLECTED" remarks="收集" type="VARCHAR(64)"/>
            <column name="EXPLAIN_" remarks="解释" type="VARCHAR(4000)"/>
            <column name="PROJECT_NAME" remarks="项目名称" type="VARCHAR(256)"/>
            <column name="TRAINING_CONTENT" remarks="培训内容" type="VARCHAR(3000)"/>
            <column name="TRAINING_SITE" remarks="培训地点" type="VARCHAR(256)"/>
            <column name="TRAINING_DATE" remarks="培训时间" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-47" author="jinghaiyang" failOnError="false">
        <createTable remarks="问卷用户答案" tableName="vote_user_answer">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="VOTE_ID" remarks="问卷id" type="VARCHAR(64)"/>
            <column name="USER_ID" remarks="用户id" type="VARCHAR(64)"/>
            <column name="ANSWER" remarks="答案" type="${clob.type}"/>
            <column name="ANSWER_STATUS" remarks="答案状态" type="VARCHAR(64)"/>
            <column name="ATTACH_STATUS" remarks="附件状态" type="VARCHAR(64)"/>
            <column name="TITLE" remarks="标题" type="VARCHAR(4000)"/>
            <column name="CREATE_TIME" remarks="发布日期" type="timestamp"/>
            <column name="UPDATE_TIME" remarks="开始时间" type="timestamp"/>
            <column name="SJ_ORG_ID" remarks="机构id" type="VARCHAR(64)"/>
            <column name="REVIEW_RESULT" remarks="结果" type="VARCHAR(512)"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-48" author="renjiahao" failOnError="false">
        <createTable remarks="人员总信息历史表" tableName="uomp_person_all_info_history">
            <column name="ID" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="PERSON_ID" remarks="人员id" type="VARCHAR(64)"/>
            <column name="INST_ID" remarks="流程实例id" type="VARCHAR(64)"/>
            <column name="PERSON_INFO" remarks="人员全部信息" type="${clob.type}"/>
        </createTable>
    </changeSet>
    <changeSet id="staff-pool-49" author="duqiang" failOnError="false">
        <modifyDataType tableName="uomp_person_info" columnName="PERSON_CARD" newDataType="varchar(256)"></modifyDataType>
    </changeSet>

    <changeSet id="staff-pool-50" author="duqiang" failOnError="false">
        <modifyDataType tableName="UOMP_ADMISSION_PERSON" columnName="PERSON_CARD" newDataType="varchar(256)"></modifyDataType>
    </changeSet>

    <changeSet id="staff-pool-51" author="duqiang" failOnError="false">
        <modifyDataType tableName="UOMP_EXIT_APPLICATION" columnName="PERSON_CARD" newDataType="varchar(256)"></modifyDataType>
    </changeSet>
    <changeSet id="staff-pool-52" author="duqiang" failOnError="false">
        <addColumn tableName="UOMP_CONTRACT_MANAGEMENT">
            <column name="sales_manager_tel" type="varchar(20)" remarks="销售经理联系电话"/>
            <column name="pre_sales_manager_tel" type="varchar(20)" remarks="售前经理联系电话"/>
        </addColumn>
    </changeSet>
    <changeSet id="staff-pool-53" author="duqiang" failOnError="false">
        <createTable tableName="uomp_party_info" remarks="乙方联合体信息">
            <column name="id" type="VARCHAR(64)" remarks="主键">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contract_management_id" type="VARCHAR(64)" remarks="合同基本信息表ID">
            </column>
            <column name="party_info" type="VARCHAR(256)" remarks="乙方联合体信息">
            </column>
            <column name="party_name" type="VARCHAR(256)" remarks="乙方联合体负责人">
            </column>
            <column name="party_tel" type="VARCHAR(20)" remarks="乙方联合电话">
            </column>
            <column name="party_manager_name" type="VARCHAR(256)" remarks="项目经理">
            </column>
            <column name="party_manager_tel" type="VARCHAR(20)" remarks="项目经理电话">
            </column>
            <column name="create_by" type="VARCHAR(64)" remarks="创建人id">
            </column>
            <column name="create_time" type="TIMESTAMP" remarks="创建时间">
            </column>
            <column name="update_by" type="VARCHAR(64)" remarks="更新人id">
            </column>
            <column name="update_time" type="TIMESTAMP" remarks="更新时间">
            </column>
        </createTable>
    </changeSet>
    <changeSet id="staff-pool-54" author="duqiang" failOnError="false">
        <addColumn tableName="UOMP_CONTRACT_MANAGEMENT">
            <column name="contract_amount" type="float(10,2)" remarks="合同金额"/>

        </addColumn>
    </changeSet>

    <changeSet id="staff-pool-team-risk_config" author="rjh" failOnError="false">
        <createTable remarks="团队风险模型配置" tableName="uomp_team_risk_config">
            <column name="id" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name_" remarks="风险指标名称" type="VARCHAR(255)"/>
            <column name="type_" remarks="风险类型" type="VARCHAR(8)"/>
            <column name="factor_" remarks="风险因素" type="VARCHAR(255)"/>
            <column name="level_" remarks="风险等级" type="VARCHAR(8)"/>
            <column name="rule_" remarks="风险判断规则" type="VARCHAR(1024)"/>
            <column name="alias_" remarks="风险别名(仅后端使用)" type="VARCHAR(64)"/>
            <column name="status_" remarks="状态" type="CHAR(2)" defaultValue="1"/>
            <column name="del_flag" remarks="逻辑删除标记 0-有效 1-无效" type="CHAR(1)" defaultValue="0"/>
            <column name="create_by" type="VARCHAR(64)" remarks="创建人id"/>
            <column name="create_time" type="TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="VARCHAR(64)" remarks="更新人id"/>
            <column name="update_time" type="TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-team-risk" author="rjh" failOnError="false">
        <createTable remarks="团队风险总表" tableName="uomp_team_risk">
            <column name="id" remarks="主键" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" remarks="用户id" type="VARCHAR(64)"/>
            <column name="config_id" remarks="风险模型id" type="VARCHAR(64)"/>
            <column name="risk_level" remarks="风险等级" type="VARCHAR(8)"/>
            <column name="config_name" remarks="风险指标名称" type="VARCHAR(255)"/>
            <column name="change_level" remarks="调整后的风险等级" type="VARCHAR(8)"/>
            <column name="reason_" remarks="判定原因" type="VARCHAR(1024)"/>
            <column name="attach_" remarks="附件" type="${clob.type}"/>
            <column name="update_by" type="VARCHAR(64)" remarks="更新人id"/>
            <column name="update_user" type="VARCHAR(255)" remarks="更新人名称"/>
            <column name="update_time" type="TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>

    <changeSet id="staff-pool-team-risk_his" author="rjh" failOnError="false">
        <createTable remarks="团队风险总表历史" tableName="uomp_team_risk_his">
            <column name="id" remarks="主键" type="VARCHAR(64)">
            </column>
            <column name="user_id" remarks="用户id" type="VARCHAR(64)"/>
            <column name="config_id" remarks="风险模型id" type="VARCHAR(64)"/>
            <column name="risk_level" remarks="风险等级" type="VARCHAR(8)"/>
            <column name="config_name" remarks="风险指标名称" type="VARCHAR(255)"/>
            <column name="change_level" remarks="调整后的风险等级" type="VARCHAR(8)"/>
            <column name="reason_" remarks="判定原因" type="VARCHAR(1024)"/>
            <column name="attach_" remarks="附件" type="${clob.type}"/>
            <column name="create_by" type="VARCHAR(64)" remarks="创建人id"/>
            <column name="create_time" type="TIMESTAMP" remarks="创建时间"/>
            <column name="update_by" type="VARCHAR(64)" remarks="更新人id"/>
            <column name="update_user" type="VARCHAR(255)" remarks="更新人名称"/>
            <column name="update_time" type="TIMESTAMP" remarks="更新时间"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
