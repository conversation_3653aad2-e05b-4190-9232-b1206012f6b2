package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="人员培训记录响应类")
@Data
public class SupplierManagementDTO implements Serializable {

    @ApiModelProperty(value="id")
    private String id;
    @ApiModelProperty(value="服务商名称")
    private String supplierName;
    @ApiModelProperty(value="类型")
    private String supplierType;
    @ApiModelProperty(value="子类")
    List<TechDTO> techList;
    @ApiModelProperty(value="组织列表")
    List<UompOrgGroupPersonDto> children;
    @ApiModelProperty(value="总数")
    Integer sum = 0;
}
