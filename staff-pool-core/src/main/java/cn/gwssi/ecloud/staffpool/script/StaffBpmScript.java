package cn.gwssi.ecloud.staffpool.script;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoHistoryMapper;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloud.staffpool.util.DictUtil;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.sys.api.groovy.IScript;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component("staffBpmScript")
public class StaffBpmScript implements IScript {

    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private DictUtil dictUtil;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private UompPersonEducationalService uompPersonEducationalService;
    @Resource
    private UompPersonJobService uompPersonJobService;
    @Resource
    private UompPersonTechnologyService uompPersonTechnologyService;
    @Resource
    private UompPersonSocialService uompPersonSocialService;
    @Resource
    private UompPersonNoCrimeService uompPersonNoCrimeService;
    @Resource
    private UompPersonAbroadService uompPersonAbroadService;
    @Resource
    private UompPersonEntryExitService uompPersonEntryExitService;
    @Resource
    private UompTrainingRecordService trainingRecordService;
    @Resource
    private VoteInfoService voteInfoService;
    @Resource
    private UompPersonAllInfoHistoryMapper uompPersonAllInfoHistoryMapper;

    public void savePersonInfoHistory(String personId,String instId){

        UompPersonInfoVo personInfoDetailDto = null;

        UompPersonInfo personInfo = uompPersonInfoService.get(personId);
        personInfoDetailDto = new UompPersonInfoVo();
        BeanUtils.copyProperties(personInfo, personInfoDetailDto);
        String regPermanentResidenceStr = dictUtil.getKeyByName("G_USER_NATIVE_CODE", personInfo.getRegPermanentResidence());
        if (!StringUtils.isEmpty(regPermanentResidenceStr)) {
            personInfoDetailDto.setRegPermanentResidenceName(regPermanentResidenceStr.replace(",", ""));
        }
        List<UompDesensitization> desensitizations = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", personInfo.getInstId(), personInfo.getCreateBy());
        if (desensitizations != null && desensitizations.size() > 0) {
            for (UompDesensitization desensitization : desensitizations) {
                //如果有关于手机号的脱敏规则则转换手机号
                if (!StringUtils.isEmpty(personInfo.getTel()) && "tel".equals(desensitization.getDesFieldCode())) {
                    personInfoDetailDto.setTel(conversionUtil.conversion(desensitization, personInfo.getTel()));
                }
                //如果有关于身份证号的脱敏规则则转换身份证号
                if (!StringUtils.isEmpty(personInfo.getPersonCard()) && "personCard".equals(desensitization.getDesFieldCode())) {
                    personInfoDetailDto.setPersonCard(conversionUtil.conversion(desensitization, personInfo.getPersonCard()));
                }
            }
        }
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonEducational> educationals = uompPersonEducationalService.query(queryFilter);
        personInfoDetailDto.setEducationList(educationals);

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setJobList(uompPersonJobService.query(queryFilter));

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setTechList(uompPersonTechnologyService.query(queryFilter));

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setSocialList(uompPersonSocialService.query(queryFilter));

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setNoCrimeList(uompPersonNoCrimeService.query(queryFilter));

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setAbroadList(uompPersonAbroadService.query(queryFilter));

        queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        personInfoDetailDto.setEntryExitList(uompPersonEntryExitService.query(queryFilter));

        //培训记录
        //查询该人员信息对应的系统id
        //职业测评
        BaseDTO baseDTO = uompPersonInfoService.selectUserId(personId);
        List<UompTrainingRecord> records = new ArrayList<>();
        List<VoteUserAnswerDto> answerDtos = new ArrayList<>();
        if (baseDTO != null && !StringUtils.isEmpty(baseDTO.getId())) {
            records = trainingRecordService.selectTrainingRecordByUserId(baseDTO.getId());

            voteInfoService.selectVoteInfoByUserId(baseDTO.getId());
        }
        personInfoDetailDto.setTrainList(records);
        personInfoDetailDto.setAssessmentList(answerDtos);

        // 黑名单
        personInfoDetailDto.setBlackList(uompPersonInfoService.selectBlack(personId));

        String personInfoJSON = JSONObject.toJSONString(personInfoDetailDto);

        UompPersonAllInfoHistory uompPersonAllInfoHistory = new UompPersonAllInfoHistory(personId, instId, personInfoJSON);

        UompPersonAllInfoHistory history = uompPersonAllInfoHistoryMapper.getByInstId(instId);
        if (history == null){
            uompPersonAllInfoHistoryMapper.create(uompPersonAllInfoHistory);
        }else {
            history.setPersonInfo(personInfoJSON);
            uompPersonAllInfoHistoryMapper.update(history);
        }
    }
}
