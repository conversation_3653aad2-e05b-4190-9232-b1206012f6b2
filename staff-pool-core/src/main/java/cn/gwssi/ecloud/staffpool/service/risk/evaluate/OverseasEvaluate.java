package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 海外经历
 */
@Component
public class OverseasEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(OverseasEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {

    }

    @Override
    public String alias() {
        return "overseas";
    }
}
