package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_social")
@Data
public class UompPersonSocialDto extends UompPersonSocial {
    @ApiModelProperty(value = "")
    private String tel;

    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;
}