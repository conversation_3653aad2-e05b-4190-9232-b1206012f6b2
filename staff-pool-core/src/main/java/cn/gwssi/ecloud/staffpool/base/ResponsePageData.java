package cn.gwssi.ecloud.staffpool.base;

import lombok.Data;
import java.util.List;

@Data
public class ResponsePageData<T> {
    protected List<T> rows;
    private String msg;
    private Integer code;
    private Integer page;
    private Integer pageSize;
    private Long total;
    private Boolean isOk = true;

    public ResponsePageData() {
    }
    public ResponsePageData(Integer code, String message) {
        setCode(code);
        setMsg(message);
    }

    public static ResponsePageData error(String message) {
        return new ResponsePageData<>(ResponseCodeEnums.BAD_REQUEST.getValue(), message);
    }
}