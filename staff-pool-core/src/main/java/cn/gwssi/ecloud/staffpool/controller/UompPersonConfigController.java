package cn.gwssi.ecloud.staffpool.controller;


import cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonConfigService;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 问卷管理
 */
@Api(description = "人员信息标签配置")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/personConfig")
public class UompPersonConfigController extends BaseController<UompPersonConfig> {
    @Resource
    private UompPersonConfigService uompPersonConfigService;

    @Override
    protected String getModelDesc() {
        return "人员信息标签配置";
    }

    @ApiOperation(value = "查看标签配置信息")
    @RequestMapping(value = "/getConfigInfo")
    public ResultMsg<UompPersonConfig> getConfigInfo(HttpServletRequest request, HttpServletResponse response,
                                                     @RequestParam(value = "configType", required = false) @ApiParam(value = "配置类型， 1-人员页签配置 ， 2-供应商页签配置") String configType) {
        return this.getSuccessResult(uompPersonConfigService.getConfigInfo(configType));
    }
}
