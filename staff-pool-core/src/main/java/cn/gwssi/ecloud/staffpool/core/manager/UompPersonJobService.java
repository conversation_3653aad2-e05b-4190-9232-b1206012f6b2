package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob;
import cn.gwssi.ecloud.staffpool.dto.UompPersonJobDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonJobService extends Manager<String, UompPersonJob> {

    int insertSelective(UompPersonJob record);

    int updateByPrimaryKeySelective(UompPersonJob record);

    int updateByPersonId(UompPersonJob record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonJobDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
