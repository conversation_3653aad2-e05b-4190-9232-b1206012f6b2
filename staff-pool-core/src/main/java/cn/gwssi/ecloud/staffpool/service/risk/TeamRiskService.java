package cn.gwssi.ecloud.staffpool.service.risk;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonInfoMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskVO;
import cn.gwssi.ecloud.staffpool.core.entity.risk.UserRiskVO;
import cn.gwssi.ecloud.staffpool.core.manager.TeamRiskConfigManager;
import cn.gwssi.ecloud.staffpool.core.manager.TeamRiskManager;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TeamRiskService {

    @Resource
    private TeamRiskManager teamRiskManager;
    @Resource
    private TeamRiskConfigManager teamRiskConfigManager;
    @Resource
    private UompPersonInfoMapper uompPersonInfoMapper;


    public List<TeamRiskVO> getList(QueryFilter queryFilter){
        List<TeamRiskVO> teamRisKList = teamRiskManager.queryRiskList(queryFilter);
        if (CollectionUtil.isNotEmpty(teamRisKList)){
            List<TeamRiskConfig> allConfig = teamRiskConfigManager.getAll();
            Map<String, TeamRiskConfig> configMap = allConfig.stream().collect(Collectors.toMap(TeamRiskConfig::getId, Function.identity()));
            for (TeamRiskVO teamRiskVO : teamRisKList) {
                String configIds = teamRiskVO.getConfigIds();
                String[] arr = configIds.split(",");
                List<TeamRiskConfig> configList = new ArrayList<>();
                for (String item : arr) {
                    String configId = item.split("-")[0];
                    String changeLevel = item.split("-")[1];
                    TeamRiskConfig teamRiskConfig = configMap.get(configId);
                    teamRiskConfig.setLevel(changeLevel);
                    configList.add(teamRiskConfig);
                }
                configList = configList.stream().sorted(Comparator.comparing(TeamRiskConfig::getLevel)).collect(Collectors.toList());
                teamRiskVO.setCurLevel(configList.get(0).getLevel());
                teamRiskVO.setRiskConfigList(configList);
            }
        }
        return teamRisKList;
    }

    public String analysis(){
        String res = "当前运维团队识别高风险%s个、中风险%s个、低风险%s个。";
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        List<TeamRisk> riskList = teamRiskManager.query(queryFilter);
        Map<String, List<TeamRisk>> levelMap = riskList.stream().collect(Collectors.groupingBy(TeamRisk::getChangeLevel));
        //高风险
        int highSize = 0;
        List<TeamRisk> highTeamRisks = levelMap.get("1");
        if (CollectionUtil.isNotEmpty(highTeamRisks)){
            highSize = highTeamRisks.size();
        }
        //中风险
        int medSize = 0;
        List<TeamRisk> medTeamRisks = levelMap.get("2");
        if (CollectionUtil.isNotEmpty(medTeamRisks)){
            medSize = medTeamRisks.size();
        }
        //低风险
        int lowSize = 0;
        List<TeamRisk> lowTeamRisks = levelMap.get("3");
        if (CollectionUtil.isNotEmpty(lowTeamRisks)){
            lowSize = lowTeamRisks.size();
        }
        return String.format(res,highSize,medSize,lowSize);
    }

    public JSONObject trend(String level){
        JSONObject resObj = new JSONObject();
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        List<TeamRisk> riskList = teamRiskManager.query(queryFilter);
        List<String> XList = new ArrayList<>();
        List<Integer> YList = new ArrayList<>();
        String num = "0";
        if (CollectionUtil.isNotEmpty(riskList)){
            List<TeamRisk> levelList = riskList.stream().filter(r -> StringUtils.equals(r.getChangeLevel(), level)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(levelList)){
                List<TeamRiskConfig> allConfig = teamRiskConfigManager.getAll();
                Map<String, TeamRiskConfig> configMap = allConfig.stream().collect(Collectors.toMap(TeamRiskConfig::getId, Function.identity()));
                LinkedHashMap<String, List<TeamRisk>> resMap = levelList.stream().collect(Collectors.groupingBy(TeamRisk::getConfigId))
                        .entrySet().stream()
                        .sorted((e1, e2) -> Integer.compare(e2.getValue().size(), e1.getValue().size()))
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (oldVal, newVal) -> oldVal,
                                LinkedHashMap::new
                        ));


                for (Map.Entry<String, List<TeamRisk>> stringListEntry : resMap.entrySet()) {
                    String key = stringListEntry.getKey();
                    List<TeamRisk> value = stringListEntry.getValue();

                    XList.add(configMap.get(key).getName());
                    YList.add(value.size());
                }
                BigDecimal decimal = new BigDecimal(levelList.size()).multiply(new BigDecimal(100)).divide(new BigDecimal(riskList.size()), 0, RoundingMode.HALF_UP);
                num = decimal.toString();
            }
        }
        resObj.put("num",num);
        resObj.put("X",XList);
        resObj.put("Y",YList);
        return resObj;
    }

    public void change(String id,String changeLevel,String reason ,String attach){
        TeamRisk teamRisk = teamRiskManager.get(id);
        Date date = new Date();
        teamRisk.setChangeLevel(changeLevel);
        teamRisk.setReason(reason);
        teamRisk.setAttach(attach);
        teamRisk.setUpdateTime(date);
        teamRiskManager.update(teamRisk);
        teamRisk.setCreateTime(date);
        teamRiskManager.createRiskHis(teamRisk);
    }

    public UserRiskVO allInfo(String userId){
        UompPersonInfo personInfo = uompPersonInfoMapper.get(userId);
        UserRiskVO userRiskVO = new UserRiskVO();
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("utr.user_id",userId,QueryOP.EQUAL);
        List<TeamRisk> riskList = teamRiskManager.query(queryFilter);
        List<TeamRisk> riskHisList = teamRiskManager.queryRiskHisList(queryFilter);
        userRiskVO.setRiskList(riskList);
        userRiskVO.setRiskHisList(riskHisList);
        return userRiskVO;

    }
}
