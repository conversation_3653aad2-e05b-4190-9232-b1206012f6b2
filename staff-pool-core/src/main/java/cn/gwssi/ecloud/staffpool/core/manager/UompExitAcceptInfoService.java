package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitAcceptInfo;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;

import java.util.Set;

public interface UompExitAcceptInfoService extends Manager<String, UompExitAcceptInfo> {

    int insertSelective(UompExitAcceptInfo record);

    Set<DefaultIdentity> selectInfoById(String id);

    /**
     * 根据退场id查询接收人是否存在账号
     *
     * @param exitApplyId 退场申请id
     * @return
     */
    Boolean judgeExitAccept(String exitApplyId);
}
