package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonSocialMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonSocialService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonSocialDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonSocialServiceImpl extends BaseManager<String, UompPersonSocial> implements UompPersonSocialService {

    @Resource
    private UompPersonSocialMapper uompPersonSocialMapper;

    @Override
    public int insertSelective(UompPersonSocial record) {
        return uompPersonSocialMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonSocial record) {
        return uompPersonSocialMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonSocial record) {
        return uompPersonSocialMapper.updateByPersonId(record);
    }

    @Override
    public int updateByPersonIds(String orgId, String[] personIds) {
        return uompPersonSocialMapper.updateByPersonIds(orgId, personIds);
    }

    @Override
    public List<UompPersonSocialDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonSocialMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonSocialMapper.deleteByPersonId(personId);
    }
}
