package cn.gwssi.ecloud.staffpool.service;

import cn.gwssi.ecloud.staffpool.api.model.MaskedData;
import cn.gwssi.ecloud.staffpool.api.service.IDataDesensitization;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;


@Service
public class DataDesensitization implements IDataDesensitization {
    private final String ALGORITHM = "AES";

    private final String AES_KEY = "1234567890abcdef";

    private final String HIDDEN_SYMBOLS = "************";

    @Override
    public String encrypt(String data) {
        if (StringUtils.isEmpty(data)){
            return null;
        }
        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8),ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE,keySpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException("AES 加密失败",e);
        }
    }

    @Override
    public String decrypt(String data) {
        if (StringUtils.isEmpty(data)){
            return null;
        }

        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8),ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE,keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(data));
            return new String(decrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES 解析失败",e);
        }

    }

    @Override
    public MaskedData process(String original, int prefixLen, int suffixLen) {
        if (original == null || original.length() < prefixLen + suffixLen){
            throw  new BusinessException("数据长度不足以分段");
        }
        MaskedData maskedData = new MaskedData();
        maskedData.setPrefix(original.substring(0,prefixLen));
        maskedData.setSuffix(original.substring(original.length() - suffixLen));
        maskedData.setHash(encrypt(original));
        maskedData.setMaskedValue(maskedData.getPrefix()+HIDDEN_SYMBOLS+maskedData.getSuffix());
        return maskedData;
    }

    @Override
    public String desensitization(String original, int prefixLen, int suffixLen) {
        if (original == null || original.length() < prefixLen + suffixLen){
            throw  new BusinessException("数据长度不足以分段");
        }
        String decryptOriginal = decrypt(original);
        String prefix = decryptOriginal.substring(0,prefixLen);
        String suffix = decryptOriginal.substring(decryptOriginal.length() - suffixLen);
        int middleLen = decryptOriginal.length() - prefixLen - suffixLen;
        return prefix+getHideCharacters(middleLen)+suffix;
    }

    private String getHideCharacters(int hideLen){
        if (hideLen <=0){
            throw new BusinessException("隐藏字符不能等于小于零");
        }
        StringBuffer stringBuffer = new StringBuffer(hideLen);
        for (int i = 0; i<hideLen; i++){
            stringBuffer.append("*");
        }
        return stringBuffer.toString();
    }
}
