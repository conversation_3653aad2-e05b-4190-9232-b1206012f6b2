package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class OrgUser extends BaseModel {

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String fullname;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String account;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String password;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String email;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String mobile;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String weixin;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String address;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String photo;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String sex;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String from;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer status;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer sn;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String telephone;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer activeStatus;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer secretLevel;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String type;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String desc;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String loginName;
}