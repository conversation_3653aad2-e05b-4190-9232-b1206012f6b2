package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply;
import cn.gwssi.ecloud.staffpool.core.manager.UompAccountApplyService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.AccountPersonAddDTO;
import cn.gwssi.ecloud.staffpool.dto.AccountPersonDTO;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgPostTypeDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgRoleTypeDTO;
import cn.gwssi.ecloud.staffpool.dto.PostDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonInfoAndAccountDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonInfoDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(description = "账号权限")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/account")
public class UompAccountApplyController extends BaseController<UompAccountApply> {

    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompAccountApplyService uompAccountApplyService;


    @ApiOperation(value = "根据人员id查询人员详情")
    @RequestMapping(value = "/getInfoByPersonId", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<UompPersonInfoDTO> getInfoByPersonId(@RequestParam("personId") @ApiParam(value = "人员id") String personId) {
        return getSuccessResult(uompPersonInfoService.getInfoByPersonId(personId));
    }

    @ApiOperation(value = "根据账号申请人员id和账号更新人员及账号申请信息")
    @RequestMapping(value = "/updateAccountById", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> updateAccountById(@RequestParam("id") @ApiParam(value = "行id") String id,
                                               @RequestParam("accountNum") @ApiParam(value = "账号") String accountNum) {
        return getSuccessResult(uompPersonInfoService.updateAccountById(id, accountNum));
    }

    @ApiOperation(value = "授权接口")
    @RequestMapping(value = "/empower", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg empower(@RequestParam("ids") @ApiParam(value = "行id集合") String ids) {
        uompPersonInfoService.empower(ids);
        return getSuccessResult();
    }

    @ApiOperation(value = "根据id激活并启用账号")
    @RequestMapping(value = "/activeAccount", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg activeAccount(@RequestParam("ids") @ApiParam(value = "行id集合,逗号隔开") String ids) {
        uompPersonInfoService.activeAccount(ids);
        return getSuccessResult();
    }


    @ApiOperation(value = "根据当前登录人返回账号申请的标题")
    @RequestMapping(value = "/getTitleByCurrentUser", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> getTitleByCurrentUser() {
        return getSuccessResult(uompPersonInfoService.getTitleByCurrentUser());
    }

    @ApiOperation(value = "查询有效业务组/ 入场申请: 进驻运维组")
    @RequestMapping(value = "/getBusiness", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<BaseDTO> getBusiness(HttpServletRequest request,
                                           @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                           @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        return uompPersonInfoService.getBusiness(queryFilter);
    }

    @ApiOperation(value = "查询所有岗位接口（带机构类型权限收敛）")
    @RequestMapping(value = "/getPostByOrgType", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<OrgPostTypeDTO> getPostByOrgType(HttpServletRequest request,
                                                       @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                       @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                       @RequestParam(value = "name", required = false) @ApiParam(value = "名称") String name) {
        QueryFilter queryFilter = getQueryFilter(request);
        if (StringUtils.isNotEmpty(name)) {
            queryFilter.addFilter("NAME_", name.trim(), QueryOP.LIKE);
        }
        return uompPersonInfoService.getPostByOrgType(queryFilter);
    }

    @ApiOperation(value = "查询所有角色接口（带机构类型权限收敛）")
    @RequestMapping(value = "/getRoleByOrgType", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<OrgRoleTypeDTO> getRoleByOrgType(HttpServletRequest request,
                                                       @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                       @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                       @RequestParam(value = "name", required = false) @ApiParam(value = "名称") String name) {
        QueryFilter queryFilter = getQueryFilter(request);
        if (StringUtils.isNotEmpty(name)) {
            queryFilter.addFilter("NAME_", name.trim(), QueryOP.LIKE);
        }
        return uompPersonInfoService.getRoleByOrgType(queryFilter);
    }

    @ApiOperation(value = "账号申请管理列表")
    @RequestMapping(value = "/list", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<AccountPersonDTO> list(HttpServletRequest request,
                                             @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                             @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                             @RequestParam(value = "name", required = false) @ApiParam(value = "姓名") String name,
                                             @RequestParam(value = "groupId", required = false) @ApiParam(value = "运维组织id") String groupId,
                                             @RequestParam(value = "status", required = false) @ApiParam(value = "账号状态 (0禁用1正常)") String status) {
        QueryFilter queryFilter = getQueryFilter(request);
        if (StringUtils.isNotEmpty(name)) {
            queryFilter.addFilter("upi.PERSON_NAME", name.trim(), QueryOP.LIKE);
        }
        if (StringUtils.isNotEmpty(groupId)) {
            queryFilter.addFilter("upi.ORG_GROUP_ID", groupId.trim(), QueryOP.EQUAL);
        }
        if (StringUtils.isNotEmpty(status)) {
            queryFilter.addFilter("ou.status_", status.trim(), QueryOP.EQUAL);
        }

        return uompPersonInfoService.list(queryFilter);
    }

    @ApiOperation(value = "管理员-添加人员账号")
    @RequestMapping(value = "/add", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> addAccount(@Validated @RequestBody AccountPersonAddDTO accountPersonAddDTO) {
        return getSuccessResult(uompPersonInfoService.addAccount(accountPersonAddDTO));
    }

    @ApiOperation(value = "账号启用、禁用")
    @RequestMapping(value = "/updateAccountStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> updateAccountStatus(@RequestParam(value = "ids") @ApiParam(value = "用户id(多个逗号隔开)") String ids,
                                                 @RequestParam(value = "status") @ApiParam(value = "账号状态(0停用 1启用)") String status) {
        uompPersonInfoService.updateAccountStatus(ids, status);
        return getSuccessResult();
    }

    /**
     * 240705查询逻辑：
     * 审核状态通过、没有分配账号、非黑名单、背调非不合格的人员
     **/
    @ApiOperation(value = "账号权限管理-尚未分配账号的人员信息")
    @RequestMapping(value = "/noAccountPeronList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPersonInfoAndAccountDTO> noAccountPeronList(HttpServletRequest request,
                                                                      @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                      @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 审核通过的
        queryFilter.addFilter("TRIAL_STATUS", "2", QueryOP.EQUAL);
        // 没有分配账号
        queryFilter.addFilter("IS_ACCOUNT", "0", QueryOP.EQUAL);
        // 非黑名单人员
        queryFilter.addFilter("BLACKLIST", "0", QueryOP.EQUAL);
        // 背调非不合格的
        queryFilter.addFilter("BACKGROUND_STATUS", "2", QueryOP.NOT_EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompAccountApplyService.getNoAccountPermissionPeronList(queryFilter);
    }

    @ApiOperation(value = "岗位列表（去除用户已有岗位）")
    @RequestMapping(value = "/noPostList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<PostDTO> noPostList(HttpServletRequest request,
                                          @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                          @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                          @RequestParam("orgUserId") @ApiParam(value = "系统用户id") String orgUserId,
                                          @RequestParam(value = "postName", required = false) @ApiParam(value = "岗位名称") String postName) {
        QueryFilter queryFilter = getQueryFilter(request);
        if (StringUtils.isNotEmpty(postName)) {
            queryFilter.addFilter("opost.name_", postName, QueryOP.LIKE);
        }
        return uompPersonInfoService.noPostList(queryFilter, orgUserId);
    }

    @Override
    protected String getModelDesc() {
        return "账号权限";
    }
}
