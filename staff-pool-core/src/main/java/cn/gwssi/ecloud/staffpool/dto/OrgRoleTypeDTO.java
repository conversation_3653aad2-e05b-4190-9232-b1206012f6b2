package cn.gwssi.ecloud.staffpool.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "查询所有角色信息相应类", description="查询所有角色信息相应类")
public class OrgRoleTypeDTO implements Serializable {

    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="别名")
    private String alias;
    @ApiModelProperty(value="名称")
    private String name;
    @ApiModelProperty(value="描述")
    private String desc;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
