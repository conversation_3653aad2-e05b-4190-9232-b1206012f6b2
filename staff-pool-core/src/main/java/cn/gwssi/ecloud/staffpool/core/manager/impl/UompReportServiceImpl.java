package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.UompReportSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompReportFile;
import cn.gwssi.ecloud.staffpool.core.manager.UompReportFileService;
import cn.gwssi.ecloud.staffpool.dto.UompReportDTO;
import cn.gwssi.ecloud.staffpool.dto.UompReportFileBase;
import cn.gwssi.ecloud.staffpool.dto.UompReportListDTO;
import cn.gwssi.ecloud.staffpool.util.DateUtils;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.gwssi.ecloud.staffpool.core.entity.UompReport;
import cn.gwssi.ecloud.staffpool.core.dao.UompReportMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompReportService;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class UompReportServiceImpl extends BaseManager<String, UompReport> implements UompReportService {

    @Resource
    private UompReportMapper uompReportMapper;
    @Autowired
    private UompReportFileService uompReportFileService;

    private SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.yyyyMMdd);

    @Override
    public int insertSelective(UompReport record) {
        return uompReportMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompReport record) {
        return uompReportMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageResult<UompReportListDTO> getReportList(QueryFilter queryFilter) {
        List<UompReport> list = uompReportMapper.query(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            PageResult pageResult = new PageResult(list);
            List<UompReportListDTO> dtoList = new ArrayList<>();
            for (UompReport uompReport : list) {
                String reportType = uompReport.getReportType();
                Date uploadTime = uompReport.getUploadTime();
                UompReportListDTO dto = new UompReportListDTO();
                BeanUtils.copyProperties(uompReport, dto);
                dto.setUploadTime(sdf.format(uploadTime));
                dto.setReportType("1".equals(reportType) ? "周报" : ("2".equals(reportType) ? "月报" : "年报"));
                // 查询文件集合信息
                QueryFilter fileFilter = new DefaultQueryFilter(true);
                fileFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                fileFilter.addFilter("REPORT_ID", uompReport.getId(), QueryOP.EQUAL);
                fileFilter.addFieldSort("CREATE_TIME", "desc");
                List<UompReportFile> fileList = uompReportFileService.query(fileFilter);
                dto.setFileListJson(this.getFileListJson(fileList));
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
            return pageResult;
        }
        return new PageResult(new ArrayList<>());
    }

    /**
     * 封装合同的文件集合json字段
     *
     * @param fileList 合同文件集合
     * @return 合同文件集合json字符串
     **/
    private String getFileListJson(List<UompReportFile> fileList) {
        String fileListJson = "[]";
        if (CollectionUtils.isNotEmpty(fileList)) {
            List<UompReportFileBase> baseList = new ArrayList<>();
            for (UompReportFile uompReportFile : fileList) {
                UompReportFileBase base = new UompReportFileBase();
                base.setId(uompReportFile.getFileId());
                base.setName(uompReportFile.getFileName());
                base.setSize(Long.parseLong(uompReportFile.getFileSize()));
                base.setUID(uompReportFile.getUploaderId());
                base.setUName(uompReportFile.getUploaderName());
                base.setDate(uompReportFile.getUploadTime());
                baseList.add(base);
            }
            fileListJson = JSONObject.toJSONString(baseList);
        }
        return fileListJson;
    }

    @Override
    public UompReportDTO getReportInfoById(String id) {
        UompReport uompReport = uompReportMapper.get(id);
        if (null == uompReport) {
            throw new BusinessMessage("服务报告不存在！");
        }
        UompReportDTO dto = new UompReportDTO();
        BeanUtils.copyProperties(uompReport, dto);
        // 查询文件集合信息
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("REPORT_ID", id, QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "desc");
        List<UompReportFile> fileList = uompReportFileService.query(queryFilter);
        // 封装合同关联文件信息
        dto.setFileListJson(this.getFileListJson(fileList));
        return dto;
    }

    @Override
    public String save(UompReportSave uompReportSave) {
        // 处理应用系统信息
        try {
            uompReportSave.setApplicationSystemName(JSONObject.parseObject(uompReportSave.getApplicationSystem()).getString("name"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 当前登录人信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId(); // 用户id
        String orgId = user.getOrgId(); // 机构id
        String fullname = user.getFullname(); // 姓名全称
        String id = uompReportSave.getId(); // 主键
        String fileListJson = uompReportSave.getFileListJson(); // 文件集合json
        if (StringUtils.isNotEmpty(id)) { //走更新
            UompReport uompReport = uompReportMapper.get(id);
            BeanUtils.copyProperties(uompReportSave, uompReport);
            uompReport.setUpdateBy(userId);
            uompReport.setUpdateTime(new Date());
            uompReport.setUpdateOrgId(orgId);
            uompReport.setUploaderId(userId);
            uompReport.setUploaderName(fullname);
            uompReport.setUploadTime(new Date());
            uompReportMapper.update(uompReport);
            // 删除原有关联
            UompReportFile uompReportFile = new UompReportFile();
            uompReportFile.setDelFlag("1");
            uompReportFile.setUpdateBy(userId);
            uompReportFile.setUpdateTime(new Date());
            uompReportFile.setUpdateOrgId(orgId);
            uompReportFile.setReportId(id);
            uompReportFileService.updateDelFlag(uompReportFile);
            // 添加文件关联
            this.addReportAndFileReltion(fileListJson, id, userId, orgId);
        } else { //走添加
            id = IdUtil.getSuid();
            UompReport uompReport = new UompReport();
            BeanUtils.copyProperties(uompReportSave, uompReport);
            uompReport.setDelFlag("0");
            uompReport.setCreateBy(userId);
            uompReport.setCreateTime(new Date());
            uompReport.setCreateOrgId(orgId);
            uompReport.setUploaderId(userId);
            uompReport.setUploaderName(fullname);
            uompReport.setUploadTime(new Date());
            uompReport.setId(id);
            uompReportMapper.insertSelective(uompReport);
            // 添加文件信息
            this.addReportAndFileReltion(fileListJson, id, userId, orgId);
        }
        return id;
    }

    /**
     * 添加报告与文件的关联关系
     *
     * @param fileListJson 文件集合json字符串
     * @param id           合同主键id
     * @param createBy     创建人
     * @param createOrgId  创建机构
     **/
    private void addReportAndFileReltion(String fileListJson, String id, String createBy, String createOrgId) {
        if (StringUtils.isNotEmpty(fileListJson)) {
            List list = JSONObject.parseObject(fileListJson, List.class);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Object obj : list) {
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(obj);
                    Map<String, Object> fileMap = jsonObject.getInnerMap();
                    UompReportFile file = new UompReportFile();
                    String fid = IdUtil.getSuid();
                    file.setId(fid);
                    file.setReportId(id);
                    file.setFileId((String) fileMap.get("id"));
                    file.setFileName((String) fileMap.get("name"));
                    file.setFileSize(fileMap.get("size").toString());
                    file.setUploaderId((String) fileMap.get("uID"));
                    file.setUploaderName((String) fileMap.get("uName"));
                    file.setUploadTime((String) fileMap.get("date"));
                    file.setCreateOrgId(createOrgId);
                    file.setCreateBy(createBy);
                    file.setCreateTime(new Date());
                    file.setDelFlag("0");
                    uompReportFileService.insertSelective(file);
                }
            }
        }
    }

    @Override
    public void deleteReport(String id) {
        UompReport uompReport = uompReportMapper.get(id);
        if (null != uompReport) {
            // 当前登录人信息
            IUser user = ContextUtil.getCurrentUser();
            String userId = user.getUserId(); // 用户id
            String orgId = user.getOrgId(); // 机构id
            uompReport.setDelFlag("1");
            uompReport.setUploadTime(new Date());
            uompReport.setUpdateBy(userId);
            uompReportMapper.update(uompReport);
            // 删除合同文件关联关系
            UompReportFile uompReportFile = new UompReportFile();
            uompReportFile.setDelFlag("1");
            uompReportFile.setUpdateBy(userId);
            uompReportFile.setUpdateTime(new Date());
            uompReportFile.setUpdateOrgId(orgId);
            uompReportFile.setReportId(id);
            uompReportFileService.updateDelFlag(uompReportFile);
        }
    }
}
