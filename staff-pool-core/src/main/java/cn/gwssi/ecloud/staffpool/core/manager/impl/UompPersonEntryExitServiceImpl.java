package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonEntryExitMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEntryExit;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonEntryExitService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEntryExitDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonEntryExitServiceImpl extends BaseManager<String, UompPersonEntryExit> implements UompPersonEntryExitService {

    @Resource
    private UompPersonEntryExitMapper uompPersonEntryExitMapper;

    @Override
    public int insertSelective(UompPersonEntryExit record) {
        return uompPersonEntryExitMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonEntryExit record) {
        return uompPersonEntryExitMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonEntryExit record) {
        return uompPersonEntryExitMapper.updateByPersonId(record);
    }

    @Override
    public List<UompPersonEntryExitDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonEntryExitMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonEntryExitMapper.deleteByPersonId(personId);
    }
}
