package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompApplicationSystemRelationMapper extends BaseDao<String, UompApplicationSystemRelation> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompApplicationSystemRelation record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UompApplicationSystemRelation record);

    int updateDelFlag(UompApplicationSystemRelation systemRelation);

    int updateDelFlagByIds(QueryFilter deleteFilter);
}