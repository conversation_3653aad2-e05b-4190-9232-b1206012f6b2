package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="根据id查询入场申请详情响应类")
@Data
public class AdmissionPersonDTO implements Serializable {

    @ApiModelProperty(value="驻场总人数")
    Integer total;
    @ApiModelProperty(value="一年内驻场人数")
    Integer admissionTotal;
    @ApiModelProperty(value="一年内退场人数")
    Integer exitTotal;
}
