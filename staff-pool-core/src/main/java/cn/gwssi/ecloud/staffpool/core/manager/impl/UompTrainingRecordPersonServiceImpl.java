package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompTrainingRecordPersonMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingRecordPersonService;
import cn.gwssi.ecloud.staffpool.dto.TrainingDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompTrainingRecordPersonServiceImpl extends BaseManager<String, UompTrainingRecordPerson> implements UompTrainingRecordPersonService {

    @Resource
    private UompTrainingRecordPersonMapper uompTrainingRecordPersonMapper;

    @Override
    public int insertSelective(UompTrainingRecordPerson record) {
        return uompTrainingRecordPersonMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompTrainingRecordPerson record) {
        return uompTrainingRecordPersonMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<TrainingDTO> trainingList() {
        PageHelper.startPage(1, 3);
        return uompTrainingRecordPersonMapper.selectAllByLimit3();
    }

    @Override
    public void deleteByRecordId(String id) {
        uompTrainingRecordPersonMapper.deleteByRecordId(id);
    }
}

