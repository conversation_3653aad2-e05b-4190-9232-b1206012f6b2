package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="人员培训记录响应类")
@Data
public class GroupBySupplierDTO implements Serializable {

    @ApiModelProperty(value="数量")
    private Integer sum;
    @ApiModelProperty(value="分类")
    private List<SupplierManagementDTO> children;
}
