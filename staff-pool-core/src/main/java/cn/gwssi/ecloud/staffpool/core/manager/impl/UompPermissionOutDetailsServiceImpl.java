package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPermissionOutDetailsMapper;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails;
import cn.gwssi.ecloud.staffpool.core.manager.UompPermissionOutDetailsService;
@Service
public class UompPermissionOutDetailsServiceImpl extends BaseManager<String, UompPermissionOutDetails> implements UompPermissionOutDetailsService{

    @Autowired
    private UompPermissionOutDetailsMapper uompPermissionOutDetailsMapper;

    @Override
    public UompPermissionOutDetails selectOutInfoById(String out_detail_id) {
        return uompPermissionOutDetailsMapper.selectOutInfoById(out_detail_id);
    }

}
