package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonDutyMapper extends BaseDao<String, UompPersonDuty> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonDuty record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonDuty record);

    List<UompPersonDutyDto> getDutyList(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("personIds") String[] personIds, @Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize, @Param("createBys") List<String> createBys);
    Integer getDutyListTotal(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("personIds") String[] personIds, @Param("createBys") List<String> createBys);

    List<UompPersonDutyDto> selectByDateAndPersonId(@Param("dates") String[] dates, @Param("personIds") String[] personIds);
}

