package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

import java.util.Date;

public class UompPermissionOutUser extends BaseModel {

    private String outBaseId;

    private String empowerUserIds;

    private String empowerUserJson;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOutBaseId() {
        return outBaseId;
    }

    public void setOutBaseId(String outBaseId) {
        this.outBaseId = outBaseId;
    }

    public String getEmpowerUserIds() {
        return empowerUserIds;
    }

    public void setEmpowerUserIds(String empowerUserIds) {
        this.empowerUserIds = empowerUserIds;
    }

    public String getEmpowerUserJson() {
        return empowerUserJson;
    }

    public void setEmpowerUserJson(String empowerUserJson) {
        this.empowerUserJson = empowerUserJson;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", outBaseId=").append(outBaseId);
        sb.append(", empowerUserIds=").append(empowerUserIds);
        sb.append(", empowerUserJson=").append(empowerUserJson);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompPermissionOutUser other = (UompPermissionOutUser) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getOutBaseId() == null ? other.getOutBaseId() == null : this.getOutBaseId().equals(other.getOutBaseId()))
                && (this.getEmpowerUserIds() == null ? other.getEmpowerUserIds() == null : this.getEmpowerUserIds().equals(other.getEmpowerUserIds()))
                && (this.getEmpowerUserJson() == null ? other.getEmpowerUserJson() == null : this.getEmpowerUserJson().equals(other.getEmpowerUserJson()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOutBaseId() == null) ? 0 : getOutBaseId().hashCode());
        result = prime * result + ((getEmpowerUserIds() == null) ? 0 : getEmpowerUserIds().hashCode());
        result = prime * result + ((getEmpowerUserJson() == null) ? 0 : getEmpowerUserJson().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}