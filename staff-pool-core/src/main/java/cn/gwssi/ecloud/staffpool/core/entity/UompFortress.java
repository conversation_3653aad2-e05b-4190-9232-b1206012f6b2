package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

import java.util.Date;

public class UompFortress extends BaseModel {

    private String nodeId;

    private String fortType;

    private String fortName;

    private String fortKey;

    private String fortVersion;

    private String serviceUrl;

    private String fortState;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getFortType() {
        return fortType;
    }

    public void setFortType(String fortType) {
        this.fortType = fortType;
    }

    public String getFortName() {
        return fortName;
    }

    public void setFortName(String fortName) {
        this.fortName = fortName;
    }

    public String getFortKey() {
        return fortKey;
    }

    public void setFortKey(String fortKey) {
        this.fortKey = fortKey;
    }

    public String getFortVersion() {
        return fortVersion;
    }

    public void setFortVersion(String fortVersion) {
        this.fortVersion = fortVersion;
    }

    public String getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }

    public String getFortState() {
        return fortState;
    }

    public void setFortState(String fortState) {
        this.fortState = fortState;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", nodeId=").append(nodeId);
        sb.append(", fortType=").append(fortType);
        sb.append(", fortName=").append(fortName);
        sb.append(", fortKey=").append(fortKey);
        sb.append(", fortVersion=").append(fortVersion);
        sb.append(", serviceUrl=").append(serviceUrl);
        sb.append(", fortState=").append(fortState);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompFortress other = (UompFortress) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getNodeId() == null ? other.getNodeId() == null : this.getNodeId().equals(other.getNodeId()))
                && (this.getFortType() == null ? other.getFortType() == null : this.getFortType().equals(other.getFortType()))
                && (this.getFortName() == null ? other.getFortName() == null : this.getFortName().equals(other.getFortName()))
                && (this.getFortKey() == null ? other.getFortKey() == null : this.getFortKey().equals(other.getFortKey()))
                && (this.getFortVersion() == null ? other.getFortVersion() == null : this.getFortVersion().equals(other.getFortVersion()))
                && (this.getServiceUrl() == null ? other.getServiceUrl() == null : this.getServiceUrl().equals(other.getServiceUrl()))
                && (this.getFortState() == null ? other.getFortState() == null : this.getFortState().equals(other.getFortState()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNodeId() == null) ? 0 : getNodeId().hashCode());
        result = prime * result + ((getFortType() == null) ? 0 : getFortType().hashCode());
        result = prime * result + ((getFortName() == null) ? 0 : getFortName().hashCode());
        result = prime * result + ((getFortKey() == null) ? 0 : getFortKey().hashCode());
        result = prime * result + ((getFortVersion() == null) ? 0 : getFortVersion().hashCode());
        result = prime * result + ((getServiceUrl() == null) ? 0 : getServiceUrl().hashCode());
        result = prime * result + ((getFortState() == null) ? 0 : getFortState().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}