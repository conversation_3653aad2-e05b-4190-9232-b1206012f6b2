package cn.gwssi.ecloud.staffpool.dto;

import java.io.Serializable;
import java.util.Date;

public class UompSupplierPersonnelDto implements Serializable {
    private String id;

    private String supplier_management_id;

    private String personnelId;

    private String person_name;

    private String personnelPost;

    private String personnelType;

    private String personnelTel;

    private String createBy;

    private Date createTime;

    private String createOrgId;

    private String updateBy;

    private Date updateTime;

    private String updateOrgId;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplier_management_id() {
        return supplier_management_id;
    }

    public void setSupplier_management_id(String supplier_management_id) {
        this.supplier_management_id = supplier_management_id;
    }

    public String getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(String personnelId) {
        this.personnelId = personnelId;
    }

    public String getPerson_name() {
        return person_name;
    }

    public void setPerson_name(String person_name) {
        this.person_name = person_name;
    }

    public String getPersonnelPost() {
        return personnelPost;
    }

    public void setPersonnelPost(String personnelPost) {
        this.personnelPost = personnelPost;
    }

    public String getPersonnelType() {
        return personnelType;
    }

    public void setPersonnelType(String personnelType) {
        this.personnelType = personnelType;
    }

    public String getPersonnelTel() {
        return personnelTel;
    }

    public void setPersonnelTel(String personnelTel) {
        this.personnelTel = personnelTel;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}