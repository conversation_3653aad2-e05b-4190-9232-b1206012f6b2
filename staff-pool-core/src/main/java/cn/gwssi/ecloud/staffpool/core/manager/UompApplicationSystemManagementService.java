package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.UompApplicationSystemManagementSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement;
import cn.gwssi.ecloud.staffpool.dto.ProjectBySupplierDTO;
import cn.gwssi.ecloud.staffpool.dto.UompApplicationSystemManagementDTO;
import cn.gwssi.ecloud.staffpool.dto.UompApplicationSystemManagementListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompContractManagementSystemDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface UompApplicationSystemManagementService extends Manager<String, UompApplicationSystemManagement> {

    int insertSelective(UompApplicationSystemManagement record);

    int updateByPrimaryKey(UompApplicationSystemManagement record);

    PageResult<UompApplicationSystemManagementListDTO> getSystemList(QueryFilter queryFilter);

    UompApplicationSystemManagementDTO getSystemInfoById(String id);

    UompApplicationSystemManagementDTO getSystemInfoByName(String name);

    PageResult<UompContractManagementSystemDTO> unbindingContractList(QueryFilter queryFilter);

    String save(UompApplicationSystemManagementSave uompApplicationSystemManagementSave);

    List<ProjectBySupplierDTO> selectIdAndNameByAll();

    void bindApplicationSystem(JSONObject applicationSystem);

}
