package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ExcelIgnoreUnannotated
public class UompPersonInfoImportDto {

    @ExcelProperty("姓名")
    private String personName;

    @ExcelProperty("身份证号")
    private String personCard;
    @ExcelProperty(value = "性别")
    private String personSex;
    @ExcelProperty(value = "出生年月")
    private String personBirthday;
    @ExcelProperty(value = "联系电话")
    private String tel;
    @ExcelProperty(value = "邮箱")
    private String email;
    @ExcelProperty(value = "居住地址")
    private String address;
    @ExcelProperty(value = "学历")
    private String education;
    @ExcelProperty(value = "工作职位")
    private String post;
    @ExcelProperty(value = "专业")
    private String major;
    @ExcelProperty(value = "就职公司")
    private String workingCompany;
    @ExcelProperty(value = "运维组织")
    private String orgGroupName;
    @ExcelProperty(value = "备注")
    private String remark;
    @ExcelProperty(value = "入职时间")
    private String entryDate;
    @ExcelProperty(value = "技术方向")
    private String technicalDirection;
    @ExcelProperty(value = "国籍")
    @NotEmpty(message = "国籍不能为空")
    private String nationality;
    @ExcelProperty(value = "政治面貌")
    private String politicsStatus;
    @ExcelProperty(value = "民族")
    private String nation;
    @ExcelProperty(value = "审核状态")
    private String trialStatus;
    @ExcelProperty(value="人员状态")
    private String dimission;
    @ExcelProperty(value="背调审查状态")
    private String backgroundStatus;
}
