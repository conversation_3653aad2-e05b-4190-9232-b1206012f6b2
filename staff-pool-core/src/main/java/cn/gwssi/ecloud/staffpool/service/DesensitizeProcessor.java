package cn.gwssi.ecloud.staffpool.service;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import cn.gwssi.ecloud.staffpool.annotation.SensitiveEnum;
import cn.gwssi.ecloud.staffpool.api.service.IDataDesensitization;
import com.github.pagehelper.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class DesensitizeProcessor {
    @Resource
    IDataDesensitization dataDesensitization;

    public Object process(String processMethod,Object obj){
        if (obj == null) return null;

        Class<?> clazz = obj.getClass();
        if (obj instanceof Page){
          List<?> pages = ((Page<?>) obj).getResult();
         for (Object o : pages){
             process(processMethod,o);
         }
        }else if(obj instanceof List){
            for(Object o : (List)obj){
                process(processMethod,o);
            }
        } else if(isCustomObject(obj)){
            List<Field> fieldList = new ArrayList<>();
            while (clazz != null && clazz != Object.class){
                fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
                clazz = clazz.getSuperclass();
            }
            masked(fieldList,processMethod,obj);
        }

        return obj;
    }



    private boolean isCustomObject(Object object){
        if (object == null) return false;
        String pkg = object.getClass().getPackage().getName();
        return !pkg.startsWith("java.") && !object.getClass().isPrimitive();
    }

    private void masked(List<Field> fieldList,String processMethod,Object obj){
        for (Field field : fieldList){
            Sensitive sensitive =  field.getAnnotation(Sensitive.class);
            if (sensitive != null && field.getType() == String.class){
                field.setAccessible(true);
                try {
                    String original = (String) field.get(obj);
                    int prefixLen = sensitive.prefixLen();
                    int suffixLen = sensitive.suffixLen();
                    int actualLen = sensitive.actualLen();
                    String masked = original;
                    if (StringUtils.isNotEmpty(processMethod)){
                        if (processMethod.equalsIgnoreCase(SensitiveEnum.SENSITIVE.getKey()) && actualLen != original.length() && !original.contains("*")){
                            masked = dataDesensitization.desensitization(original,prefixLen,suffixLen);

                        }else if (processMethod.equalsIgnoreCase(SensitiveEnum.ENCRYPT.getKey()) && !original.contains("*")){
                            masked = dataDesensitization.encrypt(original);
                        }else {
                            if (actualLen != original.length() && !original.contains("*")){
                                masked = dataDesensitization.decrypt(original);
                            }
                        }
                    }
                    field.set(obj,masked);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
