package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonDutyMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonDutyService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonDutyServiceImpl extends BaseManager<String, UompPersonDuty> implements UompPersonDutyService {
    @Resource
    private UompPersonDutyMapper dutyMapper;

    @Override
    public List<UompPersonDutyDto> getDutyList(String startDate, String endDate, String[] personIds, Integer pageNo, Integer pageSize, List<String> createBys) {
        pageNo = (pageNo - 1) * pageSize;
        return dutyMapper.getDutyList(startDate, endDate, personIds, pageNo, pageSize, createBys);
    }

    @Override
    public Integer getDutyListTotal(String startDate, String endDate, String[] personIds, List<String> createBys) {
        return dutyMapper.getDutyListTotal(startDate, endDate, personIds, createBys);
    }

    @Override
    public List<UompPersonDutyDto> selectByDateAndPersonId(String[] dates, String[] personIds) {
        return dutyMapper.selectByDateAndPersonId(dates, personIds);
    }

    @Override
    public int insertSelective(UompPersonDuty uompPersonDuty) {
        return dutyMapper.insertSelective(uompPersonDuty);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonDuty uompPersonDuty) {
        return dutyMapper.updateByPrimaryKeySelective(uompPersonDuty);
    }
}

