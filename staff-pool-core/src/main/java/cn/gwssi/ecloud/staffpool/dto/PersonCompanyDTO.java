package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="人员背调服务商总数/合格-响应类")
@Data
public class PersonCompanyDTO implements Serializable {

    @ApiModelProperty(value="公司名称")
    private String name;
    @ApiModelProperty(value="总数")
    private int total;
    @ApiModelProperty(value="合格数")
    private int num;
}
