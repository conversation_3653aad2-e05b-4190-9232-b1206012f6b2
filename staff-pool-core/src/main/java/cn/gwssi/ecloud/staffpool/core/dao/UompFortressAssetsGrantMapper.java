package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssetsGrant;
import cn.gwssi.ecloud.staffpool.dto.OutPermissionBean;
import cn.gwssi.ecloud.staffpool.dto.UompFortessAuthPermDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UompFortressAssetsGrantMapper extends BaseDao<String, UompFortressAssetsGrant> {

    int insertSelective(UompFortressAssetsGrant record);

    List<UompFortessAuthPermDTO> selectInfo(@Param("userId")String userId, @Param("resourceId")String resourceId);

    int updateDeletetStatus(@Param("userId")String userId, @Param("id")String id);

    int updatePermission(@Param("userId")String userId, @Param("permission")String permission, @Param("id")String id);

    int insertInfo(@Param("param")Map param);

    List<OutPermissionBean> selectOutPermissionByUserId(String userId);

    int countByUserId(String userId);
}