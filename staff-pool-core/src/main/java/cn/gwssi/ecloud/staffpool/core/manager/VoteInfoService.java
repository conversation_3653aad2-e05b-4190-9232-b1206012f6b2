package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.VoteInfo;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface VoteInfoService extends Manager<String, VoteInfo> {

    int insertSelective(VoteInfo record);

    int updateByPrimaryKeySelective(VoteInfo record);

    List<VoteUserAnswerDto> selectVoteInfoByUserId(String userId);

    List<VoteUserAnswerDto> selectVoteInfo(QueryFilter queryFilter);
}
