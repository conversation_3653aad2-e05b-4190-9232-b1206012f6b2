package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "uomp_person_social")
@Data
public class UompPersonSocial extends BaseModel {
    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value = "人员id")
    private String personId;

    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "姓名不能为空")
    private String relaName;

    @ApiModelProperty(value = "年龄")
    @NotEmpty(message = "年龄不能为空")
    private String relaAge;

    @ApiModelProperty(value = "国籍")
    @NotEmpty(message = "国籍不能为空")
    private String national;

    @ApiModelProperty(value = "政治面貌")
    @NotEmpty(message = "政治面貌不能为空")
    private String politicsStatus;

    @ApiModelProperty(value = "与本人关系")
    @NotEmpty(message = "与本人关系不能为空")
    private String relationWithMyself;

    @ApiModelProperty(value = "联系方式")
    @NotEmpty(message = "联系方式不能为空")
    private String relaTel;

    @ApiModelProperty(value = "居住地")
    @NotEmpty(message = "居住地不能为空")
    private String relaAddress;

    @ApiModelProperty(value = "工作单位及职务")
    @NotEmpty(message = "工作单位及职务不能为空")
    private String relaPost;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}