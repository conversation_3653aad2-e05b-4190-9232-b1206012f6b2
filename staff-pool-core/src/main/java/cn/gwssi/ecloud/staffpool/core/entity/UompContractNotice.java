package cn.gwssi.ecloud.staffpool.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * 合同到期提醒表
 */
@ApiModel(description="合同到期提醒表")
public class UompContractNotice implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 合同id
    */
    @ApiModelProperty(value="合同id")
    private String contractManagementId;

    /**
    * 提醒次数
    */
    @ApiModelProperty(value="提醒次数")
    private Integer noticeNum;

    /**
    * 质保/维保开始日
    */
    @ApiModelProperty(value="质保/维保开始日")
    private Date qualityBeginDay;

    /**
    * 质保/维保开始日
    */
    @ApiModelProperty(value="质保/维保开始日")
    private Date qualityEndDay;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * 删除标识（0 正常 1删除）
    */
    @ApiModelProperty(value="删除标识（0 正常 1删除）")
    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContractManagementId() {
        return contractManagementId;
    }

    public void setContractManagementId(String contractManagementId) {
        this.contractManagementId = contractManagementId;
    }

    public Integer getNoticeNum() {
        return noticeNum;
    }

    public void setNoticeNum(Integer noticeNum) {
        this.noticeNum = noticeNum;
    }

    public Date getQualityBeginDay() {
        return qualityBeginDay;
    }

    public void setQualityBeginDay(Date qualityBeginDay) {
        this.qualityBeginDay = qualityBeginDay;
    }

    public Date getQualityEndDay() {
        return qualityEndDay;
    }

    public void setQualityEndDay(Date qualityEndDay) {
        this.qualityEndDay = qualityEndDay;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}