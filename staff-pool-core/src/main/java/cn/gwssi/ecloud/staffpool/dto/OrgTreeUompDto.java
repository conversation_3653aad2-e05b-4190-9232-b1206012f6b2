package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.core.model.OrgTreeUomp;
import cn.gwssi.ecloudframework.base.api.model.Tree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(description = "机构树")
@Data
public class OrgTreeUompDto extends OrgTreeUomp {
    private Integer personNumber;

    public OrgTreeUompDto() {
    }

    public OrgTreeUompDto(GroupUomp groupUomp) {
        super(groupUomp);
    }
}
