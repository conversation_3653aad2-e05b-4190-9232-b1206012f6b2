package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人员值班表(UompPersonDuty)表实体类
 */
@SuppressWarnings("serial")
@Data
@ApiModel(description="排版管理列表")
public class UompPersonDutyDto extends BaseModel {

    @ApiModelProperty(value="主键")
    private String id;

    @ApiModelProperty(value="值班日期")
    private String date;

    @ApiModelProperty(value="电话")
    private String tel;

    @ApiModelProperty(value="人员名称")
    private String personName;

    @ApiModelProperty(value="运维组")
    private String orgGroupName;
}

