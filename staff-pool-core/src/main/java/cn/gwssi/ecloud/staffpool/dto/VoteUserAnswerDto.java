package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description="vote_user_answer")
@Data
public class VoteUserAnswerDto implements Serializable {
    @ApiModelProperty(value="")
    private String answerId;

    @ApiModelProperty(value="")
    private String voteId;

    @ApiModelProperty(value="")
    private Date fillTime;

    @ApiModelProperty(value="")
    private String reviewResult;

    @ApiModelProperty(value="")
    private String title;

    private static final long serialVersionUID = 1L;
}