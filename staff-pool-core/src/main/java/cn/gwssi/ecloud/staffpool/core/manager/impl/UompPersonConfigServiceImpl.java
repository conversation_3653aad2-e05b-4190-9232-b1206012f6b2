package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonConfigMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonConfigService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UompPersonConfigServiceImpl extends BaseManager<String, UompPersonConfig> implements UompPersonConfigService {

    @Resource
    private UompPersonConfigMapper uompPersonConfigMapper;

    @Override
    public int insertSelective(UompPersonConfig record) {
        return uompPersonConfigMapper.insertSelective(record);
    }


    @Override
    public int updateByPrimaryKeySelective(UompPersonConfig record) {
        return uompPersonConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public UompPersonConfig getConfigInfo(String configType) {
        return uompPersonConfigMapper.getConfigInfo(configType);
    }

}
