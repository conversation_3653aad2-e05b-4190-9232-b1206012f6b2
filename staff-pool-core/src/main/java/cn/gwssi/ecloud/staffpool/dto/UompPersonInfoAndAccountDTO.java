package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "未分配人员账号信息", description="未分配人员账号信息")
@Data
public class UompPersonInfoAndAccountDTO implements Serializable {
    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="入职时间")
    private String entryDate;
    @ApiModelProperty(value="入职公司")
    private String workingCompany;
    @ApiModelProperty(value="技术方向")
    private String technicalDirection;
    @ApiModelProperty(value="参与应用系统id")
    private String engagementProjectId;
    @ApiModelProperty(value="参与应用系统")
    private String engagementProjectName;
    @ApiModelProperty(value="参与应用系统json回显")
    private String engagementProjectJson;
    @ApiModelProperty(value="系统运维组id")
    private String orgGroupId;
    @ApiModelProperty(value="系统运维组名称")
    private String orgGroupName;
    @ApiModelProperty(value="参加运维组id")
    private String maintenanceGroupId;
    @ApiModelProperty(value="参加运维组名称")
    private String maintenanceGroupName;
    @ApiModelProperty(value="参加运维组json回显")
    private String maintenanceGroupJson;
    @ApiModelProperty(value="岗位id")
    private String postId;
    @ApiModelProperty(value="岗位名称")
    private String postName;
    @ApiModelProperty(value="岗位json回显")
    private String postJson;
    @ApiModelProperty(value="手机")
    private String mobile;
    @ApiModelProperty(value="地址")
    private String address;
    @ApiModelProperty(value="性别")
    private String sex;
}
