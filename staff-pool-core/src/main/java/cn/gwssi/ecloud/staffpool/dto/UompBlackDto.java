package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="黑名单管理列表查询")
@Data
public class UompBlackDto extends BaseModel {
    @ApiModelProperty(value = "")
    private String id;

    @ApiModelProperty(value = "")
    private String bizId;
    @ApiModelProperty(value = "")
    private String operatorName;

    @ApiModelProperty(value = "")
    private String operatorMessage;

    @ApiModelProperty(value = "")
    private String operatorReason;

    @ApiModelProperty(value = "")

    private Date operatorTime;
    @ApiModelProperty(value = "姓名")
    private String personName;
    @ApiModelProperty(value = "性别")
    private String personSex;
    @ApiModelProperty(value = "出生年月")
    private String personBirthday;
    @ApiModelProperty(value = "联系电话")
    private String tel;
    @ApiModelProperty(value = "学历")
    private String education;
    @ApiModelProperty(value = "工作职位")
    private String post;
    private String major;
    @ApiModelProperty(value = "就职公司")
    private String workingCompany;
    @ApiModelProperty(value = "运维组织")
    private String orgGroupName;
    @ApiModelProperty(value = "技能方向")
    private String technicalDirection;
    @ApiModelProperty(value = "入职时间")
    private String entryDate;

    private String trialStatus;
    private String blacklist;
    private String blacklistReason;
    private String instId;
    private String type;

    private String taskId;

    private String taskLinkId;
}