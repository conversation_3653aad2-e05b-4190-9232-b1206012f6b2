package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员值班表(UompPersonDuty)表实体类
 */
@SuppressWarnings("serial")
@Data
@ApiModel(description="人员值班表")
public class UompPersonDuty extends BaseModel {

    @ApiModelProperty(value="主键")
    private String id;

    @ApiModelProperty(value="值班日期")
    private String date;

    @ApiModelProperty(value="删除标记")
    private String delFlag;
}

