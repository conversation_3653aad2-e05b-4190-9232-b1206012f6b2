package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="现场人员情况响应类")
@Data
public class PersonStatusListDTO implements Serializable {

    @ApiModelProperty(value="入场")
    private List<MonthNumBean> entryList;
    @ApiModelProperty(value="退场")
    private List<MonthNumBean> exitList;
    @ApiModelProperty(value="临时入场")
    private List<MonthNumBean> tempList;

    @Data
    public static class MonthNumBean{

        @ApiModelProperty(value="月份")
        private String month;
        @ApiModelProperty(value="数量")
        private Integer num;
    }
}
