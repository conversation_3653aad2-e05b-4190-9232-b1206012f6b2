package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractResource;
import cn.gwssi.ecloud.staffpool.core.dao.UompContractResourceMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractResourceService;

import java.util.List;
import java.util.Map;

@Service
public class UompContractResourceServiceImpl extends BaseManager<String, UompContractResource> implements UompContractResourceService {

    @Autowired
    private UompContractResourceMapper uompContractResourceMapper;

    @Override
    public int deleteByPrimaryKey(String id) {
        return uompContractResourceMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(UompContractResource record) {
        return uompContractResourceMapper.insert(record);
    }

    @Override
    public int insertSelective(UompContractResource record) {
        return uompContractResourceMapper.insertSelective(record);
    }

    @Override
    public UompContractResource selectByPrimaryKey(String id) {
        return uompContractResourceMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(UompContractResource record) {
        return uompContractResourceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompContractResource record) {
        return uompContractResourceMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<UompContractResource> getResourceListByContractId(String contractId) {
        return uompContractResourceMapper.getResourceListByContractId(contractId);
    }

    @Override
    public void deleteByContractId(String contractId) {
        uompContractResourceMapper.deleteByContractId(contractId);
    }

    @Override
    public void deleteByIdList(List<String> idList) {
        uompContractResourceMapper.deleteByIdList(idList);
    }

    @Override
    public List<String> selectListByCmIdAndCiId(List<Map<String, String>> paramList) {
        return uompContractResourceMapper.selectListByCmIdAndCiId(paramList);
    }

    @Override
    public void insertBatch(List<UompContractResource> resourceInsertList) {
        uompContractResourceMapper.insertBatch(resourceInsertList);
    }

    @Override
    public void updateBatchByCmIdAndCiId(List<UompContractResource> resourceUpdateList) {
        uompContractResourceMapper.updateBatchByCmIdAndCiId(resourceUpdateList);
    }

}
