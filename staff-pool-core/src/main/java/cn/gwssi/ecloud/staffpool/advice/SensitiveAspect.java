package cn.gwssi.ecloud.staffpool.advice;

import cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize;
import cn.gwssi.ecloud.staffpool.annotation.SensitiveEnum;
import cn.gwssi.ecloud.staffpool.service.DesensitizeProcessor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

@Aspect
@Component
public class SensitiveAspect{
    @Resource
    DesensitizeProcessor desensitizeProcessor;
    @Pointcut("@annotation(cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize)")
    public void desensitizePointCut(JoinPoint joinPoint){

    }

    @Around("@annotation(cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize)")
    public Object pointHandle(ProceedingJoinPoint joinPoint){
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        AutoDesensitize desensitize = method.getAnnotation(AutoDesensitize.class);
        Object obj = null;
        String processMethod = desensitize.processMethod();
        if (StringUtils.isNotEmpty(processMethod) && processMethod.equalsIgnoreCase(SensitiveEnum.ENCRYPT.getKey())){
            for (Object arg : args){
                desensitizeProcessor.process(processMethod,arg);
            }

        }
        try {
            obj = joinPoint.proceed();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
        return obj;
    }

    @AfterReturning(pointcut = "@annotation(autoDesensitize)",returning = "result")
    public Object afterReturning(JoinPoint joinPoint, AutoDesensitize autoDesensitize,Object result){
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        AutoDesensitize desensitize = method.getAnnotation(AutoDesensitize.class);

        String processMethod = desensitize.processMethod();
        return desensitizeProcessor.process(processMethod,result);
    }
}
