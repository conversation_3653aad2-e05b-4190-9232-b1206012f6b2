package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.AccountListQueryVO;
import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompAccountApplyService;
import cn.gwssi.ecloud.staffpool.core.manager.UompAdmissionPersonService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.BaseInstDTO;
import cn.gwssi.ecloud.staffpool.dto.UompAccountApplyDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonInfoAndAccountDTO;
import cn.gwssi.ecloudframework.base.api.Page;
import cn.gwssi.ecloudframework.base.api.query.FieldRelation;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.query.WhereClause;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultFieldLogic;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultPage;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryField;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply;
import cn.gwssi.ecloud.staffpool.core.dao.UompAccountApplyMapper;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UompAccountApplyServiceImpl extends BaseManager<String, UompAccountApply> implements UompAccountApplyService {

    @Autowired
    private UompAccountApplyMapper uompAccountApplyMapper;
    @Autowired
    private UompPersonInfoService uompPersonInfoService;
    @Autowired
    private UompAdmissionPersonService uompAdmissionPersonService;
    @Autowired
    private SysDataDictMapper sysDataDictMapper;

    @Override
    public PageResult accountNumListPage(AccountListQueryVO accountListQueryVO) {
        // 判断登陆人角色,根据角色进行列表权限展示
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        // 运维组织
        String orgId = user.getOrgId();
        // 用户id
        String userId = user.getId();
        if (roles == null || roles.isEmpty()) {
            return new PageResult(new ArrayList());
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (null == accountListQueryVO.getPageNo()) {
            accountListQueryVO.setPageNo(1);
        }
        if (null == accountListQueryVO.getPageSize()) {
            accountListQueryVO.setPageSize(10);
        }
        QueryFilter queryFilter = new DefaultQueryFilter();
        // 设置分页
        Page page = new DefaultPage(accountListQueryVO.getPageNo(), accountListQueryVO.getPageSize(), new ArrayList<>(), true);
        queryFilter.setPage(page);
        //申请状态多选条件处理
        // 未删除
        queryFilter.addFilter("uaa.DEL_FLAG", "0", QueryOP.EQUAL);
        // 申请状态多选条件处理
        if (StringUtils.isNotEmpty(accountListQueryVO.getStatus())) {
            queryFilter.addFilter("uaa.STATUS", accountListQueryVO.getStatus(), QueryOP.IN);
        }
        // 申请时间
        if (StringUtils.isNotEmpty(accountListQueryVO.getEntryDate())) {
            String[] entryDates = accountListQueryVO.getEntryDate().split(",");
            queryFilter.addFilter("uaa.APPLY_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
            queryFilter.addFilter("uaa.APPLY_TIME", entryDates[1], QueryOP.LESS_EQUAL);
        }
        // 标题
        if (StringUtils.isNotEmpty(accountListQueryVO.getTitle())) {
            queryFilter.addFilter("uaa.TITLE", accountListQueryVO.getTitle(), QueryOP.LIKE);
        }
        // 申请人
        if (StringUtils.isNotEmpty(accountListQueryVO.getApplyPerson())) {
            queryFilter.addFilter("uaa.APPLY_PERSON", accountListQueryVO.getApplyPerson(), QueryOP.LIKE);
        }
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) { // 甲方运维负责人、总包商运维经理
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) { // 服务商运维负责人
            // 查看运维组下申请
            queryFilter.addFilter("uaa.CREATE_ORG_ID", orgId, QueryOP.EQUAL);
        } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) { // 帮助台、服务台
            // 查看本人下申请
            queryFilter.addFilter("uaa.CREATE_BY", userId, QueryOP.EQUAL);
        } else {
            // 其他角色返回空列表
            return new PageResult(new ArrayList());
        }
        List<WhereClause> whereClauses = queryFilter.getFieldLogic().getWhereClauses();
        DefaultFieldLogic orFieldLogicAnd = new DefaultFieldLogic();
        orFieldLogicAnd.setFieldRelation(FieldRelation.AND);
        orFieldLogicAnd.getWhereClauses().add(new DefaultQueryField("uaa.STATUS", QueryOP.EQUAL, "0"));
        orFieldLogicAnd.getWhereClauses().add(new DefaultQueryField("uaa.CREATE_BY", QueryOP.EQUAL, user.getUserId()));

        DefaultFieldLogic orFieldLogicOr = new DefaultFieldLogic();
        orFieldLogicOr.setFieldRelation(FieldRelation.OR);
        orFieldLogicOr.getWhereClauses().add(new DefaultQueryField("uaa.STATUS", QueryOP.NOT_EQUAL, "0"));
        orFieldLogicOr.getWhereClauses().add(orFieldLogicAnd);

        whereClauses.add(orFieldLogicOr);

        // 根据创建时间倒序
        queryFilter.addFieldSort("uaa.CREATE_TIME", "DESC");
        List<UompAccountApplyDTO> accountApplyDTOList = uompAccountApplyMapper.accountNumList(queryFilter);
        if (!CollectionUtils.isEmpty(accountApplyDTOList)) {
            for (UompAccountApplyDTO info : accountApplyDTOList) {
                // 状态：账号申请不通过，查询taskId和taskLinkedId（表单跳转使用）
                if ("3".equals(info.getStatus())) {
                    String instId = info.getInstId();
                    //根据instId查出taskId和taskLinkedId
                    BaseInstDTO baseDTO = uompPersonInfoService.selectTaskIdByInstId(instId);
                    if (null != baseDTO) {
                        info.setTaskId(baseDTO.getTaskId());
                        info.setTaskLinkId(baseDTO.getLinkId());
                    }
                }
            }
        }
        return new PageResult(accountApplyDTOList);
    }

    @Override
    public UompAccountApply selectInfoOneByPersonId(String personId) {
        return uompAccountApplyMapper.selectInfoOneByPersonId(personId);
    }

    /**
     * 账号申请人员（必须处于驻场服务中）
     * 人员审核通过、未分配账号、非黑名单、背调非不合格
     **/
    @Override
    public PageResult getNoAccountPeronList(QueryFilter queryFilter) {
        // 判断登陆人角色,根据角色进行列表权限展示
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        // 用户id
        String userId = user.getId();
        // 判断是否是运维人员(登录运维平台的都是已分配过账号的)
        QueryFilter iup = new DefaultQueryFilter(true);
        iup.addFilter("ORG_USER_ID", userId, QueryOP.EQUAL);
        iup.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        iup.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonInfo> isUompPerson = uompPersonInfoService.query(iup);
        // 通过eworker注册的账号直接返回null
        if (CollectionUtil.isEmpty(isUompPerson)) {
            return new PageResult(new ArrayList());
        }
        // 角色判断
        List<IUserRole> roles = user.getRoles();
        // 运维组织
        String orgId = user.getOrgId();
        if (roles == null || roles.isEmpty()) {
            return new PageResult(new ArrayList());
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        UompPersonInfo uompPersonInfo = isUompPerson.get(0);
        String companyId = uompPersonInfo.getWorkingCompanyId();
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) { // 甲方运维负责人、总包商运维经理
            // 查看所有通过人员
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER") || roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) { // 服务商运维负责人、帮助台、服务台
            // 服务商下的通过人员
            queryFilter.addFilter("upi.WORKING_COMPANY_ID", companyId, QueryOP.EQUAL);
        } else if (roleNames.contains("ITSM_GROUP LEADER")) { // 运维组长
            // 运维组下的通过人员
            queryFilter.addFilter("uog.ORG_GROUP_ID", orgId, QueryOP.EQUAL);
        } else {
            // 其他角色返回空列表
            return new PageResult(new ArrayList());
        }
        List<UompPersonInfoAndAccountDTO> list = uompPersonInfoService.selectNoAccountPeronList(queryFilter);
        if (!CollectionUtils.isEmpty(list)) {
            //查出技术方向字典
            Map<String, String> directionKeyMap = new HashMap<>();
            List<BaseDTO> baseList = sysDataDictMapper.selectSubListByDictKey("UOMP_TEC_DIRECTION");
            if (!CollectionUtils.isEmpty(baseList)) {
                for (BaseDTO baseDTO : baseList) {
                    directionKeyMap.put(baseDTO.getId(), baseDTO.getName());
                }
            }
            for (UompPersonInfoAndAccountDTO info : list) {
                String key = info.getTechnicalDirection();
                if (!MapUtils.isEmpty(directionKeyMap)) {
                    info.setTechnicalDirection(directionKeyMap.get(key));
                }

                List<Map> groupList = new ArrayList<>();
                List<String> groupIdList = new ArrayList<>();
                List<String> groupNameList = new ArrayList<>();
                List<Map> systemList = new ArrayList<>();
                List<String> systemIdList = new ArrayList<>();
                List<String> systemNameList = new ArrayList<>();
                List<Map> postList = new ArrayList<>();
                List<String> postIdList = new ArrayList<>();
                List<String> postNameList = new ArrayList<>();
                // 查询驻场关联的人员信息（账号申请人员需要）
                QueryFilter qf = new DefaultQueryFilter(true);
                // 身份证号
                qf.addFilter("PERSON_CARD", info.getPersonCard(), QueryOP.EQUAL);
                // 人员入场申请审核通过
                qf.addFilter("APPLY_STATUS", "2", QueryOP.EQUAL);
                // 未删除
                qf.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompAdmissionPerson> admissionPersonList = uompAdmissionPersonService.query(qf);
                if (!CollectionUtils.isEmpty(admissionPersonList)) {
                    for (UompAdmissionPerson person : admissionPersonList) {
                        //运维组
                        String maintenanceGroupJson = person.getMaintenanceGroupJson();
                        this.getJsonDetail(maintenanceGroupJson, groupIdList, groupNameList, groupList);
                        //参与应用系统
                        String systemJson = person.getEngagementProjectJson();
                        this.getJsonDetail(systemJson, systemIdList, systemNameList, systemList);
                        // 岗位
                        String postJson = person.getPostJson();
                        this.getJsonDetail(postJson, postIdList, postNameList, postList);
                    }
                }
                info.setEngagementProjectId(StringUtils.join(systemIdList, ","));
                info.setEngagementProjectName(StringUtils.join(systemNameList, ","));
                info.setEngagementProjectJson(JSONObject.toJSONString(systemList));
                info.setMaintenanceGroupId(StringUtils.join(groupIdList, ","));
                info.setMaintenanceGroupName(StringUtils.join(groupNameList, ","));
                info.setMaintenanceGroupJson(JSONObject.toJSONString(groupList));
                info.setPostId(StringUtils.join(postIdList, ","));
                info.setPostName(StringUtils.join(postNameList, ","));
                info.setPostJson(JSONObject.toJSONString(postList));
            }
        }
        return new PageResult(list);
    }

    /**
     * 账号权限管理添加人员（不关注是否驻场服务中）
     * 人员审核通过、未分配账号、非黑名单、背调非不合格
     **/
    @Override
    public PageResult getNoAccountPermissionPeronList(QueryFilter queryFilter) {
        List<UompPersonInfoAndAccountDTO> list = uompPersonInfoService.selectNoAccountPeronList(queryFilter);
        if (!CollectionUtils.isEmpty(list)) {
            //查出技术方向字典
            Map<String, String> directionKeyMap = new HashMap<>();
            List<BaseDTO> baseList = sysDataDictMapper.selectSubListByDictKey("UOMP_TEC_DIRECTION");
            if (!CollectionUtils.isEmpty(baseList)) {
                for (BaseDTO baseDTO : baseList) {
                    directionKeyMap.put(baseDTO.getId(), baseDTO.getName());
                }
            }
            for (UompPersonInfoAndAccountDTO info : list) {
                String key = info.getTechnicalDirection();
                if (!MapUtils.isEmpty(directionKeyMap)) {
                    info.setTechnicalDirection(directionKeyMap.get(key));
                }

                List<Map> groupList = new ArrayList<>();
                List<String> groupIdList = new ArrayList<>();
                List<String> groupNameList = new ArrayList<>();
                List<Map> systemList = new ArrayList<>();
                List<String> systemIdList = new ArrayList<>();
                List<String> systemNameList = new ArrayList<>();
                List<Map> postList = new ArrayList<>();
                List<String> postIdList = new ArrayList<>();
                List<String> postNameList = new ArrayList<>();
                // 查询驻场关联的人员信息（账号申请人员需要）
                QueryFilter qf = new DefaultQueryFilter(true);
                // 身份证号
                qf.addFilter("PERSON_CARD", info.getPersonCard(), QueryOP.EQUAL);
                // 人员入场申请审核通过
                qf.addFilter("APPLY_STATUS", "2", QueryOP.EQUAL);
                // 未删除
                qf.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompAdmissionPerson> admissionPersonList = uompAdmissionPersonService.query(qf);
                if (!CollectionUtils.isEmpty(admissionPersonList)) {
                    for (UompAdmissionPerson person : admissionPersonList) {
                        //运维组
                        String maintenanceGroupJson = person.getMaintenanceGroupJson();
                        this.getJsonDetail(maintenanceGroupJson, groupIdList, groupNameList, groupList);
                        //参与应用系统
                        String systemJson = person.getEngagementProjectJson();
                        this.getJsonDetail(systemJson, systemIdList, systemNameList, systemList);
                        // 岗位
                        String postJson = person.getPostJson();
                        this.getJsonDetail(postJson, postIdList, postNameList, postList);
                    }
                }
                info.setEngagementProjectId(StringUtils.join(systemIdList, ","));
                info.setEngagementProjectName(StringUtils.join(systemNameList, ","));
                info.setEngagementProjectJson(JSONObject.toJSONString(systemList));
                info.setMaintenanceGroupId(StringUtils.join(groupIdList, ","));
                info.setMaintenanceGroupName(StringUtils.join(groupNameList, ","));
                info.setMaintenanceGroupJson(JSONObject.toJSONString(groupList));
                info.setPostId(StringUtils.join(postIdList, ","));
                info.setPostName(StringUtils.join(postNameList, ","));
                info.setPostJson(JSONObject.toJSONString(postList));
            }
        }
        return new PageResult(list);
    }

    /**
     * 封装运维组织、应用系统字段
     *
     * @param jsonStr  解析字符串
     * @param idList   id集合
     * @param nameList 名称集合
     * @param list     运维组织、应用系统详情集合
     **/
    private void getJsonDetail(String jsonStr, List<String> idList, List<String> nameList, List<Map> list) {
        if (StringUtils.isNotEmpty(jsonStr)) {
            List<Object> jsonList = JSONObject.parseObject(jsonStr, List.class);
            for (Object obj : jsonList) {
                JSONObject jsonObject = (JSONObject) JSONObject.toJSON(obj);
                Map<String, Object> innerMap = jsonObject.getInnerMap();
                // 根据id去重
                String id = (String) innerMap.get("id");
                if (StringUtils.isNotEmpty(id)) {
                    if (!idList.contains(id)) {
                        list.add(innerMap);
                        idList.add(id);
                        nameList.add((String) innerMap.get("name"));
                    }
                }
            }
        }
    }
}
