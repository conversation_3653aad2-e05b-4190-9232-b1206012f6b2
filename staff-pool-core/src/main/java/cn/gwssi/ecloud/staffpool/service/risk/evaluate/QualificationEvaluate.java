package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 资质缺陷
 */
@Component
public class QualificationEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(PrivilegeAbuseEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        LOG.info("------用户{}计算资质缺陷------",uompPersonInfoVo.getPersonName());

    }

    @Override
    public String alias() {
        return "qualification";
    }
}
