package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.UompReportSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompReport;
import cn.gwssi.ecloud.staffpool.core.manager.UompReportService;
import cn.gwssi.ecloud.staffpool.dto.UompReportDTO;
import cn.gwssi.ecloud.staffpool.dto.UompReportListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(description = "服务报告管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/report")
public class UompReportController extends BaseController<UompReport> {

    @Resource
    private UompReportService uompReportService;


    @ApiOperation(value = "服务报告查询列表")
    @RequestMapping(value = "/getReportList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompReportListDTO> getReportList(HttpServletRequest request,
                                                       @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                       @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                       @RequestParam(value = "reportName", required = false) @ApiParam(value = "报告名称") String reportName,
                                                       @RequestParam(value = "systemId", required = false) @ApiParam(value = "应用系统id") String systemId,
                                                       @RequestParam(value = "systemName", required = false) @ApiParam(value = "应用系统名称") String systemName,
                                                       @RequestParam(value = "supplierName", required = false) @ApiParam(value = "服务商名称") String supplierName,
                                                       @RequestParam(value = "reportType", required = false) @ApiParam(value = "报告类型（1:周报 2:月报 3:年报），逗号隔开") String reportType,
                                                       @RequestParam(value = "uploadBeginDate", required = false) @ApiParam(value = "上传开始时间") String uploadBeginDate,
                                                       @RequestParam(value = "uploadEndDate", required = false) @ApiParam(value = "上传结束时间") String uploadEndDate) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 报告名称
        if (StringUtils.isNotEmpty(reportName)) {
            queryFilter.addFilter("REPORT_NAME", reportName, QueryOP.LIKE);
        }
        // 应用系统名称
        if (StringUtils.isNotEmpty(systemName)) {
            queryFilter.addFilter("APPLICATION_SYSTEM_NAME", systemName, QueryOP.LIKE);
        }
        // 服务商名称
        if (StringUtils.isNotEmpty(supplierName)) {
            queryFilter.addFilter("SUPPLIER_NAME", supplierName, QueryOP.LIKE);
        }
        // 报告类型
        if (StringUtils.isNotEmpty(reportType)) {
            queryFilter.addFilter("REPORT_TYPE", reportType, QueryOP.IN);
        }
        // 上传开始时间
        if (StringUtils.isNotEmpty(uploadBeginDate)) {
            queryFilter.addFilter("UPLOAD_TIME", uploadBeginDate + " 00:00:00", QueryOP.GREAT_EQUAL);
        }
        // 上传结束时间
        if (StringUtils.isNotEmpty(uploadEndDate)) {
            queryFilter.addFilter("UPLOAD_TIME", uploadEndDate + " 23:59:59", QueryOP.LESS_EQUAL);
        }
        // 应用系统id
        if (StringUtils.isNotEmpty(systemId)) {
            queryFilter.addFilter("APPLICATION_SYSTEM_MANAGEMENT_ID", systemId, QueryOP.EQUAL);
        }
        // 创建时间倒叙
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompReportService.getReportList(queryFilter);
    }

    @ApiOperation(value = "服务报告保存")
    @PostMapping(value = "/save")
    public ResultMsg<String> save(@Validated @RequestBody UompReportSave uompReportSave) {
        return getSuccessResult(uompReportService.save(uompReportSave));
    }

    @ApiOperation(value = "根据id查询服务报告详情")
    @RequestMapping(value = "/getReportInfoById", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<UompReportDTO> getReportInfoById(@RequestParam("id") @ApiParam(value = "主键id") String id) {
        return getSuccessResult(uompReportService.getReportInfoById(id));
    }

    @ApiOperation(value = "删除服务报告")
    @RequestMapping(value = "/deleteReport", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg deleteReport(@RequestParam("id") @ApiParam(value = "报告id") String id) {
        uompReportService.deleteReport(id);
        return getSuccessResult();
    }

    @Override
    protected String getModelDesc() {
        return "服务报告管理";
    }
}
