package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.TeamRiskConfigMapper;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig;
import cn.gwssi.ecloud.staffpool.core.manager.TeamRiskConfigManager;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TeamRiskConfigManagerImpl extends BaseManager<String, TeamRiskConfig> implements TeamRiskConfigManager {

    @Resource
    private TeamRiskConfigMapper teamRiskConfigMapper;

    @Override
    public List<TeamRiskConfig> getAll(){
       return teamRiskConfigMapper.getAll();
    }

}
