package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 权限滥用
 */
public class PrivilegeAbuseEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(PrivilegeAbuseEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        LOG.info("------用户{}开始计算权限滥用风险------",uompPersonInfoVo.getPersonName());
    }

    @Override
    public String alias() {
        return "privilegeAbuse";
    }

}
