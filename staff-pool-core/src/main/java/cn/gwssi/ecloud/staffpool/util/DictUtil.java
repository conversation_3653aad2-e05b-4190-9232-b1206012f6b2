package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.entity.SysDataDict;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class DictUtil {
    @Resource
    private SysDataDictMapper sysDataDictMapper;

    public String getKeyByName(String dictKey, String keyList) {
        if (StringUtils.isEmpty(dictKey)) {
            return "";
        }
        String[] keys = keyList.split(",");
        BaseDTO baseDTO = sysDataDictMapper.getKeyByName(dictKey, keys);
        if (baseDTO == null) {
            return null;
        }
        return baseDTO.getName();
    }

    public String getKeyByNameAndDictKey(String dictKey, String name, String isFirst) {
        //0 = 不区分层级，都查，1 = 只查第一级的
        if ("0".equals(isFirst)) {
            return sysDataDictMapper.selectKeyByDictkeyAndName(dictKey, name);
        } else {
            //查出顶层数据id
            String id = sysDataDictMapper.selectKeyByDictkeyAndKey(dictKey, dictKey);
            //查询第一层的数据
            return sysDataDictMapper.selectKeyByDictkeyAndNameAndParentId(dictKey, name, id);

        }
    }

    public List<String> getParentKeyByKey(String dictKey, String key) {
        String parentId = sysDataDictMapper.selectParentIdByDictKeyAndKey(dictKey, key);

        List<String> resultList = new ArrayList<>();
        if (StringUtils.isNotBlank(parentId)) {
            //先把最后一层放进去
            resultList.add(key);
            while (true) {
                Map<String, String> dictInfo = sysDataDictMapper.selectKeyAndParentIdById(parentId);
                if (dictInfo == null) {
                    break;
                } else {
                    parentId = dictInfo.get("parentId");
                    //往集合中方上级的key
                    resultList.add(dictInfo.get("keyName"));
                }
            }
            Collections.reverse(resultList);
            return resultList;
        } else {
            return null;
        }
    }

    /**
     * 根据 dictKey 查询名下所有字典项
     * @param dictKey
     * @return
     */
    public List<SysDataDict> getAllByDictKey(String dictKey){
        return sysDataDictMapper.selectAllByDictKeys(dictKey);
    }

    public String getNameByKey(String dictKey, String key){
        return sysDataDictMapper.selectNameByDictKeyAndKey(dictKey, key);
    }
}
