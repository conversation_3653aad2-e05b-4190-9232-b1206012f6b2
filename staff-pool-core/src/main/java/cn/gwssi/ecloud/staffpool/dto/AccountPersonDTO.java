package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@ApiModel(description="账号申请列表响应类")
@Data
public class AccountPersonDTO {

    @ApiModelProperty(value="账号")
    private String account;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="用户id")
    private String userId;
    @ApiModelProperty(value="用户手机号")
    private String tel;
    @ApiModelProperty(value="邮箱")
    private String email;
    @ApiModelProperty(value="系统用户id")
    private String orgUserId;
    @ApiModelProperty(value="系统运维组织id")
    private String orgGroupId;
    @ApiModelProperty(value="运维组织id")
    private String groupId;
    @ApiModelProperty(value="运维组织名称")
    private String groupName;
    @ApiModelProperty(value="就职公司名称")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="驻场服务状态 (0驻场服务 1离岗 -1未驻场)")
    private String entryStatus;
    @ApiModelProperty(value="账号状态 (0禁用1正常)")
    private String status;
}
