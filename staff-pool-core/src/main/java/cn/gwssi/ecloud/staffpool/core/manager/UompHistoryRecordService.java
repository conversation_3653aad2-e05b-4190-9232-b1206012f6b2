package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord;
import cn.gwssi.ecloud.staffpool.dto.UompBlackDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompHistoryRecordService extends Manager<String, UompHistoryRecord> {

    int insertSelective(UompHistoryRecord record);

    List<UompBlackDto> getBlackHistoryList(QueryFilter queryFilter);
}
