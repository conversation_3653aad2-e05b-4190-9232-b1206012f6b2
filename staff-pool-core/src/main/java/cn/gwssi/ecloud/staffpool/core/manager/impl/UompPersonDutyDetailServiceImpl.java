package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonDutyDetailMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompPersonEducationalMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonDutyDetailService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonDutyDetailServiceImpl extends BaseManager<String, UompPersonDutyDetail> implements UompPersonDutyDetailService {
    @Resource
    private UompPersonDutyDetailMapper dutyDetailMapper;
    @Override
    public void removeByDutyId(String dutyId) {
        dutyDetailMapper.removeByDutyId(dutyId);
    }

    @Override
    public List<UompPersonContactsDTO> selectByDutyId(String id) {
        return dutyDetailMapper.selectByDutyId(id);
    }

    @Override
    public List<UompPersonDutyDto> selectByMonth(String month,String orgId) {
        return dutyDetailMapper.selectByMonth(month,orgId);
    }

    @Override
    public int insertSelective(UompPersonDutyDetail uompPersonDutyDetail) {
        return dutyDetailMapper.insertSelective(uompPersonDutyDetail);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonDutyDetail uompPersonDutyDetail) {
        return dutyDetailMapper.updateByPrimaryKeySelective(uompPersonDutyDetail);
    }
}

