package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProjectBySupplierDTO implements Serializable {

    @ApiModelProperty(value="人员id")
    private String id;
    @ApiModelProperty(value="编码")
    private String projectCode;
    @ApiModelProperty(value="名称")
    private String projectName;
    @ApiModelProperty(value="阶段")
    private String projectStage;
    @ApiModelProperty(value="状态")
    private String projectStatus;

    @ApiModelProperty(value="主责部门id")
    private String departId;
    @ApiModelProperty(value="主责部门名")
    private String departName;
}
