package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitApplication;
import cn.gwssi.ecloud.staffpool.dto.ExitApplyDTO;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompExitApplicationService extends Manager<String, UompExitApplication> {

    int insertSelective(UompExitApplication record);

    ExitApplyDTO getExitApply(String id);

    Integer countByOutTime(String time);
}
