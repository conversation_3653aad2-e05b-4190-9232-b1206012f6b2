package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractFile;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompContractFileMapper extends BaseDao<String, UompContractFile> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompContractFile record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UompContractFile record);

    int updateDelFlagByContractManagementId(UompContractFile uompContractFile);
}