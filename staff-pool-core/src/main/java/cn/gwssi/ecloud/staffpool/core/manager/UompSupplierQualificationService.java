package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierQualificationDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompSupplierQualificationService extends Manager<String, UompSupplierQualification> {

    ResponsePageData<UompSupplierQualificationDto> getQualificationList(String supplierManagementId, Integer pageNo, Integer pageSize);

    int updateById(UompSupplierQualification qualification);

    int deleteById(String id);

    int insertSelective(UompSupplierQualification record);
}
