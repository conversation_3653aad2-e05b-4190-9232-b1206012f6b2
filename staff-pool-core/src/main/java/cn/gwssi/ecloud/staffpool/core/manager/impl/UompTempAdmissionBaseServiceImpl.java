package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.dao.UompTempAdmissionBaseMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmissionBase;
import cn.gwssi.ecloud.staffpool.core.manager.UompTempAdmissionBaseService;
@Service
public class UompTempAdmissionBaseServiceImpl extends BaseManager<String, UompTempAdmissionBase> implements UompTempAdmissionBaseService{

    @Resource
    private UompTempAdmissionBaseMapper uompTempAdmissionBaseMapper;

    @Override
    public int insertSelective(UompTempAdmissionBase record) {
        return uompTempAdmissionBaseMapper.insertSelective(record);
    }
}
