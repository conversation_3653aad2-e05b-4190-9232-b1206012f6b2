package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="临时入场列表查询响应类")
@Data
public class InfoByNameDTO implements Serializable {

    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="性别")
    private String sex;
}
