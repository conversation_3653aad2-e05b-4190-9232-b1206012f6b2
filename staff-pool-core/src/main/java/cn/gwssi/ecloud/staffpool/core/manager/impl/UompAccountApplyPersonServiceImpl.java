package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompAccountApplyPersonMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompAccountApplyPersonService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

@Service
public class UompAccountApplyPersonServiceImpl extends BaseManager<String, UompAccountApplyPerson> implements UompAccountApplyPersonService {

    @Autowired
    private UompAccountApplyPersonMapper uompAccountApplyPersonMapper;

    @Override
    public UompAccountApplyPerson selectApplyInfoById(QueryFilter queryFilter) {
        return uompAccountApplyPersonMapper.selectApplyInfoById(queryFilter);
    }

    @Override
    public void upDateAuthorizationStatusByIds(List<String> idList) {
        if (!CollectionUtils.isEmpty(idList)) {
            uompAccountApplyPersonMapper.upDateAuthorizationStatusByIds(idList);
        }
    }

    @Override
    public BaseDTO selectSystemByUserId(String userId) {
        return uompAccountApplyPersonMapper.selectSystemByUserId(userId);
    }

    @Override
    public UompAccountApplyPerson getPersonAccountStatus(String personId) {
        UompAccountApplyPerson uompAccountApplyPerson = uompAccountApplyPersonMapper.selectOneByPersonId(personId);
        if (uompAccountApplyPerson == null) {
            uompAccountApplyPerson = new UompAccountApplyPerson();
            uompAccountApplyPerson.setAccountNum("");
            uompAccountApplyPerson.setAuthorizationStatus("0");
        }

        return uompAccountApplyPerson;
    }

    @Override
    public int insertSelective(UompAccountApplyPerson record) {
        return uompAccountApplyPersonMapper.insertSelective(record);
    }

    @Override
    public List<UompAccountApplyPerson> selectAllByApplyIds(String applyIds) {
        return uompAccountApplyPersonMapper.selectAllByApplyIdIn(Arrays.asList(applyIds.split(",")));
    }

    @Override
    public UompAccountApplyPerson selectInfoByPersonId(String userId) {
        return uompAccountApplyPersonMapper.selectInfoByPersonId(userId);
    }
}
