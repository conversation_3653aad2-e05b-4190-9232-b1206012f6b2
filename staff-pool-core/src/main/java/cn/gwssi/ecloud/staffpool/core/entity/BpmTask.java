package cn.gwssi.ecloud.staffpool.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@ApiModel(description="bpm_task")
@Data
public class BpmTask implements Serializable {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    private String name;

    @ApiModelProperty(value="")
    private String subject;

    @ApiModelProperty(value="")
    private String instId;

    @ApiModelProperty(value="")
    private String taskId;

    @ApiModelProperty(value="")
    private String actInstId;

    @ApiModelProperty(value="")
    private String actExecutionId;

    @ApiModelProperty(value="")
    private String nodeId;

    @ApiModelProperty(value="")
    private String defId;

    @ApiModelProperty(value="")
    private String assigneeId;

    @ApiModelProperty(value="")
    private String assigneeNames;

    @ApiModelProperty(value="")
    private String status;

    @ApiModelProperty(value="")
    private Integer priority;

    @ApiModelProperty(value="")
    private Date dueTime;

    @ApiModelProperty(value="")
    private String taskType;

    @ApiModelProperty(value="")
    private String parentId;

    @ApiModelProperty(value="")
    private String typeId;

    @ApiModelProperty(value="")
    private Date createTime;

    @ApiModelProperty(value="")
    private String createBy;

    @ApiModelProperty(value="")
    private Integer supportMobile;

    @ApiModelProperty(value="")
    private String backNode;

    private static final long serialVersionUID = 1L;
}