package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonNoCrimeImportDto {
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;

    @ExcelProperty(value = "证明编号")
    private String proofNumber;

    @ExcelProperty(value = "查询起始时间")
    private String queryBeginTime;

    @ExcelProperty(value = "查询终止时间")
    private String queryEndTime;

    @ExcelProperty(value = "出具单位")
    private String provideUnit;

    @ExcelProperty(value = "有效期")
    private String indate;
}