package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoHistory;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompPersonAllInfoHistoryMapper extends BaseDao<String, UompPersonAllInfoHistory> {

    UompPersonAllInfoHistory getByInstId(@Param("instId")String instId);

}