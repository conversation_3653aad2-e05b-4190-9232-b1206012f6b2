package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAbroad;
import cn.gwssi.ecloud.staffpool.dto.UompPersonAbroadDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonAbroadService extends Manager<String, UompPersonAbroad> {
    int insertSelective(UompPersonAbroad record);

    int updateByPrimaryKeySelective(UompPersonAbroad record);

    int updateByPersonId(UompPersonAbroad record);

    List<UompPersonAbroadDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
