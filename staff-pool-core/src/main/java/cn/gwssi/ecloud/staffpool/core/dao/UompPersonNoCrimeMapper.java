package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime;
import cn.gwssi.ecloud.staffpool.dto.UompPersonNoCrimeDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonNoCrimeMapper extends BaseDao<String, UompPersonNoCrime> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonNoCrime record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonNoCrime record);

    int updateByPersonId(UompPersonNoCrime record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonNoCrimeDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}