package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEntryExit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="uomp_person_entry_exit")
@Data
public class UompPersonEntryExitDto extends UompPersonEntryExit {
    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;
}