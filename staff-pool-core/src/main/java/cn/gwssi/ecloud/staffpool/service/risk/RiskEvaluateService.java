package cn.gwssi.ecloud.staffpool.service.risk;

import cn.gwssi.ecloud.staffpool.service.risk.evaluate.RiskEvaluateChain;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RiskEvaluateService {

    private RiskEvaluateChain riskEvaluateChain;

    @Resource
    private ApplicationContext applicationContext;

    @PostConstruct
    private void buildChain(){
        Map<String, RiskEvaluateChain> riskEvaluateChainMap = applicationContext.getBeansOfType(RiskEvaluateChain.class);
        AtomicReference<RiskEvaluateChain> node = new AtomicReference<>();
        riskEvaluateChainMap.forEach((k,v) ->{
            if (node.get() == null){
                node.set(v);
                this.riskEvaluateChain = node.get();
                return;
            }

            node.get().setNextChain(v);
            node.set(v);
        });
    }

    public void evaluate(UompPersonInfoVo uompPersonInfoVo,String riskName){
        riskEvaluateChain.chain(uompPersonInfoVo, riskName);
    }
}
