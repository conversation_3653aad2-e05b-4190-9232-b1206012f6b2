package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompExitAdmissionRelation extends BaseModel {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    private String applyPersonId;

    @ApiModelProperty(value="")
    private String exitApplyId;

    @ApiModelProperty(value="")
    private String createBy;

    @ApiModelProperty(value="")
    private Date createTime;

    @ApiModelProperty(value="")
    private String updateBy;

    @ApiModelProperty(value="")
    private Date updateTime;

    @ApiModelProperty(value="")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}