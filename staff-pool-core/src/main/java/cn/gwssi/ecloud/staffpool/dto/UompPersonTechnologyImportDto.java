package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonTechnologyImportDto {
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "获取时间")
    private String getTime;

    @ExcelProperty(value = "资质名称")
    private String qualiftyName;

    @ExcelProperty(value = "资质类型")
    private String qualiftyType;

    @ExcelProperty(value = "颁证机构")
    private String certificationBody;

    @ExcelProperty(value = "证书有效起始时间")
    private String startTime;

    @ExcelProperty(value = "证书有效终止时间")
    private String endTime;
}