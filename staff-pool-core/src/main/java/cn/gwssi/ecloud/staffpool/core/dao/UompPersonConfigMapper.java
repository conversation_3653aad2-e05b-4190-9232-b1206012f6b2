package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UompPersonConfigMapper extends BaseDao<String, UompPersonConfig> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonConfig record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonConfig record);

    UompPersonConfig getConfigInfo(@Param("configType") String configType);
}