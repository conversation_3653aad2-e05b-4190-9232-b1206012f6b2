package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonConfig;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompPersonConfigService extends Manager<String, UompPersonConfig> {
    int insertSelective(UompPersonConfig record);

    int updateByPrimaryKeySelective(UompPersonConfig record);

    UompPersonConfig getConfigInfo(String configType);
}
