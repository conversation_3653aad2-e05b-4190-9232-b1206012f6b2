package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.AccountListQueryVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompAccountApplyService extends Manager<String, UompAccountApply> {

    PageResult accountNumListPage(AccountListQueryVO accountListQueryVO);

    PageResult getNoAccountPeronList(QueryFilter queryFilter);

    PageResult getNoAccountPermissionPeronList(QueryFilter queryFilter);

    UompAccountApply selectInfoOneByPersonId(String personId);

}
