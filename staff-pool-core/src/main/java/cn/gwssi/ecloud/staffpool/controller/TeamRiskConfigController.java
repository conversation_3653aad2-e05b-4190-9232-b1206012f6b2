package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskConfig;
import cn.gwssi.ecloud.staffpool.core.manager.TeamRiskConfigManager;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/module/staffpool/risk/config/")
@Api(
        description = "团队风险配置接口"
)
public class TeamRiskConfigController extends ControllerTools {

    @Resource
    private TeamRiskConfigManager teamRiskConfigManager;

    @RequestMapping("listJson")
    public PageResult listJson(HttpServletRequest request) {
        QueryFilter queryFilter = getQueryFilter(request);
        return new PageResult(teamRiskConfigManager.query(queryFilter));
    }

    /**
     * 获取对象
     */
    @RequestMapping("get")
    public ResultMsg<TeamRiskConfig> get(@RequestParam String id) throws Exception {
        TeamRiskConfig teamRiskConfig = null;
        if(StringUtil.isNotEmpty(id)) {
            teamRiskConfig = teamRiskConfigManager.get(id);
        }
        return getSuccessResult(teamRiskConfig);
    }

    /**
     * 保存
     */
    @RequestMapping("save")
    @CatchErr(write2errorlog = true)
    public ResultMsg<String> save(@RequestBody TeamRiskConfig teamRiskConfig) throws Exception {
        String desc;
        if (StringUtil.isEmpty(teamRiskConfig.getId())) {
            desc = "添加%s成功";
            teamRiskConfigManager.create(teamRiskConfig);
        } else {
            teamRiskConfigManager.update(teamRiskConfig);
            desc = "更新%s成功";
        }
        return getSuccessResult(teamRiskConfig.getId(),String.format(desc, "风险指标"));
    }

    /**
     * 批量删除
     */
    @RequestMapping("remove")
    public ResultMsg<String> remove(@RequestParam String id) throws Exception {
        teamRiskConfigManager.remove(id);
        return getSuccessResult(String.format("删除%s成功", "风险指标"));
    }

}
