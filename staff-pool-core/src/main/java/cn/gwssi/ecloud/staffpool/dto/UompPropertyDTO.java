package cn.gwssi.ecloud.staffpool.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "资产信息")
public class UompPropertyDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "编号")
    private String resourceNo;
    @ApiModelProperty(value = "名称")
    private String resourceName;
    @ApiModelProperty(value = "状态")
    private String resourceState;
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date tendDateStart;
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date tendDateEnd;
    @ApiModelProperty(value = "维保状态")
    private String tendStatus;
    @ApiModelProperty(value = "分类")
    private String type;
    @ApiModelProperty(value = "基线版本id")
    private String baselineId;
    @ApiModelProperty(value = "所属单位")
    private String deviceUnit;
    @ApiModelProperty(value = "所属单位id")
    private String deviceUnitId;
    @ApiModelProperty(value = "设备状态")
    private String deviceState;
    @ApiModelProperty(value = "设备型号")
    private String deviceModel;
    @ApiModelProperty(value = "开始日期-页面展示")
    private String tendDateStartStr;
    @ApiModelProperty(value = "结束日期-页面展示")
    private String tendDateEndStr;
}
