package cn.gwssi.ecloud.staffpool.util;

import java.lang.reflect.Field;

public class ReflectUtil {

    public static Object getFieldValue(Object obj,String fieldName){
        if (obj == null || fieldName == null ) return null;
        Class<?> clazz = obj.getClass();
        while (clazz != null){
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            } catch (IllegalAccessException e) {
                throw new RuntimeException("无法访问字段"+fieldName,e);
            }
        }
        throw new RuntimeException("字段不存在"+fieldName);
    }
}
