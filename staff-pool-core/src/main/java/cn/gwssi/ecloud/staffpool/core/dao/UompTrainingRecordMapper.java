package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UompTrainingRecordMapper  extends BaseDao<String, UompTrainingRecord> {

    int insertSelective(UompTrainingRecord record);
    List<UompTrainingRecord> selectTrainingRecordByUserId(String userId);

    int updateByPrimaryKeySelective(UompTrainingRecord trainingRecord);
}
