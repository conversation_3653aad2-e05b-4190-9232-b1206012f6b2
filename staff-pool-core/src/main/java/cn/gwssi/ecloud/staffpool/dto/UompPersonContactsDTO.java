package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "人员详情信息", description="人员详情")
@Data
public class UompPersonContactsDTO extends UompPersonInfo {
    @ApiModelProperty("驻场服务地点")
    private String serviceLocation;
}
