package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.dao.UompContractFileMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractFile;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractFileService;
@Service
public class UompContractFileServiceImpl extends BaseManager<String, UompContractFile> implements UompContractFileService{

    @Resource
    private UompContractFileMapper uompContractFileMapper;

    @Override
    public int insertSelective(UompContractFile record) {
        return uompContractFileMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompContractFile record) {
        return uompContractFileMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateDelFlagByContractManagementId(UompContractFile uompContractFile) {
        return uompContractFileMapper.updateDelFlagByContractManagementId(uompContractFile);
    }

}
