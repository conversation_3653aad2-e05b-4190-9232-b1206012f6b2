package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import java.util.Date;

public class UompSupplierQualification extends BaseModel {

    private String supplierManagementId;

    private String certificateName;

    private String certificateNum;

    private String issuingAuthority;

    private String issuingDate;

    private String endTime;

    private String files;

    private String standards;

    private String statement;

    private String businessArea;

    private String assessmentLevel;

    private String businessLine;

    private String qualificationLevel;

    private String trialArea;

    private String certificationSubItem;

    private String otherName;

    private String createOrgId;

    private String updateOrgId;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplierManagementId() {
        return supplierManagementId;
    }

    public void setSupplierManagementId(String supplierManagementId) {
        this.supplierManagementId = supplierManagementId;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    public String getIssuingAuthority() {
        return issuingAuthority;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public String getIssuingDate() {
        return issuingDate;
    }

    public void setIssuingDate(String issuingDate) {
        this.issuingDate = issuingDate;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getStandards() {
        return standards;
    }

    public void setStandards(String standards) {
        this.standards = standards;
    }

    public String getStatement() {
        return statement;
    }

    public void setStatement(String statement) {
        this.statement = statement;
    }

    public String getBusinessArea() {
        return businessArea;
    }

    public void setBusinessArea(String businessArea) {
        this.businessArea = businessArea;
    }

    public String getAssessmentLevel() {
        return assessmentLevel;
    }

    public void setAssessmentLevel(String assessmentLevel) {
        this.assessmentLevel = assessmentLevel;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getQualificationLevel() {
        return qualificationLevel;
    }

    public void setQualificationLevel(String qualificationLevel) {
        this.qualificationLevel = qualificationLevel;
    }

    public String getTrialArea() {
        return trialArea;
    }

    public void setTrialArea(String trialArea) {
        this.trialArea = trialArea;
    }

    public String getCertificationSubItem() {
        return certificationSubItem;
    }

    public void setCertificationSubItem(String certificationSubItem) {
        this.certificationSubItem = certificationSubItem;
    }

    public String getOtherName() {
        return otherName;
    }

    public void setOtherName(String otherName) {
        this.otherName = otherName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierManagementId=").append(supplierManagementId);
        sb.append(", certificateName=").append(certificateName);
        sb.append(", certificateNum=").append(certificateNum);
        sb.append(", issuingAuthority=").append(issuingAuthority);
        sb.append(", issuingDate=").append(issuingDate);
        sb.append(", endTime=").append(endTime);
        sb.append(", files=").append(files);
        sb.append(", standards=").append(standards);
        sb.append(", statement=").append(statement);
        sb.append(", businessArea=").append(businessArea);
        sb.append(", assessmentLevel=").append(assessmentLevel);
        sb.append(", businessLine=").append(businessLine);
        sb.append(", qualificationLevel=").append(qualificationLevel);
        sb.append(", trialArea=").append(trialArea);
        sb.append(", certificationSubItem=").append(certificationSubItem);
        sb.append(", otherName=").append(otherName);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", createOrgId=").append(createOrgId);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateOrgId=").append(updateOrgId);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompSupplierQualification other = (UompSupplierQualification) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSupplierManagementId() == null ? other.getSupplierManagementId() == null : this.getSupplierManagementId().equals(other.getSupplierManagementId()))
            && (this.getCertificateName() == null ? other.getCertificateName() == null : this.getCertificateName().equals(other.getCertificateName()))
            && (this.getCertificateNum() == null ? other.getCertificateNum() == null : this.getCertificateNum().equals(other.getCertificateNum()))
            && (this.getIssuingAuthority() == null ? other.getIssuingAuthority() == null : this.getIssuingAuthority().equals(other.getIssuingAuthority()))
            && (this.getIssuingDate() == null ? other.getIssuingDate() == null : this.getIssuingDate().equals(other.getIssuingDate()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getFiles() == null ? other.getFiles() == null : this.getFiles().equals(other.getFiles()))
            && (this.getStandards() == null ? other.getStandards() == null : this.getStandards().equals(other.getStandards()))
            && (this.getStatement() == null ? other.getStatement() == null : this.getStatement().equals(other.getStatement()))
            && (this.getBusinessArea() == null ? other.getBusinessArea() == null : this.getBusinessArea().equals(other.getBusinessArea()))
            && (this.getAssessmentLevel() == null ? other.getAssessmentLevel() == null : this.getAssessmentLevel().equals(other.getAssessmentLevel()))
            && (this.getBusinessLine() == null ? other.getBusinessLine() == null : this.getBusinessLine().equals(other.getBusinessLine()))
            && (this.getQualificationLevel() == null ? other.getQualificationLevel() == null : this.getQualificationLevel().equals(other.getQualificationLevel()))
            && (this.getTrialArea() == null ? other.getTrialArea() == null : this.getTrialArea().equals(other.getTrialArea()))
            && (this.getCertificationSubItem() == null ? other.getCertificationSubItem() == null : this.getCertificationSubItem().equals(other.getCertificationSubItem()))
            && (this.getOtherName() == null ? other.getOtherName() == null : this.getOtherName().equals(other.getOtherName()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateOrgId() == null ? other.getCreateOrgId() == null : this.getCreateOrgId().equals(other.getCreateOrgId()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUpdateOrgId() == null ? other.getUpdateOrgId() == null : this.getUpdateOrgId().equals(other.getUpdateOrgId()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierManagementId() == null) ? 0 : getSupplierManagementId().hashCode());
        result = prime * result + ((getCertificateName() == null) ? 0 : getCertificateName().hashCode());
        result = prime * result + ((getCertificateNum() == null) ? 0 : getCertificateNum().hashCode());
        result = prime * result + ((getIssuingAuthority() == null) ? 0 : getIssuingAuthority().hashCode());
        result = prime * result + ((getIssuingDate() == null) ? 0 : getIssuingDate().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getFiles() == null) ? 0 : getFiles().hashCode());
        result = prime * result + ((getStandards() == null) ? 0 : getStandards().hashCode());
        result = prime * result + ((getStatement() == null) ? 0 : getStatement().hashCode());
        result = prime * result + ((getBusinessArea() == null) ? 0 : getBusinessArea().hashCode());
        result = prime * result + ((getAssessmentLevel() == null) ? 0 : getAssessmentLevel().hashCode());
        result = prime * result + ((getBusinessLine() == null) ? 0 : getBusinessLine().hashCode());
        result = prime * result + ((getQualificationLevel() == null) ? 0 : getQualificationLevel().hashCode());
        result = prime * result + ((getTrialArea() == null) ? 0 : getTrialArea().hashCode());
        result = prime * result + ((getCertificationSubItem() == null) ? 0 : getCertificationSubItem().hashCode());
        result = prime * result + ((getOtherName() == null) ? 0 : getOtherName().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateOrgId() == null) ? 0 : getCreateOrgId().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateOrgId() == null) ? 0 : getUpdateOrgId().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}