package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompExitAcceptInfoMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompExitAcceptInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompExitAcceptInfoService;
import cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.hutool.core.collection.CollectionUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class UompExitAcceptInfoServiceImpl extends BaseManager<String, UompExitAcceptInfo> implements UompExitAcceptInfoService {

    @Resource
    private UompExitAcceptInfoMapper uompExitAcceptInfoMapper;

    @Override
    public int insertSelective(UompExitAcceptInfo record) {
        return uompExitAcceptInfoMapper.insertSelective(record);
    }

    @Override
    public Set<DefaultIdentity> selectInfoById(String id) {
        Set<DefaultIdentity> defaultIdentitySet = new HashSet<>();
        List<UompAcceptInfoDTO> uompAcceptInfoDTOList = uompExitAcceptInfoMapper.selectInfoById(id);
        if (!uompAcceptInfoDTOList.isEmpty()) {
            for (UompAcceptInfoDTO uompAcceptInfo : uompAcceptInfoDTOList) {
                DefaultIdentity defaultIdentity = new DefaultIdentity();
                defaultIdentity.setId(uompAcceptInfo.getId() == null ? "" : uompAcceptInfo.getId());
                defaultIdentity.setName(uompAcceptInfo.getName() == null ? "" : uompAcceptInfo.getName());
                defaultIdentity.setType(uompAcceptInfo.getType() == null ? "" : uompAcceptInfo.getType());
                defaultIdentity.setOrgId(uompAcceptInfo.getOrgId() == null ? "" : uompAcceptInfo.getOrgId());

                defaultIdentitySet.add(defaultIdentity);
            }
        }
        return defaultIdentitySet;
    }

    @Override
    public Boolean judgeExitAccept(String exitApplyId) {
        List<String> accountList = uompExitAcceptInfoMapper.selectAccountByExitApplyId(exitApplyId);
        return CollectionUtil.isNotEmpty(accountList);
    }
}
