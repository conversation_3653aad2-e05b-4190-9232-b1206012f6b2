package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEntryExit;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEntryExitDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonEntryExitMapper extends BaseDao<String, UompPersonEntryExit> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonEntryExit record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonEntryExit record);

    int updateByPersonId(UompPersonEntryExit record);

    List<UompPersonEntryExitDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}