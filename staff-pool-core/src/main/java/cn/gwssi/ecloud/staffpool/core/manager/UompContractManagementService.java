package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.UompContractFileSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractResourceSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.SupplierContractDTO;
import cn.gwssi.ecloud.staffpool.dto.UompContractManagementListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompContractManagementService extends Manager<String, UompContractManagement> {

    int insertSelective(UompContractManagement record);

    int updateByPrimaryKey(UompContractManagement record);

    PageResult getContractList(UompContractManagementQueryVO uompContractManagementQueryVO);

    String save(UompContractManagementSave uompContractManagementSave);

    void deleteContract(String id);

    void saveFile(UompContractFileSave uompContractFileSave);

    PageResult getFileList(QueryFilter queryFilter);

    void deleteFile(String id);

    PageResult getPropertyList(QueryFilter queryFilter);

    UompContractManagementListDTO getInfoById(String id);

    Boolean compareTime(String time);

    List<UompContractManagement> getRealtionContractList(QueryFilter queryFilter);

    List<UompContractManagement> getUnbindingContractList(String projectManagementId, String contractName);

    PageResult getContractListDialog(QueryFilter queryFilter);

    List<BaseDTO> getIdAndNameList();

    List<UompContractManagement> bindingContractListBySystemId(QueryFilter queryFilter);

    List<UompContractManagement> unbindingContractList(QueryFilter queryFilter);

    List<SupplierContractDTO> getContractTop5();

    List<UompContractManagement> getContractNoticeList(String day);

    void refresh() throws Exception;

    void saveResource(UompContractResourceSave uompContractResourceSave);

    PageResult getResourceList(QueryFilter queryFilter);

    void deleteResource(String ids);
}
