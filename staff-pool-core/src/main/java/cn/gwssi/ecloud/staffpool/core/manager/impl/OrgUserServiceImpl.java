package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.OrgUserMapper;
import cn.gwssi.ecloud.staffpool.core.entity.OrgUser;
import cn.gwssi.ecloud.staffpool.core.manager.OrgUserService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OrgUserServiceImpl extends BaseManager<String, OrgUser> implements OrgUserService {

    @Resource
    private OrgUserMapper OrgUserMapper;

    @Override
    public String selectIdByPersonCard(String personCard) {
        return OrgUserMapper.selectIdByPersonCard(personCard);
    }
}
