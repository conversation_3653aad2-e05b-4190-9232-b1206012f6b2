package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.BpmTaskMapper;
import cn.gwssi.ecloud.staffpool.core.entity.BpmTask;
import cn.gwssi.ecloud.staffpool.core.manager.BpmTaskService;
import cn.gwssi.ecloud.staffpool.dto.BpmTaskDto;
import cn.gwssi.ecloud.staffpool.dto.LabelDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
public class BpmTaskServiceImpl extends BaseManager<String, BpmTask> implements BpmTaskService {

    @Resource
    private BpmTaskMapper bpmTaskMapper;

    @Override
    public int deleteByPrimaryKey(String id) {
        return bpmTaskMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(BpmTask record) {
        return bpmTaskMapper.insert(record);
    }

    @Override
    public int insertSelective(BpmTask record) {
        return bpmTaskMapper.insertSelective(record);
    }

    @Override
    public BpmTask selectByPrimaryKey(String id) {
        return bpmTaskMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(BpmTask record) {
        return bpmTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(BpmTask record) {
        return bpmTaskMapper.updateByPrimaryKey(record);
    }

    @Override
    public int countBySelective(List<String> userRights, String userId) {
        return bpmTaskMapper.countBySelective(userRights, userId);
    }

    @Override
    public List<BpmTaskDto> selectTodoList(Set<String> userRights, String userId, String appTodoType,
                                           String intimeStart, String intimeEnd, String subject,String createStartTime,String createEndTime, String defIds,
                                           Integer pageNo, Integer pageSize,String sort,String orderBy) {
        String[] defArr = null;
        if (!StringUtils.isEmpty(defIds)) {
            defArr = defIds.split(",");
        }
        pageNo = (pageNo - 1) * pageSize;
        return bpmTaskMapper.selectTodoList(userRights, userId, appTodoType, intimeStart, intimeEnd,
                subject, defArr, pageNo, pageSize,createStartTime,createEndTime,sort,orderBy);
    }

    @Override
    public Integer selectTodoListTotal(Set<String> userRights, String userId, String appTodoType,
                                       String intimeStart, String intimeEnd, String subject, String defIds,String createStartTime,String createEndTime) {
        String[] defArr = null;
        if (!StringUtils.isEmpty(defIds)) {
            defArr = defIds.split(",");
        }
        return bpmTaskMapper.selectTodoListTotal(userRights, userId, appTodoType, intimeStart, intimeEnd, subject, defArr,
                createStartTime,createEndTime);
    }

    @Override
    public List<BpmTaskDto> selectInstList(Set<String> userRights, String userId, String appTodoType, String intimeStart,
                                           String intimeEnd, String subject, String defIds, Integer pageNo, Integer pageSize,
                                           String sort, String orderBy) {
        String[] defArr = null;
        if (!StringUtils.isEmpty(defIds)) {
            defArr = defIds.split(",");
        }
        pageNo = (pageNo - 1) * pageSize;
        return bpmTaskMapper.selectInstList(userRights, userId, appTodoType, intimeStart, intimeEnd, subject, defArr, pageNo, pageSize,sort,orderBy);
    }

    @Override
    public Integer selectInstListTotal(Set<String> userRights, String userId, String appTodoType, String intimeStart, String intimeEnd, String subject, String defIds) {
        String[] defArr = null;
        if (!StringUtils.isEmpty(defIds)) {
            defArr = defIds.split(",");
        }
        return bpmTaskMapper.selectInstListTotal(userRights, userId, appTodoType, intimeStart, intimeEnd, subject, defArr);
    }

    @Override
    public List<LabelDTO> selectApprove() {
        return bpmTaskMapper.selectApprove();
    }
}
