package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource;
import cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CmdbCommResourceMapper extends BaseDao<String, CmdbCommResource> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmdbCommResource record);

    List<CmdbCommResource> getConfList(QueryFilter var1);

    List<CmdbCommResource> selectInfoByContractId(QueryFilter queryFilter);

    List<UompPropertyDTO> selectPropertyList(QueryFilter queryFilter);

}