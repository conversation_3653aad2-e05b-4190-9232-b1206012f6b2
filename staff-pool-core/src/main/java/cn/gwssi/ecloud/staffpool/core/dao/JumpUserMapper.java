package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.JumpUser;
import cn.gwssi.ecloud.staffpool.dto.UompFortessUserDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JumpUserMapper extends BaseDao<String, JumpUser> {
    int insertSelective(JumpUser record);

    UompFortessUserDTO selectInfoByUserId(@Param("userId") String userId);

    List<UompFortessUserDTO> selectListByUserIds(@Param("idList") List<String> idList);

    int updateDeletetFlag(@Param("userId") String userId);

    String selectJumpUserByUserId(@Param("userId") String userId);

    String selectUserIdByUser(@Param("account") String account);

    int updateJumpUser(@Param("id") String id, @Param("name") String name, @Param("email") String email);

}