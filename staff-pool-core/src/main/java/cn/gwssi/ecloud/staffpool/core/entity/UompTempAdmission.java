package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompTempAdmission extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String personName;

    @ApiModelProperty(value="")
    
    private String personCard;

    @ApiModelProperty(value="")
    
    private String tel;

    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="就职公司json")
    private String workingCompanyJson;

    @ApiModelProperty(value="")
    
    private Date planVisitTime;

    @ApiModelProperty(value="")
    
    private Date realVisitTime;

    @ApiModelProperty(value="")
    
    private String destClerkId;

    @ApiModelProperty(value="")
    
    private String destClerkName;

    @ApiModelProperty(value="")
    
    private Date exitTime;

    @ApiModelProperty(value="")
    
    private String jobContent;

    @ApiModelProperty(value="")
    
    private String managerComment;

    @ApiModelProperty(value="")
    
    private String applyStatus;

    @ApiModelProperty(value="")
    
    private String instId;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private String createOrgId;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String updateOrgId;

    @ApiModelProperty(value="")
    
    private String delFlag;

    @ApiModelProperty(value="")
    
    private String applyCode;

    @ApiModelProperty(value="")
    private String applyTitle;

    @ApiModelProperty(value="")
    private String blacklist;

    @ApiModelProperty(value="")
    private String blacklistReason;

    @ApiModelProperty(value="")
    private String baseId;

    @ApiModelProperty(value="")
    private String acceptName;

    @ApiModelProperty(value="性别")
    private String sex;

    @ApiModelProperty(value="职务")
    private String applicatDuty;

    @ApiModelProperty(value = "应用系统")
    private String engagementProject;

    @ApiModelProperty(value = "应用系统id")
    private String engagementProjectId;

    @ApiModelProperty(value = "应用系统json")
    private String engagementProjectJson;

    @ApiModelProperty(value = "备案状态 0:未备案, 1:已备案")
    private String filingStatus;

    private static final long serialVersionUID = 1L;

}