package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionOutDetails;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompPermissionOutDetailsMapper extends BaseDao<String, UompPermissionOutDetails> {


    int insertSelective(UompPermissionOutDetails record);

    UompPermissionOutDetails selectOutInfoById(@Param("outDetailId") String outDetailId);

    int updateAuthorizationStatusById(@Param("resultList") List<String> resultList);
}