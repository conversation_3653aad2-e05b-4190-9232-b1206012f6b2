package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.SupplierContractDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompContractManagementMapper extends BaseDao<String, UompContractManagement> {

    int insertSelective(UompContractManagement record);

    int updateByPrimaryKey(UompContractManagement record);

    // 根据合同编号查询合同是否存在
    UompContractManagement selectInfoByContractCode(@Param("contractCode") String contractCode);

    UompContractManagement selectInfoById(@Param("id") String id);

    List<UompContractManagement> getRealtionContractList(QueryFilter queryFilter);

    List<UompContractManagement> getUnbindingContractList(@Param("projectManagementId") String projectManagementId, @Param("contractName") String contractName);

    List<BaseDTO> getIdAndNameList();

    List<UompContractManagement> bindingContractListBySystemId(QueryFilter queryFilter);

    List<UompContractManagement> unbindingContractList(QueryFilter queryFilter);

    List<UompContractManagement> selectAllByPartyBId(String partyBId);

    List<SupplierContractDTO> getContractTop5();

    List<UompContractManagement> getContractNoticeList(@Param("day")String day);
}