package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.dto.UompBlackDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord;
import cn.gwssi.ecloud.staffpool.core.dao.UompHistoryRecordMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompHistoryRecordService;

import java.util.List;

@Service
public class UompHistoryRecordServiceImpl extends BaseManager<String, UompHistoryRecord> implements UompHistoryRecordService{

    @Resource
    private UompHistoryRecordMapper uompHistoryRecordMapper;

    @Override
    public int insertSelective(UompHistoryRecord record) {
        return uompHistoryRecordMapper.insertSelective(record);
    }

    @Override
    public List<UompBlackDto> getBlackHistoryList(QueryFilter queryFilter) {
        return uompHistoryRecordMapper.getBlackHistoryList(queryFilter);
    }
}
