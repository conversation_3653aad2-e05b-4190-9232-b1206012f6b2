package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "供应商列表-对话框信息")
public class UompSupplierManagementListDialog{

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    @ApiModelProperty(value = "社会代码")
    private String creditCode;
    @ApiModelProperty(value = "简称")
    private String shortName;
    @ApiModelProperty(value = "联系电话")
    private String tel;
    @ApiModelProperty(value = "负责人")
    private String respName;
    @ApiModelProperty(value = "负责人电话")
    private String respTel;
    @ApiModelProperty(value = "类型")
    private String supplierType;
    @ApiModelProperty(value = "状态")
    private String supplierStatus;
    @ApiModelProperty(value = "注册地址")
    private String registerAddress;
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;
    @ApiModelProperty(value = "备注")
    private String remark;
}