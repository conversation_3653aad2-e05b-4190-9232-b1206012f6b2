package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitAdmissionRelation;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UompExitAdmissionRelationMapper extends BaseDao<String, UompExitAdmissionRelation> {

    int insertSelective(UompExitAdmissionRelation record);

    String selectExitApplyIdByApplyPedrsonId(String applyPersonId);

    List<String> selectApplyPersonIdByExitId(String exitId);
}