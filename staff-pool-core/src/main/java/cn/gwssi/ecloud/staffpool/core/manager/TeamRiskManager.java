package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskVO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface TeamRiskManager extends Manager<String, TeamRisk> {

    List<TeamRiskVO> queryRiskList(QueryFilter queryFilter);

    void createRiskHis(TeamRisk teamRisk);

    List<TeamRisk> queryRiskHisList(QueryFilter queryFilter);
}
