package cn.gwssi.ecloud.staffpool.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(value = "应用系统模块合同信息")
@Data
public class UompContractManagementSystemDTO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "编码")
    private String contractCode;

    @ApiModelProperty(value = "名称")
    private String contractName;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signingDate;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "甲方名称")
    private String partyAName;

    @ApiModelProperty(value = "甲方负责人名称")
    private String partyAPrincipalName;

    @ApiModelProperty(value = "乙方名称")
    private String partyBName;

    @ApiModelProperty(value = "乙方负责人名称")
    private String partyBPrincipalName;

    @ApiModelProperty(value = "质保/维保开始日")
    private Date qualityBeginDay;

    @ApiModelProperty(value = "质保/维保结束日")
    private Date qualityEndDay;
}