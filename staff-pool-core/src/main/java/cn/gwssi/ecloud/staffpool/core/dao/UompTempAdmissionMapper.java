package cn.gwssi.ecloud.staffpool.core.dao;
import cn.gwssi.ecloud.staffpool.dto.*;
import org.apache.ibatis.annotations.Param;
import cn.gwssi.ecloud.staffpool.api.model.TempApplyVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface UompTempAdmissionMapper  extends BaseDao<String, UompTempAdmission> {

    int insertSelective(UompTempAdmission uompTempAdmission);

    int updateByPersonNameAndCard(UompTempAdmission record);

    Integer countByRealVisitTime(String time);

    List<TempApplyDTO> selectAllBySelective(TempApplyVO tempApplyVO);

    InfoByNameDTO selectInfoByName(String personName);

    Integer coutByPersonCard(String personCard);

    UompTempAdmissionInfoDTO selectAllById(String id);

    UompTempAdmission selectPersonById(String id);

    Integer updateBlacklistAndBlacklistReasonByPersonNameAndPersonCard(@Param(value ="reason")String reason, @Param(value ="personName")String personName, @Param(value ="personCard")String personCard);

    List<TempApplyDTO> selectAllByPersonCardOrPersonName(@Param(value ="personCard") String personCard, @Param(value ="personName") String personName, @Param(value ="createBy") String createBy);

    TempApplyDTO selectPersonCardAndPersonNameById(String id);

    List<TempApplyDTO> selectAllBySelectiveExport(TempApplyVO tempApplyVO);

    String selectIdByAll(UompTempAdmissionDTO uompTempAdmission);

    List<PercentageDTO> countByRealVisitTimeGroupByRealVisitTime(@Param("realVisitTimeBegin") String realVisitTimeBegin, @Param("realVisitTimeEnd") String realVisitTimeEnd);

    Integer countByrealVisitTime(@Param("realVisitTimeStart") String realVisitTimeStart, @Param("realVisitTimeEnd") String realVisitTimeEnd);
}