package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAbroad;
import cn.gwssi.ecloud.staffpool.dto.UompPersonAbroadDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonAbroadMapper extends BaseDao<String, UompPersonAbroad> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonAbroad record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonAbroad record);

    int updateByPersonId(UompPersonAbroad record);

    List<UompPersonAbroadDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}