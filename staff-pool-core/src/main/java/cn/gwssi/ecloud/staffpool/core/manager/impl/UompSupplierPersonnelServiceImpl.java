package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompSupplierPersonnelMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierPersonnel;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierPersonnelService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class UompSupplierPersonnelServiceImpl extends BaseManager<String, UompSupplierPersonnel> implements UompSupplierPersonnelService {

    @Autowired
    private UompSupplierPersonnelMapper uompSupplierPersonnelMapper;

    @Override
    public int updateById(UompSupplierPersonnel supplierPersonnel) {
        return uompSupplierPersonnelMapper.updateById(supplierPersonnel);
    }

    @Override
    public int insertSelective(UompSupplierPersonnel record) {
        return uompSupplierPersonnelMapper.insertSelective(record);
    }

    @Override
    public int deleteById(String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierPersonnel personnel = new UompSupplierPersonnel();
        personnel.setId(id);
        personnel.setDelFlag("1");
        personnel.setUpdateOrgId(user.getOrgId());
        personnel.setUpdateTime(new Date());
        personnel.setUpdateBy(user.getUserId());
        return uompSupplierPersonnelMapper.updateById(personnel);
    }

}
