package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson;
import cn.gwssi.ecloud.staffpool.dto.TrainingDTO;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompTrainingRecordPersonService extends Manager<String, UompTrainingRecordPerson> {
    int insertSelective(UompTrainingRecordPerson record);

    int updateByPrimaryKeySelective(UompTrainingRecordPerson record);

    List<TrainingDTO> trainingList();

    void deleteByRecordId(String id);
}
