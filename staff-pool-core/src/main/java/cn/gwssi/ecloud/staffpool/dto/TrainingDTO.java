package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="人员培训记录响应类")
@Data
public class TrainingDTO implements Serializable {

    @ApiModelProperty(value="培训名称")
    private String trainingName;
    @ApiModelProperty(value="培训时间")
    private String trainingTime;
    @ApiModelProperty(value="签到人数")
    private String signInNum;
}
