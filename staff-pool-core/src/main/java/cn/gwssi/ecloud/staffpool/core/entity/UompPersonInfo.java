package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.Date;


public class UompPersonInfo extends BaseModel {
    @ApiModelProperty(value = "姓名")
    @NotEmpty(message = "姓名不能为空")
    private String personName;
    @ApiModelProperty(value = "身份证号")
    @NotEmpty(message = "身份证号不能为空")
    @Sensitive(prefixLen = 3,suffixLen = 3,actualLen = 18)
    private String personCard;
    @ApiModelProperty(value = "性别")
    @NotEmpty(message = "性别不能为空")
    private String personSex;
    @ApiModelProperty(value = "出生年月")
    @NotEmpty(message = "出生年月不能为空")
    private String personBirthday;
    @ApiModelProperty(value = "联系电话")
    @NotEmpty(message = "联系电话不能为空")
    private String tel;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "居住地址")
    @NotEmpty(message = "居住地址不能为空")
    private String address;
    @ApiModelProperty(value = "学历")
    @NotEmpty(message = "学历不能为空")
    private String education;
    @ApiModelProperty(value = "工作职位")
    @NotEmpty(message = "工作职位不能为空")
    private String post;
    @ApiModelProperty(value = "专业")
    private String major;
    @ApiModelProperty(value = "就职公司")
    @NotEmpty(message = "就职公司不能为空")
    private String workingCompany;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "流程实例id")
    private String instId;
    @ApiModelProperty(value = "黑名单标识 0非黑名单 1黑名单")
    private String blacklist;
    @ApiModelProperty(value = "加入黑名单原因")
    private String blacklistReason;
    @ApiModelProperty(value = "入职时间")
    @NotEmpty(message = "入职时间不能为空")
    private String entryDate;
    @ApiModelProperty(value = "附件信息")
    private String fileInfo;
    @ApiModelProperty(value = "初审状态 0暂存 1审核中 2审核通过 3审核不通过")
    private String trialStatus;
    @ApiModelProperty(value = "背景调查状态 0待审核 1合格 2不合格")
    private String backgroundStatus;
    @ApiModelProperty(value = "证件类型（冗余字段）")
    private String idType;
    @ApiModelProperty(value = "创建人机构id")
    private String createOrgId;
    @ApiModelProperty(value = "更新人机构id")
    private String updateOrgId;
    @ApiModelProperty(value = "删除标识 0正常 1删除")
    private String delFlag;
    @ApiModelProperty(value = "是否分配账号 0未分配 1已分配")
    private String isAccount;
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "服务商id")
    @NotEmpty(message = "就职公司id不能为空")
    private String workingCompanyId;
    @ApiModelProperty(value = "技能方向")
    @NotEmpty(message = "技能方向不能为空")
    private String technicalDirection;
    @ApiModelProperty(value = "户口所在地")
    private String regPermanentResidence;
    @ApiModelProperty(value = "国籍")
    @NotEmpty(message = "国籍不能为空")
    private String nationality;
    @ApiModelProperty(value = "政治面貌")
    @NotEmpty(message = "政治面貌不能为空")
    private String politicsStatus;
    @ApiModelProperty(value = "民族")
    @NotEmpty(message = "民族不能为空")
    private String nation;
    @ApiModelProperty(value = "户口所在地-省")
    private String regProvince;
    @ApiModelProperty(value = "户口所在地-市")
    private String regCity;
    @ApiModelProperty(value = "户口所在地-区/县")
    private String regRegion;
    @ApiModelProperty(value = "审核日期")
    private Date auditDate;
    @ApiModelProperty(value = "运维组织Id")
    @NotEmpty(message = "运维组织Id不能为空")
    private String orgGroupId;
    @ApiModelProperty(value = "运维组织")
    @NotEmpty(message = "运维组织不能为空")
    private String orgGroupName;
    @ApiModelProperty(value = "是否更新中（1是 0 否）")
    private String updating;
    @ApiModelProperty(value = "系统用户Id")
    private String orgUserId;
    @ApiModelProperty(value = "驻场服务状态")
    private String entryStatus;
    @ApiModelProperty(value = "是否离职")
    private String dimission;

    @ApiModelProperty(value = "身份证号前辍")
    private String personCardPrefix;

    @ApiModelProperty(value = "身份证号后辍")
    private String personCardSuffix;

    @ApiModelProperty(value = "身份证号加密串")
    private String personCardHash;

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    private static final long serialVersionUID = 1L;

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonCard() {
        return personCard;
    }

    public void setPersonCard(String personCard) {
        this.personCard = personCard;
    }

    public String getPersonSex() {
        return personSex;
    }

    public void setPersonSex(String personSex) {
        this.personSex = personSex;
    }

    public String getPersonBirthday() {
        return personBirthday;
    }

    public void setPersonBirthday(String personBirthday) {
        this.personBirthday = personBirthday;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getWorkingCompany() {
        return workingCompany;
    }

    public void setWorkingCompany(String workingCompany) {
        this.workingCompany = workingCompany;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(String blacklist) {
        this.blacklist = blacklist;
    }

    public String getBlacklistReason() {
        return blacklistReason;
    }

    public void setBlacklistReason(String blacklistReason) {
        this.blacklistReason = blacklistReason;
    }

    public String getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(String entryDate) {
        this.entryDate = entryDate;
    }

    public String getFileInfo() {
        return fileInfo;
    }

    public void setFileInfo(String fileInfo) {
        this.fileInfo = fileInfo;
    }

    public String getTrialStatus() {
        return trialStatus;
    }

    public void setTrialStatus(String trialStatus) {
        this.trialStatus = trialStatus;
    }

    public String getBackgroundStatus() {
        return backgroundStatus;
    }

    public void setBackgroundStatus(String backgroundStatus) {
        this.backgroundStatus = backgroundStatus;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getIsAccount() {
        return isAccount;
    }

    public void setIsAccount(String isAccount) {
        this.isAccount = isAccount;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getWorkingCompanyId() {
        return workingCompanyId;
    }

    public void setWorkingCompanyId(String workingCompanyId) {
        this.workingCompanyId = workingCompanyId;
    }

    public String getTechnicalDirection() {
        return technicalDirection;
    }

    public void setTechnicalDirection(String technicalDirection) {
        this.technicalDirection = technicalDirection;
    }

    public String getRegPermanentResidence() {
        return regPermanentResidence;
    }

    public void setRegPermanentResidence(String regPermanentResidence) {
        this.regPermanentResidence = regPermanentResidence;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getPoliticsStatus() {
        return politicsStatus;
    }

    public void setPoliticsStatus(String politicsStatus) {
        this.politicsStatus = politicsStatus;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getRegProvince() {
        return regProvince;
    }

    public void setRegProvince(String regProvince) {
        this.regProvince = regProvince;
    }

    public String getRegCity() {
        return regCity;
    }

    public void setRegCity(String regCity) {
        this.regCity = regCity;
    }

    public String getRegRegion() {
        return regRegion;
    }

    public void setRegRegion(String regRegion) {
        this.regRegion = regRegion;
    }

    public String getOrgGroupId() {
        return orgGroupId;
    }

    public void setOrgGroupId(String orgGroupId) {
        this.orgGroupId = orgGroupId;
    }

    public String getOrgGroupName() {
        return orgGroupName;
    }

    public void setOrgGroupName(String orgGroupName) {
        this.orgGroupName = orgGroupName;
    }

    public String getUpdating() {
        return updating;
    }

    public void setUpdating(String updating) {
        this.updating = updating;
    }

    public String getOrgUserId() {
        return orgUserId;
    }

    public void setOrgUserId(String orgUserId) {
        this.orgUserId = orgUserId;
    }

    public String getEntryStatus() {
        return entryStatus;
    }

    public void setEntryStatus(String entryStatus) {
        this.entryStatus = entryStatus;
    }

    public String getDimission() {
        return dimission;
    }

    public void setDimission(String dimission) {
        this.dimission = dimission;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getPersonCardPrefix() {
        return personCardPrefix;
    }

    public void setPersonCardPrefix(String personCardPrefix) {
        this.personCardPrefix = personCardPrefix;
    }

    public String getPersonCardSuffix() {
        return personCardSuffix;
    }

    public void setPersonCardSuffix(String personCardSuffix) {
        this.personCardSuffix = personCardSuffix;
    }

    public String getPersonCardHash() {
        return personCardHash;
    }

    public void setPersonCardHash(String personCardHash) {
        this.personCardHash = personCardHash;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", personName=").append(personName);
        sb.append(", personCard=").append(personCard);
        sb.append(", personSex=").append(personSex);
        sb.append(", personBirthday=").append(personBirthday);
        sb.append(", tel=").append(tel);
        sb.append(", address=").append(address);
        sb.append(", education=").append(education);
        sb.append(", post=").append(post);
        sb.append(", major=").append(major);
        sb.append(", workingCompany=").append(workingCompany);
        sb.append(", remark=").append(remark);
        sb.append(", instId=").append(instId);
        sb.append(", blacklist=").append(blacklist);
        sb.append(", blacklistReason=").append(blacklistReason);
        sb.append(", entryDate=").append(entryDate);
        sb.append(", fileInfo=").append(fileInfo);
        sb.append(", trialStatus=").append(trialStatus);
        sb.append(", backgroundStatus=").append(backgroundStatus);
        sb.append(", idType=").append(idType);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", createOrgId=").append(createOrgId);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateOrgId=").append(updateOrgId);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", isAccount=").append(isAccount);
        sb.append(", account=").append(account);
        sb.append(", workingCompanyId=").append(workingCompanyId);
        sb.append(", technicalDirection=").append(technicalDirection);
        sb.append(", regPermanentResidence=").append(regPermanentResidence);
        sb.append(", nationality=").append(nationality);
        sb.append(", politicsStatus=").append(politicsStatus);
        sb.append(", nation=").append(nation);
        sb.append(", regProvince=").append(regProvince);
        sb.append(", regCity=").append(regCity);
        sb.append(", regRegion=").append(regRegion);
        sb.append(", orgGroupId=").append(orgGroupId);
        sb.append(", orgGroupName=").append(orgGroupName);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompPersonInfo other = (UompPersonInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getPersonName() == null ? other.getPersonName() == null : this.getPersonName().equals(other.getPersonName()))
                && (this.getPersonCard() == null ? other.getPersonCard() == null : this.getPersonCard().equals(other.getPersonCard()))
                && (this.getPersonSex() == null ? other.getPersonSex() == null : this.getPersonSex().equals(other.getPersonSex()))
                && (this.getPersonBirthday() == null ? other.getPersonBirthday() == null : this.getPersonBirthday().equals(other.getPersonBirthday()))
                && (this.getTel() == null ? other.getTel() == null : this.getTel().equals(other.getTel()))
                && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
                && (this.getEducation() == null ? other.getEducation() == null : this.getEducation().equals(other.getEducation()))
                && (this.getPost() == null ? other.getPost() == null : this.getPost().equals(other.getPost()))
                && (this.getMajor() == null ? other.getMajor() == null : this.getMajor().equals(other.getMajor()))
                && (this.getWorkingCompany() == null ? other.getWorkingCompany() == null : this.getWorkingCompany().equals(other.getWorkingCompany()))
                && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
                && (this.getInstId() == null ? other.getInstId() == null : this.getInstId().equals(other.getInstId()))
                && (this.getBlacklist() == null ? other.getBlacklist() == null : this.getBlacklist().equals(other.getBlacklist()))
                && (this.getBlacklistReason() == null ? other.getBlacklistReason() == null : this.getBlacklistReason().equals(other.getBlacklistReason()))
                && (this.getEntryDate() == null ? other.getEntryDate() == null : this.getEntryDate().equals(other.getEntryDate()))
                && (this.getFileInfo() == null ? other.getFileInfo() == null : this.getFileInfo().equals(other.getFileInfo()))
                && (this.getTrialStatus() == null ? other.getTrialStatus() == null : this.getTrialStatus().equals(other.getTrialStatus()))
                && (this.getBackgroundStatus() == null ? other.getBackgroundStatus() == null : this.getBackgroundStatus().equals(other.getBackgroundStatus()))
                && (this.getIdType() == null ? other.getIdType() == null : this.getIdType().equals(other.getIdType()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateOrgId() == null ? other.getCreateOrgId() == null : this.getCreateOrgId().equals(other.getCreateOrgId()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getUpdateOrgId() == null ? other.getUpdateOrgId() == null : this.getUpdateOrgId().equals(other.getUpdateOrgId()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()))
                && (this.getIsAccount() == null ? other.getIsAccount() == null : this.getIsAccount().equals(other.getIsAccount()))
                && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
                && (this.getWorkingCompanyId() == null ? other.getWorkingCompanyId() == null : this.getWorkingCompanyId().equals(other.getWorkingCompanyId()))
                && (this.getTechnicalDirection() == null ? other.getTechnicalDirection() == null : this.getTechnicalDirection().equals(other.getTechnicalDirection()))
                && (this.getRegPermanentResidence() == null ? other.getRegPermanentResidence() == null : this.getRegPermanentResidence().equals(other.getRegPermanentResidence()))
                && (this.getNationality() == null ? other.getNationality() == null : this.getNationality().equals(other.getNationality()))
                && (this.getPoliticsStatus() == null ? other.getPoliticsStatus() == null : this.getPoliticsStatus().equals(other.getPoliticsStatus()))
                && (this.getNation() == null ? other.getNation() == null : this.getNation().equals(other.getNation()))
                && (this.getRegProvince() == null ? other.getRegProvince() == null : this.getRegProvince().equals(other.getRegProvince()))
                && (this.getRegCity() == null ? other.getRegCity() == null : this.getRegCity().equals(other.getRegCity()))
                && (this.getRegRegion() == null ? other.getRegRegion() == null : this.getRegRegion().equals(other.getRegRegion()))
                && (this.getOrgGroupId() == null ? other.getOrgGroupId() == null : this.getOrgGroupId().equals(other.getOrgGroupId()))
                && (this.getOrgGroupName() == null ? other.getOrgGroupName() == null : this.getOrgGroupName().equals(other.getOrgGroupName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPersonName() == null) ? 0 : getPersonName().hashCode());
        result = prime * result + ((getPersonCard() == null) ? 0 : getPersonCard().hashCode());
        result = prime * result + ((getPersonSex() == null) ? 0 : getPersonSex().hashCode());
        result = prime * result + ((getPersonBirthday() == null) ? 0 : getPersonBirthday().hashCode());
        result = prime * result + ((getTel() == null) ? 0 : getTel().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getEducation() == null) ? 0 : getEducation().hashCode());
        result = prime * result + ((getPost() == null) ? 0 : getPost().hashCode());
        result = prime * result + ((getMajor() == null) ? 0 : getMajor().hashCode());
        result = prime * result + ((getWorkingCompany() == null) ? 0 : getWorkingCompany().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getInstId() == null) ? 0 : getInstId().hashCode());
        result = prime * result + ((getBlacklist() == null) ? 0 : getBlacklist().hashCode());
        result = prime * result + ((getBlacklistReason() == null) ? 0 : getBlacklistReason().hashCode());
        result = prime * result + ((getEntryDate() == null) ? 0 : getEntryDate().hashCode());
        result = prime * result + ((getFileInfo() == null) ? 0 : getFileInfo().hashCode());
        result = prime * result + ((getTrialStatus() == null) ? 0 : getTrialStatus().hashCode());
        result = prime * result + ((getBackgroundStatus() == null) ? 0 : getBackgroundStatus().hashCode());
        result = prime * result + ((getIdType() == null) ? 0 : getIdType().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateOrgId() == null) ? 0 : getCreateOrgId().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateOrgId() == null) ? 0 : getUpdateOrgId().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        result = prime * result + ((getIsAccount() == null) ? 0 : getIsAccount().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getWorkingCompanyId() == null) ? 0 : getWorkingCompanyId().hashCode());
        result = prime * result + ((getTechnicalDirection() == null) ? 0 : getTechnicalDirection().hashCode());
        result = prime * result + ((getRegPermanentResidence() == null) ? 0 : getRegPermanentResidence().hashCode());
        result = prime * result + ((getNationality() == null) ? 0 : getNationality().hashCode());
        result = prime * result + ((getPoliticsStatus() == null) ? 0 : getPoliticsStatus().hashCode());
        result = prime * result + ((getNation() == null) ? 0 : getNation().hashCode());
        result = prime * result + ((getRegProvince() == null) ? 0 : getRegProvince().hashCode());
        result = prime * result + ((getRegCity() == null) ? 0 : getRegCity().hashCode());
        result = prime * result + ((getRegRegion() == null) ? 0 : getRegRegion().hashCode());
        result = prime * result + ((getOrgGroupId() == null) ? 0 : getOrgGroupId().hashCode());
        result = prime * result + ((getOrgGroupName() == null) ? 0 : getOrgGroupName().hashCode());
        return result;
    }
}
