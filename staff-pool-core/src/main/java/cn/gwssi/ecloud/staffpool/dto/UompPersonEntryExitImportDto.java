package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(description = "uomp_person_entry_exit")
@Data
public class UompPersonEntryExitImportDto {
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "出境/入境")
    private String entryExit;
    @ExcelProperty(value = "出入境日期")
    private String entryExitTime;
    @ExcelProperty(value = "证件名称")
    private String certificateName;
    @ExcelProperty(value = "证件号码")
    private String certificateNum;
    @ExcelProperty(value = "出入境口岸")
    private String entryExitPorts;
}