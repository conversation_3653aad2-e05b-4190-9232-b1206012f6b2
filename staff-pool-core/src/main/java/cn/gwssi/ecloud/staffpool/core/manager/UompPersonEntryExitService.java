package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEntryExit;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEntryExitDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonEntryExitService extends Manager<String, UompPersonEntryExit> {

    int insertSelective(UompPersonEntryExit record);

    int updateByPrimaryKeySelective(UompPersonEntryExit record);

    int updateByPersonId(UompPersonEntryExit record);

    List<UompPersonEntryExitDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
