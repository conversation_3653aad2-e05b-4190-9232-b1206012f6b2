package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 培训计划表
 */
@Data
@ApiModel(description="培训计划表")
public class UompTrainingPlan extends BaseModel {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 培训计划名称
    */
    @ApiModelProperty(value="培训计划名称")
    private String trainingPlanName;

    /**
    * 培训目的
    */
    @ApiModelProperty(value="培训目的")
    private String trainingPurpose;

    /**
    * 培训主题
    */
    @ApiModelProperty(value="培训主题")
    private String trainingTheme;

    /**
    * 培训资源
    */
    @ApiModelProperty(value="培训资源")
    private String trainingResource;

    /**
    * 培训对象
    */
    @ApiModelProperty(value="培训对象")
    private String trainingObject;

    /**
    * 培训方式
    */
    @ApiModelProperty(value="培训方式")
    private String trainingMode;

    /**
    * 考核方式
    */
    @ApiModelProperty(value="考核方式")
    private String evaluationMode;

    /**
    * 培训开始日期
    */
    @ApiModelProperty(value="培训开始日期")
    private Date trainingBeginTime;

    /**
    * 培训结束日期
    */
    @ApiModelProperty(value="培训结束日期")
    private Date trainingEndTime;

    /**
    * 培训内容
    */
    @ApiModelProperty(value="培训内容")
    private String trainingContent;

    /**
    * 诚信会议号
    */
    @ApiModelProperty(value="诚信会议号")
    private String onlineId;

    /**
    * 附件信息json
    */
    @ApiModelProperty(value="附件信息json")
    private String fileInfo;

    /**
    * 逻辑删除标记 0-有效 1-无效
    */
    @ApiModelProperty(value="逻辑删除标记 0-有效 1-无效")
    private String delFlag;

}
