package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import java.util.Date;

public class UompDesensitization extends BaseModel {

    private String desObjCode;

    private String desFieldCode;

    private String desRuleMode;

    private String desRuleJson;

    private String desRuleRegx;

    private String sensitiveWords;

    private String sensitiveReplaceWords;

    private String plaintextRoleJson;

    private String plaintextPostJson;

    private String plaintextUserJson;

    private String isCreater;

    private String isOperator;

    private String operatorMode;

    private String operatorScript;

    private String isEnable;

    private String isOverallEnable;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDesObjCode() {
        return desObjCode;
    }

    public void setDesObjCode(String desObjCode) {
        this.desObjCode = desObjCode;
    }

    public String getDesFieldCode() {
        return desFieldCode;
    }

    public void setDesFieldCode(String desFieldCode) {
        this.desFieldCode = desFieldCode;
    }

    public String getDesRuleMode() {
        return desRuleMode;
    }

    public void setDesRuleMode(String desRuleMode) {
        this.desRuleMode = desRuleMode;
    }

    public String getDesRuleJson() {
        return desRuleJson;
    }

    public void setDesRuleJson(String desRuleJson) {
        this.desRuleJson = desRuleJson;
    }

    public String getDesRuleRegx() {
        return desRuleRegx;
    }

    public void setDesRuleRegx(String desRuleRegx) {
        this.desRuleRegx = desRuleRegx;
    }

    public String getSensitiveWords() {
        return sensitiveWords;
    }

    public void setSensitiveWords(String sensitiveWords) {
        this.sensitiveWords = sensitiveWords;
    }

    public String getSensitiveReplaceWords() {
        return sensitiveReplaceWords;
    }

    public void setSensitiveReplaceWords(String sensitiveReplaceWords) {
        this.sensitiveReplaceWords = sensitiveReplaceWords;
    }

    public String getPlaintextRoleJson() {
        return plaintextRoleJson;
    }

    public void setPlaintextRoleJson(String plaintextRoleJson) {
        this.plaintextRoleJson = plaintextRoleJson;
    }

    public String getPlaintextPostJson() {
        return plaintextPostJson;
    }

    public void setPlaintextPostJson(String plaintextPostJson) {
        this.plaintextPostJson = plaintextPostJson;
    }

    public String getPlaintextUserJson() {
        return plaintextUserJson;
    }

    public void setPlaintextUserJson(String plaintextUserJson) {
        this.plaintextUserJson = plaintextUserJson;
    }

    public String getIsCreater() {
        return isCreater;
    }

    public void setIsCreater(String isCreater) {
        this.isCreater = isCreater;
    }

    public String getIsOperator() {
        return isOperator;
    }

    public void setIsOperator(String isOperator) {
        this.isOperator = isOperator;
    }

    public String getOperatorMode() {
        return operatorMode;
    }

    public void setOperatorMode(String operatorMode) {
        this.operatorMode = operatorMode;
    }

    public String getOperatorScript() {
        return operatorScript;
    }

    public void setOperatorScript(String operatorScript) {
        this.operatorScript = operatorScript;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getIsOverallEnable() {
        return isOverallEnable;
    }

    public void setIsOverallEnable(String isOverallEnable) {
        this.isOverallEnable = isOverallEnable;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", desObjCode=").append(desObjCode);
        sb.append(", desFieldCode=").append(desFieldCode);
        sb.append(", desRuleMode=").append(desRuleMode);
        sb.append(", desRuleJson=").append(desRuleJson);
        sb.append(", desRuleRegx=").append(desRuleRegx);
        sb.append(", sensitiveWords=").append(sensitiveWords);
        sb.append(", sensitiveReplaceWords=").append(sensitiveReplaceWords);
        sb.append(", plaintextRoleJson=").append(plaintextRoleJson);
        sb.append(", plaintextPostJson=").append(plaintextPostJson);
        sb.append(", plaintextUserJson=").append(plaintextUserJson);
        sb.append(", isCreater=").append(isCreater);
        sb.append(", isOperator=").append(isOperator);
        sb.append(", operatorMode=").append(operatorMode);
        sb.append(", operatorScript=").append(operatorScript);
        sb.append(", isEnable=").append(isEnable);
        sb.append(", isOverallEnable=").append(isOverallEnable);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompDesensitization other = (UompDesensitization) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDesObjCode() == null ? other.getDesObjCode() == null : this.getDesObjCode().equals(other.getDesObjCode()))
            && (this.getDesFieldCode() == null ? other.getDesFieldCode() == null : this.getDesFieldCode().equals(other.getDesFieldCode()))
            && (this.getDesRuleMode() == null ? other.getDesRuleMode() == null : this.getDesRuleMode().equals(other.getDesRuleMode()))
            && (this.getDesRuleJson() == null ? other.getDesRuleJson() == null : this.getDesRuleJson().equals(other.getDesRuleJson()))
            && (this.getDesRuleRegx() == null ? other.getDesRuleRegx() == null : this.getDesRuleRegx().equals(other.getDesRuleRegx()))
            && (this.getSensitiveWords() == null ? other.getSensitiveWords() == null : this.getSensitiveWords().equals(other.getSensitiveWords()))
            && (this.getSensitiveReplaceWords() == null ? other.getSensitiveReplaceWords() == null : this.getSensitiveReplaceWords().equals(other.getSensitiveReplaceWords()))
            && (this.getPlaintextRoleJson() == null ? other.getPlaintextRoleJson() == null : this.getPlaintextRoleJson().equals(other.getPlaintextRoleJson()))
            && (this.getPlaintextPostJson() == null ? other.getPlaintextPostJson() == null : this.getPlaintextPostJson().equals(other.getPlaintextPostJson()))
            && (this.getPlaintextUserJson() == null ? other.getPlaintextUserJson() == null : this.getPlaintextUserJson().equals(other.getPlaintextUserJson()))
            && (this.getIsCreater() == null ? other.getIsCreater() == null : this.getIsCreater().equals(other.getIsCreater()))
            && (this.getIsOperator() == null ? other.getIsOperator() == null : this.getIsOperator().equals(other.getIsOperator()))
            && (this.getOperatorMode() == null ? other.getOperatorMode() == null : this.getOperatorMode().equals(other.getOperatorMode()))
            && (this.getOperatorScript() == null ? other.getOperatorScript() == null : this.getOperatorScript().equals(other.getOperatorScript()))
            && (this.getIsEnable() == null ? other.getIsEnable() == null : this.getIsEnable().equals(other.getIsEnable()))
            && (this.getIsOverallEnable() == null ? other.getIsOverallEnable() == null : this.getIsOverallEnable().equals(other.getIsOverallEnable()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDesObjCode() == null) ? 0 : getDesObjCode().hashCode());
        result = prime * result + ((getDesFieldCode() == null) ? 0 : getDesFieldCode().hashCode());
        result = prime * result + ((getDesRuleMode() == null) ? 0 : getDesRuleMode().hashCode());
        result = prime * result + ((getDesRuleJson() == null) ? 0 : getDesRuleJson().hashCode());
        result = prime * result + ((getDesRuleRegx() == null) ? 0 : getDesRuleRegx().hashCode());
        result = prime * result + ((getSensitiveWords() == null) ? 0 : getSensitiveWords().hashCode());
        result = prime * result + ((getSensitiveReplaceWords() == null) ? 0 : getSensitiveReplaceWords().hashCode());
        result = prime * result + ((getPlaintextRoleJson() == null) ? 0 : getPlaintextRoleJson().hashCode());
        result = prime * result + ((getPlaintextPostJson() == null) ? 0 : getPlaintextPostJson().hashCode());
        result = prime * result + ((getPlaintextUserJson() == null) ? 0 : getPlaintextUserJson().hashCode());
        result = prime * result + ((getIsCreater() == null) ? 0 : getIsCreater().hashCode());
        result = prime * result + ((getIsOperator() == null) ? 0 : getIsOperator().hashCode());
        result = prime * result + ((getOperatorMode() == null) ? 0 : getOperatorMode().hashCode());
        result = prime * result + ((getOperatorScript() == null) ? 0 : getOperatorScript().hashCode());
        result = prime * result + ((getIsEnable() == null) ? 0 : getIsEnable().hashCode());
        result = prime * result + ((getIsOverallEnable() == null) ? 0 : getIsOverallEnable().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}