package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompTeamRiskPositionMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTeamRiskPosition;
import cn.gwssi.ecloud.staffpool.core.manager.UompTeamRiskPositionManager;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 运维岗位职责Manager实现类
 */
@Service
public class UompTeamRiskPositionManagerImpl extends BaseManager<String, UompTeamRiskPosition> implements UompTeamRiskPositionManager {

    @Resource
    private UompTeamRiskPositionMapper uompTeamRiskPositionMapper;

}
