package cn.gwssi.ecloud.staffpool.base;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResponseData<T> {
    protected T data;
    private String msg;
    private String code;

    public static ResponseData create(Integer code, String message, Object data) {
        if (data == null) {
            data = new Object();
        }
        return new ResponseData(code, message, data);
    }


    public static ResponseData success() {
        return new ResponseData(ResponseCodeEnums.SUCCESS);
    }

    public static ResponseData success(Object data) {
        if (data == null) {
            data = new Object();
        }
        return new ResponseData(ResponseCodeEnums.SUCCESS, data);
    }

    public static ResponseData error(ResponseCodeEnums status, Object obj) {
        ResponseData response = new ResponseData(status);
        response.setData(obj);
        return response;
    }

    public static ResponseData error(ResponseCodeEnums status) {
        ResponseData response = new ResponseData(status);
        return response;
    }

    public static ResponseData error(String message) {
        return new ErrorResponseData(message);
    }

    public static ResponseData error(Integer code, String message) {
        return new ErrorResponseData(code, message);
    }

    public static ResponseData error(Integer code, String message, Object data) {
        return new ErrorResponseData(code, message, data);
    }


    public ResponseData(Integer code, String message) {
        this.code = String.valueOf(code);
        this.msg = message;
    }

    public ResponseData(Integer code, String message, T data) {
        this.code = String.valueOf(code);
        this.msg = message;
        this.data = data;
    }

    public ResponseData(ResponseCodeEnums returnStatus, T data) {
        this.code = String.valueOf(returnStatus.getValue());
        this.msg = returnStatus.getDesc();
        this.data = data;
    }

    private ResponseData(ResponseCodeEnums returnStatus) {
        this.code = String.valueOf(returnStatus.getValue());
        this.msg = returnStatus.getDesc();
    }
}