package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class OrgTeam extends BaseModel {
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Integer sn;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String desc;
}