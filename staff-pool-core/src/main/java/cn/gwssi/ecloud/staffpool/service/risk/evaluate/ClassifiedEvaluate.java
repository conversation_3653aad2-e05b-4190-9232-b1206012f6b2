package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import groovyjarjarpicocli.CommandLine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 涉密审查
 */
@Component
public class ClassifiedEvaluate extends RiskEvaluateChain{
    private static final Logger LOG = LoggerFactory.getLogger(ClassifiedEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        LOG.info("------用户{}计算涉密审查------",uompPersonInfoVo.getPersonName());

    }

    @Override
    public String alias() {
        return "classified";
    }
}
