package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import cn.hutool.core.collection.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 无犯罪证明缺失
 */
@Component
public class NoCrimeEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(NoCrimeEvaluate.class);


    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        LOG.info("------用户{}计算无犯罪证明缺失------",uompPersonInfoVo.getPersonName());
        if (CollectionUtil.isEmpty(uompPersonInfoVo.getNoCrimeList())){
            // TODO: 2025/6/25 插入
        }
    }

    @Override
    public String alias() {
        return "noCrime";
    }
}
