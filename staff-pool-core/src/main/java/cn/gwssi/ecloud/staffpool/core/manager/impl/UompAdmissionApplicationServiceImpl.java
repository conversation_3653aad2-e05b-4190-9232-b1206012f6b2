package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize;
import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.ProjectBySupplierVO;
import cn.gwssi.ecloud.staffpool.api.service.IDataDesensitization;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.*;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson;
import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompAdmissionApplicationService;
import cn.gwssi.ecloud.staffpool.core.manager.UompOrgGroupService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.*;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UompAdmissionApplicationServiceImpl extends BaseManager<String, UompAdmissionApplication> implements UompAdmissionApplicationService {

    @Resource
    private UompAdmissionApplicationMapper uompAdmissionApplicationMapper;
    @Resource
    private UompAdmissionPersonMapper uompAdmissionPersonMapper;
    @Resource
    private SysDataDictMapper sysDataDictMapper;
    @Resource
    private UompExitApplicationMapper uompExitApplicationMapper;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private DictUtil dictUtil;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompOrgGroupService uompOrgGroupService;
    @Resource
    private UompApplicationSystemManagementMapper uompApplicationSystemManagementMapper;

    @Resource
    private IDataDesensitization dataDesensitization;

    @Override
    public int insertSelective(UompAdmissionApplication record) {
        return uompAdmissionApplicationMapper.insertSelective(record);
    }

    @Override
    public ResponsePageData<PersonListDTO> getPersonList(PersonListQueryVO personListQueryVO) {
        ResponsePageData<PersonListDTO> pageResult = new ResponsePageData<>();

        // 所属运维组
        if (StringUtils.isNotBlank(personListQueryVO.getMaintenanceGroupId())) {
            personListQueryVO.setMaintenanceGroupIdList(Arrays.asList(personListQueryVO.getMaintenanceGroupId().split(",")));
        }
        // 就职公司
        if (StringUtils.isNotBlank(personListQueryVO.getWorkingCompany())) {
            personListQueryVO.setWorkingCompanyList(Arrays.asList(personListQueryVO.getWorkingCompany().split(",")));
        }
        // 入场日期时间段
        if (StringUtils.isNotBlank(personListQueryVO.getInTime())) {
            List<String> inTime = Arrays.asList(personListQueryVO.getInTime().split(","));
            String inTimeBegin = inTime.get(0);
            String inTimeEnd = inTime.get(1);
            if (!StringUtils.isEmpty(inTimeBegin)) {
                personListQueryVO.setInTimeBegin(inTimeBegin);
            }
            if (!StringUtils.isEmpty(inTimeEnd)) {
                personListQueryVO.setInTimeEnd(inTimeEnd);
            }
        }
        // 参与项目
        if (StringUtils.isNotBlank(personListQueryVO.getInvolvedProject())) {
            personListQueryVO.setInvolvedProjectList(Arrays.asList(personListQueryVO.getInvolvedProject().split(",")));
        }

        // 运维组织
        if (StringUtils.isNotBlank(personListQueryVO.getOrgGroupId())) {
            personListQueryVO.setOrgGroupIdList(uompOrgGroupService.getByPath(personListQueryVO.getOrgGroupId()));
        }

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.isEmpty()) {
            return pageResult;
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                personListQueryVO.setIfSupplier("1");
                personListQueryVO.setSupplierId(workCompany.get(0).getWorkingCompanyId());
            }
        } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) {
            personListQueryVO.setCreatedBy(user.getUserId());
        } else {
            return pageResult;
        }

        personListQueryVO.setCreatedByStatus(user.getUserId());

        PageHelper.startPage(personListQueryVO.getPageNo(), personListQueryVO.getPageSize());
        List<PersonListDTO> dTOList = uompAdmissionPersonMapper.selectAllBySelectivePage(personListQueryVO);
        PageInfo<PersonListDTO> p = new PageInfo<>(dTOList);
        if (!CollectionUtils.isEmpty(dTOList)) {
            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：null,参数4：null(参数3，4只有在详情的时候才输入)）
            //该列表只有 手机号,身份证号 需要脱敏
            List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", null, null);

            //脱敏
            if (!CollectionUtils.isEmpty(ruleList)) {
                dTOList = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(dTOList))).toJSONString(), PersonListDTO.class);
            }
        }

        pageResult.setCode(200);
        pageResult.setMsg("success");
        pageResult.setTotal(p.getTotal());
        pageResult.setRows(dTOList);
        pageResult.setPage(personListQueryVO.getPageNo());
        pageResult.setPageSize(personListQueryVO.getPageSize());
        return pageResult;
    }

    @Override
    @AutoDesensitize()
    public EntryApplyDTO getEntryApplyById(String id) {
        EntryApplyDTO entryApplyDTO = uompAdmissionPersonMapper.selectAllById(id);

        //详情信息中字段二次处理
        if (entryApplyDTO != null) {
            String fileInfo = entryApplyDTO.getFileInfoJson();
            if (StringUtils.isNotEmpty(fileInfo)) {
                entryApplyDTO.setFileInfo(JSONObject.parseObject(fileInfo, List.class));
            }

            String postFileInfo = entryApplyDTO.getPostFileInfoJson();
            if (StringUtils.isNotEmpty(postFileInfo)) {
                entryApplyDTO.setPostFileInfo(JSONObject.parseObject(postFileInfo, List.class));
            }

            //职称
            String postKey = entryApplyDTO.getTechnicalPost();
            postKey = sysDataDictMapper.selectNameByPostKey(postKey);
            //为空的时候就该是原来的值
            if (StringUtils.isNotEmpty(postKey)) {
                entryApplyDTO.setTechnicalPost(postKey);
            }

            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：biz_id,参数4：create_by）
           /* List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", null, entryApplyDTO.getInstId(), entryApplyDTO.getCreateBy());
            //脱敏
            if (!CollectionUtils.isEmpty(ruleList)) {
                for (UompDesensitization rule : ruleList) {
                    //如果有关于手机号的脱敏规则则转换手机号
                    if (StringUtils.isNotEmpty(entryApplyDTO.getTel()) && "tel".equals(rule.getDesFieldCode())) {
                        entryApplyDTO.setTel(conversionUtil.conversion(rule, entryApplyDTO.getTel()));
                    }
                    //如果有关于身份证号的脱敏规则则转换身份证号
                    if (StringUtils.isNotEmpty(entryApplyDTO.getPersonCard()) && "personCard".equals(rule.getDesFieldCode())) {
                        entryApplyDTO.setPersonCard(conversionUtil.conversion(rule, entryApplyDTO.getPersonCard()));
                    }
                }
            }*/
        }

        return entryApplyDTO;
    }

    @Override
    public ResponsePageData<PersonInfoOverviewDTO> personInfoOverview(PersonListQueryVO personListQueryVO) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        ResponsePageData<PersonInfoOverviewDTO> pageResult = new ResponsePageData<>();

        // 根据申请id查询用户信息
        String personCard = uompAdmissionPersonMapper.selectPersonCardByApplyId(personListQueryVO.getId());

        PageHelper.startPage(personListQueryVO.getPageNo(), personListQueryVO.getPageSize());
        List<PersonInfoOverviewDTO> dTOList = uompAdmissionPersonMapper.selectAllByPersonCard(personCard, user.getUserId());
        if (!CollectionUtils.isEmpty(dTOList)) {
            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：null,参数4：null(参数3，4只有在详情的时候才输入)）
            //该列表只有 手机号,身份证号 需要脱敏
//            List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", null, null);
//
//            //脱敏
//            if (!CollectionUtils.isEmpty(ruleList)) {
//                dTOList = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(dTOList))).toJSONString(), PersonInfoOverviewDTO.class);
//            }
            // 根据instId查询taskId和linkedId
            for (PersonInfoOverviewDTO info : dTOList) {
                // 申请状态：拒绝
                if ("3".equals(info.getApplyStatus())) {
                    String instId = info.getInstId();
                    //根据instId查出taskId和taskLinkedId
                    BaseInstDTO baseDTO = uompPersonInfoService.selectTaskIdByInstId(instId);
                    if (null != baseDTO) {
                        info.setTaskId(baseDTO.getTaskId());
                        info.setTaskLinkId(baseDTO.getLinkId());
                    }
                }
            }
        }

        PageInfo<PersonInfoOverviewDTO> p = new PageInfo<>(dTOList);

        pageResult.setCode(200);
        pageResult.setMsg("success");
        pageResult.setTotal(p.getTotal());
        pageResult.setRows(dTOList);
        pageResult.setPage(personListQueryVO.getPageNo());
        pageResult.setPageSize(personListQueryVO.getPageSize());
        return pageResult;
    }

    @Override
    public void updateStatus(String id, String status) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompAdmissionApplication uompAdmissionApplication = new UompAdmissionApplication();
        uompAdmissionApplication.setId(id);
        uompAdmissionApplication.setApplyStatus(status);
        uompAdmissionApplication.setUpdateBy(user.getUserId());
        uompAdmissionApplication.setUpdateTime(new Date());
        uompAdmissionApplication.setUpdateOrgId(user.getOrgId());
        uompAdmissionApplicationMapper.updateById(uompAdmissionApplication);
    }

    @Override
    public List<PercentageDTO> getPersonLocation() {
        //11-22 驻场分布，驻场一个地方的驻场多次也算一个人，驻场不同地方的，各算一次
        //且是当前驻场中的
        List<PercentageDTO> percentageDTOS = uompAdmissionPersonMapper.getPersonLocation();
        Integer total = uompAdmissionPersonMapper.countTotalPersonLocation();
        //百分比计算
        for (PercentageDTO pd : percentageDTOS) {
            pd.setName(StringUtils.isNotEmpty(pd.getName()) ? dictUtil.getNameByKey("UOMP_SERVICE_LOCATION", pd.getName()) : "未知");

            pd.setPercentage(new BigDecimal(pd.getNum()).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }

        return percentageDTOS;
    }

    @Override
    public ResponsePageData<ProjectBySupplierDTO> getProjectBySupplier(ProjectBySupplierVO projectBySupplier) {
        ResponsePageData<ProjectBySupplierDTO> pageResult = new ResponsePageData<>();
        PageHelper.startPage(projectBySupplier.getPageNo(), projectBySupplier.getPageSize());
        List<ProjectBySupplierDTO> projectBySupplierDTO = uompApplicationSystemManagementMapper.selectAllByRelationId(projectBySupplier.getSupplierId(), projectBySupplier.getProjectName());
        PageInfo<ProjectBySupplierDTO> p = new PageInfo<>(projectBySupplierDTO);
        pageResult.setCode(200);
        pageResult.setMsg("success");
        pageResult.setTotal(p.getTotal());
        pageResult.setRows(projectBySupplierDTO);
        pageResult.setPage(projectBySupplier.getPageNo());
        pageResult.setPageSize(projectBySupplier.getPageSize());
        return pageResult;
    }

    @Override
    public AdmissionPersonDTO admissionPerson() {
        AdmissionPersonDTO advert = new AdmissionPersonDTO();
        Date startTime = new Date();
        //11-22 人员去重。一年内驻场多次也算一个人
        // 驻场人员总数 查询所有当前为驻场状态的
        advert.setTotal(uompAdmissionPersonMapper.countTotalByApplyStatusNew(null, null, null, null));
        // 近一年驻场人员数
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        advert.setAdmissionTotal(uompAdmissionPersonMapper.countTotalByApplyStatusNew(sdf2.format(DateUtils.getAYearAgo(startTime)), sdf2.format(startTime), null, null));
        // 近一年离场人员数
        advert.setExitTotal(uompExitApplicationMapper.countByApplyStatusNew(sdf2.format(DateUtils.getAYearAgo(startTime)), sdf2.format(startTime)));

        // 驻场人员总数 查询所有当前为驻场状态的
//        advert.setTotal(uompAdmissionPersonMapper.countTotalByApplyStatus(null, null, null, null));
        // 近一年驻场人员数
//        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
//        advert.setAdmissionTotal(uompAdmissionPersonMapper.countTotalByApplyStatus(sdf2.format(DateUtils.getAYearAgo(startTime)), sdf2.format(startTime), null, null));
        // 近一年离场人员数
//        advert.setExitTotal(uompExitApplicationMapper.countByApplyStatus(sdf2.format(DateUtils.getAYearAgo(startTime)), sdf2.format(startTime)));

        return advert;
    }

    @Override
    @AutoDesensitize(processMethod = "decrypt")
    public List<PersonListDTO> export(PersonListQueryVO personListQueryVO) {
        List<PersonListDTO> personListDTOList = new ArrayList<>();
        // 所属运维组
        if (StringUtils.isNotBlank(personListQueryVO.getMaintenanceGroupId())) {
            personListQueryVO.setMaintenanceGroupIdList(Arrays.asList(personListQueryVO.getMaintenanceGroupId().split(",")));
        }
        // 就职公司
        if (StringUtils.isNotBlank(personListQueryVO.getWorkingCompany())) {
            personListQueryVO.setWorkingCompanyList(Arrays.asList(personListQueryVO.getWorkingCompany().split(",")));
        }
        // 入场日期时间段
        if (StringUtils.isNotBlank(personListQueryVO.getInTime())) {
            List<String> inTime = Arrays.asList(personListQueryVO.getInTime().split(","));
            String inTimeBegin = inTime.get(0);
            String inTimeEnd = inTime.get(1);
            if (!StringUtils.isEmpty(inTimeBegin)) {
                personListQueryVO.setInTimeBegin(inTimeBegin);
            }
            if (!StringUtils.isEmpty(inTimeEnd)) {
                personListQueryVO.setInTimeEnd(inTimeEnd);
            }
        }
        // 参与项目
        if (StringUtils.isNotBlank(personListQueryVO.getInvolvedProject())) {
            personListQueryVO.setInvolvedProjectList(Arrays.asList(personListQueryVO.getInvolvedProject().split(",")));
        }

        // 运维组织
        if (StringUtils.isNotBlank(personListQueryVO.getOrgGroupId())) {
            personListQueryVO.setOrgGroupIdList(uompOrgGroupService.getByPath(personListQueryVO.getOrgGroupId()));
        }

        List<String> personCardArrayList = new ArrayList<>();

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.isEmpty()) {
            return personListDTOList;
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains(" ")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> uompPersonInfoList = uompPersonInfoService.query(queryFilter1);
            if (uompPersonInfoList != null && uompPersonInfoList.size() > 0) {
                personListQueryVO.setIfSupplier("1");
                personListQueryVO.setSupplierId(uompPersonInfoList.get(0).getWorkingCompanyId());
            }
        } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) {
            personListQueryVO.setCreatedBy(user.getUserId());
        } else {
            return personListDTOList;
        }

        personListQueryVO.setCreatedByStatus(user.getUserId());

        //11-20 修改为查询的方法sql
        List<PersonListDTO> dTOList = uompAdmissionPersonMapper.selectAllBySelectivePage(personListQueryVO);

        personCardArrayList = dTOList.stream().map(s -> s.getPersonCard()).collect(Collectors.toList());

        personListDTOList = uompAdmissionPersonMapper.selectAllByPersonCardIn(personCardArrayList);

        //11-20 使用身份证号做筛选条件会产生多个UOMP_ADMISSION_PERSON的数据，所以改为使用UOMP_ADMISSION_PERSON 表数据id做为条件
//        List<String> admissionIds = null;
//        admissionIds = dTOList.stream().map(PersonListDTO::getId).collect(Collectors.toList());
//        personListDTOList = uompAdmissionPersonMapper.selectAllByPersonAdmissionIds(admissionIds);


        if (!CollectionUtils.isEmpty(personListDTOList)) {
            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：null,参数4：null(参数3，4只有在详情的时候才输入)）
            //该列表只有 手机号,身份证号 需要脱敏
            List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", null, null);

            if (!CollectionUtils.isEmpty(ruleList)) {
                personListDTOList = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(personListDTOList))).toJSONString(), PersonListDTO.class);
            }
        }
        return personListDTOList;
    }

    @Override
    public String decryptEntryInfo(String id, String attr) {
        UompAdmissionPerson uompAdmissionPerson = uompAdmissionPersonMapper.get(id);
        if (uompAdmissionPerson != null){
            String original = ReflectUtil.getFieldValue(uompAdmissionPerson,attr).toString();
            return dataDesensitization.decrypt(original);
        }
        return null;
    }
}
