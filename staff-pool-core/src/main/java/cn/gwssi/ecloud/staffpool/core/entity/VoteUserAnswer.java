package cn.gwssi.ecloud.staffpool.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@ApiModel(description="vote_user_answer")
@Data
public class VoteUserAnswer implements Serializable {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    private String voteId;

    @ApiModelProperty(value="")
    private String userId;

    @ApiModelProperty(value="")
    private String answer;

    @ApiModelProperty(value="")
    private String answerStatus;

    @ApiModelProperty(value="")
    private String attachStatus;

    @ApiModelProperty(value="")
    private String title;

    @ApiModelProperty(value="")
    private String sjOrgId;

    @ApiModelProperty(value="")
    private Date createTime;

    @ApiModelProperty(value="")
    private Date updateTime;

    @ApiModelProperty(value="")
    private String reviewResult;

    private static final long serialVersionUID = 1L;
}