package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.UompContractFileSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractResourceSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractFile;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractResource;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierManagementService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.SupplierContractDTO;
import cn.gwssi.ecloud.staffpool.dto.UompContractManagementListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(description = "合同管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/contract")
public class UompContractController extends BaseController<UompContractManagement> {

    @Resource
    private UompContractManagementService uompContractManagementService;
    @Resource
    private UompSupplierManagementService uompSupplierManagementService;


    @ApiOperation(value = "合同管理查询列表")
    @PostMapping(value = "/getContractList")
    public PageResult<UompContractManagementListDTO> getContractList(
            @Validated @RequestBody UompContractManagementQueryVO uompContractManagementQueryVO) {
        return uompContractManagementService.getContractList(uompContractManagementQueryVO);
    }

    @ApiOperation(value = "合同管理保存")
    @PostMapping(value = "/save")
    public ResultMsg<String> save(@Validated @RequestBody UompContractManagementSave uompContractManagementSave) {
        return getSuccessResult(uompContractManagementService.save(uompContractManagementSave));
    }

    @ApiOperation(value = "删除合同管理")
    @RequestMapping(value = "/deleteContract", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg deleteContract(@RequestParam("id") @ApiParam(value = "主键id") String id) {
        uompContractManagementService.deleteContract(id);
        return getSuccessResult();
    }

    @ApiOperation(value = "合同管理文件信息保存")
    @PostMapping(value = "/saveFile")
    public ResultMsg saveFile(@Validated @RequestBody UompContractFileSave uompContractFileSave) {
        uompContractManagementService.saveFile(uompContractFileSave);
        return getSuccessResult();
    }

    @ApiOperation(value = "合同管理附件列表查询")
    @RequestMapping(value = "/getFileList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompContractFile> getFileList(HttpServletRequest request,
                                                    @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                    @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                    @RequestParam(value = "fileName", required = false) @ApiParam(value = "文件名称") String fileName,
                                                    @RequestParam(value = "contractManagementId") @ApiParam(value = "合同管理id") String contractManagementId) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 逻辑删除标识 0未删除 1已删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 合同id
        queryFilter.addFilter("CONTRACT_MANAGEMENT_ID", contractManagementId, QueryOP.EQUAL);
        // 文件名称
        if (StringUtils.isNotEmpty(fileName)) {
            queryFilter.addFilter("FILE_NAME", fileName, QueryOP.LIKE);
        }
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompContractManagementService.getFileList(queryFilter);
    }

    @ApiOperation(value = "合同管理附件删除")
    @RequestMapping(value = "/deleteFile", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg deleteFile(@RequestParam("id") @ApiParam(value = "文件主键id") String id) {
        uompContractManagementService.deleteFile(id);
        return getSuccessResult();
    }

    @ApiOperation(value = "合同管理关联资产列表查询")
    @RequestMapping(value = "/getPropertyList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPropertyDTO> getPropertyList(HttpServletRequest request,
                                                       @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                       @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                       @RequestParam(value = "contractManagementId") @ApiParam(value = "合同管理id") String contractManagementId,
                                                       @RequestParam(value = "resourceNo", required = false) @ApiParam(value = "资产编号") String resourceNo,
                                                       @RequestParam(value = "resourceName", required = false) @ApiParam(value = "资产名称") String resourceName) {
        QueryFilter queryFilter = getQueryFilter(request);
        //  逻辑删除标识 0未删除 1已删除
        queryFilter.addFilter("t.DEL_FLAG", "0", QueryOP.EQUAL);
        // 合同id
        queryFilter.addFilter("t.CONTRACT_ID", contractManagementId, QueryOP.EQUAL);
        // 资产审核状态审核通过的 0暂存1待审核2审核通过3审核不通过
        queryFilter.addFilter("t.AUDIT_STATE", "2", QueryOP.EQUAL);
        // 资产编号
        if (StringUtils.isNotEmpty(resourceNo)) {
            queryFilter.addFilter("t.RESOURCE_NO", resourceNo, QueryOP.LIKE);
        }
        // 资产名称
        if (StringUtils.isNotEmpty(resourceName)) {
            queryFilter.addFilter("t.RESOURCE_NAME", resourceName, QueryOP.LIKE);
        }
        queryFilter.addFieldSort("t.CREATE_TIME", "desc");
        return uompContractManagementService.getPropertyList(queryFilter);
    }

    @ApiOperation(value = "根据id查出合同基础信息")
    @RequestMapping(value = "/getInfoById", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<UompContractManagementListDTO> getInfoById(@RequestParam(value = "id") @ApiParam(value = "合同id") String id) {
        return getSuccessResult(uompContractManagementService.getInfoById(id));
    }

    @ApiOperation(value = "查询供应商合作中和已停用的供应商信息")
    @RequestMapping(value = "/getSupplierCooperationList", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<UompSupplierManagement>> getSupplierCooperationList() {
        return getSuccessResult(uompSupplierManagementService.getSupplierCooperationList());
    }

    @ApiOperation(value = "比较时间和当前服务器时间前后")
    @RequestMapping(value = "/compareTime", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<Boolean> compareTime(@RequestParam(value = "time") @ApiParam(value = "时间(时间格式：yyyy-MM-dd)") String time) {
        return getSuccessResult(uompContractManagementService.compareTime(time));
    }

    @ApiOperation(value = "合同管理查询列表接口_对话框")
    @RequestMapping(value = "/getContractList_dialog", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompContractManagementListDTO> getContractListDialog(HttpServletRequest request,
                                                                           @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                           @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                                           @RequestParam(value = "contractCode", required = false) @ApiParam(value = "合同编码") String contractCode,
                                                                           @RequestParam(value = "contractName", required = false) @ApiParam(value = "合同名称") String contractName,
                                                                           @RequestParam(value = "signingDateBegin", required = false) @ApiParam(value = "签订开始时间") String signingDateBegin,
                                                                           @RequestParam(value = "signingDateEnd", required = false) @ApiParam(value = "签订结束时间") String signingDateEnd,
                                                                           @RequestParam(value = "partyAname", required = false) @ApiParam(value = "甲方") String partyAname,
                                                                           @RequestParam(value = "partyBname", required = false) @ApiParam(value = "乙方") String partyBname,
                                                                           @RequestParam(value = "contractType", required = false) @ApiParam(value = "类型,逗号隔开") String contractType,
                                                                           @RequestParam(value = "contractStatus", required = false) @ApiParam(value = "状态,逗号隔开") String contractStatus) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 合同编码
        if (StringUtils.isNotEmpty(contractCode)) {
            queryFilter.addFilter("CONTRACT_CODE", contractCode, QueryOP.LIKE);
        }
        // 合同名称
        if (StringUtils.isNotEmpty(contractName)) {
            queryFilter.addFilter("CONTRACT_NAME", contractName, QueryOP.LIKE);
        }
        // 签约开始时间
        if (StringUtils.isNotEmpty(signingDateBegin)) {
            queryFilter.addFilter("SIGNING_DATE", signingDateBegin, QueryOP.GREAT_EQUAL);
        }
        // 签约结束时间
        if (StringUtils.isNotEmpty(signingDateEnd)) {
            queryFilter.addFilter("SIGNING_DATE", signingDateEnd, QueryOP.LESS_EQUAL);
        }
        // 甲方
        if (StringUtils.isNotEmpty(partyAname)) {
            queryFilter.addFilter("PARTY_A_NAME", partyAname, QueryOP.LIKE);
        }
        // 乙方
        if (StringUtils.isNotEmpty(partyBname)) {
            queryFilter.addFilter("PARTY_B_NAME", partyBname, QueryOP.LIKE);
        }
        // 合同类型
        if (StringUtils.isNotEmpty(contractType)) {
            queryFilter.addFilter("CONTRACT_TYPE", contractType, QueryOP.IN);
        }
        // 合同状态
        if (StringUtils.isNotEmpty(contractStatus)) {
            queryFilter.addFilter("CONTRACT_STATUS", contractStatus, QueryOP.IN);
        }
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompContractManagementService.getContractListDialog(queryFilter);
    }

    @ApiOperation(value = "获取服务等级协议无分页列表")
    @RequestMapping(value = "/sla/getIdAndNameList", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<BaseDTO>> getIdAndNameList() {
        return getSuccessResult(uompContractManagementService.getIdAndNameList());
    }

    @ApiOperation(value = "服务商合同到期统计top5")
    @RequestMapping(value = "/top5", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<SupplierContractDTO>> getContractTop5() {
        return getSuccessResult(uompContractManagementService.getContractTop5());
    }

    @ApiOperation(value = "合同到期提醒接口测试")
    @RequestMapping(value = "/refresh", method = {RequestMethod.POST, RequestMethod.GET})
    public void refresh() throws Exception {
        uompContractManagementService.refresh();
    }

    /**
     * 20241024新需求
     * 服务合同管理关联服务器资源接口
     */
    @ApiOperation(value = "合同管理资源信息保存")
    @PostMapping(value = "/saveResource")
    public ResultMsg saveResource(@Validated @RequestBody UompContractResourceSave uompContractResourceSave) {
        uompContractManagementService.saveResource(uompContractResourceSave);
        return getSuccessResult();
    }

    @ApiOperation(value = "合同管理资源明细列表查询")
    @GetMapping(value = "/getResourceList")
    public PageResult<UompContractResource> getResourceList(HttpServletRequest request,
                                                            @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                            @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                            @RequestParam(value = "name", required = false) @ApiParam(value = "资源名称") String name,
                                                            @RequestParam(value = "contractManagementId") @ApiParam(value = "合同管理id") String contractManagementId) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 合同id
        queryFilter.addFilter("CONTRACT_MANAGEMENT_ID", contractManagementId, QueryOP.EQUAL);
        // 文件名称
        if (StringUtils.isNotEmpty(name)) {
            queryFilter.addFilter("CI_NAME", name, QueryOP.LIKE);
        }
        queryFilter.addFieldSort("ID", "DESC");
        return uompContractManagementService.getResourceList(queryFilter);
    }

    @ApiOperation(value = "合同管理资源删除")
    @GetMapping(value = "/deleteResource")
    public ResultMsg deleteResource(@RequestParam("ids") @ApiParam(value = "合同关联资源主键id，多个逗号隔开") String ids) {
        uompContractManagementService.deleteResource(ids);
        return getSuccessResult();
    }



    @Override
    protected String getModelDesc() {
        return "合同管理";
    }
}
