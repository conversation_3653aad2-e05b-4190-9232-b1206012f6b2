package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

public class JumpUser extends BaseModel {

    private String account;

    private String jumpUser;

    private String jpassword;

    private String userName;

    private String syncStatus;

    private String jumpUserId;

    private String systemKey;

    private String email;

    private String flag;

    private String uompUserId;

    private static final long serialVersionUID = 1L;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getJumpUser() {
        return jumpUser;
    }

    public void setJumpUser(String jumpUser) {
        this.jumpUser = jumpUser;
    }

    public String getJpassword() {
        return jpassword;
    }

    public void setJpassword(String jpassword) {
        this.jpassword = jpassword;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getJumpUserId() {
        return jumpUserId;
    }

    public void setJumpUserId(String jumpUserId) {
        this.jumpUserId = jumpUserId;
    }

    public String getSystemKey() {
        return systemKey;
    }

    public void setSystemKey(String systemKey) {
        this.systemKey = systemKey;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getUompUserId() {
        return uompUserId;
    }

    public void setUompUserId(String uompUserId) {
        this.uompUserId = uompUserId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", account=").append(account);
        sb.append(", jumpUser=").append(jumpUser);
        sb.append(", jpassword=").append(jpassword);
        sb.append(", userName=").append(userName);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", jumpUserId=").append(jumpUserId);
        sb.append(", systemKey=").append(systemKey);
        sb.append(", email=").append(email);
        sb.append(", createTime=").append(createTime);
        sb.append(", flag=").append(flag);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", uompUserId=").append(uompUserId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JumpUser other = (JumpUser) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
            && (this.getJumpUser() == null ? other.getJumpUser() == null : this.getJumpUser().equals(other.getJumpUser()))
            && (this.getJpassword() == null ? other.getJpassword() == null : this.getJpassword().equals(other.getJpassword()))
            && (this.getUserName() == null ? other.getUserName() == null : this.getUserName().equals(other.getUserName()))
            && (this.getSyncStatus() == null ? other.getSyncStatus() == null : this.getSyncStatus().equals(other.getSyncStatus()))
            && (this.getJumpUserId() == null ? other.getJumpUserId() == null : this.getJumpUserId().equals(other.getJumpUserId()))
            && (this.getSystemKey() == null ? other.getSystemKey() == null : this.getSystemKey().equals(other.getSystemKey()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getFlag() == null ? other.getFlag() == null : this.getFlag().equals(other.getFlag()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUompUserId() == null ? other.getUompUserId() == null : this.getUompUserId().equals(other.getUompUserId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getJumpUser() == null) ? 0 : getJumpUser().hashCode());
        result = prime * result + ((getJpassword() == null) ? 0 : getJpassword().hashCode());
        result = prime * result + ((getUserName() == null) ? 0 : getUserName().hashCode());
        result = prime * result + ((getSyncStatus() == null) ? 0 : getSyncStatus().hashCode());
        result = prime * result + ((getJumpUserId() == null) ? 0 : getJumpUserId().hashCode());
        result = prime * result + ((getSystemKey() == null) ? 0 : getSystemKey().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getFlag() == null) ? 0 : getFlag().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUompUserId() == null) ? 0 : getUompUserId().hashCode());
        return result;
    }
}