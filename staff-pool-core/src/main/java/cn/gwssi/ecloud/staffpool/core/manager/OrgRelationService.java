package cn.gwssi.ecloud.staffpool.core.manager;


import cn.gwssi.ecloud.staffpool.core.entity.OrgRelation;
import cn.gwssi.ecloud.staffpool.dto.ManagerDTO;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;

import java.util.List;
import java.util.Set;

public interface OrgRelationService extends Manager<String, OrgRelation> {

    List<ManagerDTO> selectNumByStatus();

    Set<DefaultIdentity> selectUserInfoByUserId();
}

