package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.CmdbCommResourceMapper;
import cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource;
import cn.gwssi.ecloud.staffpool.core.manager.CmdbCommResourceService;
import cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CmdbCommResourceServiceImpl extends BaseManager<String, CmdbCommResource> implements CmdbCommResourceService {

    @Resource
    private CmdbCommResourceMapper cmdbCommResourceMapper;

    @Override
    public int insertSelective(CmdbCommResource record) {
        return cmdbCommResourceMapper.insertSelective(record);
    }

    @Override
    public List<CmdbCommResource> getConfList(QueryFilter queryFilter) {
        return cmdbCommResourceMapper.getConfList(queryFilter);
    }

    @Override
    public List<CmdbCommResource> selectInfoByContractId(QueryFilter queryFilter) {
        return cmdbCommResourceMapper.selectInfoByContractId(queryFilter);
    }

    @Override
    public List<UompPropertyDTO> selectPropertyList(QueryFilter queryFilter) {
        return cmdbCommResourceMapper.selectPropertyList(queryFilter);
    }

}
