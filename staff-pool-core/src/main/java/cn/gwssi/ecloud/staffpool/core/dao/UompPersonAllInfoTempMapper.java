package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompPersonAllInfoTempMapper extends BaseDao<String, UompPersonAllInfoTemp> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonAllInfoTemp record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonAllInfoTemp record);

    UompPersonAllInfoTemp getByPeronId(String peronId);
}