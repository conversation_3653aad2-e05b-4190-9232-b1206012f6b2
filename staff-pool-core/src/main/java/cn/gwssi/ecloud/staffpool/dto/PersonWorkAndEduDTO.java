package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "人员学历/在职")
public class PersonWorkAndEduDTO {

    @ApiModelProperty(value="在职")
    private List<PercentageDTO> workList;
    @ApiModelProperty(value="学历")
    private List<PercentageDTO> eduList;

}