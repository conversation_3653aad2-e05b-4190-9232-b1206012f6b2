package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitAcceptInfo;
import cn.gwssi.ecloud.staffpool.dto.AcceptListBean;
import cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UompExitAcceptInfoMapper extends BaseDao<String, UompExitAcceptInfo> {

    int insertSelective(UompExitAcceptInfo record);

    List<AcceptListBean> selectAllByExitApplyId(String exitApplyId);

    List<UompAcceptInfoDTO> selectInfoById(String id);

    List<String> selectAccountByExitApplyId(String exitApplyId);
}