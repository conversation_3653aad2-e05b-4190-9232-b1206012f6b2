package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial;
import cn.gwssi.ecloud.staffpool.dto.UompPersonSocialDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonSocialMapper extends BaseDao<String, UompPersonSocial> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonSocial record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonSocial record);

    int updateByPersonId(UompPersonSocial record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonSocialDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}