package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description="uomp_person_no_crime")
@Data
public class UompPersonNoCrime extends BaseModel {
    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value="人员id")
    private String personId;

    @ApiModelProperty(value="证明编号")
    @NotEmpty(message = "证明编号不能为空")
    private String proofNumber;

    @ApiModelProperty(value="查询开始时间")
    @NotEmpty(message = "查询开始时间不能为空")
    private String queryBeginTime;

    @ApiModelProperty(value="查询结束时间")
    @NotEmpty(message = "查询结束时间不能为空")
    private String queryEndTime;

    @ApiModelProperty(value="出具单位")
    @NotEmpty(message = "出具单位不能为空")
    private String provideUnit;

    @ApiModelProperty(value="有效期")
    @NotEmpty(message = "有效期不能为空")
    private String indate;

    @ApiModelProperty(value="")
    private String fileInfo;

    @ApiModelProperty(value="")
    private String createOrgId;

    @ApiModelProperty(value="")
    private String updateOrgId;

    @ApiModelProperty(value="")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}