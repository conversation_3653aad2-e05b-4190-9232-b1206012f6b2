package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.base.ResponseData;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.dto.SupplierManagementDTO;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierManagementListDialog;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloud.staffpool.vo.UompSupplierManagementVO;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 供应商管理
 */
@Api(description = "供应商管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/supplier")
public class SupplierController extends BaseController<UompSupplierManagement> {
    @Resource
    private UompSupplierManagementService supplierManagementService;
    @Resource
    private UompSupplierPersonnelService supplierPersonnelService;
    @Resource
    private UompPersonInfoService personInfoService;
    @Resource
    private UompSupplierFileService uompSupplierFileService;
    @Resource
    private UompSupplierQualificationService uompSupplierQualificationService;
    @Resource
    private CmdbCommResourceService commResourceService;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;

    @Override
    protected String getModelDesc() {
        return "供应商管理";
    }

    @ApiOperation(value = "供应商列表查询")
    @RequestMapping(value = "/getList")
    public PageResult getList(HttpServletRequest request, HttpServletResponse response,
                              @RequestParam(value = "supplierName", required = false) String supplierName,
                              @RequestParam(value = "shortName", required = false) String shortName,
                              @RequestParam(value = "supplierStatus", required = false) String supplierStatus,
                              @RequestParam(name = "respName", required = false) String respName,
                              @RequestParam(name = "entryTimeBegin", required = false) String entryTimeBegin,
                              @RequestParam(name = "entryTimeEnd", required = false) String entryTimeEnd) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        if (!StringUtils.isEmpty(supplierName)) {
            queryFilter.addFilter("supplier_name", supplierName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(shortName)) {
            queryFilter.addFilter("short_name", shortName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(supplierStatus)) {
            queryFilter.addFilter("supplier_status", supplierStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(respName)) {
            queryFilter.addFilter("resp_name", respName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(entryTimeBegin)) {
            queryFilter.addFilter("entry_time", entryTimeBegin + " 00:00:00", QueryOP.GREAT_EQUAL);
        }
        if (!StringUtils.isEmpty(entryTimeEnd)) {
            queryFilter.addFilter("entry_time", entryTimeEnd + " 23:59:59", QueryOP.LESS_EQUAL);
        }
        if (StringUtils.isEmpty(request.getParameter("sort"))) {
            queryFilter.addFieldSort("CREATE_TIME", "DESC");
        }
        return new PageResult(supplierManagementService.query(queryFilter));
    }

    @ApiOperation(value = "供应商基础信息保存接口")
    @RequestMapping(value = "/saveAndUpdate")
    public ResultMsg<String> saveAndUpdate(HttpServletRequest request, HttpServletResponse response, @RequestBody UompSupplierManagementVO supplierManagementVO) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompSupplierManagement supplierManagement = new UompSupplierManagement();
        log.info("1111" + JSONObject.toJSONString(supplierManagementVO));
        BeanUtils.copyProperties(supplierManagementVO, supplierManagement);
        log.info("2222" + JSONObject.toJSONString(supplierManagement));
        if (!TextUtils.isEmpty(supplierManagement.getId())) {
            supplierManagement.setUpdateOrgId(user.getOrgId());
            supplierManagementService.updateById(supplierManagement);
        } else {
            supplierManagement.setEntryId(user.getUserId());
            supplierManagement.setEntryName(user.getFullname());
            supplierManagement.setEntryTime(new Date());
            supplierManagement.setCreateOrgId(user.getOrgId());
            supplierManagement.setDelFlag("0");
            supplierManagementService.insertSelective(supplierManagement);
        }
        // 删除旧文件
        uompSupplierFileService.updateBySupplierManagementId(supplierManagement.getId());
        // 文件
        if (supplierManagementVO.getFileInfos() != null && supplierManagementVO.getFileInfos().size() > 0) {
            for (UompSupplierFile supplierFile : supplierManagementVO.getFileInfos()) {
                if (StringUtils.isEmpty(supplierFile.getFileId()) || StringUtils.isEmpty(supplierFile.getFileName()) || StringUtils.isEmpty(supplierFile.getFileType())) {
                    continue;
                }
                supplierFile.setSupplierManagementId(supplierManagement.getId());
                supplierFile.setUploaderId(user.getUserId());
                supplierFile.setUploaderName(user.getFullname());
                supplierFile.setUploadTime(new Date());
//                    supplierFile.setCreateBy(user.getUserId());
                supplierFile.setCreateOrgId(user.getOrgId());
//                    supplierFile.setCreateTime(new Date());
                supplierFile.setDelFlag("0");
                uompSupplierFileService.insertSelective(supplierFile);
            }
        }
        // 资质证书
        if (supplierManagementVO.getQualifications() != null && supplierManagementVO.getQualifications().size() > 0) {
            for (UompSupplierQualification qualification : supplierManagementVO.getQualifications()) {
                qualification.setSupplierManagementId(supplierManagement.getId());
                if (!StringUtils.isEmpty(qualification.getId())) {
                    qualification.setUpdateOrgId(user.getOrgId());
                    uompSupplierQualificationService.updateById(qualification);
                } else {
//                        qualification.setId(IdUtil.getSuid());
//                        qualification.setCreateBy(user.getUserId());
                    qualification.setCreateOrgId(user.getOrgId());
//                        qualification.setCreateTime(new Date());
                    qualification.setDelFlag("0");
                    uompSupplierQualificationService.insertSelective(qualification);
                }
            }
        }
        return this.getSuccessResult(supplierManagement.getId(), "保存成功");
    }

    private ResponseData saveUompSupplierManagement(HashMap<String, Object> params) {
        if (TextUtils.isEmpty((String) params.get("supplier_name"))) {
            return ResponseData.error("supplier_name 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("credit_code"))) {
            return ResponseData.error("credit_code 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("tel"))) {
            return ResponseData.error("tel 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("resp_name"))) {
            return ResponseData.error("resp_name 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("resp_tel"))) {
            return ResponseData.error("resp_tel 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("supplier_type"))) {
            return ResponseData.error("supplier_type 为必填项");
        }
        if (TextUtils.isEmpty((String) params.get("supplier_status"))) {
            return ResponseData.error("supplier_status 为必填项");
        }
        if (((String) params.get("credit_code")).length() != 15 && ((String) params.get("credit_code")).length() != 18) {
            throw new BusinessException("信用代码长度不正确，请检查！");
        }
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierManagement supplierManagement = new UompSupplierManagement();
        supplierManagement.setSupplierName((String) params.get("supplier_name"));
        supplierManagement.setCreditCode((String) params.get("credit_code"));
        supplierManagement.setShortName((String) params.get("short_name"));
        supplierManagement.setTel((String) params.get("tel"));
        supplierManagement.setRespName((String) params.get("resp_name"));
        supplierManagement.setRespTel((String) params.get("resp_tel"));
        supplierManagement.setSupplierType((String) params.get("supplier_type"));
        supplierManagement.setSupplierStatus((String) params.get("supplier_status"));
        supplierManagement.setRegisterAddress((String) params.get("register_address"));
        supplierManagement.setContactAddress((String) params.get("contact_address"));
        supplierManagement.setRemark((String) params.get("remark"));

        if (params.get("id") != null || !params.get("id").equals("")) {
            supplierManagement.setId((String) params.get("id"));
            supplierManagement.setUpdateBy(user.getUserId());
            supplierManagement.setUpdateOrgId(user.getOrgId());
            supplierManagement.setUpdateTime(new Date());
            return ResponseData.success(supplierManagementService.updateById(supplierManagement));
        } else {
            supplierManagement.setId(IdUtil.getSuid());
            supplierManagement.setEntryId(user.getUserId());
            supplierManagement.setEntryName(user.getFullname());
            supplierManagement.setCreateBy(user.getUserId());
            supplierManagement.setCreateOrgId(user.getOrgId());
            supplierManagement.setCreateTime(new Date());
            supplierManagement.setDelFlag("0");
            return ResponseData.success(supplierManagementService.insertSelective(supplierManagement));
        }
    }

    @ApiOperation(value = "供应商基本信息删除接口")
    @RequestMapping(value = "/delete")
    public ResultMsg<String> delete(HttpServletRequest request, HttpServletResponse response, @RequestParam String id) {
        supplierManagementService.deleteById(id);
        return this.getSuccessResult("删除成功");

    }

    @ApiOperation(value = "供应商人员查询接口")
    @RequestMapping(value = "/getPersonnelList")
    public PageResult getPersonnelList(HttpServletRequest request, HttpServletResponse response,
                                       @RequestParam(value = "supplierManagementId") String supplierManagementId,
                                       @RequestParam(value = "personName", required = false) String personName,
                                       @RequestParam(value = "technicalDirection", required = false) String technicalDirection) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("WORKING_COMPANY_ID", supplierManagementId, QueryOP.EQUAL);
        queryFilter.addFilter("TRIAL_STATUS", "2", QueryOP.EQUAL);
//        if (!StringUtils.isEmpty(personName)) {
//            queryFilter.addFilter("PERSON_NAME", personName, QueryOP.LIKE);
//        }
//        if (!StringUtils.isEmpty(technicalDirection)) {
//            queryFilter.addFilter("TECHNICAL_DIRECTION", technicalDirection, QueryOP.IN);
//        }
        List<UompPersonInfo> personInfos = personInfoService.query(queryFilter);
        List<UompDesensitization> rule_list = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (rule_list != null && rule_list.size() > 0) {
            return new PageResult(JSONObject.parseArray(conversionUtil.conversionBatch(rule_list, JSON.parseArray(JSONObject.toJSONString(personInfos))).toJSONString(), UompPersonInfo.class));
        }
        return new PageResult(personInfos);
    }

    @ApiOperation(value = "供应商人员新增and修改接口")
    @RequestMapping(value = "/savePersonnel")
    public ResultMsg<String> savePersonnel(HttpServletRequest request, HttpServletResponse response, @RequestBody UompSupplierPersonnel supplierPersonnel) {
        if (TextUtils.isEmpty(supplierPersonnel.getSupplierManagementId())) {
            throw new BusinessMessage("supplierManagementId 为必填项");
        }
        if (TextUtils.isEmpty(supplierPersonnel.getPersonnelName())) {
            throw new BusinessMessage("personnelName 为必填项");
        }
        if (TextUtils.isEmpty(supplierPersonnel.getPersonnelType())) {
            throw new BusinessMessage("personnelType 为必填项");

        }
        if (TextUtils.isEmpty(supplierPersonnel.getPersonnelTel())) {
            throw new BusinessMessage("personnelTel 为必填项");

        }
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (StringUtils.isEmpty(supplierPersonnel.getId())) {
            supplierPersonnel.setUpdateOrgId(user.getOrgId());
            supplierPersonnelService.updateById(supplierPersonnel);
        } else {
            supplierPersonnel.setCreateOrgId(user.getOrgId());
            supplierPersonnel.setDelFlag("0");
            supplierPersonnelService.insertSelective(supplierPersonnel);
        }
        return this.getSuccessResult(supplierPersonnel.getId(), "保存成功");
    }

    @ApiOperation(value = "供应商人员删除接口")
    @RequestMapping(value = "/deletePersonnel")
    public ResultMsg<String> deletePersonnel(HttpServletRequest request, HttpServletResponse response, @RequestParam String id) {
        supplierPersonnelService.deleteById(id);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "供应商文件列表查询接口")
    @RequestMapping(value = "/getFileList")
    public PageResult getFileList(HttpServletRequest request, HttpServletResponse response,
                                  @RequestParam(value = "supplierManagementId") String supplierManagementId,
                                  @RequestParam(value = "fileName", required = false) String fileName) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("SUPPLIER_MANAGEMENT_ID", supplierManagementId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        if (!StringUtils.isEmpty(fileName)) {
            queryFilter.addFilter("FILE_NAME", fileName, QueryOP.LIKE);
        }
        return new PageResult(uompSupplierFileService.query(queryFilter));
    }

//    @ApiOperation(value = "供应商文件信息保存接口")
//    @RequestMapping(value = "/saveFileInfo")
//    public ResponseData saveFileInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody HashMap<String, Object> params) {
//        if (TextUtils.isEmpty((String) params.get("supplierManagementId"))) {
//            return ResponseData.error("supplierManagementId 为必填项");
//        }
//        List<Map<String, String>> fileInfos = (List<Map<String, String>>) params.get("fileInfos");
//        if (fileInfos == null || fileInfos.size() <= 0) {
//            return ResponseData.error("fileInfos 为必填项");
//        }
//        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
//        // todo: LogOperateUtil
//        for (Map<String, String> fileInfo : fileInfos) {
//            UompSupplierFile supplierFile = new UompSupplierFile();
//            supplierFile.setId(IdUtil.getSuid());
//            supplierFile.setSupplierManagementId(fileInfo.get("supplierManagementId"));
//            supplierFile.setFileName(fileInfo.get("fileFullName"));
//            supplierFile.setFileId(fileInfo.get("fileId"));
//            supplierFile.setUploaderId(user.getUserId());
//            supplierFile.setUploaderName(user.getFullname());
//            supplierFile.setUploadTime(new Date());
//            supplierFile.setCreateBy(user.getUserId());
//            supplierFile.setCreateOrgId(user.getOrgId());
//            supplierFile.setCreateTime(new Date());
//            supplierFile.setDelFlag("0");
//            uompSupplierFileService.insertSelective(supplierFile);
//        }
//        return ResponseData.success();
//    }

    @ApiOperation(value = "供应商文件删除接口")
    @RequestMapping(value = "/deleteFile")
    public ResponseData deleteFile(HttpServletRequest request, HttpServletResponse response, @RequestParam String id) {
        return ResponseData.success(uompSupplierFileService.deleteById(id));
    }

    @ApiOperation(value = "供应商配置信息列表接口")
    @RequestMapping(value = "/getConfList")
    public PageResult getConfList(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "supplierManagementId") String supplierManagementId) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("t.DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("t.SUPPLIER_ID", supplierManagementId, QueryOP.EQUAL);
        queryFilter.addFilter("t.AUDIT_STATE", "2", QueryOP.EQUAL);
        queryFilter.addFieldSort("t.CREATE_TIME", "DESC");
        return new PageResult(commResourceService.getConfList(queryFilter));
    }

    @ApiOperation(value = "根据供应商id查询资质证书接口")
    @RequestMapping(value = "/getQualificationList")
    public PageResult getQualificationList(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "supplierManagementId") String supplierManagementId) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("SUPPLIER_MANAGEMENT_ID", supplierManagementId, QueryOP.EQUAL);
        return new PageResult(uompSupplierQualificationService.query(queryFilter));
    }

    @ApiOperation(value = "资质证书保存接口")
    @RequestMapping(value = "/saveQualification")
    public ResponseData saveQualification(HttpServletRequest request, HttpServletResponse response, @RequestBody UompSupplierQualification qualification) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        if (TextUtils.isEmpty(qualification.getSupplierManagementId())) {
            throw new BusinessMessage("supplierManagementId 为必填项");
        }
        if (!StringUtils.isEmpty(qualification.getId())) {
            qualification.setUpdateOrgId(user.getOrgId());
            return ResponseData.success(uompSupplierQualificationService.updateById(qualification));
        } else {
            qualification.setCreateOrgId(user.getOrgId());
            qualification.setDelFlag("0");
            return ResponseData.success(uompSupplierQualificationService.insertSelective(qualification));
        }
    }

    @ApiOperation(value = "删除资质证书")
    @RequestMapping(value = "/deleteQualification")
    public ResultMsg<String> deleteQualification(HttpServletRequest request, HttpServletResponse response, @RequestParam String id) {
        uompSupplierQualificationService.deleteById(id);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "文件列表查询接口_专利")
    @RequestMapping(value = "/getFileList_patent")
    public ResponseData getFileListPatent(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "supplierManagementId") String supplierManagementId) {
        return ResponseData.success(uompSupplierFileService.getFileListPatent(supplierManagementId));
    }

    @ApiOperation(value = "文件信息保存接口_专利")
    @RequestMapping(value = "/saveFileInfo_patent")
    public ResponseData saveFileInfoPatent(HttpServletRequest request, HttpServletResponse response, @RequestBody List<UompSupplierFile> fileInfos) {
        if (fileInfos == null || fileInfos.size() <= 0) {
            return ResponseData.error("fileInfos 为必填项");
        }
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        // todo: LogOperateUtil
        for (UompSupplierFile supplierFile : fileInfos) {
            supplierFile.setUploaderId(user.getUserId());
            supplierFile.setUploaderName(user.getFullname());
            supplierFile.setUploadTime(new Date());
//            supplierFile.setCreateBy(user.getUserId());
            supplierFile.setCreateOrgId(user.getOrgId());
//            supplierFile.setCreateTime(new Date());
            supplierFile.setDelFlag("0");
            uompSupplierFileService.insertSelective(supplierFile);
        }
        return ResponseData.success();
    }

    @ApiOperation(value = "供应商基础信息保存接口_专利")
    @RequestMapping(value = "/save_patent")
    public ResponseData savePatent(HttpServletRequest request, HttpServletResponse response, @RequestBody HashMap<String, Object> params) {
        // 基础信息保存
        saveUompSupplierManagement(params);
        //处理附件
        List<Map<String, String>> fileInfos = (List<Map<String, String>>) params.get("fileInfos");
        if (fileInfos == null || fileInfos.size() <= 0) {
            return ResponseData.success();
        }
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        // todo: LogOperateUtil
        for (Map<String, String> fileInfo : fileInfos) {
            UompSupplierFile supplierFile = new UompSupplierFile();
            supplierFile.setSupplierManagementId(fileInfo.get("supplier_management_id"));
            supplierFile.setFileName(fileInfo.get("fileFullName"));
            supplierFile.setFileId(fileInfo.get("fileId"));
            if (fileInfo.get("id") != null || !fileInfo.get("id").equals("")) {
                uompSupplierFileService.update(supplierFile);
            } else {
                supplierFile.setId(IdUtil.getSuid());
                supplierFile.setUploaderId(user.getUserId());
                supplierFile.setUploaderName(user.getFullname());
                supplierFile.setUploadTime(new Date());
                supplierFile.setCreateBy(user.getUserId());
                supplierFile.setCreateOrgId(user.getOrgId());
                supplierFile.setCreateTime(new Date());
                supplierFile.setDelFlag("0");
                uompSupplierFileService.insertSelective(supplierFile);
            }
        }
        return ResponseData.success();
    }

    @ApiOperation(value = "供应商列表查询_自定义对话框")
    @RequestMapping(value = "/getList_dialog")
    public PageResult<UompSupplierManagementListDialog> getListDialog(HttpServletRequest request,
                                                                      @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                      @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                                      @RequestParam(value = "supplierName", required = false) @ApiParam(value = "供应商名称") String supplierName,
                                                                      @RequestParam(value = "shortName", required = false) @ApiParam(value = "简称") String shortName,
                                                                      @RequestParam(value = "supplierStatus", required = false) @ApiParam(value = "状态，逗号隔开") String supplierStatus,
                                                                      @RequestParam(value = "entryTimeBegin", required = false) @ApiParam(value = "录入开始时间") String entryTimeBegin,
                                                                      @RequestParam(value = "entryTimeEnd", required = false) @ApiParam(value = "录入结束时间") String entryTimeEnd,
                                                                      @RequestParam(value = "respName", required = false) @ApiParam(value = "负责人名称") String respName,
                                                                      @RequestParam(value = "sortMap", required = false) @ApiParam(value = "自定义排序(格式：key#value，key:排序字段  value:排序方式 desc、asc,多个字段排序逗号隔开。例：id#desc,user_name#asc)") String sortMap) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        if (StringUtils.isNotEmpty(supplierName)) {
            queryFilter.addFilter("SUPPLIER_NAME", supplierName, QueryOP.LIKE);
        }
        if (StringUtils.isNotEmpty(shortName)) {
            queryFilter.addFilter("SHORT_NAME", shortName, QueryOP.LIKE);
        }
        if (StringUtils.isNotEmpty(entryTimeBegin)) {
            queryFilter.addFilter("ENTRY_TIME", entryTimeBegin, QueryOP.GREAT_EQUAL);
        }
        if (StringUtils.isNotEmpty(entryTimeEnd)) {
            queryFilter.addFilter("ENTRY_TIME", entryTimeEnd, QueryOP.LESS_EQUAL);
        }
        if (StringUtils.isNotEmpty(supplierStatus)) {
            queryFilter.addFilter("SUPPLIER_STATUS", supplierStatus, QueryOP.IN);
        }
        if (StringUtils.isNotEmpty(respName)) {
            queryFilter.addFilter("RESP_NAME", respName, QueryOP.LIKE);
        }
        if (StringUtils.isNotEmpty(sortMap)) {
            List<String> orderList = Arrays.asList(sortMap.split(","));
            for (String key : orderList) {
                String[] orderKey = key.split("#");
                queryFilter.addFieldSort(orderKey[0], orderKey[1]);
            }
        } else {
            queryFilter.addFieldSort("CREATE_TIME", "DESC");
        }
        return supplierManagementService.getListDialog(queryFilter);
    }

    @ApiOperation(value = "获取所有供应商")
    @RequestMapping(value = "/getAllSupplier")
    public ResultMsg<List<SupplierManagementDTO>> getAllSupplier(HttpServletRequest request, HttpServletResponse response) {
        return this.getSuccessResult(supplierManagementService.selectIdAndSupplierName(), "保存成功");
    }
}

