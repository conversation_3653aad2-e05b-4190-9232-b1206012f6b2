package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@ApiModel(description="创建账号类")
@Data
public class AccountPersonAddDTO{

    @ApiModelProperty(value="用户id")
    private String userId;
    @ApiModelProperty(value="姓名")
    @NotBlank(message = "姓名不能为空！")
    private String fullname;
    @ApiModelProperty(value="账号")
    @NotBlank(message = "账号不能为空！")
    private String account;
    @ApiModelProperty(value="密码")
    @NotBlank(message = "密码不能为空！")
    private String password;
    @ApiModelProperty(value="手机号")
    private String mobile;
    @ApiModelProperty(value="地址")
    private String address;
    @ApiModelProperty(value="性别")
    private String sex;
    @ApiModelProperty(value="系统运维组织id")
    @NotBlank(message = "运维组织不能为空！")
    private String orgGroupId;
}
