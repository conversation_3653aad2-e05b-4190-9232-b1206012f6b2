package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.AccountListQueryVO;
import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoHistoryMapper;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.*;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import cn.gwssi.ecloudbpm.module.template.core.manager.TemplateManager;
import cn.gwssi.ecloudbpm.module.template.core.model.Template;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.core.manager.SysFileManager;
import cn.gwssi.ecloudframework.sys.core.model.SysFile;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(description = "人员信息")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/personInfo")
public class UompPersonInfoController extends BaseController<UompPersonInfo> {

    @Resource
    private UompSupplierManagementService supplierManagementService;
    @Resource
    private UompAccountApplyService uompAccountApplyService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompHistoryRecordService uompHistoryRecordService;
    @Resource
    private UompTempAdmissionService uompTempAdmissionService;
    @Resource
    private UompPersonEducationalService uompPersonEducationalService;
    @Resource
    private UompPersonJobService uompPersonJobService;
    @Resource
    private UompPersonTechnologyService uompPersonTechnologyService;
    @Resource
    private UompPersonSocialService uompPersonSocialService;
    @Resource
    private UompPersonNoCrimeService uompPersonNoCrimeService;
    @Resource
    private UompPersonAbroadService uompPersonAbroadService;
    @Resource
    private UompPersonEntryExitService uompPersonEntryExitService;
    @Resource
    private UompTrainingRecordService trainingRecordService;

    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private DictUtil dictUtil;
    @Resource
    private UompPersonConfigService uompPersonConfigService;
    @Resource
    private SupplierUtil supplierUtil;
    @Autowired
    private SysDataDictMapper sysDataDictMapper;
    @Autowired
    TemplateManager templateManager;
    @Autowired
    SysFileManager sysFileManager;
    @Resource
    private UompOrgGroupService ucompOrgGroupService;

    @Resource
    private UompAdmissionPersonService uompAdmissionPersonService;



    @ApiOperation(value = "账号申请列表")
    @PostMapping(value = "/accountNumList")
    public PageResult<UompAccountApplyDTO> accountNumListPage(@Validated @RequestBody AccountListQueryVO accountListQueryVO) {
        return uompAccountApplyService.accountNumListPage(accountListQueryVO);
    }

    /**
     * 240620查询逻辑：
     * 审核状态通过、没有分配账号、处于驻场服务中、非黑名单、背调非不合格的人员
     **/
    @ApiOperation(value = "查询审批通过且尚未分配账号的人员信息")
    @RequestMapping(value = "/getNoAccountPeronList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPersonInfoAndAccountDTO> getNoAccountPeronList(HttpServletRequest request,
                                                                         @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                         @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("upi.DEL_FLAG", "0", QueryOP.EQUAL);
        // 审核通过的
        queryFilter.addFilter("upi.TRIAL_STATUS", "2", QueryOP.EQUAL);
        // 没有分配账号
        queryFilter.addFilter("upi.IS_ACCOUNT", "0", QueryOP.EQUAL);
        // 驻场服务中(0驻场服务 1离岗 -1未驻场)
        queryFilter.addFilter("upi.ENTRY_STATUS", "0", QueryOP.EQUAL);
        // 非黑名单人员
        queryFilter.addFilter("upi.BLACKLIST", "0", QueryOP.EQUAL);
        // 背调非不合格的
        queryFilter.addFilter("upi.BACKGROUND_STATUS", "2", QueryOP.NOT_EQUAL);
        queryFilter.addFieldSort("upi.CREATE_TIME", "DESC");
        return uompAccountApplyService.getNoAccountPeronList(queryFilter);
    }

    @Override
    protected String getModelDesc() {
        return "人员信息";
    }

    @ApiOperation(value = "获取人员信息列表")
    @PostMapping(value = "/getPersonList")
    public PageResult getPersonList(HttpServletRequest request, @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                    @RequestParam(value = "supplierManagementId", required = false) @ApiParam(value = "服务商Id") String supplierManagementId,
                                    @RequestParam(value = "orgGroupId", required = false) @ApiParam(value = "运维组织Id") String orgGroupId,
                                    @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                    @RequestParam(value = "education", required = false) @ApiParam(value = "学历") String education,
                                    @RequestParam(value = "entryDate", required = false) @ApiParam(value = "入职时间段") String entryDate,
                                    @RequestParam(value = "technicalDirection", required = false) @ApiParam(value = "技术方向") String technicalDirection,
                                    @RequestParam(value = "trialStatus", required = false) @ApiParam(value = "审核状态") String trialStatus,
                                    @RequestParam(value = "backgroundStatus", required = false) @ApiParam(value = "背调审查状态") String backgroundStatus,
                                    @RequestParam(value = "entryStatus", required = false) @ApiParam(value = "驻场服务状态") String entryStatus,
                                    @RequestParam(value = "dimission", required = false) @ApiParam(value = "人员状态") String dimission,
                                    @RequestParam(value = "blacklist") @ApiParam(value = "黑名单标识 0-非黑 1-黑") String blacklist,
                                    @RequestParam(value = "pageNo", defaultValue = "1") @ApiParam(value = "页码") Integer pageNo,
                                    @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 黑名单需求不确定，排除这个过滤
        queryFilter.addFilter("BLACKLIST", blacklist, QueryOP.EQUAL);
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(orgGroupId)) {
            List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
            if (orgIds != null && orgIds.size() > 0) {
                queryFilter.addFilter("ORG_GROUP_ID", orgIds, QueryOP.IN);
            }
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(education)) {
            queryFilter.addFilter("EDUCATION", education, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(trialStatus)) {
            queryFilter.addFilter("TRIAL_STATUS", trialStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(backgroundStatus)) {
            queryFilter.addFilter("BACKGROUND_STATUS", backgroundStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(dimission)) {
            queryFilter.addFilter("DIMISSION", dimission, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(entryStatus)) {
            queryFilter.addFilter("ENTRY_STATUS", entryStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(technicalDirection)) {
            queryFilter.addFilter("TECHNICAL_DIRECTION", technicalDirection, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("ENTRY_DATE", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("ENTRY_DATE", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        IsSupplierDto supplierDto = supplierUtil.isSupplier();
        if (supplierDto != null && "1".equals(supplierDto.getIfSupplier()) && !StringUtils.isEmpty(supplierDto.getSupplierId())) {
            queryFilter.addFilter("WORKING_COMPANY_id", supplierDto.getSupplierId(), QueryOP.EQUAL);
        }
        if (!StringUtils.isEmpty(supplierManagementId)) {
            queryFilter.addFilter("WORKING_COMPANY_ID", supplierManagementId, QueryOP.EQUAL);
        }
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.size() == 0) {
            return new PageResult();
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.selectList(queryFilter1,null);
            if (workCompany != null && workCompany.size() > 0) {
                queryFilter.addFilter("WORKING_COMPANY_ID", workCompany.get(0).getWorkingCompanyId(), QueryOP.EQUAL);
            }
        } else {
            return new PageResult();
        }
        List<UompPersonInfo> personInfoList = uompPersonInfoService.selectList(queryFilter,null);
        PageResult pageResult = new PageResult(personInfoList);
        List<UompPersonInfoTaskDto> taskDtos = new ArrayList<>();
        for (UompPersonInfo personInfo : personInfoList) {
            UompPersonInfoTaskDto dto = new UompPersonInfoTaskDto();
            BeanUtils.copyProperties(personInfo, dto);
            if ("3".equals(personInfo.getTrialStatus())) {
                BaseInstDTO instDTO = uompPersonInfoService.selectTaskIdByInstId(personInfo.getInstId());
                dto.setTaskLinkId(instDTO.getLinkId());
                dto.setTaskId(instDTO.getTaskId());
            }
            taskDtos.add(dto);
        }
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            List<UompPersonInfoTaskDto> uompPersonInfoTaskDtos = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(taskDtos))).toJSONString(), UompPersonInfoTaskDto.class);
            pageResult.setRows(uompPersonInfoTaskDtos);
            return pageResult;
        }
        pageResult.setRows(taskDtos);
        return pageResult;
    }

    @ApiOperation(value = "离职登记接口")
    @RequestMapping(value = "/addDimission")
    public ResultMsg<String> addDimission(HttpServletRequest request, HttpServletResponse response,
                                          @RequestParam(value = "ids") @ApiParam(value = "列表id") String ids,
                                          @RequestParam(value = "dimission") @ApiParam(value = "人员状态") String dimission) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String[] strs = ids.split(",");
        if (strs.length > 0) {
            for (String id : strs) {
                UompPersonInfo personInfo = new UompPersonInfo();
                personInfo.setId(id);
                personInfo.setDimission(dimission);
                personInfo.setUpdateOrgId(user.getOrgId());
                personInfo.setUpdateTime(new Date());
                personInfo.setUpdateBy(user.getUserId());
                uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
                uompAdmissionPersonService.exitAdmissionPerson(id, null, String.valueOf(System.currentTimeMillis()));
            }
            if ("1".equals(dimission)) {
                uompPersonInfoService.updateAccountStatus(ids, "0");
            }
        }
        return this.getSuccessResult("加入成功");
    }

    @ApiOperation(value = "加入黑名单接口")
    @RequestMapping(value = "/addBlackList")
    public ResultMsg<String> addBlackList(HttpServletRequest request, HttpServletResponse response,
                                          @RequestParam(value = "ids") @ApiParam(value = "列表id") String ids,
                                          @RequestParam(value = "blacklistReason") @ApiParam(value = "加入黑名单原因") String blacklistReason) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String[] strs = ids.split(",");
        for (String id : strs) {
            UompPersonInfo personInfo = new UompPersonInfo();
            personInfo.setId(id);
            personInfo.setBlacklist("1");
            personInfo.setBlacklistReason(blacklistReason);
            personInfo.setUpdateOrgId(user.getOrgId());
            personInfo.setUpdateTime(new Date());
            personInfo.setUpdateBy(user.getUserId());
            uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
            UompHistoryRecord historyRecord = new UompHistoryRecord();
            historyRecord.setBizId(id);
            historyRecord.setBizType("0");
            historyRecord.setOperatorId(user.getUserId());
            historyRecord.setOperatorName(user.getFullname());
            historyRecord.setOperatorMessage("加入黑名单");
            historyRecord.setOperatorReason(blacklistReason);
            historyRecord.setOperatorTime(new Date());
            historyRecord.setDelFlag("0");
            uompHistoryRecordService.insertSelective(historyRecord);
            uompAdmissionPersonService.exitAdmissionPerson(id, null, String.valueOf(System.currentTimeMillis()));
        }
        if (!StringUtils.isEmpty(ids)) {
            uompPersonInfoService.updateAccountStatus(ids, "0");
        }
        return this.getSuccessResult("加入成功");
    }

    @ApiOperation(value = "移除黑名单")
    @RequestMapping(value = "/moveBlackList")
    public ResultMsg<String> moveBlackList(HttpServletRequest request, HttpServletResponse response,
                                           @RequestParam(value = "id") @ApiParam(value = "列表id") String id,
                                           @RequestParam(value = "reason") @ApiParam(value = "移除原因") String reason,
                                           @RequestParam(value = "type") @ApiParam(value = "移除种类 0-人员 1-临时入场") String type) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        if (type.equals("0")) {
            UompPersonInfo personInfo = new UompPersonInfo();
            personInfo.setId(id);
            personInfo.setBlacklist("0");
            personInfo.setBlacklistReason(null);
            personInfo.setUpdateOrgId(user.getOrgId());
            personInfo.setUpdateTime(new Date());
            personInfo.setUpdateBy(user.getUserId());
            uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
        } else {
            //根据id查出人名和身份证号，将该人名和身份证号的所有记录都置为加入黑名单
            UompTempAdmission uompTempAdmission = uompTempAdmissionService.get(id);

            UompTempAdmission tempAdmission = new UompTempAdmission();
            tempAdmission.setPersonName(uompTempAdmission.getPersonName());
            tempAdmission.setPersonCard(uompTempAdmission.getPersonCard());
            tempAdmission.setBlacklist("0");
            tempAdmission.setBlacklistReason(null);
            uompTempAdmissionService.updateByPersonNameAndCard(tempAdmission);
        }
        UompHistoryRecord historyRecord = new UompHistoryRecord();
        historyRecord.setBizId(id);
        historyRecord.setBizType("0");
        historyRecord.setOperatorId(user.getUserId());
        historyRecord.setOperatorName(user.getFullname());
        historyRecord.setOperatorMessage("移除黑名单");
        historyRecord.setOperatorReason(reason);
        historyRecord.setOperatorTime(new Date());
        historyRecord.setDelFlag("0");
        uompHistoryRecordService.insertSelective(historyRecord);
        return this.getSuccessResult("移除成功");
    }

    @ApiOperation(value = "根据id查询人员基本信息接口")
    @RequestMapping(value = "/getPersonInfo")
    public ResultMsg<UompPersonInfoDetailDto> getPersonInfo(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "id") @ApiParam(value = "id") String id,
                                                            @RequestParam(value = "isUpdate", required = false) @ApiParam(value = "是否修改的查看，有值就是，没值就不是") String isUpdate) {
        UompPersonInfo personInfo = uompPersonInfoService.get(id);
        UompPersonInfoDetailDto personInfoDetailDto = new UompPersonInfoDetailDto();
        BeanUtils.copyProperties(personInfo, personInfoDetailDto);
        if (!StringUtils.isEmpty(personInfo.getFileInfo())) {
            JSONArray jsonArray = JSONObject.parseArray(personInfo.getFileInfo());
            personInfoDetailDto.setFileInfo(jsonArray);
        }

        String regPermanentResidenceStr = dictUtil.getKeyByName("G_USER_NATIVE_CODE", personInfo.getRegPermanentResidence());
        if (!StringUtils.isEmpty(regPermanentResidenceStr)) {
            personInfoDetailDto.setRegPermanentResidence(regPermanentResidenceStr.replace(",", ""));
        }
        if (StringUtils.isEmpty(isUpdate)) {
            List<UompDesensitization> desensitizations = desRuleUtil.getDesRule("UOMP_PERSON_INFO", null, personInfo.getInstId(), personInfo.getCreateBy());
            if (desensitizations != null && desensitizations.size() > 0) {
                for (UompDesensitization desensitization : desensitizations) {
                    //如果有关于手机号的脱敏规则则转换手机号
                    if (!StringUtils.isEmpty(personInfo.getTel()) && "tel".equals(desensitization.getDesFieldCode())) {
                        personInfoDetailDto.setTel(conversionUtil.conversion(desensitization, personInfo.getTel()));
                    }
                    //如果有关于身份证号的脱敏规则则转换身份证号
                    if (!StringUtils.isEmpty(personInfo.getPersonCard()) && "personCard".equals(desensitization.getDesFieldCode())) {
                        personInfoDetailDto.setPersonCard(conversionUtil.conversion(desensitization, personInfo.getPersonCard()));
                    }
                }
            }
        }
        return this.getSuccessResult(personInfoDetailDto);
    }

    @ApiOperation(value = "根据人员id获取教育背景列表")
    @RequestMapping(value = "/getEducationListByPersonId")
    public PageResult<List<UompPersonEducationalDto>> getEducationListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                                 @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                                 @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                                 @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonEducational> educationals = uompPersonEducationalService.query(queryFilter);
        List<UompPersonEducationalDto> educationalDtos = new ArrayList<>();
        PageResult pageResult = new PageResult(educationals);
        for (UompPersonEducational educational : educationals) {
            UompPersonEducationalDto educationalDto = new UompPersonEducationalDto();
            BeanUtils.copyProperties(educational, educationalDto);
            if (!StringUtils.isEmpty(educational.getFileInfo())) {
                JSONArray jsonArray = JSONObject.parseArray(educational.getFileInfo());
                educationalDto.setFileInfo(jsonArray);
            }
            educationalDto.setEducationTime(educational.getEducationBeginTime() + "-" + educational.getEducationEndTime());
            educationalDtos.add(educationalDto);
        }
        pageResult.setRows(educationalDtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id获取工作背景")
    @RequestMapping(value = "/getJobListByPersonId")
    public PageResult<List<UompPersonJobDto>> getJobListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                   @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                   @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                   @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonJob> jobs = uompPersonJobService.query(queryFilter);
        PageResult pageResult = new PageResult(jobs);
        List<UompPersonJobDto> jobDtos = new ArrayList<>();
        for (UompPersonJob job : jobs) {
            UompPersonJobDto jobDto = new UompPersonJobDto();
            BeanUtils.copyProperties(job, jobDto);
            if (!StringUtils.isEmpty(job.getFileInfo())) {
                JSONArray jsonArray = JSONObject.parseArray(job.getFileInfo());
                jobDto.setFileInfo(jsonArray);
            }
            jobDto.setJobTime(job.getJobBeginTime() + "-" + job.getJobEndTime());
            jobDtos.add(jobDto);
        }
        pageResult.setRows(jobDtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id获取技术资质列表")
    @RequestMapping(value = "/getTechnologyListByPersonId")
    public PageResult<List<UompPersonTechnologyDto>> getTechnologyListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                                 @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                                 @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                                 @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonTechnology> persons = uompPersonTechnologyService.query(queryFilter);
        List<UompPersonTechnologyDto> dtos = new ArrayList<>();
        PageResult pageResult = new PageResult(persons);
        for (UompPersonTechnology person : persons) {
            UompPersonTechnologyDto dto = new UompPersonTechnologyDto();
            BeanUtils.copyProperties(person, dto);
            if (!StringUtils.isEmpty(person.getFileInfo())) {
                JSONArray jsonArray = JSONObject.parseArray(person.getFileInfo());
                dto.setFileInfo(jsonArray);
            }
            dto.setStartEndTime(person.getStartTime() + "-" + person.getEndTime());
            dtos.add(dto);
        }
        pageResult.setRows(dtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id获取社会关系列表")
    @RequestMapping(value = "/getSocialListByPersonId")
    public PageResult<List<UompPersonSocialDto>> getSocialListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                         @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                         @RequestParam(value = "isDese", required = false) @ApiParam(value = "是否无需脱密(有值就不脱敏)") String isDese,
                                                                         @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                         @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonSocial> persons = uompPersonSocialService.query(queryFilter);
        PageResult pageResult = new PageResult(persons);
        List<UompPersonSocialDto> dtos = new ArrayList<>();
        for (UompPersonSocial person : persons) {
            UompPersonSocialDto dto = new UompPersonSocialDto();
            BeanUtils.copyProperties(person, dto);
            dto.setTel(person.getRelaTel());
            dtos.add(dto);
        }
        if (StringUtils.isEmpty(isDese)) {
            UompPersonInfo personInfo = uompPersonInfoService.get(personId);
            List<UompDesensitization> desensitizations = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, personInfo.getCreateBy());
            if (desensitizations != null && desensitizations.size() > 0) {
                dtos = JSONObject.parseArray(conversionUtil.conversionBatch(desensitizations, JSON.parseArray(JSONObject.toJSONString(dtos))).toJSONString(), UompPersonSocialDto.class);
            }
        }
        for (UompPersonSocialDto dto : dtos) {
            dto.setRelaTel(dto.getTel());
            dto.setTel(null);
        }
        pageResult.setRows(dtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id获取无犯罪记录列表")
    @RequestMapping(value = "/getNoCrimeListByPersonId")
    public PageResult<List<UompPersonNoCrimeDto>> getNoCrimeListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                           @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                           @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                           @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonNoCrime> persons = uompPersonNoCrimeService.query(queryFilter);
        List<UompPersonNoCrimeDto> dtos = new ArrayList<>();
        PageResult pageResult = new PageResult(persons);
        for (UompPersonNoCrime person : persons) {
            UompPersonNoCrimeDto dto = new UompPersonNoCrimeDto();
            BeanUtils.copyProperties(person, dto);
            if (!StringUtils.isEmpty(person.getFileInfo())) {
                JSONArray jsonArray = JSONObject.parseArray(person.getFileInfo());
                dto.setFileInfo(jsonArray);
            }
            dto.setQueryTime(person.getQueryBeginTime() + "-" + person.getQueryEndTime());
            dtos.add(dto);
        }
        pageResult.setRows(dtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id查询出国证件列表")
    @RequestMapping(value = "/getAbroadListByPersonId")
    public PageResult<List<UompPersonAbroadDto>> getAbroadListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                         @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                         @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                         @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonAbroad> persons = uompPersonAbroadService.query(queryFilter);
        List<UompPersonAbroadDto> dtos = new ArrayList<>();
        PageResult pageResult = new PageResult(persons);
        for (UompPersonAbroad person : persons) {
            UompPersonAbroadDto dto = new UompPersonAbroadDto();
            BeanUtils.copyProperties(person, dto);
            if (!StringUtils.isEmpty(person.getFileInfo())) {
                JSONArray jsonArray = JSONObject.parseArray(person.getFileInfo());
                dto.setFileInfo(jsonArray);
            }
            dto.setStartEndTime(person.getStartTime() + "-" + person.getEndTime());
            dtos.add(dto);
        }
        pageResult.setRows(dtos);
        return pageResult;
    }

    @ApiOperation(value = "根据人员id查询出入境记录")
    @RequestMapping(value = "/getEntryExitListByPersonId")
    public PageResult<List<UompPersonEntryExit>> getEntryExitListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                                            @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                            @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                            @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("PERSON_ID", personId, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonEntryExit> persons = uompPersonEntryExitService.query(queryFilter);
        return new PageResult(persons);
    }

    @ApiOperation(value = "人员所有信息新增or修改")
    @RequestMapping(value = "/saveAllPersonInfo")
    @Transactional
    public ResultMsg<String> saveAllPersonInfo(HttpServletRequest request, HttpServletResponse response,
                                               @RequestBody UompPersonInfoVo personInfo) {
        uompPersonInfoService.saveAllPersonInfo(personInfo);
        return this.getSuccessResult(personInfo.getId(), "保存成功");
    }

    @ApiOperation(value = "人员基本信息新增or修改")
    @RequestMapping(value = "/savePersonInfo")
    public ResultMsg<String> savePersonnel(HttpServletRequest request, HttpServletResponse response,
                                           @RequestBody UompPersonInfo personInfo) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date birthday = null;
        try {
            birthday = sdf.parse(personInfo.getPersonBirthday());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if (birthday.after(new Date())) {
            throw new BusinessException("出生年月不能在当前日期之后！");
        }
        if (!StringUtils.isEmpty(personInfo.getRegPermanentResidence())) {
            String[] strings = personInfo.getRegPermanentResidence().split(",");
            for (int i = 0; i < strings.length; i++) {
                if (i == 0) {
                    personInfo.setRegProvince(strings[0]);
                }
                if (i == 1) {
                    personInfo.setRegCity(strings[1]);
                }
                if (i == 2) {
                    personInfo.setRegRegion(strings[2]);
                }
            }
        }
        if (!StringUtils.isEmpty(personInfo.getId())) {
            UompPersonInfo uompPersonInfo = uompPersonInfoService.get(personInfo.getId());
            if (!personInfo.getPersonCard().equals(uompPersonInfo.getPersonCard())) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("PERSON_CARD", personInfo.getPersonCard(), QueryOP.EQUAL);
                queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompPersonInfo> personInfos = uompPersonInfoService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    throw new BusinessException("该身份证号已经存在系统中，不可重复，请仔细检查！");
                }
            }
            if (!personInfo.getTel().equals(uompPersonInfo.getTel())) {
                QueryFilter queryFilter1 = new DefaultQueryFilter(true);
                queryFilter1.addFilter("TEL", personInfo.getTel(), QueryOP.EQUAL);
                queryFilter1.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompPersonInfo> personInfos = uompPersonInfoService.query(queryFilter1);
                if (personInfos != null && personInfos.size() > 0) {
                    throw new BusinessException("该联系电话已经存在系统中，不可重复，请仔细检查！");
                }
            }
            personInfo.setUpdateOrgId(user.getOrgId());
            personInfo.setUpdateTime(new Date());
            personInfo.setUpdateBy(user.getUserId());
            uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
        } else {
            QueryFilter queryFilter = new DefaultQueryFilter(true);
            queryFilter.addFilter("PERSON_CARD", personInfo.getPersonCard(), QueryOP.EQUAL);
            queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
            List<UompPersonInfo> personInfos = uompPersonInfoService.query(queryFilter);
            if (personInfos != null && personInfos.size() > 0) {
                throw new BusinessException("该身份证号已经存在系统中，不可重复，请仔细检查！");
            }
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("TEL", personInfo.getTel(), QueryOP.EQUAL);
            queryFilter1.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
            personInfos = uompPersonInfoService.query(queryFilter1);
            if (personInfos != null && personInfos.size() > 0) {
                throw new BusinessException("该联系电话已经存在系统中，不可重复，请仔细检查！");
            }
            personInfo.setBlacklist(("0"));
            personInfo.setTrialStatus(("0"));
            personInfo.setBackgroundStatus(("0"));
            personInfo.setIsAccount(("0"));
            personInfo.setCreateOrgId(user.getOrgId());
            personInfo.setDelFlag("0");
            personInfo.setUpdating("0");
            uompPersonInfoService.insertSelective(personInfo);
        }
        return this.getSuccessResult(personInfo.getId(), "保存成功");
    }

    @ApiOperation(value = "人员教育背景新增or修改")
    @RequestMapping(value = "/saveEducation")
    public ResultMsg<String> saveEducation(HttpServletRequest request, HttpServletResponse response,
                                           @RequestBody UompPersonEducational person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonEducationalService.updateByPrimaryKeySelective(person);
        } else {
            QueryFilter queryFilter = new DefaultQueryFilter(true);
            queryFilter.addFilter("EDUCATION_BEGIN_TIME", person.getEducationBeginTime(), QueryOP.EQUAL);
            queryFilter.addFilter("EDUCATION_END_TIME", person.getEducationEndTime(), QueryOP.EQUAL);
            queryFilter.addFilter("SCHOOL", person.getSchool(), QueryOP.EQUAL);
            queryFilter.addFilter("MAJOR", person.getMajor(), QueryOP.EQUAL);
            queryFilter.addFilter("EDUCATION_BACKGROUND", person.getEducationBackground(), QueryOP.EQUAL);
            queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
            queryFilter.addFilter("PERSON_ID", person.getPersonId(), QueryOP.EQUAL);
            List<UompPersonEducational> personInfos = uompPersonEducationalService.query(queryFilter);
            if (personInfos != null && personInfos.size() > 0) {
                throw new BusinessException("该教育经历在列表中已存在，请勿重复添加");
            }
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonEducationalService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员工作背景新增or修改")
    @RequestMapping(value = "/saveJob")
    public ResultMsg<String> saveJob(HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody UompPersonJob person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonJobService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonJobService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员技术资质新增or修改")
    @RequestMapping(value = "/saveTechnology")
    public ResultMsg<String> saveTechnology(HttpServletRequest request, HttpServletResponse response,
                                            @RequestBody UompPersonTechnology person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonTechnologyService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonTechnologyService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员社会关系新增or修改")
    @RequestMapping(value = "/saveSocial")
    public ResultMsg<String> saveSocial(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody UompPersonSocial person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonSocialService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonSocialService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员无犯罪记录新增or修改")
    @RequestMapping(value = "/saveNoCrime")
    public ResultMsg<String> saveNoCrime(HttpServletRequest request, HttpServletResponse response,
                                         @RequestBody UompPersonNoCrime person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonNoCrimeService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonNoCrimeService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员出国证件新增or修改")
    @RequestMapping(value = "/saveAbroad")
    public ResultMsg<String> saveAbroad(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody UompPersonAbroad person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonAbroadService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonAbroadService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "人员出入境记录新增or修改")
    @RequestMapping(value = "/saveEntryExit")
    public ResultMsg<String> saveEntryExit(HttpServletRequest request, HttpServletResponse response,
                                           @RequestBody UompPersonEntryExit person) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        if (!StringUtils.isEmpty(person.getId())) {
            person.setUpdateOrgId(user.getOrgId());
            person.setUpdateTime(new Date());
            person.setUpdateBy(user.getUserId());
            uompPersonEntryExitService.updateByPrimaryKeySelective(person);
        } else {
            person.setCreateOrgId(user.getOrgId());
            person.setDelFlag("0");
            uompPersonEntryExitService.create(person);
        }
        return this.getSuccessResult(person.getId(), "保存成功");
    }

    @ApiOperation(value = "教育背景删除接口")
    @RequestMapping(value = "/deleteEducation")
    public ResultMsg<String> deleteEducation(HttpServletRequest request, HttpServletResponse response,
                                             @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonEducational person = new UompPersonEducational();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonEducationalService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "工作背景删除接口")
    @RequestMapping(value = "/deleteJob")
    public ResultMsg<String> deleteJob(HttpServletRequest request, HttpServletResponse response,
                                       @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonJob person = new UompPersonJob();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonJobService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "技术资源删除接口")
    @RequestMapping(value = "/deleteTechnology")
    public ResultMsg<String> deleteTechnology(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonTechnology person = new UompPersonTechnology();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonTechnologyService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "社会关系删除接口")
    @RequestMapping(value = "/deleteSocial")
    public ResultMsg<String> deleteSocial(HttpServletRequest request, HttpServletResponse response,
                                          @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonSocial person = new UompPersonSocial();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonSocialService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "无犯罪记录删除接口")
    @RequestMapping(value = "/deleteNoCrime")
    public ResultMsg<String> deleteNoCrime(HttpServletRequest request, HttpServletResponse response,
                                           @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonNoCrime person = new UompPersonNoCrime();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonNoCrimeService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "人员列表删除接口")
    @RequestMapping(value = "/deletePersonInfo")
    public ResultMsg<String> deletePersonInfo(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonInfo personInfo = uompPersonInfoService.get(id);
        if (personInfo != null && !"0".equals(personInfo.getBlacklist())) {
            throw new BusinessException("只能删除暂存状态的数据！");
        }
        UompPersonInfo person = new UompPersonInfo();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonInfoService.updateByPrimaryKeySelective(person);

        UompPersonEducational educational = new UompPersonEducational();
        educational.setPersonId(id);
        educational.setDelFlag("1");
        educational.setUpdateOrgId(user.getOrgId());
        educational.setUpdateTime(new Date());
        educational.setUpdateBy(user.getUserId());
        uompPersonEducationalService.updateByPersonId(educational);

        UompPersonJob person1 = new UompPersonJob();
        person1.setPersonId(id);
        person1.setDelFlag("1");
        person1.setUpdateOrgId(user.getOrgId());
        person1.setUpdateTime(new Date());
        person1.setUpdateBy(user.getUserId());
        uompPersonJobService.updateByPersonId(person1);

        UompPersonTechnology person2 = new UompPersonTechnology();
        person2.setPersonId(id);
        person2.setDelFlag("1");
        person2.setUpdateOrgId(user.getOrgId());
        person2.setUpdateTime(new Date());
        person2.setUpdateBy(user.getUserId());
        uompPersonTechnologyService.updateByPersonId(person2);

        UompPersonSocial person3 = new UompPersonSocial();
        person3.setPersonId(id);
        person3.setDelFlag("1");
        person3.setUpdateOrgId(user.getOrgId());
        person3.setUpdateTime(new Date());
        person3.setUpdateBy(user.getUserId());
        uompPersonSocialService.updateByPersonId(person3);

        UompPersonNoCrime person4 = new UompPersonNoCrime();
        person4.setPersonId(id);
        person4.setDelFlag("1");
        person4.setUpdateOrgId(user.getOrgId());
        person4.setUpdateTime(new Date());
        person4.setUpdateBy(user.getUserId());
        uompPersonNoCrimeService.updateByPersonId(person4);

        UompPersonAbroad person5 = new UompPersonAbroad();
        person5.setPersonId(id);
        person5.setDelFlag("1");
        person5.setUpdateOrgId(user.getOrgId());
        person5.setUpdateTime(new Date());
        person5.setUpdateBy(user.getUserId());
        uompPersonAbroadService.updateByPersonId(person5);

        UompPersonEntryExit person6 = new UompPersonEntryExit();
        person6.setPersonId(id);
        person6.setDelFlag("1");
        person6.setUpdateOrgId(user.getOrgId());
        person6.setUpdateTime(new Date());
        person6.setUpdateBy(user.getUserId());
        uompPersonEntryExitService.updateByPersonId(person6);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "出国证件删除接口")
    @RequestMapping(value = "/deleteAbroad")
    public ResultMsg<String> deleteAbroad(HttpServletRequest request, HttpServletResponse response,
                                          @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonAbroad person = new UompPersonAbroad();
        person.setId(id);
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonAbroadService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "出入境记录删除接口")
    @RequestMapping(value = "/deleteEntryExit")
    public ResultMsg<String> deleteEntryExit(HttpServletRequest request, HttpServletResponse response,
                                             @RequestParam String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonEntryExit person = new UompPersonEntryExit();
        person.setId(id);
        person.setDelFlag("1");
        person.setUpdateOrgId(user.getOrgId());
        person.setUpdateTime(new Date());
        person.setUpdateBy(user.getUserId());
        uompPersonEntryExitService.updateByPrimaryKeySelective(person);
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "人员管理-根据id查询人员社会关系详情")
    @RequestMapping(value = "/getSocialInfoById")
    public ResultMsg<UompPersonSocial> getSocialListByPersonId(HttpServletRequest request, HttpServletResponse response,
                                                               @RequestParam String id) {
        return new ResultMsg(uompPersonSocialService.get(id));
    }

    @ApiOperation(value = "背景情况列表查询")
    @RequestMapping(value = "/getBackGroundList")
    public PageResult<List<UompPersonInfo>> getBackGroundList(HttpServletRequest request, HttpServletResponse response,
                                                              @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                                              @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                                              @RequestParam(value = "orgGroupId", required = false) @ApiParam(value = "运维组织Id") String orgGroupId,
                                                              @RequestParam(value = "auditDate", required = false) @ApiParam(value = "审核时间段") String entryDate,
                                                              @RequestParam(value = "backgroundStatus", required = false) @ApiParam(value = "审查结果") String backgroundStatus,
                                                              @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                              @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("BLACKLIST", "0", QueryOP.EQUAL);
        queryFilter.addFilter("TRIAL_STATUS", "2", QueryOP.EQUAL);
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(orgGroupId)) {
            List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
            if (orgIds != null && orgIds.size() > 0) {
                queryFilter.addFilter("ORG_GROUP_ID", orgIds, QueryOP.IN);
            }
        }
        if (!StringUtils.isEmpty(backgroundStatus)) {
            queryFilter.addFilter("BACKGROUND_STATUS", backgroundStatus, QueryOP.IN);
        }

        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("AUDIT_DATE", entryDates[0] + " 00:00:00", QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("AUDIT_DATE", entryDates[1] + " 23:59:59", QueryOP.LESS_EQUAL);
            }
        }
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonInfo> personInfoList = uompPersonInfoService.query(queryFilter);
        PageResult pageResult = new PageResult(personInfoList);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            pageResult.setRows(JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(personInfoList))).toJSONString(), UompPersonInfo.class));
        }
        return pageResult;
    }

    @ApiOperation(value = "人员信息总览")
    @RequestMapping(value = "/getPersonAllInfo")
    public ResultMsg<UompPersonInfoVo> getPersonAllInfo(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "id") @ApiParam(value = "id") String id,
                                                        @RequestParam(value = "instId", required = false) @ApiParam(value = "流程id") String instId,
                                                        @RequestParam(value = "updating", defaultValue = "0") @ApiParam(value = "是否查询暂存数据 1查询暂存") String updating) {
        return this.getSuccessResult(uompPersonInfoService.getPersonAllInfo(id,updating,instId));
    }

    @ApiOperation(value = "获取就职公司服务商下拉数据接口")
    @RequestMapping(value = "/getSupplierDataList")
    public ResultMsg<List<LabelDTO>> getSupplierDataList(HttpServletRequest request, HttpServletResponse response) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        IsSupplierDto supplierDto = supplierUtil.isSupplier();
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        //服务商运维负责人 看自己服务商的
        if (supplierDto != null && "1".equals(supplierDto.getIfSupplier())) {
//            queryFilter.addFilter("GROUP_ID", user.getOrgId(), QueryOP.EQUAL);
            queryFilter.addFilter("SUPPLIER_STATUS", "1", QueryOP.IN);
            queryFilter.addFilter("ID", supplierDto.getSupplierId(), QueryOP.EQUAL);
            supplierManagementService.query(queryFilter);
        } else {
            queryFilter.addFilter("SUPPLIER_STATUS", "1", QueryOP.IN);
            supplierManagementService.query(queryFilter);
        }
        List<UompSupplierManagement> managements = supplierManagementService.query(queryFilter);
        List<LabelDTO> labelDTOS = new ArrayList<>();
        for (UompSupplierManagement management : managements) {
            LabelDTO labelDTO = new LabelDTO();
            labelDTO.setValue(management.getId());
            labelDTO.setLabel(management.getSupplierName());

            labelDTOS.add(labelDTO);
        }
        return new ResultMsg<>(labelDTOS);
    }

    @ApiOperation(value = "当前人员的培训记录列表接口")
    @RequestMapping(value = "/getTrainRecordList")
    public PageResult<UompTrainingRecord> getTrainRecordList(HttpServletRequest request, HttpServletResponse response,
                                                             @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                             @RequestParam(value = "trainingName", required = false) @ApiParam(value = "培训名称") String trainingName,
                                                             @RequestParam(value = "trainingMode", required = false) @ApiParam(value = "培训方式") String trainingMode,
                                                             @RequestParam(value = "trainingTime", required = false) @ApiParam(value = "培训时间段") String trainingTime,
                                                             @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                             @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        BaseDTO baseDTO = uompPersonInfoService.selectUserId(personId);
        if (baseDTO != null && !StringUtils.isEmpty(baseDTO.getId())) {
            QueryFilter queryFilter = getQueryFilter(request);
            queryFilter.addFilter("b.DEL_FLAG", "0", QueryOP.EQUAL);
            queryFilter.addFilter("a.TRAINING_PERSON_ID", baseDTO.getId(), QueryOP.EQUAL);
            if (!StringUtils.isEmpty(trainingName)) {
                queryFilter.addFilter("b.TRAINING_NAME", trainingName, QueryOP.LIKE);
            }
            if (!StringUtils.isEmpty(trainingMode)) {
                queryFilter.addFilter("b.TRAINING_MODE", trainingMode, QueryOP.EQUAL);
            }
            if (!StringUtils.isEmpty(trainingTime)) {
                String[] entryDates = trainingTime.split(",");
                if (entryDates.length > 1) {
                    queryFilter.addFilter("b.TRAINING_BEGIN_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                    queryFilter.addFilter("b.TRAINING_END_TIME", entryDates[1], QueryOP.LESS_EQUAL);
                }
            }
            return new PageResult(trainingRecordService.query(queryFilter));
        }
        return new PageResult<>(new ArrayList<>());
    }

    @ApiOperation(value = "黑名单历史记录查询接口")
    @RequestMapping(value = "/getBlackHistoryList")
    public PageResult<UompBlackDto> getBlackHistoryList(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "id") @ApiParam(value = "id") String id,
                                                        @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                                        @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                                        @RequestParam(value = "entryDate", required = false) @ApiParam(value = "入职时间段") String entryDate,
                                                        @RequestParam(value = "operatorName", required = false) @ApiParam(value = "操作人姓名") String operatorName,
                                                        @RequestParam(value = "operatorTime", required = false) @ApiParam(value = "操作时间段") String operatorTime,
                                                        @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                        @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFieldSort("f.OPERATOR_TIME", "DESC");

        if (!StringUtils.isEmpty(id)) {
            queryFilter.addFilter("f.BIZ_ID", id, QueryOP.EQUAL);
        }
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("f.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("f.PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.ENTRY_DATE", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.ENTRY_DATE", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        if (!StringUtils.isEmpty(operatorName)) {
            queryFilter.addFilter("f.OPERATOR_NAME", operatorName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(operatorTime)) {
            String[] entryDates = operatorTime.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.OPERATOR_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.OPERATOR_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        List<UompBlackDto> blackDtos = uompHistoryRecordService.getBlackHistoryList(queryFilter);
        PageResult pageResult = new PageResult(blackDtos);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            pageResult.setRows(JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(blackDtos))).toJSONString(), UompBlackDto.class));
        }
        return pageResult;
    }

    @ApiOperation(value = "黑名单管理列表查询")
    @RequestMapping(value = "/getBlackList")
    public PageResult getBlackList(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                   @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                   @RequestParam(value = "updateTime", required = false) @ApiParam(value = "加入黑名单日期") String updateTime,
                                   @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                   @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFieldSort("f.CREATE_TIME", "DESC");
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("f.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("f.PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(updateTime)) {
            String[] entryDates = updateTime.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.UPDATE_TIME", entryDates[0] + " 00:00:00", QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.UPDATE_TIME", entryDates[1] + " 23:59:59", QueryOP.LESS_EQUAL);
            }
        }
        List<UompBlackDto> blackDtos = uompPersonInfoService.getBlackList(queryFilter);
        for (UompBlackDto blackDto : blackDtos) {
            if ("3".equals(blackDto.getTrialStatus())) {
                BaseInstDTO instDTO = uompPersonInfoService.selectTaskIdByInstId(blackDto.getInstId());
                blackDto.setTaskLinkId(instDTO.getLinkId());
                blackDto.setTaskId(instDTO.getTaskId());
            }
        }
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        PageResult pageResult = new PageResult(blackDtos);
        if (ruleList != null && ruleList.size() > 0) {
            pageResult.setRows(JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(blackDtos))).toJSONString(), UompBlackDto.class));
        }
        return pageResult;
    }

    @ApiOperation(value = "黑名单列表删除接口")
    @RequestMapping(value = "/deleteBlackPersonInfo")
    public ResultMsg<String> deleteBlackPersonInfo(HttpServletRequest request, HttpServletResponse response,
                                                   @RequestParam String ids) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        String[] personIds = ids.split(",");
        uompPersonInfoService.updateByIds(user.getOrgId(), personIds);

        uompPersonEducationalService.updateByPersonIds(user.getOrgId(), personIds);

        uompPersonJobService.updateByPersonIds(user.getOrgId(), personIds);

        uompPersonTechnologyService.updateByPersonIds(user.getOrgId(), personIds);

        uompPersonSocialService.updateByPersonIds(user.getOrgId(), personIds);

        uompPersonNoCrimeService.updateByPersonIds(user.getOrgId(), personIds);

        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "背景审核结果导入接口")
    @RequestMapping(value = "/uploadBackStatus")
    public ResultMsg<String> uploadBackStatus(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam(value = "file") MultipartFile file) throws Exception {

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        //文件类型判断
        if (null == file.getOriginalFilename()
                || (!file.getOriginalFilename().endsWith(".xls")
                && !file.getOriginalFilename().endsWith(".xlsx"))) {
            throw new BusinessException("文件格式错误！");
        }

        List<Map<String, String>> errorList = new ArrayList<>();
        Map<String, String> errorMap;
        // 入库列表
        List<UompPersonInfo> personList = new ArrayList<>();
        List<UompBackStatusDto> uompBackStatusDtos = EasyExcel.read(file.getInputStream()).head(UompBackStatusDto.class).sheet(0).headRowNumber(1).doReadSync();

        if (uompBackStatusDtos != null && uompBackStatusDtos.size() > 0) {
            List<BaseDTO> backgroundStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_BACKGROUND_STATUS");
            Map<String, String> backgroundStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : backgroundStatusList) {
                backgroundStatusMap.put(baseDTO.getName(), baseDTO.getId());
            }
            int i = 1;
            for (UompBackStatusDto backStatusDto : uompBackStatusDtos) {
                boolean flag = true;
                String id = "";
                if (backStatusDto == null) {
                    i++;
                    continue;
                }
                //姓名
                if (StringUtils.isEmpty(backStatusDto.getPersonName())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第2列");
                    errorMap.put("message", "姓名不能为空");
                    errorList.add(errorMap);
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(backStatusDto.getPersonCard())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第3列");
                    errorMap.put("message", "身份证号不能为空");
                    errorList.add(errorMap);
                    flag = false;
                }

                //校验该姓名，身份证号在库中存在不存在
                if (!StringUtils.isEmpty(backStatusDto.getPersonName()) && !StringUtils.isEmpty(backStatusDto.getPersonCard())) {
                    id = uompPersonInfoService.selectIdAndAccountByPersonNameAndPersonCard(backStatusDto.getPersonName(), backStatusDto.getPersonCard());

                    if (StringUtils.isEmpty(id)) {
                        errorMap = new HashMap<>();
                        errorMap.put("row", "第" + (i + 1) + "行");
                        errorMap.put("column", "第2,3列");
                        errorMap.put("message", "该行姓名和身份证在系统中不存在，或者尚未通过审核，请检查");
                        errorList.add(errorMap);
                        flag = false;
                    }
                }
                String backgroundStatus = null;
                //结果
                if (StringUtils.isEmpty(backStatusDto.getBackgroundStatus())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第4列");
                    errorMap.put("message", "审核结果不能为空");
                    errorList.add(errorMap);
                    flag = false;
                } else {
                    backgroundStatus = backgroundStatusMap.get(backStatusDto.getBackgroundStatus());
                    //如果数据没比对上，则不在字典内，提示错误
                    if (StringUtils.isEmpty(backgroundStatus)) {
                        errorMap = new HashMap<>();
                        errorMap.put("row", "第" + (i + 1) + "行");
                        errorMap.put("column", "第4列");
                        errorMap.put("message", "审核结果错误，请填写“合格”或“不合格”");
                        errorList.add(errorMap);
                        flag = false;
                    }
                }

                if (flag) {
                    UompPersonInfo personInfo = new UompPersonInfo();
                    personInfo.setBackgroundStatus(backgroundStatus);
                    personInfo.setPersonName(backStatusDto.getPersonName());
                    //身份证号没有变化，不需要更新
                    //personInfo.setPersonCard(backStatusDto.getPersonCard());
                    personInfo.setUpdateOrgId(user.getOrgId());
                    personInfo.setUpdateBy(user.getId());
                    personInfo.setUpdateTime(new Date());
                    personInfo.setAuditDate(new Date());
                    if (StringUtils.isNotEmpty(id)) {
                        personInfo.setId(id);
                    }
                    personList.add(personInfo);
                }
                i++;
            }
        } else {
            errorMap = new HashMap<>();
            errorMap.put("message", "必填项不能为空");
            errorList.add(errorMap);
        }

        //修改校验合格的数据
        if (personList.size() > 0) {
            for (UompPersonInfo personInfo : personList) {
                personInfo.setUpdateTime(new Date());
                personInfo.setUpdateBy(user.getUserId());
                personInfo.setUpdateOrgId(user.getOrgId());
                uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
            }
        }

        if (!errorList.isEmpty()) {
            //导出数据
            // 第一个对象，新建一个工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 设置第一个sheet的名称
            Sheet sheet = workbook.createSheet("sheet1");

            // 开始添加excel第一行表头（excel中下标是0）
            Row row = sheet.createRow(0);
            sheet.setDefaultColumnWidth(16);//宽

            row.setHeightInPoints(20);//行高

            String[] fieldList = {"序号", "行数", "列数", "错误原因"};
            int i = 0;
            // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
            for (String it : fieldList) {
                // 创建一个单元格
                Cell cell = row.createCell(i);
                // 设置单元格的样式
                CellStyle cellStyle = workbook.createCellStyle();
                //设置字体
                Font font = workbook.createFont();
                //设置字号
                font.setFontHeightInPoints((short) 14);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                // 将数据放入excel的单元格中
                cell.setCellValue(it);
                i++;
            }

            // 开始创建excel单元格数据，从第二行开始（excel下标是1）
            int rowNum = 1;
            // 添加excel行数据的集合（你自己的数据集合遍历）
            for (Map<String, String> it : errorList) {
                // 创建一个单元格
                Row row1 = sheet.createRow(rowNum);
                // 设置行的高度
                row1.setHeightInPoints(16);
                //填写单元格
                row1.createCell(0).setCellValue(rowNum + "");//序号
                row1.createCell(1).setCellValue(it.get("row"));//行
                row1.createCell(2).setCellValue(it.get("column"));//列
                row1.createCell(3).setCellValue(it.get("message"));//原因

                rowNum++;
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                //把文件送到用户端
                workbook.write(bos);
                byte[] brray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(brray);
                SysFile upload = sysFileManager.upload(is, "错误信息-" + IdUtil.getSuid() + ".xlsx");
                return ResultMsg.ERROR(upload.getId());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放资源
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
                workbook.close();
            }
        } else {
            return ResultMsg.SUCCESS("导入成功!");
        }
        throw new BusinessException("导入失败!");
    }

    @ApiOperation(value = "黑名单导出接口")
    @RequestMapping(value = "/exportBlack")
    public HttpServletResponse exportBlack(HttpServletRequest request, HttpServletResponse response,
                                           @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                           @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                           @RequestParam(value = "updateTime", required = false) @ApiParam(value = "入职时间段") String updateTime) throws IOException {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFieldSort("f.CREATE_TIME", "DESC");
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("f.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("f.PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(updateTime)) {
            String[] entryDates = updateTime.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.UPDATE_TIME", entryDates[0] + " 00:00:00", QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.UPDATE_TIME", entryDates[1] + " 23:59:59", QueryOP.LESS_EQUAL);
            }
        }
        List<UompBlackDto> blackDtos = uompPersonInfoService.getBlackList(queryFilter);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            blackDtos = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(blackDtos))).toJSONString(), UompBlackDto.class);
        }

        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("黑名单数据");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号", "姓名", "性别", "联系方式", "就职公司", "运维组织", "技术方向", "工作职位", "学历", "加入黑名单日期", "加入黑名单原因"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }
        //技术方向 TECHNICAL_DIRECTION
        List<BaseDTO> tecList = sysDataDictMapper.selectSubListByUompEducation("UOMP_TEC_DIRECTION");
        Map<String, String> techMap = new HashMap<>();
        for (BaseDTO baseDTO : tecList) {
            techMap.put(baseDTO.getId(), baseDTO.getName());
        }
        //学历字段查询
        List<BaseDTO> baseDTOList = sysDataDictMapper.selectSubListByUompEducation("UOMP_EDUCATION");
        Map<String, String> educationMap = new HashMap<>();
        for (BaseDTO baseDTO : baseDTOList) {
            educationMap.put(baseDTO.getId(), baseDTO.getName());
        }
        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        for (UompBlackDto it : blackDtos) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getPersonName() == null ? "" : it.getPersonName());//姓名
            if (!StringUtils.isEmpty(it.getPersonSex())) {
                row1.createCell(2).setCellValue("0".equals(it.getPersonSex()) ? "男" : "女");//性别
            } else {
                row1.createCell(2).setCellValue("");//性别
            }
            row1.createCell(3).setCellValue(it.getTel() == null ? "" : it.getTel());//联系方式
            row1.createCell(4).setCellValue(it.getWorkingCompany() == null ? "" : it.getWorkingCompany());//就职公司
            row1.createCell(5).setCellValue(it.getOrgGroupName() == null ? "" : it.getOrgGroupName());//运维组织
            row1.createCell(6).setCellValue(techMap.get(it.getTechnicalDirection()));//技术方向
            row1.createCell(7).setCellValue(it.getPost() == null ? "" : it.getPost());//职位
            //学历处理
            String education = educationMap.get(it.getEducation());
            row1.createCell(8).setCellValue(education);//学历
//            row1.createCell(6).setCellValue(it.getMajor() == null ? "" : it.getMajor());//专业
            row1.createCell(9).setCellValue(it.getCreateTime() == null ? "" : DateUtils.dateToStr(it.getCreateTime()));//入职时间
            row1.createCell(10).setCellValue(it.getBlacklistReason() == null ? "" : it.getBlacklistReason());//加入黑名单原因
            rowNum++;
        }

        OutputStream os = null;
        try {
            //把文件送到用户端
            String downName = "黑名单列表.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }

    @ApiOperation(value = "导出黑名单历史记录接口")
    @RequestMapping(value = "/exportHistory")
    public HttpServletResponse exportHistory(HttpServletRequest request, HttpServletResponse response,
                                             @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                             @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                             @RequestParam(value = "entryDate", required = false) @ApiParam(value = "入职时间段") String entryDate,
                                             @RequestParam(value = "operatorName", required = false) @ApiParam(value = "操作人姓名") String operatorName,
                                             @RequestParam(value = "operatorTime", required = false) @ApiParam(value = "操作时间段") String operatorTime) throws IOException {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFieldSort("f.OPERATOR_TIME", "DESC");
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("f.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("f.PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.ENTRY_DATE", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.ENTRY_DATE", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        if (!StringUtils.isEmpty(operatorName)) {
            queryFilter.addFilter("f.OPERATOR_NAME", operatorName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(operatorTime)) {
            String[] entryDates = operatorTime.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("f.OPERATOR_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("f.OPERATOR_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        List<UompBlackDto> blackDtos = uompHistoryRecordService.getBlackHistoryList(queryFilter);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            blackDtos = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(blackDtos))).toJSONString(), UompBlackDto.class);
        }
        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("黑名单数据");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号", "姓名", "性别", "出生日期", "联系方式", "就职公司", "工作职位", "入职日期", "操作记录", "操作原因说明", "操作人", "操作时间"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }


        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        for (UompBlackDto it : blackDtos) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getPersonName() == null ? "" : it.getPersonName());//姓名
            if (!StringUtils.isEmpty(it.getPersonSex())) {
                row1.createCell(2).setCellValue("0".equals(it.getPersonSex()) ? "男" : "女");//性别
            } else {
                row1.createCell(2).setCellValue("");//性别
            }

            row1.createCell(3).setCellValue(it.getPersonBirthday() == null ? "" : it.getPersonBirthday());//出生年月
            row1.createCell(4).setCellValue(it.getTel() == null ? "" : it.getTel());//联系方式
            row1.createCell(5).setCellValue(it.getWorkingCompany() == null ? "" : it.getWorkingCompany());//单位
            row1.createCell(6).setCellValue(it.getPost() == null ? "" : it.getPost());//职位
            row1.createCell(7).setCellValue(it.getEntryDate() == null ? "" : it.getEntryDate());//入职时间
            row1.createCell(8).setCellValue(it.getOperatorMessage() == null ? "" : it.getOperatorMessage());//操作记录
            row1.createCell(9).setCellValue(it.getOperatorReason() == null ? "" : it.getOperatorReason());//操作原因说明
            row1.createCell(10).setCellValue(it.getOperatorName() == null ? "" : it.getOperatorName());//操作人
            row1.createCell(11).setCellValue(it.getOperatorTime() == null ? null : it.getOperatorTime());//操作时间
            rowNum++;
        }

        OutputStream os = null;
        try {
            //把文件送到用户端
            String downName = "黑名单操作记录列表.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }

    @ApiOperation(value = "人员信息导出接口")
    @RequestMapping(value = "/personExport")
    public HttpServletResponse personExport(HttpServletRequest request, HttpServletResponse response,
                                            @RequestParam(value = "ids", required = false) @ApiParam(value = "列表id") String ids,
                                            @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                            @RequestParam(value = "supplierManagementId", required = false) @ApiParam(value = "服务商Id") String supplierManagementId,
                                            @RequestParam(value = "orgGroupId", required = false) @ApiParam(value = "运维组织Id") String orgGroupId,
                                            @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                            @RequestParam(value = "education", required = false) @ApiParam(value = "学历") String education,
                                            @RequestParam(value = "entryDate", required = false) @ApiParam(value = "入职时间段") String entryDate,
                                            @RequestParam(value = "technicalDirection", required = false) @ApiParam(value = "技术方向") String technicalDirection,
                                            @RequestParam(value = "trialStatus", required = false) @ApiParam(value = "审核状态") String trialStatus,
                                            @RequestParam(value = "backgroundStatus", required = false) @ApiParam(value = "背调审查状态") String backgroundStatus,
                                            @RequestParam(value = "entryStatus", required = false) @ApiParam(value = "驻场服务状态") String entryStatus,
                                            @RequestParam(value = "dimission", required = false) @ApiParam(value = "人员状态") String dimission,
                                            @RequestParam(value = "blacklist") @ApiParam(value = "黑名单标识 0-非黑 1-黑") String blacklist) throws IOException {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("BLACKLIST", blacklist, QueryOP.EQUAL);
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(orgGroupId)) {
            List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
            if (orgIds != null && orgIds.size() > 0) {
                queryFilter.addFilter("ORG_GROUP_ID", orgIds, QueryOP.IN);
            }
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("PERSON_NAME", personName, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(education)) {
            queryFilter.addFilter("EDUCATION", education, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(trialStatus)) {
            queryFilter.addFilter("TRIAL_STATUS", trialStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(backgroundStatus)) {
            queryFilter.addFilter("BACKGROUND_STATUS", backgroundStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(dimission)) {
            queryFilter.addFilter("DIMISSION", dimission, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(entryStatus)) {
            queryFilter.addFilter("ENTRY_STATUS", entryStatus, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(technicalDirection)) {
            queryFilter.addFilter("TECHNICAL_DIRECTION", technicalDirection, QueryOP.IN);
        }
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("ENTRY_DATE", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("ENTRY_DATE", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        IsSupplierDto supplierDto = supplierUtil.isSupplier();
        if (supplierDto != null && "1".equals(supplierDto.getIfSupplier()) && !StringUtils.isEmpty(supplierDto.getSupplierId())) {
            queryFilter.addFilter("WORKING_COMPANY_id", supplierDto.getSupplierId(), QueryOP.EQUAL);
        }
        if (!StringUtils.isEmpty(supplierManagementId)) {
            queryFilter.addFilter("WORKING_COMPANY_ID", supplierManagementId, QueryOP.EQUAL);
        }
        if (!StringUtils.isEmpty(ids)) {
            queryFilter.addFilter("ID", ids, QueryOP.IN);
        }
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonInfo> personInfoList = uompPersonInfoService.personExport(queryFilter);
        if (personInfoList == null || personInfoList.size() == 0) {
            throw new BusinessException("无可导出数据");
        }
        List<String> personIdList = new ArrayList<>();
        for (UompPersonInfo personInfo : personInfoList) {
            String regPermanentResidenceStr = dictUtil.getKeyByName("G_USER_NATIVE_CODE", personInfo.getRegPermanentResidence());
            if (!StringUtils.isEmpty(regPermanentResidenceStr)) {
                personInfo.setRegPermanentResidence(regPermanentResidenceStr.replace(",", ""));
            }
            personIdList.add(personInfo.getId());
        }
        Workbook workbook = null;
        OutputStream os = null;
        try {
            //导入模板
            Template template = templateManager.get("900494158748712961");
            workbook = new XSSFWorkbook(sysFileManager.download(template.getFileId()));
            //构建基础页签数据
            Sheet sheet = workbook.getSheetAt(0);
            int i = 1;
            //学历字段查询
            List<BaseDTO> baseDTOList = sysDataDictMapper.selectSubListByUompEducation("UOMP_EDUCATION");
            Map<String, String> educationMap = new HashMap<>();
            for (BaseDTO baseDTO : baseDTOList) {
                educationMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //技术方向 TECHNICAL_DIRECTION
            List<BaseDTO> tecList = sysDataDictMapper.selectSubListByUompEducation("UOMP_TEC_DIRECTION");
            Map<String, String> techMap = new HashMap<>();
            for (BaseDTO baseDTO : tecList) {
                techMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //统招/非统招
            List<BaseDTO> educationForm = sysDataDictMapper.selectSubListByUompEducation("UOMP_EDUCATION_FORM");
            Map<String, String> educationFromMap = new HashMap<>();
            for (BaseDTO baseDTO : educationForm) {
                educationFromMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //查出资质类型的字典项
            List<BaseDTO> qualiftyType = sysDataDictMapper.selectSubListByUompEducation("UOMP_TECHNOLOGY_QUALIFTY_TYPE");
            Map<String, String> qualiftyTypeMap = new HashMap<>();
            for (BaseDTO baseDTO : qualiftyType) {
                qualiftyTypeMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //查出审核状态的字典项
            List<BaseDTO> trialStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_PEOPLE_INFO_MANAGEMENT_AUDIT_STATUS");
            Map<String, String> trialStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : trialStatusList) {
                trialStatusMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //查出人员状态的字典项
            List<BaseDTO> personStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_PERSON_STATUS");
            Map<String, String> personStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : personStatusList) {
                personStatusMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //查出背景审核状态的字典项
            List<BaseDTO> backgroundStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_BACKGROUND_STATUS");
            Map<String, String> backgroundStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : backgroundStatusList) {
                backgroundStatusMap.put(baseDTO.getId(), baseDTO.getName());
            }
            //查出驻场服务状态的字典项
            List<BaseDTO> enrtyStatusList = sysDataDictMapper.selectSubListByUompEducation("ENTRY_STATUS");
            Map<String, String> enrtyStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : enrtyStatusList) {
                enrtyStatusMap.put(baseDTO.getId(), baseDTO.getName());
            }
            for (UompPersonInfo personInfo : personInfoList) {
                //行对象
                Row row = sheet.createRow(i);
                Cell seq = row.createCell(0);
                seq.setCellValue(i + ""); //序号
                Cell person_name = row.createCell(1);
                person_name.setCellValue(personInfo.getPersonName()); //姓名
                Cell person_card = row.createCell(2);
                person_card.setCellValue(personInfo.getPersonCard());//身份证号
                Cell person_sex = row.createCell(3);
                person_sex.setCellValue(personInfo.getPersonSex().equals("0") ? "男" : "女"); //性别
                Cell person_birthday = row.createCell(4);
                person_birthday.setCellValue(personInfo.getPersonBirthday()); //出生年月
                Cell nationality = row.createCell(5);
                nationality.setCellValue(personInfo.getNationality()); //国籍
                Cell nation = row.createCell(6);
                nation.setCellValue(personInfo.getNation()); //民族
                Cell politics_status = row.createCell(7);
                politics_status.setCellValue(personInfo.getPoliticsStatus()); //政治面貌
                Cell reg_permanent_residence = row.createCell(8);
                reg_permanent_residence.setCellValue(personInfo.getRegPermanentResidence()); //户口所在地
                Cell tel = row.createCell(9);
                tel.setCellValue(personInfo.getTel()); //联系电话
                Cell email = row.createCell(10);
                email.setCellValue(personInfo.getEmail()); //邮箱
                Cell address = row.createCell(11);
                address.setCellValue(personInfo.getAddress()); //居住地址
                Cell educationCell = row.createCell(12);
                educationCell.setCellValue(educationMap.get(personInfo.getEducation()));// 学历
                Cell post = row.createCell(13);
                post.setCellValue(personInfo.getPost()); //工作职位
                Cell major = row.createCell(14);
                major.setCellValue(personInfo.getMajor()); //专业
                Cell working_company = row.createCell(15);
                working_company.setCellValue(personInfo.getWorkingCompany()); //就职公司
                Cell org_group_name = row.createCell(16);
                org_group_name.setCellValue(personInfo.getOrgGroupName()); //运维组织
                //技术方向转换
                Cell technical_direction = row.createCell(17);
                technical_direction.setCellValue(techMap.get(personInfo.getTechnicalDirection()));
                Cell entry_date = row.createCell(18);
                entry_date.setCellValue(personInfo.getEntryDate()); //入职时间
                Cell trial_status = row.createCell(19);
                trial_status.setCellValue(trialStatusMap.get(personInfo.getTrialStatus())); //审核状态
                Cell dimission_status = row.createCell(20);
                dimission_status.setCellValue(personStatusMap.get(personInfo.getDimission())); //人员状态
                Cell background_status = row.createCell(21);
                background_status.setCellValue(backgroundStatusMap.get(personInfo.getBackgroundStatus())); //背调审查状态
                Cell remark = row.createCell(22);
                remark.setCellValue(personInfo.getRemark()); //备注
                Cell entry_status = row.createCell(23);
                entry_status.setCellValue(enrtyStatusMap.get(personInfo.getEntryStatus())); //驻场服务状态
                i++;
            }

            // 获取页签配置信息
            UompPersonConfig personConfig = uompPersonConfigService.getConfigInfo("1");
            if (!StringUtils.isEmpty(personConfig.getConfigInfo())) {
                if (personConfig.getConfigInfo().contains("jybj")) {
                    List<UompPersonEducationalDto> educationals = uompPersonEducationalService.selectByPersonIds(personIdList);
                    //构建教育背景页签
                    Sheet sheet1 = workbook.getSheetAt(1);
                    i = 1;
                    for (UompPersonEducationalDto it : educationals) {
                        //行对象
                        Row row = sheet1.createRow(i);
                        Cell seq = row.createCell(0);
                        seq.setCellValue(i + ""); //序号
                        Cell person_name = row.createCell(1);
                        person_name.setCellValue(it.getPersonName()); //姓名
                        Cell person_card = row.createCell(2);
                        person_card.setCellValue(it.getPersonCard()); //身份证号
                        Cell education_time = row.createCell(3);
                        education_time.setCellValue(it.getEducationBeginTime()); //起始时间
                        Cell end_time = row.createCell(4);
                        end_time.setCellValue(it.getEducationEndTime()); //终止时间
                        Cell school = row.createCell(5);
                        school.setCellValue(it.getSchool());//就读院校
                        Cell major = row.createCell(6);
                        major.setCellValue(it.getMajor()); //就读专业
                        Cell education_background = row.createCell(7);
                        education_background.setCellValue(it.getEducationBackground()); //学历
                        Cell education_form = row.createCell(8);
                        education_form.setCellValue(educationFromMap.get(it.getEducationForm()));

                        Cell certificate_num = row.createCell(9);
                        certificate_num.setCellValue(it.getCertificateNum()); //证书编号
                        i++;
                    }
                }
                if (personConfig.getConfigInfo().contains("gzbj")) {
                    List<UompPersonJobDto> jobDtos = uompPersonJobService.selectByPersonIds(personIdList);
                    //构建工作背景页签
                    Sheet sheet2 = workbook.getSheetAt(2);
                    i = 1;
                    for (UompPersonJobDto it : jobDtos) {
                        //行对象
                        Row row = sheet2.createRow(i);
                        Cell seq = row.createCell(0);
                        seq.setCellValue(i + ""); //序号
                        Cell person_name = row.createCell(1);
                        person_name.setCellValue(it.getPersonName()); //姓名
                        Cell person_card = row.createCell(2);
                        person_card.setCellValue(it.getPersonCard()); //身份证号
                        Cell job_time = row.createCell(3);
                        job_time.setCellValue(it.getJobBeginTime()); //起始时间
                        Cell end_time = row.createCell(4);
                        end_time.setCellValue(it.getJobEndTime()); //终止时间
                        Cell company_name = row.createCell(5);
                        company_name.setCellValue(it.getCompanyName());//就职公司
                        Cell job_position = row.createCell(6);
                        job_position.setCellValue(it.getJobPosition()); //工作职位
                        Cell job_describe = row.createCell(7);
                        job_describe.setCellValue(it.getJobDescribe()); //工作描述
                        i++;
                    }
                }
                if (personConfig.getConfigInfo().contains("jszz")) {
                    List<UompPersonTechnologyDto> technologyDtos = uompPersonTechnologyService.selectByPersonIds(personIdList);
                    //构建技术资质页签
                    Sheet sheet3 = workbook.getSheetAt(3);
                    i = 1;
                    for (UompPersonTechnologyDto it : technologyDtos) {
                        //行对象
                        Row row = sheet3.createRow(i);
                        Cell seq = row.createCell(0);
                        seq.setCellValue(i + ""); //序号
                        Cell person_name = row.createCell(1);
                        person_name.setCellValue(it.getPersonName()); //姓名
                        Cell person_card = row.createCell(2);
                        person_card.setCellValue(it.getPersonCard()); //身份证号
                        Cell get_time = row.createCell(3);
                        get_time.setCellValue(it.getGetTime()); //获取时间
                        Cell qualifty_name = row.createCell(4);
                        qualifty_name.setCellValue(it.getQualiftyName());//资质名称
                        Cell qualifty_type = row.createCell(5);
                        qualifty_type.setCellValue(qualiftyTypeMap.get(it.getQualiftyType()));
                        Cell certification_body = row.createCell(6);
                        certification_body.setCellValue(it.getCertificationBody()); //颁证机构
                        Cell start_time = row.createCell(7);
                        start_time.setCellValue(it.getStartTime()); //起始时间
                        Cell end_time = row.createCell(8);
                        end_time.setCellValue(it.getEndTime()); //起始时间
                        i++;
                    }
                }
                if (personConfig.getConfigInfo().contains("shgx")) {
                    List<UompPersonSocialDto> socialDtos = uompPersonSocialService.selectByPersonIds(personIdList);
                    //构建社会关系页签
                    Sheet sheet4 = workbook.getSheetAt(4);
                    i = 1;
                    for (UompPersonSocialDto it : socialDtos) {
                        //行对象
                        Row row = sheet4.createRow(i);
                        Cell seq = row.createCell(0);
                        seq.setCellValue(i + ""); //序号
                        Cell person_name = row.createCell(1);
                        person_name.setCellValue(it.getPersonName()); //姓名
                        Cell person_card = row.createCell(2);
                        person_card.setCellValue(it.getPersonCard()); //身份证号
                        Cell rela_name = row.createCell(3);
                        rela_name.setCellValue(it.getRelaName()); //关系人姓名
                        Cell rela_age = row.createCell(4);
                        rela_age.setCellValue(it.getRelaAge());//年龄
                        Cell national = row.createCell(5);
                        national.setCellValue(it.getNational()); //国籍
                        Cell politics_status = row.createCell(6);
                        politics_status.setCellValue(it.getPoliticsStatus()); //政治面貌
                        Cell relation_with_myself = row.createCell(7);
                        relation_with_myself.setCellValue(it.getRelationWithMyself()); //与本人关系
                        Cell rela_tel = row.createCell(8);
                        rela_tel.setCellValue(it.getRelaTel()); //联系方式
                        Cell rela_post = row.createCell(9);
                        rela_post.setCellValue(it.getRelaPost()); //工作单位及职务
                        Cell rela_address = row.createCell(10);
                        rela_address.setCellValue(it.getRelaAddress()); //居住地
                        i++;
                    }
                    if (personConfig.getConfigInfo().contains("wfzjl")) {
                        List<UompPersonNoCrimeDto> noCrimeDtos = uompPersonNoCrimeService.selectByPersonIds(personIdList);
                        //构建无犯罪证明
                        Sheet sheet5 = workbook.getSheetAt(5);
                        i = 1;
                        for (UompPersonNoCrimeDto it : noCrimeDtos) {
                            //行对象
                            Row row = sheet5.createRow(i);
                            Cell seq = row.createCell(0);
                            seq.setCellValue(i + ""); //序号
                            Cell person_name = row.createCell(1);
                            person_name.setCellValue(it.getPersonName()); //姓名
                            Cell person_card = row.createCell(2);
                            person_card.setCellValue(it.getPersonCard()); //身份证号
                            Cell proof_number = row.createCell(3);
                            proof_number.setCellValue(it.getProofNumber()); //证明编号
                            Cell query_time = row.createCell(4);
                            query_time.setCellValue(it.getQueryBeginTime());//查询周期
                            Cell end_time = row.createCell(5);
                            end_time.setCellValue(it.getQueryEndTime()); //终止时间
                            Cell provide_unit = row.createCell(6);
                            provide_unit.setCellValue(it.getProvideUnit()); //出具单位
                            Cell indate = row.createCell(7);
                            indate.setCellValue(it.getIndate());//有效期
                            i++;
                        }
                    }
                    if (personConfig.getConfigInfo().contains("cgzj")) {
                        List<UompPersonAbroadDto> abroadDtos = uompPersonAbroadService.selectByPersonIds(personIdList);
                        Sheet sheet6 = workbook.getSheetAt(6);
                        i = 1;
                        for (UompPersonAbroadDto it : abroadDtos) {
                            //行对象
                            Row row = sheet6.createRow(i);
                            Cell seq = row.createCell(0);
                            seq.setCellValue(i + ""); //序号
                            Cell person_name = row.createCell(1);
                            person_name.setCellValue(it.getPersonName()); //姓名
                            Cell person_card = row.createCell(2);
                            person_card.setCellValue(it.getPersonCard()); //身份证号
                            Cell certificate_name = row.createCell(3);
                            certificate_name.setCellValue(it.getCertificateName()); //证件名称
                            Cell certificate_num = row.createCell(4);
                            certificate_num.setCellValue(it.getCertificateNum());//证件号码
                            Cell issue_at = row.createCell(5);
                            issue_at.setCellValue(it.getIssueAt()); //签发地
                            Cell start_end_time = row.createCell(6);
                            start_end_time.setCellValue(it.getStartTime());//有效期
                            Cell end_time = row.createCell(7);
                            end_time.setCellValue(it.getEndTime()); //终止时间
                            i++;
                        }
                    }
                    if (personConfig.getConfigInfo().contains("crjjl")) {
                        List<UompPersonEntryExitDto> entryExitDtos = uompPersonEntryExitService.selectByPersonIds(personIdList);
                        Sheet sheet7 = workbook.getSheetAt(7);
                        i = 1;
                        for (UompPersonEntryExitDto it : entryExitDtos) {
                            //行对象
                            Row row = sheet7.createRow(i);
                            Cell seq = row.createCell(0);
                            seq.setCellValue(i + ""); //序号
                            Cell person_name = row.createCell(1);
                            person_name.setCellValue(it.getPersonName()); //姓名
                            Cell person_card = row.createCell(2);
                            person_card.setCellValue(it.getPersonName()); //身份证号
                            Cell entry_exit = row.createCell(3);
                            entry_exit.setCellValue(it.getEntryExit()); //出境/入境
                            Cell entry_exit_time = row.createCell(4);
                            entry_exit_time.setCellValue(it.getEntryExitTime());//出入境日期
                            Cell certificate_name = row.createCell(5);
                            certificate_name.setCellValue(it.getCertificateName()); //证件名称
                            Cell certificate_num = row.createCell(6);
                            certificate_num.setCellValue(it.getCertificateNum());//证件号码
                            Cell entry_exit_ports = row.createCell(7);
                            entry_exit_ports.setCellValue(it.getEntryExitPorts());//出入境口岸
                            i++;
                        }
                    }
                }
            }
            //把文件送到用户端
            String downName = "人员信息.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            if (workbook != null) {
                workbook.close();
            }
        }
        return response;
    }

    @RequestMapping("/downloadTemplate/{id}")
    public ResponseEntity<byte[]> downloadTemplate(HttpServletRequest request,
                                                   HttpServletResponse response,
                                                   @PathVariable("id") String id) throws Exception {
        Template template = templateManager.get(id);
        HttpHeaders headers = new HttpHeaders();
        String downloadFileNam = URLEncoder.encode(template.getFileName(), "UTF-8").replace("+", "%20");
        downloadFileNam = downloadFileNam.replaceAll("\r|\n|\t", "");
        headers.setContentDispositionFormData("attachment", downloadFileNam);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(IOUtils.toByteArray(sysFileManager.download(template.getFileId())), headers, HttpStatus.OK);
    }

    @ApiOperation(value = "人员信息导入接口")
    @RequestMapping(value = "/upload")
    public ResultMsg<String> upload(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(value = "file") MultipartFile file) throws Exception {

        List<Map<String, String>> errorList = uompPersonInfoService.importPersons(file);
        if (!errorList.isEmpty()) {
            //导出数据
            // 第一个对象，新建一个工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 设置第一个sheet的名称
            Sheet sheet = workbook.createSheet("sheet1");

            // 开始添加excel第一行表头（excel中下标是0）
            Row row = sheet.createRow(0);
            sheet.setDefaultColumnWidth(16);//宽

            row.setHeightInPoints(20);//行高

            String[] fieldList = {"序号", "页签名称", "行数", "列数", "错误原因"};
            int i = 0;
            // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
            for (String it : fieldList) {
                // 创建一个单元格
                Cell cell = row.createCell(i);
                // 设置单元格的样式
                CellStyle cellStyle = workbook.createCellStyle();
                //设置字体
                Font font = workbook.createFont();
                //设置字号
                font.setFontHeightInPoints((short) 14);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                // 将数据放入excel的单元格中
                cell.setCellValue(it);
                i++;
            }

            // 开始创建excel单元格数据，从第二行开始（excel下标是1）
            int rowNum = 1;
            // 添加excel行数据的集合（你自己的数据集合遍历）
            for (Map<String, String> it : errorList) {
                // 创建一个单元格
                Row row1 = sheet.createRow(rowNum);
                // 设置行的高度
                row1.setHeightInPoints(16);
                //填写单元格
                row1.createCell(0).setCellValue(rowNum + "");//序号
                row1.createCell(1).setCellValue(it.get("sheet"));//行
                row1.createCell(2).setCellValue(it.get("row"));//行
                row1.createCell(3).setCellValue(it.get("column"));//列
                row1.createCell(4).setCellValue(it.get("message"));//原因

                rowNum++;
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                //把文件送到用户端
                workbook.write(bos);
                byte[] brray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(brray);
                SysFile upload = sysFileManager.upload(is, "错误信息-" + IdUtil.getSuid() + ".xlsx");
                return ResultMsg.ERROR(upload.getId());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放资源
                if (bos != null) {
                    bos.flush();
                    bos.close();
                }
                workbook.close();
            }
        } else {
            return ResultMsg.SUCCESS("导入成功!");
        }
        throw new BusinessException("导入失败!");
    }

    private List<Map<String, String>> makeErrorList(List<Map<String, String>> errorList, String sheet,
                                                    int row, String column, String message) {
        Map<String, String> errorMap = new HashMap<>();
        errorMap.put("sheet", sheet);
        errorMap.put("row", "第" + (row + 1) + "行");
        errorMap.put("column", column);
        errorMap.put("message", message);
        errorList.add(errorMap);
        return errorList;
    }

    @ApiOperation(value = "根据id修改人员信息状态接口")
    @RequestMapping(value = "/updateStatusById")
    public ResultMsg<String> updateStatusById(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam(value = "id") @ApiParam(value = "id") String id,
                                              @RequestParam(value = "status") @ApiParam(value = "状态") String status,
                                              @RequestParam(value = "instId", required = false) @ApiParam(value = "流程实例id") String instId) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompPersonInfo personInfo = new UompPersonInfo();
        personInfo.setId(id);
        personInfo.setTrialStatus(status);
        if (!StringUtils.isEmpty(instId)) {
            personInfo.setInstId(instId);
        }
        personInfo.setUpdateTime(new Date());
        personInfo.setUpdateBy(user.getUserId());
        personInfo.setUpdateOrgId(user.getOrgId());
        uompPersonInfoService.updateByPrimaryKeySelective(personInfo);
        return ResultMsg.SUCCESS("更新成功!");
    }

    @ApiOperation(value = "人员背调情况饼图")
    @RequestMapping(value = "/backgroundInfoPie")
    public ResultMsg<List<PercentageDTO>> backgroundInfoPie() {
        return getSuccessResult(uompPersonInfoService.backgroundInfoPie());
    }

    @ApiOperation(value = "人员背调情况柱状图")
    @RequestMapping(value = "/backgroundInfoColumnar")
    public ResultMsg<ColumnarDTO> backgroundInfoColumnar() {
        return getSuccessResult(uompPersonInfoService.backgroundInfoColumnar());
    }


    @ApiOperation(value = "人员在职/学历情况")
    @RequestMapping(value = "/personWorkAndEdu")
    public ResultMsg<PersonWorkAndEduDTO> personWorkAndEdu() {
        return getSuccessResult(uompPersonInfoService.personWorkAndEdu());
    }

    @ApiOperation(value = "人员,驻场数量")
    @RequestMapping(value = "/personEntryNumber")
    public ResultMsg<PersonEntryNumberDTO> personEntryNumber(@RequestParam(value = "inTimeStart") @ApiParam(value = "开始时间") String inTimeStart,
                                                             @RequestParam(value = "inTimeEnd") @ApiParam(value = "结束时间") String inTimeEnd) {
        PersonEntryNumberDTO personEntryNumberDTO = new PersonEntryNumberDTO();
        personEntryNumberDTO.setPersonNumber(uompTempAdmissionService.countByrealVisitTime(inTimeStart, inTimeEnd));
        personEntryNumberDTO.setEntryNumber(uompAdmissionPersonService.countByInTime(inTimeStart, inTimeEnd));
        return getSuccessResult(personEntryNumberDTO);
    }

    @RequestMapping(value ="/personCardEncrypt")
    public ResultMsg<String> personCardEncrypt(HttpServletRequest request,@RequestParam(value = "ids",required = false)String ids){
        uompPersonInfoService.batchEncrypt(ids);
        return getSuccessResult();
    }

    @GetMapping("/decryptString")
    public ResultMsg<String> decryptString(@RequestParam(value =  "id")String id,@RequestParam("attr")String attr){
        return getSuccessResult(uompPersonInfoService.decryptString(id,attr));
    }
}
