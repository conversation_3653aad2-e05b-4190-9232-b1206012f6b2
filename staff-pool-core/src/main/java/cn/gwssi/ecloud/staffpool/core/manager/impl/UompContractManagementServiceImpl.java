package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.UompContractFileSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractFileVO;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.UompContractManagementSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractResourceSave;
import cn.gwssi.ecloud.staffpool.api.model.UompContractResourceVO;
import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompApplicationSystemRelationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.CmdbCommResourceBase;
import cn.gwssi.ecloud.staffpool.dto.SupplierContractDTO;
import cn.gwssi.ecloud.staffpool.dto.UompContractManagementListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO;
import cn.gwssi.ecloud.staffpool.task.RefreshContractJob;
import cn.gwssi.ecloud.staffpool.util.DateUtils;
import cn.gwssi.ecloudframework.base.api.Page;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultPage;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.gwssi.ecloud.staffpool.core.dao.UompContractManagementMapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UompContractManagementServiceImpl extends BaseManager<String, UompContractManagement> implements UompContractManagementService {

    @Resource
    private UompContractManagementMapper uompContractManagementMapper;
    @Autowired
    private CmdbCommResourceService cmdbCommResourceService;
    @Autowired
    private UompContractFileService uompContractFileService;
    @Autowired
    private SysDataDictMapper sysDataDictMapper;
    @Autowired
    private UompApplicationSystemRelationMapper uompApplicationSystemRelationMapper;
    @Autowired
    private RefreshContractJob refreshContractJob;
    @Autowired
    private UompContractResourceService uompContractResourceService;

    @Autowired
    private UompPartyInfoService uompPartyInfoService;
    // 资源字典
    public final static String RESOURCE_STATUS = "UOMP_RESOURCE_STATUS";
    // 设备字典
    public final static String DEVICE_STATUS = "UOMP_DEVICE_STATUS";
    private SimpleDateFormat sdf1 = new SimpleDateFormat(DateUtils.yyyyMMddHHmm);
    private SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.yyyyMMddHHmmss);

    @Override
    public int insertSelective(UompContractManagement record) {
        int records = uompContractManagementMapper.insertSelective(record);
        if (CollectionUtils.isNotEmpty(record.getPartyInfoList())){
            record.getPartyInfoList().forEach(uompPartyInfo -> {
                uompPartyInfo.setContractManagementId(record.getId());
                uompPartyInfoService.insert(uompPartyInfo);
            });
        }
        return records;
    }

    @Override
    public int updateByPrimaryKey(UompContractManagement record) {
        uompPartyInfoService.delete(record.getId());
        if (CollectionUtils.isNotEmpty(record.getPartyInfoList())){
            record.getPartyInfoList().forEach(uompPartyInfo -> {
                uompPartyInfo.setContractManagementId(record.getId());
                uompPartyInfoService.insert(uompPartyInfo);
            });
        }
        return uompContractManagementMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageResult getContractList(UompContractManagementQueryVO uompContractManagementQueryVO) {
        if (null == uompContractManagementQueryVO.getPageNo()) {
            uompContractManagementQueryVO.setPageNo(1);
        }
        if (null == uompContractManagementQueryVO.getPageSize()) {
            uompContractManagementQueryVO.setPageSize(10);
        }
        QueryFilter queryFilter = new DefaultQueryFilter();
        // 设置分页
        Page page = new DefaultPage(uompContractManagementQueryVO.getPageNo(), uompContractManagementQueryVO.getPageSize(), new ArrayList<>(), true);
        queryFilter.setPage(page);
        // 合同编码
        String contractCode = uompContractManagementQueryVO.getContractCode();
        if (StringUtils.isNotEmpty(contractCode)) {
            queryFilter.addFilter("CONTRACT_CODE", contractCode, QueryOP.LIKE);
        }
        // 合同名称
        String contractName = uompContractManagementQueryVO.getContractName();
        if (StringUtils.isNotEmpty(contractName)) {
            queryFilter.addFilter("CONTRACT_NAME", contractName, QueryOP.LIKE);
        }
        // 签约开始时间
        Date signingDateBegin = uompContractManagementQueryVO.getSigningDateBegin();
        if (null != signingDateBegin) {
            queryFilter.addFilter("SIGNING_DATE", signingDateBegin, QueryOP.GREAT_EQUAL);
        }
        // 签约结束时间
        Date signingDateEnd = uompContractManagementQueryVO.getSigningDateEnd();
        if (null != signingDateEnd) {
            queryFilter.addFilter("SIGNING_DATE", signingDateEnd, QueryOP.LESS_EQUAL);
        }
        Date qualityStartBeginDay = uompContractManagementQueryVO.getQualityStartBeginDay();
        if (null != qualityStartBeginDay){
            queryFilter.addFilter("QUALITY_BEGIN_DAY", qualityStartBeginDay, QueryOP.GREAT_EQUAL);
        }

        Date qualityEndBeginDay = uompContractManagementQueryVO.getQualityEndBeginDay();
        if (null != qualityEndBeginDay){
            queryFilter.addFilter("QUALITY_BEGIN_DAY", qualityEndBeginDay, QueryOP.LESS_EQUAL);
        }

        Date qualityStartEndDay = uompContractManagementQueryVO.getQualityStartEndDay();
        if (null != qualityStartEndDay){
            queryFilter.addFilter("QUALITY_END_DAY", qualityStartEndDay, QueryOP.GREAT_EQUAL);
        }
        Date qualityEndEndDay = uompContractManagementQueryVO.getQualityEndEndDay();
        if (null != qualityEndEndDay){
            queryFilter.addFilter("QUALITY_END_DAY", qualityEndEndDay, QueryOP.LESS_EQUAL);
        }
        // 甲方
        String partyAName = uompContractManagementQueryVO.getPartyAname();
        if (StringUtils.isNotEmpty(partyAName)) {
            queryFilter.addFilter("PARTY_A_NAME", partyAName, QueryOP.LIKE);
        }
        // 乙方
        String partyBName = uompContractManagementQueryVO.getPartyBname();
        if (StringUtils.isNotEmpty(partyBName)) {
            queryFilter.addFilter("PARTY_B_NAME", partyBName, QueryOP.LIKE);
        }
        // 合同状态
        String contractStatus = uompContractManagementQueryVO.getContractStatus();
        if (StringUtils.isNotEmpty(contractStatus)) {
            queryFilter.addFilter("CONTRACT_STATUS", contractStatus, QueryOP.IN);
        }
        // 合同类型
        String contractType = uompContractManagementQueryVO.getContractType();
        if (StringUtils.isNotEmpty(contractType)) {
            queryFilter.addFilter("CONTRACT_TYPE", contractType, QueryOP.IN);
        }
        // 服务商id
        String supplierId = uompContractManagementQueryVO.getSupplierManagementId();
        if (StringUtils.isNotEmpty(supplierId)) {
            queryFilter.addFilter("PARTY_B_ID", supplierId, QueryOP.EQUAL);
        }
        // 甲方负责人
        String partyAPrincipalName = uompContractManagementQueryVO.getPartyAPrincipalName();
        if (StringUtils.isNotEmpty(partyAPrincipalName)) {
            queryFilter.addFilter("PARTY_A_PRINCIPAL_NAME", partyAPrincipalName, QueryOP.LIKE);
        }
        // 乙方负责人
        String partyBPrincipalName = uompContractManagementQueryVO.getPartyBPrincipalName();
        if (StringUtils.isNotEmpty(partyBPrincipalName)) {
            queryFilter.addFilter("PARTY_B_PRINCIPAL_NAME", partyBPrincipalName, QueryOP.LIKE);
        }

        // 未删除的合同
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        String sortField = uompContractManagementQueryVO.getSortField();
        String sortType = uompContractManagementQueryVO.getSortType();
        // 解析自定义签约日期排序方式
        if (StringUtils.isNotEmpty(sortField) && StringUtils.isNotEmpty(sortType)) {
                if ("descending".equals(sortType)) {
                    queryFilter.addFieldSort(sortField, "desc");
                } else if ("ascending".equals(sortType)) {
                    queryFilter.addFieldSort(sortField, "asc");
            }
        } else {
            queryFilter.addFieldSort("qualityEndDay", "asc");
        }
        List<UompContractManagement> list = uompContractManagementMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        List<UompContractManagementListDTO> dtoList = getSystemJsonList(list);
        pageResult.setRows(dtoList);
        return pageResult;
    }

    @Override
    public String save(UompContractManagementSave uompContractManagementSave) {
        // 获取当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        String fullname = user.getFullname();
        //质保日期校验
        Date qualityBeginDay = uompContractManagementSave.getQualityBeginDay();
        Date qualityEndDay = uompContractManagementSave.getQualityEndDay();
        if (null != qualityBeginDay && null != qualityEndDay) {
            if (qualityEndDay.getTime() <= qualityBeginDay.getTime()) {
                throw new BusinessMessage("质保结束日期应该大于质保开始日期！");
            }
        }
        String id = uompContractManagementSave.getId();
        // 合同编码
        String contractCode = uompContractManagementSave.getContractCode();
        // 合同状态 1-暂存 2-生效 3-正常  4-临期 5-失效 6-过保
        String contractStatus = uompContractManagementSave.getContractStatus();
        // 应用系统id
        String systemId = uompContractManagementSave.getProjectManagementId();
        if (StringUtils.isNotEmpty(id)) { // 更新
            //合同编号唯一校验
            UompContractManagement uompContractManagement = uompContractManagementMapper.get(id);
            if (null != uompContractManagement) {
                String oldContractCode = uompContractManagement.getContractCode();
                //如果合同编号改变，才校验唯一性
                if (!oldContractCode.equals(contractCode)) {
                    // 查询新编号是否已存在
                    UompContractManagement ucm = uompContractManagementMapper.selectInfoByContractCode(contractCode);
                    if (null != ucm) {
                        throw new BusinessMessage("该合同编号已存在，不可重复，请仔细检查！");
                    }
                }
            } else {
                throw new BusinessMessage("合同不存在！");
            }
            String oldSystemId = uompContractManagement.getProjectManagementId();
            //只有在生效按钮的时候才做校验
            String isEffect = uompContractManagementSave.getIsEffect();
            if (StringUtils.isNotEmpty(isEffect)) {
                Date date = new Date();
                //合同状态判断-只针对生效进行判断，生效传的合同状态为  3-正常，其余暂存，失效不予判断
                if ("3".equals(contractStatus)) {
                    //1.判断合同是否有维保结束日期
                    contractStatus = "3";
                    if (null != qualityEndDay) {
                        //如果有时间，且时间小于当前时间，则提示失效，不可生效
                        //结束时间按照23:59:59算
                        SimpleDateFormat sdf1 = new SimpleDateFormat(DateUtils.yyyyMMdd);
                        SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.yyyyMMddHHmmss);
                        try {
                            Date endDay = sdf2.parse(sdf1.format(qualityEndDay) + " 23:59:59");
                            if (date.after(endDay)) {
                                contractStatus = "5";
                            }
                        } catch (ParseException pe) {
                            log.warn("合同签订结束时间转换日期失败！");
                        }
                    }
                    if (!contractStatus.equals("5")) {
                        //2。如果通过失效校验，则校验资产。
                        //查询合同所关联资产
                        QueryFilter queryFilter = new DefaultQueryFilter(true);
                        // 未删除
                        queryFilter.addFilter("t.DEL_FLAG", "0", QueryOP.EQUAL);
                        // 合同id
                        queryFilter.addFilter("t.CONTRACT_ID", id, QueryOP.EQUAL);
                        // 审核状态：审核通过 0暂存1待审核2审核通过3审核不通过
                        queryFilter.addFilter("t.AUDIT_STATE", "2", QueryOP.EQUAL);
                        // 资产状态：正常
                        queryFilter.addFilter("t.RESOURCE_STATE", "2", QueryOP.NOT_EQUAL);
                        List<CmdbCommResource> list = cmdbCommResourceService.selectInfoByContractId(queryFilter);
                        List<CmdbCommResourceBase> baseList = new ArrayList<>();
                        for (CmdbCommResource cmdbCommResource : list) {
                            CmdbCommResourceBase base = new CmdbCommResourceBase();
                            BeanUtils.copyProperties(cmdbCommResource, base);
                            //status : 1-正常 2-过保 3-临期
                            Date tendDateStart = cmdbCommResource.getTendDateStart();
                            Date tendDateEnd = cmdbCommResource.getTendDateEnd();
                            if (null != tendDateStart && null != tendDateEnd) {
                                Date endTime = new Date();
                                try {
                                    //结束时间按照23:59:59算
                                    SimpleDateFormat sdf1 = new SimpleDateFormat(DateUtils.yyyyMMdd);
                                    SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.yyyyMMddHHmmss);
                                    endTime = sdf2.parse(sdf1.format(tendDateEnd) + " 23:59:59");
                                } catch (ParseException pe) {
                                    log.warn("结束时间转换日期失败！");
                                }
                                //超过结束时间就是过保，开始时间大于当前时间，结束时间小于结束时间，且7天内的就是临期，剩下统一为正常
                                if (date.after(endTime)) {
                                    base.setStatus("2");
                                } else if ((date.after(tendDateStart) || date.equals(tendDateStart)) && (date.before(tendDateEnd) || date.equals(tendDateEnd))) {
                                    long diff = tendDateEnd.getTime() - date.getTime();
                                    long day = diff / (24 * 60 * 60 * 1000);
                                    if (day < 7) {
                                        base.setStatus("3");
                                    } else {
                                        base.setStatus("1");
                                    }
                                } else {
                                    base.setStatus("1");
                                }
                            } else {
                                base.setStatus("1");
                            }
                            baseList.add(base);
                        }
                        //根据资产数据判断合同状态是否更新 合同状态 1-暂存 2-生效 3-正常  4-临期 5-失效 6-过保
                        //1.如果没有资产，状态为正常(不变)
                        if (CollectionUtils.isNotEmpty(baseList)) {
                            boolean flag = true;
                            //2.非废弃资产有过保,则合同状态为过保;
                            for (CmdbCommResourceBase base : baseList) {
                                String status = base.getStatus();
                                if ("2".equals(status)) {
                                    contractStatus = "6";
                                    flag = false;
                                    break;
                                }
                            }
                            //3.非废弃资产无过保有临期，则合同状态为临期,如果最后也没有临期，则为正常，状态不变
                            //如果没有过保的数据，就查看是否有临期数据，flag = true 就是没有过保数据
                            if (flag) {
                                int index = 0;
                                while (index < baseList.size()) {
                                    CmdbCommResourceBase base = baseList.get(index);
                                    String status = base.getStatus();
                                    if ("3".equals(status)) {
                                        contractStatus = "4";
                                        break;
                                    }
                                    index++;
                                }
                            }
                        }
                    }
                    uompContractManagementSave.setContractStatus(contractStatus);
                }
            }
            BeanUtils.copyProperties(uompContractManagementSave, uompContractManagement);
            uompContractManagement.setUpdateBy(userId);
            uompContractManagement.setUpdateTime(new Date());
            uompContractManagement.setUpdateOrgId(orgId);
            uompContractManagementMapper.updateByPrimaryKey(uompContractManagement);
            // 删除老的合同-应用系统关联关系
            if (StringUtils.isNotEmpty(oldSystemId)) {
                QueryFilter contractQueryFilter = new DefaultQueryFilter(true);
                // 应用系统id
                contractQueryFilter.addFilter("APPLICATION_SYSTEM_MANAGEMENT_ID", oldSystemId, QueryOP.EQUAL);
                // 合同id
                contractQueryFilter.addFilter("RELATION_ID", id, QueryOP.EQUAL);
                // 类型： contract(合同)
                contractQueryFilter.addFilter("RELATION_TYPE", "contract", QueryOP.EQUAL);
                // 未删除
                contractQueryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompApplicationSystemRelation> deleteList = uompApplicationSystemRelationMapper.query(contractQueryFilter);
                if (CollectionUtils.isNotEmpty(deleteList)) {
                    String ids = deleteList.stream().map(item -> item.getId()).collect(Collectors.joining(","));
                    QueryFilter deleteFilter = new DefaultQueryFilter(true);
                    deleteFilter.addFilter("ID", ids, QueryOP.IN);
                    uompApplicationSystemRelationMapper.updateDelFlagByIds(deleteFilter);
                }
            }
            // 查询合同id与应用系统id之间是否已有关联
            if (StringUtils.isNotEmpty(systemId)) {
                QueryFilter contractQueryFilter = new DefaultQueryFilter(true);
                contractQueryFilter.addFilter("APPLICATION_SYSTEM_MANAGEMENT_ID", systemId, QueryOP.EQUAL);
                contractQueryFilter.addFilter("RELATION_ID", id, QueryOP.EQUAL);
                contractQueryFilter.addFilter("RELATION_TYPE", "contract", QueryOP.EQUAL);
                contractQueryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                List<UompApplicationSystemRelation> newList = uompApplicationSystemRelationMapper.query(contractQueryFilter);
                if (CollectionUtils.isEmpty(newList)) {
                    // 添加应用系统-合同关联关系
                    this.addSystemAndContractRelation(systemId, id, userId, orgId);
                }
            }
            //维护乙方联合体数据
            uompPartyInfoService.delete(uompContractManagement.getId());
            if(CollectionUtils.isNotEmpty(uompContractManagement.getPartyInfoList())){
                uompContractManagementSave.getPartyInfoList().forEach(uompPartyInfoVo -> {
                    UompPartyInfo uompPartyInfo = BeanCopierUtils.transformBean(uompPartyInfoVo, UompPartyInfo.class);
                    uompPartyInfo.setContractManagementId(uompContractManagement.getId());
                    uompPartyInfoService.insert(uompPartyInfo);
                });
            }
        } else {  // 保存
            //合同编号唯一校验
            // 查询新编号是否已存在
            UompContractManagement ucm = uompContractManagementMapper.selectInfoByContractCode(contractCode);
            if (null != ucm) {
                throw new BusinessMessage("该合同编号已存在，不可重复，请仔细检查！");
            }
            //第一次保存，对直接生效的情况进行维保时间判断
            if (StringUtils.isNotEmpty(uompContractManagementSave.getIsEffect())) {
                Date date = new Date();
                //合同状态判断-只针对生效进行判断，生效传的合同状态为  3-正常，其余暂存，失效不予判断
                if ("3".equals(contractStatus)) {
                    //1.判断合同是否有维保结束日期，
                    Date endTime = uompContractManagementSave.getQualityEndDay();
                    if (null != endTime) {
                        //如果有时间，且时间小于当前时间，则提示失效，不可生效
                        //结束时间按照23:59:59算
                        try {
                            SimpleDateFormat sdf1 = new SimpleDateFormat(DateUtils.yyyyMMdd);
                            SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtils.yyyyMMddHHmmss);
                            Date endDay = sdf2.parse(sdf1.format(endTime) + " 23:59:59");
                            if (date.after(endDay)) {
                                uompContractManagementSave.setContractStatus("5");
                            }
                        } catch (ParseException pe) {
                            log.warn("结束时间转换日期失败！");
                        }
                    }
                }
            }
            //插入
            id = IdUtil.getSuid();
            //插入新数据状态为 1-暂存
            UompContractManagement save = new UompContractManagement();
            BeanUtils.copyProperties(uompContractManagementSave, save);
            save.setCreateBy(userId);
            save.setCreateTime(new Date());
            save.setCreateOrgId(orgId);
            save.setDelFlag("0");
            save.setId(id);
            // 添加录入人
            save.setEntryId(userId);
            save.setEntryName(fullname);
            uompContractManagementMapper.insertSelective(save);
            if (CollectionUtils.isNotEmpty(save.getPartyInfoList())){
                save.getPartyInfoList().forEach(uompPartyInfo -> {
                    uompPartyInfo.setContractManagementId(save.getId());
                    uompPartyInfoService.insert(uompPartyInfo);
                });
            }
            // 添加应用系统-合同关联关系（合同状态：正常  存在应用系统）
            if (StringUtils.isNotEmpty(systemId)) {
                this.addSystemAndContractRelation(systemId, id, userId, orgId);
            }
        }
        return id;
    }

    // 添加应用系统-合同关联关系
    private void addSystemAndContractRelation(String systemId, String contractId, String createBy, String createOrgId) {
        UompApplicationSystemRelation relation = new UompApplicationSystemRelation();
        relation.setId(IdUtil.getSuid());
        relation.setApplicationSystemManagementId(systemId);
        relation.setRelationId(contractId);
        relation.setRelationType("contract");
        relation.setCreateTime(new Date());
        relation.setCreateBy(createBy);
        relation.setCreateOrgId(createOrgId);
        relation.setDelFlag("0");
        uompApplicationSystemRelationMapper.insertSelective(relation);
    }

    @Override
    public void deleteContract(String id) {
        // 获取当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        // 更新合同信息 delFlag 0未删除 1已删除（逻辑删除）
        UompContractManagement uompContractManagement = new UompContractManagement();
        uompContractManagement.setDelFlag("1");
        uompContractManagement.setUpdateOrgId(orgId);
        uompContractManagement.setUpdateBy(userId);
        uompContractManagement.setUpdateTime(new Date());
        uompContractManagement.setId(id);
        uompContractManagementMapper.updateByPrimaryKey(uompContractManagement);
        // 更新合同信息关联的合同文件 delFlag 0未删除 1已删除（逻辑删除）
        UompContractFile uompContractFile = new UompContractFile();
        uompContractFile.setUpdateOrgId(orgId);
        uompContractFile.setUpdateBy(userId);
        uompContractFile.setUpdateTime(new Date());
        uompContractFile.setContractManagementId(id);
        uompContractFile.setDelFlag("1");
        uompContractFileService.updateDelFlagByContractManagementId(uompContractFile);
        // todo 20241024 删除合同关联资源服务信息
        uompContractResourceService.deleteByContractId(id);
        // 删除合同-应用系统关联
        QueryFilter deleteFilter = new DefaultQueryFilter(true);
        deleteFilter.addFilter("RELATION_ID", id, QueryOP.EQUAL);
        deleteFilter.addFilter("RELATION_TYPE", "contract", QueryOP.EQUAL);
        uompApplicationSystemRelationMapper.updateDelFlagByIds(deleteFilter);
        //删除乙方联合体
        uompPartyInfoService.delete(uompContractManagement.getId());
    }

    @Override
    public void saveFile(UompContractFileSave uompContractFileSave) {
        // 获取当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        String fullname = user.getFullname();
        String contractManagementId = uompContractFileSave.getContractManagementId();
        List<UompContractFileVO> list = uompContractFileSave.getFileInfos();
        if (CollectionUtils.isNotEmpty(list)) {
            for (UompContractFileVO vo : list) {
                // 添加合同关联的合同文件信息
                String fileFullName = vo.getFileFullName();
                String fileId = vo.getFileId();
                String id = IdUtil.getSuid();
                UompContractFile uompContractFile = new UompContractFile();
                uompContractFile.setDelFlag("0");
                uompContractFile.setContractManagementId(contractManagementId);
                uompContractFile.setFileId(fileId);
                uompContractFile.setFileName(fileFullName);
                uompContractFile.setUploaderId(userId);
                uompContractFile.setUploaderName(fullname);
                uompContractFile.setCreateOrgId(orgId);
                uompContractFile.setCreateBy(userId);
                uompContractFile.setUploadTime(new Date());
                uompContractFile.setCreateTime(new Date());
                uompContractFile.setId(id);
                uompContractFileService.insertSelective(uompContractFile);
            }
        }
    }

    @Override
    public PageResult getFileList(QueryFilter queryFilter) {
        List<UompContractFile> list = uompContractFileService.query(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            return new PageResult(list);
        }
        return new PageResult(new ArrayList());
    }

    @Override
    public void deleteFile(String id) {
        // 获取当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        // 更新合同文件 delFlag 1已删除
        UompContractFile uompContractFile = new UompContractFile();
        uompContractFile.setId(id);
        uompContractFile.setDelFlag("1");
        uompContractFile.setUpdateOrgId(orgId);
        uompContractFile.setUpdateTime(new Date());
        uompContractFile.setUpdateBy(userId);
        uompContractFileService.update(uompContractFile);
    }

    @Override
    public void saveResource(UompContractResourceSave uompContractResourceSave) {
        String contractManagementId = uompContractResourceSave.getContractManagementId();
        List<UompContractResourceVO> list = uompContractResourceSave.getResourceInfos();
        if (CollectionUtils.isNotEmpty(list)) {
            //2024-12-11 查询该资源是否已经添加，添加则对其进行修改操作，以保证和cmdb保持统一
            //查询该批次中是否有重复资源
            //1.拼接参数
            List<Map<String, String>> paramList = list.stream().map(info -> {
                Map<String, String> map = new HashMap<>();
                map.put("contractManagementId", contractManagementId);
                map.put("cInstId", info.getcInstId());
                return map;
            }).collect(Collectors.toList());
            //2.查询是否有重复的资源，并返回cInstId
            List<String> cInstIdList = uompContractResourceService.selectListByCmIdAndCiId(paramList);
            //重复的做修改，不重复的做新增
            List<UompContractResourceVO> updateList =
                    list.stream().filter(info -> cInstIdList.contains(info.getcInstId())).collect(Collectors.toList());
            List<UompContractResourceVO> insertList =
                    list.stream().filter(info -> !cInstIdList.contains(info.getcInstId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(updateList)) {
                List<UompContractResource> resourceUpdateList = new ArrayList<>();
                updateList.forEach(updateInfo -> {
                    UompContractResource uompContractResource = new UompContractResource();
                    BeanUtils.copyProperties(updateInfo, uompContractResource);
                    uompContractResource.setContractManagementId(contractManagementId);
                    resourceUpdateList.add(uompContractResource);
                });
                uompContractResourceService.updateBatchByCmIdAndCiId(resourceUpdateList);
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                List<UompContractResource> resourceInsertList = new ArrayList<>();
                insertList.forEach(insertInfo -> {
                    UompContractResource uompContractResource = new UompContractResource();
                    BeanUtils.copyProperties(insertInfo, uompContractResource);
                    String id = IdUtil.getSuid();
                    uompContractResource.setId(id);
                    uompContractResource.setContractManagementId(contractManagementId);
                    resourceInsertList.add(uompContractResource);
                });
                uompContractResourceService.insertBatch(resourceInsertList);
            }

//            for (UompContractResourceVO vo : list) {
//                // 添加合同关联的合同文件信息
//                String id = IdUtil.getSuid();
//                UompContractResource uompContractResource = new UompContractResource();
//                // 字段赋值
//                uompContractResource.setId(id);
//                uompContractResource.setContractManagementId(contractManagementId);
//                uompContractResource.setCInstId(vo.getcInstId());
//                uompContractResource.setCiName(vo.getCiName());
//                uompContractResource.setBrand(vo.getBrand());
//                uompContractResource.setBrandModel(vo.getBrandModel());
//                uompContractResource.setCpuFramework(vo.getCpuFramework());
//                uompContractResource.setUseds(vo.getUseds());
//                uompContractResource.setOs(vo.getOs());
//                uompContractResource.setOsVersion(vo.getOsVersion());
//                uompContractResource.setMachineRoom(vo.getMachineRoom());
//                uompContractResource.setCabinet(vo.getCabinet());
//                uompContractResourceService.insert(uompContractResource);
//            }
        }
    }

    @Override
    public PageResult getResourceList(QueryFilter queryFilter) {
        List<UompContractResource> list = uompContractResourceService.query(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            return new PageResult(list);
        }
        return new PageResult(new ArrayList());
    }

    @Override
    public void deleteResource(String ids) {
        // 删除合同关联资源
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = Arrays.asList(ids.split(","));
            uompContractResourceService.deleteByIdList(idList);
        }
    }

    @Override
    public PageResult getPropertyList(QueryFilter queryFilter) {
        // 资产字典
        List<BaseDTO> resourceStatusList = sysDataDictMapper.selectSubListByDictKey(RESOURCE_STATUS);
        // 设备字典
        List<BaseDTO> deviceStatusList = sysDataDictMapper.selectSubListByDictKey(DEVICE_STATUS);
        //查询审核通过的,资产状态非报废
        List<UompPropertyDTO> list = cmdbCommResourceService.selectPropertyList(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            for (UompPropertyDTO dto : list) {
                updateSetUompProperty(dto, resourceStatusList, deviceStatusList);
            }
        }
        return new PageResult(list);
    }

    public void updateSetUompProperty(UompPropertyDTO dto, List<BaseDTO> resourceStatusList, List<BaseDTO> deviceStatusList) {
        Date date = new Date();
        // 维保状态判断 tend_status
        // 没有维保时间的按正常处理
        Date tendDateStart = dto.getTendDateStart();
        Date tendDateEnd = dto.getTendDateEnd();
        if (null == tendDateStart || null == tendDateEnd) {
            dto.setTendStatus("正常");
        } else {
            //维保结束日期按照当天的23:59:59算
            Date endTime = new Date();
            try {
                String endTimeStr = sdf1.format(tendDateEnd) + " 23:59:59";
                endTime = sdf2.parse(endTimeStr);
            } catch (ParseException pe) {
                log.warn("结束日期转换错误");
            }
            if (date.before(tendDateStart)) {
                dto.setTendStatus("未开始");
            }
            if (date.after(endTime)) {
                dto.setTendStatus("过保");
            }
            if ((date.after(tendDateStart) || date.equals(tendDateStart)) && (date.before(tendDateEnd) || date.equals(tendDateEnd))) {
                dto.setTendStatus("正常");
                //如果7天内改成临期
                long diff = tendDateEnd.getTime() - date.getTime();
                long day = diff / (24 * 60 * 60 * 1000);
                if (day < 7) {
                    dto.setTendStatus("临期");
                }
            }
        }
        //时间格式修改
        if (null != tendDateStart) {
            dto.setTendDateStartStr(sdf1.format(tendDateStart));
        }
        if (null != tendDateEnd) {
            dto.setTendDateEndStr(sdf1.format(tendDateEnd));
        }
        String resourceState = dto.getResourceState();
        String deviceState = dto.getDeviceState();
        //资产状态映射处理
        for (BaseDTO baseDTO : resourceStatusList) {
            if (StringUtils.isNotEmpty(resourceState) && resourceState.equals(baseDTO.getId())) {
                dto.setResourceState(baseDTO.getName());
                break;
            }
        }

        //设备状态映射处理
        for (BaseDTO baseDTO : deviceStatusList) {
            if (StringUtils.isNotEmpty(deviceState) && deviceState.equals(baseDTO.getId())) {
                dto.setDeviceState(baseDTO.getName());
                break;
            }
        }
    }

    @Override
    public UompContractManagementListDTO getInfoById(String id) {
        UompContractManagement uompContractManagement = uompContractManagementMapper.selectInfoById(id);
        if (null != uompContractManagement) {
            uompContractManagement.setPartyInfoList(uompPartyInfoService.selectByContractId(id));
            List<UompContractManagementListDTO> list = getSystemJsonList(Arrays.asList(uompContractManagement));
            return list.get(0);
        }
        return new UompContractManagementListDTO();
    }

    @Override
    public Boolean compareTime(String time) {
        //服务器当前时间
        Date date = new Date();
        //结束时间按照23:59:59算
        Date newTime;
        try {
            newTime = sdf2.parse(time + " 23:59:59");
        } catch (ParseException pe) {
            throw new BusinessMessage("time时间格式不正确！");
        }
        return date.after(newTime);
    }

    @Override
    public List<UompContractManagement> getRealtionContractList(QueryFilter queryFilter) {
        return uompContractManagementMapper.getRealtionContractList(queryFilter);
    }

    @Override
    public List<UompContractManagement> getUnbindingContractList(String projectManagementId, String contractName) {
        return uompContractManagementMapper.getUnbindingContractList(projectManagementId, contractName);
    }

    @Override
    public PageResult getContractListDialog(QueryFilter queryFilter) {
        List<UompContractManagement> list = uompContractManagementMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        pageResult.setRows(getSystemJsonList(list));
        return pageResult;
    }

    // 封装合同列表返回对象
    private List<UompContractManagementListDTO> getSystemJsonList(List<UompContractManagement> list) {
        List<UompContractManagementListDTO> dtoList = new ArrayList();
        if (!CollectionUtils.isEmpty(list)) {
            for (UompContractManagement uompContractManagement : list) {
                UompContractManagementListDTO dto = new UompContractManagementListDTO();
                BeanUtils.copyProperties(uompContractManagement, dto);
                // 获取应用系统id和name，拼接json返回
                String systemId = uompContractManagement.getProjectManagementId();
                String systemName = uompContractManagement.getProjectManagementName();
                if (StringUtils.isNotEmpty(systemId) && StringUtils.isNotEmpty(systemName)) {
                    BaseDTO baseDTO = new BaseDTO();
                    baseDTO.setId(systemId);
                    baseDTO.setName(systemName);
                    String systemJson = JSONObject.toJSONString(Arrays.asList(baseDTO));
                    dto.setProjectManagementJson(systemJson);
                }
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    @Override
    public List<BaseDTO> getIdAndNameList() {
        return uompContractManagementMapper.getIdAndNameList();
    }

    @Override
    public List<UompContractManagement> bindingContractListBySystemId(QueryFilter queryFilter) {
        return uompContractManagementMapper.bindingContractListBySystemId(queryFilter);
    }

    @Override
    public List<UompContractManagement> unbindingContractList(QueryFilter queryFilter) {
        return uompContractManagementMapper.unbindingContractList(queryFilter);
    }

    @Override
    public List<UompContractManagement> getContractNoticeList(String day) {
        return uompContractManagementMapper.getContractNoticeList(day);
    }

    @Override
    public void refresh() throws Exception {
        refreshContractJob.execute(null);
    }

    @Override
    public List<SupplierContractDTO> getContractTop5() {
        // 查询所有服务商中合同状态不是暂存和失效的最新合同质保结束时间
        List<SupplierContractDTO> list = uompContractManagementMapper.getContractTop5();
        //  去重，处理同一服务商下，多个合同质保结束时间一致下仅获取一条记录
        List<String> ids = new ArrayList<>();
        List<SupplierContractDTO> topList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            //当前时间
            LocalDate now = LocalDate.now();
            for (SupplierContractDTO dto : list) {
                String supplierId = dto.getSupplierId();
                if (ids.contains(supplierId)) {
                    continue;
                }
                // 计算合同剩余天数
                Date qualityEndDay = dto.getQualityEndDay();
                LocalDate entryLocalDate = qualityEndDay.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                long days = ChronoUnit.DAYS.between(now, entryLocalDate);
                dto.setDay(days);
                ids.add(supplierId);
                topList.add(dto);
            }
        }
        return topList;
    }

}
