package cn.gwssi.ecloud.staffpool.core.entity.risk;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class UserRiskVO extends BaseModel {

    @ApiModelProperty("服务商")
    private String serviceProvider;

    @ApiModelProperty("运维组")
    private String teamName;

    @ApiModelProperty("风险配置ids")
    private String configIds;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("补充信息清单")
    private String files;

    @ApiModelProperty("当前用户风险等级")
    private String curLevel;

    private List<TeamRisk> riskList;

    private List<TeamRisk> riskHisList;

    public UserRiskVO() {
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getConfigIds() {
        return configIds;
    }

    public void setConfigIds(String configIds) {
        this.configIds = configIds;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getCurLevel() {
        return curLevel;
    }

    public void setCurLevel(String curLevel) {
        this.curLevel = curLevel;
    }

    public List<TeamRisk> getRiskList() {
        return riskList;
    }

    public void setRiskList(List<TeamRisk> riskList) {
        this.riskList = riskList;
    }

    public List<TeamRisk> getRiskHisList() {
        return riskHisList;
    }

    public void setRiskHisList(List<TeamRisk> riskHisList) {
        this.riskHisList = riskHisList;
    }
}
