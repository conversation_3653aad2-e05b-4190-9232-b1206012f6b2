package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompOrgGroupMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompOrgGroup;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompOrgGroupService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.core.model.OrderGroup;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.orgCustom.api.service.UserCustomService;
import cn.gwssi.ecloudframework.module.orgCustom.core.manager.GroupCustomManager;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.GroupCustom;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import com.alibaba.excel.util.CollectionUtils;
import org.apache.avalon.framework.service.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UompOrgGroupServiceImpl extends BaseManager<String, GroupUomp> implements UompOrgGroupService {

    @Resource
    private UompOrgGroupMapper uompOrgGroupMapper;
    @Resource
    private UompPersonInfoService uompPersonInfoService;

    @Resource
    private UserCustomService userCustomService;

    @Resource
    private GroupCustomManager groupManager;

    @Override
    public void chageOrder(List<GroupUomp> groupUomps) {
        groupUomps.forEach(group -> {
            if (StringUtils.isEmpty(group.getId())) {
                throw new BusinessMessage("ID不能为空");
            } else {
                uompOrgGroupMapper.chageOrder(group);
            }
        });
    }

    @Override
    public List<String> getByPath(String orgGroupId) {
        return uompOrgGroupMapper.getByPath(orgGroupId);
    }

    @Override
    public List<OrderGroup> getOrderGroup() {
        List<OrderGroup> orderGroups = uompOrgGroupMapper.getOrderGroup();
        orderGroups.forEach(orderGroup -> {
            String orgId = orderGroup.getOrgId();
            List<OrderGroup> orderUsers = new ArrayList<>();
            List<? extends IUser> usersByGroup = userCustomService.getUsersByOrgIds(orgId);
            usersByGroup.forEach(user -> {
                OrderGroup orderUser = new OrderGroup(user.getFullname(), user.getUserId(), user.getUserId(), "user", orgId);
                orderUsers.add(orderUser);
            });
            orderGroup.setChildrens(orderUsers);
        });
        orderGroups = orderGroups.stream().filter(orderGroup -> !CollectionUtils.isEmpty(orderGroup.getChildrens()) && orderGroup.getChildrens().size() > 0).collect(Collectors.toList());
        return orderGroups;
    }

    /**
     * 初始化运维组织根节点
     *
     */
    @Override
    public void initUompGroup(GroupUomp uompOrgGroup) {
        GroupCustom groupCustom = BeanCopierUtils.transformBean(uompOrgGroup,GroupCustom.class);
//        groupCustom.setParentId("1");
        groupManager.create(groupCustom);
        uompOrgGroup.setOrgGroupId(groupCustom.getId());
        create(uompOrgGroup);
    }

    @Override
    public void create(GroupUomp entity) {
        if (uompOrgGroupMapper.getByCode(entity.getCode(), null) != null) {
            throw new BusinessMessage("组织编码“" + entity.getCode() + "”已存在，请修改！");
        }
        //默认创建排序为0
        if (null == entity.getSn()) {
            entity.setSn(0);
        }
        entity.setId(IdUtil.getSuid());
        setPath(entity);

        super.create(entity);
    }

    @Override
    public String updateById(GroupUomp entity) throws ServiceException {
        if (uompOrgGroupMapper.getByCode(entity.getCode(), entity.getId()) != null) {
            throw new BusinessMessage("组织编码“" + entity.getCode() + "”已存在，请修改！");
        }
        setPath(entity);
        UompOrgGroup uompOrgGroup = new UompOrgGroup();
        BeanUtils.copyProperties(entity, uompOrgGroup);
        uompOrgGroup.setType(String.valueOf(entity.getType()));

        // 判断是否设置了状态停用
        if (entity.getStatus() == 0) {
            // 校验本机构或下级机构存在用户 则不允许修改为停用
            // 判断本机构是否有用户
            List<UompPersonInfo> uompPersonInfos = uompPersonInfoService.selectAllByOrgStatusAll(entity.getId());
            if (!CollectionUtils.isEmpty(uompPersonInfos)) {
                throw new ServiceException("组织存在相应用户,不可以操作停用！");
            }

            // 判断下属机构是否有用户
            List<GroupUomp> childList = uompOrgGroupMapper.getChildByPath(entity.getPath() + "%");
            for (GroupUomp groupUomp : childList) {
                // 判断下属机构是否都已停用
                if (groupUomp.getStatus() != 0 && !groupUomp.getId().equals(entity.getId())) {
                    throw new ServiceException("组织下属组织未停用,不可以操作停用！");
                }
                // 判断下属机构是否存在用户
                List<UompPersonInfo> uompPersonInfoChilds = uompPersonInfoService.selectAllByOrgStatusAll(groupUomp.getId());
                if (!CollectionUtils.isEmpty(uompPersonInfoChilds)) {
                    throw new ServiceException("组织下属组织存在相应用户,不可以操作停用！");
                }
            }
        }

        uompOrgGroupMapper.updateById(uompOrgGroup, entity.getId());
        return "";
    }

    @Override
    public String removeById(String id) throws ServiceException {
        GroupUomp group = uompOrgGroupMapper.get(id);

        // 校验本机构或下级机构存在用户 则不允许删除
        // 判断本机构是否有用户
        List<UompPersonInfo> uompPersonInfos = uompPersonInfoService.selectAllByOrgStatusAll(id);
        if (!CollectionUtils.isEmpty(uompPersonInfos)) {
            throw new ServiceException("组织下存在相应用户,不可以操作删除！");
        }

        if (group != null) {
            // 判断下属机构是否有用户
            List<GroupUomp> childList = uompOrgGroupMapper.getChildByPath(group.getPath() + "%");
            // 判断下属机构是否存在用户
            for (GroupUomp groupUomp : childList) {
                List<UompPersonInfo> uompPersonInfoChilds = uompPersonInfoService.selectAllByOrgStatusAll(groupUomp.getId());
                if (!CollectionUtils.isEmpty(uompPersonInfoChilds)) {
                    throw new ServiceException("组织下存在相应用户,不可以操作删除！");
                }
            }

            // 级联删除子组织
            childList.forEach(g -> {
                uompOrgGroupMapper.remove(g.getId());
            });
        }

        super.remove(id);
        return "";
    }

    @Override
    public List<GroupUomp> getAll() {
        return uompOrgGroupMapper.getAll("", "");
    }

    public List<GroupUomp> getAll(String virtual, String status) {
        return uompOrgGroupMapper.getAll(virtual, status);
    }

    private void setPath(GroupUomp entity) {
        entity.setPath(entity.getId());
        if (StringUtil.isNotZeroEmpty(entity.getParentId())) {
            GroupUomp parent = uompOrgGroupMapper.get(entity.getParentId());
            if (parent != null && parent.getPath() != null) {
                entity.setPath(parent.getPath().concat(".").concat(entity.getId()));
            }
        }
    }
}
