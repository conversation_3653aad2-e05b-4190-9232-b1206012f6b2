package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_abroad")
@Data
public class UompPersonAbroadDto extends BaseModel {
    @ApiModelProperty(value = "")
    private String personId;

    @ApiModelProperty(value = "")
    private String certificateName;

    @ApiModelProperty(value = "")
    private String certificateNum;

    @ApiModelProperty(value = "")
    private String issueAt;

    @ApiModelProperty(value = "")
    private String startEndTime;

    @ApiModelProperty(value = "")
    private String startTime;

    @ApiModelProperty(value = "")
    private String endTime;

    @ApiModelProperty(value = "")
    private JSONArray fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;
    private static final long serialVersionUID = 1L;
}