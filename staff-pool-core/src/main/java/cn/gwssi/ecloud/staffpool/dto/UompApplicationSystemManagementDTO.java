package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 应用系统信息表
 */
@ApiModel(value = "应用系统信息", description = "应用系统信息")
@Data
public class UompApplicationSystemManagementDTO extends BaseModel {

    @ApiModelProperty(value = "应用系统名称")
    private String applicationSystemName;
    @ApiModelProperty(value = "运维部门id")
    private String departId;
    @ApiModelProperty(value = "运维部门名称")
    private String departName;
    @ApiModelProperty(value = "负责人名称")
    private String principalName;
    @ApiModelProperty(value = "负责人id")
    private String principalId;
    @ApiModelProperty(value = "是否核心应用（1：是   0：否）")
    private String isHeart;
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;
    @ApiModelProperty(value = "状态（0：使用中  1已停用）")
    private String systemStatus;
    @ApiModelProperty(value = "服务商id")
    private String supplierId;
    @ApiModelProperty(value = "服务商名称")
    private String supplierName;
    @ApiModelProperty(value = "服务商负责人")
    private String supplierDepartName;
    @ApiModelProperty(value = "联系电话")
    private String supplierTel;
    @ApiModelProperty(value = "创建机构")
    private String createOrgId;
    @ApiModelProperty(value = "更新机构")
    private String updateOrgId;
    @ApiModelProperty(value = "删除标识（0:正常  1：已删除）")
    private String delFlag;
    @ApiModelProperty(value = "合同信息集合")
    private List<UompContractManagementSystemDTO> contractList = new ArrayList<>();
    @ApiModelProperty(value = "驻场人员集合")
    private List<PersonListDTO> personListDTOList = new ArrayList<>();
    @ApiModelProperty(value = "距离退场天数")
    private Integer outDays;
}