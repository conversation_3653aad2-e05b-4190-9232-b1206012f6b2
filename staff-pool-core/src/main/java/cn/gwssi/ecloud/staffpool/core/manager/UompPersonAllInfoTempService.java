package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompPersonAllInfoTempService extends Manager<String, UompPersonAllInfoTemp> {

    int insertSelective(UompPersonAllInfoTemp record);

    int updateByPrimaryKeySelective(UompPersonAllInfoTemp record);

    UompPersonAllInfoTemp getByPeronId(String personId);
}
