package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="人员,驻场数量-响应类")
@Data
public class PersonEntryNumberDTO implements Serializable {

    @ApiModelProperty(value="临时入场人数")
    private int personNumber;
    @ApiModelProperty(value="新增驻场人数")
    private int entryNumber;
}
