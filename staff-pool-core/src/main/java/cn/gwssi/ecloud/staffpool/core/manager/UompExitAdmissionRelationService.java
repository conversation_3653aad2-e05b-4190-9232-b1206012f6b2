package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitAdmissionRelation;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.Date;

public interface UompExitAdmissionRelationService extends Manager<String, UompExitAdmissionRelation> {

    int insertSelective(UompExitAdmissionRelation record);

    void updateOutTimeByExitId(String exitId, Date outTime);
}
