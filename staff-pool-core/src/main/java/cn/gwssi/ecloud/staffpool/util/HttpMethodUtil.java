package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

/*
 * http请求工具类
 * <AUTHOR>
 */
public class HttpMethodUtil {

    public static String getPost(String url, String body, Map<String, String> map) {
        String result = "";
        CloseableHttpClient httpClient = null;
        HttpPost httpPost = null;
        HttpEntity httpEntity = null;
        CloseableHttpResponse httpResponse = null;
        try {
            httpClient = HttpClientBuilder.create().build();
            httpPost = new HttpPost(url);
            Set<String> keys = map.keySet();
            for (String key : keys) {
                httpPost.setHeader(key, map.get(key));
            }
            /*RequestConfig defaultRequestConfig = RequestConfig.custom()
                    .setSocketTimeout(3000)
                    .setConnectTimeout(3000)
                    .setConnectionRequestTimeout(3000)
                    .build();
            httpPost.setConfig(defaultRequestConfig);*/
            httpEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(httpEntity);
            httpResponse = httpClient.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException ioe) {
            ioe.printStackTrace();
            throw new BusinessException(ioe.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(e.getMessage());
        } finally {
          if (null != httpClient) {
              try {
                  httpClient.close();
              } catch (IOException ie) {
                  // 忽略
              }
          }
        }
        return result;
    }

    public static String getPut(String url, String body, Map<String, String> map) {
        String result = "";
        CloseableHttpClient httpClient = null;
        HttpPut httpPut = null;
        HttpEntity httpEntity = null;
        CloseableHttpResponse httpResponse = null;
        try {
            httpClient = HttpClientBuilder.create().build();
            httpPut = new HttpPut(url);
            Set<String> keys = map.keySet();
            for (String key : keys) {
                httpPut.setHeader(key, map.get(key));
            }
            /*RequestConfig defaultRequestConfig = RequestConfig.custom()
                    .setSocketTimeout(3000)
                    .setConnectTimeout(3000)
                    .setConnectionRequestTimeout(3000)
                    .build();
            httpPost.setConfig(defaultRequestConfig);*/
            httpEntity = new StringEntity(body, "utf-8");
            httpPut.setEntity(httpEntity);
            httpResponse = httpClient.execute(httpPut);
            HttpEntity entity = httpResponse.getEntity();
            result = EntityUtils.toString(entity);
        } catch (IOException ioe) {
            ioe.printStackTrace();
            throw new BusinessException(ioe.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(e.getMessage());
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException ie) {
                    // 忽略
                }
            }
        }
        return result;
    }
}
