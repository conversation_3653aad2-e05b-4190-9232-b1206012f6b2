package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement;
import cn.gwssi.ecloud.staffpool.dto.ProjectBySupplierDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompApplicationSystemManagementMapper extends BaseDao<String, UompApplicationSystemManagement> {

    int insertSelective(UompApplicationSystemManagement record);

    int updateByPrimaryKey(UompApplicationSystemManagement record);

    List<ProjectBySupplierDTO> selectIdAndNameByAll();

    UompApplicationSystemManagement selectIdByApplicationSystemName(String applicationSystemName);

    List<ProjectBySupplierDTO> selectAllByRelationId(@Param("supplierId") String supplierId, @Param("projectName") String projectName);
}