package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CmdbTableBaseConfigMapper extends BaseDao<String, CmdbTableBaseConfig> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmdbTableBaseConfig record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CmdbTableBaseConfig record);
}