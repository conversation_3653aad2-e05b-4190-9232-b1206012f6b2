package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_educational")
@Data
public class UompPersonEducationalDto extends BaseModel {
    @ApiModelProperty(value = "")
    private String personId;

    @ApiModelProperty(value = "")
    private String educationTime;

    private String educationBeginTime;

    @ApiModelProperty(value = "")
    private String educationEndTime;

    @ApiModelProperty(value = "")
    private String school;

    @ApiModelProperty(value = "")
    private String major;

    @ApiModelProperty(value = "")
    private String educationBackground;

    @ApiModelProperty(value = "")
    private JSONArray fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String educationForm;

    @ApiModelProperty(value = "")
    private String certificateNum;

    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;

    private static final long serialVersionUID = 1L;
}