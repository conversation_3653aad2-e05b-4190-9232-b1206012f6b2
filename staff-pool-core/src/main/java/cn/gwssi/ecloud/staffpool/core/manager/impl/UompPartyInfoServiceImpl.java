package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPartyInfoMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPartyInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompPartyInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UompPartyInfoServiceImpl implements UompPartyInfoService {
    @Autowired
    private UompPartyInfoMapper uompPartyInfoMapper;
    @Override
    public void insert(UompPartyInfo uompPartyInfo) {
        uompPartyInfoMapper.create(uompPartyInfo);
    }

    @Override
    public List<UompPartyInfo> selectByContractId(String contractId) {
        return uompPartyInfoMapper.query(contractId);
    }

    @Override
    public void delete(String contractId) {
        uompPartyInfoMapper.remove(contractId);
    }
}
