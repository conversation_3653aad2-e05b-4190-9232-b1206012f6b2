package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class RiskEvaluateChain {

    private static final Logger LOG = LoggerFactory.getLogger(RiskEvaluateChain.class);

    private RiskEvaluateChain nextChain;

    public void chain(UompPersonInfoVo uompPersonInfoVo,String riskName){
        if (riskName.contains(alias())){
            try {
                evaluate(uompPersonInfoVo);
            }catch (Exception e){
                LOG.error("catch exception when evaluate {} ",alias(),e);
            }
        }

        if (nextChain != null){
            nextChain.chain(uompPersonInfoVo,riskName);
        }
    }


    public abstract void evaluate(UompPersonInfoVo uompPersonInfoVo);

    public abstract String alias();

    public void setNextChain(RiskEvaluateChain nextChain){
        this.nextChain = nextChain;
    }
}
