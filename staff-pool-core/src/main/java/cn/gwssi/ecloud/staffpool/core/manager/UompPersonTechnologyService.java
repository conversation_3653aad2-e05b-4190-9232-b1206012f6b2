package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology;
import cn.gwssi.ecloud.staffpool.dto.UompPersonTechnologyDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonTechnologyService extends Manager<String, UompPersonTechnology> {

    int insertSelective(UompPersonTechnology record);

    int updateByPrimaryKeySelective(UompPersonTechnology record);

    int updateByPersonId(UompPersonTechnology record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonTechnologyDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
