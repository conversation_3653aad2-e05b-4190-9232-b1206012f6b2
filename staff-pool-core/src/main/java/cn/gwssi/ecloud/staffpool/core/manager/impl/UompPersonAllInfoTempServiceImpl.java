package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoTempMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoTemp;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonAllInfoTempService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UompPersonAllInfoTempServiceImpl extends BaseManager<String, UompPersonAllInfoTemp> implements UompPersonAllInfoTempService {

    @Autowired
    private UompPersonAllInfoTempMapper uompPersonAllInfoTempMapper;

    @Override
    public int insertSelective(UompPersonAllInfoTemp record) {
        return uompPersonAllInfoTempMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonAllInfoTemp record) {
        return uompPersonAllInfoTempMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public UompPersonAllInfoTemp getByPeronId(String personId) {
        return uompPersonAllInfoTempMapper.getByPeronId(personId);
    }
}
