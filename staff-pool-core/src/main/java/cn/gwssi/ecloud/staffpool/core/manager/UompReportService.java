package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.UompReportSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompReport;
import cn.gwssi.ecloud.staffpool.dto.UompReportDTO;
import cn.gwssi.ecloud.staffpool.dto.UompReportListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;

public interface UompReportService extends Manager<String, UompReport> {

    int insertSelective(UompReport record);

    int updateByPrimaryKey(UompReport record);

    PageResult<UompReportListDTO> getReportList(QueryFilter queryFilter);

    UompReportDTO getReportInfoById(String id);

    String save(UompReportSave uompReportSave);

    void deleteReport(String id);
}
