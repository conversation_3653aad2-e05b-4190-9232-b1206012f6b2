package cn.gwssi.ecloud.staffpool.controller;


import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonDutyDetailService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonDutyService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.module.orgCustom.api.service.UserCustomService;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(description = "值班排班")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/personDuty")
public class UompPersonDutyController extends BaseController<UompPersonDuty> {

    @Resource
    private UompPersonDutyService dutyService;
    @Resource
    private UompPersonDutyDetailService dutyDetailService;
    @Resource
    UserCustomService customService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;

    @ApiOperation(value = "管理列表")
    @RequestMapping(value = "/list")
    public PageResult<UompPersonDutyDto> list(HttpServletRequest request, HttpServletResponse response,
                                              @RequestParam(value = "personIds", required = false) @ApiParam(value = "人员id") String personIds,
                                              @RequestParam(value = "date", required = false) @ApiParam(value = "值班日期") String date,
                                              @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                              @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        String[] persons = null;
        if (!StringUtils.isEmpty(personIds)) {
            persons = personIds.split(",");
        }
        String startDate = null;
        String endDate = null;
        if (!StringUtils.isEmpty(date)) {
            String[] entryDates = date.split(",");
            if (entryDates.length > 1) {
                startDate = entryDates[0];
                endDate = entryDates[1];
            }
        }

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.size() == 0) {
            return new PageResult();
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
//        String createBy = null;
        //11-20 排版管理列表 增加运维组长权限控制,运维组长可见组下所有人创建的数据
        List<String> createBys = new ArrayList<>();
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
//            createBy = user.getUserId();
            createBys.add(user.getUserId());
        }else if (roleNames.contains("ITSM_GROUP LEADER")) {
            // 查看本运维组下所创建的以及运维组下的值班人
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                String roleOrgGroupId = workCompany.get(0).getOrgGroupId();
                //查询组下所有人（非黑，在职，审核通过，背调非不合格）
                QueryFilter queryFilter2 = new DefaultQueryFilter(true);
                queryFilter2.addFilter("ORG_GROUP_ID", roleOrgGroupId, QueryOP.EQUAL);
                queryFilter2.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                queryFilter2.addFilter("BLACKLIST", 0, QueryOP.EQUAL);// 非黑名单
                queryFilter2.addFilter("DIMISSION", 0, QueryOP.EQUAL);// 在职
                queryFilter2.addFilter("BACKGROUND_STATUS", 2, QueryOP.NOT_EQUAL);// 非不合格
                queryFilter2.addFilter("TRIAL_STATUS", 2, QueryOP.EQUAL);// 审批通过
                List<UompPersonInfo> personList = uompPersonInfoService.query(queryFilter2);
                personList.forEach(person -> createBys.add(person.getOrgUserId()));
            }
        } else {
            return new PageResult();
        }

        List<UompPersonDutyDto> list = dutyService.getDutyList(startDate, endDate, persons, pageNo, pageSize, createBys);
        Map<String, UompPersonDutyDto> map = new LinkedHashMap<>();
        for (UompPersonDutyDto dto : list) {
            if (map.get(dto.getId()) == null) {
                dto.setPersonName(dto.getPersonName() + " " + dto.getTel() + "; ");
                map.put(dto.getId(), dto);
            } else {
                UompPersonDutyDto dutyDto = map.get(dto.getId());
                dutyDto.setPersonName(dutyDto.getPersonName() + " " + dto.getPersonName() + dto.getTel() + "; ");
            }
        }
        for (String key : map.keySet()) {
            UompPersonDutyDto dutyDto = map.get(key);
            dutyDto.setCreateBy(customService.getUserById(dutyDto.getCreateBy()).getFullname());
        }

        return new PageResult<>(new ArrayList<>(map.values()), dutyService.getDutyListTotal(startDate, endDate, persons, createBys));
    }

    @ApiOperation(value = "新增或编辑")
    @RequestMapping(value = "/add")
    public ResultMsg<String> add(HttpServletRequest request, HttpServletResponse response,
                                 @RequestParam(value = "id", required = false) @ApiParam(value = "值班") String id,
                                 @RequestParam(value = "personIds") @ApiParam(value = "人员ids") String personIds,
                                 @RequestParam(value = "dates") @ApiParam(value = "值班日期") String dates) {
        if (!StringUtils.isEmpty(dates) && !StringUtils.isEmpty(personIds)) {
            List<UompPersonDutyDto> list = dutyService.selectByDateAndPersonId(dates.split(","), personIds.split(","));
            if (list.size() > 0) {
                StringBuilder stringBuilder = new StringBuilder("保存失败，");
                for (UompPersonDutyDto dutyDto : list) {
                    if (!StringUtils.isEmpty(id) && dutyDto.getId().equals(id)) {
                        continue;
                    }
                    stringBuilder.append(dutyDto.getPersonName()).append("在").append(dutyDto.getDate()).append("已存在\n");
                }
                if (stringBuilder.length() > 6) {
                    stringBuilder.append("请重新选择");
                    return this.getWarnResult(stringBuilder.toString());
                }
            }
            UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
            if (!StringUtils.isEmpty(id)) {
                String[] strs = dates.split(",");
                String[] personIdStrs = personIds.split(",");
                if (strs.length > 0 && personIdStrs.length > 0) {
                    UompPersonDuty uompPersonDuty = new UompPersonDuty();
                    uompPersonDuty.setId(id);
                    uompPersonDuty.setDate(strs[0]);
                    uompPersonDuty.setUpdateBy(user.getUserId());
                    uompPersonDuty.setUpdateTime(new Date());
                    dutyService.updateByPrimaryKeySelective(uompPersonDuty);
                    dutyDetailService.removeByDutyId(id);
                    for (String personId : personIdStrs) {
                        UompPersonDutyDetail dutyDetail = new UompPersonDutyDetail();
                        dutyDetail.setDutyId(id);
                        dutyDetail.setPersonId(personId);
                        dutyDetail.setDate(strs[0]);
                        dutyDetail.setDelFlag("0");
                        dutyDetailService.create(dutyDetail);
                    }

                }
            } else {
                String[] strs = dates.split(",");
                String[] personIdStrs = personIds.split(",");
                for (String date : strs) {
                    UompPersonDuty uompPersonDuty = new UompPersonDuty();
                    uompPersonDuty.setDate(date);
                    uompPersonDuty.setDelFlag("0");
                    dutyService.create(uompPersonDuty);
                    for (String personId : personIdStrs) {
                        UompPersonDutyDetail dutyDetail = new UompPersonDutyDetail();
                        dutyDetail.setDutyId(uompPersonDuty.getId());
                        dutyDetail.setPersonId(personId);
                        dutyDetail.setDate(date);
                        dutyDetail.setDelFlag("0");
                        dutyDetailService.create(dutyDetail);
                    }
                }
            }
        }
        return this.getSuccessResult("保存成功");
    }

    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete")
    public ResultMsg<String> delete(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(value = "ids", required = false) @ApiParam(value = "值班ids") String ids) throws
            ParseException {
        if (!StringUtils.isEmpty(ids)) {
            QueryFilter queryFilter = new DefaultQueryFilter(true);
            queryFilter.addFilter("ID", ids, QueryOP.IN);
            List<UompPersonDuty> duties = dutyService.query(queryFilter);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = DateUtil.beginOfDay(new Date());
            Set<String> set = new HashSet<>();
            // 之前的不可删除
            for (UompPersonDuty duty : duties) {
                Date dutyDate = simpleDateFormat.parse(duty.getDate());
                if (dutyDate.before(date)) {
                    set.add(duty.getDate());
                }
            }
            if (set.size() > 0) {
                StringBuilder stringBuilder = new StringBuilder("删除失败，日期 ");
                for (String d : set) {
                    stringBuilder.append(d).append(" ");
                }
                stringBuilder.append("不可删除，请重新选择");
                return this.getWarnResult(stringBuilder.toString());
            } else {
                for (UompPersonDuty duty : duties) {
                    dutyService.remove(duty.getId());
                    dutyDetailService.removeByDutyId(duty.getId());
                }
            }

        }
        return this.getSuccessResult("删除成功");
    }

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/detail")
    public ResultMsg<List<UompPersonContactsDTO>> detail(HttpServletRequest request, HttpServletResponse response,
                                                         @RequestParam(value = "id") @ApiParam(value = "值班id") String id) {
        return this.getSuccessResult(dutyDetailService.selectByDutyId(id));
    }

    @ApiOperation(value = "值班表")
    @RequestMapping(value = "/dutyDateList")
    public ResultMsg<Map<String, List<UompPersonDutyDto>>> dutyDateList(HttpServletRequest request, HttpServletResponse response,
                                                                        @RequestParam(value = "year") @ApiParam(value = "年") Integer year,
                                                                        @RequestParam(value = "month") @ApiParam(value = "月") Integer month,
                                                                        @RequestParam(value = "orgId",required = false) String orgId) {
        List<UompPersonDutyDto> list = dutyDetailService.selectByMonth(year + "-" + (month < 10 ? "0" : "") + month,orgId);
        Map<String, List<UompPersonDutyDto>> map = new HashMap<>();
        for (UompPersonDutyDto uompPersonDutyDto : list) {
            map.putIfAbsent(uompPersonDutyDto.getDate(), new ArrayList<>());
            map.get(uompPersonDutyDto.getDate()).add(uompPersonDutyDto);
        }
        return this.getSuccessResult(map);
    }

    @Override
    protected String getModelDesc() {
        return "值班排班";
    }
}

