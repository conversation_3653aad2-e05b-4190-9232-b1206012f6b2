package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonEducationalMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonEducationalService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEducationalDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonEducationalServiceImpl extends BaseManager<String, UompPersonEducational> implements UompPersonEducationalService {

    @Resource
    private UompPersonEducationalMapper uompPersonEducationalMapper;

    @Override
    public int insertSelective(UompPersonEducational record) {
        return uompPersonEducationalMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonEducational record) {
        return uompPersonEducationalMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonEducational record) {
        return uompPersonEducationalMapper.updateByPersonId(record);
    }

    @Override
    public int updateByPersonIds(String orgId, String[] personIds) {
        return uompPersonEducationalMapper.updateByPersonIds(orgId, personIds);
    }

    @Override
    public List<UompPersonEducationalDto> selectByPersonIds(List<String> personIds) {
        return uompPersonEducationalMapper.selectByPersonIds(personIds);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonEducationalMapper.deleteByPersonId(personId);
    }
}
