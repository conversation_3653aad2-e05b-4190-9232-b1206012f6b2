package cn.gwssi.ecloud.staffpool.vo;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 岗位信息VO
 */
public class PositionVO {

    @ApiModelProperty(value="岗位ID")
    private String id;

    @ApiModelProperty(value="岗位名称")
    private String name;

    @ApiModelProperty(value="权限")
    private String permission;

    @ApiModelProperty(value="技术资质")
    private String technicalLevel;

    @ApiModelProperty(value="创建时间")
    private Date createTime;

    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    public PositionVO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getTechnicalLevel() {
        return technicalLevel;
    }

    public void setTechnicalLevel(String technicalLevel) {
        this.technicalLevel = technicalLevel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
