package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonAllInfoHistoryMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAllInfoHistory;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.BpmTaskService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.BpmTaskDto;
import cn.gwssi.ecloud.staffpool.dto.LabelDTO;
import cn.gwssi.ecloudbpm.wf.core.manager.TaskIdentityLinkManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTask;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Api(description = "审批中心")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/approveCenter")
public class ApproveCenterController extends BaseController<BpmTask> {
    @Resource
    private TaskIdentityLinkManager taskIdentityLinkManager;
    @Resource
    private BpmTaskService bpmTaskService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompPersonAllInfoHistoryMapper uompPersonAllInfoHistoryMapper;

    @Override
    protected String getModelDesc() {
        return "";
    }

    @ApiOperation(value = "待办")
    @RequestMapping(value = "/todoTaskList")
    public PageResult todoTaskList(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam(value = "defIds", required = false) @ApiParam(value = "待审事项") String defIds,
                                   @RequestParam(value = "subject", required = false) @ApiParam(value = "标题") String subject,
                                   @RequestParam(value = "intimeStart", required = false) @ApiParam(value = "转入时间起") String intimeStart,
                                   @RequestParam(value = "intimeEnd", required = false) @ApiParam(value = "转入时间止") String intimeEnd,
                                   @RequestParam(value = "appTodoType", required = false) @ApiParam(value = "待办类型 移动端用") String appTodoType,
                                   @RequestParam(value = "createStartTime", required = false) @ApiParam(value = "创建开始时间") String createStartTime ,
                                   @RequestParam(value = "createEndTime", required = false) @ApiParam(value = "创建结束时间") String createEndTime,
                                   @RequestParam(value = "sort", required = false) @ApiParam(value = "排序字段") String sort,
                                   @RequestParam(value = "orderBy", required = false) @ApiParam(value = "排序字段") String orderBy,
                                   @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                   @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        Set<String> userRights = taskIdentityLinkManager.getUserRights(user.getUserId());
        List<BpmTaskDto> bpmTaskDtos = bpmTaskService.selectTodoList(userRights, user.getUserId(), appTodoType,
                intimeStart, intimeEnd, subject, createStartTime,createEndTime,defIds, pageNo, pageSize,sort,orderBy);
        if (bpmTaskDtos != null && bpmTaskDtos.size() > 0) {
            for (BpmTaskDto dto : bpmTaskDtos) {
                if (dto.getNodeId() != null && dto.getNodeId().contains("UOMP_PERSON_APPLY")) {
//                    UompPersonAllInfoHistory history = uompPersonAllInfoHistoryMapper.getByInstId(dto.getInstId());
//                    if(history != null){
//                        dto.setPersonInfoId(history.getPersonId());
//                    }
////
                    QueryFilter queryFilter = new DefaultQueryFilter(true);
                    queryFilter.addFilter("INST_ID", dto.getInstId(), QueryOP.EQUAL);
                    List<UompPersonInfo> personInfoList = uompPersonInfoService.query(queryFilter);
                    if (personInfoList != null && personInfoList.size() > 0) {
                        dto.setPersonInfoId(personInfoList.get(0).getId());
                    }
                }
            }
        }
        return new PageResult(bpmTaskDtos != null ? bpmTaskDtos : new ArrayList(),
                bpmTaskService.selectTodoListTotal(userRights, user.getUserId(), appTodoType,
                        intimeStart, intimeEnd, subject, defIds,createStartTime,createEndTime));
    }

    @ApiOperation(value = "已办")
    @RequestMapping(value = "/approveInstList")
    public PageResult approveInstList(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam(value = "defIds", required = false) @ApiParam(value = "待审事项") String defIds,
                                      @RequestParam(value = "subject", required = false) @ApiParam(value = "标题") String subject,
                                      @RequestParam(value = "intimeStart", required = false) @ApiParam(value = "转入时间起") String intimeStart,
                                      @RequestParam(value = "intimeEnd", required = false) @ApiParam(value = "转入时间止") String intimeEnd,
                                      @RequestParam(value = "appTodoType", required = false) @ApiParam(value = "待办类型 移动端用") String appTodoType,
                                      @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                      @RequestParam(value = "sort") @ApiParam(value = "排序") String sort,
                                      @RequestParam(value = "orderBy") @ApiParam(value = "排序") String orderBy,
                                      @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        Set<String> userRights = taskIdentityLinkManager.getUserRights(user.getUserId());
        List<BpmTaskDto> bpmTaskDtos = bpmTaskService.selectInstList(userRights, user.getUserId(), appTodoType,
                intimeStart, intimeEnd, subject, defIds, pageNo, pageSize,sort,orderBy);
        if (bpmTaskDtos != null && bpmTaskDtos.size() > 0) {
            for (BpmTaskDto dto : bpmTaskDtos) {
                if (dto.getNodeId() != null && dto.getNodeId().contains("UOMP_PERSON_APPLY")) {
//                    UompPersonAllInfoHistory history = uompPersonAllInfoHistoryMapper.getByInstId(dto.getInstId());
//                    if(history != null){`
//                        dto.setPersonInfoId(history.getPersonId());
//                    }
                    QueryFilter queryFilter = new DefaultQueryFilter(true);
                    queryFilter.addFilter("INST_ID", dto.getInstId(), QueryOP.EQUAL);
                    List<UompPersonInfo> personInfoList = uompPersonInfoService.query(queryFilter);
                    if (personInfoList != null && personInfoList.size() > 0) {
                        dto.setPersonInfoId(personInfoList.get(0).getId());
                    }
                }
            }
        }
        return new PageResult(bpmTaskDtos != null ? bpmTaskDtos : new ArrayList(), bpmTaskService.selectInstListTotal(userRights, user.getUserId(), appTodoType, intimeStart, intimeEnd, subject, defIds));
    }

    @ApiOperation(value = "待审事项")
    @RequestMapping(value = "/approveList")
    public ResultMsg<List<LabelDTO>> approveList(HttpServletRequest request, HttpServletResponse response) {
        return this.getSuccessResult(bpmTaskService.selectApprove());
    }
}
