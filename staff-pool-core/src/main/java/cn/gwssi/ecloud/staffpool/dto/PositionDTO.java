package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModelProperty;

/**
 * 岗位信息DTO
 */
public class PositionDTO {

    @ApiModelProperty(value="岗位ID")
    private String id;

    @ApiModelProperty(value="岗位名称")
    private String name;

    @ApiModelProperty(value="权限")
    private String permission;

    @ApiModelProperty(value="技术资质")
    private String technicalLevel;

    public PositionDTO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getTechnicalLevel() {
        return technicalLevel;
    }

    public void setTechnicalLevel(String technicalLevel) {
        this.technicalLevel = technicalLevel;
    }
}
