package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail;
import cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonDutyDetailMapper extends BaseDao<String, UompPersonDutyDetail> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonDutyDetail record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonDutyDetail record);

    void removeByDutyId(String dutyId);

    List<UompPersonContactsDTO> selectByDutyId(String dutyId);

    List<UompPersonDutyDto> selectByMonth(@Param("month") String month,  @Param("orgId") String orgId);
}
