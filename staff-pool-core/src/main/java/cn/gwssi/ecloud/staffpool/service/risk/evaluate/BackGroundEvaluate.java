package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 背调缺失
 */
@Component
public class BackGroundEvaluate extends RiskEvaluateChain {

    private static final Logger LOG = LoggerFactory.getLogger(BackGroundEvaluate.class);


    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {

    }

    @Override
    public String alias() {
        return "backGround";
    }
}
