package cn.gwssi.ecloud.staffpool.core.entity.risk;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

public class TeamRiskConfig extends BaseModel {

    @ApiModelProperty(value="风险指标名称")
    private String name;

    @ApiModelProperty(value="风险类型")
    private String type;

    @ApiModelProperty(value="风险因素")
    private String factor;

    @ApiModelProperty(value="风险等级")
    private String level;

    @ApiModelProperty(value="风险判断规则")
    private String rule;

    @ApiModelProperty(value="状态")
    private String status;

    @ApiModelProperty(value="逻辑删除标记 0-有效 1-无效")
    private String delFlag;

    public TeamRiskConfig() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFactor() {
        return factor;
    }

    public void setFactor(String factor) {
        this.factor = factor;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
