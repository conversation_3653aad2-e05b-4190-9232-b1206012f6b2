package cn.gwssi.ecloud.staffpool.service;

import cn.gwssi.ecloud.staffpool.annotation.SensitiveEnum;
import cn.gwssi.ecloud.staffpool.api.model.TestDTO;
import cn.gwssi.ecloud.staffpool.api.service.ITestService;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

public class TestService {

    static final String ALGORITHM = "AES";

    static final String AES_KEY = "1234567890abcdef";

    public static void main(String[] args) throws Exception{


        System.out.println("".contains(null));


    }


    static Object getFieldValue(Object obj,String fieldName){
        if (obj == null || fieldName == null ) return null;
        Class<?> clazz = obj.getClass();
        while (clazz != null){
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                if (field.get(obj) instanceof String) return (String) field.get(obj);
                return null;
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            } catch (IllegalAccessException e) {
                throw new RuntimeException("无法访问字段"+fieldName,e);
            }
        }
        throw new RuntimeException("字段不存在"+fieldName);
    }
    private static boolean isCustomObject(Object object){
        if (object == null) return false;
        String pkg = object.getClass().getPackage().getName();
        return !pkg.startsWith("java.") && !object.getClass().isPrimitive();
    }

}
