package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.base.ResponseData;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompTeamRiskPosition;
import cn.gwssi.ecloud.staffpool.core.manager.UompTeamRiskPositionManager;
import cn.gwssi.ecloud.staffpool.dto.PositionDTO;
import cn.gwssi.ecloud.staffpool.dto.PositionQueryDTO;
import cn.gwssi.ecloud.staffpool.vo.PositionVO;
import cn.gwssi.ecloudframework.base.core.util.BeanUtils;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.db.model.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.query.QueryOP;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 运维岗位职责管理Controller
 */
@Api(tags = "运维岗位职责管理")
@RestController
@RequestMapping("/module/staff-pool/position")
public class PositionController {

    @Resource
    private UompTeamRiskPositionManager uompTeamRiskPositionManager;

    /**
     * 4.1 岗位列表
     */
    @ApiOperation("分页查询运维岗位列表")
    @PostMapping("/list")
    public ResponsePageData<PositionVO> getPositionList(@RequestBody PositionQueryDTO queryDTO) {
        try {
            QueryFilter queryFilter = new DefaultQueryFilter();
            
            // 添加查询条件
            if (StringUtil.isNotEmpty(queryDTO.getName())) {
                queryFilter.addFilter("NAME", queryDTO.getName(), QueryOP.LK);
            }
            
            PageResult<UompTeamRiskPosition> pageResult = uompTeamRiskPositionManager.query(queryFilter);
            
            // 转换为VO
            List<PositionVO> voList = new ArrayList<>();
            for (UompTeamRiskPosition position : pageResult.getRows()) {
                PositionVO vo = new PositionVO();
                BeanUtils.copyProperties(position, vo);
                voList.add(vo);
            }
            
            return ResponsePageData.ok(voList, pageResult.getTotal());
        } catch (Exception e) {
            return ResponsePageData.fail("查询岗位列表失败：" + e.getMessage());
        }
    }

    /**
     * 4.2 新建岗位
     */
    @ApiOperation("新建运维岗位")
    @PostMapping("/create")
    public ResponseData<String> createPosition(@RequestBody PositionDTO positionDTO) {
        try {
            UompTeamRiskPosition position = new UompTeamRiskPosition();
            BeanUtils.copyProperties(positionDTO, position);
            
            uompTeamRiskPositionManager.create(position);
            
            return ResponseData.ok("创建岗位成功");
        } catch (Exception e) {
            return ResponseData.fail("创建岗位失败：" + e.getMessage());
        }
    }

    /**
     * 4.3 编辑岗位
     */
    @ApiOperation("编辑运维岗位信息")
    @PutMapping("/{positionId}")
    public ResponseData<String> updatePosition(@PathVariable String positionId, @RequestBody PositionDTO positionDTO) {
        try {
            UompTeamRiskPosition position = uompTeamRiskPositionManager.get(positionId);
            if (position == null) {
                return ResponseData.fail("岗位不存在");
            }
            
            BeanUtils.copyProperties(positionDTO, position);
            position.setId(positionId);
            
            uompTeamRiskPositionManager.update(position);
            
            return ResponseData.ok("更新岗位成功");
        } catch (Exception e) {
            return ResponseData.fail("更新岗位失败：" + e.getMessage());
        }
    }

    /**
     * 4.4 删除岗位
     */
    @ApiOperation("批量删除运维岗位")
    @DeleteMapping
    public ResponseData<String> deletePositions(@RequestBody List<String> positionIds) {
        try {
            for (String positionId : positionIds) {
                uompTeamRiskPositionManager.remove(positionId);
            }
            
            return ResponseData.ok("删除岗位成功");
        } catch (Exception e) {
            return ResponseData.fail("删除岗位失败：" + e.getMessage());
        }
    }
}
