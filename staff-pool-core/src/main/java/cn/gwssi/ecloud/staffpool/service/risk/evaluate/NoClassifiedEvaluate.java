package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 保密协议缺失
 */
@Component
public class NoClassifiedEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(NoClassifiedEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        LOG.info("------用户{}计算保密协议缺失------",uompPersonInfoVo.getPersonName());

    }

    @Override
    public String alias() {
        return "noClassified";
    }
}
