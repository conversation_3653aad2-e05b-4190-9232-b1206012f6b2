package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEducationalDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonEducationalMapper extends BaseDao<String, UompPersonEducational> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonEducational record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonEducational record);

    int updateByPersonId(UompPersonEducational record);

    int updateByPersonIds(@Param("orgId") String orgId, @Param("ids") String[] ids);

    List<UompPersonEducationalDto> selectByPersonIds(@Param("personIds")List<String> personIds);

    void deleteByPersonId(String personId);
}