package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.OrgPost;
import cn.gwssi.ecloud.staffpool.dto.OrgPostDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgPostTypeDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrgPostMapper extends BaseDao<String, OrgPost> {

    List<OrgPostTypeDTO> getPostByOrgType(@Param("name") String name, @Param("orgType") String orgType);

    String selectPositionsByUserId(@Param("userId") String userId);

    String selectPositionsByAccount(@Param("account") String account);

    List<UompPermissionBaseDTO> selectOwerPostByUserId(QueryFilter queryFilter);

    List<UompPermissionBaseDTO> selectOwerNoPostByUserId(@Param("userId") String userId, @Param("orgType") String orgType);

    String selectNameByUserIdAndTypeIn(String userId);

    String selectNameByUserIdAndTypeNotIn(String userId);

    List<OrgPostDTO> selectAllByUserId(String userId);

    List<OrgPostDTO> selectAllByUserName(String userId);

    int countByPostKey(String postKey);

    List<OrgPost> queryAllPost();
}
