package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonNoCrimeMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonNoCrimeService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonNoCrimeDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonNoCrimeServiceImpl extends BaseManager<String, UompPersonNoCrime> implements UompPersonNoCrimeService {

    @Resource
    private UompPersonNoCrimeMapper uompPersonNoCrimeMapper;

    @Override
    public int insertSelective(UompPersonNoCrime record) {
        return uompPersonNoCrimeMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonNoCrime record) {
        return uompPersonNoCrimeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonNoCrime record) {
        return uompPersonNoCrimeMapper.updateByPersonId(record);
    }

    @Override
    public int updateByPersonIds(String orgId, String[] personIds) {
        return uompPersonNoCrimeMapper.updateByPersonIds(orgId, personIds);
    }

    @Override
    public List<UompPersonNoCrimeDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonNoCrimeMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonNoCrimeMapper.deleteByPersonId(personId);
    }
}
