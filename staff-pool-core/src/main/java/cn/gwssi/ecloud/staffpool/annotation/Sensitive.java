package cn.gwssi.ecloud.staffpool.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * prefixLen 为前辍长度
 * suffixLen 为后辍长度
 * actualLen 为字符串真实长度
 * 如手机号直实长度为11 那么actualLen = 11
 * 想显示为186****8431 那么prefixLen = 3 suffixLen = 4
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Sensitive {
    int prefixLen()  default 3;
    int suffixLen () default 3;

    int actualLen() default 11;
}
