package cn.gwssi.ecloud.staffpool.core.model;

import cn.gwssi.ecloudframework.base.api.model.Tree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(description = "机构树")
public class OrgTreeUomp extends GroupUomp implements Tree<OrgTreeUomp> {
    private static final long serialVersionUID = -700694295167942753L;
    @ApiModelProperty("图标")
    protected String icon;
    @ApiModelProperty("是否选中")
    protected boolean nocheck = false;
    @ApiModelProperty("是否不显示")
    protected boolean chkDisabled = false;
    @ApiModelProperty("是否点击")
    protected boolean click = true;
    @ApiModelProperty("前端title")
    protected String title;
    @ApiModelProperty("子节点")
    protected List<OrgTreeUomp> children;

    public OrgTreeUomp() {
    }

    public OrgTreeUomp(String name, String id, String parentId, String icon) {
        setName(name);
        this.parentId = parentId;
        this.id = id;
        this.icon = icon;

    }

    /**
     * GroupList2TreeList
     */
    public static List<OrgTreeUomp> GroupList2TreeList(List<GroupUomp> groupUompList, String icon) {
        if (groupUompList == null || groupUompList.size() == 0) {
            return Collections.emptyList();
        }

        List<OrgTreeUomp> groupTreeList = new ArrayList<>();
        for (GroupUomp groupUomp : groupUompList) {
            OrgTreeUomp grouptree = new OrgTreeUomp(groupUomp);
            grouptree.setIcon(icon);
            groupTreeList.add(grouptree);
        }
        return groupTreeList;
    }

    public OrgTreeUomp(GroupUomp groupUomp) {
        this.id = groupUomp.getId();
        this.name = groupUomp.name;
        this.code = groupUomp.code;
        this.sn = groupUomp.sn;
        this.parentId = groupUomp.parentId;
        this.path = groupUomp.path;
        this.type = groupUomp.type;
        this.desc = groupUomp.desc;
        this.orgGroupId = groupUomp.orgGroupId;
        this.simple = groupUomp.simple;
        this.respName = groupUomp.respName;
        this.respId = groupUomp.respId;
        this.status = groupUomp.status;
        this.virtual = groupUomp.virtual;
        if (!this.name.contains("style=")) {
            this.title = name;
        }
    }

    @Override
    public void setName(String name) {
        this.name = name;
        // 将title 设置成name
        if ("".equals(title) && !this.name.contains("style=")) {
            this.title = name;
        }
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isNocheck() {
        return nocheck;
    }

    public void setNocheck(boolean nocheck) {
        this.nocheck = nocheck;
    }

    public boolean isChkDisabled() {
        return chkDisabled;
    }

    public boolean isClick() {
        return click;
    }

    public void setClick(boolean click) {
        this.click = click;
    }

    public void setChkDisabled(boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }

    @Override
    public List<OrgTreeUomp> getChildren() {
        return children;
    }

    @Override
    public void setChildren(List<OrgTreeUomp> list) {
        this.children = list;
    }
}
