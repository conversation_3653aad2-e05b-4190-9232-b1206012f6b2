package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

import java.util.Date;

public class UompAccountApplyPerson extends BaseModel {

    private String applyId;

    private String personId;

    private String personName;

    private String role;

    private String permission;

    private String position;

    private String accountNum;

    private String createOrgId;

    private String updateOrgId;

    private String entryDate;

    private String personJson;

    private String authorizationStatus;

    private String orgUserId;

    private String maintenanceGroupId;

    private String maintenanceGroupJson;

    private String engagementProjectId;

    private String engagementProjectJson;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getAccountNum() {
        return accountNum;
    }

    public void setAccountNum(String accountNum) {
        this.accountNum = accountNum;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(String entryDate) {
        this.entryDate = entryDate;
    }

    public String getPersonJson() {
        return personJson;
    }

    public void setPersonJson(String personJson) {
        this.personJson = personJson;
    }

    public String getAuthorizationStatus() {
        return authorizationStatus;
    }

    public void setAuthorizationStatus(String authorizationStatus) {
        this.authorizationStatus = authorizationStatus;
    }

    public String getOrgUserId() {
        return orgUserId;
    }

    public void setOrgUserId(String orgUserId) {
        this.orgUserId = orgUserId;
    }

    public String getMaintenanceGroupId() {
        return maintenanceGroupId;
    }

    public void setMaintenanceGroupId(String maintenanceGroupId) {
        this.maintenanceGroupId = maintenanceGroupId;
    }

    public String getMaintenanceGroupJson() {
        return maintenanceGroupJson;
    }

    public void setMaintenanceGroupJson(String maintenanceGroupJson) {
        this.maintenanceGroupJson = maintenanceGroupJson;
    }

    public String getEngagementProjectId() {
        return engagementProjectId;
    }

    public void setEngagementProjectId(String engagementProjectId) {
        this.engagementProjectId = engagementProjectId;
    }

    public String getEngagementProjectJson() {
        return engagementProjectJson;
    }

    public void setEngagementProjectJson(String engagementProjectJson) {
        this.engagementProjectJson = engagementProjectJson;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyId=").append(applyId);
        sb.append(", personId=").append(personId);
        sb.append(", personName=").append(personName);
        sb.append(", role=").append(role);
        sb.append(", permission=").append(permission);
        sb.append(", position=").append(position);
        sb.append(", accountNum=").append(accountNum);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", createOrgId=").append(createOrgId);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateOrgId=").append(updateOrgId);
        sb.append(", entryDate=").append(entryDate);
        sb.append(", personJson=").append(personJson);
        sb.append(", authorizationStatus=").append(authorizationStatus);
        sb.append(", orgUserId=").append(orgUserId);
        sb.append(", maintenanceGroupId=").append(maintenanceGroupId);
        sb.append(", maintenanceGroupJson=").append(maintenanceGroupJson);
        sb.append(", engagementProjectId=").append(engagementProjectId);
        sb.append(", engagementProjectJson=").append(engagementProjectJson);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompAccountApplyPerson other = (UompAccountApplyPerson) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getApplyId() == null ? other.getApplyId() == null : this.getApplyId().equals(other.getApplyId()))
                && (this.getPersonId() == null ? other.getPersonId() == null : this.getPersonId().equals(other.getPersonId()))
                && (this.getPersonName() == null ? other.getPersonName() == null : this.getPersonName().equals(other.getPersonName()))
                && (this.getRole() == null ? other.getRole() == null : this.getRole().equals(other.getRole()))
                && (this.getPermission() == null ? other.getPermission() == null : this.getPermission().equals(other.getPermission()))
                && (this.getPosition() == null ? other.getPosition() == null : this.getPosition().equals(other.getPosition()))
                && (this.getAccountNum() == null ? other.getAccountNum() == null : this.getAccountNum().equals(other.getAccountNum()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateOrgId() == null ? other.getCreateOrgId() == null : this.getCreateOrgId().equals(other.getCreateOrgId()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getUpdateOrgId() == null ? other.getUpdateOrgId() == null : this.getUpdateOrgId().equals(other.getUpdateOrgId()))
                && (this.getEntryDate() == null ? other.getEntryDate() == null : this.getEntryDate().equals(other.getEntryDate()))
                && (this.getPersonJson() == null ? other.getPersonJson() == null : this.getPersonJson().equals(other.getPersonJson()))
                && (this.getAuthorizationStatus() == null ? other.getAuthorizationStatus() == null : this.getAuthorizationStatus().equals(other.getAuthorizationStatus()))
                && (this.getOrgUserId() == null ? other.getOrgUserId() == null : this.getOrgUserId().equals(other.getOrgUserId()))
                && (this.getMaintenanceGroupId() == null ? other.getMaintenanceGroupId() == null : this.getMaintenanceGroupId().equals(other.getMaintenanceGroupId()))
                && (this.getMaintenanceGroupJson() == null ? other.getMaintenanceGroupJson() == null : this.getMaintenanceGroupJson().equals(other.getMaintenanceGroupJson()))
                && (this.getEngagementProjectId() == null ? other.getEngagementProjectId() == null : this.getEngagementProjectId().equals(other.getEngagementProjectId()))
                && (this.getEngagementProjectJson() == null ? other.getEngagementProjectJson() == null : this.getEngagementProjectJson().equals(other.getEngagementProjectJson()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyId() == null) ? 0 : getApplyId().hashCode());
        result = prime * result + ((getPersonId() == null) ? 0 : getPersonId().hashCode());
        result = prime * result + ((getPersonName() == null) ? 0 : getPersonName().hashCode());
        result = prime * result + ((getRole() == null) ? 0 : getRole().hashCode());
        result = prime * result + ((getPermission() == null) ? 0 : getPermission().hashCode());
        result = prime * result + ((getPosition() == null) ? 0 : getPosition().hashCode());
        result = prime * result + ((getAccountNum() == null) ? 0 : getAccountNum().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateOrgId() == null) ? 0 : getCreateOrgId().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateOrgId() == null) ? 0 : getUpdateOrgId().hashCode());
        result = prime * result + ((getEntryDate() == null) ? 0 : getEntryDate().hashCode());
        result = prime * result + ((getPersonJson() == null) ? 0 : getPersonJson().hashCode());
        result = prime * result + ((getAuthorizationStatus() == null) ? 0 : getAuthorizationStatus().hashCode());
        result = prime * result + ((getOrgUserId() == null) ? 0 : getOrgUserId().hashCode());
        result = prime * result + ((getMaintenanceGroupId() == null) ? 0 : getMaintenanceGroupId().hashCode());
        result = prime * result + ((getMaintenanceGroupJson() == null) ? 0 : getMaintenanceGroupJson().hashCode());
        result = prime * result + ((getEngagementProjectId() == null) ? 0 : getEngagementProjectId().hashCode());
        result = prime * result + ((getEngagementProjectJson() == null) ? 0 : getEngagementProjectJson().hashCode());
        return result;
    }
}