package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_config")
@Data
public class UompPersonConfig extends BaseModel {
    @ApiModelProperty(value = "")
    private String configInfo;

    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String configType;

    private static final long serialVersionUID = 1L;
}