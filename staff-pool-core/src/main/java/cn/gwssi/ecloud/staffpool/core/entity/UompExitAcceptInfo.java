package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

public class UompExitAcceptInfo extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String exitApplyId;

    @ApiModelProperty(value="")
    
    private String engagementProjectId;

    @ApiModelProperty(value="")
    
    private String engagementProjectName;

    @ApiModelProperty(value="")
    
    private String acceptUserId;

    @ApiModelProperty(value="")
    
    private String acceptUserName;

    @ApiModelProperty(value="")
    
    private String acceptContext;

    @ApiModelProperty(value="")
    
    private String acceptContextFile;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String delFlag;

    private static final long serialVersionUID = 1L;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getExitApplyId() {
        return exitApplyId;
    }

    public void setExitApplyId(String exitApplyId) {
        this.exitApplyId = exitApplyId;
    }

    public String getEngagementProjectId() {
        return engagementProjectId;
    }

    public void setEngagementProjectId(String engagementProjectId) {
        this.engagementProjectId = engagementProjectId;
    }

    public String getEngagementProjectName() {
        return engagementProjectName;
    }

    public void setEngagementProjectName(String engagementProjectName) {
        this.engagementProjectName = engagementProjectName;
    }

    public String getAcceptUserId() {
        return acceptUserId;
    }

    public void setAcceptUserId(String acceptUserId) {
        this.acceptUserId = acceptUserId;
    }

    public String getAcceptUserName() {
        return acceptUserName;
    }

    public void setAcceptUserName(String acceptUserName) {
        this.acceptUserName = acceptUserName;
    }

    public String getAcceptContext() {
        return acceptContext;
    }

    public void setAcceptContext(String acceptContext) {
        this.acceptContext = acceptContext;
    }

    public String getAcceptContextFile() {
        return acceptContextFile;
    }

    public void setAcceptContextFile(String acceptContextFile) {
        this.acceptContextFile = acceptContextFile;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}