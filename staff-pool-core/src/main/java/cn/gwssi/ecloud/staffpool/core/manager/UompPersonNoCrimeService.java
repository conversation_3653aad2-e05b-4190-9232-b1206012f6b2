package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonNoCrime;
import cn.gwssi.ecloud.staffpool.dto.UompPersonNoCrimeDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonNoCrimeService extends Manager<String, UompPersonNoCrime> {

    int insertSelective(UompPersonNoCrime record);

    int updateByPrimaryKeySelective(UompPersonNoCrime record);

    int updateByPersonId(UompPersonNoCrime record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonNoCrimeDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
