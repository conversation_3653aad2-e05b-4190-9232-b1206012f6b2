package cn.gwssi.ecloud.staffpool.core.model;

import cn.gwssi.ecloud.staffpool.dto.OrgPersonInfoDTO;
import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.GroupCustomForPathName;
import cn.gwssi.ecloudframework.org.api.constant.GroupTypeConstant;
import cn.gwssi.ecloudframework.org.api.model.IGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.parser.Feature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

@ApiModel(
        description = "自定义组"
)
public class GroupUomp extends BaseModel implements IGroup {
    private static final long serialVersionUID = -700694295167942753L;
    @ApiModelProperty("名字")
    protected String name;
    @ApiModelProperty("父ID")
    protected String parentId;
    @ApiModelProperty("编码")
    protected String code;
    @ApiModelProperty("类型")
    protected Integer type;
    @ApiModelProperty("描述")
    protected String desc;
    @ApiModelProperty("路径")
    protected String path;
    @ApiModelProperty("排序")
    protected Integer sn;
    @ApiModelProperty("简称")
    protected String simple;
    @ApiModelProperty("显示名")
    protected String showName;
    @ApiModelProperty("状态:0禁用,1正常")
    protected Integer status;
    @ApiModelProperty("是否虚拟:0否,1是")
    protected Integer virtual;
    @ApiModelProperty("曾用名")
    protected String historyName;
    @JSONField(
            serialize = false
    )
    @ApiModelProperty("路径名")
    protected String pathName;
    @ApiModelProperty("主编码")
    protected String mcode;
    @ApiModelProperty("组人数")
    protected Integer userNum;
    @ApiModelProperty("父机构名称")
    protected String parentName;
    @ApiModelProperty("机构路径信息")
    protected List<GroupCustomForPathName> lstPathName;

    @ApiModelProperty("负责人")
    protected String respName;
    @ApiModelProperty("负责人 id")
    protected String respId;
    @ApiModelProperty("组织 id")
    protected String orgGroupId;

    @ApiModelProperty("组下人员")
    protected List<OrgPersonInfoDTO> orgPersonInfoDTOList;

    public GroupUomp() {
    }

    public static GroupUomp GroupCustomForPathName(GroupUomp groupCustom) {
        GroupUomp temp = new GroupUomp();
        temp.setId(groupCustom.getId());
        temp.setName(groupCustom.getName());
        temp.setCode(groupCustom.getCode());
        temp.setVirtual(groupCustom.getVirtual());
        temp.setType(groupCustom.getType());
        temp.setRespName(groupCustom.getRespName());
        temp.setOrgGroupId(groupCustom.getOrgGroupId());
        return temp;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentId() {
        return this.parentId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return this.type;
    }

    public String getParentName() {
        return this.parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getPath() {
        return this.path;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    public Integer getSn() {
        return this.sn;
    }

    public String getGroupId() {
        return this.id;
    }

    public String getGroupCode() {
        return this.code;
    }

    public String getGroupType() {
        return GroupTypeConstant.ORG.key();
    }

    public Integer getGroupLevel() {
        return this.type;
    }

    public String getGroupName() {
        return this.name;
    }

    public Integer getUserNum() {
        return this.userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public String getSimple() {
        return this.simple;
    }

    public void setSimple(String simple) {
        this.simple = simple;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getShowName() {
        return this.showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public Integer getVirtual() {
        return this.virtual;
    }

    public void setVirtual(Integer virtual) {
        this.virtual = virtual;
    }

    public String getHistoryName() {
        return this.historyName;
    }

    public void setHistoryName(String historyName) {
        this.historyName = historyName;
    }

    public String getPathName() {
        return this.pathName;
    }

    public void setPathName(String pathName) {
        this.pathName = pathName;
    }

    public String getMcode() {
        return this.mcode;
    }

    public void setMcode(String mcode) {
        this.mcode = mcode;
    }

    public List<GroupCustomForPathName> getLstPathName() {
        if (null == this.lstPathName) {
            try {
                if (StringUtils.isNotEmpty(this.pathName)) {
                    this.lstPathName = (List)JSON.parseObject(this.pathName, new TypeReference<List<GroupCustomForPathName>>() {
                    }, new Feature[0]);
                }
            } catch (Exception var2) {
            }
        }

        return this.lstPathName;
    }

    public void setLstPathName(List<GroupCustomForPathName> lstPathName) {
        this.lstPathName = lstPathName;
    }

    public String getRespName() {
        return respName;
    }

    public void setRespName(String respName) {
        this.respName = respName;
    }

    public String getOrgGroupId() {
        return orgGroupId;
    }

    public void setOrgGroupId(String orgGroupId) {
        this.orgGroupId = orgGroupId;
    }

    public String getRespId() {
        return respId;
    }

    public void setRespId(String respId) {
        this.respId = respId;
    }

    public List<OrgPersonInfoDTO> getOrgPersonInfoDTOList() {
        return orgPersonInfoDTOList;
    }

    public void setOrgPersonInfoDTOList(List<OrgPersonInfoDTO> orgPersonInfoDTOList) {
        this.orgPersonInfoDTOList = orgPersonInfoDTOList;
    }
}
