package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.manager.UompTeamService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.gwssi.ecloud.staffpool.core.dao.UompTeamMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTeam;

import java.util.List;

@Service
public class UompTeamServiceImpl extends BaseManager<String, UompTeam> implements UompTeamService {

    @Autowired
    private UompTeamMapper uompTeamMapper;

    public int insertSelective(UompTeam record) {
        return uompTeamMapper.insertSelective(record);
    }

    @Override
    public List<BaseDTO> selectAllByStatus() {
        return uompTeamMapper.selectAllByStatus();
    }
}
