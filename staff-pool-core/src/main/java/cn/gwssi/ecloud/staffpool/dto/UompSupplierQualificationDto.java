package cn.gwssi.ecloud.staffpool.dto;

import java.io.Serializable;
import java.util.Date;

public class UompSupplierQualificationDto implements Serializable {
    private String id;

    private String supplier_management_id;

    private String certificate_name;

    private String certificate_num;

    private String issuing_authority;

    private String issuing_date;

    private String end_time;

    private String files;

    private String standards;

    private String statement;

    private String business_area;

    private String assessment_level;

    private String business_line;

    private String qualification_level;

    private String trial_area;

    private String certification_sub_item;

    private String other_name;

    private String createBy;

    private Date createTime;

    private String createOrgId;

    private String updateBy;

    private Date updateTime;

    private String updateOrgId;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplier_management_id() {
        return supplier_management_id;
    }

    public void setSupplier_management_id(String supplier_management_id) {
        this.supplier_management_id = supplier_management_id;
    }

    public String getCertificate_name() {
        return certificate_name;
    }

    public void setCertificate_name(String certificate_name) {
        this.certificate_name = certificate_name;
    }

    public String getCertificate_num() {
        return certificate_num;
    }

    public void setCertificate_num(String certificate_num) {
        this.certificate_num = certificate_num;
    }

    public String getIssuing_authority() {
        return issuing_authority;
    }

    public void setIssuing_authority(String issuing_authority) {
        this.issuing_authority = issuing_authority;
    }

    public String getIssuing_date() {
        return issuing_date;
    }

    public void setIssuing_date(String issuing_date) {
        this.issuing_date = issuing_date;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getStandards() {
        return standards;
    }

    public void setStandards(String standards) {
        this.standards = standards;
    }

    public String getStatement() {
        return statement;
    }

    public void setStatement(String statement) {
        this.statement = statement;
    }

    public String getBusiness_area() {
        return business_area;
    }

    public void setBusiness_area(String business_area) {
        this.business_area = business_area;
    }

    public String getAssessment_level() {
        return assessment_level;
    }

    public void setAssessment_level(String assessment_level) {
        this.assessment_level = assessment_level;
    }

    public String getBusiness_line() {
        return business_line;
    }

    public void setBusiness_line(String business_line) {
        this.business_line = business_line;
    }

    public String getQualification_level() {
        return qualification_level;
    }

    public void setQualification_level(String qualification_level) {
        this.qualification_level = qualification_level;
    }

    public String getTrial_area() {
        return trial_area;
    }

    public void setTrial_area(String trial_area) {
        this.trial_area = trial_area;
    }

    public String getCertification_sub_item() {
        return certification_sub_item;
    }

    public void setCertification_sub_item(String certification_sub_item) {
        this.certification_sub_item = certification_sub_item;
    }

    public String getOther_name() {
        return other_name;
    }

    public void setOther_name(String other_name) {
        this.other_name = other_name;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}