package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

import java.util.Date;

public class UompPermissionOutBase extends BaseModel {

    private String applyId;

    private String outApplySysType;

    private String outApplySysId;

    private String outApplySysName;

    private String outApplySysJson;

    private String outApplyTitle;

    private String outApplyUserId;

    private String outApplyUserName;

    private Date outApplyTime;

    private String outApplyMatter;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getOutApplySysType() {
        return outApplySysType;
    }

    public void setOutApplySysType(String outApplySysType) {
        this.outApplySysType = outApplySysType;
    }

    public String getOutApplySysId() {
        return outApplySysId;
    }

    public void setOutApplySysId(String outApplySysId) {
        this.outApplySysId = outApplySysId;
    }

    public String getOutApplySysName() {
        return outApplySysName;
    }

    public void setOutApplySysName(String outApplySysName) {
        this.outApplySysName = outApplySysName;
    }

    public String getOutApplySysJson() {
        return outApplySysJson;
    }

    public void setOutApplySysJson(String outApplySysJson) {
        this.outApplySysJson = outApplySysJson;
    }

    public String getOutApplyTitle() {
        return outApplyTitle;
    }

    public void setOutApplyTitle(String outApplyTitle) {
        this.outApplyTitle = outApplyTitle;
    }

    public String getOutApplyUserId() {
        return outApplyUserId;
    }

    public void setOutApplyUserId(String outApplyUserId) {
        this.outApplyUserId = outApplyUserId;
    }

    public String getOutApplyUserName() {
        return outApplyUserName;
    }

    public void setOutApplyUserName(String outApplyUserName) {
        this.outApplyUserName = outApplyUserName;
    }

    public Date getOutApplyTime() {
        return outApplyTime;
    }

    public void setOutApplyTime(Date outApplyTime) {
        this.outApplyTime = outApplyTime;
    }

    public String getOutApplyMatter() {
        return outApplyMatter;
    }

    public void setOutApplyMatter(String outApplyMatter) {
        this.outApplyMatter = outApplyMatter;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyId=").append(applyId);
        sb.append(", outApplySysType=").append(outApplySysType);
        sb.append(", outApplySysId=").append(outApplySysId);
        sb.append(", outApplySysName=").append(outApplySysName);
        sb.append(", outApplySysJson=").append(outApplySysJson);
        sb.append(", outApplyTitle=").append(outApplyTitle);
        sb.append(", outApplyUserId=").append(outApplyUserId);
        sb.append(", outApplyUserName=").append(outApplyUserName);
        sb.append(", outApplyTime=").append(outApplyTime);
        sb.append(", outApplyMatter=").append(outApplyMatter);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompPermissionOutBase other = (UompPermissionOutBase) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getApplyId() == null ? other.getApplyId() == null : this.getApplyId().equals(other.getApplyId()))
                && (this.getOutApplySysType() == null ? other.getOutApplySysType() == null : this.getOutApplySysType().equals(other.getOutApplySysType()))
                && (this.getOutApplySysId() == null ? other.getOutApplySysId() == null : this.getOutApplySysId().equals(other.getOutApplySysId()))
                && (this.getOutApplySysName() == null ? other.getOutApplySysName() == null : this.getOutApplySysName().equals(other.getOutApplySysName()))
                && (this.getOutApplySysJson() == null ? other.getOutApplySysJson() == null : this.getOutApplySysJson().equals(other.getOutApplySysJson()))
                && (this.getOutApplyTitle() == null ? other.getOutApplyTitle() == null : this.getOutApplyTitle().equals(other.getOutApplyTitle()))
                && (this.getOutApplyUserId() == null ? other.getOutApplyUserId() == null : this.getOutApplyUserId().equals(other.getOutApplyUserId()))
                && (this.getOutApplyUserName() == null ? other.getOutApplyUserName() == null : this.getOutApplyUserName().equals(other.getOutApplyUserName()))
                && (this.getOutApplyTime() == null ? other.getOutApplyTime() == null : this.getOutApplyTime().equals(other.getOutApplyTime()))
                && (this.getOutApplyMatter() == null ? other.getOutApplyMatter() == null : this.getOutApplyMatter().equals(other.getOutApplyMatter()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyId() == null) ? 0 : getApplyId().hashCode());
        result = prime * result + ((getOutApplySysType() == null) ? 0 : getOutApplySysType().hashCode());
        result = prime * result + ((getOutApplySysId() == null) ? 0 : getOutApplySysId().hashCode());
        result = prime * result + ((getOutApplySysName() == null) ? 0 : getOutApplySysName().hashCode());
        result = prime * result + ((getOutApplySysJson() == null) ? 0 : getOutApplySysJson().hashCode());
        result = prime * result + ((getOutApplyTitle() == null) ? 0 : getOutApplyTitle().hashCode());
        result = prime * result + ((getOutApplyUserId() == null) ? 0 : getOutApplyUserId().hashCode());
        result = prime * result + ((getOutApplyUserName() == null) ? 0 : getOutApplyUserName().hashCode());
        result = prime * result + ((getOutApplyTime() == null) ? 0 : getOutApplyTime().hashCode());
        result = prime * result + ((getOutApplyMatter() == null) ? 0 : getOutApplyMatter().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}