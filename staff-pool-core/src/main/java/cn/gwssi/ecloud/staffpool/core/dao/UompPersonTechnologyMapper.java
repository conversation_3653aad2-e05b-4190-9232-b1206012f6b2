package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology;
import cn.gwssi.ecloud.staffpool.dto.UompPersonTechnologyDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonTechnologyMapper extends BaseDao<String, UompPersonTechnology> {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonTechnology record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonTechnology record);

    int updateByPersonId(UompPersonTechnology record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonTechnologyDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}