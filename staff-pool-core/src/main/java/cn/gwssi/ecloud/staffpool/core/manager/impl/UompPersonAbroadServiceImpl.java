package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonAbroadMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonAbroad;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonAbroadService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonAbroadDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonAbroadServiceImpl extends BaseManager<String, UompPersonAbroad> implements UompPersonAbroadService {

    @Resource
    private UompPersonAbroadMapper uompPersonAbroadMapper;

    @Override
    public int insertSelective(UompPersonAbroad record) {
        return uompPersonAbroadMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonAbroad record) {
        return uompPersonAbroadMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonAbroad record) {
        return uompPersonAbroadMapper.updateByPersonId(record);
    }

    @Override
    public List<UompPersonAbroadDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonAbroadMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonAbroadMapper.deleteByPersonId(personId);
    }
}
