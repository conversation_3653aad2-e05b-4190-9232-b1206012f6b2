package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;

/**
 * 服务报告信息表
 */
@ApiModel(value = "服务报告信息表", description = "服务报告信息表")
@Data
public class UompReport extends BaseModel {

    /**
     * 报告名称
     */
    @ApiModelProperty(value = "报告名称")
    private String reportName;

    /**
     * 报告编码
     */
    @ApiModelProperty(value = "报告编码")
    private String reportCode;

    /**
     * 报告类型
     */
    @ApiModelProperty(value = "报告类型 （1：周报 2：月报 3：年报）")
    private String reportType;

    /**
     * 报告开始时间
     */
    @ApiModelProperty(value = "报告开始时间")
    private Date reportBegin;

    /**
     * 报告结束时间
     */
    @ApiModelProperty(value = "报告结束时间")
    private Date reportEnd;

    /**
     * 应用系统id
     */
    @ApiModelProperty(value = "应用系统id")
    private String applicationSystemManagementId;

    /**
     * 应用 系统名称
     */
    @ApiModelProperty(value = "应用系统名称")
    private String applicationSystemName;

    /**
     * 服务商
     */
    @ApiModelProperty(value = "服务商")
    private String supplierName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadTime;

    /**
     * 上传人id
     */
    @ApiModelProperty(value = "上传人id")
    private String uploaderId;

    /**
     * 上传人
     */
    @ApiModelProperty(value = "上传人")
    private String uploaderName;

    /**
     * 创建机构
     */
    @ApiModelProperty(value = "创建机构")
    private String createOrgId;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新机构")
    private String updateOrgId;

    /**
     * 删除标识（0:正常  1：已删除）
     */
    @ApiModelProperty(value = "删除标识（0:正常  1：已删除）")
    private String delFlag;
}