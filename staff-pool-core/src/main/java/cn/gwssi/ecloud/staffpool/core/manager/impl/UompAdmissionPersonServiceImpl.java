package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize;
import cn.gwssi.ecloud.staffpool.api.model.CheckPersonProjectVO;
import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.api.service.IDataDesensitization;
import cn.gwssi.ecloud.staffpool.core.dao.*;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.UompAdmissionPersonService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.DictUtil;
import cn.gwssi.ecloud.staffpool.util.ReflectUtil;
import cn.gwssi.ecloud.staffpool.util.SupplierUtil;
import cn.gwssi.ecloud.staffpool.vo.ExitAdmissionPersonUploadVO;
import cn.gwssi.ecloudbpm.module.cmdb.api.service.ICMDBService;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.api.model.dto.SysFileDTO;
import cn.gwssi.ecloudframework.sys.api.service.SysFileService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UompAdmissionPersonServiceImpl extends BaseManager<String, UompAdmissionPerson> implements UompAdmissionPersonService {

    @Resource
    private UompAdmissionPersonMapper uompAdmissionPersonMapper;
    @Resource
    private SysDataDictMapper sysDataDictMapper;
    @Resource
    private UompExitApplicationMapper uompExitApplicationMapper;
    @Resource
    private UompPersonInfoMapper uompPersonInfoMapper;
    @Resource
    private UompExitAdmissionRelationMapper uompExitAdmissionRelationMapper;
    @Resource
    private SysFileService sysFileService;
    @Resource
    private UompApplicationSystemManagementMapper uompApplicationSystemManagementMapper;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private DictUtil dictUtil;
    @Resource
    private SupplierUtil supplierUtil;
    @Resource
    private ICMDBService icmdbService;

    @Resource
    private IDataDesensitization dataDesensitization;

    @Override
    @AutoDesensitize(processMethod ="decrypt")
    public List<EntryPersonSelectDTO> getEntryPersonSelectList() {
        // 获取供应商信息
        IsSupplierDto isSupplierDto = supplierUtil.isSupplier();
        String ifSupplier = isSupplierDto.getIfSupplier();
        String supplierId = isSupplierDto.getSupplierId();

        List<EntryPersonSelectDTO> entryPersonSelectDTOList = uompAdmissionPersonMapper.selectAllByEntryPersonSelect(ifSupplier, supplierId);

        //结果数据二次处理
        if (!CollectionUtils.isEmpty(entryPersonSelectDTOList)) {
            //查出技术方向字典
            List<BaseDTO> educationKeyList = sysDataDictMapper.selectSubListByDictKey("UOMP_TEC_DIRECTION");
            Map<String, String> directionKeyMap = new HashMap<>();
            for (BaseDTO item : educationKeyList) {
                directionKeyMap.put(item.getId(), item.getName());
            }

            for (EntryPersonSelectDTO item : entryPersonSelectDTOList) {
                //技术方向
                String postId = item.getPostId();
                item.setPostName(directionKeyMap.get(postId));

                // 查询指定用户的所有驻场应用
                item.setProjectList(uompAdmissionPersonMapper.selectEngagementProjectByPersonCard(item.getPersonCard()));
            }
        }

        return entryPersonSelectDTOList;
    }

    @Override
    public ResultMsg<String> checkPersonProject(CheckPersonProjectVO checkPersonProjectVO) {
        List<String> engagementProjectIdList = Arrays.asList(checkPersonProjectVO.getEngagementProjectId().split(","));
        List<String> engagementProjectIds = uompAdmissionPersonMapper.selectEngagementProjectIdByPersonCard(checkPersonProjectVO.getPersonCard());

        if (!engagementProjectIds.isEmpty()) {
            //查看已有的和新填的是否有交集，有交集的返回错误值
            for (String it : engagementProjectIds) {
                if (!StringUtils.isBlank(it)) {
                    List<String> idList = Arrays.asList(it.split(","));
                    if (CollectionUtils.containsAny(idList, engagementProjectIdList)) {
                        return ResultMsg.SUCCESS("该人员已经申请过该批次参与项目中的入场申请，且尚未退场，不可再次申请同一项目");
                    }
                }
            }
        }


        //通过person_card 查询该人员是否有退场未完成的流程，有得话暂时不让入场
        Integer count = uompExitApplicationMapper.countByPersonCard(checkPersonProjectVO.getPersonCard());

        if (count > 0) {
            return ResultMsg.SUCCESS("该人员存在尚未完成的退场申请流程，暂时不可申请入场！");
        }

        return ResultMsg.SUCCESS();
    }

    @Override
    public ResultMsg<String> checkExit(String personCard) {
        Integer count = uompAdmissionPersonMapper.countByPersonCard(personCard);

        if (count > 0) {
            return ResultMsg.SUCCESS("该人员存在非审核通过状态的入场申请流程，不可申请退场！");
        }
        return ResultMsg.SUCCESS();
    }

    @Override
    public Integer countByInTime(String time) {
        return uompAdmissionPersonMapper.countByInTime(time);
    }

    @Override
    public void updateStatus(String id, String status) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        UompAdmissionPerson uompAdmissionPerson = new UompAdmissionPerson();
        uompAdmissionPerson.setApplyId(id);
        uompAdmissionPerson.setApplyStatus(status);
        uompAdmissionPerson.setInTime(new Date());
        uompAdmissionPerson.setUpdateBy(user.getUserId());
        uompAdmissionPerson.setUpdateTime(new Date());
        uompAdmissionPerson.setUpdateOrgId(user.getOrgId());
        uompAdmissionPersonMapper.updateByApplyId(uompAdmissionPerson);
    }

    @Override
    public Integer countByPersonCard(String personCard) {
        return uompExitApplicationMapper.countByPersonCard(personCard);
    }

    @Override
    public void insertExitRelation(String exitId, String personCard) {
        //获取当前登录用户ID
        String userId = ContextUtil.getCurrentUserId();

        //入场人员id
        //通过person_card 查寻所有该人员入场的申请id(所有审核通过，且未退场的)
        List<String> applyPersonIdList = uompAdmissionPersonMapper.selectIdByPersonCard(personCard);

        //循环插入退场id和入场申请人员表id的关联关系
        UompExitAdmissionRelation uompExitAdmissionRelation;
        for (String applyPersonId : applyPersonIdList) {
            uompExitAdmissionRelation = new UompExitAdmissionRelation();
            uompExitAdmissionRelation.setId(IdUtil.getSuid());
            uompExitAdmissionRelation.setApplyPersonId(applyPersonId);
            uompExitAdmissionRelation.setExitApplyId(exitId);
            uompExitAdmissionRelation.setCreateBy(userId);
            uompExitAdmissionRelation.setCreateTime(new Date());
            uompExitAdmissionRelation.setDelFlag("0");
            uompExitAdmissionRelationMapper.insertSelective(uompExitAdmissionRelation);
        }

        //修改该批次驻场人员信息表中的退场状态
        if (!applyPersonIdList.isEmpty()) {
            uompAdmissionPersonMapper.updateOutApplyStatusByIdIn(applyPersonIdList);
        }
    }

    @Override
    public List<PersonListDTO> exportInfo(PersonListQueryVO personListQueryVO) {
        return uompAdmissionPersonMapper.selectAllBySelectiveExport(personListQueryVO);
    }

    @Override
    public ResultMsg<String> upload(MultipartFile file) throws Exception {
        //获取当前登录用户信息
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();

        //文件类型判断
        if (null == file.getOriginalFilename()
                || (!file.getOriginalFilename().endsWith(".xls")
                && !file.getOriginalFilename().endsWith(".xlsx"))) {
            return ResultMsg.ERROR("文件格式错误！");
        }

        //学历字典
        List<SysDataDict> educationList = dictUtil.getAllByDictKey("UOMP_EDUCATION");
        Map<String, String> educationMap = new HashMap<>();
        educationList.forEach(sysDataDict -> educationMap.put(sysDataDict.getKey(),sysDataDict.getName()));

        // 异常列表
        List<Map<String, String>> errorList = new ArrayList<>();

        // 驻场入库列表
        List<UompAdmissionPerson> uompAdmissionPersonUploadList = new ArrayList<>();
        // 退场入库列表
        List<ExitAdmissionPersonUploadVO> exitAdmissionPersonUploadVOList = new ArrayList<>();

        List<UompAdmissionPersonDTO> uompAdmissionPersonList = EasyExcel.read(file.getInputStream()).head(UompAdmissionPersonDTO.class).sheet(0).headRowNumber(1).doReadSync();
        Map<String, String> errorMap;
        boolean flag = true;
        for (int i = 0; i < uompAdmissionPersonList.size(); i++) {
            String servicType = "";
            List<String> engagementProjectIdList = new ArrayList<>();
            List<Map<String, String>> engagementProjectJsonList = new ArrayList<>();
            UompAdmissionPersonDTO uompAdmissionPersonDTO = uompAdmissionPersonList.get(i);

            // 服务类型
            if (StringUtils.isNotBlank(uompAdmissionPersonDTO.getServiceType())) {
                servicType = dictUtil.getKeyByNameAndDictKey("UOMP_SERVIC_TYPE", uompAdmissionPersonDTO.getServiceType(), "0");
                if (StringUtils.isBlank(servicType)) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第6列");
                    errorMap.put("message", "请输入正确的服务类型");
                    errorList.add(errorMap);
                    flag = false;
                } else {
                    if ("1".equalsIgnoreCase(servicType)) {
                        // 驻场
                        // 驻场日期
                        if (uompAdmissionPersonDTO.getInTime() == null) {
                            errorMap = new HashMap<>();
                            errorMap.put("row", "第" + (i + 1) + "行");
                            errorMap.put("column", "第7列");
                            errorMap.put("message", "驻场日期为空或者时间日期格式不正确");
                            errorList.add(errorMap);
                            flag = false;
                        }
                        // 驻场服务地点
                        if (StringUtils.isNotBlank(uompAdmissionPersonDTO.getServiceLocation())) {
                            String serviceLocation = dictUtil.getKeyByNameAndDictKey("UOMP_SERVICE_LOCATION", uompAdmissionPersonDTO.getServiceLocation(), "0");
                            if (StringUtils.isBlank(serviceLocation)) {
                                errorMap = new HashMap<>();
                                errorMap.put("row", "第" + (i + 1) + "行");
                                errorMap.put("column", "第5列");
                                errorMap.put("message", "请按系统输入正确的驻场服务地点");
                                errorList.add(errorMap);
                                flag = false;
                            }else {
                                uompAdmissionPersonDTO.setServiceLocation(serviceLocation);
                            }
                        }
                    } else {
                        // 退场
                        // 退场日期
                        if (uompAdmissionPersonDTO.getOutTime() == null) {
                            errorMap = new HashMap<>();
                            errorMap.put("row", "第" + (i + 1) + "行");
                            errorMap.put("column", "第8列");
                            errorMap.put("message", "退场日期为空或者时间日期格式不正确");
                            errorList.add(errorMap);
                            flag = false;
                        }
                    }
                }
            }

            // 校验姓名
            if (StringUtils.isBlank(uompAdmissionPersonDTO.getPersonName())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第2列");
                errorMap.put("message", "姓名不能为空");
                errorList.add(errorMap);
                flag = false;
            }

            // 身份证
            if (StringUtils.isBlank(uompAdmissionPersonDTO.getPersonCard())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第3列");
                errorMap.put("message", "身份证不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompAdmissionPersonDTO.getPersonCard().length() != 18) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第3列");
                errorMap.put("message", "请填写正确长度的身份证号");
                errorList.add(errorMap);
                flag = false;
            }

            // 用姓名+身份证号 查询在人员信息中是否存在，不存在的不可以导入
            UompPersonInfo uompPersonInfo = null;
            if (StringUtils.isNotBlank(uompAdmissionPersonDTO.getPersonName()) && StringUtils.isNotBlank(uompAdmissionPersonDTO.getPersonCard())) {
               //对身份主号进行加密
                if (!uompAdmissionPersonDTO.getPersonCard().contains("*")){
                    uompAdmissionPersonDTO.setPersonCard(personCardEncrypt(uompAdmissionPersonDTO.getPersonCard()));
                }
                uompPersonInfo = uompPersonInfoMapper.selectOneByPersonNameAndPersonCard(uompAdmissionPersonDTO.getPersonName(), uompAdmissionPersonDTO.getPersonCard());
                if (uompPersonInfo == null || StringUtils.isBlank(uompPersonInfo.getId())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第2,3列");
                    errorMap.put("message", "该姓名+身份证号在人员信息中不存在或尚未通过审批，请导入存在且审批通过的人员！");
                    errorList.add(errorMap);
                    flag = false;
                } else {
                    // 应用系统
                    // 参与项目（必须是项目管理中的项目）
                    if (StringUtils.isBlank(uompAdmissionPersonDTO.getEngagementProject())) {
                        errorMap = new HashMap<>();
                        errorMap.put("row", "第" + (i + 1) + "行");
                        errorMap.put("column", "第4列");
                        errorMap.put("message", "应用系统不能为空");
                        errorList.add(errorMap);
                        flag = false;
                    } else {
                        //用逗号分隔参与项目
                        List<String> engagementProjectList = Arrays.asList(uompAdmissionPersonDTO.getEngagementProject().split(","));

                        //批量正确标识
                        Boolean batchFlag = true;

                        //查询所有系统名称是否存在
                        //11-21 从cmdb获取应用数据并组装成以名称为key的map
                        Map<String, Map<String, String>> systemMap = makeSystemByCmdb();
                        for (String engagementProjectName : engagementProjectList) {
//                            UompApplicationSystemManagement uompApplicationSystemManagement = uompApplicationSystemManagementMapper.selectIdByApplicationSystemName(engagementProjectName);
                            if (systemMap.get(engagementProjectName) == null) {
                                errorMap = new HashMap<>();
                                errorMap.put("row", "第" + (i + 1) + "行");
                                errorMap.put("column", "第4列");
                                errorMap.put("message", "【" + engagementProjectName + "】-系统状态异常，请检查");
                                errorList.add(errorMap);
                                flag = false;
                                batchFlag = false;
                                break;
                            } else {
                                //11-21 系统存在，封装参数
                                Map<String, String> system = systemMap.get(engagementProjectName);
                                engagementProjectIdList.add(system.get("id"));
                                engagementProjectJsonList.add(system);

                                //11-21 todo 暂时取消关联校验
                                //存在得话，判断该项目是否关联该条数据的供应商，如果不是则提示非关联关系，supplierId
//                                if (!uompApplicationSystemManagement.getSupplierId().equals(uompPersonInfo.getWorkingCompanyId())) {
//                                    errorMap = new HashMap<>();
//                                    errorMap.put("row", "第" + (i + 1) + "行");
//                                    errorMap.put("column", "第4列");
//                                    errorMap.put("message", "【" + engagementProjectName + "】不是服务商【" + uompApplicationSystemManagement.getSupplierName() + "】关联的应用系统，请检查！");
//                                    errorList.add(errorMap);
//                                    flag = false;
//                                    batchFlag = false;
//                                    break;
//
//                                } else {
//                                    //存在的情况下直接添加id和json
//                                    engagementProjectIdList.add(uompApplicationSystemManagement.getId());
//
//                                    Map<String, String> jsonInfo = new HashMap<>();
//                                    jsonInfo.put("id", uompApplicationSystemManagement.getId());
//                                    jsonInfo.put("name", engagementProjectName);
//                                    engagementProjectJsonList.add(jsonInfo);
//                                }
                            }
                        }
                        //如果是true的情况继续往下校验
                        if (batchFlag) {
                            if ("1".equalsIgnoreCase(servicType)) {
                                // 驻场
                                //查询该人员存不存在已经入场了的该项目
                                if (StringUtils.isNotBlank(uompAdmissionPersonDTO.getPersonCard())) {
                                    List<String> infoList = uompAdmissionPersonMapper.selectEngagementProjectIdByPersonCardAndApplyStatus(uompAdmissionPersonDTO.getPersonCard());

                                    if (!infoList.isEmpty()) {
                                        //查看已有的和新填的是否有交集，有交集的返回错误值
                                        for (String it : infoList) {
                                            List<String> idList = Arrays.asList(it.split(","));
                                            if (CollectionUtils.containsAny(idList, engagementProjectIdList)) {
                                                errorMap = new HashMap<>();
                                                errorMap.put("row", "第" + (i + 1) + "行");
                                                errorMap.put("column", "第9列");
                                                errorMap.put("message", "该人员已经申请过该批次应用系统的入场申请，且尚未退场，不可再次申请相同的应用系统");
                                                errorList.add(errorMap);
                                                flag = false;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }else {
                flag = false;
            }

            //重复校验(姓名，身份证号，运维组一致，且不是退场情况下的不让导入)
            if (flag) {
                if ("1".equalsIgnoreCase(servicType)) {
                    // 驻场
                    //判断该批次导入数据中，是否存在同样人员进入同一应用系统的情况
                    for (UompAdmissionPerson item : uompAdmissionPersonUploadList) {
                        if (uompAdmissionPersonDTO.getPersonCard().equals(item.getPersonCard()) && uompAdmissionPersonDTO.getEngagementProject().equals(item.getEngagementProject())) {
                            errorMap = new HashMap<>();
                            errorMap.put("row", "第" + (i + 1) + "行");
                            errorMap.put("column", "");
                            errorMap.put("message", "该批次导入的数据中已经存在该人员参与相同应用系统的情况，不可重复导入");
                            errorList.add(errorMap);
                            flag = false;
                            break;
                        }
                    }
                }
            }

            if (flag) {
                if ("1".equalsIgnoreCase(servicType)) {
                    // 驻场
                    //处理初始化字段
                    UompAdmissionPerson uompAdmissionPerson = new UompAdmissionPerson();
                    BeanUtils.copyProperties(uompAdmissionPersonDTO, uompAdmissionPerson);
                    //导入的，是否导入默认状态为 0-是导入，审核状态是 2- 审核通过的 退场状态为 0-没有退场申请
                    uompAdmissionPerson.setId(IdUtil.getSuid());
                    uompAdmissionPerson.setIsExport("0");
                    uompAdmissionPerson.setApplyStatus("2");
                    uompAdmissionPerson.setOutApplyStatus("0");
                    uompAdmissionPerson.setCreateBy(userId);
                    uompAdmissionPerson.setCreateTime(new Date());
                    uompAdmissionPerson.setCreateOrgId(orgId);
                    uompAdmissionPerson.setDelFlag("0");
                    //11-21 初始化一些基础字段
                    uompAdmissionPerson.setEntryDate(uompPersonInfo.getEntryDate());
                    uompAdmissionPerson.setEducation(educationMap.get(uompPersonInfo.getEducation()));
                    uompAdmissionPerson.setWorkingCompanyId(uompPersonInfo.getWorkingCompanyId());
                    uompAdmissionPerson.setWorkingCompany(uompPersonInfo.getWorkingCompany());
                    uompAdmissionPerson.setMaintenanceGroupId(uompPersonInfo.getOrgGroupId());
                    uompAdmissionPerson.setMaintenanceGroupName(uompPersonInfo.getOrgGroupName());
                    Map<String, String> maintenanceGroupMap = new HashMap<>();
                    maintenanceGroupMap.put("id",uompPersonInfo.getOrgGroupId());
                    maintenanceGroupMap.put("name",uompPersonInfo.getOrgGroupName());
                    List<Map<String, String>> maintenanceGroupMapList = Collections.singletonList(maintenanceGroupMap);
                    String maintenanceGroupJson = JSON.toJSONString(maintenanceGroupMapList);
                    uompAdmissionPerson.setMaintenanceGroupJson(maintenanceGroupJson);
                    uompAdmissionPerson.setPersonId(uompPersonInfo.getId());
                    List<String> departNameList = engagementProjectJsonList.stream().map(info -> info.get("departName")).collect(Collectors.toList());
                    uompAdmissionPerson.setDepartName(StringUtils.join(departNameList,","));
                    uompAdmissionPerson.setTel(uompPersonInfo.getTel());
                    uompAdmissionPerson.setTechnicalDirection(uompPersonInfo.getTechnicalDirection());

                    uompAdmissionPerson.setEngagementProjectId(StringUtils.join(engagementProjectIdList, ","));
                    uompAdmissionPerson.setEngagementProjectJson(JSONObject.toJSONString(engagementProjectJsonList));

                    uompAdmissionPersonUploadList.add(uompAdmissionPerson);
                } else {
                    // 退场
                    ExitAdmissionPersonUploadVO exitAdmissionPersonUploadVo = new ExitAdmissionPersonUploadVO();
                    exitAdmissionPersonUploadVo.setLine(String.valueOf(i + 1));
                    exitAdmissionPersonUploadVo.setPersonCard(uompAdmissionPersonDTO.getPersonCard());
                    exitAdmissionPersonUploadVo.setOutTime(String.valueOf(uompAdmissionPersonDTO.getOutTime().getTime()));

                    exitAdmissionPersonUploadVOList.add(exitAdmissionPersonUploadVo);
                }

            }
        }

        // 操作驻场添加
        if (!uompAdmissionPersonUploadList.isEmpty()) {
            for (UompAdmissionPerson u : uompAdmissionPersonUploadList) {
                uompAdmissionPersonMapper.insertSelective(u);
                //更新人员驻场状态
                uompPersonInfoService.updateEntryStatusByPersonIdOrPersonCard("0",u.getPersonId(),u.getPersonCard());
            }
        }
        // 操作退场添加
        if (!exitAdmissionPersonUploadVOList.isEmpty()) {
            for (ExitAdmissionPersonUploadVO u : exitAdmissionPersonUploadVOList) {
                if (!this.exitAdmissionPerson("", u.getPersonCard(), u.getOutTime())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (u.getLine()) + "行");
                    errorMap.put("column", "");
                    errorMap.put("message", "退场操作失败!");
                    errorList.add(errorMap);
                }
            }
        }

        if (!errorList.isEmpty()) {
            //导出数据
            // 第一个对象，新建一个工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 设置第一个sheet的名称
            Sheet sheet = workbook.createSheet("sheet1");

            // 开始添加excel第一行表头（excel中下标是0）
            Row row = sheet.createRow(0);
            sheet.setDefaultColumnWidth(16);//宽

            row.setHeightInPoints(20);//行高

            String[] fieldList = {"序号", "行数", "列数", "错误原因"};
            int i = 0;
            // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
            for (String it : fieldList) {
                // 创建一个单元格
                Cell cell = row.createCell(i);
                // 设置单元格的样式
                CellStyle cellStyle = workbook.createCellStyle();
                //设置字体
                Font font = workbook.createFont();
                //设置字号
                font.setFontHeightInPoints((short) 14);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                // 将数据放入excel的单元格中
                cell.setCellValue(it);
                i++;
            }


            // 开始创建excel单元格数据，从第二行开始（excel下标是1）
            int rowNum = 1;
            // 添加excel行数据的集合（你自己的数据集合遍历）
            for (Map<String, String> it : errorList) {
                // 创建一个单元格
                Row row1 = sheet.createRow(rowNum);
                // 设置行的高度
                row1.setHeightInPoints(16);
                //填写单元格
                row1.createCell(0).setCellValue(rowNum + "");//序号
                row1.createCell(1).setCellValue(it.get("row"));//行
                row1.createCell(2).setCellValue(it.get("column"));//列
                row1.createCell(3).setCellValue(it.get("message"));//原因

                rowNum++;
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                //把文件送到用户端
                workbook.write(bos);
                byte[] brray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(brray);
                SysFileDTO upload = sysFileService.upload(is, "错误信息-" + IdUtil.getSuid() + ".xlsx");
                return ResultMsg.ERROR(upload.getId());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放资源
                bos.flush();
                bos.close();
                workbook.close();
            }
        } else {
            return ResultMsg.SUCCESS("导入成功!");
        }
        return ResultMsg.ERROR("导入失败!");
    }

    /**
     * 身份证号加密
     * @param personCard
     * @return
     */
    private String personCardEncrypt(String personCard){
        if (StringUtils.isNotEmpty(personCard)&& !personCard.contains("*") && personCard.length() == 18){
            return dataDesensitization.encrypt(personCard);
        }
        return personCard;
    }

    @Override
    public List<PercentageDTO> getPersonInTime() {
//        List<PercentageDTO> percentageDTOS = uompAdmissionPersonMapper.getPersonInTime("", "");
        //11-22 统计当前驻场中的人员服务时常,(服务时长取人员入职时间)
        List<PercentageDTO> percentageDTOS = uompAdmissionPersonMapper.getPersonInTimeNew();
        return getPersonInTimeInfo(percentageDTOS);
    }

    @Override
    public List<PercentageDTO> getPersonInTimeBySupplierId(String supplierId) {
        List<PercentageDTO> percentageDTOS = uompAdmissionPersonMapper.getPersonInTime(supplierId, "");
        return getPersonInTimeInfo(percentageDTOS);
    }

    @Override
    public List<PercentageDTO> getPersonInTimeBySupplierName(String supplierName) {
        List<PercentageDTO> percentageDTOS = uompAdmissionPersonMapper.getPersonInTime("", supplierName);
        return getPersonInTimeInfo(percentageDTOS);
    }

    @Override
    public Boolean exitAdmissionPerson(String personId, String personCard, String outTime) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        // 根据用户id或者身份证号查询当前为驻场状态的驻场记录
        UompAdmissionPerson uompAdmissionPerson = uompAdmissionPersonMapper.selectOneByPersonIdAndPersonCard(personId, personCard);
        // 手动插入退场记录
        if (uompAdmissionPerson == null || StringUtils.isBlank(uompAdmissionPerson.getId())) {
            return uompPersonInfoService.updateEntryStatusByPersonIdOrPersonCard("1", personId, personCard);
        } else {
            UompExitApplication uompExitApplication = new UompExitApplication();
            uompExitApplication.setApplyPersonId(uompAdmissionPerson.getId());
            uompExitApplication.setPersonName(uompAdmissionPerson.getPersonName());
            uompExitApplication.setTel(uompAdmissionPerson.getTel());
            uompExitApplication.setWorkingCompany(uompAdmissionPerson.getWorkingCompany());
            uompExitApplication.setWorkingCompanyId(uompAdmissionPerson.getWorkingCompanyId());
            uompExitApplication.setWorkingCompanyJson(uompAdmissionPerson.getWorkingCompanyJson());
            uompExitApplication.setMaintenanceGroupId(uompAdmissionPerson.getMaintenanceGroupId());
            uompExitApplication.setMaintenanceGroupName(uompAdmissionPerson.getMaintenanceGroupName());
            uompExitApplication.setPersonCard(uompAdmissionPerson.getPersonCard());
            uompExitApplication.setOutApplyTitle(uompAdmissionPerson.getPersonName() + "-退场");
            uompExitApplication.setOutTime(new Date(Long.parseLong(outTime)));
            uompExitApplication.setApplyStatus("2");
            uompExitApplication.setCreateBy(user.getUserId());
            uompExitApplication.setCreateTime(new Date());
            uompExitApplication.setCreateOrgId(user.getOrgId());
            uompExitApplication.setDelFlag("0");
            uompExitApplicationMapper.insertSelective(uompExitApplication);

            // 新增关联关系
            UompExitAdmissionRelation uompExitAdmissionRelation = new UompExitAdmissionRelation();
            uompExitAdmissionRelation.setExitApplyId(uompExitApplication.getId());
            uompExitAdmissionRelation.setApplyPersonId(uompAdmissionPerson.getId());
            uompExitAdmissionRelation.setCreateBy(user.getUserId());
            uompExitAdmissionRelation.setCreateTime(new Date());
            uompExitAdmissionRelation.setDelFlag("0");
            uompExitAdmissionRelationMapper.insertSelective(uompExitAdmissionRelation);

            // 修改驻场记录驻场状态
            uompAdmissionPerson.setOutApplyStatus("1");
            uompAdmissionPerson.setOutTime(new Date(Long.parseLong(outTime)));
            uompAdmissionPerson.setUpdateBy(user.getUserId());
            uompAdmissionPerson.setUpdateTime(new Date());
            uompAdmissionPerson.setUpdateOrgId(user.getOrgId());
            if (uompAdmissionPersonMapper.updateById(uompAdmissionPerson) > 0) {
                // 修改对应人员驻场状态
                return updateEntryStatusByAdmissionPersonId("1", uompAdmissionPerson.getId());
            }

            return false;
        }
    }

    @Override
    public Boolean updateEntryStatusByAdmissionPersonId(String entryStatus, String admissionPersonId) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        List<UompAdmissionPerson> uompAdmissionPersonList = uompAdmissionPersonMapper.selectAllByIdIn(Arrays.asList(admissionPersonId.split(",")));

        for (UompAdmissionPerson uomAdmissionPerson : uompAdmissionPersonList) {

            if (StringUtils.isNotBlank(uomAdmissionPerson.getPersonId())) {
                UompPersonInfo uompPersonInfo = new UompPersonInfo();
                uompPersonInfo.setEntryStatus(entryStatus);
                uompPersonInfo.setUpdateBy(user.getUserId());
                uompPersonInfo.setUpdateTime(new Date());
                uompPersonInfo.setUpdateUser(user.getFullname());
                uompPersonInfo.setUpdateOrgId(user.getOrgId());
                uompPersonInfo.setId(uomAdmissionPerson.getPersonId());
                uompPersonInfoMapper.updateByPrimaryKeySelective(uompPersonInfo);
            } else {
                uompPersonInfoMapper.updateEntryStatusByPersonCard(entryStatus, uomAdmissionPerson.getPersonCard());
            }
        }

        return true;
    }

    @Override
    public Boolean admissionPersonActiveAccount(String admissionPersonId) {
        List<UompAdmissionPerson> uompAdmissionPersonList = uompAdmissionPersonMapper.selectAllByIdIn(Arrays.asList(admissionPersonId.split(",")));

        String ids = "";
        for (UompAdmissionPerson uomAdmissionPerson : uompAdmissionPersonList) {
            // 根据 personId 或 personCard 查询用户信息
            UompPersonInfo uompPersonInfo = uompPersonInfoService.selectOneByPersonIdOrPersonCard(uomAdmissionPerson.getPersonId(), uomAdmissionPerson.getPersonCard());
            if (uompPersonInfo != null) {
                ids += "," + uompPersonInfo.getOrgUserId();
            }
        }
        uompPersonInfoService.activeAccount(ids);

        return true;
    }

    @Override
    public Integer countByInTime(String inTimeStart, String inTimeEnd) {
        return uompAdmissionPersonMapper.countByInTimeRange(inTimeStart, inTimeEnd);
    }

    @Override
    public void personCardEncrypt(QueryFilter queryFilter) {
        List<UompAdmissionPerson> uompAdmissionPersonList = uompAdmissionPersonMapper.query(queryFilter);
        for (UompAdmissionPerson uompAdmissionPerson : uompAdmissionPersonList){
            if (StringUtils.isNotEmpty(uompAdmissionPerson.getPersonCard()) &&
                    uompAdmissionPerson.getPersonCard().length() ==18){
                uompAdmissionPersonMapper.encryptPersonCard(dataDesensitization.
                        encrypt(uompAdmissionPerson.getPersonCard()),uompAdmissionPerson.getId());
            }
        }
    }

    private List<PercentageDTO> getPersonInTimeInfo(List<PercentageDTO> percentageDTOS) {
        List<PercentageDTO> dtos = new ArrayList<>();
        Map<Integer, Integer> map = new HashMap<>();
        map.put(1, 0);
        map.put(2, 0);
        map.put(3, 0);
        map.put(4, 0);
        map.put(5, 0);

        Integer total = 0;
        for (PercentageDTO dto : percentageDTOS) {
            if (StringUtils.isEmpty(dto.getName())) {
                continue;
            }
            total += dto.getNum();
            int year = Integer.parseInt(dto.getName());
            if (year < 1) {
                map.put(1, map.get(1) + dto.getNum());
            } else if (year < 3) {
                map.put(2, map.get(2) + dto.getNum());
            } else if (year < 5) {
                map.put(3, map.get(3) + dto.getNum());
            } else if (year < 10) {
                map.put(4, map.get(4) + dto.getNum());
            } else {
                map.put(5, map.get(5) + dto.getNum());
            }
        }
        for (Integer key : map.keySet()) {
            PercentageDTO percentageDTO = new PercentageDTO();
            percentageDTO.setNum(map.get(key));
            if (key == 1) {
                percentageDTO.setName("不足1年");
            } else if (key == 2) {
                percentageDTO.setName("1-3年");
            } else if (key == 3) {
                percentageDTO.setName("3-5年");
            } else if (key == 4) {
                percentageDTO.setName("5-10年");
            } else {
                percentageDTO.setName("10年以上");
            }
            if (map.get(key) > 0 && total > 0) {
                percentageDTO.setPercentage(new BigDecimal(map.get(key)).divide(new BigDecimal(total), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%");
            }
            dtos.add(percentageDTO);
        }
        return dtos;
    }

    /**
     * 从cmdb获取应用系统数据，并组装
     * @return
     */
    private Map<String, Map<String, String>> makeSystemByCmdb(){
        Map application = icmdbService.instListByModelName("application", null, null);
        List<Object> headerList = (List<Object>) application.get("header");
        List<Map<String, String>> dataList = (List<Map<String, String>>) application.get("list");

        //数据处理，以attrName为key的 map
        Map<String, JSONObject> headerMap = new HashMap<>();
        headerList.forEach(info -> {
            JSONObject infoJsonObject = JSONObject.parseObject(JSON.toJSONString(info));
            String attrName = String.valueOf(infoJsonObject.get("attrName"));
            headerMap.put(attrName, infoJsonObject);
        });

        //获取请求头中，名称（ci_name），主责部门(responsible_org)的attrId
        JSONObject ciNameJsonObject = headerMap.get("ci_name");
        JSONObject responsibleOrgJsonObject = headerMap.get("responsible_org");
        String ciNameAttrId = String.valueOf(ciNameJsonObject.get("attrId"));
        String responsibleOrgAttrId = String.valueOf(responsibleOrgJsonObject.get("attrId"));

        //构造以ciName为key的应用数据map,数据结构适配驻场申请中  {"id":"","name":"","departName":""}
        Map<String, Map<String, String>> resultMap = new HashMap<>();
        dataList.forEach(data -> {
            Map<String, String> dataMap = new HashMap<String, String>();
            dataMap.put("id", data.get("id"));
            dataMap.put("name", data.get(ciNameAttrId));
            dataMap.put("departName", data.get(responsibleOrgAttrId));

            resultMap.put(data.get(ciNameAttrId), dataMap);
        });

        return resultMap;
    }

    @Override
    public String decryptEntryInfo(String id,String attr){
        if (StringUtils.isEmpty(attr)){
            attr = "personCard";
        }
        if (StringUtils.isNotEmpty(id)){
            EntryApplyDTO entryApplyDTO = uompAdmissionPersonMapper.selectAllById(id);
            if (entryApplyDTO != null && StringUtils.isNotEmpty(entryApplyDTO.getPersonCard())){
                String original = ReflectUtil.getFieldValue(entryApplyDTO,attr).toString();
                return dataDesensitization.decrypt(original);
            }
        }
        return null;
    }
}
