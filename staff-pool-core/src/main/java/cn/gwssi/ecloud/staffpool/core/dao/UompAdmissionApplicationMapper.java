package cn.gwssi.ecloud.staffpool.core.dao;
import org.apache.ibatis.annotations.Param;

import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompAdmissionApplicationMapper extends BaseDao<String, UompAdmissionApplication> {

    int insertSelective(UompAdmissionApplication record);

    Integer updateById(UompAdmissionApplication updated);

    UompAdmissionApplication selectAllById(@Param("id")String id);
}