package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.CmdbCommResource;
import cn.gwssi.ecloud.staffpool.dto.UompPropertyDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface CmdbCommResourceService extends Manager<String, CmdbCommResource> {

    int insertSelective(CmdbCommResource record);

    List<CmdbCommResource> getConfList(QueryFilter queryFilter);

    List<CmdbCommResource> selectInfoByContractId(QueryFilter queryFilter);

    List<UompPropertyDTO> selectPropertyList(QueryFilter queryFilter);
}
