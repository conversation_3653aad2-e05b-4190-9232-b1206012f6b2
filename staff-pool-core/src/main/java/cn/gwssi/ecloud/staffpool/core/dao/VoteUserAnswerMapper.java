package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface VoteUserAnswerMapper extends BaseDao<String, VoteUserAnswer> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(VoteUserAnswer record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(VoteUserAnswer record);
}