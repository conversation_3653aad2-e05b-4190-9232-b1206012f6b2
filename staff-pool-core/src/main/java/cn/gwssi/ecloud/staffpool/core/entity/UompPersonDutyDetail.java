package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人员值班详情表
 */
@ApiModel(description="人员值班详情表")
@Data
public class UompPersonDutyDetail extends BaseModel {

    @ApiModelProperty(value="id")
    private String id;

    @ApiModelProperty(value="值班id")
    private String dutyId;

    @ApiModelProperty(value="人员id")
    private String personId;

    @ApiModelProperty(value="值班日期")
    private String date;

    @ApiModelProperty(value="删除标记")
    private String delFlag;
}

