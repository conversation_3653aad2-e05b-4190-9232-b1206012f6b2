package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.TempApplyVO;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompTempAdmissionService;
import cn.gwssi.ecloud.staffpool.core.manager.impl.UompTempAdmissionServiceImpl;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloud.staffpool.util.SupplierUtil;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(description = "现场管理临时驻场管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/tempEntryApply")
public class UompTempEntryApplyController extends BaseController<UompTempAdmission> {

    @Resource
    private UompTempAdmissionService uompTempAdmissionService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private SysDataDictMapper sysDataDictMapper;
    @Resource
    private SupplierUtil supplierUtil;
    @Resource
    private UompSupplierManagementService supplierManagementService;

    @Override
    protected String getModelDesc() {
        return "现场管理临时驻场管理";
    }

    @ApiOperation(value = "临时入场列表查询接口")
    @PostMapping(value = "/getTempApplyList")
    public ResponsePageData<TempApplyDTO> getTempApplyList(@RequestBody TempApplyVO tempApplyVO) {
        return uompTempAdmissionService.getTempApplyList(tempApplyVO);
    }

    @ApiOperation(value = "删除临时申请人员接口")
    @GetMapping(value = "/deleteTempApply")
    public ResultMsg<Integer> deleteTempApply(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(uompTempAdmissionService.deleteTempApply(id));
    }

    @ApiOperation(value = "临时列表导出接口")
    @PostMapping(value = "/export")
    public HttpServletResponse export(HttpServletResponse response, @RequestBody TempApplyVO tempApplyVO) throws IOException {
        // 到访日期时间段
        if (!StringUtils.isEmpty(tempApplyVO.getRealVisitTime())) {
            List<String> realVisitTime = Arrays.asList(tempApplyVO.getRealVisitTime().split(","));
            String realVisitTimeBegin = realVisitTime.get(0);
            String realVisitTimeEnd = realVisitTime.get(1);
            if (!StringUtils.isEmpty(realVisitTimeBegin)) {
                tempApplyVO.setRealVisitTimeBegin(realVisitTimeBegin);
            }
            if (!StringUtils.isEmpty(realVisitTimeEnd)) {
                tempApplyVO.setRealVisitTimeEnd(realVisitTimeEnd);
            }
        }

        // 就职公司
        if (!StringUtils.isEmpty(tempApplyVO.getWorkingCompany())) {
            tempApplyVO.setWorkingCompanyList(Arrays.asList(tempApplyVO.getWorkingCompany().split(",")));
        }

        // 参与项目
        if (!StringUtils.isEmpty(tempApplyVO.getInvolvedProject())) {
            tempApplyVO.setInvolvedProjectList(Arrays.asList(tempApplyVO.getInvolvedProject().split(",")));
        }

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles != null && !roles.isEmpty()) {
            List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
                @Override
                public String apply(IUserRole iUserRole) {
                    return iUserRole.getAlias();
                }
            }).collect(Collectors.toList());
            if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) {
                // 查看所有
            } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
                QueryFilter queryFilter1 = new DefaultQueryFilter(true);
                queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
                List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
                if (workCompany != null && workCompany.size() > 0) {
                    tempApplyVO.setIfSupplier("1");
                    tempApplyVO.setSupplierId(workCompany.get(0).getWorkingCompanyId());
                }
            } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) {
                tempApplyVO.setCreatedBy(user.getUserId());
            } else {
                // 查询空数据
                tempApplyVO.setCreatedBy("-1");
            }
        }

        List<TempApplyDTO> tempApplyDTOList = uompTempAdmissionService.exportInfo(tempApplyVO);
        tempApplyDTOList = UompTempAdmissionServiceImpl.getTempApplyDTOS(tempApplyDTOList, desRuleUtil, conversionUtil);
        // 查询审核状态的字典值
        List<BaseDTO> keys = sysDataDictMapper.selectSubListByUompEducation("UOMP_PEOPLE_INFO_MANAGEMENT_AUDIT_STATUS");
        Map<String, String> dictMap = keys.stream().collect(Collectors.toMap(BaseDTO::getId, BaseDTO::getName));
        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("临时驻场申请列表");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号", "姓名", "身份证号", "联系方式", "就职公司", "接待人", "到访时间", "离开时间", "工作内容", "审核状态"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }

        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        for (TempApplyDTO it : tempApplyDTOList) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getPersonName() == null ? "" : it.getPersonName());//姓名
            row1.createCell(2).setCellValue(it.getPersonCard() == null ? "" : it.getPersonCard());//身份证号
            row1.createCell(3).setCellValue(it.getTel() == null ? "" : it.getTel());//联系方式
            row1.createCell(4).setCellValue(it.getWorkingCompany() == null ? "" : it.getWorkingCompany());//就职公司
            row1.createCell(5).setCellValue(it.getAcceptName() == null ? "" : it.getAcceptName());//接待人
            row1.createCell(6).setCellValue(it.getRealVisitTime() == null ? "" : it.getRealVisitTime());//到访时间
            row1.createCell(7).setCellValue(it.getExitTime() == null ? "" : it.getExitTime());//离开时间
            row1.createCell(8).setCellValue(it.getJobContent() == null ? "" : it.getJobContent());//工作内容
            row1.createCell(9).setCellValue(dictMap.get(it.getApplyStatus()));//审核状态
            rowNum++;
        }

        //把文件送到用户端
        String downName = "临时入场人员表.xlsx";
        response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
        ServletOutputStream os = response.getOutputStream();
        try {
            workbook.write(os);
        } catch (Exception e) {
            log.error("异常");
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }

    @ApiOperation(value = "临时入场模板导入")
    @PostMapping(value = "/upload")
    public ResultMsg<String> upload(@RequestParam(value = "file") MultipartFile file) throws Exception {
        return uompTempAdmissionService.upload(file);
    }

    @ApiOperation(value = "根据姓名查询人员信息")
    @RequestMapping(value = "/getInfoByName")
    public ResultMsg<InfoByNameDTO> getInfoByName(@RequestParam(value = "personName") String personName) {
        return ResultMsg.SUCCESS(uompTempAdmissionService.getInfoByName(personName));
    }

    @ApiOperation(value = "临时入场管理-加入黑名单接口")
    @GetMapping(value = "/addBlackList")
    public ResultMsg<Boolean> addBlackList(@RequestParam(value = "id") String id, @RequestParam(value = "reason") String reason) {
        return ResultMsg.SUCCESS(uompTempAdmissionService.addBlackList(id, reason));
    }

    @ApiOperation(value = "临时入场管理-根据id查详情")
    @GetMapping(value = "/getInfoById")
    public ResultMsg<UompTempAdmissionInfoDTO> getInfoById(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(uompTempAdmissionService.getInfoById(id));
    }

    @ApiOperation(value = "临时入场-根据身份证号校验是否加入黑名单")
    @GetMapping(value = "/checkBlackByCard")
    public ResultMsg<Boolean> checkBlackByCard(@RequestParam(value = "personCard") String personCard) {
        return ResultMsg.SUCCESS(uompTempAdmissionService.checkBlackByCard(personCard));
    }

    @ApiOperation(value = "人员信息总览")
    @PostMapping(value = "/personInfoOverview")
    public ResponsePageData<TempApplyDTO> personInfoOverview(@RequestBody TempApplyVO tempApplyVO) {
        if (StringUtils.isEmpty(tempApplyVO.getId())) {
            ResponsePageData pageResult = new ResponsePageData();
            pageResult.setCode(500);
            pageResult.setMsg("申请id 为必填项");
            return pageResult;
        }
        return uompTempAdmissionService.personInfoOverview(tempApplyVO);
    }

    @ApiOperation(value = "获取就职公司服务商下拉数据接口")
    @RequestMapping(value = "/getSupplierDataList")
    public ResultMsg<List<LabelDTO>> getSupplierDataList(HttpServletRequest request, HttpServletResponse response) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        IsSupplierDto supplierDto = supplierUtil.isSupplier();

        QueryFilter queryFilter = new DefaultQueryFilter(true);
        if (StringUtils.isBlank(supplierDto.getSupplierId())) {
            // 通过用户id查询运维用户信息
            UompPersonInfo uompPersonInfo = uompPersonInfoService.selectAllByUserId(user.getUserId());

            if (uompPersonInfo != null) {
                queryFilter.addFilter("ID", uompPersonInfo.getWorkingCompanyId(), QueryOP.EQUAL);
            }
        }

        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("SUPPLIER_STATUS", "1", QueryOP.IN);
        supplierManagementService.query(queryFilter);
        List<UompSupplierManagement> managements = supplierManagementService.query(queryFilter);
        List<LabelDTO> labelDTOS = new ArrayList<>();
        for (UompSupplierManagement management : managements) {
            LabelDTO labelDTO = new LabelDTO();
            labelDTO.setValue(management.getId());
            labelDTO.setLabel(management.getSupplierName());

            labelDTOS.add(labelDTO);
        }
        return new ResultMsg<>(labelDTOS);
    }
}
