package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="根据id查询入场申请详情响应类")
@Data
public class UompTempAdmissionInfoDTO implements Serializable {

    @ApiModelProperty(value="主键")
    private String id;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="预计到访时间")
    private String planVisitTime;
    @ApiModelProperty(value="实际到访时间")
    private String realVisitTime;
    @ApiModelProperty(value="录入人")
    private String destClerkName;
    @ApiModelProperty(value="离场时间")
    private String exitTime;
    @ApiModelProperty(value="工作内容")
    private String jobContent;
    @ApiModelProperty(value="接待人")
    private String acceptName;
    @ApiModelProperty(value="创建人")
    private String createBy;
    @ApiModelProperty(value="流程实例id")
    private String instId;
    @ApiModelProperty(value="职务")
    private String applicatDuty;
    @ApiModelProperty(value="性别")
    private String sex;
}
