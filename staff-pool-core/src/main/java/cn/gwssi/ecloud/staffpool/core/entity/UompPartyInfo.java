package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;

@ApiModel(value = "乙方联合体信息")
public class UompPartyInfo extends BaseModel {

    private String id;

    //合同表ID
    private String contractManagementId;

    //乙方联合体信息
    private String partyInfo;

    //乙方联合体负责人
    private String partyName;

    //乙方联合电话
    private String partyTel;

    //项目经理
    private String partyManagerName;

    //项目经理电话
    private String partyManagerTel;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getContractManagementId() {
        return contractManagementId;
    }

    public void setContractManagementId(String contractManagementId) {
        this.contractManagementId = contractManagementId;
    }

    public String getPartyInfo() {
        return partyInfo;
    }

    public void setPartyInfo(String partyInfo) {
        this.partyInfo = partyInfo;
    }

    public String getPartyName() {
        return partyName;
    }

    public void setPartyName(String partyName) {
        this.partyName = partyName;
    }

    public String getPartyTel() {
        return partyTel;
    }

    public void setPartyTel(String partyTel) {
        this.partyTel = partyTel;
    }

    public String getPartyManagerName() {
        return partyManagerName;
    }

    public void setPartyManagerName(String partyManagerName) {
        this.partyManagerName = partyManagerName;
    }

    public String getPartyManagerTel() {
        return partyManagerTel;
    }

    public void setPartyManagerTel(String partyManagerTel) {
        this.partyManagerTel = partyManagerTel;
    }
}
