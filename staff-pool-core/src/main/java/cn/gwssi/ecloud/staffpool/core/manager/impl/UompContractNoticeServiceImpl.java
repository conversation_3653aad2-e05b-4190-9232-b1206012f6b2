package cn.gwssi.ecloud.staffpool.core.manager.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice;
import cn.gwssi.ecloud.staffpool.core.dao.UompContractNoticeMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractNoticeService;

import java.util.List;

@Service
public class UompContractNoticeServiceImpl implements UompContractNoticeService{

    @Autowired
    private UompContractNoticeMapper uompContractNoticeMapper;

    @Override
    public int deleteByPrimaryKey(String id) {
        return uompContractNoticeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(UompContractNotice record) {
        return uompContractNoticeMapper.insert(record);
    }

    @Override
    public int insertSelective(UompContractNotice record) {
        return uompContractNoticeMapper.insertSelective(record);
    }

    @Override
    public UompContractNotice selectByPrimaryKey(String id) {
        return uompContractNoticeMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(UompContractNotice record) {
        return uompContractNoticeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompContractNotice record) {
        return uompContractNoticeMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<UompContractNotice> getListByContractIds(List<String> contractIds) {
        return uompContractNoticeMapper.getListByContractIds(contractIds);
    }


}
