package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.service.risk.TeamRiskService;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/module/staffpool/risk/")
@Api(
        description = "团队风险接口"
)
public class TeamRiskController extends ControllerTools {

    @Resource
    private TeamRiskService teamRiskService;


    @RequestMapping("listJson")
    public PageResult listJson(HttpServletRequest request) {
        String level = request.getParameter("level");
        QueryFilter queryFilter = getQueryFilter(request);
        if (StringUtils.isNotEmpty(level)){
            queryFilter.addParamsFilter("queryLevel",level);
        }
        return new PageResult(teamRiskService.getList(queryFilter));
    }

    @GetMapping("trend")
    public ResultMsg trend(@RequestParam("level")String level) {
        return getSuccessResult(teamRiskService.trend(level));
    }

    @GetMapping("analysis")
    public ResultMsg analysis() {
        return getSuccessResult(teamRiskService.analysis());
    }

    @GetMapping("export")
    public void export(HttpServletRequest request, HttpServletResponse response) {

    }

    @PostMapping("change")
    public ResultMsg change(@RequestParam("id")String id,@RequestParam("changeLevel")String changeLevel,@RequestParam("reason")String reason,@RequestParam("attach")String attach) {
        teamRiskService.change(id, changeLevel, reason, attach);
        return getSuccessResult();
    }

    @GetMapping("allInfo")
    public ResultMsg allInfo(@RequestParam("userId")String userId) {
        return getSuccessResult();
    }

}
