package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;


public class OrgPost extends BaseModel {

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String code;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String isCivilServant;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String description;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String level;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String type;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String orgId;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsCivilServant() {
        return isCivilServant;
    }

    public void setIsCivilServant(String isCivilServant) {
        this.isCivilServant = isCivilServant;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}