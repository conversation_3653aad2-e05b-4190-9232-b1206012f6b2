package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompHistoryRecord extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String bizId;

    @ApiModelProperty(value="")
    
    private String bizType;

    @ApiModelProperty(value="")
    
    private String operatorId;

    @ApiModelProperty(value="")
    
    private String operatorName;

    @ApiModelProperty(value="")
    
    private String operatorMessage;

    @ApiModelProperty(value="")
    
    private String operatorReason;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String delFlag;

    @ApiModelProperty(value="")
    
    private Date operatorTime;

    private static final long serialVersionUID = 1L;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }
}