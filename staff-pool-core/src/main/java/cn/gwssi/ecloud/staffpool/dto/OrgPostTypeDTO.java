package cn.gwssi.ecloud.staffpool.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "查询所有岗位信息相应类", description="查询所有岗位信息相应类")
@Data
public class OrgPostTypeDTO implements Serializable {

    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="编码")
    private String code;
    @ApiModelProperty(value="名称")
    private String name;
    @ApiModelProperty(value="描述")
    private String desc;
}
