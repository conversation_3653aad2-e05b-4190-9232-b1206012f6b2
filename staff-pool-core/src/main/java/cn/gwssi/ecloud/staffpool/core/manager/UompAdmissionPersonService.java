package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.CheckPersonProjectVO;
import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson;
import cn.gwssi.ecloud.staffpool.dto.EntryPersonSelectDTO;
import cn.gwssi.ecloud.staffpool.dto.PercentageDTO;
import cn.gwssi.ecloud.staffpool.dto.PersonListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.manager.Manager;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface UompAdmissionPersonService extends Manager<String, UompAdmissionPerson> {

    List<EntryPersonSelectDTO> getEntryPersonSelectList();

    ResultMsg<String> checkPersonProject(CheckPersonProjectVO checkPersonProjectVO);

    ResultMsg<String> checkExit(String personCard);

    Integer countByInTime(String time);

    void updateStatus(String id, String status);

    Integer countByPersonCard(String personCard);

    void insertExitRelation(String exitId, String personCard);

    List<PersonListDTO> exportInfo(PersonListQueryVO personListQueryVO);

    ResultMsg<String> upload(MultipartFile file) throws Exception;

    List<PercentageDTO> getPersonInTime();

    List<PercentageDTO> getPersonInTimeBySupplierId(String supplierId);

    List<PercentageDTO> getPersonInTimeBySupplierName(String supplierName);

    /**
     * 通过用户id或者身份证号操作自动退场
     *
     * @param personCard 身份证号
     * @param personId   人员id
     * @param outTime    退场时间戳
     * @return
     */
    Boolean exitAdmissionPerson(String personId, String personCard, String outTime);

    /**
     * 入场申请人员记录id
     * @param entryStatus 驻场状态
     * @param admissionPersonId  入场申请人员记录id
     * @return
     */
    Boolean updateEntryStatusByAdmissionPersonId(String entryStatus, String admissionPersonId);

    /**
     * 入场申请账号授权后变更账号状态
     * @param admissionPersonId 入场申请人员记录id
     * @return
     */
    Boolean admissionPersonActiveAccount(String admissionPersonId);

    /**
     * 获取指定入场时间内的驻场数
     * @param inTimeStart 开始时间
     * @param inTimeEnd 结束时间
     * @return
     */
    Integer countByInTime(String inTimeStart, String inTimeEnd);

    void personCardEncrypt(QueryFilter queryFilter);

    String decryptEntryInfo(String id,String attr);
}
