package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "uomp_person_abroad")
@Data
public class UompPersonAbroad extends BaseModel {
    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value="人员id")
    private String personId;
    @NotEmpty(message = "证件名称不能为空")
    @ApiModelProperty(value = "证件名称")
    private String certificateName;

    @NotEmpty(message = "证件号不能为空")
    @ApiModelProperty(value="证件号")
    private String certificateNum;

    @ApiModelProperty(value = "签发地")
    @NotEmpty(message = "签发地不能为空")
    private String issueAt;

    @ApiModelProperty(value = "起始时间")
    @NotEmpty(message = "起始时间不能为空")
    private String startTime;

    @ApiModelProperty(value = "终止时间")
    @NotEmpty(message = "终止时间不能为空")
    private String endTime;

    @ApiModelProperty(value = "附件信息")
    @NotEmpty(message = "附件信息不能为空")
    private String fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}