package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.dao.UompTrainingRecordMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingRecordService;

import java.util.List;

@Service
public class UompTrainingRecordServiceImpl extends BaseManager<String, UompTrainingRecord> implements UompTrainingRecordService{

    @Resource
    private UompTrainingRecordMapper uompTrainingRecordMapper;

    @Override
    public int insertSelective(UompTrainingRecord record) {
        return uompTrainingRecordMapper.insertSelective(record);
    }

    @Override
    public List<UompTrainingRecord> selectTrainingRecordByUserId(String userId) {
        return uompTrainingRecordMapper.selectTrainingRecordByUserId(userId);
    }

    @Override
    public int updateByPrimaryKeySelective(UompTrainingRecord trainingRecord) {
        return uompTrainingRecordMapper.updateByPrimaryKeySelective(trainingRecord);
    }
}
