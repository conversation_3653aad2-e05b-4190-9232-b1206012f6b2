package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompDesensitizationMapper extends BaseDao<String, UompDesensitization> {

    int insertSelective(UompDesensitization record);

    String selectAllEnable();

    List<UompDesensitization> selectAllConfig(QueryFilter queryFilter);

    int selectWz(@Param("sqlStr") String sqlStr);
}