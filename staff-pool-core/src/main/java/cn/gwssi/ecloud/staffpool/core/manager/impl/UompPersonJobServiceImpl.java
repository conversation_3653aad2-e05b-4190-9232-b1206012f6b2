package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonJobMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonJobService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonJobDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonJobServiceImpl extends BaseManager<String, UompPersonJob> implements UompPersonJobService {

    @Resource
    private UompPersonJobMapper uompPersonJobMapper;

    @Override
    public int insertSelective(UompPersonJob record) {
        return uompPersonJobMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonJob record) {
        return uompPersonJobMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonJob record) {
        return uompPersonJobMapper.updateByPersonId(record);
    }

    @Override
    public int updateByPersonIds(String orgId, String[] personIds) {
        return uompPersonJobMapper.updateByPersonIds(orgId, personIds);
    }

    @Override
    public List<UompPersonJobDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonJobMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonJobMapper.deleteByPersonId(personId);
    }
}
