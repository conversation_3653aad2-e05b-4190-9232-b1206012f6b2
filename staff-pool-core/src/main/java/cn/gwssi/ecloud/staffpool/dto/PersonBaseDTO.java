package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "背调不合格人员集合")
public class PersonBaseDTO implements Serializable {
   @ApiModelProperty(value = "公司名称")
   private String workName;
   @ApiModelProperty(value = "公司id")
   private String workId;
   @ApiModelProperty(value = "人员名称集合")
   private List<BaseDTO> nameList;
}