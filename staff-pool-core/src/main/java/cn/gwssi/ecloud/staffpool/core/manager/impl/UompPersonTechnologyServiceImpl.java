package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompPersonTechnologyMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonTechnologyService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonTechnologyDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompPersonTechnologyServiceImpl extends BaseManager<String, UompPersonTechnology> implements UompPersonTechnologyService {

    @Resource
    private UompPersonTechnologyMapper uompPersonTechnologyMapper;

    @Override
    public int insertSelective(UompPersonTechnology record) {
        return uompPersonTechnologyMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonTechnology record) {
        return uompPersonTechnologyMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPersonId(UompPersonTechnology record) {
        return uompPersonTechnologyMapper.updateByPersonId(record);
    }

    @Override
    public int updateByPersonIds(String orgId, String[] personIds) {
        return uompPersonTechnologyMapper.updateByPersonIds(orgId, personIds);
    }

    @Override
    public List<UompPersonTechnologyDto> selectByPersonIds(List<String> personIdList) {
        return uompPersonTechnologyMapper.selectByPersonIds(personIdList);
    }

    @Override
    public void deleteByPersonId(String personId) {
        uompPersonTechnologyMapper.deleteByPersonId(personId);
    }
}
