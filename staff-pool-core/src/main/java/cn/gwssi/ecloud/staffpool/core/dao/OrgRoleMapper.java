package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.OrgRole;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgRoleTypeDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrgRoleMapper extends BaseDao<String, OrgRole> {

    List<OrgRoleTypeDTO> getRoleByOrgType(@Param("name")String name, @Param("orgType")String orgType);

    String selectRoleByUserId(@Param("userId")String userId);

    String selectRoleByAccount(@Param("account")String account);

    List<UompPermissionBaseDTO> selectOwerRoleByUserId(QueryFilter queryFilter);

    List<UompPermissionBaseDTO> selectOwerNoRoleByUserId(@Param("userId")String userId, @Param("orgType")String orgType);

    String selectNameByUserId(String userId);

    List<BaseDTO> selectBaseByUserId(String userId);

    OrgRole getRoleByAlias(@Param("alias") String alias);

    List<String> getMrRoleId();

}
