package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson;
import cn.gwssi.ecloud.staffpool.dto.TrainingDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UompTrainingRecordPersonMapper extends BaseDao<String, UompTrainingRecordPerson> {


    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompTrainingRecordPerson record);


    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompTrainingRecordPerson record);

    List<TrainingDTO> selectAllByLimit3();

    void deleteByRecordId(String id);
}
