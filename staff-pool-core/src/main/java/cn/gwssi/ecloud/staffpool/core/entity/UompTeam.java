package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import java.util.Date;

public class UompTeam extends BaseModel {

    private String name;

    private String orgid;

    private String teamType;

    private String describe;

    private String status;

    private String delFlag;

    private String orgName;

    private String teamLeader;

    private String teamLeaderId;

    private String teamRelations;

    private String groupLeaders;

    private String groupLeaderIds;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgid() {
        return orgid;
    }

    public void setOrgid(String orgid) {
        this.orgid = orgid;
    }

    public String getTeamType() {
        return teamType;
    }

    public void setTeamType(String teamType) {
        this.teamType = teamType;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getTeamLeader() {
        return teamLeader;
    }

    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }

    public String getTeamLeaderId() {
        return teamLeaderId;
    }

    public void setTeamLeaderId(String teamLeaderId) {
        this.teamLeaderId = teamLeaderId;
    }

    public String getTeamRelations() {
        return teamRelations;
    }

    public void setTeamRelations(String teamRelations) {
        this.teamRelations = teamRelations;
    }

    public String getGroupLeaders() {
        return groupLeaders;
    }

    public void setGroupLeaders(String groupLeaders) {
        this.groupLeaders = groupLeaders;
    }

    public String getGroupLeaderIds() {
        return groupLeaderIds;
    }

    public void setGroupLeaderIds(String groupLeaderIds) {
        this.groupLeaderIds = groupLeaderIds;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", orgid=").append(orgid);
        sb.append(", teamType=").append(teamType);
        sb.append(", describe=").append(describe);
        sb.append(", status=").append(status);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", orgName=").append(orgName);
        sb.append(", teamLeader=").append(teamLeader);
        sb.append(", teamLeaderId=").append(teamLeaderId);
        sb.append(", teamRelations=").append(teamRelations);
        sb.append(", groupLeaders=").append(groupLeaders);
        sb.append(", groupLeaderIds=").append(groupLeaderIds);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompTeam other = (UompTeam) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getOrgid() == null ? other.getOrgid() == null : this.getOrgid().equals(other.getOrgid()))
            && (this.getTeamType() == null ? other.getTeamType() == null : this.getTeamType().equals(other.getTeamType()))
            && (this.getDescribe() == null ? other.getDescribe() == null : this.getDescribe().equals(other.getDescribe()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getTeamLeader() == null ? other.getTeamLeader() == null : this.getTeamLeader().equals(other.getTeamLeader()))
            && (this.getTeamLeaderId() == null ? other.getTeamLeaderId() == null : this.getTeamLeaderId().equals(other.getTeamLeaderId()))
            && (this.getTeamRelations() == null ? other.getTeamRelations() == null : this.getTeamRelations().equals(other.getTeamRelations()))
            && (this.getGroupLeaders() == null ? other.getGroupLeaders() == null : this.getGroupLeaders().equals(other.getGroupLeaders()))
            && (this.getGroupLeaderIds() == null ? other.getGroupLeaderIds() == null : this.getGroupLeaderIds().equals(other.getGroupLeaderIds()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getOrgid() == null) ? 0 : getOrgid().hashCode());
        result = prime * result + ((getTeamType() == null) ? 0 : getTeamType().hashCode());
        result = prime * result + ((getDescribe() == null) ? 0 : getDescribe().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getTeamLeader() == null) ? 0 : getTeamLeader().hashCode());
        result = prime * result + ((getTeamLeaderId() == null) ? 0 : getTeamLeaderId().hashCode());
        result = prime * result + ((getTeamRelations() == null) ? 0 : getTeamRelations().hashCode());
        result = prime * result + ((getGroupLeaders() == null) ? 0 : getGroupLeaders().hashCode());
        result = prime * result + ((getGroupLeaderIds() == null) ? 0 : getGroupLeaderIds().hashCode());
        return result;
    }
}