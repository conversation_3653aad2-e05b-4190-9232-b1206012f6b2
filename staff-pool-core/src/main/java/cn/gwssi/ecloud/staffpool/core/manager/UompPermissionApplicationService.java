package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.PermissionListQueryVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication;
import cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPermissionApplicationService extends Manager<String, UompPermissionApplication> {

    PageResult getList(PermissionListQueryVO permissionListQueryVO);

    PageResult getPersonList(QueryFilter queryFilter);

    PageResult getRoleByUserId(QueryFilter queryFilter);

    PageResult getPostByUserId(QueryFilter queryFilter);

    PageResult getNoPostByUserId(QueryFilter queryFilter);

    PageResult getNoRoleByUserId(QueryFilter queryFilter);

    List<String> inEmpower(String permissionInIds);

    void updateStatus(String id, String status);

    List<OrgGroupBaseDTO> getTreeByOrgType();
}
