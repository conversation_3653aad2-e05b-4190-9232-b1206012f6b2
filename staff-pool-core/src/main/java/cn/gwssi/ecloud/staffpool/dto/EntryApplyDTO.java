package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(description="根据id查询入场申请详情响应类")
@Data
public class EntryApplyDTO implements Serializable {

    @ApiModelProperty(value="入场人员id")
    private String id;
    @ApiModelProperty(value="人员id")
    private String personId;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    @Sensitive(prefixLen = 3,suffixLen = 3,actualLen = 18)
    private String personCard;
    @ApiModelProperty(value="入职时间")
    private String entryDate;
    @ApiModelProperty(value="学历")
    private String education;
    @ApiModelProperty(value="职称")
    private String technicalPost;
    @ApiModelProperty(value="所在公司")
    private String workingCompany;
    @ApiModelProperty(value="所在公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="所在公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="参与系统")
    private String engagementProject;
    @ApiModelProperty(value="笔试成绩")
    private String score;
    @ApiModelProperty(value="面谈结果")
    private String interviewResult;
    @ApiModelProperty(value="运维组")
    private String maintenanceGroupName;
    @ApiModelProperty(value="岗位")
    private String postName;
    @ApiModelProperty(value="工作内容")
    private String jobContent;

    @ApiModelProperty(value="")
    private String fileInfoJson;
    @ApiModelProperty(value="")
    private String postFileInfoJson;
    @ApiModelProperty(value="笔试附件")
    private List<FileInfoDTO> fileInfo;
    @ApiModelProperty(value="岗位附件")
    private List<FileInfoDTO> postFileInfo;
    @ApiModelProperty(value="编号")
    private String applyCode;
    @ApiModelProperty(value="标题")
    private String applyTitle;
    @ApiModelProperty(value="申请人")
    private String applyUserName;
    @ApiModelProperty(value="申请时间")
    private Date applyTime;
    @ApiModelProperty(value="申请事项")
    private String applyMatter;
    @ApiModelProperty(value="服务类型")
    private String servicType;
    @ApiModelProperty(value="驻场服务地点")
    private String serviceLocation;
    @ApiModelProperty(value="是否涉密岗位")
    private String isClassifiedPosition;
    @ApiModelProperty(value="技术方向")
    private String technicalDirection;
    @ApiModelProperty(value="创建人")
    private String createBy;
    @ApiModelProperty(value="流程实例id")
    private String instId;

    @ApiModelProperty(value="预计到访时间")
    private String planVisitTime;
    @ApiModelProperty(value="计划退出时间")
    private String planOutTime;
    @ApiModelProperty(value="角色")
    private String personRole;
    @ApiModelProperty(value="角色 id")
    private String personRoleId;
    @ApiModelProperty(value="角色 json")
    private String personRoleJson;
    @ApiModelProperty(value="主管部门")
    private String departName;
    @ApiModelProperty(value="主管部门 id")
    private String departId;
    @ApiModelProperty(value="主管部门 json")
    private String departJson;
    @ApiModelProperty(value="岗位类型")
    private String positionType;
    @ApiModelProperty(value="涉密岗位性质")
    private String classifiedPosition;
    @ApiModelProperty(value="申请岗位 JSON")
    private String postJson;
    @ApiModelProperty(value = "性别 0: 男,  1:女")
    private String sex;
    @ApiModelProperty(value = "申请岗位id")
    private String postId;
}
