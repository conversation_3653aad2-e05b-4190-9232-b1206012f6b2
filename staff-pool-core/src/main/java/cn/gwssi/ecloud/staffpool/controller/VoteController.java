package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.VoteInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.manager.VoteInfoService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 * 问卷管理
 */
@Api(description = "问卷管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/vote")
public class VoteController extends BaseController<VoteInfo> {

    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private VoteInfoService voteInfoService;

    @Override
    protected String getModelDesc() {
        return "问卷管理";
    }

    @ApiOperation(value = "职业测评查询接口")
    @RequestMapping(value = "/queryOccupationAssessment")
    public PageResult<VoteUserAnswerDto> queryOccupationAssessment(HttpServletRequest request, HttpServletResponse response,
                                                                   @RequestParam(value = "personId") @ApiParam(value = "人员id") String personId,
                                                                   @RequestParam(value = "title", required = false) @ApiParam(value = "职业测评名称") String title,
                                                                   @RequestParam(value = "date", required = false) @ApiParam(value = "测评时间") String date,
                                                                   @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                   @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {
        BaseDTO baseDTO = uompPersonInfoService.selectUserId(personId);
        if (baseDTO != null && !StringUtils.isEmpty(baseDTO.getId())) {
            QueryFilter queryFilter = getQueryFilter(request);
            queryFilter.addFilter("vote.type", "3", QueryOP.EQUAL);
            queryFilter.addFilter("answer.answer_status", "1", QueryOP.EQUAL);
            queryFilter.addFilter("answer.user_id", baseDTO.getId(), QueryOP.EQUAL);
            queryFilter.addFieldSort("update_time", "DESC");
            if (!StringUtils.isEmpty(title)) {
                queryFilter.addFilter("vote.TITLE", title, QueryOP.LIKE);
            }
            if (!StringUtils.isEmpty(date)) {
                String[] entryDates = date.split(",");
                if (entryDates.length > 1) {
                    queryFilter.addFilter("answer.update_time", entryDates[0], QueryOP.GREAT_EQUAL);
                    queryFilter.addFilter("answer.update_time", entryDates[1], QueryOP.LESS_EQUAL);
                }
            }
            return new PageResult(voteInfoService.selectVoteInfo(queryFilter));
        }
        return new PageResult<>(new ArrayList<>());
    }
}
