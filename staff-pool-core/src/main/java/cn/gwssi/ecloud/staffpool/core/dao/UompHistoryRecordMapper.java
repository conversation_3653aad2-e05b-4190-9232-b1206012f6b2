package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord;
import cn.gwssi.ecloud.staffpool.dto.UompBlackDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UompHistoryRecordMapper extends BaseDao<String, UompHistoryRecord> {

    int insertSelective(UompHistoryRecord record);

    List<UompBlackDto> getBlackHistoryList(QueryFilter queryFilter);

}