package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.OrgPostMapper;
import cn.gwssi.ecloud.staffpool.core.entity.OrgPost;
import cn.gwssi.ecloud.staffpool.core.manager.OrgPostService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrgPostServiceImpl extends BaseManager<String, OrgPost> implements OrgPostService {

    @Resource
    private OrgPostMapper orgPostMapper;

    @Override
    public Boolean countByPostKey(String postKey) {
        return orgPostMapper.countByPostKey(postKey)>0;
    }
}
