package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 培训计划参会人员信息表
 */
@Data
@ApiModel(description="培训计划参会人员信息表")
public class UompTrainingPlanPerson  extends BaseModel {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 培训计划id
    */
    @ApiModelProperty(value="培训计划id")
    private String trainingPlanId;

    /**
    * 参会人员id
    */
    @ApiModelProperty(value="参会人员id")
    private String trainingPersonId;

    /**
    * 参会人员名称
    */
    @ApiModelProperty(value="参会人员名称")
    private String trainingPersonName;
}
