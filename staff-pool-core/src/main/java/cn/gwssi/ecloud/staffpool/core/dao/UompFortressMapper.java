package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompFortress;
import cn.gwssi.ecloud.staffpool.dto.UompFortessDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompFortressMapper extends BaseDao<String, UompFortress> {

    int insertSelective(UompFortress record);

    UompFortessDTO selectInfoById(@Param("fortId") String fortId);

    List<UompFortessDTO> selectAll();
}