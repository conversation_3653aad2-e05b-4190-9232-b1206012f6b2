package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="退场申请-通过身份证号查询人员角色角色权限等信息响应类")
@Data
public class PersonInfoDTO implements Serializable {

    @ApiModelProperty(value="名称")
    private String account;
    private String personName;
    private String userId;
    private String groupName;
    private String postName;
    private String roleName;
    private List<OutPermissionBean> outPermissionList;
}
