package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_job")
@Data
public class UompPersonJobDto extends BaseModel {
    @ApiModelProperty(value = "")
    private String personId;

    @ApiModelProperty(value = "")
    private String jobTime;

    @ApiModelProperty(value = "")
    private String jobBeginTime;

    @ApiModelProperty(value = "")
    private String jobEndTime;

    @ApiModelProperty(value = "")
    private String companyName;

    @ApiModelProperty(value = "")
    private String jobPosition;

    @ApiModelProperty(value = "")
    private String jobDescribe;

    @ApiModelProperty(value = "")
    private JSONArray fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;

    private static final long serialVersionUID = 1L;
}