package cn.gwssi.ecloud.staffpool.core.manager;


import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDutyDetail;
import cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

/**
 * 人员值班详情表(UompPersonDutyDetail)表服务接口
 */
public interface UompPersonDutyDetailService extends Manager<String, UompPersonDutyDetail> {


    void removeByDutyId(String dutyId);

    List<UompPersonContactsDTO> selectByDutyId(String id);

    List<UompPersonDutyDto> selectByMonth(String month,String orgId);

    int insertSelective(UompPersonDutyDetail uompPersonDuty);

    int updateByPrimaryKeySelective(UompPersonDutyDetail uompPersonDuty);
}

