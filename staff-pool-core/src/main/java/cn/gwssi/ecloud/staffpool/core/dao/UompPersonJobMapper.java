package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonJob;
import cn.gwssi.ecloud.staffpool.dto.UompPersonJobDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompPersonJobMapper extends BaseDao<String, UompPersonJob> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompPersonJob record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompPersonJob record);

    int updateByPersonId(UompPersonJob record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonJobDto> selectByPersonIds(@Param("personIds") List<String> personIdList);

    void deleteByPersonId(String personId);
}