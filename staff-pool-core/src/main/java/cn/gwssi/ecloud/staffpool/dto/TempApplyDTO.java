package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="临时入场列表查询响应类")
@Data
public class TempApplyDTO implements Serializable {

    @ApiModelProperty(value="主键")
    private String id;
    @ApiModelProperty(value="编号")
    private String applyCode;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="就职公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="预计到访时间")
    private String planVisitTime;
    @ApiModelProperty(value="实际到访时间")
    private String realVisitTime;
    @ApiModelProperty(value="录入人id")
    private String destClerkId;
    @ApiModelProperty(value="录入人姓名")
    private String destClerkName;
    @ApiModelProperty(value="离场时间")
    private String exitTime;
    @ApiModelProperty(value="工作内容")
    private String jobContent;
    @ApiModelProperty(value="黑名单 0-非黑名单 1-是黑名单")
    private String blacklist;
    @ApiModelProperty(value="接待人")
    private String acceptName;
    @ApiModelProperty(value="应用系统")
    private String engagementProject;
    @ApiModelProperty(value="应用系统id")
    private String engagementProjectId;
    @ApiModelProperty(value="应用系统json")
    private String engagementProjectJson;
    @ApiModelProperty(value="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过")
    private String applyStatus;
    @ApiModelProperty(value="备案状态 0:未备案, 1:已备案")
    private String filingStatus;

    @ApiModelProperty(value="流程实例id")
    private String instId;

    @ApiModelProperty(value="标题")
    private String applyTitle;
    @ApiModelProperty(value="性别")
    private String sex;
    @ApiModelProperty(value="职务")
    private String applicatDuty;
}
