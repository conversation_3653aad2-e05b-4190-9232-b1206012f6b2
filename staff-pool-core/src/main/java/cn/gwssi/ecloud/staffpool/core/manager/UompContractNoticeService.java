package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice;

import java.util.List;

public interface UompContractNoticeService{

    int deleteByPrimaryKey(String id);

    int insert(UompContractNotice record);

    int insertSelective(UompContractNotice record);

    UompContractNotice selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(UompContractNotice record);

    int updateByPrimaryKey(UompContractNotice record);

    List<UompContractNotice> getListByContractIds(List<String> contractIds);

}
