package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.VoteUserAnswerMapper;
import cn.gwssi.ecloud.staffpool.core.entity.VoteUserAnswer;
import cn.gwssi.ecloud.staffpool.core.manager.VoteUserAnswerService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class VoteUserAnswerServiceImpl extends BaseManager<String, VoteUserAnswer> implements VoteUserAnswerService {

    @Resource
    private VoteUserAnswerMapper voteUserAnswerMapper;

    @Override
    public int insertSelective(VoteUserAnswer record) {
        return voteUserAnswerMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(VoteUserAnswer record) {
        return voteUserAnswerMapper.updateByPrimaryKeySelective(record);
    }
}
