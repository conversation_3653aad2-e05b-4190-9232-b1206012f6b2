package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.dto.UompPersonInfoDTO;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface  UompPersonInfoService extends Manager<String, UompPersonInfo> {

    int insertSelective(UompPersonInfo record);

    int updateByIds(String orgId, String[] ids);

    int updateByPrimaryKeySelective(UompPersonInfo record);

    String selectIdAndAccountByPersonNameAndPersonCard(String personName, String personCard);

    UompPersonInfoDTO getInfoByPersonId(String personId);

    List<UompDesensitization> getDesRule(String tableName, String desRuleParam, String instId, String createBy);

    String updateAccountById(String id, String accountNum);

    void empower(String ids);

    void activeAccount(String ids);

    String getTitleByCurrentUser();

    PageResult getBusiness(QueryFilter queryFilter);

    PageResult getPostByOrgType(QueryFilter queryFilter);

    BaseDTO selectUserId(String id);

    List<UompBlackDto> selectBlack(String id);

    PageResult getRoleByOrgType(QueryFilter queryFilter);

    BaseInstDTO selectTaskIdByInstId(String instId);

    ResponsePageData<UompSupplierPersonnelDto> getPersonnelList(String supplierManagementId, String personName, String technicalDirection, Integer pageNo, Integer pageSize);

    // 查询审批通过且尚未分配账号的人员信息
    List<UompPersonInfoAndAccountDTO> selectNoAccountPeronList(QueryFilter queryFilter);

    List<PersonSeletDTO> getPersonSeletList();

    List<Map<String, String>> getAcceptPersonSelectList(String id);

    BackgroundListDTO backgroundList();

    List<PercentageDTO> getEducationList();

    List<PercentageDTO> personAgeList();

    void deactivateAccount(String personCard);

    PersonInfoDTO getPersonInfoByCard(String personCard);

    List<UompBlackDto> getBlackList(QueryFilter queryFilter);

    void deleteByPersonCard(String id, String personCard);

    String selectAccountByPersonCard(String personCard);

    Integer countByOrg(String orgGroupId);

    List<UompPersonInfo> selectAllByOrg(String orgGroupId);

    List<UompPersonInfo> selectAllByOrgStatusAll(String orgGroupId);

    void updatePersonAllInfo(String personId);

    void saveAndUpdatePersonDetail(UserDTO user, UompPersonInfoVo personInfo);

    List<PercentageDTO> backgroundInfoPie();

    List<PercentageDTO> backgroundInfoPieByWorkingCompanyId(String workingCompanyId);

    ColumnarDTO backgroundInfoColumnar();

    PersonStructureDTO personStructure();

    List<TechnicalDirectionDTO> technicalDirection();

    PersonWorkAndEduDTO personWorkAndEdu();

    String getOrgUserIdByUserId(String userId);

    List<PercentageDTO> getPersonInTimeBySupplierId(String supplierId);

    List<PercentageDTO> getPersonInTimeBySupplierName(String supplierName);

    // 账号申请管理列表
    PageResult<AccountPersonDTO> list(QueryFilter queryFilter);

    // 创建账号
    String addAccount(AccountPersonAddDTO accountPersonAddDTO);

    // 账号启用、停用
    void updateAccountStatus(String ids, String status);

    String updateAccountByPersonId(String id, String accountNum);

    PageResult<PostDTO> noPostList(QueryFilter queryFilter, String orgUserId);

    UompPersonInfo selectOneByPersonIdOrPersonCard(String personId, String personCard);

    Boolean updateEntryStatusByPersonIdOrPersonCard(String entryStatus, String personId, String personCard);

    List<UompPersonContactsDTO> selectContacts(QueryFilter queryFilter);

    Integer countAllByNormal();

    Integer countAllByEntry();

    UompPersonInfo selectAllByUserId(String userId);

    /**
     * 人员信息保存更新
     * @param uompPersonInfoVo
     */
    void saveAllPersonInfo(UompPersonInfoVo uompPersonInfoVo);

    /**
     * 查询人员信息详情
     * @param id
     * @param updating
     * @param instId
     * @return
     */
    UompPersonInfoVo getPersonAllInfo(String id, String updating,String instId);

    /**
     * 查询
     * @param queryFilter
     * @param queryType
     * @return
     */
    List<UompPersonInfo> selectList(QueryFilter queryFilter, String queryType);

    /**
     * 人员导出
     * @param queryFilter
     * @return
     */
    List<UompPersonInfo> personExport(QueryFilter queryFilter);

    /**
     * 导入
     * @param file
     * @return
     * @throws Exception
     */
    List<Map<String, String>> importPersons(MultipartFile file) throws Exception;

    /**
     * 批量加密
     */
    void batchEncrypt(String  ids);


    /**
     * 解密字符串
     * @param id
     * @param attr
     * @return
     */
    String decryptString(String id,String attr);

}
