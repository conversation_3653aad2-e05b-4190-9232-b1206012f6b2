package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.UompApplicationSystemManagementSave;
import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement;
import cn.gwssi.ecloud.staffpool.core.manager.UompApplicationSystemManagementService;
import cn.gwssi.ecloud.staffpool.dto.UompApplicationSystemManagementDTO;
import cn.gwssi.ecloud.staffpool.dto.UompApplicationSystemManagementListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompContractManagementSystemDTO;
import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.module.cmdb.api.model.CAttributeValuesDTO;
import cn.gwssi.ecloudbpm.module.cmdb.api.model.CModelDataDTO;
import cn.gwssi.ecloudbpm.module.cmdb.api.service.ICMDBService;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(description = "应用系统管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/system")
public class UompApplicationSystemController extends BaseController<UompApplicationSystemManagement> {

    @Resource
    private UompApplicationSystemManagementService uompApplicationSystemManagementService;


    @Resource
    private ICMDBService icmdbService;

    @ApiOperation(value = "应用系统查询列表")
    @RequestMapping(value = "/getSystemList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompApplicationSystemManagementListDTO> getSystemList(HttpServletRequest request,
                                                                            @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                            @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                                            @RequestParam(value = "applicationSystemName", required = false) @ApiParam(value = "应用系统名称") String applicationSystemName,
                                                                            @RequestParam(value = "departName", required = false) @ApiParam(value = "运维部门名称") String departName,
                                                                            @RequestParam(value = "systemStatus", required = false) @ApiParam(value = "系统状态，逗号隔开") String systemStatus,
                                                                            @RequestParam(value = "principalName", required = false) @ApiParam(value = "负责人名称") String principalName,
                                                                            @RequestParam(value = "supplierName", required = false) @ApiParam(value = "服务商名称") String supplierName,
                                                                            @RequestParam(value = "supplierId", required = false) @ApiParam(value = "服务商id") String supplierId) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("s.DEL_FLAG", "0", QueryOP.EQUAL);
        // 应用系统名称
        if (StringUtils.isNotEmpty(applicationSystemName)) {
            queryFilter.addFilter("s.application_system_name", applicationSystemName, QueryOP.LIKE);
        }
        // 运维部门
        if (StringUtils.isNotEmpty(departName)) {
            queryFilter.addFilter("s.depart_name", departName, QueryOP.LIKE);
        }
        // 应用系统状态 0使用中 1已停用
        if (StringUtils.isNotEmpty(systemStatus)) {
            queryFilter.addFilter("s.system_status", systemStatus, QueryOP.IN);
        }
        // 负责人
        if (StringUtils.isNotEmpty(principalName)) {
            queryFilter.addFilter("s.principal_name", principalName, QueryOP.LIKE);
        }
        // 服务商id
        if (StringUtils.isNotEmpty(supplierId)) {
            queryFilter.addFilter("s.SUPPLIER_ID", supplierId, QueryOP.EQUAL);
        }
        // 服务商名称
        if (StringUtils.isNotEmpty(supplierName)) {
            queryFilter.addFilter("m.SUPPLIER_NAME", supplierName, QueryOP.LIKE);
            queryFilter.getParams().put("supplierName", supplierName);
        }
        // 上线时间倒叙
        queryFilter.addFieldSort("s.ONLINE_TIME", "DESC");
        return uompApplicationSystemManagementService.getSystemList(queryFilter);
    }

    @ApiOperation(value = "应用系统保存")
    @PostMapping(value = "/save")
    public ResultMsg<String> save(@Validated @RequestBody UompApplicationSystemManagementSave uompApplicationSystemManagementSave) {
        return getSuccessResult(uompApplicationSystemManagementService.save(uompApplicationSystemManagementSave));
    }

    @ApiOperation(value = "根据id查询应用系统详情")
    @RequestMapping(value = "/getSystemInfoById", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<UompApplicationSystemManagementDTO> getSystemInfoById(@RequestParam("id") @ApiParam(value = "主键id") String id) {
        return getSuccessResult(uompApplicationSystemManagementService.getSystemInfoById(id));
    }

    @ApiOperation(value = "根据应用名称查询应用系统详情")
    @RequestMapping(value = "/getSystemInfoByName", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<UompApplicationSystemManagementDTO> getSystemInfoByName(@RequestParam("name") @ApiParam(value = "系统名") String name) {
        return getSuccessResult(uompApplicationSystemManagementService.getSystemInfoByName(name));
    }

    @ApiOperation(value = "应用系统未关联合同查询列表")
    @RequestMapping(value = "/unbindingContractList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompContractManagementSystemDTO> unbindingContractList(HttpServletRequest request,
                                                                             @RequestParam("pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                             @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                                             @RequestParam(value = "contractName", required = false) @ApiParam(value = "合同名称") String contractName,
                                                                             @RequestParam(value = "supplierId", required = false) @ApiParam(value = "服务商id") String supplierId) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 查询合同状态正常的 1暂存 3正常 4临期 5失效 6过保
        queryFilter.addFilter("CONTRACT_STATUS", "3", QueryOP.EQUAL);
        // 合同名称模糊查询
        if (StringUtils.isNotEmpty(contractName)) {
            queryFilter.addFilter("CONTRACT_NAME", contractName, QueryOP.LIKE);
        }
        // 服务商id
        if (StringUtils.isNotEmpty(supplierId)) {
            queryFilter.addFilter("PARTY_B_ID", supplierId, QueryOP.EQUAL);
        }
        // 签订日期倒叙
        queryFilter.addFieldSort("SIGNING_DATE", "desc");
        return uompApplicationSystemManagementService.unbindingContractList(queryFilter);
    }


    @ApiOperation(value = "应用系统查询列表")
    @RequestMapping(value = "/bindApplicationSystem", method = {RequestMethod.POST})
    public ResultMsg bindApplicationSystem(@RequestBody JSONObject applicationSystem) {
        uompApplicationSystemManagementService.bindApplicationSystem(applicationSystem);
        return getSuccessResult();
    }

    @GetMapping({"/cmdb/instListByParam"})
    public PageResult instListByParam(@RequestParam(required = true) boolean noPage, @RequestParam(defaultValue = "1") int pageNo, @RequestParam(defaultValue = "10") int pageSize, @RequestParam(required = true) String modelName, @RequestParam(required = false) String attributeName, @RequestParam(required = false) String attributeValue, @RequestParam(required = true) boolean isAuth) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setNoPage(noPage);
        pageQuery.setPageNo(pageNo);
        pageQuery.setPageSize(pageSize);
        CModelDataDTO queryDto = new CModelDataDTO();
        queryDto.setModelName(modelName);
        queryDto.setAuth(isAuth);
        CAttributeValuesDTO cAttributeValuesDTO = new CAttributeValuesDTO();
        cAttributeValuesDTO.setValue(attributeValue);
        cAttributeValuesDTO.setName(attributeName);
        queryDto.setAttributes(Lists.newArrayList(new CAttributeValuesDTO[]{cAttributeValuesDTO}));
        PageResult applicationSystems = (PageResult) icmdbService.instList(pageQuery, queryDto).get("data");

        List<Map<String, Object>> data = applicationSystems.getRows();
        data.forEach(system -> {
            String id = String.valueOf(system.get("id"));
            UompApplicationSystemManagement uompApplicationSystemManagement = uompApplicationSystemManagementService.get(id);
            if(!Objects.isNull(uompApplicationSystemManagement)) {
                system.put("service_provider", uompApplicationSystemManagement.getSupplierName());
            }
        });
        return applicationSystems;
    }

    @Override
    protected String getModelDesc() {
        return "应用系统管理";
    }
}
