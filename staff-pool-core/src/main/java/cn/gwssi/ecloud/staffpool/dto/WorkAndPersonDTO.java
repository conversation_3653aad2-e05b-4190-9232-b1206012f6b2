package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "服务商不合格人员类")
public class WorkAndPersonDTO implements Serializable {
   @ApiModelProperty(value = "服务商id")
   private String workId;
   @ApiModelProperty(value = "服务商名称")
   private String workName;
   @ApiModelProperty(value = "人员id")
   private String personId;
   @ApiModelProperty(value = "人员名称")
   private String personName;
}