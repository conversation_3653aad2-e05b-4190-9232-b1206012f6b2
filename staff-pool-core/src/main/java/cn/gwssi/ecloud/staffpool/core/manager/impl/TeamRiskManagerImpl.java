package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.TeamRiskMapper;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRisk;
import cn.gwssi.ecloud.staffpool.core.entity.risk.TeamRiskVO;
import cn.gwssi.ecloud.staffpool.core.manager.TeamRiskManager;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TeamRiskManagerImpl extends BaseManager<String, TeamRisk> implements TeamRiskManager {

    @Resource
    private TeamRiskMapper teamRiskMapper;

    @Override
    public List<TeamRiskVO> queryRiskList(QueryFilter queryFilter) {
        return teamRiskMapper.queryRiskList(queryFilter);
    }

    @Override
    public void createRiskHis(TeamRisk teamRisk) {
        teamRiskMapper.createRiskHis(teamRisk);
    }

    @Override
    public List<TeamRisk> queryRiskHisList(QueryFilter queryFilter) {
        return teamRiskMapper.queryRiskHisList(queryFilter);
    }


}
