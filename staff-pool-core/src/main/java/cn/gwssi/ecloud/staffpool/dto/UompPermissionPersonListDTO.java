package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "权限申请-用户选择接口类", description = "权限申请-用户选择接口类")
@Data
public class UompPermissionPersonListDTO implements Serializable {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @ApiModelProperty(value = "账号")
    private String userAccount;
    @ApiModelProperty(value = "机构id")
    private String groupId;
    @ApiModelProperty(value = "所属机构")
    private String groupName;
    @ApiModelProperty(value = "路径地址")
    private String orgPathName;
    @ApiModelProperty(value = "运维组名称")
    private String maintenanceGroupName;
    @ApiModelProperty(value = "已有角色")
    private String existingRoles;
    @ApiModelProperty(value = "已有岗位")
    private String existingPositions;
    @ApiModelProperty(value = "应用系统")
    private String systemName;
    @ApiModelProperty(value = "已有应用系统（json）")
    private String existingSystem;
}
