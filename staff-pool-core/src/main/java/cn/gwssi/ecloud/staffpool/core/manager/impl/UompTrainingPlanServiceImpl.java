package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan;
import cn.gwssi.ecloud.staffpool.core.dao.UompTrainingPlanMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingPlanService;
@Service
public class UompTrainingPlanServiceImpl  extends BaseManager<String, UompTrainingPlan> implements UompTrainingPlanService{

    @Autowired
    private UompTrainingPlanMapper uompTrainingPlanMapper;

    @Override
    public int insertSelective(UompTrainingPlan record) {
        return uompTrainingPlanMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompTrainingPlan record) {
        return uompTrainingPlanMapper.updateByPrimaryKeySelective(record);
    }
}
