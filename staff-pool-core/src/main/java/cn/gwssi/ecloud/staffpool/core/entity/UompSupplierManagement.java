package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;

import java.util.Date;

@ApiModel(value = "供应商信息")
public class UompSupplierManagement extends BaseModel {

    private String supplierName;

    private String creditCode;

    private String shortName;

    private String tel;

    private String respName;

    private String respTel;

    private String supplierType;

    private String supplierStatus;

    private String registerProvince;

    private String registerCity;

    private String registerRegin;

    private String registerAddress;

    private String contactAddress;

    private String remark;

    private String createOrgId;

    private String updateOrgId;

    private String delFlag;

    private Date entryTime;

    private String entryId;

    private String entryName;

    private String groupId;

    private String usedName;

    private String startCooperationTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRespName() {
        return respName;
    }

    public void setRespName(String respName) {
        this.respName = respName;
    }

    public String getRespTel() {
        return respTel;
    }

    public void setRespTel(String respTel) {
        this.respTel = respTel;
    }

    public String getSupplierType() {
        return supplierType;
    }

    public void setSupplierType(String supplierType) {
        this.supplierType = supplierType;
    }

    public String getSupplierStatus() {
        return supplierStatus;
    }

    public void setSupplierStatus(String supplierStatus) {
        this.supplierStatus = supplierStatus;
    }

    public String getRegisterProvince() {
        return registerProvince;
    }

    public void setRegisterProvince(String registerProvince) {
        this.registerProvince = registerProvince;
    }

    public String getRegisterCity() {
        return registerCity;
    }

    public void setRegisterCity(String registerCity) {
        this.registerCity = registerCity;
    }

    public String getRegisterRegin() {
        return registerRegin;
    }

    public void setRegisterRegin(String registerRegin) {
        this.registerRegin = registerRegin;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Date getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(Date entryTime) {
        this.entryTime = entryTime;
    }

    public String getEntryId() {
        return entryId;
    }

    public void setEntryId(String entryId) {
        this.entryId = entryId;
    }

    public String getEntryName() {
        return entryName;
    }

    public void setEntryName(String entryName) {
        this.entryName = entryName;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getUsedName() {
        return usedName;
    }

    public void setUsedName(String usedName) {
        this.usedName = usedName;
    }

    public String getStartCooperationTime() {
        return startCooperationTime;
    }

    public void setStartCooperationTime(String startCooperationTime) {
        this.startCooperationTime = startCooperationTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", creditCode=").append(creditCode);
        sb.append(", shortName=").append(shortName);
        sb.append(", tel=").append(tel);
        sb.append(", respName=").append(respName);
        sb.append(", respTel=").append(respTel);
        sb.append(", supplierType=").append(supplierType);
        sb.append(", supplierStatus=").append(supplierStatus);
        sb.append(", registerProvince=").append(registerProvince);
        sb.append(", registerCity=").append(registerCity);
        sb.append(", registerRegin=").append(registerRegin);
        sb.append(", registerAddress=").append(registerAddress);
        sb.append(", contactAddress=").append(contactAddress);
        sb.append(", remark=").append(remark);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", createOrgId=").append(createOrgId);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateOrgId=").append(updateOrgId);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", entryTime=").append(entryTime);
        sb.append(", entryId=").append(entryId);
        sb.append(", entryName=").append(entryName);
        sb.append(", groupId=").append(groupId);
        sb.append(", usedName=").append(usedName);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompSupplierManagement other = (UompSupplierManagement) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
                && (this.getCreditCode() == null ? other.getCreditCode() == null : this.getCreditCode().equals(other.getCreditCode()))
                && (this.getShortName() == null ? other.getShortName() == null : this.getShortName().equals(other.getShortName()))
                && (this.getTel() == null ? other.getTel() == null : this.getTel().equals(other.getTel()))
                && (this.getRespName() == null ? other.getRespName() == null : this.getRespName().equals(other.getRespName()))
                && (this.getRespTel() == null ? other.getRespTel() == null : this.getRespTel().equals(other.getRespTel()))
                && (this.getSupplierType() == null ? other.getSupplierType() == null : this.getSupplierType().equals(other.getSupplierType()))
                && (this.getSupplierStatus() == null ? other.getSupplierStatus() == null : this.getSupplierStatus().equals(other.getSupplierStatus()))
                && (this.getRegisterProvince() == null ? other.getRegisterProvince() == null : this.getRegisterProvince().equals(other.getRegisterProvince()))
                && (this.getRegisterCity() == null ? other.getRegisterCity() == null : this.getRegisterCity().equals(other.getRegisterCity()))
                && (this.getRegisterRegin() == null ? other.getRegisterRegin() == null : this.getRegisterRegin().equals(other.getRegisterRegin()))
                && (this.getRegisterAddress() == null ? other.getRegisterAddress() == null : this.getRegisterAddress().equals(other.getRegisterAddress()))
                && (this.getContactAddress() == null ? other.getContactAddress() == null : this.getContactAddress().equals(other.getContactAddress()))
                && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateOrgId() == null ? other.getCreateOrgId() == null : this.getCreateOrgId().equals(other.getCreateOrgId()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getUpdateOrgId() == null ? other.getUpdateOrgId() == null : this.getUpdateOrgId().equals(other.getUpdateOrgId()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()))
                && (this.getEntryTime() == null ? other.getEntryTime() == null : this.getEntryTime().equals(other.getEntryTime()))
                && (this.getEntryId() == null ? other.getEntryId() == null : this.getEntryId().equals(other.getEntryId()))
                && (this.getEntryName() == null ? other.getEntryName() == null : this.getEntryName().equals(other.getEntryName()))
                && (this.getGroupId() == null ? other.getGroupId() == null : this.getGroupId().equals(other.getGroupId()))
                && (this.getUsedName() == null ? other.getUsedName() == null : this.getUsedName().equals(other.getUsedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getCreditCode() == null) ? 0 : getCreditCode().hashCode());
        result = prime * result + ((getShortName() == null) ? 0 : getShortName().hashCode());
        result = prime * result + ((getTel() == null) ? 0 : getTel().hashCode());
        result = prime * result + ((getRespName() == null) ? 0 : getRespName().hashCode());
        result = prime * result + ((getRespTel() == null) ? 0 : getRespTel().hashCode());
        result = prime * result + ((getSupplierType() == null) ? 0 : getSupplierType().hashCode());
        result = prime * result + ((getSupplierStatus() == null) ? 0 : getSupplierStatus().hashCode());
        result = prime * result + ((getRegisterProvince() == null) ? 0 : getRegisterProvince().hashCode());
        result = prime * result + ((getRegisterCity() == null) ? 0 : getRegisterCity().hashCode());
        result = prime * result + ((getRegisterRegin() == null) ? 0 : getRegisterRegin().hashCode());
        result = prime * result + ((getRegisterAddress() == null) ? 0 : getRegisterAddress().hashCode());
        result = prime * result + ((getContactAddress() == null) ? 0 : getContactAddress().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateOrgId() == null) ? 0 : getCreateOrgId().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateOrgId() == null) ? 0 : getUpdateOrgId().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        result = prime * result + ((getEntryTime() == null) ? 0 : getEntryTime().hashCode());
        result = prime * result + ((getEntryId() == null) ? 0 : getEntryId().hashCode());
        result = prime * result + ((getEntryName() == null) ? 0 : getEntryName().hashCode());
        result = prime * result + ((getGroupId() == null) ? 0 : getGroupId().hashCode());
        result = prime * result + ((getUsedName() == null) ? 0 : getUsedName().hashCode());
        return result;
    }
}