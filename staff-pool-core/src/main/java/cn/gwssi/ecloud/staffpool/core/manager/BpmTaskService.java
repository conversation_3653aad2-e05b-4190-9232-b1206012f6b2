package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.BpmTask;
import cn.gwssi.ecloud.staffpool.dto.BpmTaskDto;
import cn.gwssi.ecloud.staffpool.dto.LabelDTO;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;
import java.util.Set;

public interface BpmTaskService extends Manager<String, BpmTask> {

    int deleteByPrimaryKey(String id);

    int insert(BpmTask record);

    int insertSelective(BpmTask record);

    BpmTask selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(BpmTask record);

    int updateByPrimaryKey(BpmTask record);

    int countBySelective(List<String> userRights, String userId);

    List<BpmTaskDto> selectTodoList(Set<String> userRights, String userId, String appTodoType,
                                    String intimeStart, String intimeEnd, String subject,String createStartTime,String createEndTime, String defIds,
                                    Integer pageNo, Integer pageSize, String sort,String orderBy);

    Integer selectTodoListTotal(Set<String> userRights, String userId, String appTodoType,
                                         String intimeStart, String intimeEnd, String subject, String defIds,
                                String createStartTime,String createEndTime);

    List<BpmTaskDto> selectInstList(Set<String> userRights, String userId, String appTodoType, String intimeStart,
                                    String intimeEnd, String subject, String defIds, Integer pageNo, Integer pageSize,
                                    String sort,String orderBy);

    Integer selectInstListTotal(Set<String> userRights, String userId, String appTodoType, String intimeStart, String intimeEnd, String subject, String defIds);

    List<LabelDTO> selectApprove();
}
