package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "人员账号申请列表响应类", description = "人员账号申请列表响应类")
@Data
public class UompAccountApplyDTO implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "申请人")
    private String applyPerson;
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "流程实例id")
    private String instId;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "任务关联id")
    private String taskLinkId;
}
