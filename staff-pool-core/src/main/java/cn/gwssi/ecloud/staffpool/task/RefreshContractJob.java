package cn.gwssi.ecloud.staffpool.task;

import cn.gwssi.ecloud.staffpool.core.dao.OrgUserMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractResource;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractNoticeService;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractResourceService;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.org.core.model.User;
import cn.gwssi.ecloudframework.sys.api.jms.model.DefaultJmsDTO;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.ecloudframework.sys.api.jms.producer.JmsProducer;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 刷新合同提醒定时任务
 *
 * <AUTHOR>
 */
@JobHandler(value = "RefreshContractJob")
@Component
public class RefreshContractJob extends IJobHandler {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UompContractManagementService uompContractManagementService;
    @Autowired
    private UompContractResourceService uompContractResourceService;
    @Autowired
    private UompContractNoticeService uompContractNoticeService;
    @Autowired
    private OrgUserMapper orgUserMapper;
    @Resource
    private JmsProducer producer;


    private static final String NOTICE_TITLE = "%s即将到期，请及时续签，涉及的资产如下：%s。";
    private static final String NOTICE_TITLE_NO = "%s即将到期，请及时续签。";

    /**
     * 执行定时任务，推送合同到期消息提醒
     * 1.在维保合同到期日前30天进行消息提醒,目前已到期的合同不做提醒。
     * 2.针对一个合同提醒过之后，若后续修改维保结束时间，达到到期前30天标准，则会再提醒一次；若后续未修改，则不会再提醒。
     * 3.推送人员范围：运维人员中角色包含甲方运维负责人或总包商运维经理（注：通过运维平台进行账号申请的人员）
     * @param s
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("------------------------------合同到期提醒定时任务开始执行--------------------------------------{}", s);
        // 查询到期的合同集合 正常的合同且维保结束时间小于等于30
        int day = SysPropertyUtil.getIntByAlias("contract_notice",30);
        List<UompContractManagement> list = uompContractManagementService.getContractNoticeList(String.valueOf(day));
        if (CollectionUtil.isNotEmpty(list)) {
            logger.info("合同到期提醒size：{}", list.size());
            // 初始化需要提醒的合同集合map
            Map<String, UompContractManagement> initContractMap = list.stream().collect(Collectors.toMap(UompContractManagement :: getId, it -> it));
            // 实际需要提醒的合同（若提醒表已提醒过，需要剔除）
            List<UompContractManagement> noticeList = list;
            // 需要添加的合同提醒数据
            List<UompContractNotice> addNoticeList = new ArrayList();
            // 需要更新的合同提醒数据
            List<UompContractNotice> updateNoticeList = new ArrayList();
            // 比较提醒表，1：是否有合同已经提醒 2：是否维保结束时间有更新
            List<String> cids = new ArrayList<>(initContractMap.keySet());
            List<UompContractNotice> noticeContractList = uompContractNoticeService.getListByContractIds(cids);
            if (CollectionUtil.isNotEmpty(noticeContractList)) {
                for (UompContractNotice uompContractNotice : noticeContractList) {
                    String contractId = uompContractNotice.getContractManagementId();
                    UompContractManagement uompContractManagement = initContractMap.get(contractId);
                    if (uompContractManagement.getQualityEndDay() != uompContractNotice.getQualityEndDay()) { // 合同修改维保结束时间，再提醒一次
                        // 更新合同维保时间以及提醒次数
                        uompContractNotice.setQualityBeginDay(uompContractManagement.getQualityBeginDay());
                        uompContractNotice.setQualityEndDay(uompContractManagement.getQualityEndDay());
                        uompContractNotice.setNoticeNum((uompContractNotice.getNoticeNum() + 1));
                        updateNoticeList.add(uompContractNotice);
                    } else { // 合同未修改，不提醒
                        noticeList.remove(uompContractManagement);
                    }
                }
                // 需要添加的合同提醒
                List<String> ids = noticeContractList.stream().map(it -> it.getContractManagementId()).collect(Collectors.toList());
                List<UompContractManagement> contractManagementList = list.stream().filter(it -> !ids.contains(it.getId())).collect(Collectors.toList());
                for (UompContractManagement contractManagement : contractManagementList) {
                    UompContractNotice ucn = new UompContractNotice();
                    ucn.setId(IdUtil.getSuid());
                    ucn.setContractManagementId(contractManagement.getId());
                    ucn.setQualityBeginDay(contractManagement.getQualityBeginDay());
                    ucn.setQualityEndDay(contractManagement.getQualityEndDay());
                    ucn.setNoticeNum(1);
                    ucn.setDelFlag("0");
                    addNoticeList.add(ucn);
                }
            } else {
                for (UompContractManagement ucm : list) {
                    UompContractNotice uompContractNotice = new UompContractNotice();
                    uompContractNotice.setId(IdUtil.getSuid());
                    uompContractNotice.setContractManagementId(ucm.getId());
                    uompContractNotice.setQualityBeginDay(ucm.getQualityBeginDay());
                    uompContractNotice.setQualityEndDay(ucm.getQualityEndDay());
                    uompContractNotice.setNoticeNum(1);
                    uompContractNotice.setDelFlag("0");
                    addNoticeList.add(uompContractNotice);
                }
            }
            // 获取需要提醒的合同以及资源集合，封装提醒内容
            for (UompContractManagement ucm : noticeList) {
                // 提醒标题
                String title = "";
                // 合同名称
                String name = ucm.getContractName();
                // 资源名称集合
                String resourceNames = "";
                // 查询资源信息
                List<UompContractResource> ucrList = uompContractResourceService.getResourceListByContractId(ucm.getId());
                if (CollectionUtils.isNotEmpty(ucrList)) {
                    List<String> nameList = ucrList.stream().map(it -> it.getCiName()).collect(Collectors.toList());
                    resourceNames = String.join("、", nameList);
                }
                title = StringUtils.isNotEmpty(resourceNames) ? String.format(NOTICE_TITLE, name, resourceNames) : String.format(NOTICE_TITLE_NO, name);
                // 封装数据，发送提醒信息
                sendMessage(title, ucm.getId());
            }
            // 添加、更新合同提醒表
            for (UompContractNotice ucn : addNoticeList) {
                try {
                    uompContractNoticeService.insert(ucn);
                } catch (Exception ex) {
                    // 不做处理
                }
            }
            for (UompContractNotice ucn : updateNoticeList) {
                try {
                    uompContractNoticeService.updateByPrimaryKey(ucn);
                } catch (Exception ex) {
                    // 不做处理
                }
            }
        }
        logger.info("------------------------------合同到期提醒定时任务执行结束--------------------------------------{}", s);
        return new ReturnT<>("执行成功");
    }


    private void sendMessage(String content, String id){
        try {
            //接收人  角色是甲方运维负责人、总包商运维经理 的接收人
            List<User> userList = orgUserMapper.getUserList();
            if (CollectionUtils.isNotEmpty(userList)) {
                //发送留言消息
                // 默认发送人
                UserDTO userDTO = new UserDTO();
                userDTO.setFullname("系统发送");
                userDTO.setId("1");
                List<SysIdentity> receivers=new ArrayList<>(0);
                for (User user : userList) {
                    DefaultIdentity defaultIdentity=new DefaultIdentity(user);
                    receivers.add(defaultIdentity);
                }
                NotifyMessage notifyMessage =new NotifyMessage(content,null,userDTO,receivers);
                //消息概要
                Map<String,Object> map=new HashMap<>(0);
                map.put("title",content);
                // 跳转合同详情id
                map.put("detailId", id);
                // 服务合同管理菜单code
                map.put("requestMethod", "gw-page-newgwuomp_contract_manage");
                // todo 调试阶段，后续修改head
                map.put("head","合同");
                map.put("type","通知");
                map.put("content", content);
                notifyMessage.setExtendVars(map);
                notifyMessage.setTag("维保合同通知");
                DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
                producer.sendToQueue(jmsDTO);
                logger.info("排队队列发送完毕");
            }
        }catch (Exception e){
            logger.error("排队队列发送失败",e);
        }
    }
}