package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "uomp_person_all_info_temp")
@Data
public class UompPersonAllInfoTemp extends BaseModel {
    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    private String personId;

    /**
     * 人员全部信息
     */
    @ApiModelProperty(value = "人员全部信息")
    private String personInfo;

    /**
     * 教育背景信息
     */
    @ApiModelProperty(value = "教育背景信息")
    private String educationalInfo;

    /**
     * 工作背景信息
     */
    @ApiModelProperty(value = "工作背景信息")
    private String jobInfo;

    /**
     * 技术资质信息
     */
    @ApiModelProperty(value = "技术资质信息")
    private String techInfo;

    /**
     * 社会关系信息
     */
    @ApiModelProperty(value = "社会关系信息")
    private String socialInfo;

    /**
     * 无犯罪记录信息
     */
    @ApiModelProperty(value = "无犯罪记录信息")
    private String noCrimeInfo;

    /**
     * 出国证件信息
     */
    @ApiModelProperty(value = "出国证件信息")
    private String abroadInfo;

    /**
     * 出入境记录信息
     */
    @ApiModelProperty(value = "出入境记录信息")
    private String enrtyExitInfo;

    /**
     * 逻辑删除标记 0-有效 1-无效
     */
    @ApiModelProperty(value = "逻辑删除标记 0-有效 1-无效")
    private String delFlag;
}