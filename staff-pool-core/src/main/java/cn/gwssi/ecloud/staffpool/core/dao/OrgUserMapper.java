package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.OrgUser;
import cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgUserBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionPersonListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.org.core.model.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrgUserMapper extends BaseDao<String, OrgUser> {

    String selectOrgUserByAccount(@Param("accountNum")String accountNum);

    int updateStatusById(@Param("id")String id);

    List<UompPermissionPersonListDTO> selectUserStatus(QueryFilter queryFilter);

    OrgGroupBaseDTO selectListByGroupId(@Param("groupId")String groupId);

    List<OrgGroupBaseDTO> selectAllByStatus();

    int updateStatusById1(@Param("id")String id, @Param("status")int status);

    String selectFullNameById(@Param("id")String id);

    String selectUserInfoByAccount(@Param("account")String account);

    List<OrgUserBaseDTO> selectUserInfoByIds(@Param("idList")List<String> idList);

    String selectAllBypersonCard(String personCard);

    Integer countByAccount(String account);

    String selectIdByPersonCard(String personCard);

    void updateStatus(QueryFilter updateFilter);

    List<User> getUserList();
}
