package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="根据id查询入场申请详情响应类")
@Data
public class ExitApplyDTO implements Serializable {

    @ApiModelProperty(value="退场申请id")
    private String id;
    @ApiModelProperty(value="退场申请编号")
    private String outApplyCode;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="联系电话")
    private String tel;
    @ApiModelProperty(value="在岗年限")
    private String onlineTime;
    @ApiModelProperty(value="就职方向")
    private String postName;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="就职公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="计划退出时间")
    private String planOutTime;
    @ApiModelProperty(value="退出原因")
    private String outReason;
    @ApiModelProperty(value="是否删除账号")
    private String isDelete;
    @ApiModelProperty(value="实际退场时间")
    private String outTime;
    @ApiModelProperty(value="流程实例id")
    private String instId;
    @ApiModelProperty(value="申请人")
    private String createBy;

    private String outReasonFileJson;
    @ApiModelProperty(value="退场申请文件")
    private List<FileInfoBean> outReasonFile;

    @ApiModelProperty(value="退场接收人信息")
    private List<AcceptListBean> acceptList;
}
