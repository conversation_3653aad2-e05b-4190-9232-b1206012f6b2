package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlan;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecordPerson;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingPlanPersonService;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingPlanService;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingRecordPersonService;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingRecordService;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompTrainingPlanDto;
import cn.gwssi.ecloud.staffpool.dto.UompTrainingRecordDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 问卷管理
 */
@Api(description = "问卷管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/traing")
public class UompTrainingController extends BaseController<UompTrainingPlan> {

    @Resource
    private UompTrainingPlanService trainingPlanService;
    @Resource
    private UompTrainingPlanPersonService trainingPlanPersonService;
    @Resource
    private UompTrainingRecordService trainingRecordService;
    @Resource
    private UompTrainingRecordPersonService trainingRecordPersonService;
    @Autowired
    private SysDataDictMapper sysDataDictMapper;

    @Override
    protected String getModelDesc() {
        return "问卷管理";
    }

    @ApiOperation(value = "培训计划-条件查询接口")
    @RequestMapping(value = "/getTrainingPlanlist")
    public PageResult<UompTrainingPlan> getTrainingPlanlist(HttpServletRequest request, HttpServletResponse response,
                                                            @RequestParam(value = "trainingTheme", required = false) @ApiParam(value = "培训主题") String trainingTheme,
                                                            @RequestParam(value = "trainingResource", required = false) @ApiParam(value = "培训资源") String trainingResource,
                                                            @RequestParam(value = "trainingObject", required = false) @ApiParam(value = "培训对象") String trainingObject,
                                                            @RequestParam(value = "entryDate", required = false) @ApiParam(value = "计划培训时间") String entryDate,
                                                            @RequestParam(value = "status", required = false) @ApiParam(value = "培训状态 : 0 待开始  1 进行中   2 已结束") String status,
                                                            @RequestParam(value = "trainingMode", required = false) @ApiParam(value = "线上培训") String trainingMode,
                                                            @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                            @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {

        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("training_theme", trainingTheme, QueryOP.IN);
        queryFilter.addFilter("training_resource", trainingResource, QueryOP.IN);
        queryFilter.addFilter("training_object", trainingObject, QueryOP.IN);
        queryFilter.addFilter("DEL_FLAG", 0, QueryOP.EQUAL);
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("TRAINING_BEGIN_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("TRAINING_END_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        queryFilter.addFieldSort("create_time", "DESC");
        // todo 线下培训逻辑
        return new PageResult<>(trainingPlanService.query(queryFilter));
    }

    @ApiOperation(value = "培训计划-根据计划ID详情查询")
    @RequestMapping(value = "/getPlanInfo")
    public ResultMsg<UompTrainingPlanDto> getPlanInfo(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam(value = "id") @ApiParam(value = "id") String id) {

        UompTrainingPlan plan = trainingPlanService.get(id);

        QueryFilter queryFilter1 = new DefaultQueryFilter(true);
        queryFilter1.addFilter("TRAINING_PLAN_ID", id, QueryOP.EQUAL);
        List<UompTrainingPlanPerson> persons = trainingPlanPersonService.query(queryFilter1);
        UompTrainingPlanDto uompTrainingPlanDto = new UompTrainingPlanDto();
        BeanUtils.copyProperties(plan, uompTrainingPlanDto);
        uompTrainingPlanDto.setTrainingPersons(persons);
        return getSuccessResult(uompTrainingPlanDto);
    }

    @ApiOperation(value = "培训计划-根据计划ID删除")
    @RequestMapping(value = "/deleteById")
    public ResultMsg<String> deleteById(HttpServletRequest request, HttpServletResponse response,
                                        @RequestParam(value = "id") @ApiParam(value = "id") String id) {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("b.TRAINING_PLAN_ID", id, QueryOP.EQUAL);
        List<UompTrainingRecord> records = trainingRecordService.query(queryFilter);
        if (records != null && records.size() > 0) {
            return getWarnResult("该计划已被记录关联，不可删除！");

        }
        UompTrainingPlan plan = trainingPlanService.get(id);
        if (plan != null && "1".equals(plan.getTrainingMode()) && plan.getTrainingBeginTime().before(new Date())) {
            return getWarnResult("该计划是线上培训，且已经开始，不可删除！");
        }
        // todo 是否被记录使用
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        plan.setDelFlag("1");
        plan.setUpdateBy(user.getUserId());
        plan.setUpdateTime(new Date());
        trainingPlanService.updateByPrimaryKeySelective(plan);
        trainingPlanPersonService.deleteByPlanId(id);
        return getSuccessResult();
    }

    @ApiOperation(value = "培训计划-编辑")
    @RequestMapping(value = "/edit")
    public ResultMsg<String> edit(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody UompTrainingPlanDto dto) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        if (StringUtils.isEmpty(dto.getId())) {
            dto.setDelFlag("0");
            trainingPlanService.create(dto);
            trainingPlanPersonService.deleteByPlanId(dto.getId());
        } else {
            dto.setUpdateBy(user.getUserId());
            dto.setUpdateTime(new Date());
            trainingPlanService.updateByPrimaryKeySelective(dto);
        }
        if (dto.getTrainingPersons() != null && dto.getTrainingPersons().size() > 0) {
            for (UompTrainingPlanPerson person : dto.getTrainingPersons()) {
                person.setTrainingPlanId(dto.getId());
                trainingPlanPersonService.create((person));
            }
            // todo 给入会人员发送通知
        }
        return getSuccessResult();
    }

    @ApiOperation(value = "培训计划-导出")
    @RequestMapping(value = "/export")
    public HttpServletResponse export(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam(value = "trainingTheme", required = false) @ApiParam(value = "培训主题") String trainingTheme,
                                      @RequestParam(value = "trainingResource", required = false) @ApiParam(value = "培训资源") String trainingResource,
                                      @RequestParam(value = "trainingObject", required = false) @ApiParam(value = "培训对象") String trainingObject,
                                      @RequestParam(value = "entryDate", required = false) @ApiParam(value = "计划培训时间") String entryDate,
                                      @RequestParam(value = "status", required = false) @ApiParam(value = "培训状态 : 0 待开始  1 进行中   2 已结束") String status,
                                      @RequestParam(value = "trainingMode", required = false) @ApiParam(value = "线上培训") String trainingMode) throws IOException {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("training_theme", trainingTheme, QueryOP.IN);
        queryFilter.addFilter("training_resource", trainingResource, QueryOP.IN);
        queryFilter.addFilter("training_object", trainingObject, QueryOP.IN);
        queryFilter.addFilter("DEL_FLAG", 0, QueryOP.EQUAL);
        if (!StringUtils.isEmpty(entryDate)) {
            String[] entryDates = entryDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("TRAINING_BEGIN_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("TRAINING_END_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        queryFilter.addFieldSort("create_time", "DESC");
        List<UompTrainingPlan> plans = trainingPlanService.query(queryFilter);

        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("培训计划");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号", "培训计划名称", "培训主题", "培训对象", "计划培训开始时间", "计划培训结束时间", "培训资源", "考核方式"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }
        //培训主题
        List<BaseDTO> tecList = sysDataDictMapper.selectSubListByUompEducation("UOMP_TRAINING_OBJ");
        Map<String, String> theme_hashMap = new HashMap<>();
        for (BaseDTO baseDTO : tecList) {
            theme_hashMap.put(baseDTO.getId(), baseDTO.getName());
        }
        //培训资源
        List<BaseDTO> baseDTOList = sysDataDictMapper.selectSubListByUompEducation("UOMP_TRAINING_RESOURCES");
        Map<String, String> resource_hashMap = new HashMap<>();
        for (BaseDTO baseDTO : baseDTOList) {
            resource_hashMap.put(baseDTO.getId(), baseDTO.getName());
        }
        //培训对象
        List<BaseDTO> dtos = sysDataDictMapper.selectSubListByUompEducation("UOMP_TRAINING_TOPICS");
        Map<String, String> object_hashMap = new HashMap<>();
        for (BaseDTO baseDTO : dtos) {
            object_hashMap.put(baseDTO.getId(), baseDTO.getName());
        }
        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        for (UompTrainingPlan it : plans) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getTrainingPlanName() == null ? "" : it.getTrainingPlanName());//培训计划名称
            row1.createCell(2).setCellValue(theme_hashMap.get(it.getTrainingTheme() == null ? "" : it.getTrainingTheme()));//培训主题
            //培训对象处理
            List<String> training_object_str_list = new ArrayList<>();
            if (!StringUtils.isEmpty(it.getTrainingObject())) {
                List<String> training_object_list = Arrays.asList(it.getTrainingObject().split(","));
                for (String object_key : training_object_list) {
                    training_object_str_list.add(object_hashMap.get(object_key));
                }
            }
            row1.createCell(3).setCellValue(StringUtils.join(training_object_str_list, ","));//培训对象
            row1.createCell(4).setCellValue(it.getTrainingBeginTime() == null ? "" : dateFormat.format(it.getTrainingBeginTime()));//计划培训开始时间
            row1.createCell(5).setCellValue(it.getTrainingEndTime() == null ? "" : dateFormat.format(it.getTrainingEndTime()));//计划培训结束时间
            row1.createCell(6).setCellValue(resource_hashMap.get(it.getTrainingResource() == null ? "" : it.getTrainingResource()));//培训资源
            row1.createCell(7).setCellValue(it.getEvaluationMode() == null ? "" : it.getEvaluationMode());//考核方式
            rowNum++;
        }

        OutputStream os = null;
        try {
            //把文件送到用户端
            String downName = "培训计划.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }

    @ApiOperation(value = "培训记录查询列表接口")
    @RequestMapping(value = "/queryTrainingRecordList")
    public PageResult<UompTrainingRecord> queryTrainingRecordList(HttpServletRequest request, HttpServletResponse response,
                                                                  @RequestParam(value = "trainingName", required = false) @ApiParam(value = "培训名称") String trainingName,
                                                                  @RequestParam(value = "trainingDate", required = false) @ApiParam(value = "培训时间") String trainingDate,
                                                                  @RequestParam(value = "trainingSite", required = false) @ApiParam(value = "线上培训") String trainingSite,
                                                                  @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                  @RequestParam("pageSize") @ApiParam(value = "每页数量") Integer pageSize) {

        QueryFilter queryFilter = getQueryFilter();
        queryFilter.addFilter("b.TRAINING_NAME", trainingName, QueryOP.LIKE);
        queryFilter.addFilter("b.TRAINING_SITE", trainingSite, QueryOP.LIKE);
        if (!StringUtils.isEmpty(trainingDate)) {
            String[] entryDates = trainingDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("TRAINING_BEGIN_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("TRAINING_END_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        queryFilter.addFieldSort("b.create_time", "DESC");
        // todo 线下培训逻辑
        return new PageResult<>(trainingRecordService.query(queryFilter));
    }

    @ApiOperation(value = "培训计划-根据计划ID详情查询")
    @RequestMapping(value = "/queryTrainingRecordById")
    public ResultMsg<UompTrainingRecordDto> queryTrainingRecordById(HttpServletRequest request, HttpServletResponse response,
                                                                    @RequestParam(value = "id") @ApiParam(value = "id") String id) {

        UompTrainingRecord record = trainingRecordService.get(id);
        QueryFilter queryFilter1 = new DefaultQueryFilter(true);
        queryFilter1.addFilter("TRAINING_RECORD_ID", id, QueryOP.EQUAL);
        List<UompTrainingRecordPerson> persons = trainingRecordPersonService.query(queryFilter1);
        UompTrainingRecordDto uompTrainingRecordDto = new UompTrainingRecordDto();
        BeanUtils.copyProperties(record, uompTrainingRecordDto);
        uompTrainingRecordDto.setTrainingPersons(persons);
        return getSuccessResult(uompTrainingRecordDto);
    }

    @ApiOperation(value = "培训记录删除接口")
    @RequestMapping(value = "/deleteTrainingRecordById")
    public ResultMsg<String> deleteTrainingRecordById(HttpServletRequest request, HttpServletResponse response,
                                                      @RequestParam(value = "id") @ApiParam(value = "id") String id) {
        trainingRecordService.remove(id);
        trainingRecordPersonService.deleteByRecordId(id);
        return getSuccessResult();
    }

    @ApiOperation(value = "培训记录保存接口")
    @RequestMapping(value = "/saveTrainingRecord")
    public ResultMsg<String> saveTrainingRecord(HttpServletRequest request, HttpServletResponse response,
                                                @RequestBody UompTrainingRecordDto dto) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        if (StringUtils.isEmpty(dto.getId())) {
            dto.setDelFlag("0");
            trainingRecordService.create(dto);
            trainingRecordPersonService.deleteByRecordId(dto.getId());
        } else {
            dto.setUpdateBy(user.getUserId());
            dto.setUpdateTime(new Date());
            trainingRecordService.updateByPrimaryKeySelective(dto);
        }
        if (dto.getTrainingPersons() != null && dto.getTrainingPersons().size() > 0) {
            for (UompTrainingRecordPerson person : dto.getTrainingPersons()) {
                person.setTrainingRecordId(dto.getId());
                trainingRecordPersonService.create((person));
            }
        }
        return getSuccessResult();
    }

    @ApiOperation(value = "培训计划-导出")
    @RequestMapping(value = "/exportTrainingRecordList")
    public HttpServletResponse exportTrainingRecordList(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "trainingName", required = false) @ApiParam(value = "培训名称") String trainingName,
                                                        @RequestParam(value = "trainingDate", required = false) @ApiParam(value = "培训时间") String trainingDate,
                                                        @RequestParam(value = "trainingSite", required = false) @ApiParam(value = "线上培训") String trainingSite) throws IOException {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("b.TRAINING_NAME", trainingName, QueryOP.LIKE);
        queryFilter.addFilter("b.TRAINING_SITE", trainingSite, QueryOP.LIKE);
        if (!StringUtils.isEmpty(trainingDate)) {
            String[] entryDates = trainingDate.split(",");
            if (entryDates.length > 1) {
                queryFilter.addFilter("TRAINING_BEGIN_TIME", entryDates[0], QueryOP.GREAT_EQUAL);
                queryFilter.addFilter("TRAINING_END_TIME", entryDates[1], QueryOP.LESS_EQUAL);
            }
        }
        queryFilter.addFieldSort("b.create_time", "DESC");
        List<UompTrainingRecord> records = trainingRecordService.query(queryFilter);

        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("培训实施记录");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号","培训名称","培训讲师","培训开始时间","培训结束时间","培训时长","培训地点","关联培训计划","签到人数"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }
        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        for (UompTrainingRecord it : records) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getTrainingName() == null ? "" : it.getTrainingName());//培训名称
            row1.createCell(2).setCellValue(it.getTrainingTeacher() == null ? "" : it.getTrainingTeacher());//培训讲师
            row1.createCell(3).setCellValue(it.getTrainingBeginTime() == null ? "" : dateFormat.format(it.getTrainingBeginTime()));//计划培训开始时间
            row1.createCell(4).setCellValue(it.getTrainingEndTime() == null ? "" : dateFormat.format(it.getTrainingEndTime()));//计划培训结束时间
            row1.createCell(5).setCellValue(it.getTrainingDuration() == null ? "" : it.getTrainingDuration());//培训讲师
            row1.createCell(6).setCellValue(it.getTrainingSite() == null ? "" : it.getTrainingSite());//培训讲师
            row1.createCell(7).setCellValue(it.getTrainingPlanName() == null ? "" : it.getTrainingPlanName());//培训讲师
            row1.createCell(8).setCellValue(it.getSignInNum() == null ? "" : it.getSignInNum());//考核方式
            rowNum++;
        }

        OutputStream os = null;
        try {
            //把文件送到用户端
            String downName = "培训实施记录.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }
}
