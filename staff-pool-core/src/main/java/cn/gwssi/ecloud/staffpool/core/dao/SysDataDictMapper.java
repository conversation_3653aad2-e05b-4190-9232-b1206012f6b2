package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.SysDataDict;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.FilePatentDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SysDataDictMapper extends BaseDao<String, SysDataDict> {
    SysDataDict selectSupplier();

    List<FilePatentDto> selectPatent();

    List<BaseDTO> selectSubListByDictKey(String dictKey);

    String selectNameByPostKey(String postKey);

    List<BaseDTO> selectSubListByUompEducation(String dictKey);

    BaseDTO getKeyByName(@Param("dictKey") String dictKey, @Param("keyList") String[] keyList);

    String selectKeyByDictkeyAndName(@Param("dictKey") String dictKey, @Param("name") String name);

    String selectKeyByDictkeyAndKey(@Param("dictKey") String dictKey, @Param("key") String key);

    String selectKeyByDictkeyAndNameAndParentId(@Param("dictKey") String dictKey, @Param("name") String name, @Param("id") String id);

    String selectParentIdByDictKeyAndKey(@Param("dictKey") String dictKey, @Param("key") String key);

    String selectNameByDictKeyAndKey(@Param("dictKey") String dictKey, @Param("key") String key);

    Map<String, String> selectKeyAndParentIdById(String id);

    List<SysDataDict> selectAllByDictKeys(String dictKey);
}