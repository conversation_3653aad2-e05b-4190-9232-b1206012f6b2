package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.dao.CmdbTableBaseConfigMapper;
import cn.gwssi.ecloud.staffpool.core.entity.CmdbTableBaseConfig;
import cn.gwssi.ecloud.staffpool.core.manager.CmdbTableBaseConfigService;
@Service
public class CmdbTableBaseConfigServiceImpl extends BaseManager<String, CmdbTableBaseConfig> implements CmdbTableBaseConfigService{

    @Resource
    private CmdbTableBaseConfigMapper cmdbTableBaseConfigMapper;

    @Override
    public int insertSelective(CmdbTableBaseConfig record) {
        return cmdbTableBaseConfigMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(CmdbTableBaseConfig record) {
        return cmdbTableBaseConfigMapper.updateByPrimaryKey(record);
    }

}
