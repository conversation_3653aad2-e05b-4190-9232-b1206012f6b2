package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.BpmTask;
import cn.gwssi.ecloud.staffpool.dto.BpmTaskDto;
import cn.gwssi.ecloud.staffpool.dto.LabelDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface BpmTaskMapper extends BaseDao<String, BpmTask> {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BpmTask record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BpmTask record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    BpmTask selectByPrimaryKey(String id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BpmTask record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BpmTask record);

    int countBySelective(@Param("userRights") List<String> userRights, @Param("userId") String userId);

    List<BpmTaskDto> selectTodoList(@Param("userRights") Set<String> userRights, @Param("userId") String userId, @Param("appTodoType") String appTodoType,
                                    @Param("intimeStart") String intimeStart, @Param("intimeEnd") String intimeEnd, @Param("subject") String subject, @Param("defIds") String[] defIds,
                                    @Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize,
                                    @Param("createStartTime") String createStartTime,
                                    @Param("createEndTime") String createEndTime,
                                    @Param("sort")String sort,
                                    @Param("orderBy")String orderBy);

    List<BpmTaskDto> selectInstList(@Param("userRights") Set<String> userRights, @Param("userId") String userId, @Param("appTodoType") String appTodoType,
                                    @Param("intimeStart") String intimeStart, @Param("intimeEnd") String intimeEnd, @Param("subject") String subject, @Param("defIds") String[] defIds,
                                    @Param("pageNo") Integer pageNo, @Param("pageSize") Integer pageSize,
                                    @Param("sort")String sort,@Param("orderBy")String orderBy);

    Integer selectInstListTotal(@Param("userRights") Set<String> userRights, @Param("userId") String userId, @Param("appTodoType") String appTodoType,
                                    @Param("intimeStart") String intimeStart, @Param("intimeEnd") String intimeEnd, @Param("subject") String subject, @Param("defIds") String[] defIds);

    List<LabelDTO> selectApprove();

    Integer selectTodoListTotal(@Param("userRights") Set<String> userRights, @Param("userId") String userId, @Param("appTodoType") String appTodoType,
                                         @Param("intimeStart") String intimeStart, @Param("intimeEnd") String intimeEnd,
                                @Param("subject") String subject, @Param("defIds") String[] defIds,
                                @Param("createStartTime") String createStartTime,
                                @Param("createEndTime") String createEndTime);
}