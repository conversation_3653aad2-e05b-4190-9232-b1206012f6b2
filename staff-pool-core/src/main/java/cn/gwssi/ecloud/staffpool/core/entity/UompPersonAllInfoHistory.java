package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

public class UompPersonAllInfoHistory extends BaseModel {

    private String personId;

    private String instId;

    private String personInfo;


    public UompPersonAllInfoHistory() {
    }

    public UompPersonAllInfoHistory(String personId, String instId, String personInfo) {
        this.personId = personId;
        this.instId = instId;
        this.personInfo = personInfo;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getPersonInfo() {
        return personInfo;
    }

    public void setPersonInfo(String personInfo) {
        this.personInfo = personInfo;
    }
}
