package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompFortressAssets;
import cn.gwssi.ecloud.staffpool.dto.UompFortessResourceDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

public interface UompFortressAssetsMapper extends BaseDao<String, UompFortressAssets> {

    int insertSelective(UompFortressAssets record);

    UompFortessResourceDTO selectInfoByFortAssetId(@Param("fortAssetId")String fortAssetId);
}