package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "背调tpo5")
public class ColumnarDTO {

    @ApiModelProperty(value="合格top5")
    private List<ColumnarBaseDTO> passList = new ArrayList<>();
    @ApiModelProperty(value="不合格集合")
    private List<PersonBaseDTO> noList = new ArrayList<>();

}