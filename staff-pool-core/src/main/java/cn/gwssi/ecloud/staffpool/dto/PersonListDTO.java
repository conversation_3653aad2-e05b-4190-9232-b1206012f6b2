package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="现场管理驻场管理-驻场人员列表响应类")
@Data
public class PersonListDTO implements Serializable {

    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="人员id")
    private String personId;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    @Sensitive(actualLen = 18)
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="就职公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="所属运维组")
    private String maintenanceGroupName;
    @ApiModelProperty(value="进驻时期")
    private String inTime;
    @ApiModelProperty(value="退场申请状态 0-无申请 1-有申请")
    private String outApplyStatus;
    @ApiModelProperty(value="驻场状态 0-驻场中 1-已退场")
    private String entryStatus;
    @ApiModelProperty(value="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过")
    private String applyStatus;
    @ApiModelProperty(value="退出日期")
    private String outTime;
    @ApiModelProperty(value="工作岗位")
    private String postName;
    @ApiModelProperty(value="参与系统")
    private String engagementProject;
    @ApiModelProperty(value="参与系统json")
    private String engagementProjectJson;
    @ApiModelProperty(value="预计到访时间")
    private String planVisitTime;
    @ApiModelProperty(value="计划退出时间")
    private String planOutTime;
    @ApiModelProperty(value="角色")
    private String personRole;
    @ApiModelProperty(value="角色 id")
    private String personRoleId;
    @ApiModelProperty(value="角色 json")
    private String personRoleJson;
    @ApiModelProperty(value="主管部门")
    private String departName;
    @ApiModelProperty(value="主管部门 id")
    private String departId;
    @ApiModelProperty(value="主管部门 json")
    private String departJson;
    @ApiModelProperty(value="岗位类型")
    private String positionType;
    @ApiModelProperty(value="涉密岗位性质")
    private String classifiedPosition;
    @ApiModelProperty(value="申请岗位 JSON")
    private String postJson;
    @ApiModelProperty(value = "申请岗位id")
    private String postId;
    @ApiModelProperty(value="流程实例id")
    private String instId;
    @ApiModelProperty(value="入场申请id")
    private String applyId;
    @ApiModelProperty(value="技术方向")
    private String technicalDirection;
    @ApiModelProperty(value="驻场服务地点")
    private String serviceLocation;
    @ApiModelProperty(value="学历")
    private String education;
    @ApiModelProperty(value = "性别 0: 男,  1:女")
    private String sex;
}
