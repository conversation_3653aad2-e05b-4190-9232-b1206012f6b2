package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractResource;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UompContractResourceMapper extends BaseDao<String, UompContractResource> {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(UompContractResource record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompContractResource record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    UompContractResource selectByPrimaryKey(String id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompContractResource record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UompContractResource record);

    List<UompContractResource> getResourceListByContractId(@Param("contractId") String contractId);

    void deleteByContractId(@Param("contractId")String contractId);

    void deleteByIdList(@Param("idList")List<String> idList);

    List<String> selectListByCmIdAndCiId(@Param("paramList") List<Map<String, String>> paramList);

    void insertBatch(@Param("infoList") List<UompContractResource> infoList);

    void updateBatchByCmIdAndCiId(@Param("infoList") List<UompContractResource> infoList);
}