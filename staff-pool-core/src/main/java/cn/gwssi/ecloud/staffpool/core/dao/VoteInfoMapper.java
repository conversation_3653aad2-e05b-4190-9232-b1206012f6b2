package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.VoteInfo;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface VoteInfoMapper extends BaseDao<String, VoteInfo> {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(VoteInfo record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(VoteInfo record);

    List<VoteUserAnswerDto> selectVoteInfoByUserId(@Param("userId") String userId);
    List<VoteUserAnswerDto> selectVoteInfo(QueryFilter var1);
}