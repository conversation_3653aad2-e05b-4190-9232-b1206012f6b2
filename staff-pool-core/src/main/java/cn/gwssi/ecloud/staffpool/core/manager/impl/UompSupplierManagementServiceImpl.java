package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.*;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierManagementService;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.IntervalDay;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
public class UompSupplierManagementServiceImpl extends BaseManager<String, UompSupplierManagement> implements UompSupplierManagementService {

    @Autowired
    private UompSupplierManagementMapper uompSupplierManagementMapper;
    @Autowired
    private UompSupplierPersonnelMapper uompSupplierPersonnelMapper;
    @Autowired
    private UompSupplierFileMapper uompSupplierFileMapper;
    @Autowired
    private OrgGroupMapper orgGroupMapper;
    @Autowired
    private SysDataDictMapper dataDictMapper;
    @Resource
    private UompPersonInfoMapper uompPersonInfoMapper;
    @Resource
    private UompAdmissionPersonMapper uompAdmissionPersonMapper;
    @Resource
    private UompContractManagementMapper uompContractManagementMapper;
    @Resource
    private IntervalDay intervalDay;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompApplicationSystemManagementMapper uompApplicationSystemManagementMapper;
    @Resource
    private UompSupplierManagementService uompSupplierManagementService;

    public int insertSelective(UompSupplierManagement record) {
        UompSupplierManagement temp = uompSupplierManagementMapper.selectIdByCreditCode(record.getCreditCode());
        if (temp != null && !TextUtils.isEmpty(temp.getId())) {
            throw new BusinessException("该信用代码已存在，不可重复，请仔细检查！");
        }
        int value = uompSupplierManagementMapper.insertSelective(record);
        SysDataDict sysDataDict = dataDictMapper.selectSupplier();
        if (sysDataDict != null) {
            OrgGroup group = orgGroupMapper.selectSn();
            // todo: 引入 GroupCustom
            UompSupplierManagement management = new UompSupplierManagement();
            management.setId(record.getId());
//            GroupCustom
//            management.setGroupId();
//            uompSupplierManagementMapper.updateById(management);
        }
        return value;
    }

    @Override
    public int updateById(UompSupplierManagement record) {
        UompSupplierManagement old = uompSupplierManagementMapper.get(record.getId());
        if (old != null && !old.getCreditCode().equals(record.getCreditCode())) {
            UompSupplierManagement temp = uompSupplierManagementMapper.selectIdByCreditCode(record.getCreditCode());
            if (temp != null && !TextUtils.isEmpty(temp.getId())) {
                throw new BusinessException("该信用代码已存在，不可重复，请仔细检查！");
            }
        }
        if (old != null && !old.getSupplierName().equals(record.getSupplierName())) {
            UompSupplierManagement temp = uompSupplierManagementMapper.selectIdBySupplierName(record.getSupplierName());
            if (temp != null && !TextUtils.isEmpty(temp.getId())) {
                throw new BusinessException("该供应商名称已存在，不可重复，请仔细检查！");
            }
        }
        int value = uompSupplierManagementMapper.updateById(record);
        OrgGroup orgGroup = new OrgGroup();
        orgGroup.setId(old.getGroupId());
        orgGroup.setName(record.getSupplierName());
        orgGroup.setCode(record.getCreditCode());
        orgGroupMapper.updateById(orgGroup);
        return value;
    }

    @Override
    public ResponsePageData<UompSupplierManagement> getList(String supplierName, String shortName, String supplierStatus, String respName, String entryTimeBegin, String entryTimeEnd, Map<String, String> sortMap, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(
                pageNo != null ? pageNo : 1,
                pageSize != null ? pageSize : 10);
        StringBuilder orderbySQL = new StringBuilder();
        if (sortMap != null) {
            for (String key : sortMap.keySet()) {
                if (sortMap.get(key) != null && !Objects.equals(sortMap.get(key), "")) {
                    orderbySQL.append(key).append(' ').append(sortMap.get(key).replace("ending", "")).append(',');
                }
            }

            if (!"".equals(orderbySQL.toString())) {
                orderbySQL = new StringBuilder(" order by " + orderbySQL.substring(0, orderbySQL.length() - 1));
            }
        } else {
            orderbySQL = new StringBuilder(" order by CREATE_TIME desc");
        }
        String[] strings = null;
        if (!TextUtils.isEmpty(supplierStatus)) {
            strings = supplierStatus.split(",");
        }
//        List<UompSupplierManagement> supplierManagements = uompSupplierManagementMapper.getList(supplierName, shortName, strings, respName, entryTimeBegin, entryTimeEnd, orderbySQL.toString());
//        PageInfo<UompSupplierManagement> p = new PageInfo<>(supplierManagements);
        ResponsePageData<UompSupplierManagement> responsePageData = new ResponsePageData<>();
//        responsePageData.setCode(200);
//        responsePageData.setMsg("success");
//        responsePageData.setTotal(p.getTotal());
//        responsePageData.setRows(supplierManagements);

        return responsePageData;
    }

    @Override
    public int deleteById(String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierFile file = new UompSupplierFile();
        file.setId(id);
        file.setDelFlag("1");
        file.setUpdateOrgId(user.getOrgId());
        file.setUpdateTime(new Date());
        file.setUpdateBy(user.getUserId());
        uompSupplierFileMapper.updateById(file);
        UompSupplierPersonnel personnel = new UompSupplierPersonnel();
        personnel.setId(id);
        personnel.setDelFlag("1");
        personnel.setUpdateOrgId(user.getOrgId());
        personnel.setUpdateTime(new Date());
        personnel.setUpdateBy(user.getUserId());
        uompSupplierPersonnelMapper.updateById(personnel);
        UompSupplierManagement management = new UompSupplierManagement();
        management.setId(id);
        management.setDelFlag("1");
        management.setUpdateOrgId(user.getOrgId());
        management.setUpdateTime(new Date());
        management.setUpdateBy(user.getUserId());

        //todo 删除供应商的时候将机构也一同删除（待定）（ps：可能会有影响，比如机构下有层级，删除的时候怎么办？之前全删？）
        return uompSupplierManagementMapper.updateById(management);
    }

    @Override
    public String selectSupplierIdByGroupId(String orgId) {
        return uompSupplierManagementMapper.selectSupplierIdByGroupId(orgId);
    }

    // 查询供应商合作中和已停用的供应商信息
    @Override
    public List<UompSupplierManagement> getSupplierCooperationList() {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        // 未删除的
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 供应商状态 1合作中 2已停用
        queryFilter.addFilter("SUPPLIER_STATUS", "1,2", QueryOP.IN);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompSupplierManagementMapper.query(queryFilter);
    }

    @Override
    public List<UompProjectSupplierListDTO> pageSupplierToProject(QueryFilter queryFilter) {
        return uompSupplierManagementMapper.pageSupplierToProject(queryFilter);
    }

    @Override
    public GroupBySupplierDTO groupBySupplier() {
        GroupBySupplierDTO groupBySupplierDTO = new GroupBySupplierDTO();
        List<SupplierManagementDTO> supplierList = uompSupplierManagementMapper.selectIdAndSupplierNameBySupplierStatus();

        //总数
        int sum = 0;
        for (SupplierManagementDTO supplier : supplierList) {
            //11-25 服务商人员统计也按照非黑，在职，审核通过，背调非不合格统计
            List<UompPersonInfo> personInfos = uompPersonInfoMapper.selectPersonByWorkingCompany(supplier.getId());
            if (personInfos == null || personInfos.isEmpty()) {
                continue;
            }
            supplier.setSum(personInfos.size());
            Map<String, List<UompPersonInfo>> map = new HashMap<>();
            for (UompPersonInfo personInfo : personInfos) {
                map.putIfAbsent(personInfo.getOrgGroupId(), new ArrayList<>());
                map.get(personInfo.getOrgGroupId()).add(personInfo);
            }
            // 运维组织
            List<UompOrgGroupPersonDto> orgGroupPersonDtos = new ArrayList<>();
            for (String key : map.keySet()) {
                UompOrgGroupPersonDto orgGroupPersonDto = new UompOrgGroupPersonDto();
                if (map.get(key) != null && !map.get(key).isEmpty()) {
                    orgGroupPersonDto.setName(map.get(key).get(0).getOrgGroupName());
                }
                orgGroupPersonDto.setPersonList(map.get(key));
                orgGroupPersonDtos.add(orgGroupPersonDto);
            }
            supplier.setChildren(orgGroupPersonDtos);
        }
        groupBySupplierDTO.setSum(sum);
        groupBySupplierDTO.setChildren(supplierList);

        return groupBySupplierDTO;
    }

    @Override
    public GroupBySupplierByGXDTO groupBySupplierByGX() {
        GroupBySupplierByGXDTO groupBySupplierByGXDTO = new GroupBySupplierByGXDTO();
        //总数
        int sum = 0;
        List<TechDTO> techList;
        //查看所有合作中的供应商列表
        List<SupplierManagementDTO> typeList = uompSupplierManagementMapper.selectSupplierTypeBySupplierStatus();

        Set<String> typeSet = new HashSet();
        if (!CollectionUtils.isEmpty(typeList)) {
            for (SupplierManagementDTO type : typeList) {
                if (type.getSupplierType().length() > 1) {
                    String[] sTypes = type.getSupplierType().split(",");
                    for (String sType : sTypes) {
                        typeSet.add(sType);
                    }
                } else {
                    typeSet.add(type.getSupplierType());
                }
            }
        }
        Map<String, List<SupplierManagementDTO>> typeMap = new HashMap<>();
        for (String types : typeSet) {
            typeMap.put(types, uompSupplierManagementMapper.selectAllBySupplierType(types));
        }

        for (String key : typeMap.keySet()) {
            List<SupplierManagementDTO> supplierManagementDTOList = typeMap.get(key);
            for (SupplierManagementDTO supplier : supplierManagementDTOList) {
                techList = new ArrayList<>();
                String id = supplier.getId();

                //根据技术方向查出该供应商分别人数
                List<BaseDTO> baseDTOList = dataDictMapper.selectSubListByDictKey("UOMP_TEC_DIRECTION");
                TechDTO tech;
                for (BaseDTO baseDTO : baseDTOList) {
                    tech = new TechDTO();
                    tech.setKey(baseDTO.getId());
                    tech.setName(baseDTO.getName());
                    tech.setCount(uompPersonInfoMapper.countByWorkingCompanyIdAndTechnicalDirection(id, baseDTO.getId()));
                    techList.add(tech);

                    sum += tech.getCount();
                    supplier.setSum(supplier.getSum() + tech.getCount());
                }

                //封装技术方向的集合
                supplier.setTechList(techList);
            }
        }
        groupBySupplierByGXDTO.setSum(sum);
        groupBySupplierByGXDTO.setChildren(typeMap);

        return groupBySupplierByGXDTO;
    }

    @Override
    public PageResult<UompSupplierManagementListDialog> getListDialog(QueryFilter queryFilter) {
        List<UompSupplierManagement> list = uompSupplierManagementMapper.query(queryFilter);
        List<UompSupplierManagementListDialog> dtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            PageResult pageResult = new PageResult(list);
            for (UompSupplierManagement uompSupplierManagement : list) {
                UompSupplierManagementListDialog dialog = new UompSupplierManagementListDialog();
                BeanUtils.copyProperties(uompSupplierManagement, dialog);
                dtoList.add(dialog);
            }
            pageResult.setRows(dtoList);
            return pageResult;
        }
        return new PageResult(new ArrayList());
    }

    @Override
    public List<SupplierManagementDTO> selectIdAndSupplierName() {
        return uompSupplierManagementMapper.selectIdAndSupplierNameBySupplierStatus();
    }

    @Override
    public List<ListAndNameDto> supplierServiceYears() {
        List<PercentageDTO> percentageDTOS = uompSupplierManagementMapper.supplierServiceYears();
        Map<Integer, List<PercentageDTO>> map = new HashMap<>();
        for (PercentageDTO percentageDTO : percentageDTOS) {
            int year = percentageDTO.getNum();
            if (year < 3) {
                map.putIfAbsent(1, new ArrayList<>());
                map.get(1).add(percentageDTO);
            } else if (year < 5) {
                map.putIfAbsent(2, new ArrayList<>());
                map.get(2).add(percentageDTO);
            } else if (year < 10) {
                map.putIfAbsent(3, new ArrayList<>());
                map.get(3).add(percentageDTO);
            } else {
                map.putIfAbsent(4, new ArrayList<>());
                map.get(4).add(percentageDTO);
            }
        }
        List<ListAndNameDto> list = new ArrayList<>();
        for (Integer key : map.keySet()) {
            ListAndNameDto listAndNameDto = new ListAndNameDto();
            listAndNameDto.setData(map.get(key));
            listAndNameDto.setNum(map.get(key) != null ? map.get(key).size() : 0);
            if (key == 1) {
                listAndNameDto.setName("不足3年");
            } else if (key == 2) {
                listAndNameDto.setName("3-5年");
            } else if (key == 3) {
                listAndNameDto.setName("5-10年");
            } else {
                listAndNameDto.setName("10年以上");
            }
            list.add(listAndNameDto);
        }
        return list;
    }

    @Override
    public SupplierInfoDTO supplierInfo(String supplierId) {
        SupplierInfoDTO supplierInfoDTO = new SupplierInfoDTO();
        // 服务商详情
        UompSupplierManagement uompSupplierManagement = uompSupplierManagementService.get(supplierId);
        if (uompSupplierManagement != null) {
            supplierInfoDTO.setSupplierName(uompSupplierManagement.getSupplierName());
            supplierInfoDTO.setTel(uompSupplierManagement.getTel());
            supplierInfoDTO.setRespName(uompSupplierManagement.getRespName());
        }
        // 查询应用系统关联合同关系
        List<UompContractManagement> contractList = uompContractManagementMapper.selectAllByPartyBId(supplierId);
        // 距离运维天数
        if (!contractList.isEmpty()) {
            Date startDate = new Date();
            Date endDate = contractList.get(0).getQualityEndDay();
            if (endDate != null) {
                supplierInfoDTO.setOutDays(intervalDay.compareDays(startDate, endDate));
            }
        }
        // 人员结构
        PersonStructureDTO personStructureDTO = new PersonStructureDTO();
        List<PercentageDTO> percentageDTO = new ArrayList<>();
        // 查询服务商名下所有人员
        //11-25 服务商人员统计也按照非黑，在职，审核通过，背调非不合格统计
        List<UompPersonInfo> personInfos = uompPersonInfoMapper.selectPersonByWorkingCompany(supplierId);
        if (!CollectionUtil.isEmpty(personInfos)) {
            personStructureDTO.setTotal(String.valueOf(personInfos.size()));
            // 联合查询已驻场人员
            //11-25 按照身份证号去重
            int admissionTotal = uompAdmissionPersonMapper.countTotalByApplyStatusNew(null, null, supplierId, null);
            if (admissionTotal <= 0) {
                if (StringUtils.isNotBlank(supplierInfoDTO.getSupplierName())) {
                    admissionTotal = uompAdmissionPersonMapper.countTotalByApplyStatusNew(null, null, null, supplierInfoDTO.getSupplierName());
                }
            }
            PercentageDTO admissionPercentageDTO = new PercentageDTO();
            // 驻场人数
            admissionPercentageDTO.setName("驻场人数");
            admissionPercentageDTO.setNum(admissionTotal);
            admissionPercentageDTO.setPercentage(new BigDecimal(admissionTotal).divide(new BigDecimal(personInfos.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
            percentageDTO.add(admissionPercentageDTO);
            PercentageDTO insidePercentageDTO = new PercentageDTO();
            // 未驻场
            insidePercentageDTO.setName("未驻场");
            insidePercentageDTO.setNum(personInfos.size() - admissionTotal);
            insidePercentageDTO.setPercentage(new BigDecimal(insidePercentageDTO.getNum()).divide(new BigDecimal(personInfos.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
            percentageDTO.add(insidePercentageDTO);
        }
        personStructureDTO.setPercentageDTO(percentageDTO);
        supplierInfoDTO.setPersonStructureDTO(personStructureDTO);
        // 人员背调
        //11-25 服务商人员统计也按照非黑，在职，审核通过，背调非不合格统计
        supplierInfoDTO.setBackgroundedPercentageDTOs(uompPersonInfoService.backgroundInfoPieByWorkingCompanyId(supplierId));
        // 人员服务年限
        //11-25 服务商人员统计也按照非黑，在职，审核通过，背调非不合格统计
        List<PercentageDTO> inTimePercentageDTOs = uompPersonInfoService.getPersonInTimeBySupplierId(supplierId);
        if (CollectionUtil.isEmpty(inTimePercentageDTOs)) {
            inTimePercentageDTOs = uompPersonInfoService.getPersonInTimeBySupplierName(supplierInfoDTO.getSupplierName());
        } else {
            int total = 0;
            for (PercentageDTO pd : inTimePercentageDTOs) {
                total += pd.getNum();
            }
            if (total == 0) {
                inTimePercentageDTOs = uompPersonInfoService.getPersonInTimeBySupplierName(supplierInfoDTO.getSupplierName());
            }
        }
        supplierInfoDTO.setInTimePercentageDTOs(inTimePercentageDTOs);
        // 服务对象
        supplierInfoDTO.setProjectBySupplierDTO(uompApplicationSystemManagementMapper.selectAllByRelationId(supplierId, ""));
        return supplierInfoDTO;
    }
}