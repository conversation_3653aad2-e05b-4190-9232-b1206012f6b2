package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompTrainingPlanPersonMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson;
import cn.gwssi.ecloud.staffpool.core.manager.UompTrainingPlanPersonService;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UompTrainingPlanPersonServiceImpl extends BaseManager<String, UompTrainingPlanPerson> implements UompTrainingPlanPersonService {

    @Autowired
    private UompTrainingPlanPersonMapper uompTrainingPlanPersonMapper;

    @Override
    public int insertSelective(UompTrainingPlanPerson record) {
        return uompTrainingPlanPersonMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(UompTrainingPlanPerson record) {
        return uompTrainingPlanPersonMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void deleteByPlanId(String id) {
        uompTrainingPlanPersonMapper.deleteByPlanId(id);
    }
}
