package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.OrgRelationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.OrgRelation;
import cn.gwssi.ecloud.staffpool.core.manager.OrgRelationService;
import cn.gwssi.ecloud.staffpool.dto.ManagerDTO;
import cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class OrgRelationServiceImpl extends BaseManager<String, OrgRelation> implements OrgRelationService {

    @Resource
    private OrgRelationMapper orgRelationMapper;

    @Override
    public List<ManagerDTO> selectNumByStatus() {
        return orgRelationMapper.selectNumByStatus();
    }

    /**
     * 临时入场-流程表单-节点人员
     * @return
     */
    @Override
    public Set<DefaultIdentity> selectUserInfoByUserId() {
        Set<DefaultIdentity> defaultIdentitySet = new HashSet<>();
        String userId = ContextUtil.getCurrentUserId();
        List<UompAcceptInfoDTO> uompAcceptInfoDTOList = orgRelationMapper.selectUserInfoByUserId(userId);
        if (!uompAcceptInfoDTOList.isEmpty()){
            for (UompAcceptInfoDTO uompAcceptInfo : uompAcceptInfoDTOList){
                DefaultIdentity defaultIdentity = new DefaultIdentity();
                defaultIdentity.setId(uompAcceptInfo.getId()==null?"":uompAcceptInfo.getId());
                defaultIdentity.setName(uompAcceptInfo.getName()==null?"":uompAcceptInfo.getName());
                defaultIdentity.setType(uompAcceptInfo.getType()==null?"":uompAcceptInfo.getType());
                defaultIdentity.setOrgId(uompAcceptInfo.getOrgId()==null?"":uompAcceptInfo.getOrgId());

                defaultIdentitySet.add(defaultIdentity);
            }
        }
        return defaultIdentitySet;
    }
}

