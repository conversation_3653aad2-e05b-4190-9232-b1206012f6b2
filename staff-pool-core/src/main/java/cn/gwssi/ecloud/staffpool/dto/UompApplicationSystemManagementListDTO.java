package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 应用系统信息表
 */
@ApiModel(value = "应用系统列表", description = "应用系统列表")
@Data
public class UompApplicationSystemManagementListDTO {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 应用系统名称
     */
    @ApiModelProperty(value = "应用系统名称")
    private String applicationSystemName;
    /**
     * 运维部门名称
     */
    @ApiModelProperty(value = "运维部门名称")
    private String departName;
    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;
    /**
     * 是否核心应用（1：是   0：否）
     */
    @ApiModelProperty(value = "是否核心应用（1：是   0：否）")
    private String isHeart;
    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    private String onlineTime;
    /**
     * 状态（0：使用中  1已停用）
     */
    @ApiModelProperty(value = "状态（0：使用中  1已停用）")
    private String systemStatus;
    /**
     * 服务商名称
     */
    @ApiModelProperty(value = "服务商名称")
    private String supplierName;
}