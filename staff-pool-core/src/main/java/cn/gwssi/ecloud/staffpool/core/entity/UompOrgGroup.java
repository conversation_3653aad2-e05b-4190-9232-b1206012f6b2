package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 人员库组织机构表
 */
@ApiModel(description="人员库组织机构表")
@Data
public class UompOrgGroup extends BaseModel {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 名称
    */
    @ApiModelProperty(value="名称")
    private String name;

    /**
    * 父ID
    */
    @ApiModelProperty(value="父ID")
    private String parentId;

    /**
    * 排序
    */
    @ApiModelProperty(value="排序")
    private Integer sn;

    /**
    * 编号
    */
    @ApiModelProperty(value="编号")
    private String code;

    /**
    * 类型：0集团，1公司，3部门
    */
    @ApiModelProperty(value="类型：0集团，1公司，3部门")
    private String type;

    /**
    * 描述
    */
    @ApiModelProperty(value="描述")
    private String desc;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * 更新人
    */
    @ApiModelProperty(value="更新人")
    private String updateBy;

    /**
    * 机构路径
    */
    @ApiModelProperty(value="机构路径")
    private String path;

    /**
    * 简称
    */
    @ApiModelProperty(value="简称")
    private String simple;

    /**
    * 状态：0禁用，1正常
    */
    @ApiModelProperty(value="状态：0禁用，1正常")
    private Integer status;

    /**
    * 显示名称
    */
    @ApiModelProperty(value="显示名称")
    private String showName;

    /**
    * 是否虚拟:0否,1是
    */
    @ApiModelProperty(value="是否虚拟:0否,1是")
    private Integer virtual;

    /**
    * 曾用名
    */
    @ApiModelProperty(value="曾用名")
    private String historyName;

    /**
    * 机构路径
    */
    @ApiModelProperty(value="机构路径")
    private String pathName;

    /**
    * 主编号
    */
    @ApiModelProperty(value="主编号")
    private String mcode;

    /**
    * 负责人
    */
    @ApiModelProperty(value="负责人")
    private String respName;

    @ApiModelProperty(value="负责人id")
    private String respId;

    /**
    * 组织 id
    */
    @ApiModelProperty(value="组织 id")
    private String orgGroupId;

    private static final long serialVersionUID = 1L;
}