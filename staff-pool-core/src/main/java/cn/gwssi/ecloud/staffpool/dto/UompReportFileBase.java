package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "服务报告文件上传对象")
public class UompReportFileBase implements Serializable {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "大小")
    private Long size;
    @ApiModelProperty(value = "上传人id")
    private String uID;
    @ApiModelProperty(value = "上传人")
    private String uName;
    @ApiModelProperty(value = "上传日期")
    private String date;
}