package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "入场申请人员信息下拉选择响应类")
@Data
public class PersonSeletDTO implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "姓名")
    private String personName;
    @ApiModelProperty(value = "身份证号")
    @Sensitive(actualLen = 18)
    private String personCard;
    @ApiModelProperty(value = "所在公司")
    private String workingCompany;
    @ApiModelProperty(value = "所在公司id")
    private String workingCompanyId;
    @ApiModelProperty(value = "学历")
    private String education;
    @ApiModelProperty(value = "联系方式")
    private String tel;
    @ApiModelProperty(value = "技术方向")
    private String technicalDirection;
    @ApiModelProperty(value = "入职时间")
    private String entryDate;
    @ApiModelProperty(value = "就职公司id")
    private String supplierId;
    @ApiModelProperty(value = "运维组id")
    private String orgGroupId;
    @ApiModelProperty(value = "运维组名")
    private String orgGroupName;
    @ApiModelProperty(value = "性别 0: 男,  1:女")
    private String personSex;
}
