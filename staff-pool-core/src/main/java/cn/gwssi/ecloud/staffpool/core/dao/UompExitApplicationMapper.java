package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompExitApplication;
import cn.gwssi.ecloud.staffpool.dto.ExitApplyDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UompExitApplicationMapper extends BaseDao<String, UompExitApplication> {

    int insertSelective(UompExitApplication record);

    ExitApplyDTO selectAllById(String exitId);

    Integer countByPersonCard(String personCard);

    Integer countByOutTime(String time);

    Integer updateIsDeleteById(String id);

    Integer countByApplyStatus(@Param("outTimeBegin") String outTimeBegin, @Param("outTimeEnd") String outTimeEnd);

    Integer countByApplyStatusNew(@Param("outTimeBegin") String outTimeBegin, @Param("outTimeEnd") String outTimeEnd);

}