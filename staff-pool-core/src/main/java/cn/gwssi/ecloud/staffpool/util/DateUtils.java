package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.sys.api.model.calendar.WorkCalenDar;
import cn.gwssi.ecloudframework.sys.core.dao.WorkCalenDarDao;
import cn.hutool.extra.spring.SpringUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 时间工具
 *
 * <AUTHOR>
 * @date 2023/06/01
 */
public class DateUtils {

    public static final String yyyyMMdd = "yyyy-MM-dd";
    public static final String yyyyMM = "yyyy-MM";
    public static final String yyyyMMddHHmm = "yyyy-MM-dd HH:mm";
    public static final String yyyyMMddHHmmss = "yyyy-MM-dd HH:mm:ss";

    public static SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMdd);
    public static SimpleDateFormat sdf1 = new SimpleDateFormat(yyyyMMddHHmmss);
    /**
     * 将Date转换为LocalDate，并加上指定天数，返回Date
     *
     * <AUTHOR>
     * @date 2022/08/26
     */
    public static Date plusDays(Date startDate, long thisDate) {
        return toDate(toLocalDate(startDate).plusDays(thisDate));
    }

    /**
     * 将Date转换为LocalDate，并加上指定天数，返回Date
     *
     * <AUTHOR>
     * @date 2022/08/26
     */
    public static Date plusOneDay(Date startDate) {
        return toDate(toLocalDate(startDate).plusDays(1L));
    }

    /**
     * 将Date转换为字符串
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static Date toDate(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMdd);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将Date转换为字符串
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static String dateToStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMdd);
        return sdf.format(date);
    }

    /**
     * 将Date转换为字符串
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static String dateToYmStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMM);
        return sdf.format(date);
    }

    /**
     * 将字符串格式转换成LocalDate类型
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static LocalDate toLocalDate(String str) {
        if (str.length() > yyyyMMdd.length()) {
            str = str.substring(0, yyyyMMdd.length());
        }
        return LocalDate.parse(str);
    }

    /**
     * 获取两个日期间隔了多少天
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static long betweenOfDay(Date start, Date end) {
        return toLocalDate(end).toEpochDay() - toLocalDate(start).toEpochDay() + 1;
    }

    /**
     * 获取两个日期间隔了多少天
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static long betweenOfDay(LocalDate start, LocalDate end) {
        return end.toEpochDay() - start.toEpochDay() + 1;
    }

    /**
     * 将date转换LocalDate
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 将localDate转换Date
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static Date toDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 创建一个00:00:00的当前日期Date
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static Date nowDate() {
        return Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将Date转换成LocaDateTime
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
//        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 将LocaDateTime转换成Date
     *
     * <AUTHOR>
     * @date 2022/08/17
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getNaturalDay(Date date, int days) {
        LocalDate overdate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return overdate.plusDays(days).toString();
    }

    /**
     * @param
     * @return
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Description //获取近一年所有月份，比如今天是2022年9月，则返回2021年8月至今的12个月份
     * @Date 8:56 2022/9/28
     **/
    public static List<String> getNearYearMonths() {
        List<String> months = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -12);
        for (int i = 0; i < 12; i++) {
            Date date = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            months.add(sdf.format(date));
            calendar.add(Calendar.MONTH, 1);
        }
        return months;
    }

    /**
     * @param n
     * @return
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Description //获取近n天所有日期，比如n=15,今天是2023年6月1日，则返回2023年5月17日至2023年5月31日
     * @Date 8:56 2022/9/28
     **/
    public static List<String> getNearDays(int n) {
        List<String> days = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -n);
        for (int i = 0; i < n; i++) {
            Date date = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            days.add(sdf.format(date));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        return days;
    }

    /**
     * @param
     * @return list
     * <AUTHOR>
     * @Description //获取某段日期的同比或者环比对应的日期
     * @Date 16:01 2022/9/29
     **/
    public static Map<String, String> getTbHbDate(String date_start, String date_end, String type) throws ParseException {
        Date resultDateStart = new Date();
        Date resultDateEnd = new Date();
        Map<String, String> dateMap = new HashMap<String, String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtil.isNotEmpty(date_start) && StringUtil.isNotEmpty(date_end)) {
            Date startDate = sdf.parse(date_start);
            Date endDate = sdf.parse(date_end);
            Calendar calendar = Calendar.getInstance();
            if ("TB".equals(type)) {
                //同比 年份减1
                calendar.setTime(startDate);
                calendar.add(Calendar.YEAR, -1);
                resultDateStart = calendar.getTime();
                calendar.clear();
                calendar.setTime(endDate);
                calendar.add(Calendar.YEAR, -1);
                resultDateEnd = calendar.getTime();
            } else {
                //环比 根据天数差 计算
                int days = Integer.valueOf(betweenOfDay(startDate, endDate) + "");
                calendar.setTime(startDate);
                calendar.add(Calendar.DAY_OF_MONTH, -days);
                resultDateStart = calendar.getTime();
                calendar.clear();
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_MONTH, -days);
                resultDateEnd = calendar.getTime();
            }
        }
        dateMap.put("date_start", sdf.format(resultDateStart));
        dateMap.put("date_end", sdf.format(resultDateEnd));
        return dateMap;
    }

    /**
     * @param
     * @return list
     * <AUTHOR>
     * @Description //获取某段日期的同比或者环比对应的日期
     * @Date 16:01 2022/9/29
     **/
    public static Map<String, String> getTbHbDate(String date_start, String date_end, String type, int ss) throws ParseException {
        Date resultDateStart = new Date();
        Date resultDateEnd = new Date();
        Map<String, String> dateMap = new HashMap<String, String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtil.isNotEmpty(date_start) && StringUtil.isNotEmpty(date_end)) {
            Date startDate = sdf.parse(date_start);
            Date endDate = sdf.parse(date_end);
            Calendar calendar = Calendar.getInstance();
            if ("TB".equals(type)) {
                //同比 年份减1
                calendar.setTime(startDate);
                calendar.add(Calendar.YEAR, 1 * ss);
                resultDateStart = calendar.getTime();
                calendar.clear();
                calendar.setTime(endDate);
                calendar.add(Calendar.YEAR, 1 * ss);
                resultDateEnd = calendar.getTime();
            } else {
                //环比 根据天数差 计算
                int days = Integer.valueOf(betweenOfDay(startDate, endDate) + "");
                calendar.setTime(startDate);
                calendar.add(Calendar.DAY_OF_MONTH, days * ss);
                resultDateStart = calendar.getTime();
                calendar.clear();
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_MONTH, days * ss);
                resultDateEnd = calendar.getTime();
            }
        }
        dateMap.put("date_start", sdf.format(resultDateStart));
        dateMap.put("date_end", sdf.format(resultDateEnd));
        return dateMap;
    }

    /**
     * 将Date转换为字符串
     *
     * <AUTHOR>
     * @date 2023/06/08
     */
    public static String getCurrentDateStr(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //检查字符串是否是日期
     * @Date 16:27 2023/7/5
     **/
    public static boolean checkIsDate(String dateStr) {
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            sdf.parse(dateStr);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    //Date类型转Calendar类型
    public static Calendar dataToCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * @param date1
     * @param date2
     * @return true o false
     * <AUTHOR>
     * @Description //判断两个日期是否是同一天
     * @Date 10:52 2023/7/18
     **/
    public static boolean checkIfOneDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar calendar = dataToCalendar(date1);
        Calendar calendar2 = dataToCalendar(date2);
        return calendar.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
                calendar.get(Calendar.DAY_OF_YEAR) == calendar2.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * @param date1
     * @param date2
     * @return true o false
     * <AUTHOR>
     * @Description //判断两个日期是否是同一周
     * @Date 10:52 2023/7/18
     **/
    public static boolean checkIfOneWeek(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar calendar = dataToCalendar(date1);
        Calendar calendar2 = dataToCalendar(date2);
        //西方周日是每周的第一天 此处设置周一为每周第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar2.setFirstDayOfWeek(Calendar.MONDAY);
        return calendar.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
                calendar.get(Calendar.WEEK_OF_YEAR) == calendar2.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * @param date1
     * @param date2
     * @return true o false
     * <AUTHOR>
     * @Description //判断两个日期是否是同一月
     * @Date 10:52 2023/7/18
     **/
    public static boolean checkIfOneMonth(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar calendar = dataToCalendar(date1);
        Calendar calendar2 = dataToCalendar(date2);
        return calendar.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
                calendar.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH);
    }

    /**
     * @param date
     * @param startDate
     * @param endDate
     * @return boolean
     * <AUTHOR>
     * @Description //判断日期是否在范围内
     * @Date 11:21 2023/7/19
     **/
    public static boolean checkDateIfInRange(Date date, Date startDate, Date endDate) {
        if (startDate == null || endDate == null || date == null) {
            return false;
        }
        Calendar dateCalendar = dataToCalendar(date);
        Calendar startCalendar = dataToCalendar(startDate);
        Calendar endCalendar = dataToCalendar(endDate);
        if (dateCalendar.before(endCalendar) && dateCalendar.after(startCalendar)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @return int
     * <AUTHOR>
     * @Description //返回今天是星期几
     * @Date 11:28 2023/7/19
     **/
    public static int getWeekDays(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar dateCalendar = dataToCalendar(date);
        int day = dateCalendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (day == 0) {
            return 7;
        } else {
            return day;
        }

    }

    /**
     * 将字符串转换成指定格式的日期date
     *
     * <AUTHOR>
     * @date 2023/10/09
     */
    public static Date getFormatDate(String datestr, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.parse(datestr);
    }

    /**
     * @param time
     * @return java.lang.String
     * <AUTHOR>
     * @Description //根据时间-时 生成当天的cron表达式
     * @Date 16:51 2023/10/09
     **/
    public static String createCron(String hour, String minute) {
        StringBuilder cronSb = new StringBuilder();
        cronSb.append("0 "); //秒
        cronSb.append(minute + " "); //分
        cronSb.append(hour + " "); //时
        Calendar calendar = Calendar.getInstance();
        cronSb.append(calendar.get(Calendar.DAY_OF_MONTH) + " "); //日
        cronSb.append(calendar.get(Calendar.MONTH) + 1); //月
        cronSb.append(" ? "); //周
        cronSb.append(calendar.get(Calendar.YEAR)); //年
        return cronSb.toString();
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //判断今天是否是本月最后一天
     * @Date 17:09 2023/10/09
     **/
    public static boolean ifLastDayOfMonth() {
        LocalDate date = LocalDate.now();
        int dayOfMonth = date.getDayOfMonth();
        int lastDay = date.lengthOfMonth();
        if (dayOfMonth == lastDay) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param date   日期不传默认当前系统日期
     * @param format
     * @return java.lang.String
     * <AUTHOR>
     * @Description //获取指定日期对应的第一个工作日，如果指定日期是非工作日，则返回非工作日后的第一个工作日，反之，返回指定日期
     * @Date 20:23 2023/11/16
     **/
    public static String getFirstWorkDay(Date date, String format) {
        if (date == null) {
            date = new Date();
        }
        WorkCalenDarDao workCalenDarDao = SpringUtil.getBean("workCalenDarDao");
        List<WorkCalenDar> workdayList = workCalenDarDao.getWorkDayByDays(date);
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(workdayList.get(0).getDay());
    }

    /**
     * @param time
     * @return boolean
     * <AUTHOR>
     * @Description //判断计划执行时间是否超过当前系统时间
     * @Date 14:50 2023/12/06
     **/
    public static boolean checkIfOverNowTime(String hour, String minute) {
        if (StringUtil.isEmpty(hour)) {
            return false;
        }
        String time = hour;
        if (StringUtil.isNotEmpty(minute)) {
            time = time + ":" + minute;
        } else {
            time = time + ":00";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date compareDate = sdf.parse(getCurrentDateStr("yyyy-MM-dd") + " " + time);
            Calendar calendar = Calendar.getInstance();
            Date date2 = calendar.getTime();
            return compareDate.after(date2);
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 获取指定日期一年前的日期
     *
     * @param date
     * @return
     */
    public static Date getAYearAgo(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, -1);
        return calendar.getTime();
    }

    /**
     * @param
     * @return
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Description //获取近半年所有月份，比如今天是2024年6月，则返回2024年6月往前6个月
     **/
    public static List<String> getSixMonths() {
        List<String> months = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -5);
        for (int i = 0; i < 6; i++) {
            Date date = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            months.add(sdf.format(date));
            calendar.add(Calendar.MONTH, 1);
        }
        return months;
    }

    public static void main(String[] args) throws ParseException {
        System.out.println(getSixMonths());
//        String str = createCron("23:55");
//        CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
//        cronTriggerImpl.setCronExpression(str);
//        List<Date> nextExecTime = TriggerUtils.computeFireTimes(cronTriggerImpl, null, 1);
//        System.out.println(nextExecTime.get(0));
//        System.out.println(createCron("23:55"));
//        System.out.println(createCron("19:00"));
    }

}
