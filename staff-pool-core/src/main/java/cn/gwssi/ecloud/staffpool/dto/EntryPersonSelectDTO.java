package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EntryPersonSelectDTO implements Serializable {

    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="人员id")
    private String personId;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    @Sensitive(prefixLen = 3,suffixLen = 3,actualLen = 18)
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="入职时间")
    private String entryDate;
    @ApiModelProperty(value="所在公司")
    private String workingCompany;
    @ApiModelProperty(value="所在公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="所在公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="岗位id")
    private String postId;
    @ApiModelProperty(value="岗位名称")
    private String postName;
    @ApiModelProperty(value="参与系统集合")
    private List<ProjectListBean> projectList;
}
