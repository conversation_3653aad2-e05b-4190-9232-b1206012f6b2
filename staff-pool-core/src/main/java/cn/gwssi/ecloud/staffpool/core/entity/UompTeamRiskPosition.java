package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 运维岗位职责实体类
 */
public class UompTeamRiskPosition extends BaseModel {

    @ApiModelProperty(value="岗位名称")
    private String name;

    @ApiModelProperty(value="权限")
    private String permission;

    @ApiModelProperty(value="技术资质")
    private String technicalLevel;

    public UompTeamRiskPosition() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getTechnicalLevel() {
        return technicalLevel;
    }

    public void setTechnicalLevel(String technicalLevel) {
        this.technicalLevel = technicalLevel;
    }
}
