package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description="cmdb_table_base_config")
public class CmdbTableBaseConfig extends BaseModel {
    @ApiModelProperty(value="")
    private String baselineId;

    @ApiModelProperty(value="")
    private String modelId;

    @ApiModelProperty(value="")
    private String key;

    @ApiModelProperty(value="")
    private String name;

    @ApiModelProperty(value="")
    private String comment;

    @ApiModelProperty(value="")
    private String dsKey;

    @ApiModelProperty(value="")
    private String dsName;

    @ApiModelProperty(value="")
    private String groupId;

    @ApiModelProperty(value="")
    private String groupName;

    @ApiModelProperty(value="")
    private Integer external;

    @ApiModelProperty(value="")
    private String orgId;

    @ApiModelProperty(value="")
    private String modelType;

    @ApiModelProperty(value="")
    private String teamName;

    @ApiModelProperty(value="")
    private String modelStatus;

    @ApiModelProperty(value="")
    private String teamId;

    @ApiModelProperty(value="")
    private String baseline;

    @ApiModelProperty(value="")
    private String baselineStatus;

    @ApiModelProperty(value="")
    private String baselineAudit;

    @ApiModelProperty(value="")
    private Date releaseTime;

    @ApiModelProperty(value="")
    private String releasePersion;

    @ApiModelProperty(value="")
    private String releasePersionId;

    @ApiModelProperty(value="")
    private String delFlag;

    @ApiModelProperty(value="")
    private Integer sn;

    @ApiModelProperty(value="")
    private String operator;

    @ApiModelProperty(value="")
    private String baselineIdPre;

    @ApiModelProperty(value="")
    private String modelIcon;

    @ApiModelProperty(value="")
    private String orderNo;

    public String getBaselineId() {
        return baselineId;
    }

    public void setBaselineId(String baselineId) {
        this.baselineId = baselineId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getDsKey() {
        return dsKey;
    }

    public void setDsKey(String dsKey) {
        this.dsKey = dsKey;
    }

    public String getDsName() {
        return dsName;
    }

    public void setDsName(String dsName) {
        this.dsName = dsName;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getExternal() {
        return external;
    }

    public void setExternal(Integer external) {
        this.external = external;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getModelStatus() {
        return modelStatus;
    }

    public void setModelStatus(String modelStatus) {
        this.modelStatus = modelStatus;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getBaseline() {
        return baseline;
    }

    public void setBaseline(String baseline) {
        this.baseline = baseline;
    }

    public String getBaselineStatus() {
        return baselineStatus;
    }

    public void setBaselineStatus(String baselineStatus) {
        this.baselineStatus = baselineStatus;
    }

    public String getBaselineAudit() {
        return baselineAudit;
    }

    public void setBaselineAudit(String baselineAudit) {
        this.baselineAudit = baselineAudit;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getReleasePersion() {
        return releasePersion;
    }

    public void setReleasePersion(String releasePersion) {
        this.releasePersion = releasePersion;
    }

    public String getReleasePersionId() {
        return releasePersionId;
    }

    public void setReleasePersionId(String releasePersionId) {
        this.releasePersionId = releasePersionId;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getSn() {
        return sn;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getBaselineIdPre() {
        return baselineIdPre;
    }

    public void setBaselineIdPre(String baselineIdPre) {
        this.baselineIdPre = baselineIdPre;
    }

    public String getModelIcon() {
        return modelIcon;
    }

    public void setModelIcon(String modelIcon) {
        this.modelIcon = modelIcon;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}