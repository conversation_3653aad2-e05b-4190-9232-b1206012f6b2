package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(description="服务商详情")
@Data
public class SupplierInfoDTO implements Serializable {

    @ApiModelProperty(value="服务商名")
    private String supplierName;
    @ApiModelProperty(value="服务商负责人")
    private String respName;
    @ApiModelProperty(value="服务商联系电话")
    private String tel;
    @ApiModelProperty(value="距离退场天数")
    private Integer outDays;
    @ApiModelProperty(value="人员结构")
    PersonStructureDTO personStructureDTO;
    @ApiModelProperty(value="人员背调")
    List<PercentageDTO> backgroundedPercentageDTOs;
    @ApiModelProperty(value="人员服务年限")
    List<PercentageDTO> inTimePercentageDTOs;
    @ApiModelProperty(value="服务对象")
    List<ProjectBySupplierDTO> projectBySupplierDTO;
}
