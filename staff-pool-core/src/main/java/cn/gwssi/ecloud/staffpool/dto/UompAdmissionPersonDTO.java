package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UompAdmissionPersonDTO {
    @ExcelProperty("姓名")
    private String personName;

    @ExcelProperty("身份证号")
    private String personCard;

    @ExcelProperty("服务类型")
    private String serviceType;

    @ExcelProperty("驻场服务地点")
    private String serviceLocation;

    @ExcelProperty("应用系统")
    private String engagementProject;

    @ExcelProperty("驻场日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date inTime;

    @ExcelProperty("退场日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date outTime;
}
