package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompExitApplication extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String applyPersonId;

    @ApiModelProperty(value="")
    
    private String outApplyCode;

    @ApiModelProperty(value="")
    
    private String personName;

    @ApiModelProperty(value="")
    
    private String tel;

    @ApiModelProperty(value="")
    
    private String onlineTime;

    @ApiModelProperty(value="")
    
    private String maintenanceGroupId;

    @ApiModelProperty(value="")
    
    private String maintenanceGroupName;

    @ApiModelProperty(value="")
    
    private String postId;

    @ApiModelProperty(value="")
    
    private String postName;

    @ApiModelProperty(value="")
    
    private String workingCompany;

    private String workingCompanyId;

    private String workingCompanyJson;

    @ApiModelProperty(value="")
    
    private String jobAcceptId;

    @ApiModelProperty(value="")
    
    private String jobAcceptName;

    @ApiModelProperty(value="")
    
    private Date planOutTime;

    @ApiModelProperty(value="")
    
    private String outReason;

    @ApiModelProperty(value="")
    
    private String outReasonFile;

    @ApiModelProperty(value="")
    
    private String jobHandoverSituation;

    @ApiModelProperty(value="")
    
    private String jobHandoverSituationFile;

    @ApiModelProperty(value="")
    
    private String managerComment;

    @ApiModelProperty(value="")
    
    private String leaderComment;

    @ApiModelProperty(value="")
    
    private String isDelete;

    @ApiModelProperty(value="")
    
    private Date outTime;

    @ApiModelProperty(value="")
    
    private String applyStatus;

    @ApiModelProperty(value="")
    
    private String instId;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private String createOrgId;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String updateOrgId;

    @ApiModelProperty(value="")
    
    private String delFlag;

    @ApiModelProperty(value="")
    
    private String outApplyTitle;

    @ApiModelProperty(value="")
    
    private String createUser;

    @ApiModelProperty(value="")
    
    private String personCard;

    private static final long serialVersionUID = 1L;
}