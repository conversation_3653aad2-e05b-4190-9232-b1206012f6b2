package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description="人员信息总览响应类")
@Data
public class PersonInfoOverviewDTO implements Serializable {

    @ApiModelProperty(value="主键id")
    private String id;
    @ApiModelProperty(value="标题")
    private String title;
    @ApiModelProperty(value="类型 1:入场 2:退场")
    private String type;
    @ApiModelProperty(value="人员id")
    private String personId;
    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="联系方式")
    private String tel;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
    @ApiModelProperty(value="就职公司json")
    private String workingCompanyJson;
    @ApiModelProperty(value="所属运维组")
    private String maintenanceGroupName;
    @ApiModelProperty(value="参与系统")
    private String engagementProject;
    @ApiModelProperty(value="入场日期")
    private Date inTime;
    @ApiModelProperty(value="退出日期")
    private Date outTime;
    @ApiModelProperty(value="流程实例id")
    private String instId;
    @ApiModelProperty(value="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过")
    private String applyStatus;
    @ApiModelProperty(value="添加日期")
    private Date createTime;
    @ApiModelProperty(value="驻场状态 0-驻场中 1-已退场")
    private String entryStatus;
    @ApiModelProperty(value="任务id")
    private String taskId;
    @ApiModelProperty(value="linkid")
    private String taskLinkId;
    @ApiModelProperty(value = "性别 0: 男,  1:女")
    private String sex;

}
