package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 应用系统关联表
 */
@ApiModel(value = "应用系统关联表", description = "应用系统关联表")
@Data
public class UompApplicationSystemRelation extends BaseModel {

    /**
     * 应用系统id
     */
    @ApiModelProperty(value = "应用系统id")
    private String applicationSystemManagementId;

    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id")
    private String relationId;

    /**
     * 关联类型（contract:合同）
     */
    @ApiModelProperty(value = "关联类型（contract:合同）")
    private String relationType;

    /**
     * 创建机构
     */
    @ApiModelProperty(value = "创建机构")
    private String createOrgId;

    /**
     * 更新机构
     */
    @ApiModelProperty(value = "更新机构")
    private String updateOrgId;

    /**
     * 删除标识（0:正常  1：已删除）
     */
    @ApiModelProperty(value = "删除标识（0:正常  1：已删除）")
    private String delFlag;
}