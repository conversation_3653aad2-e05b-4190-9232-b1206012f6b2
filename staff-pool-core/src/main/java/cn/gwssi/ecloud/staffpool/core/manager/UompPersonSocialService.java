package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonSocial;
import cn.gwssi.ecloud.staffpool.dto.UompPersonSocialDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonSocialService extends Manager<String, UompPersonSocial> {
    int insertSelective(UompPersonSocial record);

    int updateByPrimaryKeySelective(UompPersonSocial record);

    int updateByPersonId(UompPersonSocial record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonSocialDto> selectByPersonIds(List<String> personIdList);

    void deleteByPersonId(String personId);
}
