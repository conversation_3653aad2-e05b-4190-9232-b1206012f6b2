package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="uomp_person_no_crime")
@Data
public class UompPersonNoCrimeDto extends BaseModel {
    @ApiModelProperty(value="")
    private String personId;

    @ApiModelProperty(value="")
    private String proofNumber;

    @ApiModelProperty(value="")
    private String queryTime;

    @ApiModelProperty(value="")
    private String queryBeginTime;

    @ApiModelProperty(value="")
    private String queryEndTime;

    @ApiModelProperty(value="")
    private String provideUnit;

    @ApiModelProperty(value="")
    private String indate;

    @ApiModelProperty(value="")
    private JSONArray fileInfo;

    @ApiModelProperty(value="")
    private String createOrgId;

    @ApiModelProperty(value="")
    private String updateOrgId;

    @ApiModelProperty(value="")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String personName;

    @ApiModelProperty(value = "")
    private String personCard;

    private static final long serialVersionUID = 1L;
}