package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompAdmissionPersonMapper;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.entity.UompExitAdmissionRelation;
import cn.gwssi.ecloud.staffpool.core.dao.UompExitAdmissionRelationMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompExitAdmissionRelationService;

import java.util.Date;
import java.util.List;

@Service
public class UompExitAdmissionRelationServiceImpl extends BaseManager<String, UompExitAdmissionRelation> implements UompExitAdmissionRelationService{

    @Resource
    private UompExitAdmissionRelationMapper uompExitAdmissionRelationMapper;
    @Resource
    private UompAdmissionPersonMapper uompAdmissionPersonMapper;

    @Override
    public int insertSelective(UompExitAdmissionRelation record) {
        return uompExitAdmissionRelationMapper.insertSelective(record);
    }

    @Override
    public void updateOutTimeByExitId(String exitId, Date outTime) {
        List<String> applPersonIdList = uompExitAdmissionRelationMapper.selectApplyPersonIdByExitId(exitId);

        //声明 修改时间的sql
        if (!applPersonIdList.isEmpty()){
            uompAdmissionPersonMapper.updateOutTimeByIdIn(outTime, applPersonIdList);
        }
    }
}
