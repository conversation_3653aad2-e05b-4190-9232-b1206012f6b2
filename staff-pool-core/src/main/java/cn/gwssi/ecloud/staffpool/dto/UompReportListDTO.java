package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
    * 服务报告信息表
    */
@ApiModel(value = "服务报告列表")
@Data
public class UompReportListDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value="id")
    private String id;

    /**
    * 报告名称
    */
    @ApiModelProperty(value="报告名称")
    private String reportName;

    /**
    * 报告编码
    */
    @ApiModelProperty(value="报告编码")
    private String reportCode;

    /**
    * 报告类型
    */
    @ApiModelProperty(value="报告类型 （周报 月报 年报）")
    private String reportType;

    /**
    * 应用系统id
    */
    @ApiModelProperty(value="应用系统id")
    private String applicationSystemManagementId;

    /**
    * 应用 系统名称
    */
    @ApiModelProperty(value="应用系统名称")
    private String applicationSystemName;

    /**
    * 服务商
    */
    @ApiModelProperty(value="服务商")
    private String supplierName;

    /**
    * 上传时间
    */
    @ApiModelProperty(value="上传时间")
    private String uploadTime;

    /**
    * 上传人
    */
    @ApiModelProperty(value="上传人")
    private String uploaderName;

    /**
     * 上传文件列表
     */
    @ApiModelProperty(value="上传文件集合")
    private String fileListJson;
}