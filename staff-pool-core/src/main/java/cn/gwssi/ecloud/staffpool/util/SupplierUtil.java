package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloud.staffpool.core.dao.OrgRelationMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompPersonInfoMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompSupplierManagementMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.dto.IsSupplierDto;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class SupplierUtil {
    @Autowired
    private OrgRelationMapper orgRelationMapper;
    @Autowired
    private UompSupplierManagementMapper uompSupplierManagementMapper;
    @Resource
    private UompPersonInfoMapper uompPersonInfoMapper;

    // 是否是供应商
    public IsSupplierDto isSupplier() {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        //11-21 只有服务商运维负责人才看自己服务商的
        //服务商运维负责人
        int gysCount = orgRelationMapper.countByUserIdAndRole(user.getUserId(), "G_ROLE_SEVICEMANAGER");
        //总包
        int zbCount = orgRelationMapper.countByUserIdAndRole(user.getUserId(), "G_ROLE_MANAGER");
        //甲方负责人
        int jfCount = orgRelationMapper.countByUserIdAndRole(user.getUserId(), "G_ROLE_CUSTOMER_LEADER");
        String ifSupplier = "0";
        String supplierId = null;
        //总包,甲方 看所有，服务商运维负责人看自己服务商的
        if (zbCount > 0 || jfCount > 0) {
            //如果既有总包或甲方又有服务商运维负责人的角色，按最大权限取
        } else if (gysCount > 0) {
            ifSupplier = "1";
            // 通过用户id查询运维用户信息
            UompPersonInfo uompPersonInfo = uompPersonInfoMapper.selectAllByUserId(user.getUserId());
            if (uompPersonInfo != null) {
                supplierId = uompPersonInfo.getWorkingCompanyId();
            }
        }

        if (StringUtils.isBlank(supplierId)) {
            ifSupplier = "0";
        }

        IsSupplierDto isSupplierDto = new IsSupplierDto();
        isSupplierDto.setIfSupplier(ifSupplier);
        isSupplierDto.setOrgId(user.getOrgId());
        isSupplierDto.setSupplierId(supplierId);
        return isSupplierDto;
    }
}
