package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompOrgGroupService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.core.model.OrderGroup;
import cn.gwssi.ecloud.staffpool.core.model.OrgTreeUomp;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgPersonInfoDTO;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.BeanUtils;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.module.orgCustom.core.manager.GroupCustomManager;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.GroupCustom;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Api(description = "人员库-组织机构")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/org/group")
public class UompOrgGroupController extends BaseController<GroupUomp> {

    @Resource
    private UompOrgGroupService ucompOrgGroupService;
    @Resource
    private GroupCustomManager groupManager;
    @Resource
    private UompPersonInfoService uompPersonInfoService;

    @Override
    protected String getModelDesc() {
        return "人员库-组织机构";
    }

    @ApiOperation(value = "详情")
    @PostMapping("get")
    public ResultMsg<GroupUomp> get(@RequestParam String id) {
        GroupUomp groupUomp = ucompOrgGroupService.get(id);
        if (groupUomp != null && !"0".equals(groupUomp.getParentId())) {
            String parentOrgName = ucompOrgGroupService.get(groupUomp.getParentId()).getName();
            groupUomp.setParentName(parentOrgName);
        }

        return getSuccessResult(groupUomp);
    }

    @ApiOperation(value = "平行")
    @GetMapping("getTreeData")
    public List<OrgTreeUomp> getTreeData() {
        List<OrgTreeUomp> groupTreeList = getGroupTree();
        if (CollectionUtil.isEmpty(groupTreeList)) {
            groupTreeList = new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(groupTreeList)) {
            OrgTreeUomp rootGroup = new OrgTreeUomp();
            rootGroup.setName("组织");
            rootGroup.setId("0");
            rootGroup.setParentId("0");
            groupTreeList.add(rootGroup);
        }
        return groupTreeList;
    }

    @ApiOperation(value = "树形")
    @GetMapping("getOrgTree")
    public ResultMsg<List<OrgTreeUomp>> getOrgTree() {
        List<OrgTreeUomp> groupTreeList = getGroupTree();
        if (CollectionUtil.isEmpty(groupTreeList)) {
            groupTreeList = new ArrayList<>();
        }

        if (CollectionUtil.isEmpty(groupTreeList)) {
            OrgTreeUomp rootGroup = new OrgTreeUomp();
            rootGroup.setName("组织");
            rootGroup.setId("0");
            groupTreeList.add(rootGroup);
        }

        return getSuccessResult(BeanUtils.listToTree(groupTreeList));
    }

    @ApiOperation(value = "树形-人员使用")
    @GetMapping("getOrgTreePerson")
    public ResultMsg<List<OrgTreeUomp>> getOrgTreePerson() {
        List<OrgTreeUomp> groupTreeList = getGroupTree("1");
        if (CollectionUtil.isEmpty(groupTreeList)) {
            groupTreeList = new ArrayList<>();
        }

        if (CollectionUtil.isEmpty(groupTreeList)) {
            OrgTreeUomp rootGroup = new OrgTreeUomp();
            rootGroup.setName("组织");
            rootGroup.setId("0");
            groupTreeList.add(rootGroup);
        }

        return getSuccessResult(BeanUtils.listToTree(groupTreeList));
    }

    private List<OrgTreeUomp> getGroupTree() {
        List<OrgTreeUomp> groupTreeList = new ArrayList<>();
        List<GroupUomp> groupUompList = ucompOrgGroupService.getAll();
        if (CollectionUtil.isEmpty(groupUompList)){
            groupUompList = new ArrayList<>();
            initGroupUomp(groupUompList);

        }
        for (GroupUomp groupUomp : groupUompList) {
            OrgTreeUomp groupTree = new OrgTreeUomp(groupUomp);
            groupTreeList.add(groupTree);
        }
        return groupTreeList;
    }

    private void initGroupUomp(List<GroupUomp> groupUompList){
        GroupUomp groupUomp = setGroupUomp();
        ucompOrgGroupService.initUompGroup(groupUomp);
        groupUompList.add(groupUomp);
    }
    private GroupUomp setGroupUomp(){
        GroupUomp uompOrgGroup = new GroupUomp();
        uompOrgGroup.setOrgGroupId("1");
        uompOrgGroup.setParentId("0");
        uompOrgGroup.setName("运维组织");
        uompOrgGroup.setCode("uomp_ywzz");
        uompOrgGroup.setShowName("运维组织");
        uompOrgGroup.setSimple("运维组织");
        uompOrgGroup.setStatus(1);
        uompOrgGroup.setVirtual(1);
        return uompOrgGroup;
    }

    private List<OrgTreeUomp> getGroupTree(String status) {
        List<OrgTreeUomp> groupTreeList = new ArrayList<>();
        List<GroupUomp> groupUompList = ucompOrgGroupService.getAll("", status);
        for (GroupUomp groupUomp : groupUompList) {
            OrgTreeUomp groupTree = new OrgTreeUomp(groupUomp);
            groupTreeList.add(groupTree);
        }
        return groupTreeList;
    }

    @ApiOperation(value = "树形-概览图使用")
    @GetMapping("getOrgTreeAndUserNum")
    public ResultMsg<List<OrgTreeUomp>> getOrgTreeAndPersonNumber() {
        List<OrgTreeUomp> groupTreeList = getGroupTreeAndPersonNumber();
        if (CollectionUtil.isEmpty(groupTreeList)) {
            groupTreeList = new ArrayList<>();
            OrgTreeUomp rootGroup = new OrgTreeUomp();
            rootGroup.setName("组织");
            rootGroup.setId("0");
            groupTreeList.add(rootGroup);
        }

        return getSuccessResult(BeanUtils.listToTree(groupTreeList));
    }

    private List<OrgTreeUomp> getGroupTreeAndPersonNumber() {
        List<OrgTreeUomp> groupTreeList = new ArrayList<>();
        //概览图不显示隐藏
        List<GroupUomp> groupUompList = ucompOrgGroupService.getAll("", "1");
        for (GroupUomp groupUomp : groupUompList) {
            OrgTreeUomp groupTree = new OrgTreeUomp(groupUomp);
            //11-22 统计非黑，审核通过，背调非不合格，在职
            List<UompPersonInfo> uompPersonInfos = uompPersonInfoService.selectAllByOrg(groupTree.getId());
            groupTree.setUserNum(uompPersonInfos != null ? uompPersonInfos.size() : 0);

            if (uompPersonInfos != null) {
                List<OrgPersonInfoDTO> orgPersonInfos = new ArrayList<>();
                Map<String, List<BaseDTO>> map = new HashMap<>();
                for (UompPersonInfo uompPersonInfo : uompPersonInfos) {
                    if (map.containsKey(uompPersonInfo.getWorkingCompany())) {
                        List<BaseDTO> personNums = map.get(uompPersonInfo.getWorkingCompany());
                        BaseDTO baseDTO = new BaseDTO();
                        baseDTO.setId(uompPersonInfo.getId());
                        baseDTO.setName(uompPersonInfo.getPersonName());
                        personNums.add(baseDTO);
                        map.put(uompPersonInfo.getWorkingCompany(), personNums);
                    } else {
                        List<BaseDTO> personNums = new ArrayList<>();
                        BaseDTO baseDTO = new BaseDTO();
                        baseDTO.setId(uompPersonInfo.getId());
                        baseDTO.setName(uompPersonInfo.getPersonName());
                        personNums.add(baseDTO);
                        map.put(uompPersonInfo.getWorkingCompany(), personNums);
                    }
                }

                Set<String> keySet = map.keySet();
                OrgPersonInfoDTO ori = new OrgPersonInfoDTO();
                for (String s : keySet) {
                    ori = new OrgPersonInfoDTO();
                    ori.setCompany(s);
                    ori.setMembers(map.get(s));
                    orgPersonInfos.add(ori);
                }

                groupTree.setOrgPersonInfoDTOList(orgPersonInfos);
            }

            groupTreeList.add(groupTree);
        }
        return groupTreeList;
    }

    @ApiOperation(value = "排序")
    @PostMapping("changeOrder")
    public ResultMsg<String> changeOrder(@RequestBody List<GroupUomp> param) {
        ucompOrgGroupService.chageOrder(param);
        // 处理组织机构树排序
        List<GroupCustom> groupCustomList = new ArrayList<>();
        GroupCustom groupCustomGroup = new GroupCustom();
        for (GroupUomp g : param) {
            groupCustomGroup = new GroupCustom();

            org.springframework.beans.BeanUtils.copyProperties(g, groupCustomGroup);

            GroupUomp groupUomp = ucompOrgGroupService.get(g.getId());
            groupCustomGroup.setId(groupUomp.getOrgGroupId());

            groupCustomList.add(groupCustomGroup);
        }
        groupManager.chageOrder(groupCustomList);
        return getSuccessResult();
    }

    @ApiOperation(value = "添加/修改")
    @PostMapping("save")
    public ResultMsg<String> save(@RequestBody GroupUomp t) {
        String desc;
        GroupCustom group = new GroupCustom();
        org.springframework.beans.BeanUtils.copyProperties(t, group);
        if (StringUtil.isEmpty(t.getId())) {
            // 判断是否有默认组织
            if (StringUtils.isBlank(t.getParentId()) || "0".equals(t.getParentId())) {
                // 查询是否有默认组织
                GroupCustom groupCustom = groupManager.get("1");
                if (groupCustom == null || !"0".equals(groupCustom.getParentId())) {
                    // 同步远程组织机构
                    try {
                        GroupCustom groupParent = new GroupCustom();
                        groupParent.setParentId("0");
                        groupParent.setName("默认机构");
                        groupParent.setCode("1");
                        groupParent.setType(1);
                        groupParent.setId("1");
                        groupParent.setShowName("默认机构");
                        groupManager.create(groupParent);
                    } catch (Exception e) {
                        log.error("根节点同步异常");
                    }

                    t.setParentId("0");
//                    ucompOrgGroupService.create(t);
                } else {
                    t.setParentId(groupCustom.getId());
                }

            }
            desc = "添加%s成功";
            // 查询上级机构详情
            GroupUomp groupUomp = ucompOrgGroupService.get(t.getParentId());
            if (ObjectUtil.isEmpty(groupUomp)){
                throw new BusinessException("只能有一个根节点");
            }
            group.setParentId(groupUomp.getOrgGroupId());
            groupManager.create(group);
            t.setOrgGroupId(group.getId());
            ucompOrgGroupService.create(t);
        } else {
            desc = "更新%s成功";
            try {
                ucompOrgGroupService.updateById(t);
            } catch (Exception e) {
                ResultMsg errorResultMsg = ResultMsg.ERROR(e.getMessage());
                errorResultMsg.setCode("400");
                return errorResultMsg;
            }
            // 查询上级机构详情
            if ("0".equals(t.getParentId())) {
                group.setParentId("0");
            } else {
                GroupUomp groupUompParent = ucompOrgGroupService.get(t.getParentId());
                if (groupUompParent == null || StringUtils.isBlank(groupUompParent.getOrgGroupId())) {
                    ResultMsg errorResultMsg = ResultMsg.ERROR("上级组织不存在,请检查");
                    errorResultMsg.setCode("400");
                    return errorResultMsg;
                }
                group.setParentId(groupUompParent.getOrgGroupId());
            }
            GroupUomp groupUomp = ucompOrgGroupService.get(t.getId());
            if (groupUomp == null || StringUtils.isBlank(groupUomp.getOrgGroupId())) {
                ResultMsg errorResultMsg = ResultMsg.ERROR("组织数据异常,请反馈开发人员");
                errorResultMsg.setCode("400");
                return errorResultMsg;
            }
            group.setId(groupUomp.getOrgGroupId());
            groupManager.update(group);
        }
        return getSuccessResult(t.getId(), String.format(desc, getModelDesc()));
    }

    @ApiOperation(value = "删除, 支持批量删除 id 逗号分隔")
    @PostMapping("remove")
    public ResultMsg<String> remove(@RequestParam String id) {
        String[] aryIds = StringUtil.getStringAryByStr(id);
        String orgGroupId = "";
        for (String groupId : aryIds) {
            // 根据主键查询详情
            GroupUomp groupUomp = ucompOrgGroupService.get(groupId);
            if (groupUomp != null && StringUtils.isNotBlank(groupUomp.getOrgGroupId())) {
                // 删除系统组织机构
                orgGroupId = String.join(",", groupUomp.getOrgGroupId(), orgGroupId);
            }
            try {
                ucompOrgGroupService.removeById(groupId);
            } catch (Exception e) {
                ResultMsg errorResultMsg = ResultMsg.ERROR(e.getMessage());
                errorResultMsg.setCode("400");
                return errorResultMsg;
            }
        }
        if (StringUtils.isNotBlank(orgGroupId)) {
            int lastIndex = orgGroupId.lastIndexOf(",");
            orgGroupId = orgGroupId.substring(0, lastIndex);
            groupManager.removeByIds(StringUtil.getStringAryByStr(orgGroupId));
        }
        return getSuccessResult(String.format("删除%s成功", getModelDesc()));
    }


    @ApiOperation("获取工单运维组织")
    @RequestMapping({"getOrderGroup"})
    public ResultMsg<List<OrderGroup>> getOrderGroup(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return ResultMsg.SUCCESS(ucompOrgGroupService.getOrderGroup());
    }

}
