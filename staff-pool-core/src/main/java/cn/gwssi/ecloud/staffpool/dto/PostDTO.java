package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "岗位列表类")
public class PostDTO implements Serializable {
   @ApiModelProperty(value = "id")
   private String id;
   @ApiModelProperty(value = "名称")
   private String name;
   @ApiModelProperty(value = "描述")
   private String desc;
}