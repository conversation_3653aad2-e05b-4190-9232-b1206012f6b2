package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingPlanPerson;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompTrainingPlanPersonService extends Manager<String, UompTrainingPlanPerson> {

    int insertSelective(UompTrainingPlanPerson record);
    int updateByPrimaryKeySelective(UompTrainingPlanPerson record);

    void deleteByPlanId(String id);
}
