package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description="uomp_person_entry_exit")
@Data
public class UompPersonEntryExit extends BaseModel {

    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value="人员id")
    private String personId;
    @NotEmpty(message = "出境/入境不能为空")
    @ApiModelProperty(value="出境/入境")
    private String entryExit;
    @NotEmpty(message = "出入境时间不能为空")
    @ApiModelProperty(value="出入境时间")
    private String entryExitTime;
    @NotEmpty(message = "证件名称不能为空")
    @ApiModelProperty(value="证件名称")
    private String certificateName;
    @NotEmpty(message = "证件号不能为空")
    @ApiModelProperty(value="证件号")
    private String certificateNum;
    @NotEmpty(message = "出入境口岸不能为空")
    @ApiModelProperty(value="出入境口岸")
    private String entryExitPorts;

    @ApiModelProperty(value="")
    private String createOrgId;

    @ApiModelProperty(value="")
    private String updateOrgId;

    @ApiModelProperty(value="")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}