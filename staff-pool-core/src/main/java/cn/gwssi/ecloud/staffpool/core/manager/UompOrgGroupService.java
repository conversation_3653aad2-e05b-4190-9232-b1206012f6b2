package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompOrgGroup;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.core.model.OrderGroup;
import cn.gwssi.ecloudframework.base.manager.Manager;
import org.apache.avalon.framework.service.ServiceException;

import java.util.List;

public interface UompOrgGroupService extends Manager<String, GroupUomp> {

    /**
     * 排序
     *
     * @param groupUomps
     */
    void chageOrder(List<GroupUomp> groupUomps);

    List<GroupUomp> getAll(String virtual, String status);

    String removeById(String id) throws ServiceException;

    List<String> getByPath(String orgGroupId);

    String updateById(GroupUomp entity) throws ServiceException;

    List<OrderGroup> getOrderGroup();

    void initUompGroup(GroupUomp groupUomp);
}
