package cn.gwssi.ecloud.staffpool.core.model;

import cn.gwssi.ecloud.staffpool.dto.OrgPersonInfoDTO;
import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.GroupCustomForPathName;
import cn.gwssi.ecloudframework.org.api.constant.GroupTypeConstant;
import cn.gwssi.ecloudframework.org.api.model.IGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.parser.Feature;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@ApiModel(
        description = "自定义组"
)
public class OrderGroup extends BaseModel{
    private static final long serialVersionUID = -700694295167942753L;
    @ApiModelProperty("名字")
    protected String name;
    @ApiModelProperty("ID")
    protected String id;
    @ApiModelProperty("编码")
    protected String code;
    @ApiModelProperty("类型")
    protected String type;
    @ApiModelProperty("机构ID")
    protected String orgId;

    @ApiModelProperty("组下人员")
    protected List<OrderGroup> childrens;


    public OrderGroup(String name, String id, String code, String type, String orgId) {
        this.name = name;
        this.id = id;
        this.code = code;
        this.type = type;
        this.orgId = orgId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void String(String type) {
        this.type = type;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public List<OrderGroup> getChildrens() {
        return childrens;
    }

    public void setChildrens(List<OrderGroup> childrens) {
        this.childrens = childrens;
    }

    public void setType(String type) {
        this.type = type;
    }
}
