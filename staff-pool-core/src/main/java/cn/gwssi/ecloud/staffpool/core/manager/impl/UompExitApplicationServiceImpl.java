package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompExitAcceptInfoMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompExitAdmissionRelationMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompExitApplicationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.core.entity.UompExitApplication;
import cn.gwssi.ecloud.staffpool.core.manager.UompExitApplicationService;
import cn.gwssi.ecloud.staffpool.dto.AcceptListBean;
import cn.gwssi.ecloud.staffpool.dto.ExitApplyDTO;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UompExitApplicationServiceImpl extends BaseManager<String, UompExitApplication> implements UompExitApplicationService {

    @Resource
    private UompExitApplicationMapper uompExitApplicationMapper;
    @Resource
    private UompExitAdmissionRelationMapper uomExitAdmissionRelationMapper;
    @Resource
    private UompExitAcceptInfoMapper uompExitAcceptInfoMapper;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;

    @Override
    public int insertSelective(UompExitApplication record) {
        return uompExitApplicationMapper.insertSelective(record);
    }

    @Override
    public ExitApplyDTO getExitApply(String id) {
        //查询该入场人员关联的退场id
        String exitId = uomExitAdmissionRelationMapper.selectExitApplyIdByApplyPedrsonId(id);

        //声明查询详情sql
        ExitApplyDTO exitApplyDTO = uompExitApplicationMapper.selectAllById(exitId);

        //详情信息中有些字段的二次处理
        if (exitApplyDTO != null) {
            String out_reason_file = exitApplyDTO.getOutReasonFileJson();
            if (StringUtils.isNotEmpty(out_reason_file)) {
                exitApplyDTO.setOutReasonFile(JSONObject.parseObject(out_reason_file, List.class));
            }

            //查询该退场的子表
            List<AcceptListBean> acceptListBeanList = uompExitAcceptInfoMapper.selectAllByExitApplyId(exitApplyDTO.getId());
            if (!CollectionUtils.isEmpty(acceptListBeanList)){
                for (AcceptListBean accept : acceptListBeanList) {
                    accept.setAcceptContextFile(JSONObject.parseObject(accept.getAcceptContextFileJson(), List.class));
                }
                exitApplyDTO.setAcceptList(acceptListBeanList);
            }

            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：biz_id,参数4：create_by）
            List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", null, exitApplyDTO.getInstId(), exitApplyDTO.getCreateBy());
            //脱敏
            if (!CollectionUtils.isEmpty(ruleList)) {
                for (UompDesensitization rule : ruleList) {
                    //如果有关于手机号的脱敏规则则转换手机号
                    if (StringUtils.isNotEmpty(exitApplyDTO.getTel()) && "TEL".equals(rule.getDesFieldCode())) {
                        exitApplyDTO.setTel(conversionUtil.conversion(rule, exitApplyDTO.getTel()));
                    }
                }
            }
        }

        return exitApplyDTO;
    }

    @Override
    public Integer countByOutTime(String time) {
        return uompExitApplicationMapper.countByOutTime(time);
    }
}
