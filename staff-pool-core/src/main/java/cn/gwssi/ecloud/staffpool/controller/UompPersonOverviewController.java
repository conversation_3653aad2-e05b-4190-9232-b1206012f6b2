package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Api(description = "人员库概览")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/personOverview")
public class UompPersonOverviewController extends BaseController<UompPersonInfo> {

    @Resource
    private UompTrainingRecordPersonService uompTrainingRecordPersonService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompTempAdmissionService uompTempAdmissionService;
    @Resource
    private UompAdmissionPersonService uompTempAdmissionPersonService;
    @Resource
    private UompExitApplicationService uompExitApplicationService;
    @Resource
    private OrgRelationService orgRelationService;
    @Resource
    private UompSupplierManagementService uompSupplierManagementService;
    @Resource
    private UompAdmissionApplicationService uompAdmissionApplicationService;
    @Resource
    private UompAdmissionPersonService uompAdmissionPersonService;

    @Override
    protected String getModelDesc() {
        return "人员库概览";
    }

    @ApiOperation(value = "人员培训记录接口")
    @GetMapping(value = "/trainingList")
    public ResultMsg<List<TrainingDTO>> trainingList() {
        return ResultMsg.SUCCESS(uompTrainingRecordPersonService.trainingList());
    }

    @ApiOperation(value = "人员背调情况接口")
    @GetMapping(value = "/backgroundList")
    public ResultMsg<BackgroundListDTO> backgroundList() {
        return ResultMsg.SUCCESS(uompPersonInfoService.backgroundList());
    }

    @ApiOperation(value = "现场人员情况接口")
    @GetMapping(value = "/personStatusList")
    public ResultMsg<PersonStatusListDTO> personStatusList() {
        PersonStatusListDTO personStatusListDTO = new PersonStatusListDTO();

        // 临时
        List<PersonStatusListDTO.MonthNumBean> tempList = new ArrayList<>();
        // 入场
        List<PersonStatusListDTO.MonthNumBean> entryList = new ArrayList<>();
        // 退场
        List<PersonStatusListDTO.MonthNumBean> exitList = new ArrayList<>();

        int i = -5;
        while (i <= 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Calendar ca1 = Calendar.getInstance();
            ca1.setTime(new Date());
            ca1.add(Calendar.MONTH, i);
            String time = sdf.format(ca1.getTime());
//            timeList.add(time);
            i++;

            //处理数据
            //临时
            PersonStatusListDTO.MonthNumBean monthNumBean = new PersonStatusListDTO.MonthNumBean();
            monthNumBean.setMonth(time);
            monthNumBean.setNum(uompTempAdmissionService.countByRealVisitTime(time));
            tempList.add(monthNumBean);

            //入场
            monthNumBean = new PersonStatusListDTO.MonthNumBean();
            monthNumBean.setMonth(time);
            monthNumBean.setNum(uompTempAdmissionPersonService.countByInTime(time));
            entryList.add(monthNumBean);

            //退场
            monthNumBean = new PersonStatusListDTO.MonthNumBean();
            monthNumBean.setMonth(time);
            monthNumBean.setNum(uompExitApplicationService.countByOutTime(time));
            exitList.add(monthNumBean);
        }

        //结果数据封装
        personStatusListDTO.setEntryList(entryList);
        personStatusListDTO.setExitList(exitList);
        personStatusListDTO.setTempList(tempList);
        return ResultMsg.SUCCESS(personStatusListDTO);
    }

    @ApiOperation(value = "人员学历构成接口")
    @GetMapping(value = "/getEducationList")
    public ResultMsg<List<PercentageDTO>> getEducationList() {
        return ResultMsg.SUCCESS(uompPersonInfoService.getEducationList());
    }

    @ApiOperation(value = "运维组分类接口")
    @GetMapping(value = "/getManagerList")
    public ResultMsg<List<ManagerDTO>> getManagerList() {
        return ResultMsg.SUCCESS(orgRelationService.selectNumByStatus());
    }

    @ApiOperation(value = "人员年龄构成接口")
    @GetMapping(value = "/personAgeList")
    public ResultMsg<List<PercentageDTO>> personAgeList() {
        return ResultMsg.SUCCESS(uompPersonInfoService.personAgeList());
    }

    @ApiOperation(value = "人员库概览-根据供应商分类")
    @GetMapping(value = "/groupBySupplier")
    public ResultMsg<GroupBySupplierDTO> groupBySupplier() {
        return ResultMsg.SUCCESS(uompSupplierManagementService.groupBySupplier());
    }

    @ApiOperation(value = "人员结构")
    @GetMapping(value = "/personStructure")
    public ResultMsg<PersonStructureDTO> personStructure() {
        return ResultMsg.SUCCESS(uompPersonInfoService.personStructure());
    }

    @ApiOperation(value = "技术方向分布")
    @GetMapping(value = "/technicalDirection")
    public ResultMsg<List<TechnicalDirectionDTO>> technicalDirection() {
        return ResultMsg.SUCCESS(uompPersonInfoService.technicalDirection());
    }

    @ApiOperation(value = "人员驻场分布")
    @GetMapping(value = "/getPersonLocation")
    public ResultMsg<List<PercentageDTO>> getPersonLocation() {
        return ResultMsg.SUCCESS(uompAdmissionApplicationService.getPersonLocation());
    }

    @ApiOperation(value = "服务时长分布")
    @GetMapping(value = "/getPersonInTime")
    public ResultMsg<List<PercentageDTO>> getPersonInTime() {
        return ResultMsg.SUCCESS(uompAdmissionPersonService.getPersonInTime());
    }

    @ApiOperation(value = "人员驻场情况")
    @GetMapping(value = "/admissionPerson")
    public ResultMsg<AdmissionPersonDTO> admissionPerson() {
        return ResultMsg.SUCCESS(uompAdmissionApplicationService.admissionPerson());
    }

    @ApiOperation(value = "临时入场人员趋势")
    @GetMapping(value = "/tempPersonTrend")
    public ResultMsg<List<PercentageDTO>> tempPersonTrend() {
        return ResultMsg.SUCCESS(uompTempAdmissionService.tempPersonTrend());
    }

    @ApiOperation(value = "服务商服务年限")
    @GetMapping(value = "/supplierServiceYears")
    public ResultMsg<List<ListAndNameDto>> supplierServiceYears() {
        return ResultMsg.SUCCESS(uompSupplierManagementService.supplierServiceYears());
    }

    @ApiOperation(value = "服务商详情")
    @PostMapping(value = "/supplierInfo")
    public ResultMsg<SupplierInfoDTO> supplierInfo(@RequestParam(value = "supplierId") String supplierId) {
        return ResultMsg.SUCCESS(uompSupplierManagementService.supplierInfo(supplierId));
    }
}
