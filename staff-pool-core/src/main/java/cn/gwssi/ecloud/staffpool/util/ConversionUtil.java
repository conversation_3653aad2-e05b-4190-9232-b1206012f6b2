package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据规则进行脱敏转换(列表批量)（/desensitize/conversionBatch）
 */
@Component
public class ConversionUtil {

    public JSONArray conversionBatch(List<UompDesensitization> ruleList, JSONArray pageRows) {
        JSONArray resultJSONArray = new JSONArray();
        //该方法实现前提是，field的key要和返回列表中脱敏字段保持一致

        for (int i = 0; i < pageRows.size(); i++) {
            JSONObject row = pageRows.getJSONObject(i);
            for (UompDesensitization rule : ruleList) {
                String fieldCode = rule.getDesFieldCode();
                String value = row.getString(fieldCode);
                //数据为空跳过
                if (value != null) {
                    //解析脱敏规则
                    String desRuleMode = rule.getDesRuleMode();
                    //看看是替换还是正则
                    //1.替换
                    if ("0".equals(desRuleMode)) {
                        JSONArray desRuleList = JSON.parseArray(rule.getDesRuleJson());
                        for (int k = 0; k < desRuleList.size(); k++) {
                            JSONObject desRule = desRuleList.getJSONObject(k);
                            Integer beginIndex = desRule.getInteger("begin");
                            Integer endIndex = desRule.getInteger("end");
                            String replace = desRule.getString("replace");
                            //截取字符
                            //如果begin>value长度则不做脱敏，如果end > value长度，按照value的长度算，
                            if (beginIndex < value.length()) {
                                if (endIndex > value.length()) {
                                    endIndex = value.length();
                                }
                                //中间替换数量截取
                                int index = endIndex - beginIndex;
                                StringBuilder builder = new StringBuilder();

                                builder.append(value.substring(0, beginIndex - 1));
                                int j = 0;
                                while (j < index + 1) {
                                    builder.append(replace);
                                    j++;
                                }

                                builder.append(value.substring(endIndex));
                                value = String.valueOf(builder);
                            }
                        }
                    }
                    //2.正则
                    if ("1".equals(desRuleMode)) {
                        //错误的正则，不做脱敏处理了
                        try {
                            JSONObject desRuleRegx = JSONObject.parseObject(rule.getDesRuleRegx());
                            String regex = desRuleRegx.getString("regex");
                            String replace = desRuleRegx.getString("replace");

                            Pattern pattern = Pattern.compile(regex);
                            Matcher matcher = pattern.matcher(value);
                            value = matcher.replaceAll(replace);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    //查看敏感词过滤是否有值(如果有值，替换词也必然有值)
                    if (StringUtils.isNotEmpty(rule.getSensitiveWords())) {
                        //将过滤词分隔成数组，固定用|分隔
                        List<String> sensitive_words = Arrays.asList(rule.getSensitiveWords().split("\\|"));
                        for (String it : sensitive_words) {
                            value = value.replace(it, rule.getSensitiveReplaceWords());
                        }
                    }

                    row.put(fieldCode, value);
                }
            }
            resultJSONArray.add(row);
        }
        return resultJSONArray;
    }

    public String conversion(UompDesensitization rule, String value) {
        //解析脱敏规则
        String desRuleMode = rule.getDesRuleMode();
        //看看是替换还是正则
        //1.替换
        if ("0".equals(desRuleMode)) {
            JSONArray desRuleList = JSON.parseArray(rule.getDesRuleJson());
            for (int k = 0; k < desRuleList.size(); k++) {
                JSONObject desRule = desRuleList.getJSONObject(k);
                Integer beginIndex = desRule.getInteger("begin");
                Integer endIndex = desRule.getInteger("end");
                String replace = desRule.getString("replace");
                //截取字符
                //如果begin>value长度则不做脱敏，如果end > value长度，按照value的长度算，
                if (beginIndex < value.length()) {
                    if (endIndex > value.length()) {
                        endIndex = value.length();
                    }
                    //中间替换数量截取
                    int index = endIndex - beginIndex;
                    StringBuilder builder = new StringBuilder();

                    builder.append(value.substring(0, beginIndex - 1));
                    int j = 0;
                    while (j < index + 1) {
                        builder.append(replace);
                        j++;
                    }

                    builder.append(value.substring(endIndex));
                    value = String.valueOf(builder);
                }
            }
        }
        //2.正则
        if ("1".equals(desRuleMode)) {
            //错误的正则，不做脱敏处理了
            try {
                JSONObject desRuleRegx = JSONObject.parseObject(rule.getDesRuleRegx());
                String regex = desRuleRegx.getString("regex");
                String replace = desRuleRegx.getString("replace");

                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(value);
                value = matcher.replaceAll(replace);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (StringUtils.isNotEmpty(rule.getSensitiveWords())) {
            //将过滤词分隔成数组，固定用|分隔
            List<String> sensitive_words = Arrays.asList(rule.getSensitiveWords().split("\\|"));
            for (String it : sensitive_words) {
                value = value.replace(it, rule.getSensitiveReplaceWords());
            }
        }

        //查看敏感词过滤是否有值(如果有值，替换词也必然有值)
        if (StringUtils.isNotEmpty(rule.getSensitiveWords())) {
            //将过滤词分隔成数组，固定用|分隔
            List<String> sensitive_words = Arrays.asList(rule.getSensitiveWords().split("\\|"));
            for (String it : sensitive_words) {
                value = value.replace(it, rule.getSensitiveReplaceWords());
            }
        }

        return value;
    }
}
