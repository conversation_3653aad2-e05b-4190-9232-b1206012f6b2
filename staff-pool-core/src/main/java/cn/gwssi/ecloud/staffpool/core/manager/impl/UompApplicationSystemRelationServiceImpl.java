package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.dao.UompApplicationSystemRelationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation;
import cn.gwssi.ecloud.staffpool.core.manager.UompApplicationSystemRelationService;
@Service
public class UompApplicationSystemRelationServiceImpl extends BaseManager<String, UompApplicationSystemRelation> implements UompApplicationSystemRelationService{

    @Resource
    private UompApplicationSystemRelationMapper uompApplicationSystemRelationMapper;

    @Override
    public int insertSelective(UompApplicationSystemRelation record) {
        return uompApplicationSystemRelationMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompApplicationSystemRelation record) {
        return uompApplicationSystemRelationMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateDelFlag(UompApplicationSystemRelation systemRelation) {
        return uompApplicationSystemRelationMapper.updateDelFlag(systemRelation);
    }

}
