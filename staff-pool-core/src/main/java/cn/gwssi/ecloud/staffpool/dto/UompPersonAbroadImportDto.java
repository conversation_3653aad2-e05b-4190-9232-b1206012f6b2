package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonAbroadImportDto {
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "证件名称")
    private String certificateName;

    @ExcelProperty(value = "证件号码")
    private String certificateNum;

    @ExcelProperty(value = "签发地")
    private String issueAt;

    @ExcelProperty(value = "证件有效期起始时间")
    private String startTime;

    @ExcelProperty(value = "证件有效期终止时间")
    private String endTime;
}