package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;

import java.util.Date;

public class UompPermissionOutDetails extends BaseModel {

    private String outUserId;

    private String outApplyType;

    private String empowerResourceIds;

    private String empowerResourceJson;

    private String permission;

    private String empowerBeginTime;

    private String empowerEndTime;

    private String authorizationStatus;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public String getOutApplyType() {
        return outApplyType;
    }

    public void setOutApplyType(String outApplyType) {
        this.outApplyType = outApplyType;
    }

    public String getEmpowerResourceIds() {
        return empowerResourceIds;
    }

    public void setEmpowerResourceIds(String empowerResourceIds) {
        this.empowerResourceIds = empowerResourceIds;
    }

    public String getEmpowerResourceJson() {
        return empowerResourceJson;
    }

    public void setEmpowerResourceJson(String empowerResourceJson) {
        this.empowerResourceJson = empowerResourceJson;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getEmpowerBeginTime() {
        return empowerBeginTime;
    }

    public void setEmpowerBeginTime(String empowerBeginTime) {
        this.empowerBeginTime = empowerBeginTime;
    }

    public String getEmpowerEndTime() {
        return empowerEndTime;
    }

    public void setEmpowerEndTime(String empowerEndTime) {
        this.empowerEndTime = empowerEndTime;
    }

    public String getAuthorizationStatus() {
        return authorizationStatus;
    }

    public void setAuthorizationStatus(String authorizationStatus) {
        this.authorizationStatus = authorizationStatus;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", outUserId=").append(outUserId);
        sb.append(", outApplyType=").append(outApplyType);
        sb.append(", empowerResourceIds=").append(empowerResourceIds);
        sb.append(", empowerResourceJson=").append(empowerResourceJson);
        sb.append(", permission=").append(permission);
        sb.append(", empowerBeginTime=").append(empowerBeginTime);
        sb.append(", empowerEndTime=").append(empowerEndTime);
        sb.append(", authorizationStatus=").append(authorizationStatus);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompPermissionOutDetails other = (UompPermissionOutDetails) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getOutUserId() == null ? other.getOutUserId() == null : this.getOutUserId().equals(other.getOutUserId()))
                && (this.getOutApplyType() == null ? other.getOutApplyType() == null : this.getOutApplyType().equals(other.getOutApplyType()))
                && (this.getEmpowerResourceIds() == null ? other.getEmpowerResourceIds() == null : this.getEmpowerResourceIds().equals(other.getEmpowerResourceIds()))
                && (this.getEmpowerResourceJson() == null ? other.getEmpowerResourceJson() == null : this.getEmpowerResourceJson().equals(other.getEmpowerResourceJson()))
                && (this.getPermission() == null ? other.getPermission() == null : this.getPermission().equals(other.getPermission()))
                && (this.getEmpowerBeginTime() == null ? other.getEmpowerBeginTime() == null : this.getEmpowerBeginTime().equals(other.getEmpowerBeginTime()))
                && (this.getEmpowerEndTime() == null ? other.getEmpowerEndTime() == null : this.getEmpowerEndTime().equals(other.getEmpowerEndTime()))
                && (this.getAuthorizationStatus() == null ? other.getAuthorizationStatus() == null : this.getAuthorizationStatus().equals(other.getAuthorizationStatus()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOutUserId() == null) ? 0 : getOutUserId().hashCode());
        result = prime * result + ((getOutApplyType() == null) ? 0 : getOutApplyType().hashCode());
        result = prime * result + ((getEmpowerResourceIds() == null) ? 0 : getEmpowerResourceIds().hashCode());
        result = prime * result + ((getEmpowerResourceJson() == null) ? 0 : getEmpowerResourceJson().hashCode());
        result = prime * result + ((getPermission() == null) ? 0 : getPermission().hashCode());
        result = prime * result + ((getEmpowerBeginTime() == null) ? 0 : getEmpowerBeginTime().hashCode());
        result = prime * result + ((getEmpowerEndTime() == null) ? 0 : getEmpowerEndTime().hashCode());
        result = prime * result + ((getAuthorizationStatus() == null) ? 0 : getAuthorizationStatus().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}