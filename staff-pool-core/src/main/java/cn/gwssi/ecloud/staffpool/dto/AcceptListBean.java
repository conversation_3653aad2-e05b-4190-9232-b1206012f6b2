package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AcceptListBean {

    @ApiModelProperty(value="主键")
    private String acceptId;
    @ApiModelProperty(value="项目名称")
    private String engagementProjectName;
    @ApiModelProperty(value="交接人名")
    private String acceptUserName;
    @ApiModelProperty(value="交接内容")
    private String acceptContext;
    private String acceptContextFileJson;
    @ApiModelProperty(value="交接附件")
    private List<FileInfoBean> acceptContextFile;
}