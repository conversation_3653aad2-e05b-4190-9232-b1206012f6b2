package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonEducationalImportDto{
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "起始时间")
    private String educationBeginTime;
    @ExcelProperty(value = "终止时间")
    private String educationEndTime;
    @ExcelProperty(value = "就读院校")
    private String school;
    @ExcelProperty(value = "就读专业")
    private String major;
    @ExcelProperty(value = "学历/学位")
    private String educationBackground;
    @ExcelProperty(value = "是否统招")
    private String educationForm;
    @ExcelProperty(value = "证书编号")
    private String certificateNum;
}