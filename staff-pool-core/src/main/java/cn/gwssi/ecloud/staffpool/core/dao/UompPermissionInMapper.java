package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionIn;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

public interface UompPermissionInMapper extends BaseDao<String, UompPermissionIn> {

    int insertSelective(UompPermissionIn record);

    int updateAuthorizationStatus(@Param("status")String status, @Param("id")String id);
}