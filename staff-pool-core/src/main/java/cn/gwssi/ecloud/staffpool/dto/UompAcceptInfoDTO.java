package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="通过退场申请id查出相关接收人信息")
@Data
public class UompAcceptInfoDTO implements Serializable {

    @ApiModelProperty(value="id")
    private String id;
    @ApiModelProperty(value="名称")
    private String name;
    @ApiModelProperty(value="机构")
    private String orgId;
    @ApiModelProperty(value="类型")
    private String type;
}
