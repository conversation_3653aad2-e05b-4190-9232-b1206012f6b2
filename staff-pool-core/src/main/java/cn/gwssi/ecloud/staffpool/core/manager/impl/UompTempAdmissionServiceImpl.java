package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.TempApplyVO;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.UompHistoryRecordMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompTempAdmissionMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.core.entity.UompHistoryRecord;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission;
import cn.gwssi.ecloud.staffpool.core.manager.UompApplicationSystemManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompTempAdmissionService;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DateUtils;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloudbpm.module.cmdb.api.service.ICMDBService;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.base.rest.util.LogOperateUtil;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.api.model.dto.SysFileDTO;
import cn.gwssi.ecloudframework.sys.api.service.SysFileService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class UompTempAdmissionServiceImpl extends BaseManager<String, UompTempAdmission> implements UompTempAdmissionService {

    @Resource
    private UompTempAdmissionMapper uompTempAdmissionMapper;
    @Resource
    private UompHistoryRecordMapper uompHistoryRecordMapper;
    @Resource
    private SysFileService sysFileService;
    @Resource
    private UompApplicationSystemManagementService uomApplicationSystemManagementService;
    @Resource
    private UompSupplierManagementService uompSupplierManagementService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private ICMDBService icmdbService;

    @Override
    public int insertSelective(UompTempAdmission record) {
        return uompTempAdmissionMapper.insertSelective(record);
    }

    @Override
    public int updateByPersonNameAndCard(UompTempAdmission record) {
        return uompTempAdmissionMapper.updateByPersonNameAndCard(record);
    }

    @Override
    public Integer countByRealVisitTime(String time) {
        return uompTempAdmissionMapper.countByRealVisitTime(time);
    }

    @Override
    public ResponsePageData<TempApplyDTO> getTempApplyList(TempApplyVO tempApplyVO) {
        ResponsePageData<TempApplyDTO> pageResult = new ResponsePageData<>();

        PageHelper.startPage(tempApplyVO.getPageNo(), tempApplyVO.getPageSize());

        // 到访日期时间段
        if (!StringUtils.isEmpty(tempApplyVO.getRealVisitTime())) {
            List<String> realVisitTime = Arrays.asList(tempApplyVO.getRealVisitTime().split(","));
            String realVisitTimeBegin = realVisitTime.get(0);
            String realVisitTimeEnd = realVisitTime.get(1);
            if (!StringUtils.isEmpty(realVisitTimeBegin)) {
                tempApplyVO.setRealVisitTimeBegin(realVisitTimeBegin);
            }
            if (!StringUtils.isEmpty(realVisitTimeEnd)) {
                tempApplyVO.setRealVisitTimeEnd(realVisitTimeEnd);
            }
        }

        // 就职公司
        if (!StringUtils.isEmpty(tempApplyVO.getWorkingCompany())) {
            tempApplyVO.setWorkingCompanyList(Arrays.asList(tempApplyVO.getWorkingCompany().split(",")));
        }

        // 参与项目
        if (!StringUtils.isEmpty(tempApplyVO.getInvolvedProject())) {
            tempApplyVO.setInvolvedProjectList(Arrays.asList(tempApplyVO.getInvolvedProject().split(",")));
        }

        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.isEmpty()) {
            return pageResult;
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                tempApplyVO.setIfSupplier("1");
                tempApplyVO.setSupplierId(workCompany.get(0).getWorkingCompanyId());
            }
        } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) {
            tempApplyVO.setCreatedBy(user.getUserId());
        } else {
            return pageResult;
        }

        tempApplyVO.setCreatedByStatus(user.getUserId());

        List<TempApplyDTO> tempApplyDTOS = uompTempAdmissionMapper.selectAllBySelective(tempApplyVO);
        PageInfo<TempApplyDTO> p = new PageInfo<>(tempApplyDTOS);
        //如果分页结果为空返回空列表，否则遍历其中数据进一步处理
        tempApplyDTOS = getTempApplyDTOS(tempApplyDTOS);

        pageResult.setCode(200);
        pageResult.setMsg("success");
        pageResult.setTotal(p.getTotal());
        pageResult.setRows(tempApplyDTOS);
        pageResult.setPage(tempApplyVO.getPageNo());
        pageResult.setPageSize(tempApplyVO.getPageSize());
        return pageResult;
    }

    @Override
    public Integer deleteTempApply(String id) {
        //获取当前登录用户信息
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompTempAdmission uompTempAdmission = new UompTempAdmission();
        uompTempAdmission.setId(id);
        uompTempAdmission.setUpdateBy(user.getUserId());
        uompTempAdmission.setUpdateTime(new Date());
        uompTempAdmission.setUpdateOrgId(user.getOrgId());
        uompTempAdmission.setDelFlag("1");

        return uompTempAdmissionMapper.update(uompTempAdmission);
    }

    @Override
    public InfoByNameDTO getInfoByName(String personName) {
        return uompTempAdmissionMapper.selectInfoByName(personName);
    }

    @Override
    public Boolean addBlackList(String id, String reason) {
        LogOperateUtil.setValue("id", id);
        LogOperateUtil.setValue("reason", reason);
        //获取当前登录用户信息
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        String fullname = user.getFullname();

        //根据id查出人名和身份证号，将该人名和身份证号的所有记录都置为加入黑名单
        UompTempAdmission ucompTempAdmission = uompTempAdmissionMapper.selectPersonById(id);
        String personName = ucompTempAdmission.getPersonName();
        String personCard = ucompTempAdmission.getPersonCard();

        //加入黑名单。blacklist 改为 1
        uompTempAdmissionMapper.updateBlacklistAndBlacklistReasonByPersonNameAndPersonCard(reason, personName, personCard);

        //记录历史操作记录  type = 0  黑名单
        String hisId = IdUtil.getSuid();

        UompHistoryRecord uompHistoryRecord = new UompHistoryRecord();
        uompHistoryRecord.setId(hisId);
        uompHistoryRecord.setBizId(id);
        uompHistoryRecord.setBizType("0");
        uompHistoryRecord.setOperatorId(userId);
        uompHistoryRecord.setOperatorName(fullname);
        uompHistoryRecord.setOperatorMessage("加入黑名单");
        uompHistoryRecord.setOperatorReason(reason);
        uompHistoryRecord.setOperatorTime(new Date(System.currentTimeMillis()));
        uompHistoryRecord.setCreateBy(userId);
        uompHistoryRecord.setCreateTime(new Date(System.currentTimeMillis()));
        uompHistoryRecord.setDelFlag("0");
        return uompHistoryRecordMapper.insertSelective(uompHistoryRecord) > 0;
    }

    @Override
    public UompTempAdmissionInfoDTO getInfoById(String id) {
        UompTempAdmissionInfoDTO uompTempAdmissionInfoDTO = uompTempAdmissionMapper.selectAllById(id);

        //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：biz_id,参数4：create_by）
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", null, uompTempAdmissionInfoDTO.getInstId(), uompTempAdmissionInfoDTO.getCreateBy());
        //脱敏
        if (!CollectionUtils.isEmpty(ruleList)) {
            for (UompDesensitization rule : ruleList) {
                //如果有关于手机号的脱敏规则则转换手机号
                if (StringUtils.isNotEmpty(uompTempAdmissionInfoDTO.getTel()) && "tel".equals(rule.getDesFieldCode())) {
                    uompTempAdmissionInfoDTO.setTel(conversionUtil.conversion(rule, uompTempAdmissionInfoDTO.getTel()));
                }
                //如果有关于身份证号的脱敏规则则转换身份证号
                if (StringUtils.isNotEmpty(uompTempAdmissionInfoDTO.getPersonCard()) && "personCard".equals(rule.getDesFieldCode())) {
                    uompTempAdmissionInfoDTO.setPersonCard(conversionUtil.conversion(rule, uompTempAdmissionInfoDTO.getPersonCard()));
                }
            }
        }

        return uompTempAdmissionInfoDTO;
    }

    @Override
    public Boolean checkBlackByCard(String personCard) {
        Integer countAll = uompTempAdmissionMapper.coutByPersonCard(personCard);

        return countAll == 0;
    }

    @Override
    public ResponsePageData<TempApplyDTO> personInfoOverview(TempApplyVO tempApplyVO) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        ResponsePageData<TempApplyDTO> pageResult = new ResponsePageData<>();

        // 根据申请id查询用户信息
        TempApplyDTO tempApplyDTO = uompTempAdmissionMapper.selectPersonCardAndPersonNameById(tempApplyVO.getId());

        PageHelper.startPage(tempApplyVO.getPageNo(), tempApplyVO.getPageSize());

        String personCard = tempApplyDTO.getPersonCard(), personName = "";
        if (StringUtils.isBlank(personCard)) {
            personName = tempApplyDTO.getPersonName();
        }
        List<TempApplyDTO> tempApplyDTOS = uompTempAdmissionMapper.selectAllByPersonCardOrPersonName(personCard, personName, user.getUserId());
        PageInfo<TempApplyDTO> p = new PageInfo<>(tempApplyDTOS);
        //如果分页结果为空返回空列表，否则遍历其中数据进一步处理
        tempApplyDTOS = getTempApplyDTOS(tempApplyDTOS);

        pageResult.setCode(200);
        pageResult.setMsg("success");
        pageResult.setTotal(p.getTotal());
        pageResult.setRows(tempApplyDTOS);
        pageResult.setPage(tempApplyVO.getPageNo());
        pageResult.setPageSize(tempApplyVO.getPageSize());
        return pageResult;
    }

    @Nullable
    private List<TempApplyDTO> getTempApplyDTOS(List<TempApplyDTO> tempApplyDTOS) {
        tempApplyDTOS = getTempApplyDTOS(tempApplyDTOS, desRuleUtil, conversionUtil);
        return tempApplyDTOS;
    }

    @Nullable
    public static List<TempApplyDTO> getTempApplyDTOS(List<TempApplyDTO> tempApplyDTOS, DesRuleUtil desRuleUtil, ConversionUtil conversionUtil) {
        if (!CollectionUtils.isEmpty(tempApplyDTOS)) {
            //获取脱敏配置信息（参数1：UOMP_PERSON_INFO，参数2(针对具体字段的时候才填)：null，参数3：null,参数4：null(参数3，4只有在详情的时候才输入)）
            //该列表只有 手机号,身份证号 需要脱敏
            List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", null, null);

            if (!CollectionUtils.isEmpty(ruleList)) {
                tempApplyDTOS = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(tempApplyDTOS))).toJSONString(), TempApplyDTO.class);
            }
        }
        return tempApplyDTOS;
    }

    @Override
    public List<TempApplyDTO> exportInfo(TempApplyVO tempApplyVO) {
        return uompTempAdmissionMapper.selectAllBySelectiveExport(tempApplyVO);
    }

    @Override
    public ResultMsg upload(MultipartFile file) throws Exception {
        int total = 0;

        //获取当前登录用户信息
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String fullname = user.getFullname();
        String orgId = user.getOrgId();

        //文件类型判断
        if (null == file.getOriginalFilename()
                || (!file.getOriginalFilename().endsWith(".xls")
                && !file.getOriginalFilename().endsWith(".xlsx"))) {
            return ResultMsg.ERROR("文件格式错误！");
        }

        // 查询应用系统
//        Map<String, String> projectBySupplierMap = new HashMap<>();
//        List<ProjectBySupplierDTO> projectBySupplierDTOs = uomApplicationSystemManagementService.selectIdAndNameByAll();
//        for (ProjectBySupplierDTO projectBySupplier : projectBySupplierDTOs) {
//            projectBySupplierMap.put(projectBySupplier.getProjectName(), projectBySupplier.getId());
//        }
        //查询应用系统 12-18 修改bug192388
        Map<String, String> projectBySupplierMap = makeSystemByCmdb();

        // 查询就职公司
        Map<String, String> supplierMap = new HashMap<>();
        List<SupplierManagementDTO> supplierList = uompSupplierManagementService.selectIdAndSupplierName();
        for (SupplierManagementDTO supplierManagementDTO : supplierList) {
            supplierMap.put(supplierManagementDTO.getSupplierName(), supplierManagementDTO.getSupplierName());
        }

        // 异常列表
        List<Map<String, String>> errorList = new ArrayList<>();

        // 入库列表
        List<UompTempAdmission> uompTempAdmissionUploadList = new ArrayList<>();

        UompTempAdmission uompTempAdmissionUpload;
        List<UompTempAdmissionDTO> uompTempAdmissionList = EasyExcel.read(file.getInputStream()).head(UompTempAdmissionDTO.class).sheet(0).headRowNumber(1).doReadSync();
        UompTempAdmissionDTO uompTempAdmission;
        Map<String, String> errorMap;
        boolean flag = true;
        for (int i = 0; i < uompTempAdmissionList.size(); i++) {
            uompTempAdmissionUpload = new UompTempAdmission();
            uompTempAdmission = uompTempAdmissionList.get(i);

            // 校验姓名
            if (StringUtils.isBlank(uompTempAdmission.getPersonName())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第2列");
                errorMap.put("message", "姓名不能为空");
                errorList.add(errorMap);
                flag = false;
            }

            // 身份证
            if (StringUtils.isBlank(uompTempAdmission.getPersonCard())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第3列");
                errorMap.put("message", "身份证不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompTempAdmission.getPersonCard().length() != 18) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第3列");
                errorMap.put("message", "请填写正确长度的身份证号");
                errorList.add(errorMap);
                flag = false;
            }

            // 联系方式
            if (StringUtils.isBlank(uompTempAdmission.getTel())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第4列");
                errorMap.put("message", "联系方式不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompTempAdmission.getTel().length() > 15) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第4列");
                errorMap.put("message", "联系方式不能超过15位");
                errorList.add(errorMap);
                flag = false;
            }

            // 就职公司
            if (StringUtils.isBlank(uompTempAdmission.getWorkingCompany())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第5列");
                errorMap.put("message", "就职公司不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompTempAdmission.getWorkingCompany().length() > 100) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第5列");
                errorMap.put("message", "就职公司不能超过100位");
                errorList.add(errorMap);
                flag = false;
            } else if (!supplierMap.containsKey(uompTempAdmission.getWorkingCompany())){
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第5列");
                errorMap.put("message", "就职公司不存在,请输入系统中已维护的公司");
                errorList.add(errorMap);
                flag = false;
            } else {
                uompTempAdmissionUpload.setWorkingCompanyId(supplierMap.get(uompTempAdmission.getWorkingCompany()));
            }

            // 预计到访时间
            if (uompTempAdmission.getPlanVisitTime() == null) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第6列");
                errorMap.put("message", "预计到访时间不能为空或者时间日期格式不正确");
                errorList.add(errorMap);
                flag = false;
            }

            //接待人
            if (StringUtils.isBlank(uompTempAdmission.getAcceptName())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第7列");
                errorMap.put("message", "接待人不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompTempAdmission.getAcceptName().length() > 60) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第7列");
                errorMap.put("message", "接待人不能超过60位");
                errorList.add(errorMap);
                flag = false;
            }

            boolean timeFlag = true;
            // 实际到访时间
            if (uompTempAdmission.getRealVisitTime() == null) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第8列");
                errorMap.put("message", "实际到访时间不能为空或者时间日期格式不正确");
                errorList.add(errorMap);
                flag = false;
                timeFlag = false;
            }

            // 离开时间
            if (uompTempAdmission.getExitTime() == null) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第9列");
                errorMap.put("message", "实际到访时间不能为空或者时间日期格式不正确");
                errorList.add(errorMap);
                flag = false;
                timeFlag = false;
            }

            // 离开时间不能小于到访时间(如果时间格式无误进行校验)
            if (timeFlag) {
                if (uompTempAdmission.getExitTime().before(uompTempAdmission.getRealVisitTime())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "");
                    errorMap.put("message", "离开时间不应该小于到访时间");
                    errorList.add(errorMap);
                    flag = false;
                }
            }

            // 工作内容
            if (StringUtils.isBlank(uompTempAdmission.getJobContent())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第10列");
                errorMap.put("message", "工作内容不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (uompTempAdmission.getJobContent().length() > 1000) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第10列");
                errorMap.put("message", "工作内容不能超过1000位");
                errorList.add(errorMap);
                flag = false;
            }

            // 应用系统
            if (StringUtils.isBlank(uompTempAdmission.getEngagementProject())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第11列");
                errorMap.put("message", "应用系统不能为空");
                errorList.add(errorMap);
                flag = false;
            } else {
                // 根据应用系统名查询应用系统
                if (!projectBySupplierMap.containsKey(uompTempAdmission.getEngagementProject())) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "第11列");
                    errorMap.put("message", "应用系统不存在");
                    errorList.add(errorMap);
                    flag = false;
                } else {
                    uompTempAdmissionUpload.setEngagementProjectId(projectBySupplierMap.get(uompTempAdmission.getEngagementProject()));
                }
            }

            // 备案状态
            if (StringUtils.isBlank(uompTempAdmission.getFilingStatus())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第12列");
                errorMap.put("message", "备案状态不能为空");
                errorList.add(errorMap);
                flag = false;
            } else if (!"1".equals(uompTempAdmission.getFilingStatus()) && !"0".equals(uompTempAdmission.getFilingStatus()) && !"已备案".equals(uompTempAdmission.getFilingStatus()) && !"未备案".equals(uompTempAdmission.getFilingStatus())) {
                errorMap = new HashMap<>();
                errorMap.put("row", "第" + (i + 1) + "行");
                errorMap.put("column", "第12列");
                errorMap.put("message", "备案状态只能为1,0 或 已备案,未备案");
                errorList.add(errorMap);
                flag = false;
            }

            //校验数据是否重复，完全一致才算重复
            if (flag) {
                String info = uompTempAdmissionMapper.selectIdByAll(uompTempAdmission);
                if (StringUtils.isNotBlank(info)) {
                    errorMap = new HashMap<>();
                    errorMap.put("row", "第" + (i + 1) + "行");
                    errorMap.put("column", "");
                    errorMap.put("message", "该行数据已在数据库中存在，不可重复导入");
                    errorList.add(errorMap);
                    flag = false;
                }
            }

            if (flag) {
                BeanUtils.copyProperties(uompTempAdmission, uompTempAdmissionUpload);

                if (!"1".equals(uompTempAdmission.getFilingStatus()) && !"0".equals(uompTempAdmission.getFilingStatus())) {
                    uompTempAdmissionUpload.setFilingStatus("已备案".equals(uompTempAdmission.getFilingStatus()) ? "1" : "0");
                }

                //处理初始化字段
                uompTempAdmissionUpload.setId(IdUtil.getSuid());
                uompTempAdmissionUpload.setApplyStatus("2");
                uompTempAdmissionUpload.setCreateBy(userId);
                uompTempAdmissionUpload.setCreateTime(new Date());
                uompTempAdmissionUpload.setCreateOrgId(orgId);
                uompTempAdmissionUpload.setDelFlag("0");
                uompTempAdmissionUpload.setBlacklist("0");
                uompTempAdmissionUpload.setDestClerkId(userId);
                uompTempAdmissionUpload.setDestClerkName(fullname);

                uompTempAdmissionUploadList.add(uompTempAdmissionUpload);
            }
        }

        if (!uompTempAdmissionUploadList.isEmpty()) {
            for (UompTempAdmission u : uompTempAdmissionUploadList) {
                total += 1;
                uompTempAdmissionMapper.insertSelective(u);
            }
        }

        if (!errorList.isEmpty()) {
            //导出数据
            // 第一个对象，新建一个工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            // 设置第一个sheet的名称
            Sheet sheet = workbook.createSheet("sheet1");

            // 开始添加excel第一行表头（excel中下标是0）
            Row row = sheet.createRow(0);
            sheet.setDefaultColumnWidth(16);//宽

            row.setHeightInPoints(20);//行高

            String[] fieldList = {"序号", "行数", "列数", "错误原因"};
            int i = 0;
            // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
            for (String it : fieldList) {
                // 创建一个单元格
                Cell cell = row.createCell(i);
                // 设置单元格的样式
                CellStyle cellStyle = workbook.createCellStyle();
                //设置字体
                Font font = workbook.createFont();
                //设置字号
                font.setFontHeightInPoints((short) 14);
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                // 将数据放入excel的单元格中
                cell.setCellValue(it);
                i++;
            }


            // 开始创建excel单元格数据，从第二行开始（excel下标是1）
            int rowNum = 1;
            // 添加excel行数据的集合（你自己的数据集合遍历）
            for (Map<String, String> it : errorList) {
                // 创建一个单元格
                Row row1 = sheet.createRow(rowNum);
                // 设置行的高度
                row1.setHeightInPoints(16);
                //填写单元格
                row1.createCell(0).setCellValue(rowNum + "");//序号
                row1.createCell(1).setCellValue(it.get("row"));//行
                row1.createCell(2).setCellValue(it.get("column"));//列
                row1.createCell(3).setCellValue(it.get("message"));//原因

                rowNum++;
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                //把文件送到用户端
                workbook.write(bos);
                byte[] brray = bos.toByteArray();
                InputStream is = new ByteArrayInputStream(brray);
                SysFileDTO upload = sysFileService.upload(is, "错误信息-" + IdUtil.getSuid() + ".xlsx");
                ResultMsg<String> resultMsg = ResultMsg.ERROR("临时入场信息导入失败,错误信息请查看下载的excel文件");
                resultMsg.setData(upload.getId());
                resultMsg.setCode("400");
                return resultMsg;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放资源
                bos.flush();
                bos.close();
                workbook.close();
            }
        } else {
            return ResultMsg.SUCCESS("导入成功-" + total + "条");
        }
        return ResultMsg.ERROR("导入失败!");
    }

    @Override
    public List<PercentageDTO> tempPersonTrend() {
        List<PercentageDTO> percentageDTOs = new ArrayList<>();
        List<String> monthsList = DateUtils.getSixMonths();
        try {
            List<PercentageDTO> percentageDTOList = uompTempAdmissionMapper.countByRealVisitTimeGroupByRealVisitTime(monthsList.get(0), monthsList.get(monthsList.size() - 1));

            PercentageDTO percentageDTO;
            boolean sign;
            for (String s : monthsList) {
                percentageDTO = new PercentageDTO();
                sign = false;
                for (PercentageDTO position : percentageDTOList) {
                    if (position.getName().equalsIgnoreCase(s)) {
                        percentageDTO = position;
                        sign = true;
                    }
                }
                if (!sign) {
                    percentageDTO.setName(s);
                    percentageDTO.setNum(0);
                }

                percentageDTOs.add(percentageDTO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return percentageDTOs;
    }

    @Override
    public Integer countByrealVisitTime(String realVisitTimeStart, String realVisitTimeEnd) {
        return uompTempAdmissionMapper.countByrealVisitTime(realVisitTimeStart, realVisitTimeEnd);
    }


    /**
     * 从cmdb获取应用系统数据，并组装
     * @return
     */
    private Map<String, String> makeSystemByCmdb(){
        Map application = icmdbService.instListByModelName("application", null, null);
        List<Object> headerList = (List<Object>) application.get("header");
        List<Map<String, String>> dataList = (List<Map<String, String>>) application.get("list");

        //数据处理，以attrName为key的 map
        Map<String, JSONObject> headerMap = new HashMap<>();
        headerList.forEach(info -> {
            JSONObject infoJsonObject = JSONObject.parseObject(JSON.toJSONString(info));
            String attrName = String.valueOf(infoJsonObject.get("attrName"));
            headerMap.put(attrName, infoJsonObject);
        });

        //获取请求头中，名称（ci_name），主责部门(responsible_org)的attrId
        JSONObject ciNameJsonObject = headerMap.get("ci_name");
        JSONObject responsibleOrgJsonObject = headerMap.get("responsible_org");
        String ciNameAttrId = String.valueOf(ciNameJsonObject.get("attrId"));
        String responsibleOrgAttrId = String.valueOf(responsibleOrgJsonObject.get("attrId"));

        //构造以ciName为key,id为value
        Map<String, String> resultMap = new HashMap<>();
        dataList.forEach(data -> {
            resultMap.put(data.get(ciNameAttrId), data.get("id"));
        });

        return resultMap;
    }
}
