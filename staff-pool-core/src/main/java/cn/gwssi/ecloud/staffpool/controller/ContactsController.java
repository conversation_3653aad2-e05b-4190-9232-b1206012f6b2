package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.core.manager.UompOrgGroupService;
import cn.gwssi.ecloud.staffpool.core.manager.UompPersonInfoService;
import cn.gwssi.ecloud.staffpool.dto.UompPersonContactsDTO;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(description = "人员信息")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/contacts")
public class ContactsController extends BaseController<UompPersonInfo> {
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private UompOrgGroupService ucompOrgGroupService;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DesRuleUtil desRuleUtil;

    @ApiOperation(value = "通讯录列表")
    @PostMapping(value = "/getContactsList")
    public PageResult getContactsList(HttpServletRequest request, @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                                      @RequestParam(value = "orgGroupId", required = false) @ApiParam(value = "运维组织Id") String orgGroupId,
                                      @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                                      @RequestParam(value = "pageNo", defaultValue = "1") @ApiParam(value = "页码") Integer pageNo,
                                      @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("i.DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("i.BLACKLIST", 0, QueryOP.EQUAL);// 非黑名单
        queryFilter.addFilter("i.DIMISSION", 0, QueryOP.EQUAL);// 在职
        queryFilter.addFilter("i.BACKGROUND_STATUS", 2, QueryOP.NOT_EQUAL);// 非不合格
        //11-20 只能审核通过的才能显示
        queryFilter.addFilter("i.TRIAL_STATUS", 2, QueryOP.EQUAL);// 审核通过
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("i.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }

        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("i.PERSON_NAME", personName, QueryOP.LIKE);
        }
        queryFilter.addFieldSort("i.CREATE_TIME", "DESC");
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.size() == 0) {
            return new PageResult();
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());

        String roleOrgGroupId = "";
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER") || roleNames.contains("ITSM_HELP")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {// 查看本公司下
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                queryFilter.addFilter("WORKING_COMPANY_ID", workCompany.get(0).getWorkingCompanyId(), QueryOP.EQUAL);
            }
        } else if (roleNames.contains("ITSM_SERVICE") || roleNames.contains("ITSM_GROUP LEADER")) {// 查看本运维组下
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                roleOrgGroupId = workCompany.get(0).getOrgGroupId();
//                queryFilter.addFilter("ORG_GROUP_ID", workCompany.get(0).getOrgGroupId(), QueryOP.EQUAL);
            }
        } else {
            return new PageResult();
        }
        //11-20 当有roleOrgGroupId的时候应该添加权限只查roleOrgGroupId下的人员
        if (!StringUtils.isEmpty(orgGroupId)) {
            if (!StringUtils.isEmpty(roleOrgGroupId)) {
                //只有当前节点对应上才返回数据，其余节点为空
                if (orgGroupId.equals(roleOrgGroupId)){
                    queryFilter.addFilter("i.ORG_GROUP_ID", roleOrgGroupId, QueryOP.EQUAL);
                }else {
                    return new PageResult();
                }
            }else{
                List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
                if (orgIds != null && orgIds.size() > 0) {
                    queryFilter.addFilter("i.ORG_GROUP_ID", orgIds, QueryOP.IN);
                }
            }
        }
//        if (!StringUtils.isEmpty(orgGroupId)) {
//            List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
//            if (StringUtils.isNotBlank(roleOrgGroupId)) {
//                orgIds.add(roleOrgGroupId);
//            }
//            if (orgIds != null && orgIds.size() > 0) {
//                queryFilter.addFilter("i.ORG_GROUP_ID", orgIds, QueryOP.IN);
//            }
//        }
        List<UompPersonContactsDTO> personInfoList = uompPersonInfoService.selectContacts(queryFilter);
        PageResult pageResult = new PageResult(personInfoList);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            List<UompPersonContactsDTO> uompPersonInfoTaskDtos = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(personInfoList))).toJSONString(), UompPersonContactsDTO.class);
            pageResult.setRows(uompPersonInfoTaskDtos);
            return pageResult;
        }
        pageResult.setRows(personInfoList);
        return pageResult;
    }

    @ApiOperation(value = "通讯录列表-手环使用")
    @PostMapping(value = "/getContactsList/bracelet")
    public PageResult bracelet(HttpServletRequest request, @RequestParam(value = "workingCompany", required = false) @ApiParam(value = "就职公司") String workingCompany,
                               @RequestParam(value = "orgGroupId", required = false) @ApiParam(value = "运维组织Id") String orgGroupId,
                               @RequestParam(value = "personName", required = false) @ApiParam(value = "姓名") String personName,
                               @RequestParam(value = "pageNo", defaultValue = "1") @ApiParam(value = "页码") Integer pageNo,
                               @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam(value = "每页数量") Integer pageSize) {
        QueryFilter queryFilter = getQueryFilter(request);
        queryFilter.addFilter("i.ENTRY_STATUS", "0", QueryOP.EQUAL);
        queryFilter.addFilter("i.DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("i.BLACKLIST", 0, QueryOP.EQUAL);// 非黑名单
        queryFilter.addFilter("i.DIMISSION", 0, QueryOP.EQUAL);// 在职
        queryFilter.addFilter("i.BACKGROUND_STATUS", 2, QueryOP.NOT_EQUAL);// 非不合格
        if (!StringUtils.isEmpty(workingCompany)) {
            queryFilter.addFilter("i.WORKING_COMPANY", workingCompany, QueryOP.LIKE);
        }
        if (!StringUtils.isEmpty(orgGroupId)) {
            List<String> orgIds = ucompOrgGroupService.getByPath(orgGroupId);
            if (orgIds != null && orgIds.size() > 0) {
                queryFilter.addFilter("i.ORG_GROUP_ID", orgIds, QueryOP.IN);
            }
        }
        if (!StringUtils.isEmpty(personName)) {
            queryFilter.addFilter("i.PERSON_NAME", personName, QueryOP.LIKE);
        }
        queryFilter.addFieldSort("i.CREATE_TIME", "DESC");
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        if (roles == null || roles.size() == 0) {
            return new PageResult();
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER") || roleNames.contains("ITSM_HELP")) {
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) {// 查看本公司下
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                queryFilter.addFilter("WORKING_COMPANY_ID", workCompany.get(0).getWorkingCompanyId(), QueryOP.EQUAL);
            }
        } else if (roleNames.contains("ITSM_SERVICE")) {// 查看本运维组下
            QueryFilter queryFilter1 = new DefaultQueryFilter(true);
            queryFilter1.addFilter("ORG_USER_ID", user.getUserId(), QueryOP.EQUAL);
            List<UompPersonInfo> workCompany = uompPersonInfoService.query(queryFilter1);
            if (workCompany != null && workCompany.size() > 0) {
                queryFilter.addFilter("ORG_GROUP_ID", workCompany.get(0).getOrgGroupId(), QueryOP.EQUAL);
            }
        } else {
            return new PageResult();
        }
        List<UompPersonContactsDTO> personInfoList = uompPersonInfoService.selectContacts(queryFilter);
        PageResult pageResult = new PageResult(personInfoList);
        List<UompDesensitization> ruleList = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "TEL", null, null);
        if (ruleList != null && ruleList.size() > 0) {
            List<UompPersonContactsDTO> uompPersonInfoTaskDtos = JSONObject.parseArray(conversionUtil.conversionBatch(ruleList, JSON.parseArray(JSONObject.toJSONString(personInfoList))).toJSONString(), UompPersonContactsDTO.class);
            pageResult.setRows(uompPersonInfoTaskDtos);
            return pageResult;
        }
        pageResult.setRows(personInfoList);
        return pageResult;
    }

    @Override
    protected String getModelDesc() {
        return "通讯录";
    }
}
