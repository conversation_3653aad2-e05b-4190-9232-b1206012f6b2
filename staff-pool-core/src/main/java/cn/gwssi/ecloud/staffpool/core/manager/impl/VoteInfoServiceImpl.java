package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.VoteInfoMapper;
import cn.gwssi.ecloud.staffpool.core.entity.VoteInfo;
import cn.gwssi.ecloud.staffpool.core.manager.VoteInfoService;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VoteInfoServiceImpl extends BaseManager<String, VoteInfo> implements VoteInfoService {

    @Resource
    private VoteInfoMapper voteInfoMapper;

    @Override
    public int insertSelective(VoteInfo record) {
        return voteInfoMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(VoteInfo record) {
        return voteInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public List<VoteUserAnswerDto> selectVoteInfoByUserId(String userId) {
        return voteInfoMapper.selectVoteInfoByUserId(userId);
    }

    @Override
    public List<VoteUserAnswerDto> selectVoteInfo(QueryFilter queryFilter) {
        return voteInfoMapper.selectVoteInfo(queryFilter);
    }
}
