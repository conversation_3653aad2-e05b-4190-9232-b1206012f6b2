package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="根据id查询入场申请详情响应类")
@Data
public class AcceptPersonSelectDTO implements Serializable {

    @ApiModelProperty(value="人员id")
    private String id;
    @ApiModelProperty(value="名称")
    private String personName;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="账号")
    private String account;
}
