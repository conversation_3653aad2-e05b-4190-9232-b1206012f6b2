package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompReportFile;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UompReportFileMapper extends BaseDao<String, UompReportFile> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompReportFile record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UompReportFile record);

    int updateDelFlag(UompReportFile uompReportFile);
}