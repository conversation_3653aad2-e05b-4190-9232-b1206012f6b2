package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.annotation.AutoDesensitize;
import cn.gwssi.ecloud.staffpool.annotation.Sensitive;
import cn.gwssi.ecloud.staffpool.api.service.IDataDesensitization;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.*;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.DateUtils;
import cn.gwssi.ecloud.staffpool.util.DictUtil;
import cn.gwssi.ecloud.staffpool.util.ReflectUtil;
import cn.gwssi.ecloud.staffpool.util.SupplierUtil;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.core.cache.ICache;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.orgCustom.core.manager.UserDetailManager;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.OrgRelationCustom;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.UserDetail;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.org.core.manager.OrgRelationManager;
import cn.gwssi.ecloudframework.org.core.manager.UserManager;
import cn.gwssi.ecloudframework.org.core.model.OrgRelation;
import cn.gwssi.ecloudframework.org.core.model.User;
import cn.gwssi.ecloudframework.sys.api.platform.ISysPropertiesPlatFormService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UompPersonInfoServiceImpl extends BaseManager<String, UompPersonInfo> implements UompPersonInfoService {

    @Autowired
    private UompPersonInfoMapper uompPersonInfoMapper;
    @Autowired
    private OrgRelationMapper orgRelationMapper;
    @Autowired
    private UompDesensitizationMapper uompDesensitizationMapper;
    @Autowired
    private OrgUserMapper orgUserMapper;
    @Autowired
    private OrgTeamMapper orgTeamMapper;
    @Autowired
    private OrgRoleMapper orgRoleMapper;
    @Autowired
    private OrgPostMapper orgPostMapper;
    @Resource
    private OrgRelationManager orgRelationManager;
    @Autowired
    private UompOrgGroupMapper uompOrgGroupMapper;
    @Autowired
    private UompAccountApplyPersonService uompAccountApplyPersonService;
    @Autowired
    private UompAccountApplyService uompAccountApplyService;
    @Autowired
    private UompTeamService uompTeamService;
    @Resource
    private SysDataDictMapper sysDataDictMapper;
    @Resource
    private UompExitApplicationMapper uompExitApplicationMapper;
    @Resource
    private DictUtil dictUtil;
    @Resource
    private SupplierUtil supplierUtil;
    @Resource
    private UompPersonEducationalService uompPersonEducationalService;
    @Resource
    private UompPersonJobService uompPersonJobService;
    @Resource
    private UompPersonTechnologyService uompPersonTechnologyService;
    @Resource
    private UompPersonSocialService uompPersonSocialService;
    @Resource
    private UompPersonNoCrimeService uompPersonNoCrimeService;
    @Resource
    private UompPersonAbroadService uompPersonAbroadService;
    @Resource
    private UompPersonEntryExitService uompPersonEntryExitService;
    @Resource
    private UompPersonAllInfoTempService uompPersonAllInfoTempService;
    @Resource
    private UserDetailManager userDetailManager;
    @Resource
    private UompAdmissionPersonService uomAdmissionPersonService;
    @Resource
    private UompSupplierManagementService supplierManagementService;
    @Resource
    ICache iCache;
    @Resource
    private UserManager userManager;

    @Resource
    IDataDesensitization dataDesensitization;

    @Resource
    UompPersonAllInfoHistoryMapper uompPersonAllInfoHistoryMapper;

    @Resource
    UompTrainingRecordService trainingRecordService;

    @Resource
    VoteInfoService voteInfoService;

    @Resource
    private UompPersonConfigService uompPersonConfigService;

    @Resource
    private ISysPropertiesPlatFormService iSysPropertiesPlatFormService;

    private final static String UOMP_PERSON_INFO = "UOMP_PERSON_INFO";

    @Override
    public List<UompPersonInfo> query(QueryFilter queryFilter){
        List<UompPersonInfo> uompPersonInfos =  uompPersonInfoMapper.query(queryFilter);
        //对身份证号显示进行脱敏处理
        /*List<UompPersonInfo> uompPersonInfoList = uompPersonInfos.stream().map(uompPersonInfo -> {
            if (personCardIsDecrypt(uompPersonInfo.getPersonCard())){
                uompPersonInfo.setPersonCard(dataDesensitization.desensitization(uompPersonInfo.getPersonCard(),3,3));
            }
            return uompPersonInfo;
        }).collect(Collectors.toList());*/
        return uompPersonInfos;
    }

    @Override
    public int insertSelective(UompPersonInfo record) {
        return uompPersonInfoMapper.insertSelective(record);
    }

    @Override
    public int updateByIds(String orgId, String[] ids) {
        return uompPersonInfoMapper.updateByIds(orgId, ids);
    }

    @Override
    public int updateByPrimaryKeySelective(UompPersonInfo record) {
        //身份证号脱敏,加密存储
//        personCardEncrypt(record);
        return uompPersonInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public String selectIdAndAccountByPersonNameAndPersonCard(String personName, String personCard) {
        if (StringUtils.isNotEmpty(personCard) && personCard.length() == 18){
            personCard = dataDesensitization.encrypt(personCard);
        }
        return uompPersonInfoMapper.selectIdAndAccountByPersonNameAndPersonCard(personName, personCard);
    }

    @Override
    public UompPersonInfoDTO getInfoByPersonId(String personId) {
        UompPersonInfoDTO dto = null;
        if (StringUtils.isNotEmpty(personId)) {
            QueryFilter queryFilter = new DefaultQueryFilter(true);
            queryFilter.addFilter("a.ID", personId, QueryOP.EQUAL);
            queryFilter.addFilter("a.DEL_FLAG", '0', QueryOP.EQUAL);
            //查询人员详情
            dto = uompPersonInfoMapper.selectInfoOneByPersonId(queryFilter);
            // 查询账号申请的instId
            UompAccountApply uompAccountApply = uompAccountApplyService.selectInfoOneByPersonId(personId);
            if (uompAccountApply != null) {
                //获取脱敏配置信息
                List<UompDesensitization> ruleList = getDesRule(UOMP_PERSON_INFO, "TEL", uompAccountApply.getInstId(), uompAccountApply.getCreateBy());
                //脱敏
                if (!CollectionUtils.isEmpty(ruleList)) {
                    for (UompDesensitization rule : ruleList) {
                        //如果有关于手机号的脱敏规则则转换手机号
                        String desFieldCode = rule.getDesFieldCode();
                        String tel = dto.getTel();
                        if (StringUtils.isNotEmpty(tel) && "TEL".equals(desFieldCode)) {
                            String newTel = conversion(rule, tel);
                            dto.setTel(newTel);
                        }
                    }
                }
            }
        }
        return dto;
    }

    private String conversion(UompDesensitization rule, String tel) {
        //解析脱敏规则
        String desRuleMode = rule.getDesRuleMode();
        //看看是替换还是正则
        //1.替换
        if ("0".equals(desRuleMode)) {
            String desRuleJson = rule.getDesRuleJson();
            List<Object> ruleJsonList = JSONObject.parseObject(desRuleJson, List.class);
            for (Object obj : ruleJsonList) {
                Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                int beginIndex = (int) map.get("begin");
                int endIndex = (int) map.get("end");
                String replace = (String) map.get("replace");
                //截取字符
                //如果begin>value长度则不做脱敏，如果end > value长度，按照value的长度算，
                if (beginIndex < tel.length()) {
                    if (endIndex > tel.length()) {
                        endIndex = tel.length();
                    }
                    //中间替换数量截取
                    int index = endIndex - beginIndex;
                    StringBuilder builder = new StringBuilder();
                    builder.append(tel.substring(0, beginIndex - 1));
                    int j = 0;
                    while (j < index + 1) {
                        builder.append(replace);
                        j++;
                    }
                    builder.append(tel.substring(endIndex));
                    tel = builder.toString();
                }
            }
        }
        //2.正则
        if ("1".equals(desRuleMode)) {
            //错误的正则，不做脱敏处理了
            try {
                String desRuleRegx = rule.getDesRuleRegx();
                Map<String, Object> desRuleMap = JSONObject.parseObject(desRuleRegx, Map.class);
                String regex = (String) desRuleMap.get("regex");
                String replace = (String) desRuleMap.get("replace");
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(tel);
                tel = matcher.replaceAll(replace);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //查看敏感词过滤是否有值(如果有值，替换词也必然有值)
        if (StringUtils.isNotEmpty(rule.getSensitiveWords())) {
            //将过滤词分隔成数组，固定用|分隔
            List<String> swList = Arrays.asList(rule.getSensitiveWords().split("|"));
            for (String key : swList) {
                tel = tel.replace(key, rule.getSensitiveReplaceWords());
            }
        }
        return tel;
    }

    /**
     * 获取脱敏配置信息
     * @param tableName 表名
     * @param desRuleParam 需要脱敏字段
     * @param instId 流程实例id
     * @param createBy 创建人
     * @return List<UompDesensitization> 脱敏规则集合
     **/
    @Override
    public List<UompDesensitization> getDesRule(String tableName, String desRuleParam, String instId, String createBy) {
        //返回需要脱敏的集合
        List<UompDesensitization> resultRuleList = new ArrayList<>();
        //本人拥有角色id集合
        List<String> userRoleIdList;
        //本人拥有岗位id集合
        List<String> userPostIdList;
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        //本人角色
        List<IUserRole> roles = user.getRoles();
        userRoleIdList = roles.stream().map(item -> item.getRoleId()).distinct().collect(Collectors.toList());
        //查询本人所拥有的岗位(不含业务组)
        List<OrgPostBaseDTO> orgPostBaseDTOList = orgRelationMapper.selectOwerListByUserId(userId);
        userPostIdList = orgPostBaseDTOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        //1.查询配置全局启用状态，如果全局是不启用的则直接返回
        String allEnable = uompDesensitizationMapper.selectAllEnable();
        //如果为空，或者不等于 0-是 的时候则跳过
        if (!"0".equals(allEnable)) {
            return resultRuleList;
        }
        //2.查询脱敏配置
        //如果des_field_code有值，则查询对象下的某些字段，没有则是该对象下所有配置字段
        //只查询启用状态的即可
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("IS_ENABLE", "0", QueryOP.EQUAL);
        if (StringUtils.isNotEmpty(tableName)) {
            queryFilter.addFilter("DES_OBJ_CODE", tableName, QueryOP.EQUAL);
            if (StringUtils.isNotEmpty(desRuleParam)) {
                queryFilter.addFilter("DES_FIELD_CODE", desRuleParam, QueryOP.IN);
            }
        }
        List<UompDesensitization> list = uompDesensitizationMapper.selectAllConfig(queryFilter);
        for (UompDesensitization it : list) {
            boolean isShow = true;
            //校验每条脱敏规则的明文显示是否符合该人，如果符合明文则无需添加该条脱敏规则
            //1)角色明文过滤
            String plaintextRoleJson = it.getPlaintextRoleJson();
            if (StringUtils.isNotEmpty(plaintextRoleJson)) {
                List<Object> plaintextRoleList = JSONObject.parseObject(plaintextRoleJson, List.class);
                if (!CollectionUtils.isEmpty(plaintextRoleList)) {
                    for (Object obj : plaintextRoleList) {
                        //如果本人拥有该明文显示角色，则直接跳过，不用添加该条规则
                        Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                        if (userRoleIdList.contains((String) map.get("id"))) {
                            isShow = false;
                            break;
                        }
                    }
                }
            }
            //如果上边校验过了，继续查，没过直接不用查了
            if (isShow) {
                //2)岗位明文过滤
                String plaintextPostJson = it.getPlaintextPostJson();
                if (StringUtils.isNotEmpty(plaintextPostJson)) {
                    List<Object> plaintextPostList = JSONObject.parseObject(plaintextPostJson, List.class);
                    if (!CollectionUtils.isEmpty(plaintextPostList)) {
                        for (Object obj : plaintextPostList) {
                            //如果本人拥有该明文显示岗位，则直接跳过，不用添加该条规则
                            Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                            if (userPostIdList.contains((String) map.get("id"))) {
                                isShow = false;
                                break;
                            }
                        }
                    }
                }
            }
            //如果上边校验过了，继续查，没过直接不用查了
            if (isShow) {
                //3)人员明文过滤
                String plaintextUserJson = it.getPlaintextUserJson();
                if (StringUtils.isNotEmpty(plaintextUserJson)) {
                    List<Object> plaintextUserList = JSONObject.parseObject(plaintextUserJson, List.class);
                    if (!CollectionUtils.isEmpty(plaintextUserList)) {
                        for (Object obj : plaintextUserList) {
                            //如果明文人员包含本人，则跳过
                            Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                            if (userId.equals((String) map.get("id"))) {
                                isShow = false;
                                break;
                            }
                        }
                    }
                }
            }

            if (isShow) {
                //4)录入人可见判断（只有详情中才判断录入人可见，所以有create_by是详情查看，列表的规则校验无需传这个参数）
                if (StringUtils.isNotEmpty(createBy)) {
                    String isCreater = it.getIsCreater();
                    if ("0".equals(isCreater)) {
                        //如果录入人是登录人，则无需脱敏
                        if (userId.equals(createBy)) {
                            isShow = false;
                        }
                    }
                }
            }
            if (isShow) {
                //如果有业务id，则证明是详情查询，这时候需要校验录入人可见，经办人可见等
                if (StringUtils.isNotEmpty(instId)) {
                    //5)经办人可见判断
                    String isOperator = it.getIsOperator();
                    if ("0".equals(isOperator)) {
                        //校验方式 0- sql脚本
                        if ("0".equals(it.getOperatorMode())) {
                            String sql = it.getOperatorScript();
                            if (StringUtils.isNotEmpty(sql)) {
                                //todo 暂时固定写法，将业务id，和人员id，替换成对应值; 固定返回值是数量
                                sql = sql.replace("#{user_id}", "'" + userId + "'").replace("#{biz_id}", "'" + instId + "'");
                                //执行该sql(如果sql报错，则直接跳过该明文校验，按照需要脱密算)
                                try {
                                    int num = uompDesensitizationMapper.selectWz(sql);
                                    //如果执行数据量大于0，则经办人有当前用户，无需脱密
                                    if (num > 0) {
                                        isShow = false;
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            }

            if (isShow) {
                //封装脱敏规则
                UompDesensitization ud = new UompDesensitization();
                ud.setDesFieldCode(it.getDesFieldCode());
                ud.setDesRuleMode(it.getDesRuleMode());
                ud.setDesRuleJson(it.getDesRuleJson());
                ud.setDesRuleRegx(it.getDesRuleRegx());
                ud.setSensitiveReplaceWords(it.getSensitiveReplaceWords());
                ud.setSensitiveWords(it.getSensitiveWords());
                resultRuleList.add(ud);
            }
        }
        return resultRuleList;
    }

    @Override
    public String updateAccountById(String id, String accountNum) {
        // 根据账号查询系统用户id
        String orgUserId = orgUserMapper.selectOrgUserByAccount(accountNum);
        // 根据id查询账号申请人员信息
        UompAccountApplyPerson uompAccountApplyPerson = uompAccountApplyPersonService.get(id);
        if (null != uompAccountApplyPerson) {
            //声明修改账号申请信息中人员id，账号信息的sql
            uompAccountApplyPerson.setOrgUserId(orgUserId);
            uompAccountApplyPerson.setAccountNum(accountNum);
            uompAccountApplyPersonService.update(uompAccountApplyPerson);
            // 更新人员分配账号状态、账号
            String personId = uompAccountApplyPerson.getPersonId();
            UompPersonInfo uompPersonInfo = new UompPersonInfo();
            uompPersonInfo.setId(personId);
            uompPersonInfo.setIsAccount("1"); // 已分配账号
            uompPersonInfo.setAccount(accountNum);
            uompPersonInfo.setOrgUserId(orgUserId);
            uompPersonInfoMapper.updateByPrimaryKeySelective(uompPersonInfo);
        }
        return orgUserId;
    }

    /**
     * 0624驻场申请添加人员账号申请和授权流程：
     * 账号申请调用： 账号申请人员行id
     * 驻场申请调用： 依据驻场申请关联查出账户申请人员行id调用该接口 （账号申请人员表中APPLY_ID关联到驻场申请id）
     **/
    @Override
    public void empower(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        for (String id : idList) {
            //查询申请信息
            QueryFilter queryFilter = new DefaultQueryFilter(true);
            queryFilter.addFilter("a.ID", id, QueryOP.EQUAL);
            UompAccountApplyPerson info = uompAccountApplyPersonService.selectApplyInfoById(queryFilter);
            if (info != null) {
                String orgUserId = info.getOrgUserId();
                String position = info.getPosition();
                String role = info.getRole();
                String authorizationStatus = info.getAuthorizationStatus();
                String groupId = info.getMaintenanceGroupId();
                if ("1".equals(authorizationStatus)) {
                    throw new BusinessMessage("用户已按角色和岗位授权过，不可再次授权");
                }
                if (StringUtils.isNotEmpty(role)) {
                    //授权角色
                    List<OrgRelation> roList = new ArrayList<>();
                    // 添加默认角色 运管平台基础用户
                    List<String> mrRole = orgRoleMapper.getMrRoleId();
                    List<String> exitRole = new ArrayList<>();
                    List<Object> roleList = JSONObject.parseObject(role, List.class);
                    for (Object obj : roleList) {
                        Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                        String roleId = (String) map.get("id");
                        if (mrRole.contains(id)) {
                            exitRole.add(id);
                        }
                        OrgRelation orgRelation = new OrgRelation();
                        orgRelation.setGroupId(roleId);
                        orgRelation.setSn("0");
                        orgRelation.setType("userRole");
                        orgRelation.setUserId(orgUserId);
                        roList.add(orgRelation);
                    }
                    if (!CollectionUtils.isEmpty(mrRole)) {
                        for (String roleId : mrRole) {
                            if (exitRole.contains(role)) {
                                continue;
                            }
                            OrgRelation orgRelation = new OrgRelation();
                            orgRelation.setGroupId(roleId);
                            orgRelation.setSn("0");
                            orgRelation.setType("userRole");
                            orgRelation.setUserId(orgUserId);
                            roList.add(orgRelation);
                        }
                    }
                    orgRelationManager.batchAdd(roList);
                }
                if (StringUtils.isNotEmpty(position)) {
                    List<OrgRelation> poList = new ArrayList<>();
                    //解析岗位
                    List<Object> positionList = JSONObject.parseObject(position, List.class);
                    for (Object obj : positionList) {
                        Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                        String postId = (String) map.get("id");
                        OrgRelation orgRelation = new OrgRelation();
                        orgRelation.setGroupId(postId);
                        orgRelation.setIsMaster(0);
                        orgRelation.setStatus(1);
                        orgRelation.setType("postUser");
                        orgRelation.setUserId(orgUserId);
                        orgRelation.setOrgId(groupId);
                        poList.add(orgRelation);
                    }
                    orgRelationManager.batchAdd(poList);
                }
            }
        }
        //修改信息中授权状态
        uompAccountApplyPersonService.upDateAuthorizationStatusByIds(idList);
    }

    @Override
    public void activeAccount(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = Arrays.asList(ids.split(","));
            for (String id : idList) {
                //修改为激活状态
                orgUserMapper.updateStatusById(id);
            }
        }
    }

    @Override
    public String getTitleByCurrentUser() {
        // 获取当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String orgName = user.getOrgName();
        //时间格式封装
        DateFormat formatter = new SimpleDateFormat(DateUtils.yyyyMMddHHmm);
        Date date = new Date();
        String time = formatter.format(date);
        StringBuilder sb = new StringBuilder();
        return sb.append("【").append(orgName).append("】发起账号申请").append(time).toString();
    }

    @Override
    public PageResult getBusiness(QueryFilter queryFilter) {
        List<BaseDTO> dtoList = new ArrayList<>();
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // 状态：0无效 1有效
        queryFilter.addFilter("STATUS", "1", QueryOP.EQUAL);
        // 创建时间倒叙
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        List<UompTeam> list = uompTeamService.query(queryFilter);
        if (!CollectionUtils.isEmpty(list)) {
            BaseDTO dto;
            for (UompTeam info : list) {
                dto = new BaseDTO();
                dto.setId(info.getId());
                dto.setName(info.getName());
                dtoList.add(dto);
            }
        }
        return new PageResult(dtoList);
    }

    public BaseInstDTO selectTaskIdByInstId(String instId) {
        if (StringUtils.isNotEmpty(instId)) {
            return uompPersonInfoMapper.selectTaskIdByInstId(instId);
        }
        return null;
    }

    @Override
    public BaseDTO selectUserId(String id) {
        return uompPersonInfoMapper.selectUserId(id);
    }

    @Override
    public List<UompBlackDto> selectBlack(String id) {
        return uompPersonInfoMapper.selectBlack(id);
    }

    @Override
    public PageResult getRoleByOrgType(QueryFilter queryFilter) {
        List<OrgRoleTypeDTO> dtoList = new ArrayList<>();
        // 查询所有启用的角色 ENABLED_=1
        queryFilter.addFilter("ENABLED_", 1, QueryOP.EQUAL);
        queryFilter.addFieldSort("ID_", "DESC");
        List<OrgRole> list = orgRoleMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        if (!CollectionUtils.isEmpty(list)) {
            for (OrgRole info : list) {
                OrgRoleTypeDTO dto = new OrgRoleTypeDTO();
                dto.setId(info.getId());
                dto.setName(info.getName());
                dto.setAlias(info.getAlias());
                dto.setDesc(info.getDescription());
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
        }
        return pageResult;
    }

    @Override
    public PageResult getPostByOrgType(QueryFilter queryFilter) {
        // 查询所有岗位
        List<OrgPostTypeDTO> dtoList = new ArrayList<>();
        queryFilter.addFieldSort("ID_", "DESC");
        List<OrgPost> list = orgPostMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        if (!CollectionUtils.isEmpty(list)) {
            for (OrgPost info : list) {
                OrgPostTypeDTO dto = new OrgPostTypeDTO();
                dto.setId(info.getId());
                dto.setName(info.getName());
                dto.setCode(info.getCode());
                dto.setDesc(info.getDescription());
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
        }
        return pageResult;
    }


    public String getOrgType() {
        //获取当前登录用户ID
        String orgId = ContextUtil.getCurrentGroupId();
        //查询机构类型
        String orgType = orgTeamMapper.selectOrgGroupTypeList(orgId);
        return orgType;
    }

    @Override
    public ResponsePageData<UompSupplierPersonnelDto> getPersonnelList(String supplierManagementId, String personName, String technicalDirection, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(
                pageNo != null ? pageNo : 1,
                pageSize != null ? pageSize : 10);
        String[] strings = null;
        if (!TextUtils.isEmpty(technicalDirection)) {
            strings = technicalDirection.split(",");
        }
        return null;
    }

    @Override
    public List<UompPersonInfoAndAccountDTO> selectNoAccountPeronList(QueryFilter queryFilter) {
        return uompPersonInfoMapper.selectNoAccountPeronList(queryFilter);
    }

    @Override
//    @AutoDesensitize(processMethod = "decrypt")
    public List<PersonSeletDTO> getPersonSeletList() {
        // 获取供应商信息
        IsSupplierDto isSupplierDto = supplierUtil.isSupplier();
        String ifSupplier = isSupplierDto.getIfSupplier();
        String supplierId = isSupplierDto.getSupplierId();

        List<PersonSeletDTO> personSeletDTOList = uompPersonInfoMapper.selectAllByPersonSelet(ifSupplier, supplierId);

        //学历字段查询
        List<BaseDTO> educationKeyList = sysDataDictMapper.selectSubListByDictKey("UOMP_EDUCATION");

        //特殊字段二次处理
        for (PersonSeletDTO it : personSeletDTOList) {
            //处理学历
            for (BaseDTO educationKey : educationKeyList) {
                String id = educationKey.getId();
                if (id.equals(it.getEducation())) {
                    it.setEducation(educationKey.getName());
                    break;
                }
            }
        }

        return personSeletDTOList;
    }

    @Override
    public List<Map<String, String>> getAcceptPersonSelectList(String id) {
        // 查询记录对应的用户同机构的用户
        List<AcceptPersonSelectDTO> acceptPersonSelectDTOList = uompPersonInfoMapper.selectAllByAdmissionPersonId(id);

        List<Map<String, String>> resultList = new ArrayList<>();
        for (AcceptPersonSelectDTO it : acceptPersonSelectDTOList) {
            Map<String, String> result = new HashMap<>();
            String account = StringUtils.isNotBlank(it.getAccount()) ? it.getAccount() : "无账号";
            result.put("personName", it.getPersonName() + "(" + account + ")_" + it.getWorkingCompany());
            result.put("id", it.getId());
            resultList.add(result);
        }

        return resultList;
    }

    @Override
    public BackgroundListDTO backgroundList() {
        BackgroundListDTO backgroundList = new BackgroundListDTO();
        List<KeyWordNumDTO> keyWordNumDTOList = uompPersonInfoMapper.countByBackgroundStatus();

        backgroundList.setResultX(new String[]{"合格", "不合格"});

        Integer[] numArray = new Integer[]{0, 0};
        //处理结果情况
        if (!keyWordNumDTOList.isEmpty()) {
            //1.如果数据，加两个0
            if (keyWordNumDTOList.size() == 1) { //如果只有一个结果，判断是合格的还是不合格的
                for (KeyWordNumDTO it : keyWordNumDTOList) {
                    if ("1".equals(it.getKeyWord())) {
                        numArray[0] = it.getNum();
                        numArray[1] = 0;
                    } else {
                        numArray[1] = 0;
                        numArray[0] = it.getNum();
                    }
                }
            } else {
                numArray[0] = keyWordNumDTOList.get(0).getNum();
                numArray[1] = keyWordNumDTOList.get(1).getNum();
            }
        }

        backgroundList.setResultY(numArray);

        return backgroundList;
    }

    @Override
    public List<PercentageDTO> getEducationList() {
        List<PercentageDTO> educationList = new ArrayList<>();

        List<KeyWordNumDTO> keyWordNumDTOList = uompPersonInfoMapper.countByTrialStatus();

        //结果数据二次处理
        int sum = 0;
        //学历字典查询
        List<BaseDTO> baseList = sysDataDictMapper.selectSubListByDictKey("UOMP_EDUCATION");

        PercentageDTO education;
        for (BaseDTO educationKey : baseList) {
            education = new PercentageDTO();
            education.setName(educationKey.getName());

            String key = educationKey.getId();
            for (KeyWordNumDTO item : keyWordNumDTOList) {
                if (item.getKeyWord().equals(key)) {
                    education.setNum(item.getNum());
                    break;
                }
            }

            //增加总数
            sum = sum + education.getNum();
            educationList.add(education);
        }

        //计算占比
        for (PercentageDTO item : educationList) {
            int num = item.getNum();
            if (0 == num) {
                item.setPercentage("0.00%");
            } else {
                BigDecimal percentage = new BigDecimal(num).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                item.setPercentage(percentage.toString() + '%');
            }
        }

        return educationList;
    }

    @Override
    public List<PercentageDTO> personAgeList() {
        List<PercentageDTO> percentageDTOList = new ArrayList<>();

        //25岁以下（含）
        int twentyFiveDown = 0;
        //25-30岁（含）
        int thirty = 0;
        //30-40岁（含）
        int forty = 0;
        //40-50岁（含）
        int fifty = 0;
        //50-60岁（含）
        int sixty = 0;
        //60岁以上
        int sixtyUp = 0;

        List<String> personBirthdayList = uompPersonInfoMapper.selectPersonBirthdayByTrialStatus();

        //当前时间
        LocalDate now = LocalDate.now();

        //结果集数据处理
        for (String personBirthday : personBirthdayList) {

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date birthDate;
            try {
                birthDate = sdf.parse(personBirthday);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            LocalDate birthLocalDate = birthDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            Period between = Period.between(birthLocalDate, now);
            int age = between.getYears();

            //判断
            if (age <= 25) {
                twentyFiveDown++;
            }
            if (age > 25 && age <= 30) {
                thirty++;
            }
            if (age > 30 && age <= 40) {
                forty++;
            }
            if (age > 40 && age <= 50) {
                fifty++;
            }
            if (age > 50 && age <= 60) {
                sixty++;
            }
            if (age > 60) {
                sixtyUp++;
            }
        }

        int sum = twentyFiveDown + thirty + forty + fifty + sixty + sixtyUp;

        //封装数据
        PercentageDTO percentageDTO = new PercentageDTO();
        percentageDTO.setName("25岁以下（含）");
        percentageDTO.setNum(twentyFiveDown);
        if (0 == twentyFiveDown) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(twentyFiveDown).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("25-30岁（含）");
        percentageDTO.setNum(thirty);
        if (0 == thirty) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(thirty).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("30-40岁（含）");
        percentageDTO.setNum(forty);
        if (0 == forty) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(forty).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("40-50岁（含）");
        percentageDTO.setNum(fifty);
        if (0 == fifty) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(fifty).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("50-60岁（含）");
        percentageDTO.setNum(sixty);
        if (0 == sixty) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(sixty).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("60岁以上");
        percentageDTO.setNum(sixtyUp);
        if (0 == sixtyUp) {
            percentageDTO.setPercentage("0.00%");
        } else {
            percentageDTO.setPercentage(new BigDecimal(sixtyUp).divide(new BigDecimal(sum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTOList.add(percentageDTO);

        return percentageDTOList;
    }

    @Override
    public void deactivateAccount(String personCard) {
        UompPersonInfo uompPersonInfo = uompPersonInfoMapper.selectAllByPersonCardAndTrialStatus(personCard);

        if (uompPersonInfo == null) {
            return;
        }

        String account = uompPersonInfo.getAccount();
        //通过账号获取到人员系统账号数据
        if (StringUtils.isNotEmpty(account)) {
            String userId = orgUserMapper.selectUserInfoByAccount(account);
            //通过user_id 调用停用账户接口
            orgUserMapper.updateStatusById1(userId, 0);
        }
    }

    @Override
    public PersonInfoDTO getPersonInfoByCard(String personCard) {
        PersonInfoDTO personInfoDTO = new PersonInfoDTO();
        if (StringUtils.isNotEmpty(personCard) && personCard.length() == 18){
            personCard = dataDesensitization.encrypt(personCard);
        }
        UompPersonInfo uompPersonInfo = uompPersonInfoMapper.selectAllByPersonCardAndTrialStatus(personCard);
        if (uompPersonInfo == null) {
            return null;
        }

        //通过账号获取到人员系统账号数据
        String account = uompPersonInfo.getAccount();
        String personName = uompPersonInfo.getPersonName();

        //封装账号，姓名信息
        personInfoDTO.setAccount(account);
        personInfoDTO.setPersonName(personName);

        if (StringUtils.isNotEmpty(account)) {
            String userId = orgUserMapper.selectUserInfoByAccount(account);
            if (StringUtils.isNotEmpty(userId)) {
                personInfoDTO.setUserId(userId);

                //查询运维组(去掉无效的)
//                personInfoDTO.setGroupName(orgPostMapper.selectNameByUserIdAndTypeIn(userId));
                GroupUomp groupUomp = uompOrgGroupMapper.get(uompPersonInfo.getOrgGroupId());
                if (groupUomp != null) {
                    personInfoDTO.setGroupName(groupUomp.getName());
                }

                //查询岗位
                personInfoDTO.setPostName(orgPostMapper.selectNameByUserIdAndTypeNotIn(userId));

                //查询角色
                personInfoDTO.setRoleName(orgRoleMapper.selectNameByUserId(userId));
            }
        }
        return personInfoDTO;
    }

    @Override
    public List<UompBlackDto> getBlackList(QueryFilter queryFilter) {
        return uompPersonInfoMapper.getBlackList(queryFilter);
    }

    @Override
    public void deleteByPersonCard(String id, String personCard) {
        //普通用户角色code
        String ptCode = "G_ROLE_GENERAL_USER";

        //通过身份证号获取user_id
        if (StringUtils.isNotEmpty(personCard) && personCard.length() == 18){
            personCard = dataDesensitization.encrypt(personCard);
        }
        UompPersonInfo uompPersonInfo = uompPersonInfoMapper.selectAllByPersonCardAndTrialStatus(personCard);
        if (uompPersonInfo != null && StringUtils.isNotBlank(uompPersonInfo.getAccount())) {
            String userId = uompPersonInfo.getOrgUserId();
            if (StringUtils.isBlank(userId) && StringUtils.isNotBlank(uompPersonInfo.getAccount())) {
                userId = orgUserMapper.selectUserInfoByAccount(uompPersonInfo.getAccount());
            }

            if (StringUtils.isBlank(userId)) {
                return;
            }

            //查出人员所有岗位，运维组，和角色
            //角色
            List<BaseDTO> roleList = orgRoleMapper.selectBaseByUserId(userId);

            //1.查询是否有外部权限，有的话删除
//            int num = uompFortressAssetsGrantMapper.countByUserId(userId);
//            if (num > 0) {
                // todo 531版本不实现《外部申请系统》，隐藏暂不实现
//                    uompPermissionApplicationService.deleteUser(userId);
//            }

            //2.删除授权角色
            List<OrgRelation> orgRoleList = new ArrayList<>();
            for (BaseDTO roleObj : roleList) {
                String code = roleObj.getName();
                //如果是普通用户则略过
                if (ptCode.equals(code)) {
                    continue;
                }

                String roleId = roleObj.getId();

                OrgRelation orgRelation = new OrgRelation();
                orgRelation.setGroupId(roleId);
                orgRelation.setType("userRole");
                orgRelation.setUserId(userId);

                orgRoleList.add(orgRelation);
            }
            if (!orgRoleList.isEmpty()) {
                orgRelationManager.batchRemove(orgRoleList);
            }

            //3.岗位，运维组
            List<OrgPostDTO> postList = orgPostMapper.selectAllByUserId(userId);

            List<OrgRelation> orgPostList = new ArrayList<>();
            for (OrgPostDTO post : postList) {
                String postId = post.getId();
                String orgId = post.getOrgId();

                OrgRelation orgRelation = new OrgRelation();
                orgRelation.setGroupId(postId);
                orgRelation.setType("postUser");
                orgRelation.setUserId(userId);
                orgRelation.setOrgId(orgId);

                orgPostList.add(orgRelation);
            }
            if (!orgPostList.isEmpty()) {
                orgRelationManager.batchRemove(orgPostList);
            }

            //删除后将退场数据中的is_delete改为0
            uompExitApplicationMapper.updateIsDeleteById(id);
        }
    }

    @Override
    public String selectAccountByPersonCard(String personCard) {
        return uompPersonInfoMapper.selectAccountByPersonCard(personCard);
    }

    @Override
    public Integer countByOrg(String orgGroupId) {
        return uompPersonInfoMapper.countByOrg(orgGroupId);
    }

    @Override
    public List<UompPersonInfo> selectAllByOrg(String orgGroupId) {
        return uompPersonInfoMapper.selectAllByOrg(orgGroupId);
    }

    @Override
    public List<UompPersonInfo> selectAllByOrgStatusAll(String orgGroupId) {
        return uompPersonInfoMapper.selectAllByOrgStatusAll(orgGroupId);
    }

    @Override
    public void updatePersonAllInfo(String personId) {
        UompPersonAllInfoTemp infoTemp = uompPersonAllInfoTempService.getByPeronId(personId);
        // 恢复临时表数据
        if (infoTemp != null && !StringUtils.isEmpty(infoTemp.getPersonInfo())) {
            UompPersonInfoVo personInfoDetailDto = JSONObject.parseObject(infoTemp.getPersonInfo(), UompPersonInfoVo.class);
            UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
            personInfoDetailDto.setPersonCard(null);
            personInfoDetailDto.setUpdateOrgId(user.getOrgId());
            personInfoDetailDto.setUpdating("0");
            personInfoDetailDto.setInstId(null);
            uompPersonInfoMapper.updateByPrimaryKeySelective(personInfoDetailDto);
            saveAndUpdatePersonDetail(user, personInfoDetailDto);
            uompPersonAllInfoTempService.remove(infoTemp.getId());
        }
    }

    @Override
    public void saveAndUpdatePersonDetail(UserDTO user, UompPersonInfoVo personInfo) {
        if (personInfo.getEducationList() != null && personInfo.getEducationList().size() > 0) {
            uompPersonEducationalService.deleteByPersonId(personInfo.getId());
            for (UompPersonEducational person : personInfo.getEducationList()) {
                person.setPersonId(personInfo.getId());
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("EDUCATION_BEGIN_TIME", person.getEducationBeginTime(), QueryOP.EQUAL);
                queryFilter.addFilter("EDUCATION_END_TIME", person.getEducationEndTime(), QueryOP.EQUAL);
                queryFilter.addFilter("SCHOOL", person.getSchool(), QueryOP.EQUAL);
                queryFilter.addFilter("MAJOR", person.getMajor(), QueryOP.EQUAL);
                queryFilter.addFilter("EDUCATION_BACKGROUND", person.getEducationBackground(), QueryOP.EQUAL);
                queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", person.getPersonId(), QueryOP.EQUAL);
                List<UompPersonEducational> personInfos = uompPersonEducationalService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    throw new BusinessException("该教育经历在列表中已存在，请勿重复添加");
                }
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonEducationalService.create(person);
            }
        }
        if (personInfo.getJobList() != null && personInfo.getJobList().size() > 0) {
            uompPersonJobService.deleteByPersonId(personInfo.getId());
            for (UompPersonJob person : personInfo.getJobList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonJobService.create(person);
            }
        }
        if (personInfo.getTechList() != null && personInfo.getTechList().size() > 0) {
            uompPersonTechnologyService.deleteByPersonId(personInfo.getId());
            for (UompPersonTechnology person : personInfo.getTechList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonTechnologyService.create(person);
            }
        }
        if (personInfo.getSocialList() != null && personInfo.getSocialList().size() > 0) {
            uompPersonSocialService.deleteByPersonId(personInfo.getId());
            for (UompPersonSocial person : personInfo.getSocialList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonSocialService.create(person);
            }
        }
        if (personInfo.getNoCrimeList() != null && personInfo.getNoCrimeList().size() > 0) {
            uompPersonNoCrimeService.deleteByPersonId(personInfo.getId());
            for (UompPersonNoCrime person : personInfo.getNoCrimeList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonNoCrimeService.create(person);
            }
        }
        if (personInfo.getAbroadList() != null && personInfo.getAbroadList().size() > 0) {
            uompPersonAbroadService.deleteByPersonId(personInfo.getId());
            for (UompPersonAbroad person : personInfo.getAbroadList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonAbroadService.create(person);
            }
        }
        if (personInfo.getEntryExitList() != null && personInfo.getEntryExitList().size() > 0) {
            uompPersonEntryExitService.deleteByPersonId(personInfo.getId());
            for (UompPersonEntryExit person : personInfo.getEntryExitList()) {
                person.setPersonId(personInfo.getId());
                person.setCreateOrgId(user.getOrgId());
                person.setDelFlag("0");
                uompPersonEntryExitService.create(person);
            }
        }
    }

    /**
     * 查询逻辑：
     * 1. 人员审核通过的（status = '2'）
     * 2. 审核通过的人员背调状态分组占比
     */
    @Override
    public List<PercentageDTO> backgroundInfoPie() {
        List<PercentageDTO> backgroundList = new ArrayList<>();
        // 查询背调字典
        List<BaseDTO> baseList = sysDataDictMapper.selectSubListByDictKey("UOMP_BACKGROUND_STATUS");
        // 查询人员信息 合格  不合格  未审查人数
        List<KeyWordNumDTO> keyWordNumDTOList = uompPersonInfoMapper.countPassPersonByBackgroundStatus();
        // 总数
        int sum = 0;
        if (!CollectionUtils.isEmpty(keyWordNumDTOList)) {
            sum = keyWordNumDTOList.stream().mapToInt(KeyWordNumDTO::getNum).sum();
        }
        // 封装返回类
        backgroundList = this.getPercentageData(sum, baseList, keyWordNumDTOList, backgroundList);
        return backgroundList;
    }

    /**
     * 封装背调人员的状态占比对象
     * @param sum 人员审核通过总数
     * @param baseList 背调人员状态枚举集合
     * @param keyWordNumList 查询背调人员状态的占比情况集合
     * @param backgroundList 背调人员状态响应对象集合
     **/
    private List<PercentageDTO> getPercentageData(int sum, List<BaseDTO> baseList, List<KeyWordNumDTO> keyWordNumList, List<PercentageDTO> backgroundList) {
        PercentageDTO background;
        for (BaseDTO baseDTO : baseList) {
            background = new PercentageDTO();
            background.setName(baseDTO.getName());
            String key = baseDTO.getId();
            for (KeyWordNumDTO item : keyWordNumList) {
                if (item.getKeyWord().equals(key)) {
                    background.setNum(item.getNum());
                    break;
                }
            }
            backgroundList.add(background);
        }
        if (!CollectionUtils.isEmpty(backgroundList)) {
            for (PercentageDTO item : backgroundList) {
                int num = item.getNum();
                item.setPercentage(this.getPercentageNum(num, sum));
            }
        }
        return backgroundList;
    }

    /**
     * 计算数量占比
     * @param num 个数
     * @param sum 总数
     **/
    private String getPercentageNum(int num, int sum) {
        if (0 == num) {
            return "0.00%";
        } else {
            BigDecimal percentage = new BigDecimal(num).divide(new BigDecimal(sum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
            return percentage.toString() + '%';
        }
    }

    @Override
    public List<PercentageDTO> backgroundInfoPieByWorkingCompanyId(String workingCompanyId) {
        List<PercentageDTO> backgroundList = new ArrayList<>();
        // 查询背调字典
        List<BaseDTO> baseList = sysDataDictMapper.selectSubListByDictKey("UOMP_BACKGROUND_STATUS");
        // 查询人员信息 合格  不合格  未审查人数
        List<KeyWordNumDTO> keyWordNumDTOList = uompPersonInfoMapper.countPassPersonByBackgroundStatusAndWorkingCompanyId(workingCompanyId);
        // 总数
        int sum = 0;
        if (!CollectionUtils.isEmpty(keyWordNumDTOList)) {
            sum = keyWordNumDTOList.stream().mapToInt(KeyWordNumDTO::getNum).sum();
        }
        // 封装返回类
        backgroundList = this.getPercentageData(sum, baseList, keyWordNumDTOList, backgroundList);
        return backgroundList;
    }

    /**
     * 查询逻辑：
     * 1. 人员审核通过的（status = '2'）
     * 2. 服务商状态是合作中的（status = '1'）
     * 3. 合作中服务商中背调合格的占比top5（即：服务商人员合格/服务商人员总数 top5）
     * 4. 合作中服务商中不合格人员集合
     */
    @Override
    public ColumnarDTO backgroundInfoColumnar() {
        ColumnarDTO dto = new ColumnarDTO();
        // 查询总数和合格的服务商信息
        //11-22 增加sql 非黑，在职的条件
        List<PersonCompanyDTO> passList = uompPersonInfoMapper.getWorkingCompanyTotalAndPass();
        // 若服务商查询总数不存在，也不存在合格、不合格人员
        if (CollectionUtils.isEmpty(passList)) {
            return dto;
        }
        List<ColumnarBaseDTO> perList = new ArrayList<>();
        // 计算占比
        for (PersonCompanyDTO companyDTO : passList) {
            ColumnarBaseDTO percentageDTO = new ColumnarBaseDTO();
            percentageDTO.setName(companyDTO.getName());
            int num = companyDTO.getNum();
            int sum = companyDTO.getTotal();
            percentageDTO.setNum(companyDTO.getNum());
            if (0 == companyDTO.getNum()) {
                percentageDTO.setPercentage(0.00d);
            } else {
                BigDecimal percentage = new BigDecimal(num).divide(new BigDecimal(sum), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
                percentageDTO.setPercentage(percentage.doubleValue());
            }
            perList.add(percentageDTO);
        }
        // 占比降序取前5
        dto.setPassList(perList.stream()
                .sorted(Comparator.comparing(ColumnarBaseDTO::getPercentage).reversed())
                .limit(5)
                .collect(Collectors.toList()));
        // 查询不合格人员信息
        //11-22 增加sql 非黑，在职的条件
        List<WorkAndPersonDTO> noPassList = uompPersonInfoMapper.getWorkingCompanyNoPass();
        if (!CollectionUtils.isEmpty(noPassList)) {
            List<PersonBaseDTO> noList = new ArrayList<>();
            // 公司名称：不合格人员集合
            Map<String, PersonBaseDTO> map = new HashMap<>();
            for (WorkAndPersonDTO base : noPassList) {
                Set<String> keys = map.keySet();
                // 封装返回对象
                if (keys.contains(base.getWorkId())) {
                    PersonBaseDTO personBase = map.get(base.getWorkId());
                    List<BaseDTO> baseList = personBase.getNameList();
                    BaseDTO baseDTO = new BaseDTO();
                    baseDTO.setId(base.getPersonId());
                    baseDTO.setName(base.getPersonName());
                    baseList.add(baseDTO);
                    personBase.setNameList(baseList);
                    map.put(base.getWorkId(), personBase);
                } else {
                    PersonBaseDTO personBase = new PersonBaseDTO();
                    List<BaseDTO> baseList = new ArrayList<>();
                    BaseDTO baseDTO = new BaseDTO();
                    baseDTO.setId(base.getPersonId());
                    baseDTO.setName(base.getPersonName());
                    baseList.add(baseDTO);
                    personBase.setNameList(baseList);
                    personBase.setWorkId(base.getWorkId());
                    personBase.setWorkName(base.getWorkName());
                    map.put(base.getWorkId(), personBase);
                }
            }
            for (String key : map.keySet()) {
                noList.add(map.get(key));
            }
            dto.setNoList(noList);
        }
        return dto;
    }

    @Override
    public PersonStructureDTO personStructure() {
        PersonStructureDTO person = new PersonStructureDTO();
        // 获取内部人员
        List<UompPersonInfo> inside = getPersonInfoByOrgGroupType("1", "1", "");
        // 获取外部人员
        List<UompPersonInfo> outside = getPersonInfoByOrgGroupType("1", "2", "");

        List<PercentageDTO> percentageDTO = new ArrayList<>();

        int insideNum = inside != null ? inside.size() : 0, outsideNum = outside != null ? outside.size() : 0;
        person.setTotal(String.valueOf(insideNum + outsideNum));
        PercentageDTO insidePercentageDTO = new PercentageDTO();
        if (!CollectionUtils.isEmpty(inside)) {
            // 内部
            insidePercentageDTO.setName("内部");
            insidePercentageDTO.setNum(insideNum);
            insidePercentageDTO.setPercentage(new BigDecimal(insideNum).divide(new BigDecimal(person.getTotal()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTO.add(insidePercentageDTO);
        PercentageDTO outsidePercentageDTO = new PercentageDTO();
        if (!CollectionUtils.isEmpty(outside)) {
            // 外部
            outsidePercentageDTO.setName("外部");
            outsidePercentageDTO.setNum(outsideNum);
            outsidePercentageDTO.setPercentage(new BigDecimal(outsideNum).divide(new BigDecimal(person.getTotal()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        }
        percentageDTO.add(outsidePercentageDTO);

        person.setPercentageDTO(percentageDTO);
        return person;
    }

    @Override
    public String getOrgUserIdByUserId(String userId) {
        return uompPersonInfoMapper.getOrgUserIdByUserId(userId);
    }

    /**
     * 查询逻辑：
     * 审查通过的人员
     */
    @Override
    public PersonWorkAndEduDTO personWorkAndEdu() {
        PersonWorkAndEduDTO dto = new PersonWorkAndEduDTO();
        List<PercentageDTO> eduList = new ArrayList<>();
        // 查询学历
        // 获取学历字典
        List<BaseDTO> eduBaseList = sysDataDictMapper.selectSubListByDictKey("UOMP_EDUCATION");
        // 查询审核通过的人员学历统计
        //11-22 增加筛选条件 在职，非黑，背调非不合格
        List<KeyWordNumDTO> list = uompPersonInfoMapper.countByEdu();
        // 总数
        int sum = 0;
        if (!CollectionUtils.isEmpty(list)) {
            sum = list.stream().mapToInt(KeyWordNumDTO::getNum).sum();
        }
        // 封装返回类
        eduList = this.getPercentageData(sum, eduBaseList, list, eduList);
        dto.setEduList(eduList);
        // 查询审核通过的人员在职
        List<PercentageDTO> percentageDTOList = new ArrayList<>();
        // 不足一年
        int oneDown = 0;
        // （含）1-3年
        int one = 0;
        //（含）3-5年
        int three = 0;
        //（含）5-10年
        int five = 0;
        //（含）10年以上
        int tenUp = 0;
        //11-22 增加筛选条件 在职，非黑，背调非不合格
        List<String> entryList = uompPersonInfoMapper.countByEntry();
        //当前时间
        LocalDate now = LocalDate.now();
        //结果集数据处理
        for (String personEntry : entryList) {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.yyyyMMdd);
            Date entryDate;
            try {
                entryDate = sdf.parse(personEntry);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            // 获取两个日期之间的年数
            LocalDate entryLocalDate = entryDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            long work = ChronoUnit.YEARS.between(entryLocalDate, now);
            //判断
            if (work < 1) {
                oneDown++;
            }
            if (work >= 1 && work < 3) {
                one++;
            }
            if (work >= 3 && work < 5) {
                three++;
            }
            if (work >= 5 && work < 10) {
                five++;
            }
            if (work >= 10) {
                tenUp++;
            }
        }
        int entrySum = oneDown + one + three + five + tenUp;
        //封装数据
        PercentageDTO percentageDTO = new PercentageDTO();
        percentageDTO.setName("不足1年");
        percentageDTO.setNum(oneDown);
        // 获取占比数
        percentageDTO.setPercentage(this.getPercentageNum(oneDown, entrySum));
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("1-3年");
        percentageDTO.setNum(one);
        percentageDTO.setPercentage(this.getPercentageNum(one, entrySum));
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("3-5年");
        percentageDTO.setNum(three);
        percentageDTO.setPercentage(this.getPercentageNum(three, entrySum));
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("5-10年");
        percentageDTO.setNum(five);
        percentageDTO.setPercentage(this.getPercentageNum(five, entrySum));
        percentageDTOList.add(percentageDTO);

        percentageDTO = new PercentageDTO();
        percentageDTO.setName("10年以上");
        percentageDTO.setNum(tenUp);
        percentageDTO.setPercentage(this.getPercentageNum(tenUp, entrySum));
        percentageDTOList.add(percentageDTO);

        dto.setWorkList(percentageDTOList);
        return dto;
    }

    @Override
    public List<TechnicalDirectionDTO> technicalDirection() {
        List<TechnicalDirectionDTO> technicalDirectionDTOS = new ArrayList<>();

        List<SysDataDict> sysDataDicts = dictUtil.getAllByDictKey("UOMP_TEC_DIRECTION");

        TechnicalDirectionDTO technicalDirectionDTO;
        for (SysDataDict sd : sysDataDicts) {
            technicalDirectionDTO = new TechnicalDirectionDTO();
            List<PercentageDTO> percentageDTOList = new ArrayList<>();
            // 获取内部人员
            List<UompPersonInfo> inside = getPersonInfoByOrgGroupType("1", "1", sd.getKey());
            PercentageDTO insidePercentage = new PercentageDTO();
            insidePercentage.setName("内部");
            insidePercentage.setNum(inside != null ? inside.size() : 0);
            percentageDTOList.add(insidePercentage);
            // 获取外部人员
            List<UompPersonInfo> outside = getPersonInfoByOrgGroupType("1", "2", sd.getKey());
            PercentageDTO outsidePercentage = new PercentageDTO();
            outsidePercentage.setName("外部");
            outsidePercentage.setNum(outside != null ? outside.size() : 0);
            percentageDTOList.add(outsidePercentage);

            technicalDirectionDTO.setTechnicalDirection(sd.getName());
            technicalDirectionDTO.setPercentageDTO(percentageDTOList);

            technicalDirectionDTOS.add(technicalDirectionDTO);
        }

        return technicalDirectionDTOS;
    }

    /**
     * 根据类型查询对应机构 id list 并获取对应用户列表
     *
     * @param type  机构类型
     * @param equal 等于还是不等于
     * @return
     */
    private List<UompPersonInfo> getPersonInfoByOrgGroupType(String type, String equal, String technicalDirection) {
        // 查询对应类型下的机构 id list
        List<String> orgGroupIds = uompOrgGroupMapper.selectIdByType(type, equal);
        if (orgGroupIds == null || orgGroupIds.size() == 0) {
            return null;
        }
        //11-20 修改uompPersonInfoMapper.selectAllByOrgGroupId方法，sql中添加blacklist和dimission的条件
        //blacklist = 0, dimission = 0
        return uompPersonInfoMapper.selectAllByOrgGroupId(orgGroupIds, technicalDirection);
    }

    @Override
    public List<PercentageDTO> getPersonInTimeBySupplierId(String supplierId) {
        List<PercentageDTO> percentageDTOS = uompPersonInfoMapper.getPersonInTime(supplierId, "");
        return getPersonInTimeInfo(percentageDTOS);
    }

    @Override
    public List<PercentageDTO> getPersonInTimeBySupplierName(String supplierName) {
        List<PercentageDTO> percentageDTOS = uompPersonInfoMapper.getPersonInTime("", supplierName);
        return getPersonInTimeInfo(percentageDTOS);
    }

    private List<PercentageDTO> getPersonInTimeInfo(List<PercentageDTO> percentageDTOS) {
        List<PercentageDTO> dtos = new ArrayList<>();
        Map<Integer, Integer> map = new HashMap<>();
        map.put(1, 0);
        map.put(2, 0);
        map.put(3, 0);
        map.put(4, 0);
        map.put(5, 0);

        Integer total = 0;
        for (PercentageDTO dto : percentageDTOS) {
            if (StringUtils.isEmpty(dto.getName())) {
                continue;
            }
            total += dto.getNum();
            int year = Integer.parseInt(dto.getName());
            if (year < 1) {
                map.put(1, map.get(1) + dto.getNum());
            } else if (year < 3) {
                map.put(2, map.get(2) + dto.getNum());
            } else if (year < 5) {
                map.put(3, map.get(3) + dto.getNum());
            } else if (year < 10) {
                map.put(4, map.get(4) + dto.getNum());
            } else {
                map.put(5, map.get(5) + dto.getNum());
            }
        }
        for (Integer key : map.keySet()) {
            PercentageDTO percentageDTO = new PercentageDTO();
            percentageDTO.setNum(map.get(key));
            if (key == 1) {
                percentageDTO.setName("不足1年");
            } else if (key == 2) {
                percentageDTO.setName("1-3年");
            } else if (key == 3) {
                percentageDTO.setName("3-5年");
            } else if (key == 4) {
                percentageDTO.setName("5-10年");
            } else {
                percentageDTO.setName("10年以上");
            }
            if (map.get(key) > 0 && total > 0) {
                percentageDTO.setPercentage(new BigDecimal(map.get(key)).divide(new BigDecimal(total), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%");
            }
            dtos.add(percentageDTO);
        }
        return dtos;
    }


    /**
     * 账号申请管理列表查询逻辑：
     * 1. 查询运管人员表的账号申请列表（账号审核通过，人员审核通过）
     * 2. 依据系统人员id关联系统人员表查询账号状态
     */
    @Override
    public PageResult<AccountPersonDTO> list(QueryFilter queryFilter) {
        // 人员未删除
        queryFilter.addFilter("upi.DEL_FLAG", "0", QueryOP.EQUAL);
        // 人员审核通过
        queryFilter.addFilter("upi.TRIAL_STATUS", "2", QueryOP.EQUAL);
        // 人员账号审核通过
        queryFilter.addFilter("upi.IS_ACCOUNT", "1", QueryOP.EQUAL);
        // 系统人员账号已激活
        queryFilter.addFilter("ou.ACTIVE_STATUS_", "1", QueryOP.EQUAL);
        // 账号申请时间倒叙
        queryFilter.addFieldSort("ou.CREATE_TIME_", "desc");
        List<AccountPersonDTO> list = uompPersonInfoMapper.getAccountPassList(queryFilter);
        return new PageResult<>(list);
    }

    /**
     * 添加账号逻辑：
     * 1. 判断人员是否非黑名单--防止多人操作导致黑名单人员创建账号
     * 2. 封装对象，调用系统创建账号接口,默认是激活，启用状态
     * 3. 用户默认角色（运管平台基础用户）
     * 4. 更新人员表账号信息：账号，系统用户id
     */
    @Override
    public String addAccount(AccountPersonAddDTO accountPersonAddDTO) {
        // 运维人员id
        String userId = accountPersonAddDTO.getUserId();
        // 判断人员是否是黑名单
        if (StringUtils.isNotEmpty(userId)) {
            UompPersonInfo uompPersonInfo = uompPersonInfoMapper.get(userId);
            if (null == uompPersonInfo) {
                throw new BusinessMessage("人员不存在！");
            }
            String blacklist = uompPersonInfo.getBlacklist();
            if ("1".equals(blacklist)) {
                throw new BusinessMessage("黑名单人员不能创建账号！");
            }
        }
        // 用户id置空，封装UserDetail（系统用户id）走添加
        accountPersonAddDTO.setUserId(null);
        String account = accountPersonAddDTO.getAccount();
        validatePassword(accountPersonAddDTO.getPassword());
        // 封装创建账号对象
        UserDetail userDetail = new UserDetail();
        BeanUtils.copyProperties(accountPersonAddDTO, userDetail);
        userDetail.setSn(0);
        userDetail.setLoginName(accountPersonAddDTO.getAccount());
        userDetail.setStatus(1);
        userDetail.setActiveStatus(1);
        OrgRelationCustom orgRelationCustom = new OrgRelationCustom();
        orgRelationCustom.setIsMaster(1);
        orgRelationCustom.setType("groupUser");
        orgRelationCustom.setGroupId(accountPersonAddDTO.getOrgGroupId());
        userDetail.setOrgRelationList(Arrays.asList(orgRelationCustom));
        userDetailManager.save(userDetail);
        // 根据账号查询系统用户id
        String orgUserId = orgUserMapper.selectUserInfoByAccount(account);
        // 添加默认账号角色： 运管平台基础用户：G_ROLE_GENERAL_USER
        List<OrgRelation> orgRoleList = new ArrayList<>();
        // 运管平台基础用户角色
        OrgRole generalUserRole = orgRoleMapper.getRoleByAlias("G_ROLE_GENERAL_USER");
        if (null != generalUserRole) {
            orgRoleList.add(getRoleRelInfo(generalUserRole.getId(), orgUserId));
        }
        // 添加默认角色
        if (!CollectionUtils.isEmpty(orgRoleList)) {
            orgRelationManager.batchAdd(orgRoleList);
        }
        // 更新人员账号信息
        UompPersonInfo uompPersonInfo = new UompPersonInfo();
        uompPersonInfo.setAccount(account);
        uompPersonInfo.setIsAccount("1");
        uompPersonInfo.setOrgUserId(orgUserId);
        uompPersonInfo.setUpdateTime(new Date());
        uompPersonInfo.setUpdateBy(ContextUtil.getCurrentUserId());
        uompPersonInfo.setId(userId);
        uompPersonInfoMapper.updateByPrimaryKeySelective(uompPersonInfo);
        return orgUserId;
    }

    private OrgRelation getRoleRelInfo(String id, String userId) {
        OrgRelation orgRelation = new OrgRelation();
        orgRelation.setGroupId(id);
        orgRelation.setType("userRole");
        orgRelation.setSn("0");
        orgRelation.setUserId(userId);
        return orgRelation;
    }

    /**
     * 更新账号状态逻辑：
     * 1. 启动时，判断用户是否是黑名单，黑名单不能启用
     * 2. 查询对应系统用户id
     * 3. 更新账号启用、停用接口
     */
    @Override
    public void updateAccountStatus(String ids, String status) {
        // 黑名单人员名称集合
        List<String> blackPersonNameList = new ArrayList<>();
        // 系统用户id集合
        List<String> orgUserIdList = new ArrayList<>();
        // 查询系统用户id集合
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        // 未删除
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        // id集合
        queryFilter.addFilter("ID", ids, QueryOP.IN);
        List<UompPersonInfo> infoList = uompPersonInfoMapper.query(queryFilter);
        if (!CollectionUtils.isEmpty(infoList)) {
            for (UompPersonInfo uompPersonInfo : infoList) {
                // 判断启用人员是否是黑名单
                if ("1".equals(status) && "1".equals(uompPersonInfo.getBlacklist())) {
                    blackPersonNameList.add(uompPersonInfo.getPersonName());
                } else {
                    String orgUserId = uompPersonInfo.getOrgUserId();
                    if (StringUtils.isNotEmpty(orgUserId)) {
                        orgUserIdList.add(orgUserId);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(orgUserIdList)) {
            String orgUserIds = String.join(",", orgUserIdList);
            QueryFilter updateFilter = new DefaultQueryFilter(true);
            updateFilter.addFilter("id_", orgUserIds, QueryOP.IN);
            updateFilter.getParams().put("status", Integer.valueOf(status));
            orgUserMapper.updateStatus(updateFilter);
            orgUserIdList.forEach(id ->{
                User orgUser = userManager.get(id);
                iCache.delByKey("jwtToken:jwt:pc:".concat(orgUser.getAccount()));
                iCache.delByKey("ecloudframework:loginUser:".concat(orgUser.getAccount()));
            });
        }
        if (!CollectionUtils.isEmpty(blackPersonNameList)) {
            StringBuilder result = new StringBuilder();
            result.append("黑名单人员[").append(String.join(",", blackPersonNameList)).append("]不能启用账号！");
            throw new BusinessMessage(result.toString());
        }


    }

    @Override
    public String updateAccountByPersonId(String id, String accountNum) {
        //声明根据账号查询人员id的sql
        String orgUserId = orgUserMapper.selectOrgUserByAccount(accountNum);
        // 根据 id 查询用户驻场申请信息
        UompAdmissionPerson uompAdmissionPerson = uomAdmissionPersonService.get(id);
        if (uompAdmissionPerson == null) {
            throw new BusinessMessage("数据异常,请重试!");
        }
        // 查询是否已存在账号申请记录
        UompAccountApplyPerson uompAccountApplyPerson = uompAccountApplyPersonService.getPersonAccountStatus(uompAdmissionPerson.getPersonId());
        // 查询根据账号信息
        if (null == uompAccountApplyPerson || StringUtils.isBlank(uompAccountApplyPerson.getId())) {
            UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
            uompAccountApplyPerson = new UompAccountApplyPerson();
            uompAccountApplyPerson.setApplyId(uompAdmissionPerson.getId());
            uompAccountApplyPerson.setPersonId(uompAdmissionPerson.getPersonId());
            uompAccountApplyPerson.setPersonName(uompAdmissionPerson.getPersonName());
            uompAccountApplyPerson.setRole(uompAdmissionPerson.getPersonRoleJson());
            uompAccountApplyPerson.setPosition(uompAdmissionPerson.getPostJson());
            uompAccountApplyPerson.setEntryDate(uompAdmissionPerson.getEntryDate());
            uompAccountApplyPerson.setAuthorizationStatus("0");
            uompAccountApplyPerson.setMaintenanceGroupId(uompAdmissionPerson.getMaintenanceGroupId());
            uompAccountApplyPerson.setMaintenanceGroupJson(uompAdmissionPerson.getMaintenanceGroupJson());
            uompAccountApplyPerson.setEngagementProjectId(uompAdmissionPerson.getEngagementProjectId());
            uompAccountApplyPerson.setEngagementProjectJson(uompAdmissionPerson.getEngagementProjectJson());
            uompAccountApplyPerson.setCreateBy(user.getUserId());
            uompAccountApplyPerson.setCreateTime(new Date());
            uompAccountApplyPerson.setCreateOrgId(user.getOrgId());
            uompAccountApplyPersonService.insertSelective(uompAccountApplyPerson);
        }

        //声明修改账号申请信息中人员id，账号信息的sql
        uompAccountApplyPerson.setOrgUserId(orgUserId);
        uompAccountApplyPerson.setAccountNum(accountNum);
        uompAccountApplyPersonService.update(uompAccountApplyPerson);
        // 更新人员分配账号状态、账号
        UompPersonInfo uompPersonInfo = new UompPersonInfo();
        uompPersonInfo.setId(uompAccountApplyPerson.getPersonId());
        uompPersonInfo.setIsAccount("1"); // 已分配账号
        uompPersonInfo.setAccount(accountNum);
        uompPersonInfo.setOrgUserId(orgUserId);
        uompPersonInfoMapper.updateByPrimaryKeySelective(uompPersonInfo);

        return orgUserId;
    }

    @Override
    public PageResult<PostDTO> noPostList(QueryFilter queryFilter, String orgUserId) {
        if (StringUtils.isEmpty(orgUserId)){
            orgUserId = ContextUtil.getCurrentUserId();
        }
        // 查询本人拥有的岗位
        List<OrgPostBaseDTO> owerPostList = orgRelationMapper.selectOwerListByUserId(orgUserId);
        String ids = "";
        if (!CollectionUtils.isEmpty(owerPostList)) {
            ids = owerPostList.stream().map(item -> item.getId()).collect(Collectors.joining(","));
        }
        // 排除本人拥有的岗位
        if (StringUtils.isNotEmpty(ids)) {
            queryFilter.addFilter("opost.id_", ids, QueryOP.NOT_IN);
        }
        queryFilter.addFieldSort("opost.create_time_", "desc");
        // 查询用户不存在的岗位列表
        List<PostDTO> list = orgRelationMapper.selectNoOwerPostList(queryFilter);
        return new PageResult<>(list);
    }

    @Override
    public UompPersonInfo selectOneByPersonIdOrPersonCard(String personId, String personCard) {
        return uompPersonInfoMapper.selectOneByIdOrPersonCard(personId, personCard);
    }

    @Override
    public Boolean updateEntryStatusByPersonIdOrPersonCard(String entryStatus, String personId, String personCard) {
        return uompPersonInfoMapper.updateEntryStatusByIdOrPersonCard(entryStatus, personId, personCard) > 0;
    }

    @Override
    public List<UompPersonContactsDTO> selectContacts(QueryFilter queryFilter) {
        return uompPersonInfoMapper.selectContacts(queryFilter);
    }

    @Override
    public Integer countAllByNormal() {
        return uompPersonInfoMapper.countByTrialStatusAndBlacklistAndBackgroundStatusNotAndDimissionAndEntryStatus("2", "0", "2", "0", null);
    }

    @Override
    public Integer countAllByEntry() {
        return uompPersonInfoMapper.countByTrialStatusAndBlacklistAndBackgroundStatusNotAndDimissionAndEntryStatus("2", "0", "2", "0", "0");
    }

    @Override
    public UompPersonInfo selectAllByUserId(String userId) {
        return uompPersonInfoMapper.selectAllByUserId(userId);
    }

    @Override
    @AutoDesensitize(processMethod = "encrypt")
    public void saveAllPersonInfo(UompPersonInfoVo personInfo) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date birthday = null;
        try {
            birthday = sdf.parse(personInfo.getPersonBirthday());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if (birthday.after(new Date())) {
            throw new BusinessException("出生年月不能在当前日期之后！");
        }
        if (!StringUtils.isEmpty(personInfo.getRegPermanentResidence())) {
            String[] strings = personInfo.getRegPermanentResidence().split(",");
            for (int i = 0; i < strings.length; i++) {
                if (i == 0) {
                    personInfo.setRegProvince(strings[0]);
                }
                if (i == 1) {
                    personInfo.setRegCity(strings[1]);
                }
                if (i == 2) {
                    personInfo.setRegRegion(strings[2]);
                }
            }
        }
        if (!StringUtils.isEmpty(personInfo.getId())) {
            UompPersonInfo uompPersonInfo = get(personInfo.getId());
            String personCard = uompPersonInfo.getPersonCard();
            //身份证号重复校验
            if (!personInfo.getPersonCard().contains("*") && !personInfo.getPersonCard().equals(personCard)) {
                IdCardValidate(personInfo.getPersonCard());
            }
            //电话号码重复检验
            if (!personInfo.getTel().equals(uompPersonInfo.getTel())) {
                telValidate(personInfo.getTel());
            }
            if (!StringUtils.isEmpty(personInfo.getUpdating()) && "1".equals(personInfo.getUpdating())) {
                // 之前存在临时数据，删除后新增
                UompPersonAllInfoTemp temp = uompPersonAllInfoTempService.getByPeronId(personInfo.getId());
                if (temp != null) {
                    uompPersonAllInfoTempService.remove(temp.getId());
                }
                //todo 暂时没有弄清楚临时表是干什么用的，身份证号暂时不脱敏；
                uompPersonAllInfoTempService.insertSelective(setUompPersonAllInfoTemp(personInfo));
                // 更新状态      06.14 改为流程脚本中更新
//                uompPersonInfo.setUpdating("1");
//                uompPersonInfoService.updateByPrimaryKeySelective(uompPersonInfo);
            } else {
                personInfo.setPersonCard(null);//修改时，身份证没有发生变化，不修改身份证号
                personInfo.setUpdateOrgId(user.getOrgId());
                personInfo.setUpdateTime(new Date());
                personInfo.setUpdateBy(user.getUserId());
                this.updateByPrimaryKeySelective(personInfo);
                this.saveAndUpdatePersonDetail(user, personInfo);
            }
        } else {
            IdCardValidate(personInfo.getPersonCard());
            telValidate(personInfo.getTel());
            complementPersonInfo(personInfo,user.getOrgId());
            insertSelective(personInfo);
            saveAndUpdatePersonDetail(user, personInfo);
        }
    }

    @Override
    @AutoDesensitize(processMethod = "sensitive")
    public UompPersonInfoVo getPersonAllInfo(String id, String updating, String instId) {
        UompPersonInfoVo personInfoDetailDto = null;

        if (!StringUtils.isEmpty(updating) && "1".equals(updating)) {
            personInfoDetailDto = getTempPersonInfo(updating,id);
            if (personInfoDetailDto != null ){
                return personInfoDetailDto;
            }
        }

        UompPersonInfo personInfo = uompPersonInfoMapper.get(id);
        String personInfoInstId = personInfo.getInstId();
        String infoUpdating = personInfo.getUpdating();

        //如果流程实例id不为空。且流程实例id不相同就说明是历史数据。
        if (StringUtils.isNotEmpty(instId) && !StringUtils.equals(instId,personInfoInstId)){
            return getHistoryPersonInfo(instId,personInfoDetailDto);
        }
        if (!StringUtils.isEmpty(infoUpdating) && "1".equals(infoUpdating)) {
            personInfoDetailDto = getTempPersonInfo(updating,id);
            if (personInfoDetailDto != null ){
                return personInfoDetailDto;
            }
        }

        personInfoDetailDto = new UompPersonInfoVo();
        BeanUtils.copyProperties(personInfo, personInfoDetailDto);
        String regPermanentResidenceStr = dictUtil.getKeyByName("G_USER_NATIVE_CODE", personInfo.getRegPermanentResidence());
        if (!StringUtils.isEmpty(regPermanentResidenceStr)) {
            personInfoDetailDto.setRegPermanentResidenceName(regPermanentResidenceStr.replace(",", ""));
        }
        /*List<UompDesensitization> desensitizations = desRuleUtil.getDesRule("UOMP_PERSON_INFO", "tel,personCard", personInfo.getInstId(), personInfo.getCreateBy());
        if (desensitizations != null && desensitizations.size() > 0) {
            for (UompDesensitization desensitization : desensitizations) {
                //如果有关于手机号的脱敏规则则转换手机号
                if (!StringUtils.isEmpty(personInfo.getTel()) && "tel".equals(desensitization.getDesFieldCode())) {
                    personInfoDetailDto.setTel(conversionUtil.conversion(desensitization, personInfo.getTel()));
                }
                //如果有关于身份证号的脱敏规则则转换身份证号
                if (!StringUtils.isEmpty(personInfo.getPersonCard()) && "personCard".equals(desensitization.getDesFieldCode())) {
                    personInfoDetailDto.setPersonCard(conversionUtil.conversion(desensitization, personInfo.getPersonCard()));
                }
            }
        }*/
        //查询人员教育信息
        personInfoDetailDto.setEducationList(getPersonEducation(id));
        //查询人员工作信息
        personInfoDetailDto.setJobList(getPersonJobInfo(id));

        personInfoDetailDto.setTechList(getPersonTechInfo(id));

        personInfoDetailDto.setSocialList(getPersonSocial(id));

        personInfoDetailDto.setNoCrimeList(getNoCrime(id));

        personInfoDetailDto.setAbroadList(getPersonAbroad(id));

        personInfoDetailDto.setEntryExitList(getPersonExit(id));
        //培训记录
        //查询该人员信息对应的系统id
        //职业测评
        BaseDTO baseDTO = selectUserId(id);
        List<UompTrainingRecord> records = new ArrayList<>();
        List<VoteUserAnswerDto> answerDtos = new ArrayList<>();
        if (baseDTO != null && !StringUtils.isEmpty(baseDTO.getId())) {
            records = trainingRecordService.selectTrainingRecordByUserId(baseDTO.getId());

            voteInfoService.selectVoteInfoByUserId(baseDTO.getId());
        }
        personInfoDetailDto.setTrainList(records);
        personInfoDetailDto.setAssessmentList(answerDtos);
        // 黑名单
        personInfoDetailDto.setBlackList(selectBlack(id));
//        personInfoDetailDto.setPersonCard(personCardDesensitization(personInfoDetailDto.getPersonCard()));
        return personInfoDetailDto;
    }

    @Override
    @AutoDesensitize(processMethod = "sensitive")
    public List<UompPersonInfo> selectList(QueryFilter queryFilter, String queryType) {
        return uompPersonInfoMapper.query(queryFilter);
    }

    @Override
    @AutoDesensitize(processMethod = "decrypt")
    public List<UompPersonInfo> personExport(QueryFilter queryFilter) {
        return uompPersonInfoMapper.query(queryFilter);
    }

    @Override
    public List<Map<String, String>> importPersons(MultipartFile file) throws Exception{
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        //文件类型判断
        if (null == file.getOriginalFilename()
                || (!file.getOriginalFilename().endsWith(".xls")
                && !file.getOriginalFilename().endsWith(".xlsx"))) {
            throw new BusinessException("文件格式错误！");
        }

        List<Map<String, String>> errorList = new ArrayList<>();
        //身份证和id的map
        Map<String, String> map = new HashMap<>();
        //更新数据
        List<String> updatePerson = new ArrayList<>();
        //证件名称
        Map<String, String> certificateNameList = new HashMap<>();
        //手机号集合
        List<String> tel_list = new ArrayList<>();
        // 入库列表
        List<UompPersonInfo> personList = new ArrayList<>();
        List<UompPersonEducational> educationList = new ArrayList<>();
        List<UompPersonJob> jobList = new ArrayList<>();
        List<UompPersonTechnology> techList = new ArrayList<>();
        List<UompPersonSocial> socialList = new ArrayList<>();
        List<UompPersonNoCrime> noCrimeList = new ArrayList<>();
        List<UompPersonAbroad> abroadList = new ArrayList<>();
        List<UompPersonEntryExit> entryExitList = new ArrayList<>();
        IsSupplierDto supplierDto = supplierUtil.isSupplier();
        List<UompPersonInfoImportDto> uompPersonInfoImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonInfoImportDto.class).sheet(0).headRowNumber(1).doReadSync();
        if (uompPersonInfoImportDtos != null && uompPersonInfoImportDtos.size() > 0) {
            DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            DateFormat formatter1 = new SimpleDateFormat("yyyyMMdd");

            //学历字段查询
            List<BaseDTO> baseDTOList = sysDataDictMapper.selectSubListByUompEducation("UOMP_EDUCATION");
            Map<String, String> educationMap = new HashMap<>();
            for (BaseDTO baseDTO : baseDTOList) {
                educationMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //技术方向 TECHNICAL_DIRECTION
            List<BaseDTO> tecList = sysDataDictMapper.selectSubListByUompEducation("UOMP_TEC_DIRECTION");
            Map<String, String> techMap = new HashMap<>();
            for (BaseDTO baseDTO : tecList) {
                techMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //人员审核状态
            List<BaseDTO> trialStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_PEOPLE_INFO_MANAGEMENT_AUDIT_STATUS");
            Map<String, String> trialStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : trialStatusList) {
                trialStatusMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //查出人员状态的字典项
            List<BaseDTO> personStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_PERSON_STATUS");
            Map<String, String> personStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : personStatusList) {
                personStatusMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //查出背景审核状态的字典项
            List<BaseDTO> backgroundStatusList = sysDataDictMapper.selectSubListByUompEducation("UOMP_BACKGROUND_STATUS");
            Map<String, String> backgroundStatusMap = new HashMap<>();
            for (BaseDTO baseDTO : backgroundStatusList) {
                backgroundStatusMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //民族字典name的集合
            List<BaseDTO> groupList = sysDataDictMapper.selectSubListByUompEducation("G_USER_GROUP");
            Map<String, String> groupMap = new HashMap<>();
            for (BaseDTO baseDTO : groupList) {
                groupMap.put(baseDTO.getName(), baseDTO.getId());
            }
            //查出资质类型的字典项
            List<BaseDTO> contenanceList = sysDataDictMapper.selectSubListByUompEducation("G_USER_POLITIC_CONTENANCE");
            Map<String, String> contenanceMap = new HashMap<>();
            for (BaseDTO baseDTO : contenanceList) {
                contenanceMap.put(baseDTO.getName(), baseDTO.getId());
            }
            List<SupplierManagementDTO> supplierList = supplierManagementService.selectIdAndSupplierName();
            Map<String, String> supplierMap = new HashMap<>();
            for (SupplierManagementDTO baseDTO : supplierList) {
                supplierMap.put(baseDTO.getSupplierName(), baseDTO.getId());
            }
            List<GroupUomp> groupUomps = uompOrgGroupMapper.getAll("","");
            Map<String, String> orgGroupMap = new HashMap<>();
            for (GroupUomp baseDTO : groupUomps) {
                orgGroupMap.put(baseDTO.getName(), baseDTO.getId());
            }
            int i = 1;
            for (UompPersonInfoImportDto dto : uompPersonInfoImportDtos) {
                if (dto == null) {
                    i++;
                    continue;
                }
                UompPersonInfo personInfo = new UompPersonInfo();
                BeanUtils.copyProperties(dto, personInfo);
                boolean flag = true;
                //姓名
                if (StringUtils.isEmpty(personInfo.getPersonName())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第2列", "姓名不能为空");
                    flag = false;
                } else if (personInfo.getPersonName().length() > 50) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第2列", "姓名长度不能超过50");
                    flag = false;
                }
                //出生年月
                Date birthdDate = null;
                //身份证号
                if (StringUtils.isEmpty(personInfo.getPersonCard())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (personInfo.getPersonCard().length() != 18) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第3列", "身份证号长度应为18位");
                        flag = false;
                    } else {
                        //校验该批次中有没有重复的身份证号
                        if (!StringUtils.isEmpty(map.get(personInfo.getPersonCard()))) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第3列", "该导入批次中该身份证号重复，请检查");
                            flag = false;
                        } else {
                            //校验该身份证号在库中存在不存在.
                            //对解析的身份证号进行加密
                            String encryptPersonCard = dataDesensitization.encrypt(personInfo.getPersonCard());
                            QueryFilter queryFilter = new DefaultQueryFilter(true);
                            queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                            queryFilter.addFilter("PERSON_CARD", encryptPersonCard, QueryOP.EQUAL);
                            List<UompPersonInfo> personInfo1 = query(queryFilter);
                            if (personInfo1.size() > 0 && !StringUtils.isEmpty(personInfo1.get(0).getId())) {
                                personInfo.setId(personInfo1.get(0).getId());
                                updatePerson.add(personInfo1.get(0).getId());
                            }
                            //根据校验身份证前6位获取到户籍所在地
                            String subString = personInfo.getPersonCard().substring(0, 6);
                            //户籍key集合
                            List<String> reg_list = dictUtil.getParentKeyByKey("G_USER_NATIVE_CODE", subString);
                            if (reg_list == null || reg_list.size() == 0) {
                                personInfo.setRegPermanentResidence("");
                                personInfo.setRegProvince("");
                                personInfo.setRegCity("");
                                personInfo.setRegRegion("");
                            } else {
                                //拼接出户籍所在地数据
                                personInfo.setRegPermanentResidence(StringUtils.join(reg_list, ","));
                                int regNum = 0;
                                for (String item : reg_list) {
                                    if (regNum == 0) {
                                        personInfo.setRegProvince(item);
                                    }
                                    if (regNum == 1) {
                                        personInfo.setRegCity(item);
                                    }
                                    if (regNum == 2) {
                                        personInfo.setRegRegion(item);
                                    }
                                    regNum++;
                                }
                            }
                            birthdDate = formatter1.parse(personInfo.getPersonCard().substring(6, 14));
                        }
                    }
                }
                //性别
                if (StringUtils.isEmpty(personInfo.getPersonSex())) {

                    errorList = makeErrorList(errorList, "人员基础信息", i, "第4列", "性别不能为空");
                    flag = false;
                } else {
                    if (!"男".equals(personInfo.getPersonSex()) && !"女".equals(personInfo.getPersonSex())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第4列", "请输入正确的性别");
                        flag = false;
                    } else {
                        personInfo.setPersonSex("男".equals(personInfo.getPersonSex()) ? "0" : "1");
                    }
                }
                //出生年月
                if (StringUtils.isEmpty(personInfo.getPersonBirthday())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第5列", "出生年月不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(personInfo.getPersonBirthday());
                        if (date.after(new Date())) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第5列", "出生年月不能在当前日期之后");
                            flag = false;
                        } else if (date.compareTo(birthdDate) != 0) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第5列", "出生年月和身份证号中不一致");
                            flag = false;
                        } else {
                            personInfo.setPersonBirthday(formatter.format(date));
                        }
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第5列", "出生年月未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }
                //国籍
                if (StringUtils.isEmpty(personInfo.getNationality())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第6列", "国籍不能为空");
                    flag = false;
                } else if (personInfo.getNationality().length() > 100) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第6列", "国籍不能超过100位");
                    flag = false;
                }
                //民族
                if (StringUtils.isEmpty(personInfo.getNation())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第7列", "民族不能为空");
                    flag = false;
                } else if (!groupMap.containsKey(personInfo.getNation())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第7列", "民族内容格式有误，请按照系统输入正确民族格式");
                    flag = false;
                }
                //政治面貌
                if (StringUtils.isEmpty(personInfo.getPoliticsStatus())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第8列", "政治面貌不能为空");
                    flag = false;
                    //政治面貌验证是否是字典中的
                } else if (!contenanceMap.containsKey(personInfo.getPoliticsStatus())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第8列", "政治面貌内容格式有误，请按照系统输入正确政治面貌");
                    flag = false;
                }
                //联系电话
                if (StringUtils.isEmpty(personInfo.getTel())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第10列", "联系电话不能为空");
                    flag = false;
                } else {
                    if (personInfo.getTel().length() > 15) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第10列", "联系电话不能超过15位");
                        flag = false;
                    } else {
                        //校验该批次中有没有重复的手机号
                        if (tel_list.contains(personInfo.getTel())) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第10列", "该导入批次中该联系电话重复，请检查");
                            flag = false;
                        } else {
                            QueryFilter queryFilter = new DefaultQueryFilter(true);
                            queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                            queryFilter.addFilter("tel", personInfo.getTel(), QueryOP.EQUAL);
                            List<UompPersonInfo> personInfo1 = query(queryFilter);
                            //校验该联系电话在库中存在不存在
                            if (personInfo1 != null && personInfo1.size() > 0) {
                                if (!personInfo1.get(0).getId().equals(personInfo.getId())) {
                                    errorList = makeErrorList(errorList, "人员基础信息", i, "第10列", "该联系电话在系统中已经存在，不可重复输入");
                                    flag = false;
                                }
                            }
                        }
                    }
                }
                //邮箱

                //居住地址
                if (StringUtils.isEmpty(personInfo.getAddress())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第12列", "居住地址不能为空");
                    flag = false;
                } else if (personInfo.getAddress().length() > 100) {

                    errorList = makeErrorList(errorList, "人员基础信息", i, "第12列", "居住地址不能超过100位");
                    flag = false;

                }
                //学历
                if (StringUtils.isEmpty(personInfo.getEducation())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第13列", "学历不能为空");
                    flag = false;
                } else {
                    boolean keyFlag = true;
                    //如果数据没比对上，则不在字典内，提示错误
                    if (!educationMap.containsKey(personInfo.getEducation())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第13列", "学历错误，不在可选范围内，请检查！");
                        flag = false;
                    } else {
                        personInfo.setEducation(educationMap.get(personInfo.getEducation()));
                    }
                }
                //工作职位
                if (StringUtils.isEmpty(personInfo.getPost())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第14列", "工作职位不能为空");
                    flag = false;
                } else if (personInfo.getPost().length() > 10) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第14列", "工作职位不能超过30位");
                    flag = false;
                }
                //专业

                //就职公司
                if (StringUtils.isEmpty(personInfo.getWorkingCompany())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第16列", "就职公司不能为空");
                    flag = false;
                } else {
                    if (personInfo.getWorkingCompany().length() > 100) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第16列", "就职公司不能超过100位");
                        flag = false;
                    } else {
                        //如果数据没比对上，提示错误
                        if (!supplierMap.containsKey(personInfo.getWorkingCompany())) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第16列", "就职公司，不在系统合作中的服务商内，请检查！");
                            flag = false;
                        } else {
                            personInfo.setWorkingCompanyId(supplierMap.get(personInfo.getWorkingCompany()));
                            //如果对上了，则看是否是服务商权限
                            //如果是服务商管理员权限导入得话校验是不是本服务商
                            if (supplierDto != null && "1".equals(supplierDto.getIfSupplier()))
                                if (!personInfo.getWorkingCompany().equals(supplierDto.getSupplierId())) {
                                    errorList = makeErrorList(errorList, "人员基础信息", i, "第16列", "就职公司只能导入本服务商的名称！");
                                    flag = false;
                                }
                        }
                    }
                }
                //运维组织
                if (StringUtils.isEmpty(personInfo.getOrgGroupName())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第17列", "运维组织不能为空");
                    flag = false;
                } else {
                    if (personInfo.getOrgGroupName().length() > 100) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第17列", "运维组织不能超过100位");
                        flag = false;
                    } else {
                        //如果数据没比对上，提示错误
                        if (!orgGroupMap.containsKey(personInfo.getOrgGroupName())) {
                            errorList = makeErrorList(errorList, "人员基础信息", i, "第17列", "运维组织，不在系统的运维组织内，请检查！");
                            flag = false;
                        } else {
                            personInfo.setOrgGroupId(orgGroupMap.get(personInfo.getOrgGroupName()));
                        }
                    }
                }
                // //技术方向
                if (StringUtils.isEmpty(personInfo.getTechnicalDirection())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第18列", "技术方向不能为空");
                    flag = false;
                } else {
                    //如果数据没比对上，则不在字典内，提示错误
                    if (!techMap.containsKey(personInfo.getTechnicalDirection())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第18列", "技术方向，不在系统可选范围内，请按系统下拉数据中检查！");
                        flag = false;
                    } else {
                        personInfo.setTechnicalDirection(techMap.get(personInfo.getTechnicalDirection()));
                    }
                }

                //入职时间
                if (StringUtils.isEmpty(personInfo.getEntryDate())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第19列", "入职时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(personInfo.getEntryDate());
                        personInfo.setEntryDate(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第19列", "入职时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }
                //审核状态
                if (StringUtils.isEmpty(personInfo.getTrialStatus())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第20列", "审核状态不能为空");
                    flag = false;
                } else {
                    //如果数据没比对上，则不在字典内，提示错误
                    if (!trialStatusMap.containsKey(personInfo.getTrialStatus())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第20列", "审核状态，不在系统可选范围内，请按系统下拉数据中检查！");
                        flag = false;
                    } else {
                        personInfo.setTrialStatus(trialStatusMap.get(personInfo.getTrialStatus()));
                    }
                }
                //人员状态
                if (StringUtils.isEmpty(personInfo.getDimission())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第21列", "人员状态不能为空");
                    flag = false;
                } else {
                    //如果数据没比对上，则不在字典内，提示错误
                    if (!personStatusMap.containsKey(personInfo.getDimission())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第21列", "人员状态，不在系统可选范围内，请按系统下拉数据中检查！");
                        flag = false;
                    } else {
                        personInfo.setDimission(personStatusMap.get(personInfo.getDimission()));
                    }
                }
                //背调审查状态
                if (StringUtils.isEmpty(personInfo.getBackgroundStatus())) {
                    errorList = makeErrorList(errorList, "人员基础信息", i, "第22列", "背调审查状态不能为空");
                    flag = false;
                } else {
                    //如果数据没比对上，则不在字典内，提示错误
                    if (!backgroundStatusMap.containsKey(personInfo.getBackgroundStatus())) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第22列", "背调审查状态，不在系统可选范围内，请按系统下拉数据中检查！");
                        flag = false;
                    } else {
                        personInfo.setBackgroundStatus(backgroundStatusMap.get(personInfo.getBackgroundStatus()));
                    }
                }
                //备注
                if (!StringUtils.isEmpty(personInfo.getRemark())) {
                    if (personInfo.getRemark().length() > 1000) {
                        errorList = makeErrorList(errorList, "人员基础信息", i, "第23列", "备注不能超过1000位");
                        flag = false;
                    }
                } else {
                    personInfo.setRemark("");
                }
                if (flag) {
                    //处理初始化字段
                    if (StringUtils.isEmpty(personInfo.getId())) {
                        String id = IdUtil.getSuid();
                        personInfo.setId(id);
                    }
                    //存储身份证和id的关系
                    map.put(personInfo.getPersonCard(), personInfo.getId());
                    //导入成功的手机号
                    tel_list.add(personInfo.getTel());

                    personList.add(personInfo);
                }
                i++;
            }

            // 获取页签配置信息
            UompPersonConfig personConfig = uompPersonConfigService.getConfigInfo("1");
            if (!StringUtils.isEmpty(personConfig.getConfigInfo())) {
                if (personConfig.getConfigInfo().contains("jybj")) {
                    //人员教育信息
                    educationList = parseEducation(errorList,map,i,file);
                }
                if (personConfig.getConfigInfo().contains("gzbj")) {
                    //工作背景
                    jobList = parseJobs(errorList,map,i,file);
                }
                if (personConfig.getConfigInfo().contains("jszz")) {
                    //技术资质
                    //查出资质类型的字典项
                    techList = parseTechnology(errorList,map,i,file);
                }
                if (personConfig.getConfigInfo().contains("shgx")) {
                    //社会关系
                    socialList = parsePersonSocial(errorList,map,i,file);

                }
                if (personConfig.getConfigInfo().contains("wfzjl")) {
                    //无犯罪证明
                    noCrimeList = parseNoCrime(errorList,map,i,file);

                }
                if (personConfig.getConfigInfo().contains("cgzj")) {
                    //出国证件
                    abroadList = parsePersonAbroad(errorList,map,i,file,certificateNameList);
                }
                if (personConfig.getConfigInfo().contains("crjjl")) {
                    //出入境记录
                    entryExitList = parsePersonExit(errorList,map,i,file,certificateNameList);
                }
            }

            for (UompPersonInfo info : personList) {
                if (updatePerson.contains(info.getId())) {
                    info.setUpdateOrgId(user.getOrgId());
                    info.setUpdateTime(new Date());
                    info.setUpdateBy(user.getUserId());
                    if (StringUtils.isNotEmpty(info.getPersonCard()) && info.getPersonCard().length() ==18
                            && !info.getPersonCard().contains("*")){
                        info.setPersonCard(dataDesensitization.encrypt(info.getPersonCard()));
                    }
                    updateByPrimaryKeySelective(info);
                } else {
                    info.setBlacklist("0");
                    info.setFileInfo("[]");
//                    info.setBackgroundStatus("0");
                    info.setIsAccount("0");
                    info.setDelFlag("0");
                    info.setCreateOrgId(user.getOrgId());
                    info.setUpdating("0");
                    if (StringUtils.isNotEmpty(info.getPersonCard()) && info.getPersonCard().length() ==18
                            && !info.getPersonCard().contains("*")){
                        info.setPersonCard(dataDesensitization.encrypt(info.getPersonCard()));
                    }
                    insertSelective(info);
                }
            }
            for (UompPersonEducational educational : educationList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("EDUCATION_BEGIN_TIME", educational.getEducationBeginTime(), QueryOP.EQUAL);
                queryFilter.addFilter("EDUCATION_END_TIME", educational.getEducationEndTime(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", educational.getPersonId(), QueryOP.EQUAL);
                List<UompPersonEducational> personInfos = uompPersonEducationalService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonEducational uompPersonEducational : personInfos) {
                        uompPersonEducationalService.remove(uompPersonEducational.getId());
                    }
                }
                educational.setDelFlag("0");
                educational.setFileInfo("[]");
                educational.setCreateOrgId(user.getOrgId());
                uompPersonEducationalService.create(educational);
            }
            for (UompPersonJob job : jobList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("JOB_BEGIN_TIME", job.getJobBeginTime(), QueryOP.EQUAL);
                queryFilter.addFilter("JOB_END_TIME", job.getJobEndTime(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", job.getPersonId(), QueryOP.EQUAL);
                List<UompPersonJob> personInfos = uompPersonJobService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonJob uompPersonEducational : personInfos) {
                        uompPersonJobService.remove(uompPersonEducational.getId());
                    }
                }
                job.setDelFlag("0");
                job.setFileInfo("[]");
                job.setCreateOrgId(user.getOrgId());
                uompPersonJobService.create(job);
            }
            for (UompPersonTechnology tech : techList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("GET_TIME", tech.getGetTime(), QueryOP.EQUAL);
                queryFilter.addFilter("QUALIFTY_NAME", tech.getQualiftyName(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", tech.getPersonId(), QueryOP.EQUAL);
                List<UompPersonTechnology> personInfos = uompPersonTechnologyService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonTechnology uompPersonEducational : personInfos) {
                        uompPersonTechnologyService.remove(uompPersonEducational.getId());
                    }
                }
                tech.setDelFlag("0");
                tech.setFileInfo("[]");
                tech.setCreateOrgId(user.getOrgId());
                uompPersonTechnologyService.create(tech);
            }
            for (UompPersonSocial social : socialList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("RELA_NAME", social.getRelaName(), QueryOP.EQUAL);
                queryFilter.addFilter("RELATION_WITH_MYSELF", social.getRelationWithMyself(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", social.getPersonId(), QueryOP.EQUAL);
                List<UompPersonSocial> personInfos = uompPersonSocialService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonSocial uompPersonEducational : personInfos) {
                        uompPersonSocialService.remove(uompPersonEducational.getId());
                    }
                }
                social.setDelFlag("0");
                social.setCreateOrgId(user.getOrgId());
                uompPersonSocialService.create(social);
            }
            for (UompPersonNoCrime noCrime : noCrimeList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("PROOF_NUMBER", noCrime.getProofNumber(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", noCrime.getPersonId(), QueryOP.EQUAL);
                List<UompPersonNoCrime> personInfos = uompPersonNoCrimeService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonNoCrime uompPersonEducational : personInfos) {
                        uompPersonNoCrimeService.remove(uompPersonEducational.getId());
                    }
                }
                noCrime.setDelFlag("0");
                noCrime.setFileInfo("[]");
                noCrime.setCreateOrgId(user.getOrgId());
                uompPersonNoCrimeService.create(noCrime);
            }
            for (UompPersonAbroad abroad : abroadList) {
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("CERTIFICATE_NUM", abroad.getCertificateNum(), QueryOP.EQUAL);
                queryFilter.addFilter("PERSON_ID", abroad.getPersonId(), QueryOP.EQUAL);
                List<UompPersonAbroad> personInfos = uompPersonAbroadService.query(queryFilter);
                if (personInfos != null && personInfos.size() > 0) {
                    for (UompPersonAbroad uompPersonEducational : personInfos) {
                        uompPersonAbroadService.remove(uompPersonEducational.getId());
                    }
                }
                abroad.setDelFlag("0");
                abroad.setFileInfo("[]");
                abroad.setCreateOrgId(user.getOrgId());
                uompPersonAbroadService.create(abroad);
            }
            for (UompPersonEntryExit entryExit : entryExitList) {
                entryExit.setDelFlag("0");
                entryExit.setCreateOrgId(user.getOrgId());
                uompPersonEntryExitService.create(entryExit);
            }
        } else {
            HashMap<String, String> errorMap = new HashMap<>();
            errorMap.put("sheet", "人员基础信息");
            errorMap.put("message", "必填项不能为空");
            errorList.add(errorMap);
        }
        return errorList;
    }

    @Override
    public void batchEncrypt(String ids) {
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        if(StringUtils.isNotEmpty(ids)){
            queryFilter.addFilter("id",ids,QueryOP.IN);
        }
        List<UompPersonInfo> uompPersonInfos = query(queryFilter);
        for(UompPersonInfo uompPersonInfo : uompPersonInfos){
            String personCard = uompPersonInfo.getPersonCard();
            if (StringUtils.isNotEmpty(personCard) && personCard.length() == 18){
                personCard = dataDesensitization.encrypt(personCard);
                uompPersonInfoMapper.updatePersonCardById(uompPersonInfo.getId(),personCard);
            }
        }
        QueryFilter queryFilter1 = new DefaultQueryFilter(true);
        if(StringUtils.isNotEmpty(ids)){
            queryFilter.addFilter("PERSON_ID",ids,QueryOP.IN);
        }
        //更新uomp_adminssion_person
        uomAdmissionPersonService.personCardEncrypt(queryFilter);

    }

    @Override
    public String decryptString(String id, String attr) {
        if (StringUtils.isEmpty(attr)){
            attr = "personCard";
        }
        if (StringUtils.isNotEmpty(id)){
            UompPersonInfo uompPersonInfo = get(id);
            if (uompPersonInfo != null){
                String original = ReflectUtil.getFieldValue(uompPersonInfo,attr).toString();
                return dataDesensitization.decrypt(original);
            }
        }
        return null;
    }





    /**
     * 查询人员教育信息
     * @param id
     */
    private List<UompPersonEducational> getPersonEducation(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonEducationalService.query(queryFilter);
    }

    /**
     * 查询人员工作情况
     * @param id
     * @return
     */
    private List<UompPersonJob> getPersonJobInfo(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonJobService.query(queryFilter);
    }

    private List<UompPersonTechnology> getPersonTechInfo(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonTechnologyService.query(queryFilter);
    }

    private List<UompPersonSocial> getPersonSocial(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonSocialService.query(queryFilter);
    }

    private List<UompPersonNoCrime> getNoCrime(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonNoCrimeService.query(queryFilter);
    }

    private List<UompPersonAbroad> getPersonAbroad(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonAbroadService.query(queryFilter);
    }

    private List<UompPersonEntryExit> getPersonExit(String id){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_ID", id, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFieldSort("CREATE_TIME", "DESC");
        return uompPersonEntryExitService.query(queryFilter);
    }

    /**
     * 解密身份证
     * @param personCard
     * @return
     */
    private String decryptPersonCard(String personCard){
        if (StringUtils.isNotEmpty(personCard) && personCard.length() == 18){
            return dataDesensitization.decrypt(personCard);
        }
        return null;
    }

    /**
     * 补充人员信息
     * @param personInfo
     * @param orgId
     */
    private void complementPersonInfo(UompPersonInfoVo personInfo,String orgId){
        personInfo.setBlacklist(("0"));
        personInfo.setTrialStatus(("0"));
        personInfo.setBackgroundStatus(("0"));
        personInfo.setIsAccount(("0"));
        personInfo.setCreateOrgId(orgId);
        personInfo.setDelFlag("0");
        personInfo.setUpdating("0");

    }

    /**
     * 身份证号去重校验
     * @param newIdCard
     *
     */
    private void IdCardValidate(String newIdCard){
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("PERSON_CARD", newIdCard, QueryOP.EQUAL);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        List<UompPersonInfo> personInfos = query(queryFilter);
        if (personInfos != null && personInfos.size() > 0) {
            throw new BusinessException("该身份证号已经存在系统中，不可重复，请仔细检查！");
        }

    }

    /**
     * 电话号码去重校验
     * @param tel
     */
    private void telValidate(String tel){

        QueryFilter queryFilter1 = new DefaultQueryFilter(true);
        queryFilter1.addFilter("TEL", tel, QueryOP.EQUAL);
        queryFilter1.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        List<UompPersonInfo> personInfos = query(queryFilter1);
        if (personInfos != null && personInfos.size() > 0) {
            throw new BusinessException("该联系电话已经存在系统中，不可重复，请仔细检查！");
        }

    }

    private UompPersonAllInfoTemp setUompPersonAllInfoTemp(UompPersonInfoVo personInfo){
        UompPersonAllInfoTemp infoTemp = new UompPersonAllInfoTemp();
        infoTemp.setPersonId(personInfo.getId());
        infoTemp.setPersonInfo(JSONObject.toJSONString(personInfo));
        infoTemp.setEducationalInfo(JSONObject.toJSONString(personInfo.getEducationList()));
        infoTemp.setJobInfo(JSONObject.toJSONString(personInfo.getJobList()));
        infoTemp.setTechInfo(JSONObject.toJSONString(personInfo.getTechList()));
        infoTemp.setSocialInfo(JSONObject.toJSONString(personInfo.getSocialList()));
        infoTemp.setAbroadInfo(JSONObject.toJSONString(personInfo.getAbroadList()));
        infoTemp.setEnrtyExitInfo(JSONObject.toJSONString(personInfo.getEntryExitList()));
        return infoTemp;
    }



    /**
     * 判断身份证号是否需要解密
     * @param personCard
     * @return
     */
    private boolean personCardIsDecrypt(String personCard){
        if (StringUtils.isNotEmpty(personCard) && !personCard.contains("*") && personCard.length() > 18){
            return true;
        }else {
            return  false;
        }
    }

    /**
     * 身份证号脱敏
     * @param personCard
     * @return
     */
    private String personCardDesensitization(String personCard){
        if (personCardIsDecrypt(personCard)){
            return dataDesensitization.desensitization(personCard,3,3);
        }else {
            return personCard;
        }
    }

    /**
     * 查询人员临时数据
     * @param updating
     * @param id
     * @return
     */
    private UompPersonInfoVo getTempPersonInfo(String updating,String id){
            UompPersonAllInfoTemp infoTemp = uompPersonAllInfoTempService.getByPeronId(id);
            // 临时数据
            if (infoTemp != null && !StringUtils.isEmpty(infoTemp.getPersonInfo())) {
                return JSONObject.parseObject(infoTemp.getPersonInfo(), UompPersonInfoVo.class);

            }
        return null;
    }

    /**
     * 查询历史人员信息
     * @param instId
     * @param personInfoDetailDto
     * @return
     */
    private UompPersonInfoVo getHistoryPersonInfo(String instId,UompPersonInfoVo personInfoDetailDto){
        UompPersonAllInfoHistory personAllInfoHistory = uompPersonAllInfoHistoryMapper.getByInstId(instId);
        personInfoDetailDto = JSONObject.parseObject(personAllInfoHistory.getPersonInfo(), UompPersonInfoVo.class);
        return personInfoDetailDto;
    }

    private List<Map<String, String>> makeErrorList(List<Map<String, String>> errorList, String sheet,
                                                    int row, String column, String message) {
        Map<String, String> errorMap = new HashMap<>();
        errorMap.put("sheet", sheet);
        errorMap.put("row", "第" + (row + 1) + "行");
        errorMap.put("column", column);
        errorMap.put("message", message);
        errorList.add(errorMap);
        return errorList;
    }

    /**
     * 解析人员教育信息
     * @param errorList
     * @param map
     * @param i
     */
    private List<UompPersonEducational> parseEducation(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                                                       MultipartFile file) throws Exception{
        List<UompPersonEducational> educationList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        //统招/非统招
        List<BaseDTO> educationForm = sysDataDictMapper.selectSubListByUompEducation("UOMP_EDUCATION_FORM");
        Map<String, String> educationFromMap = new HashMap<>();
        for (BaseDTO baseDTO : educationForm) {
            educationFromMap.put(baseDTO.getName(), baseDTO.getId());
        }
        List<UompPersonEducationalImportDto> educationals = EasyExcel.read(file.getInputStream()).head(UompPersonEducationalImportDto.class).sheet(1).headRowNumber(1).doReadSync();
        if (educationals != null && educationals.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonEducationalImportDto importDto : educationals) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonEducational educational = new UompPersonEducational();
                BeanUtils.copyProperties(importDto, educational);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }

                boolean timeFlag = true;
                Date start = null;
                //起始时间
                if (StringUtils.isEmpty(importDto.getEducationBeginTime())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第4列", "起始时间不能为空");
                    flag = false;
                    timeFlag = false;
                } else {
                    try {
                        start = formatter.parse(importDto.getEducationBeginTime());
                        educational.setEducationBeginTime(formatter.format(start));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第4列", "起始时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                        timeFlag = false;
                    }
                }
                //终止时间
                Date end = null;
                if (StringUtils.isEmpty(importDto.getEducationEndTime())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第5列", "终止时间不能为空");
                    flag = false;
                    timeFlag = false;
                } else {
                    try {
                        end = formatter.parse(importDto.getEducationEndTime());
                        educational.setEducationEndTime(formatter.format(end));
                    } catch (ParseException e) {

                        errorList = makeErrorList(errorList, "教育背景", i, "第5列", "终止时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                        timeFlag = false;
                    }
                }
                //起止时间大小校验(如果时间格式无误进行校验)
                if (timeFlag) {
                    if (start.after(end)) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第4,5列", "起始时间不能大于终止时间");
                        flag = false;
                    }
                }


                //就读院校
                if (StringUtils.isEmpty(importDto.getSchool())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第6列", "就读院校不能为空");
                    flag = false;
                } else {
                    if (importDto.getSchool().length() > 40) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第6列", "就读院校不能超过40位");
                        flag = false;
                    }
                }
                //就读专业
                if (StringUtils.isEmpty(importDto.getMajor())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第7列", "就读专业不能为空");
                    flag = false;
                } else {
                    if (importDto.getMajor().length() > 40) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第7列", "就读专业不能超过40位");
                        flag = false;
                    }
                }
                //学历
                if (StringUtils.isEmpty(importDto.getEducationBackground())) {
                    errorList = makeErrorList(errorList, "教育背景", i, "第8列", "学历不能为空");
                    flag = false;
                } else {
                    if (importDto.getEducationBackground().length() > 40) {
                        errorList = makeErrorList(errorList, "教育背景", i, "第8列", "学历不能超过40位");
                        flag = false;
                    }
                }
                //是否统招
                if (StringUtils.isEmpty(importDto.getEducationForm())) {
                    educational.setEducationForm("");
                } else {
                    if (educationFromMap.containsKey(importDto.getEducationForm())) {
                        educational.setEducationForm(educationFromMap.get(importDto.getEducationForm()));
                    } else { //如果数据没比对上，则不在字典内，提示错误
                        errorList = makeErrorList(errorList, "教育背景", i, "第9列", "请选择统招或者非统招");
                        flag = false;
                    }
                }
                //证书编号
                if (StringUtils.isEmpty(importDto.getCertificateNum())) {
                    educational.setCertificateNum("");
                }
                if (flag) {
                    educational.setPersonId(map.get(importDto.getPersonCard()));
                    educationList.add(educational);
                }
                i++;
            }
        }
        return educationList;
    }

    /**
     * 解析人员工作信息
     * @param errorList
     * @param map
     * @param i
     * @param file
     * @return
     * @throws Exception
     */
    private List<UompPersonJob> parseJobs(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                           MultipartFile file) throws Exception{
        List<UompPersonJob> jobList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<UompPersonJobImportDto> jobs = EasyExcel.read(file.getInputStream()).head(UompPersonJobImportDto.class).sheet(2).headRowNumber(1).doReadSync();
        if (jobs != null && jobs.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonJobImportDto importDto : jobs) {
                boolean flag = true;
                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonJob job = new UompPersonJob();
                BeanUtils.copyProperties(importDto, job);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "工作背景", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //起始时间
                boolean timeFlag = true;
                Date start = null;
                if (StringUtils.isEmpty(importDto.getJobBeginTime())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第4列", "起始时间不能为空");
                    flag = false;
                    timeFlag = false;
                } else {
                    try {
                        start = formatter.parse(importDto.getJobBeginTime());
                        job.setJobBeginTime(formatter.format(start));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "工作背景", i, "第4列", "起始时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                        timeFlag = false;
                    }

                }

                //终止时间
                Date end = null;
                if (StringUtils.isEmpty(importDto.getJobEndTime())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第5列", "终止时间不能为空");
                    flag = false;
                    timeFlag = false;
                } else {
                    //非 至今 都要校验时间格式
                    if (!"至今".equals(importDto.getJobEndTime())) {
                        try {
                            end = formatter.parse(importDto.getJobEndTime());
                            job.setJobEndTime(formatter.format(end));
                        } catch (ParseException e) {
                            errorList = makeErrorList(errorList, "工作背景", i, "第5列", "终止时间未按yyyy-MM-dd文本格式填写");
                            flag = false;
                            timeFlag = false;
                        }

                        //非至今的校验起始，终止日期大小
                        //起止时间大小校验(如果时间格式无误进行校验)
                        if (timeFlag) {
                            if (start.after(end)) {
                                errorList = makeErrorList(errorList, "工作背景", i, "第4,5列", "起始时间不能大于终止时间");
                                flag = false;
                            }
                        }
                    }
                }
                //就职公司
                if (StringUtils.isEmpty(importDto.getCompanyName())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第6列", "就职公司不能为空");
                    flag = false;
                } else {
                    if (importDto.getCompanyName().length() > 40) {
                        errorList = makeErrorList(errorList, "工作背景", i, "第6列", "就职公司不能超过40位");
                        flag = false;
                    }
                }
                //工作职位
                if (StringUtils.isEmpty(importDto.getJobPosition())) {
                    errorList = makeErrorList(errorList, "工作背景", i, "第7列", "工作职位不能为空");
                    flag = false;
                } else {
                    if (importDto.getJobPosition().length() > 40) {
                        errorList = makeErrorList(errorList, "工作背景", i, "第7列", "工作职位不能超过40位");
                        flag = false;
                    }
                }
                //工作描述
                if (StringUtils.isEmpty(importDto.getJobDescribe())) {

                    errorList = makeErrorList(errorList, "工作背景", i, "第8列", "工作描述不能为空");
                    flag = false;
                } else {
                    if (importDto.getJobDescribe().length() > 300) {
                        errorList = makeErrorList(errorList, "工作背景", i, "第8列", "工作描述不能超过300位");
                        flag = false;
                    }
                }

                if (flag) {
                    job.setPersonId(map.get(importDto.getPersonCard()));
                    jobList.add(job);
                }
                i++;
            }
        }
        return  jobList;
    }

    /**
     * 解析人员技能信息
     * @param errorList
     * @param map
     * @param i
     * @param file
     * @throws Exception
     */
    private List<UompPersonTechnology> parseTechnology(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                                 MultipartFile file) throws Exception{
        List<UompPersonTechnology> techList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<BaseDTO> qualiftyType = sysDataDictMapper.selectSubListByUompEducation("UOMP_TECHNOLOGY_QUALIFTY_TYPE");
        Map<String, String> qualiftyTypeMap = new HashMap<>();
        for (BaseDTO baseDTO : qualiftyType) {
            qualiftyTypeMap.put(baseDTO.getName(), baseDTO.getId());
        }
        List<UompPersonTechnologyImportDto> technologyImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonTechnologyImportDto.class).sheet(3).headRowNumber(1).doReadSync();
        if (technologyImportDtos != null && technologyImportDtos.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonTechnologyImportDto importDto : technologyImportDtos) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonTechnology technology = new UompPersonTechnology();
                BeanUtils.copyProperties(importDto, technology);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //获取时间
                if (StringUtils.isEmpty(importDto.getGetTime())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第4列", "获取时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getGetTime());
                        technology.setGetTime(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第4列", "获取时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }
                //资质名称
                if (StringUtils.isEmpty(importDto.getQualiftyName())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第5列", "资质名称不能为空");
                    flag = false;
                } else {
                    if (importDto.getQualiftyName().length() > 20) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第5列", "资质名称不能超过20位");
                        flag = false;
                    }
                }
                //资质类型
                if (StringUtils.isEmpty(importDto.getQualiftyType())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第6列", "资质类型不能为空");
                    flag = false;
                } else {
                    if (qualiftyTypeMap.containsKey(importDto.getQualiftyType())) {
                        technology.setQualiftyType(qualiftyTypeMap.get(importDto.getQualiftyType()));
                    } else {//如果数据没比对上，则不在字典内，提示错误
                        errorList = makeErrorList(errorList, "技术资质", i, "第6列", "资质类型错误，不在可选范围内，请检查");
                        flag = false;
                    }
                }
                //颁证机构
                if (StringUtils.isEmpty(importDto.getCertificationBody())) {
                    errorList = makeErrorList(errorList, "技术资质", i, "第7列", "颁证机构不能为空");
                    flag = false;
                } else {
                    if (importDto.getCertificationBody().length() > 40) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第7列", "颁证机构不能超过40位");
                        flag = false;
                    }
                }

                Date start = null;
                //起始时间
                if (StringUtils.isEmpty(importDto.getStartTime())) {
                    technology.setStartTime("");
                } else {
                    try {
                        start = formatter.parse(importDto.getStartTime());
                        technology.setStartTime(formatter.format(start));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第8列", "证书有效起始时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }
                //终止时间
                Date end = null;
                if (StringUtils.isEmpty(importDto.getEndTime())) {
                    if (!StringUtils.isEmpty(technology.getStartTime())) {
                        errorList = makeErrorList(errorList, "技术资质", i, "第9列", "因证书有效起始时间存在，所以证书有效终止时间不可为空");
                        flag = false;
                    } else {
                        technology.setEndTime("");
                    }
                } else {
                    if (!"永久有效".equals(importDto.getEndTime())) {
                        try {
                            end = formatter.parse(importDto.getEndTime());
                            technology.setEndTime(formatter.format(end));
                        } catch (ParseException e) {
                            errorList = makeErrorList(errorList, "技术资质", i, "第9列", "证书有效终止时间未按yyyy-MM-dd文本格式填写，或可填写为“永久有效”");
                            flag = false;
                        }
                    }
                }

                if (flag) {
                    technology.setPersonId(map.get(importDto.getPersonCard()));
                    techList.add(technology);
                }
                i++;
            }
        }
        return  techList;
    }

    private  List<UompPersonSocial> parsePersonSocial(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                                   MultipartFile file) throws Exception{
        List<UompPersonSocial> socialList = new ArrayList<>();
        List<UompPersonSocialImportDto> socialImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonSocialImportDto.class).sheet(4).headRowNumber(1).doReadSync();
        if (socialImportDtos != null && socialImportDtos.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonSocialImportDto importDto : socialImportDtos) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonSocial social = new UompPersonSocial();
                BeanUtils.copyProperties(importDto, social);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //关系人姓名
                if (StringUtils.isEmpty(importDto.getRelaName())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第4列", "关系人姓名不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelaName().length() > 60) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第4列", "关系人姓名不能超过60位");
                        flag = false;
                    }
                }

                //年龄
                if (StringUtils.isEmpty(importDto.getRelaAge())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第5列", "年龄不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelaAge().length() > 3) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第5列", "年龄不能超过3位");
                        flag = false;
                    }
                }

                //国籍
                if (StringUtils.isEmpty(importDto.getNational())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第6列", "国籍不能为空");
                    flag = false;
                } else {
                    if (importDto.getNational().length() > 30) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第6列", "国籍不能超过30位");
                        flag = false;
                    }
                }

                //政治面貌
                if (StringUtils.isEmpty(importDto.getPoliticsStatus())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第7列", "政治面貌不能为空");
                    flag = false;
                } else {
                    if (importDto.getPoliticsStatus().length() > 10) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第7列", "政治面貌不能超过10位");
                        flag = false;
                    }
                }

                //与本人关系
                if (StringUtils.isEmpty(importDto.getRelationWithMyself())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第8列", "与本人关系不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelationWithMyself().length() > 10) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第8列", "与本人关系不能超过10位");
                        flag = false;
                    }
                }

                //联系方式
                if (StringUtils.isEmpty(importDto.getRelaTel())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第9列", "联系方式不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelaTel().length() > 15) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第9列", "联系方式不能超过15位");
                        flag = false;
                    }
                }

                //居住地
                if (StringUtils.isEmpty(importDto.getRelaAddress())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第10列", "居住地不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelaAddress().length() > 100) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第10列", "居住地不能超过100位");
                        flag = false;
                    }
                }

                //工作单位及职务
                if (StringUtils.isEmpty(importDto.getRelaPost())) {
                    errorList = makeErrorList(errorList, "社会关系", i, "第11列", "工作单位及职务不能为空");
                    flag = false;
                } else {
                    if (importDto.getRelaPost().length() > 100) {
                        errorList = makeErrorList(errorList, "社会关系", i, "第11列", "工作单位及职务不能超过100位");
                        flag = false;
                    }
                }


                if (flag) {
                    social.setPersonId(map.get(importDto.getPersonCard()));
                    socialList.add(social);
                }
                i++;
            }
        }
        return socialList;
    }

    private List<UompPersonNoCrime> parseNoCrime(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                               MultipartFile file) throws Exception{
        List<UompPersonNoCrime> noCrimeList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<UompPersonNoCrimeImportDto> noCrimeImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonNoCrimeImportDto.class).sheet(5).headRowNumber(1).doReadSync();
        if (noCrimeImportDtos != null && noCrimeImportDtos.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonNoCrimeImportDto importDto : noCrimeImportDtos) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonNoCrime noCrime = new UompPersonNoCrime();
                BeanUtils.copyProperties(importDto, noCrime);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //证明编号
                if (StringUtils.isEmpty(importDto.getProofNumber())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第4列", "证明编号不能为空");
                    flag = false;
                } else {
                    if (importDto.getProofNumber().length() > 60) {
                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第4列", "证明编号不能超过60位");
                        flag = false;
                    }
                }

                //查询起始时间
                if (StringUtils.isEmpty(importDto.getQueryBeginTime())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第5列", "查询起始时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getQueryBeginTime());
                        noCrime.setQueryBeginTime(formatter.format(date));
                    } catch (ParseException e) {

                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第5列", "查询起始时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }

                //查询终止时间
                if (StringUtils.isEmpty(importDto.getQueryEndTime())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第6列", "查询终止时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getQueryEndTime());
                        noCrime.setQueryEndTime(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第6列", "查询终止时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }

                //出具单位
                if (StringUtils.isEmpty(importDto.getProvideUnit())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第7列", "出具单位不能为空");
                    flag = false;
                } else {
                    if (importDto.getProvideUnit().length() > 40) {
                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第7列", "出具单位不能超过40位");
                        flag = false;
                    }
                }
                //有效期
                if (StringUtils.isEmpty(importDto.getIndate())) {
                    errorList = makeErrorList(errorList, "无犯罪证明", i, "第8列", "有效期不能为空");
                    flag = false;
                } else {
                    if (importDto.getIndate().length() > 10) {
                        errorList = makeErrorList(errorList, "无犯罪证明", i, "第8列", "有效期不能超过10位");
                        flag = false;
                    }
                }
                if (flag) {
                    noCrime.setPersonId(map.get(importDto.getPersonCard()));
                    noCrimeList.add(noCrime);
                }
                i++;
            }
        }
        return  noCrimeList;

    }

    private List<UompPersonAbroad> parsePersonAbroad(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                                   MultipartFile file,Map<String, String> certificateNameList) throws Exception{
        List<UompPersonAbroad> abroadList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<UompPersonAbroadImportDto> abroadImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonAbroadImportDto.class).sheet(6).headRowNumber(1).doReadSync();
        if (abroadImportDtos != null && abroadImportDtos.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonAbroadImportDto importDto : abroadImportDtos) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonAbroad abroad = new UompPersonAbroad();
                BeanUtils.copyProperties(importDto, abroad);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "出国(境)证件", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //证件名称
                if (StringUtils.isEmpty(importDto.getCertificateName())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第4列", "证件名称不能为空");
                    flag = false;
                } else {
                    if (importDto.getCertificateName().length() > 100) {
                        errorList = makeErrorList(errorList, "出国(境)证件", i, "第4列", "证件名称不能超过100位");
                        flag = false;
                    }
                }

                //证件号码
                if (StringUtils.isEmpty(importDto.getCertificateNum())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第5列", "证件号码不能为空");
                    flag = false;
                }

                //签发地
                if (StringUtils.isEmpty(importDto.getIssueAt())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第6列", "签发地不能为空");
                    flag = false;
                } else {
                    if (importDto.getIssueAt().length() > 100) {
                        errorList = makeErrorList(errorList, "出国(境)证件", i, "第6列", "签发地不能超过100位");
                        flag = false;
                    }
                }

                //起始时间
                if (StringUtils.isEmpty(importDto.getStartTime())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第7列", "证件有效期起始时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getStartTime());
                        abroad.setStartTime(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "出国(境)证件", i, "第7列", "证件有效期起始时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }

                }

                //终止时间
                if (StringUtils.isEmpty(importDto.getEndTime())) {
                    errorList = makeErrorList(errorList, "出国(境)证件", i, "第8列", "证件有效期终止时间不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getEndTime());
                        abroad.setEndTime(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "出国(境)证件", i, "第8列", "证件有效期终止时间未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }


                if (flag) {
                    abroad.setPersonId(map.get(importDto.getPersonCard()));
                    abroadList.add(abroad);
                    certificateNameList.put(abroad.getCertificateNum(), abroad.getCertificateName());
                }
                i++;
            }
        }
        return abroadList;
    }
    private List<UompPersonEntryExit> parsePersonExit(List<Map<String, String>> errorList,Map<String,String> map ,int i,
                                  MultipartFile file,Map<String, String> certificateNameList) throws Exception{
        List<UompPersonEntryExit> entryExitList = new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        List<UompPersonEntryExitImportDto> entryExitImportDtos = EasyExcel.read(file.getInputStream()).head(UompPersonEntryExitImportDto.class).sheet(7).headRowNumber(1).doReadSync();
        if (entryExitImportDtos != null && entryExitImportDtos.size() > 0) {
            //构建教育背景页签
            i = 1;
            for (UompPersonEntryExitImportDto importDto : entryExitImportDtos) {
                boolean flag = true;

                if (importDto == null) {
                    i++;
                    continue;
                }
                UompPersonEntryExit entryExit = new UompPersonEntryExit();
                BeanUtils.copyProperties(importDto, entryExit);

                //姓名
                if (StringUtils.isEmpty(importDto.getPersonName())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第2列", "姓名不能为空");
                    flag = false;
                }

                //身份证号
                if (StringUtils.isEmpty(importDto.getPersonCard())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第3列", "身份证号不能为空");
                    flag = false;
                } else {
                    if (map.get(importDto.getPersonCard()) == null) {
                        errorList = makeErrorList(errorList, "出入境记录", i, "第3列", "身份证号关联不到人员基础信息页正确的身份证号，请检查是否一致，或身份证号是否有误");
                        flag = false;
                    }
                }
                //出境/入境
                if (StringUtils.isEmpty(importDto.getEntryExit())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第4列", "出境/入境不能为空");
                    flag = false;
                }

                //出入境日期
                if (StringUtils.isEmpty(importDto.getEntryExitTime())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第5列", "出入境日期不能为空");
                    flag = false;
                } else {
                    try {
                        Date date = formatter.parse(importDto.getEntryExitTime());
                        entryExit.setEntryExitTime(formatter.format(date));
                    } catch (ParseException e) {
                        errorList = makeErrorList(errorList, "出入境记录", i, "第5列", "出入境日期未按yyyy-MM-dd文本格式填写");
                        flag = false;
                    }
                }
                //证件号码
                if (StringUtils.isEmpty(importDto.getCertificateNum())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第7列", "证件号码不能为空");
                    flag = false;
                } else if (!certificateNameList.containsKey(importDto.getCertificateNum())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第7列", "证件号码在出国证件中不存在");
                    flag = false;
                }
                //证件名称
                if (StringUtils.isEmpty(importDto.getCertificateName())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第6列", "证件名称不能为空");
                    flag = false;
                } else {
                    if (!certificateNameList.containsValue(importDto.getCertificateName())) {
                        errorList = makeErrorList(errorList, "出入境记录", i, "第6列", "证件名称在出国证件中不存在");
                        flag = false;
                    } else if (importDto.getCertificateName().length() > 100) {
                        errorList = makeErrorList(errorList, "出入境记录", i, "第6列", "证件名称不能超过100位");
                        flag = false;
                    } else {
                        entryExit.setCertificateName(importDto.getCertificateNum());
                    }
                }
                //出入境口岸
                if (StringUtils.isEmpty(importDto.getEntryExitPorts())) {
                    errorList = makeErrorList(errorList, "出入境记录", i, "第8列", "出入境口岸不能为空");
                    flag = false;
                } else {
                    if (importDto.getEntryExitPorts().length() > 100) {
                        errorList = makeErrorList(errorList, "出入境记录", i, "第8列", "出入境口岸不能超过100位");
                        flag = false;
                    }
                }

                if (flag) {
                    entryExit.setPersonId(map.get(importDto.getPersonCard()));
                    entryExitList.add(entryExit);
                }
                i++;
            }
        }
        return entryExitList;
    }

    private void validatePassword(String newPassword){
        String decentralizationEnable = iSysPropertiesPlatFormService.getByAlias("security.pwd.weakDisable");
        if (StringUtils.isNotEmpty(decentralizationEnable) && "true".equals(decentralizationEnable)) {
            if (StringUtils.isNotEmpty(newPassword) && newPassword.length() < 6) {
                throw new BusinessMessage("密码位数低于6位为弱密码，禁止保存");
            }
        }
        String lengthMin = iSysPropertiesPlatFormService.getByAlias("security.pwd.lengthMin");
        if (StringUtils.isNotEmpty(lengthMin)) {
            if (newPassword.length() < Integer.parseInt(lengthMin)) {
                throw new BusinessMessage("密码位数不能低于" + lengthMin + "位");
            }
        }
        String complex = iSysPropertiesPlatFormService.getByAlias("security.pwd.complex");
        if (StringUtils.isNotEmpty(complex)) {
            JSONObject obj = JSON.parseObject(complex);
            complex = obj.getString("complex");
            if ("general".equals(complex)) {
                Pattern r = Pattern.compile("^[a-zA-Z0-9]*$");
                Matcher m = r.matcher(newPassword);
                if (!m.find()) {
                    throw new BusinessMessage("密码格式为字母大小写、数字组合");
                }
            } else if ("complex".equals(complex)) {
                Pattern r = Pattern.compile("[a-zA-Z]+");
                Matcher m = r.matcher(newPassword);
                boolean ifError = !m.find();
                if (!ifError) {
                    r = Pattern.compile("[0-9]+");
                    m = r.matcher(newPassword);
                    if (!m.find()) {
                        ifError = true;
                    }
                }
                if (!ifError) {
                    r = Pattern.compile("[~!@#$%^&*()”’,.]+");
                    m = r.matcher(newPassword);
                    if (!m.find()) {
                        ifError = true;
                    }
                }
                if (!ifError) {
                    r = Pattern.compile("^[a-zA-Z0-9~!@#$%^&*()”’,.]*$");
                    m = r.matcher(newPassword);
                    if (!m.find()) {
                        ifError = true;
                    }
                }
                if (ifError) {
                    throw new BusinessMessage("密码格式为字母、数字、特殊符号~!@#$%^&*()”’,.组合");
                }
            }
        }
    }
}
