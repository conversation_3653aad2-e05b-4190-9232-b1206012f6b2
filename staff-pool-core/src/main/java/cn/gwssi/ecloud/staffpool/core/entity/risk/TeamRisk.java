package cn.gwssi.ecloud.staffpool.core.entity.risk;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

public class TeamRisk extends BaseModel {

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("风险模型id")
    private String configId;

    @ApiModelProperty("风险模型name")
    private String configName;

    @ApiModelProperty("风险等级")
    private String level;

    @ApiModelProperty("调整后的风险等级")
    private String changeLevel;

    @ApiModelProperty("调整后的风险等级")
    private String reason;

    @ApiModelProperty("调整后的风险等级")
    private String attach;

    public TeamRisk() {
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getChangeLevel() {
        return changeLevel;
    }

    public void setChangeLevel(String changeLevel) {
        this.changeLevel = changeLevel;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }
}
