package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.PermissionListQueryVO;
import cn.gwssi.ecloud.staffpool.core.dao.OrgPostMapper;
import cn.gwssi.ecloud.staffpool.core.dao.OrgRelationMapper;
import cn.gwssi.ecloud.staffpool.core.dao.OrgRoleMapper;
import cn.gwssi.ecloud.staffpool.core.dao.OrgUserMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompPermissionApplicationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.BaseInstDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionPersonListDTO;
import cn.gwssi.ecloudframework.base.api.Page;
import cn.gwssi.ecloudframework.base.api.query.FieldRelation;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.query.WhereClause;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultFieldLogic;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultPage;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryField;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.core.manager.OrgRelationManager;
import cn.gwssi.ecloudframework.org.core.model.OrgRelation;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UompPermissionApplicationServiceImpl extends BaseManager<String, UompPermissionApplication> implements UompPermissionApplicationService {

    @Autowired
    private UompPermissionApplicationMapper uompPermissionApplicationMapper;
    @Autowired
    private UompPersonInfoService uompPersonInfoService;
    @Autowired
    private OrgUserMapper orgUserMapper;
    @Autowired
    private OrgRelationMapper orgRelationMapper;
    @Autowired
    private OrgRoleMapper orgRoleMapper;
    @Autowired
    private OrgPostMapper orgPostMapper;
    @Autowired
    private OrgRelationManager orgRelationManager;
    @Autowired
    private UompPermissionInService uompPermissionInService;
    @Autowired
    private UompAccountApplyPersonService uompAccountApplyPersonService;
    @Autowired
    private UompOrgGroupService uompOrgGroupService;
    @Autowired
    private UompAdmissionPersonService uompAdmissionPersonService;

    @Override
    public PageResult getList(PermissionListQueryVO permissionListQueryVO) {
        // 判断登陆人角色,根据角色进行列表权限展示
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        List<IUserRole> roles = user.getRoles();
        // 运维组织
        String orgId = user.getOrgId();
        // 用户id
        String userId = user.getId();
        if (roles == null || roles.isEmpty()) {
            return new PageResult(new ArrayList());
        }
        // 判断是否是运维人员(登录运维平台的都是已分配过账号的)
        QueryFilter iup = new DefaultQueryFilter(true);
        iup.addFilter("ORG_USER_ID", userId, QueryOP.EQUAL);
        iup.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        iup.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonInfo> isUompPerson = uompPersonInfoService.query(iup);
        // 通过eworker注册的账号直接返回null
        if (CollectionUtil.isEmpty(isUompPerson)) {
            return new PageResult(new ArrayList());
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        if (null == permissionListQueryVO.getPageNo()) {
            permissionListQueryVO.setPageNo(1);
        }
        if (null == permissionListQueryVO.getPageSize()) {
            permissionListQueryVO.setPageSize(10);
        }
        QueryFilter queryFilter = new DefaultQueryFilter();
        // 设置分页
        Page page = new DefaultPage(permissionListQueryVO.getPageNo(), permissionListQueryVO.getPageSize(), new ArrayList<>(), true);
        queryFilter.setPage(page);
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) { // 甲方运维负责人、总包商运维经理
            // 查看所有
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER")) { // 服务商运维负责人
            // 查看运维组下申请
            queryFilter.addFilter("uaa.CREATE_ORG_ID", orgId, QueryOP.EQUAL);
        } else if (roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) { // 帮助台、服务台
            // 查看本人下申请
            queryFilter.addFilter("uaa.CREATE_BY", userId, QueryOP.EQUAL);
        } else {
            // 其他角色返回空列表
            return new PageResult(new ArrayList());
        }
        // 未删除
        queryFilter.addFilter("uaa.DEL_FLAG", "0", QueryOP.EQUAL);
        // 权限申请状态
        String applyStatus = permissionListQueryVO.getApplyStatus();
        if (StringUtils.isNotEmpty(applyStatus)) {
            queryFilter.addFilter("uaa.APPLY_STATUS", applyStatus, QueryOP.IN);
        }
        // 申请时间
        String apply_time = permissionListQueryVO.getApplyTime();
        if (StringUtils.isNotEmpty(apply_time)) {
            String[] datas = apply_time.split(",");
            queryFilter.addFilter("uaa.APPLY_TIME", datas[0], QueryOP.GREAT_EQUAL);
            queryFilter.addFilter("uaa.APPLY_TIME", datas[1], QueryOP.LESS_EQUAL);
        }
        // 申请人
        String applyUserName = permissionListQueryVO.getApplyUserName();
        if (StringUtils.isNotEmpty(applyUserName)) {
            queryFilter.addFilter("uaa.APPLY_USER_NAME", applyUserName, QueryOP.LIKE);
        }
        // 申请标题
        String applyTitle = permissionListQueryVO.getApplyTitle();
        if (StringUtils.isNotEmpty(applyTitle)) {
            queryFilter.addFilter("uaa.APPLY_TITLE", applyTitle, QueryOP.LIKE);
        }

        // 申请时间倒叙
        queryFilter.addFieldSort("uaa.APPLY_TIME", "DESC");
        List<WhereClause> whereClauses = queryFilter.getFieldLogic().getWhereClauses();
        DefaultFieldLogic orFieldLogicAnd = new DefaultFieldLogic();
        orFieldLogicAnd.setFieldRelation(FieldRelation.AND);
        orFieldLogicAnd.getWhereClauses().add(new DefaultQueryField("uaa.APPLY_STATUS", QueryOP.EQUAL, "0"));
        orFieldLogicAnd.getWhereClauses().add(new DefaultQueryField("uaa.CREATE_BY", QueryOP.EQUAL, user.getUserId()));

        DefaultFieldLogic orFieldLogicOr = new DefaultFieldLogic();
        orFieldLogicOr.setFieldRelation(FieldRelation.OR);
        orFieldLogicOr.getWhereClauses().add(new DefaultQueryField("uaa.APPLY_STATUS", QueryOP.NOT_EQUAL, "0"));
        orFieldLogicOr.getWhereClauses().add(orFieldLogicAnd);

        whereClauses.add(orFieldLogicOr);

        List<UompPermissionListDTO> dTOList = uompPermissionApplicationMapper.getList(queryFilter);
        if (!CollectionUtils.isEmpty(dTOList)) {
            for (UompPermissionListDTO dto : dTOList) {
                // 审核状态：权限申请不通过 查询taskId和taskLinkedId(跳转申请表单需要)
                String status = dto.getApplyStatus();
                if ("3".equals(status)) {
                    // 流程实例id
                    String instId = dto.getInstId();
                    //根据instId查出taskId和taskLinkedId
                    BaseInstDTO baseDTO = uompPersonInfoService.selectTaskIdByInstId(instId);
                    if (null != baseDTO) {
                        // 任务id
                        dto.setTaskId(baseDTO.getTaskId());
                        // 流程任务id
                        dto.setTaskLinkId(baseDTO.getLinkId());
                    }
                }
            }
        }
        return new PageResult(dTOList);
    }

    /**
     * 20240625查询逻辑：
     * 1. 人员账号表： 账号激活并启用的普通用户
     * 2. 运维人员表： 审核通过、背调非不合格、非黑名单、已分配账号、处于驻场服务中的人员
     **/
    @Override
    public PageResult getPersonList(QueryFilter queryFilter) {
        // 判断登陆人角色,根据角色进行列表权限展示
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        // 用户id
        String userId = user.getId();
        // 判断是否是运维人员(登录运维平台的都是已分配过账号的)
        QueryFilter iup = new DefaultQueryFilter(true);
        iup.addFilter("ORG_USER_ID", userId, QueryOP.EQUAL);
        iup.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        iup.addFieldSort("CREATE_TIME", "DESC");
        List<UompPersonInfo> isUompPerson = uompPersonInfoService.query(iup);
        // 通过eworker注册的账号直接返回null
        if (CollectionUtil.isEmpty(isUompPerson)) {
            return new PageResult(new ArrayList());
        }
        // 角色判断
        List<IUserRole> roles = user.getRoles();
        // 运维组织
        String orgId = user.getOrgId();
        if (roles == null || roles.isEmpty()) {
            return new PageResult(new ArrayList());
        }
        List<String> roleNames = roles.stream().map(new Function<IUserRole, String>() {
            @Override
            public String apply(IUserRole iUserRole) {
                return iUserRole.getAlias();
            }
        }).collect(Collectors.toList());
        UompPersonInfo uompPersonInfo = isUompPerson.get(0);
        String companyId = uompPersonInfo.getWorkingCompanyId();
        if (roleNames.contains("G_ROLE_CUSTOMER_LEADER") || roleNames.contains("G_ROLE_MANAGER")) { // 甲方运维负责人、总包商运维经理
            // 查看所有通过人员
        } else if (roleNames.contains("G_ROLE_SEVICEMANAGER") || roleNames.contains("ITSM_HELP") || roleNames.contains("ITSM_SERVICE")) { // 服务商运维负责人、帮助台、服务台
            // 服务商下的通过人员
            queryFilter.addFilter("upi.WORKING_COMPANY_ID", companyId, QueryOP.EQUAL);
        } else if (roleNames.contains("ITSM_GROUP LEADER")) { // 运维组长
            // 运维组下的通过人员
            queryFilter.addFilter("tgroup.ORG_GROUP_ID", orgId, QueryOP.EQUAL);
        } else {
            // 其他角色返回空列表
            return new PageResult(new ArrayList());
        }
        // 人员账号类型：普通用户
        queryFilter.addFilter("tuser.TYPE_", "1", QueryOP.EQUAL);
        // 人员账号状态：激活状态（1激活 0未激活）
        queryFilter.addFilter("tuser.ACTIVE_STATUS_", 1, QueryOP.EQUAL);
        // 人员未删除的
        queryFilter.addFilter("upi.DEL_FLAG", "0", QueryOP.EQUAL);
        // 人员审核通过的
        queryFilter.addFilter("upi.TRIAL_STATUS", "2", QueryOP.EQUAL);
        // 人员非黑名单
        queryFilter.addFilter("upi.BLACKLIST", "0", QueryOP.EQUAL);
        // 人员背调非不合格
        queryFilter.addFilter("upi.BACKGROUND_STATUS", "2", QueryOP.NOT_EQUAL);
        // 人员已分配账号的
        queryFilter.addFilter("upi.IS_ACCOUNT", "1", QueryOP.EQUAL);
        //2024-12-10 非驻场的也可申请
        // 人员驻场服务中
//        queryFilter.addFilter("upi.ENTRY_STATUS", "0", QueryOP.EQUAL);
        //根据启用/停用状态筛选用户，前提是激活状态的
        List<UompPermissionPersonListDTO> personList = orgUserMapper.selectUserStatus(queryFilter);
        if (!CollectionUtils.isEmpty(personList)) {
            for (UompPermissionPersonListDTO dto : personList) {
                // 运维组织 人员表中获取运维组织名称
                dto.setMaintenanceGroupName((StringUtils.isNotEmpty(dto.getGroupName())) ? dto.getGroupName() : "未分配");
                //查询账号所绑定的角色
                String existingRoles = orgRoleMapper.selectRoleByAccount(dto.getUserAccount());
                dto.setExistingRoles(existingRoles == null ? "未分配" : existingRoles);
                String existingPositions = orgPostMapper.selectPositionsByAccount(dto.getUserAccount());
                dto.setExistingPositions(existingPositions == null ? "未分配" : existingPositions);
                //11-25 应用系统 查询账号申请，账号申请中没有则查询驻场申请
                //1.查询账号申请中人员信息，审核通过，且有账号的
                UompAccountApplyPerson info = uompAccountApplyPersonService.selectInfoByPersonId(dto.getUserId());
                if (info != null && StringUtils.isNotBlank(info.getEngagementProjectJson())) {
                    List<Map> mapList = JSONObject.parseArray(info.getEngagementProjectJson(),Map.class);
                    StringJoiner stringJoiner = new StringJoiner(",");
                    mapList.forEach(map -> stringJoiner.add(String.valueOf(map.get("name"))));
                    dto.setSystemName(stringJoiner.toString());
                    dto.setExistingSystem(info.getEngagementProjectJson());
                }else {
                    //查询驻场的应用系统
                    QueryFilter qf = new DefaultQueryFilter(true);
                    // 身份证号
                    qf.addFilter("PERSON_ID", dto.getUserId(), QueryOP.EQUAL);
                    // 人员入场申请审核通过
                    qf.addFilter("APPLY_STATUS", "2", QueryOP.EQUAL);
                    // 未删除
                    qf.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
                    qf.addFieldSort("CREATE_TIME","DESC");
                    List<UompAdmissionPerson> admissionPersonList = uompAdmissionPersonService.query(qf);
                    if (!CollectionUtils.isEmpty(admissionPersonList)){
                        UompAdmissionPerson uompAdmissionPerson = admissionPersonList.get(0);
                        dto.setSystemName(uompAdmissionPerson.getEngagementProject());
                        dto.setExistingSystem(uompAdmissionPerson.getEngagementProjectJson());
                    }
                }
                // 查询账号绑定的应有系统 账号申请管理模块的人员没有人员系统，可添加
//                BaseDTO baseDTO = uompAccountApplyPersonService.selectSystemByUserId(dto.getUserId());
//                if (null != baseDTO) {
//                    if (StringUtils.isNotEmpty(baseDTO.getId())) {
//                        dto.setSystemName(baseDTO.getName());
//                        dto.setExistingSystem(JSONObject.toJSONString(Arrays.asList(baseDTO)));
//                    }
//                }
            }
        }
        return new PageResult(personList);
    }

    @Override
    public PageResult getRoleByUserId(QueryFilter queryFilter) {
        // 根据用户id查询系统用户id
        String userId = (String) queryFilter.getParams().get("userId");
        UompPersonInfo uompPersonInfo = uompPersonInfoService.get(userId);
        if (null == uompPersonInfo || (null != uompPersonInfo && StringUtils.isEmpty(uompPersonInfo.getOrgUserId()))) {
            return new PageResult(new ArrayList());
        }
        // 系统用户id
        String orgUserId = uompPersonInfo.getOrgUserId();
        queryFilter.addFilter("orelation.user_id_", orgUserId, QueryOP.EQUAL);
        List<UompPermissionBaseDTO> list = orgRoleMapper.selectOwerRoleByUserId(queryFilter);
        return new PageResult(list);
    }

    @Override
    public PageResult getPostByUserId(QueryFilter queryFilter) {
        // 根据用户id查询系统用户id
        String userId = (String) queryFilter.getParams().get("userId");
        UompPersonInfo uompPersonInfo = uompPersonInfoService.get(userId);
        if (null == uompPersonInfo || (null != uompPersonInfo && StringUtils.isEmpty(uompPersonInfo.getOrgUserId()))) {
            return new PageResult(new ArrayList());
        }
        // 系统用户id
        String orgUserId = uompPersonInfo.getOrgUserId();
        queryFilter.addFilter("orelation.user_id_", orgUserId, QueryOP.EQUAL);
        List<UompPermissionBaseDTO> list = orgPostMapper.selectOwerPostByUserId(queryFilter);
        return new PageResult(list);
    }

    /**
     * 531版本暂不区分用户是否存在岗位，查询全部岗位
     **/
    @Override
    public PageResult getNoPostByUserId(QueryFilter queryFilter) {
        // 查询所有岗位
        List<UompPermissionBaseDTO> dtoList = new ArrayList<>();
        queryFilter.addFieldSort("ID_", "DESC");
        List<OrgPost> list = orgPostMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        if (!CollectionUtils.isEmpty(list)) {
            for (OrgPost info : list) {
                UompPermissionBaseDTO dto = new UompPermissionBaseDTO();
                dto.setId(info.getId());
                dto.setName(info.getName());
                dto.setCode(info.getCode());
                dto.setDescription(info.getDescription());
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
        }
        return pageResult;
    }

    /**
     * 531版本暂不区分用户是否存在角色，查询启用的全部角色
     **/
    @Override
    public PageResult getNoRoleByUserId(QueryFilter queryFilter) {
        List<UompPermissionBaseDTO> dtoList = new ArrayList<>();
        // 查询所有启用的角色 ENABLED_=1
        queryFilter.addFilter("ENABLED_", 1, QueryOP.EQUAL);
        queryFilter.addFieldSort("ID_", "DESC");
        List<OrgRole> list = orgRoleMapper.query(queryFilter);
        PageResult pageResult = new PageResult(list);
        if (!CollectionUtils.isEmpty(list)) {
            for (OrgRole info : list) {
                UompPermissionBaseDTO dto = new UompPermissionBaseDTO();
                dto.setId(info.getId());
                dto.setName(info.getName());
                dto.setCode(info.getAlias());
                dto.setDescription(info.getDescription());
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
        }
        return pageResult;
    }

    @Override
    public List<String> inEmpower(String permissionInIds) {
        //失败结果集合
        List<String> errorList = new ArrayList<>();
        if (StringUtils.isNotEmpty(permissionInIds)) {
            List<String> ids = Arrays.asList(permissionInIds.split(","));
            for (String id : ids) {
                //查询内部权限表数据
                UompPermissionIn uompPermissionIn = uompPermissionInService.get(id);
                if (null != uompPermissionIn) {
                    String authorizationStatus = uompPermissionIn.getAuthorizationStatus();
                    //校验是否已授权,已授权的直接跳过不处理
                    if ("1".equals(authorizationStatus)) {
                        continue;
                    }
                    // 运维人员id
                    String userId = uompPermissionIn.getUserId();
                    // 根据运维人员id查询系统人员id
                    String orgUserId = uompPersonInfoService.getOrgUserIdByUserId(userId);
                    // 通过人员id获取系统机构id
                    String orgGroupId = orgRelationMapper.selectGroupIdByUserId(orgUserId);
                    String role = uompPermissionIn.getRole();
                    String position = uompPermissionIn.getPosition();
                    String applyType = uompPermissionIn.getApplyType();
                    // 角色集合
                    List<OrgRelation> orgRoleList;
                    // 岗位集合
                    List<OrgRelation> orgPostList;
                    try {
                        // 0-增权限 1-删权限 2-激活权限
                        if ("0".equals(applyType)) {
                            //解析角色json
                            orgRoleList = getRoleInfoList(role, orgUserId);
                            if (!CollectionUtils.isEmpty(orgRoleList)) {
                                orgRelationManager.batchAdd(orgRoleList);
                            }
                            //解析岗位json
                            orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                            if (!CollectionUtils.isEmpty(orgPostList)) {
                                orgRelationManager.batchAdd(orgPostList);
                            }
                        } else if ("1".equals(applyType)) {
                            //解析角色json
                            orgRoleList = getRoleInfoList(role, orgUserId);
                            if (!CollectionUtils.isEmpty(orgRoleList)) {
                                orgRelationManager.batchRemove(orgRoleList);
                            }
                            // 解析岗位json
                            orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                            if (!CollectionUtils.isEmpty(orgPostList)) {
                                orgRelationManager.batchRemove(orgPostList);
                            }
                        } else if ("2".equals(applyType)) {
                            //修改为激活状态
                            orgUserMapper.updateStatusById1(userId, 1);
                            //解析角色json
                            orgRoleList = getRoleInfoList(role, orgUserId);
                            if (!CollectionUtils.isEmpty(orgRoleList)) {
                                orgRelationManager.batchAdd(orgRoleList);
                            }
                            //解析岗位json
                            orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                            if (!CollectionUtils.isEmpty(orgPostList)) {
                                orgRelationManager.batchAdd(orgPostList);
                            }
                        }
                    } catch (Exception e) { // 异常
                        // 1.依据applyType参数进行角色、岗位回滚 - 反向执行
                        try {
                            if ("0".equals(applyType)) {
                                //解析角色json
                                orgRoleList = getRoleInfoList(role, orgUserId);
                                if (!CollectionUtils.isEmpty(orgRoleList)) {
                                    orgRelationManager.batchRemove(orgRoleList);
                                }
                                // 解析岗位json
                                orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                                if (!CollectionUtils.isEmpty(orgPostList)) {
                                    orgRelationManager.batchRemove(orgPostList);
                                }
                            } else if ("1".equals(applyType)) {
                                //解析角色json
                                orgRoleList = getRoleInfoList(role, orgUserId);
                                if (!CollectionUtils.isEmpty(orgRoleList)) {
                                    orgRelationManager.batchAdd(orgRoleList);
                                }
                                //解析岗位json
                                orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                                if (!CollectionUtils.isEmpty(orgPostList)) {
                                    orgRelationManager.batchAdd(orgPostList);
                                }
                            } else if ("2".equals(applyType)) {
                                //修改为激活状态
                                orgUserMapper.updateStatusById1(userId, 0);
                                //解析角色json
                                orgRoleList = getRoleInfoList(role, orgUserId);
                                if (!CollectionUtils.isEmpty(orgRoleList)) {
                                    orgRelationManager.batchRemove(orgRoleList);
                                }
                                // 解析岗位json
                                orgPostList = getPostInfoList(position, orgUserId, orgGroupId);
                                if (!CollectionUtils.isEmpty(orgPostList)) {
                                    orgRelationManager.batchRemove(orgPostList);
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        // 2.记录错误permission_in_id
                        errorList.add(id);
                        // 3.跳出循环，不做状态更新
                        continue;
                    }
                    //将数据改为已授权状态
                    uompPermissionIn.setAuthorizationStatus("1");
                    uompPermissionInService.update(uompPermissionIn);
                }
            }
        }
        return errorList;
    }

    /**
     * 封装角色公共方法
     *
     * @param role      角色集合
     * @param orgUserId 系统用户id
     * @return List<OrgRelation> 添加或者删除的角色对象集合
     **/
    private List<OrgRelation> getRoleInfoList(String role, String orgUserId) {
        List<OrgRelation> orgRoleList = new ArrayList<>();
        if (StringUtils.isNotEmpty(role)) {
            //解析角色json
            List<Object> roleList = JSONObject.parseObject(role, List.class);
            for (Object roleObj : roleList) {
                Map<String, Object> roleMap = (Map) JSONObject.toJSON(roleObj);
                String roleId = (String) roleMap.get("id");
                if (StringUtils.isNotEmpty(roleId)) {
                    OrgRelation orgRelation = new OrgRelation();
                    orgRelation.setGroupId(roleId);
                    orgRelation.setSn("0");
                    orgRelation.setType("userRole");
                    orgRelation.setUserId(orgUserId);
                    orgRoleList.add(orgRelation);
                }
            }
        }
        return orgRoleList;
    }

    /**
     * 封装岗位公共方法
     *
     * @param post      岗位集合
     * @param orgUserId 系统用户id
     * @param groupId   系统运维组织id
     * @return List<OrgRelation> 添加或者删除的岗位对象集合
     **/
    private List<OrgRelation> getPostInfoList(String post, String orgUserId, String groupId) {
        List<OrgRelation> orgPostList = new ArrayList<>();
        if (StringUtils.isNotEmpty(post)) {
            //解析岗位json
            List<Object> positionList = JSONObject.parseObject(post, List.class);
            for (Object positionObj : positionList) {
                Map<String, Object> positionMap = (Map) JSONObject.toJSON(positionObj);
                String postId = (String) positionMap.get("id");
                if (StringUtils.isNotEmpty(postId)) {
                    OrgRelation orgRelation = new OrgRelation();
                    orgRelation.setGroupId(postId);
                    orgRelation.setIsMaster(0);
                    orgRelation.setStatus(1);
                    orgRelation.setType("postUser");
                    orgRelation.setUserId(orgUserId);
                    orgRelation.setOrgId(groupId);
                    orgPostList.add(orgRelation);
                }
            }
        }
        return orgPostList;
    }

    @Override
    public void updateStatus(String id, String status) {
        if (StringUtils.isNotEmpty(id) && StringUtils.isNotEmpty(status)) {
            UompPermissionApplication uompPermissionApplication = uompPermissionApplicationMapper.get(id);
            if (null != uompPermissionApplication) {
                uompPermissionApplication.setApplyStatus(status);
                uompPermissionApplicationMapper.update(uompPermissionApplication);
            }
        }
    }

    @Override
    public List<OrgGroupBaseDTO> getTreeByOrgType() {
        List<OrgGroupBaseDTO> dtoList = new ArrayList<>();
        // 查询运维组织机构
        List<GroupUomp> groupUompList = uompOrgGroupService.getAll();
        for (GroupUomp groupUomp : groupUompList) {
            OrgGroupBaseDTO orgGroup = new OrgGroupBaseDTO();
            orgGroup.setId(groupUomp.getId());
            orgGroup.setName(groupUomp.getName());
            orgGroup.setParentId(groupUomp.getParentId());
            orgGroup.setPath(groupUomp.getPath());
            dtoList.add(orgGroup);
        }
        return dtoList;
    }
}