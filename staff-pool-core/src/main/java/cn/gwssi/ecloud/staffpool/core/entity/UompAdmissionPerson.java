package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompAdmissionPerson extends BaseModel {

    @ExcelIgnore
    private String applyId;

    @ApiModelProperty(value = "人员id")
    private String personId;

    @ApiModelProperty("姓名")
    private String personName;

    @ApiModelProperty("身份证号")
    private String personCard;

    @ApiModelProperty("入职日期")
    private String entryDate;

    @ApiModelProperty("学历")
    private String education;

    @ApiModelProperty("职称")
    private String technicalPost;

    @ApiModelProperty("所在公司")
    private String workingCompany;

    @ApiModelProperty("所在公司id")
    private String workingCompanyId;

    @ApiModelProperty("所在公司json")
    private String workingCompanyJson;

    @ApiModelProperty("联系方式")
    private String tel;

    @ApiModelProperty("参与项目")
    private String engagementProject;

    @ApiModelProperty("笔试成绩")
    private String score;

    @ApiModelProperty("面谈结果")
    private String interviewResult;

    @ExcelIgnore
    private String maintenanceGroupId;

    @ApiModelProperty("进驻运维组")
    private String maintenanceGroupName;

    @ExcelIgnore
    private String postId;

    @ExcelIgnore
    private String postName;

    @ApiModelProperty("工作内容")
    private String jobContent;

    @ExcelIgnore
    private String isExport;

    @ExcelIgnore
    private String applyStatus;

    @ApiModelProperty("进驻时间")
    @DateTimeFormat("yyyy-MM-dd")
    private Date inTime;

    @ExcelIgnore
    private Date outTime;

    @ExcelIgnore
    private String outApplyStatus;

    @ExcelIgnore
    private String fileInfo;

    @ExcelIgnore
    private String createOrgId;

    @ExcelIgnore
    private String updateOrgId;

    @ExcelIgnore
    private String delFlag;

    @ExcelIgnore
    private String postFileInfo;

    @ApiModelProperty("服务类型")
    private String servicType;

    @ApiModelProperty("驻场服务地点")
    private String serviceLocation;

    @ApiModelProperty("是否涉密岗位")
    private String isClassifiedPosition;

    @ApiModelProperty("技术方向")
    private String technicalDirection;

    private String engagementProjectId;

    private String engagementProjectJson;

    private String maintenanceGroupJson;

    @ApiModelProperty(value = "预计到访时间")
    private String planVisitTime;
    @ApiModelProperty(value = "计划退出时间")
    private String planOutTime;
    @ApiModelProperty(value = "角色")
    private String personRole;
    @ApiModelProperty(value = "角色 id")
    private String personRoleId;
    @ApiModelProperty(value = "角色 json")
    private String personRoleJson;
    @ApiModelProperty(value = "主管部门")
    private String departName;
    @ApiModelProperty(value = "主管部门 id")
    private String departId;
    @ApiModelProperty(value = "主管部门 json")
    private String departJson;
    @ApiModelProperty(value = "岗位类型")
    private String positionType;
    @ApiModelProperty(value = "涉密岗位性质")
    private String classifiedPosition;
    @ApiModelProperty(value = "申请岗位 JSON")
    private String postJson;

    @ApiModelProperty(value = "性别 0: 男,  1:女")
    private String sex;

    private static final long serialVersionUID = 1L;
}