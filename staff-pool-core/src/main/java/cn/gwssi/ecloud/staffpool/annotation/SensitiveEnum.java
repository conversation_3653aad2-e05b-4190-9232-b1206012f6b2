package cn.gwssi.ecloud.staffpool.annotation;

public enum SensitiveEnum {


    SENSITIVE("sensitive","脱敏"),

    ENCRYPT("encrypt","加密"),

    DECRYPT("decrypt","解密");

    private String key;

    private String value;

    SensitiveEnum(String key,String value){
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
