package cn.gwssi.ecloud.staffpool.vo;

import cn.gwssi.ecloud.staffpool.core.entity.*;
import cn.gwssi.ecloud.staffpool.dto.UompBlackDto;
import cn.gwssi.ecloud.staffpool.dto.VoteUserAnswerDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UompPersonInfoVo extends UompPersonInfo {
    private String regPermanentResidenceName;
    private List<UompPersonEducational> educationList;
    private List<UompPersonJob> jobList;
    private List<UompPersonTechnology> techList;
    private List<UompPersonSocial> socialList;
    private List<UompPersonNoCrime> noCrimeList;

    private List<UompPersonAbroad> abroadList;

    private List<UompPersonEntryExit> entryExitList;

    private List<UompTrainingRecord> trainList;

    private List<VoteUserAnswerDto> assessmentList;

    private List<UompBlackDto> blackList;
}