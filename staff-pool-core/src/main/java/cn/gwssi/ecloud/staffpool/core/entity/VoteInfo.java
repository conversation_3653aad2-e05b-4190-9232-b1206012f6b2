package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@ApiModel(description="vote_info")
@Data
public class VoteInfo extends BaseModel {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    private String title;

    @ApiModelProperty(value="")
    private String state;

    @ApiModelProperty(value="")
    private Date releaseTime;

    @ApiModelProperty(value="")
    private Date startTime;

    @ApiModelProperty(value="")
    private Date endTime;

    @ApiModelProperty(value="")
    private String surveyScope;

    @ApiModelProperty(value="")
    private String anonymity;

    @ApiModelProperty(value="")
    private String releasePeopleId;

    @ApiModelProperty(value="")
    private String status;

    @ApiModelProperty(value="")
    private String collected;

    @ApiModelProperty(value="")
    private String explain;

    @ApiModelProperty(value="")
    private String releasePeopleName;

    @ApiModelProperty(value="")
    private String type;

    @ApiModelProperty(value="")
    private String projectName;

    @ApiModelProperty(value="")
    private String trainingContent;

    @ApiModelProperty(value="")
    private String trainingSite;

    @ApiModelProperty(value="")
    private String trainingDate;

    private static final long serialVersionUID = 1L;
}