package cn.gwssi.ecloud.staffpool.vo;

import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification;
import io.swagger.annotations.ApiModel;
import java.util.List;

//@EqualsAndHashCode(callSuper = true)
@ApiModel(description="服务商基本信息保存")
//@Data
public class UompSupplierManagementVO extends UompSupplierManagement {
//    private String supplierName;
//
//    private String creditCode;
//
//    private String shortName;
//
//    private String tel;
//
//    private String respName;
//
//    private String respTel;
//
//    private String supplierType;
//
//    private String supplierStatus;
//
//    private String registerProvince;
//
//    private String registerCity;
//
//    private String registerRegin;
//
//    private String registerAddress;
//
//    private String contactAddress;
//
//    private String remark;
//
//    private String createOrgId;
//
//    private String updateOrgId;
//
//    private String delFlag;
//
//    private Date entryTime;
//
//    private String entryId;
//
//    private String entryName;
//
//    private String groupId;
//
//    private String usedName;

    private List<UompSupplierFile> fileInfos;

    private List<UompSupplierQualification> qualifications;

    public List<UompSupplierFile> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<UompSupplierFile> fileInfos) {
        this.fileInfos = fileInfos;
    }

    public List<UompSupplierQualification> getQualifications() {
        return qualifications;
    }

    public void setQualifications(List<UompSupplierQualification> qualifications) {
        this.qualifications = qualifications;
    }
}