package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@ApiModel(description="人员培训记录响应类")
@Data
public class GroupBySupplierByGXDTO implements Serializable {

    @ApiModelProperty(value="数量")
    private Integer sum;
    @ApiModelProperty(value="分类")
    private Map<String, List<SupplierManagementDTO>> children;
}
