package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompTrainingRecord;
import cn.gwssi.ecloud.staffpool.dto.UompTrainingRecordDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompTrainingRecordService extends Manager<String, UompTrainingRecord> {

    int insertSelective(UompTrainingRecord record);

    List<UompTrainingRecord> selectTrainingRecordByUserId(String userId);

    int updateByPrimaryKeySelective(UompTrainingRecord trainingRecord);
}
