package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "人员详情信息", description="人员详情")
@Data
public class UompPersonInfoDTO implements Serializable {

    @ApiModelProperty(value="居住地址")
    private String address;
    @ApiModelProperty(value="运维组织id")
    private String groupId;
    @ApiModelProperty(value="系统运维组织id")
    private String orgGroupId;
    @ApiModelProperty(value="人员名称")
    private String personName;
    @ApiModelProperty(value="性别")
    private String personSex;
    @ApiModelProperty(value="岗位")
    private String post;
    @ApiModelProperty(value="就职公司名称")
    private String supplierName;
    @ApiModelProperty(value="电话")
    private String tel;
    @ApiModelProperty(value="就职公司id")
    private String workingCompanyId;
}
