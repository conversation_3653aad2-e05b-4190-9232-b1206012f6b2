package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UompTempAdmissionDTO {

    @ExcelProperty("姓名")
    private String personName;

    @ExcelProperty("身份证号")
    private String personCard;

    @ExcelProperty("联系方式")
    private String tel;

    @ExcelProperty("就职公司")
    private String workingCompany;

    @ExcelProperty("预计到访时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    private Date planVisitTime;

    @ExcelProperty("接待人")
    private String acceptName;

    @ExcelProperty("实际到访时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    private Date realVisitTime;

    @ExcelProperty("离开时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm")
    private Date exitTime;

    @ExcelProperty("工作内容")
    private String jobContent;

    @ExcelProperty("应用系统")
    private String engagementProject;

    @ExcelProperty("备案状态")
    private String filingStatus;
}
