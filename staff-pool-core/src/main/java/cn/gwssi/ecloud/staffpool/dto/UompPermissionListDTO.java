package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "权限申请列表类", description = "权限申请列表类")
@Data
public class UompPermissionListDTO implements Serializable {
    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "标题")
    private String applyTitle;
    @ApiModelProperty(value = "申请人")
    private String applyUserName;
    @ApiModelProperty(value = "申请时间")
    private String applyTime;
    @ApiModelProperty(value = "状态")
    private String applyStatus;
    @ApiModelProperty(value = "流程实例id")
    private String instId;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "linkid")
    private String taskLinkId;
}
