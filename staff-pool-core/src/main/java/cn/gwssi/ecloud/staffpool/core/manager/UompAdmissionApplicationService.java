package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.ProjectBySupplierVO;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompAdmissionApplicationService extends Manager<String, UompAdmissionApplication> {

    int insertSelective(UompAdmissionApplication record);

    ResponsePageData<PersonListDTO> getPersonList(PersonListQueryVO personListQueryVO);

    EntryApplyDTO getEntryApplyById(String id);

    ResponsePageData<PersonInfoOverviewDTO> personInfoOverview(PersonListQueryVO personListQueryVO);

    void updateStatus(String id, String status);

    List<PercentageDTO> getPersonLocation();

    AdmissionPersonDTO admissionPerson();

    ResponsePageData<ProjectBySupplierDTO> getProjectBySupplier(ProjectBySupplierVO projectBySupplier);

    List<PersonListDTO> export(PersonListQueryVO personListQueryVO);

    /**
     * 驻场人员信息解密
     * @param id
     * @param attr
     * @return
     */
    String decryptEntryInfo(String id,String attr);
}
