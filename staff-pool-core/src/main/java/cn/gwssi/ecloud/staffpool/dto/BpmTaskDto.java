package cn.gwssi.ecloud.staffpool.dto;

import cn.gwssi.ecloud.staffpool.core.entity.BpmTask;
import lombok.Data;

@Data
public class BpmTaskDto extends BpmTask {
    private String creator;

    private String reportId;
    private String instStatus;
    private String instCreateTime;
    private String instName;
    private String checkStatus;
    private String checkTime;
    private String nodeTypeKey;
    private String nodeTypeName;
    private String linkId;
    private String linkTaskType;
    private String personInfoId;

    private String opinionId;

    private String defName;

    private String subject;

    private String approveTime;

    private String approveStatus;

    private String nodeName;

    private String approverName;
}