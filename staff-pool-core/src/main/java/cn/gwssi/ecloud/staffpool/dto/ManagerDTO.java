package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description="运维组分类响应类")
@Data
public class ManagerDTO implements Serializable {

    @ApiModelProperty(value="id")
    private String id;
    @ApiModelProperty(value="名称")
    private String name;
    @ApiModelProperty(value="数量")
    private int num;
    @ApiModelProperty(value="组长ids")
    private String groupLeaderIds;
    @ApiModelProperty(value="领导数")
    private int leaderNum;
    @ApiModelProperty(value="普通组员数")
    private int unleaderNum;
}
