package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.PermissionListQueryVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication;
import cn.gwssi.ecloud.staffpool.core.manager.UompPermissionApplicationService;
import cn.gwssi.ecloud.staffpool.dto.OrgGroupBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionListDTO;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionPersonListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(description = "权限申请")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/permission")
public class UompPermissionApplicationController extends BaseController<UompPermissionApplication> {

    @Resource
    private UompPermissionApplicationService uompPermissionApplicationService;


    @ApiOperation(value = "列表查询")
    @PostMapping(value = "/getList")
    public PageResult<UompPermissionListDTO> getList(@Validated @RequestBody PermissionListQueryVO permissionListQueryVO) {
        return uompPermissionApplicationService.getList(permissionListQueryVO);
    }

    @ApiOperation(value = "用户选择接口")
    @RequestMapping(value = "/getPersonList", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPermissionPersonListDTO> getPersonList(HttpServletRequest request,
                                                                 @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                                 @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                                 @RequestParam(value = "status") @ApiParam(value = "状态 1-启用 0-禁用") Integer status,
                                                                 @RequestParam(value = "pathStr", required = false) @ApiParam(value = "路径") String pathStr,
                                                                 @RequestParam(value = "userName", required = false) @ApiParam(value = "用户名称") String userName) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 运维组织机构路径
        if (StringUtils.isNotEmpty(pathStr)) {
            // 左相似
            queryFilter.addFilter("tgroup.PATH_", pathStr, QueryOP.LEFT_LIKE);
        }
        // 人员名称
        if (StringUtils.isNotEmpty(userName)) {
            queryFilter.addFilter("tuser.FULLNAME_", userName, QueryOP.LIKE);
        }
        // 用户状态
        queryFilter.addFilter("tuser.STATUS_", status, QueryOP.EQUAL);
        return uompPermissionApplicationService.getPersonList(queryFilter);
    }

    @ApiOperation(value = "获取本人所拥有的角色")
    @RequestMapping(value = "/getRoleByUserId", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPermissionBaseDTO> getRoleByUserId(HttpServletRequest request,
                                                             @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                             @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                             @RequestParam(value = "userId") @ApiParam(value = "用户id") String userId) {
        // 不考虑用户中角色是否有禁用情况,查询用户的拥有角色
        QueryFilter queryFilter = getQueryFilter(request);
        // 角色类型
        queryFilter.addFilter("orelation.type_", "userRole", QueryOP.EQUAL);
        // 启用
        queryFilter.addFilter("orelation.status_", 1, QueryOP.EQUAL);
        queryFilter.addFieldSort("orole.create_time_", "DESC");
        queryFilter.getParams().put("userId", userId);
        return uompPermissionApplicationService.getRoleByUserId(queryFilter);
    }

    @ApiOperation(value = "获取本人所拥有的岗位")
    @RequestMapping(value = "/getPostByUserId", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPermissionBaseDTO> getPostByUserId(HttpServletRequest request,
                                                             @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                             @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                             @RequestParam(value = "userId") @ApiParam(value = "用户id") String userId) {
        QueryFilter queryFilter = getQueryFilter(request);
        // 岗位类型
        queryFilter.addFilter("orelation.type_", "postUser", QueryOP.EQUAL);
        // 启用
        queryFilter.addFilter("orelation.status_", 1, QueryOP.EQUAL);
        queryFilter.addFieldSort("opost.create_time_", "DESC");
        queryFilter.getParams().put("userId", userId);
        return uompPermissionApplicationService.getPostByUserId(queryFilter);
    }


    @ApiOperation(value = "获取本人没有的岗位")
    @RequestMapping(value = "/getNoPostByUserId", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPermissionBaseDTO> getNoPostByUserId(HttpServletRequest request,
                                                               @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                               @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                               @RequestParam(value = "userId") @ApiParam(value = "用户id") String userId) {
        QueryFilter queryFilter = getQueryFilter(request);
        return uompPermissionApplicationService.getNoPostByUserId(queryFilter);
    }

    @ApiOperation(value = "获取本人没有的角色")
    @RequestMapping(value = "/getNoRoleByUserId", method = {RequestMethod.POST, RequestMethod.GET})
    public PageResult<UompPermissionBaseDTO> getNoRoleByUserId(HttpServletRequest request,
                                                               @RequestParam(value = "pageNo") @ApiParam(value = "页码") Integer pageNo,
                                                               @RequestParam(value = "pageSize") @ApiParam(value = "每页数量") Integer pageSize,
                                                               @RequestParam(value = "userId") @ApiParam(value = "用户id") String userId) {
        QueryFilter queryFilter = getQueryFilter(request);
        return uompPermissionApplicationService.getNoRoleByUserId(queryFilter);
    }

    @ApiOperation(value = "内部授权")
    @RequestMapping(value = "/inEmpower", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<String>> inEmpower(@RequestParam("permissionInIds") @ApiParam(value = "申请内部权限表id,逗号隔开") String permissionInIds) {
        return getSuccessResult(uompPermissionApplicationService.inEmpower(permissionInIds));
    }

    @ApiOperation(value = "修改审核状态")
    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg updateStatus(@RequestParam(value = "id") @ApiParam(value = "主键id") String id,
                                  @RequestParam(value = "status") @ApiParam(value = "审核状态") String status) {
        uompPermissionApplicationService.updateStatus(id, status);
        return getSuccessResult("ok");
    }

    @ApiOperation(value = "根据当前登录人的机构类型获取机构树")
    @RequestMapping(value = "/getTreeByOrgType", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<OrgGroupBaseDTO>> getTreeByOrgType() {
        return getSuccessResult(uompPermissionApplicationService.getTreeByOrgType());
    }

    @Override
    protected String getModelDesc() {
        return "权限申请";
    }
}
