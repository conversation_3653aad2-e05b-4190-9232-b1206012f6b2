package cn.gwssi.ecloud.staffpool.core.dao;
import cn.gwssi.ecloud.staffpool.core.entity.UompOrgGroup;

import cn.gwssi.ecloud.staffpool.core.model.GroupUomp;
import cn.gwssi.ecloud.staffpool.core.model.OrderGroup;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UompOrgGroupMapper extends BaseDao<String, GroupUomp> {

    /**
     * 排序
     *
     * @param groupUomp
     */
    void chageOrder(GroupUomp groupUomp);

    List<GroupUomp> getAllSelective();

    GroupUomp getByCode(@Param("code") String code, @Param("excludeId") String excludeId);

    void remove(String id);

    List<GroupUomp> getChildByPath(String path);

    int updateById(@Param("updated")UompOrgGroup updated,@Param("id")String id);

    List<GroupUomp> getAll(@Param("virtual")String virtual, @Param("status")String status);

    List<String> getByPath(String orgGroupId);

    List<String> selectIdByType(@Param("type") String type,@Param("equal") String equal);
    List<OrderGroup> getOrderGroup();
}
