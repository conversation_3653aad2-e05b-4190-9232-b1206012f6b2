package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class UompTrainingRecord extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String trainingName;

    @ApiModelProperty(value="")
    
    private String projectName;

    @ApiModelProperty(value="")
    
    private String trainingMode;

    @ApiModelProperty(value="")
    
    private String conferenceNum;

    @ApiModelProperty(value="")
    
    private Date trainingBeginTime;

    @ApiModelProperty(value="")
    
    private Date trainingEndTime;

    @ApiModelProperty(value="")
    
    private String trainingTeacher;

    @ApiModelProperty(value="")
    
    private String trainingPlanId;

    @ApiModelProperty(value="")
    
    private String trainingContent;

    @ApiModelProperty(value="")
    
    private String trainingSite;

    @ApiModelProperty(value="")
    
    private String signInNum;

    @ApiModelProperty(value="")
    
    private String fileInfo;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private String createOrgId;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String updateOrgId;

    @ApiModelProperty(value="")
    
    private String delFlag;

    @ApiModelProperty(value="")
    
    private String trainingPlanName;

    @ApiModelProperty(value="")
    
    private String trainingDuration;

    private static final long serialVersionUID = 1L;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getTrainingName() {
        return trainingName;
    }

    public void setTrainingName(String trainingName) {
        this.trainingName = trainingName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTrainingMode() {
        return trainingMode;
    }

    public void setTrainingMode(String trainingMode) {
        this.trainingMode = trainingMode;
    }

    public String getConferenceNum() {
        return conferenceNum;
    }

    public void setConferenceNum(String conferenceNum) {
        this.conferenceNum = conferenceNum;
    }

    public Date getTrainingBeginTime() {
        return trainingBeginTime;
    }

    public void setTrainingBeginTime(Date trainingBeginTime) {
        this.trainingBeginTime = trainingBeginTime;
    }

    public Date getTrainingEndTime() {
        return trainingEndTime;
    }

    public void setTrainingEndTime(Date trainingEndTime) {
        this.trainingEndTime = trainingEndTime;
    }

    public String getTrainingTeacher() {
        return trainingTeacher;
    }

    public void setTrainingTeacher(String trainingTeacher) {
        this.trainingTeacher = trainingTeacher;
    }

    public String getTrainingPlanId() {
        return trainingPlanId;
    }

    public void setTrainingPlanId(String trainingPlanId) {
        this.trainingPlanId = trainingPlanId;
    }

    public String getTrainingContent() {
        return trainingContent;
    }

    public void setTrainingContent(String trainingContent) {
        this.trainingContent = trainingContent;
    }

    public String getTrainingSite() {
        return trainingSite;
    }

    public void setTrainingSite(String trainingSite) {
        this.trainingSite = trainingSite;
    }

    public String getSignInNum() {
        return signInNum;
    }

    public void setSignInNum(String signInNum) {
        this.signInNum = signInNum;
    }

    public String getFileInfo() {
        return fileInfo;
    }

    public void setFileInfo(String fileInfo) {
        this.fileInfo = fileInfo;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getTrainingPlanName() {
        return trainingPlanName;
    }

    public void setTrainingPlanName(String trainingPlanName) {
        this.trainingPlanName = trainingPlanName;
    }

    public String getTrainingDuration() {
        return trainingDuration;
    }

    public void setTrainingDuration(String trainingDuration) {
        this.trainingDuration = trainingDuration;
    }
}