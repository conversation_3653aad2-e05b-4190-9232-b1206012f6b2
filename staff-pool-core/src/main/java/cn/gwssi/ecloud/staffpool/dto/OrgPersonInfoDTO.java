package cn.gwssi.ecloud.staffpool.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "机构人员响应类")
public class OrgPersonInfoDTO implements Serializable {

    @ApiModelProperty(value = "公司")
    private String company;
    @ApiModelProperty(value = "人员")
    private List<BaseDTO> members;
}
