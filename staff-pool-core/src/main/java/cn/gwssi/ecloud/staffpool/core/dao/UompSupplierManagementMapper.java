package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.dto.PercentageDTO;
import cn.gwssi.ecloud.staffpool.dto.UompProjectSupplierListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloud.staffpool.dto.SupplierManagementDTO;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompSupplierManagementMapper extends BaseDao<String, UompSupplierManagement> {

    int insertSelective(UompSupplierManagement record);

    int updateById(UompSupplierManagement record);

    UompSupplierManagement selectIdByCreditCode(String creditCode);

    UompSupplierManagement selectIdBySupplierName(String supplierName);

    String selectSupplierIdByGroupId(@Param("orgId")String orgId);

    List<UompProjectSupplierListDTO> pageSupplierToProject(QueryFilter queryFilter);

    List<SupplierManagementDTO> selectIdAndSupplierNameBySupplierStatus();

    List<SupplierManagementDTO> selectSupplierTypeBySupplierStatus();

    List<SupplierManagementDTO> selectAllBySupplierType(String types);

    String selectIdByWorkingCompany(String workingCompany);

    String selectGroupIdByWorkingCompany(String workingCompany);

    List<PercentageDTO> supplierServiceYears();
}