package cn.gwssi.ecloud.staffpool.core.dao;
import java.util.Collection;

import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloud.staffpool.dto.UompAccountApplyPersonDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompAccountApplyPersonMapper extends BaseDao<String, UompAccountApplyPerson> {
    int insertSelective(UompAccountApplyPerson record);

    int updateByOrguserIdAccountNumId(@Param("orgUserId") String orgUserId, @Param("accountNum") String accountNum, @Param("id") String id);

    int upDateAuthorizationStatusByIds(@Param("idList") List<String> idList);

    UompAccountApplyPerson selectApplyInfoById(QueryFilter queryFilter);

    List<UompAccountApplyPersonDTO> selectUserInfoByInstId(@Param("instId") String instId);

    List<UompAccountApplyPersonDTO> selectAllByIds(@Param("idList") List<String> idList);

    BaseDTO selectSystemByUserId(@Param("userId") String userId);

    UompAccountApplyPerson selectOneByPersonId(@Param("personId")String personId);

    List<UompAccountApplyPerson> selectAllByApplyIdIn(@Param("applyIdCollection")Collection<String> applyIdCollection);


    UompAccountApplyPerson selectInfoByPersonId(@Param("userId") String userId);
}