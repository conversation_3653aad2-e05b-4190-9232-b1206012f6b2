package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "uomp_person_technology")
@Data
public class UompPersonTechnology extends BaseModel {
    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value = "人员id")
    private String personId;

    @ApiModelProperty(value = "获取时间")
    @NotEmpty(message = "获取时间不能为空")
    private String getTime;

    @ApiModelProperty(value = "资质名称")
    @NotEmpty(message = "资质名称不能为空")
    private String qualiftyName;

    @ApiModelProperty(value = "资质类型")
    @NotEmpty(message = "资质类型不能为空")
    private String qualiftyType;

    @ApiModelProperty(value = "颁证机构")
    @NotEmpty(message = "颁证机构不能为空")
    private String certificationBody;

    @ApiModelProperty(value = "")
    private String fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    @ApiModelProperty(value = "")
    private String startTime;

    @ApiModelProperty(value = "")
    private String endTime;

    private static final long serialVersionUID = 1L;
}