package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务报告文件表
 */
@ApiModel(value = "服务报告文件表", description = "服务报告文件表")
@Data
public class UompReportFile extends BaseModel {

    /**
     * 应用系统id
     */
    @ApiModelProperty(value = "应用系统id")
    private String reportId;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private String uploadTime;

    /**
     * 上传人id
     */
    @ApiModelProperty(value = "上传人id")
    private String uploaderId;

    /**
     * 上传人
     */
    @ApiModelProperty(value = "上传人")
    private String uploaderName;

    /**
     * 创建机构
     */
    @ApiModelProperty(value = "创建机构")
    private String createOrgId;

    /**
     * 更新机构
     */
    @ApiModelProperty(value = "更新机构")
    private String updateOrgId;

    /**
     * 删除标识（0:正常  1：已删除）
     */
    @ApiModelProperty(value = "删除标识（0:正常  1：已删除）")
    private String delFlag;
}