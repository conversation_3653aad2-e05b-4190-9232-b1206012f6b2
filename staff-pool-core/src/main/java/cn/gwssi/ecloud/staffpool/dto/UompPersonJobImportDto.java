package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonJobImportDto{
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "起始时间")
    private String jobBeginTime;

    @ExcelProperty(value = "终止时间")
    private String jobEndTime;

    @ExcelProperty(value = "就职公司")
    private String companyName;

    @ExcelProperty(value = "工作职位")
    private String jobPosition;

    @ExcelProperty(value = "工作描述")
    private String jobDescribe;
}