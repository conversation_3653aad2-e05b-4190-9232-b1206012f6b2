package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonInfo;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompPersonInfoMapper extends BaseDao<String, UompPersonInfo> {

    int insertSelective(UompPersonInfo record);

    int updateByPrimaryKeySelective(UompPersonInfo record);

    int updateByIds(@Param("orgId") String orgId, @Param("ids") String[] ids);

    UompPersonInfoDTO selectInfoOneByPersonId(QueryFilter queryFilter);

    BaseInstDTO selectTaskIdByInstId(@Param("instId") String instId);

    BaseDTO selectUserId(@Param("id") String id);

    List<UompBlackDto> selectBlack(String id);

    List<UompBlackDto> getBlackList(QueryFilter queryFilter);

    List<UompPersonInfoAndAccountDTO> selectNoAccountPeronList(QueryFilter queryFilter);

    /**
     * 查询非黑名单，且审核通过的人员数据，2023-12-20背调不合格的也不能申请入场
     *
     * @param ifSupplier
     * @param supplierId
     * @return
     */
    List<PersonSeletDTO> selectAllByPersonSelet(@Param("ifSupplier") String ifSupplier, @Param("supplierId") String supplierId);

    /**
     * //2023-12-4 所有人员库中，有账号且启用状态的人员，(抛出所选人员)
     * //2023-12-25 且是入场状态的
     *
     * @param admissionPersonId
     * @return
     */
    List<AcceptPersonSelectDTO> selectAllByAdmissionPersonId(@Param("admissionPersonId") String admissionPersonId);

    String selectAccountByPersonCard(String personCard);

    List<KeyWordNumDTO> countByBackgroundStatus();

    List<KeyWordNumDTO> countByTrialStatus();

    List<String> selectPersonBirthdayByTrialStatus();

    Integer countByWorkingCompanyIdAndTechnicalDirection(@Param("id") String id, @Param("key") String key);

    List<UompPersonInfo> selectPersonByWorkingCompany(@Param("id") String id);

    Integer countByOrg(String orgGroupId);

    List<UompPersonInfo> selectAllByOrg(String orgGroupId);

    List<UompPersonInfo> selectAllByOrgStatusAll(String orgGroupId);

    UompPersonInfo selectAllByPersonCardAndTrialStatus(String personCard);

    String selectIdAndAccountByPersonNameAndPersonCard(@Param("personName") String personName, @Param("personCard") String personCard);

    UompPersonInfo selectOneByPersonNameAndPersonCard(@Param("personName") String personName, @Param("personCard") String personCard);

    List<KeyWordNumDTO> countPassPersonByBackgroundStatus();

    List<KeyWordNumDTO> countPassPersonByBackgroundStatusAndWorkingCompanyId(String workingCompanyId);

    List<PersonCompanyDTO> getWorkingCompanyTotalAndPass();

    List<WorkAndPersonDTO> getWorkingCompanyNoPass();

    List<KeyWordNumDTO> countByEdu();

    List<String> countByEntry();

    String getOrgUserIdByUserId(@Param("userId")String userId);

    /**
     * 查询指定机构下的用户列表
     * @param orgGroupIdList 机构 id List
     * @return
     */
    List<UompPersonInfo> selectAllByOrgGroupId(@Param("orgGroupIdList") List<String> orgGroupIdList, @Param("technicalDirection") String technicalDirection);

    List<PercentageDTO> getPersonInTime(@Param("workingCompanyId") String workingCompanyId, @Param("workingCompany") String workingCompany);

    List<AccountPersonDTO> getAccountPassList(QueryFilter queryFilter);

    int updateEntryStatusByPersonCard(@Param("updatedEntryStatus")String updatedEntryStatus,@Param("personCard")String personCard);

    UompPersonInfo selectOneByIdOrPersonCard(@Param("id")String id,@Param("personCard")String personCard);

    int updateEntryStatusByIdOrPersonCard(@Param("updatedEntryStatus")String updatedEntryStatus,@Param("id")String id,@Param("personCard")String personCard);

    List<UompPersonContactsDTO> selectContacts(QueryFilter queryFilter);

    /**
     * 查询相应状态下的人员数量
     * @param trialStatus 初审状态
     * @param blacklist 黑名单标识
     * @param backgroundStatus 背景调查状态
     * @param dimission 是否离职
     * @param entryStatus 驻场服务状态
     * @return
     */
    int countByTrialStatusAndBlacklistAndBackgroundStatusNotAndDimissionAndEntryStatus(@Param("trialStatus")String trialStatus, @Param("blacklist")String blacklist, @Param("backgroundStatus")String backgroundStatus, @Param("dimission")String dimission, @Param("entryStatus")String entryStatus);

    UompPersonInfo selectAllByUserId(@Param("userId")String userId);

    /**
     * 更新身份证加密信息
     * @param id
     */
    void updatePersonCardById(@Param("id")String id,@Param("personCard")String personCard);
}
