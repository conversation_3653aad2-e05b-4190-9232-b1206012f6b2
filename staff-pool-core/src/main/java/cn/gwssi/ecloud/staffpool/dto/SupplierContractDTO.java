package cn.gwssi.ecloud.staffpool.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "服务商合同top5")
public class SupplierContractDTO {
   @ApiModelProperty(value = "合同id")
   private String id;
   @ApiModelProperty(value = "合同名称")
   private String name;
   @ApiModelProperty(value = "服务商id")
   private String supplierId;
   @ApiModelProperty(value = "服务商名称")
   private String supplierName;
   @ApiModelProperty(value = "最新合同质保结束时间")
   private Date qualityEndDay;
   @ApiModelProperty(value = "合同剩余天数")
   private long day;
}