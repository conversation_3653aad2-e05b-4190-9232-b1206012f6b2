package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;

/**
 * 应用系统信息表
 */
@ApiModel(value = "应用系统信息表", description = "应用系统信息表")
@Data
public class UompApplicationSystemManagement extends BaseModel {

    /**
     * 应用系统名称
     */
    @ApiModelProperty(value = "应用系统名称")
    private String applicationSystemName;

    /**
     * 运维部门id
     */
    @ApiModelProperty(value = "运维部门id")
    private String departId;

    /**
     * 运维部门名称
     */
    @ApiModelProperty(value = "运维部门名称")
    private String departName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;

    /**
     * 是否核心应用（1：是   0：否）
     */
    @ApiModelProperty(value = "是否核心应用（1：是   0：否）")
    private String isHeart;

    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    /**
     * 状态（0：使用中  1已停用）
     */
    @ApiModelProperty(value = "状态（0：使用中  1已停用）")
    private String systemStatus;

    /**
     * 服务商id
     */
    @ApiModelProperty(value = "服务商id")
    private String supplierId;

    /**
     * 服务商名称
     */
    @ApiModelProperty(value = "服务商名称")
    private String supplierName;

    /**
     * 服务商负责人
     */
    @ApiModelProperty(value = "服务商负责人")
    private String supplierDepartName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String supplierTel;

    /**
     * 创建机构
     */
    @ApiModelProperty(value = "创建机构")
    private String createOrgId;

    /**
     * 更新机构
     */
    @ApiModelProperty(value = "更新机构")
    private String updateOrgId;

    /**
     * 删除标识（0:正常  1：已删除）
     */
    @ApiModelProperty(value = "删除标识（0:正常  1：已删除）")
    private String delFlag;
}