package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.Manager;
import java.util.List;
import java.util.Map;

public interface UompSupplierManagementService extends Manager<String, UompSupplierManagement> {

    int insertSelective(UompSupplierManagement record);

    int updateById(UompSupplierManagement record);

    ResponsePageData<UompSupplierManagement> getList(String supplierName, String shortName, String supplierStatus, String respName, String entryTimeBegin, String entryTimeEnd, Map<String, String> sortMap, Integer pageNo, Integer pageSize);

    int deleteById(String id);

    // 根据机构id查询供应商
    String selectSupplierIdByGroupId(String orgId);

    List<UompSupplierManagement> getSupplierCooperationList();

    List<UompProjectSupplierListDTO> pageSupplierToProject(QueryFilter queryFilter);

    GroupBySupplierDTO groupBySupplier();

    GroupBySupplierByGXDTO groupBySupplierByGX();

    PageResult<UompSupplierManagementListDialog> getListDialog(QueryFilter queryFilter);

    List<SupplierManagementDTO> selectIdAndSupplierName();

    List<ListAndNameDto> supplierServiceYears();

    SupplierInfoDTO supplierInfo(String supplierId);
}
