package cn.gwssi.ecloud.staffpool.controller;

import cn.gwssi.ecloud.staffpool.api.model.CheckPersonProjectVO;
import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.ProjectBySupplierVO;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionApplication;
import cn.gwssi.ecloud.staffpool.core.manager.*;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.ConversionUtil;
import cn.gwssi.ecloud.staffpool.util.DesRuleUtil;
import cn.gwssi.ecloud.staffpool.util.DictUtil;
import cn.gwssi.ecloudbpm.wf.core.manager.TaskIdentityLinkManager;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Api(description = "现场管理驻场管理")
@Slf4j
@RestController
@RequestMapping("/module/staffpool/entryApply")
public class UompEntryApplyController extends BaseController<UompAdmissionApplication> {

    @Resource
    private UompAdmissionApplicationService uompAdmissionApplicationService;
    @Resource
    private UompExitApplicationService uompExitApplicationService;
    @Resource
    private UompPersonInfoService ucompPersonInfoService;
    @Resource
    private UompAdmissionPersonService uompAdmissionPersonService;
    @Resource
    private OrgUserService orgUserService;
    @Resource
    private TaskIdentityLinkManager taskIdentityLinkManager;
    @Resource
    private BpmTaskService bpmTaskService;
    @Resource
    private UompExitAdmissionRelationService uompExitAdmissionRelationService;
    @Resource
    private UompExitAcceptInfoService uompExitAcceptInfoService;
    @Resource
    private UompTeamService uompTeamService;
    @Resource
    private UompApplicationSystemManagementService uomApplicationSystemManagementService;
    @Resource
    private UompAccountApplyPersonService uompAccountApplyPersonService;
    @Resource
    private UompPersonInfoService uompPersonInfoService;
    @Resource
    private DesRuleUtil desRuleUtil;
    @Resource
    private ConversionUtil conversionUtil;
    @Resource
    private DictUtil dictUtil;

    @Override
    protected String getModelDesc() {
        return "现场管理驻场管理";
    }

    @ApiOperation(value = "驻场人员列表查询接口")
    @PostMapping(value = "/getPersonList")
    public ResponsePageData<PersonListDTO> getPersonList(@RequestBody PersonListQueryVO personListQueryVO) {
        return uompAdmissionApplicationService.getPersonList(personListQueryVO);
    }

    @ApiOperation(value = "根据id查询入场申请详情")
    @RequestMapping(value = "/getEntryApplyById", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<EntryApplyDTO> getEntryApplyById(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(uompAdmissionApplicationService.getEntryApplyById(id));
    }

    @ApiOperation(value = "根据入场人员id查询退场申请详情")
    @RequestMapping(value = "/getExitApply", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<ExitApplyDTO> getExitApply(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(uompExitApplicationService.getExitApply(id));
    }

    @ApiOperation(value = "驻场人员导出接口")
    @PostMapping(value = "/export")
    public HttpServletResponse export(HttpServletResponse response, @RequestBody PersonListQueryVO personListQueryVO) throws IOException, ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");

        List<PersonListDTO> personListDTOList = uompAdmissionApplicationService.export(personListQueryVO);


        //导出数据
        // 第一个对象，新建一个工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        // 设置第一个sheet的名称
        Sheet sheet = workbook.createSheet("驻场人员列表");

        // 开始添加excel第一行表头（excel中下标是0）
        Row row = sheet.createRow(0);
        sheet.setDefaultColumnWidth(16);//宽

        row.setHeightInPoints(20);//行高

        String[] fieldList = {"序号", "姓名", "身份证号", "联系方式", "就职公司", "运维组织", "技术方向", "驻场服务状态", "驻场服务地点", "进驻日期", "退场日期"};
        int i = 0;
        // 添加excel第一行表头信息（你想要添加的表头数据，集合类型，遍历放进去）
        for (String it : fieldList) {
            // 创建一个单元格
            Cell cell = row.createCell(i);
            // 设置单元格的样式
            CellStyle cellStyle = workbook.createCellStyle();
            //设置字体
            Font font = workbook.createFont();
            //设置字号
            font.setFontHeightInPoints((short) 14);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
            // 将数据放入excel的单元格中
            cell.setCellValue(it);
            i++;
        }


        // 开始创建excel单元格数据，从第二行开始（excel下标是1）
        int rowNum = 1;
        // 添加excel行数据的集合（你自己的数据集合遍历）
        for (PersonListDTO it : personListDTOList) {
            // 创建一个单元格
            Row row1 = sheet.createRow(rowNum);
            // 设置行的高度
            row1.setHeightInPoints(16);
            //填写单元格
            row1.createCell(0).setCellValue(rowNum + "");//序号
            row1.createCell(1).setCellValue(it.getPersonName() == null ? "" : it.getPersonName());//姓名
            row1.createCell(2).setCellValue(it.getPersonCard() == null ? "" : it.getPersonCard());//身份证号
            row1.createCell(3).setCellValue(it.getTel() == null ? "" : it.getTel());//联系方式
            row1.createCell(4).setCellValue(it.getWorkingCompany() == null ? "" : it.getWorkingCompany());//就职公司
            row1.createCell(5).setCellValue(it.getMaintenanceGroupName() == null ? "" : it.getMaintenanceGroupName());//运维组
            row1.createCell(6).setCellValue(it.getTechnicalDirection() == null ? "" : dictUtil.getNameByKey("UOMP_TEC_DIRECTION", it.getTechnicalDirection()));//技术方向
            row1.createCell(7).setCellValue(it.getEntryStatus() == null ? "" : dictUtil.getNameByKey("ENTRY_STATUS", it.getEntryStatus()));//驻场服务状态
            row1.createCell(8).setCellValue(it.getServiceLocation() == null ? "" : dictUtil.getNameByKey("UOMP_SERVICE_LOCATION", it.getServiceLocation()));//驻场服务地点
            row1.createCell(9).setCellValue(it.getInTime() == null ? "" : ymd.format(sdf.parse(it.getInTime())));//入驻时间
            row1.createCell(10).setCellValue(it.getOutTime() == null ? "" : ymd.format(sdf.parse(it.getOutTime())));//退出时间

            rowNum++;
        }

        ServletOutputStream os = null;
        try {
            //把文件送到用户端
            String downName = "驻场人员表.xlsx";
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(downName, "utf-8"));
            os = response.getOutputStream();
            workbook.write(os);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //释放资源
            if (os != null) {
                os.flush();
                os.close();
            }
            workbook.close();
        }
        return response;
    }

    @ApiOperation(value = "驻场人员导入接口")
    @PostMapping(value = "/upload")
    public ResultMsg<String> upload(@RequestParam(value = "file") MultipartFile file) throws Exception {
        return uompAdmissionPersonService.upload(file);
    }

    @ApiOperation(value = "入场申请人员信息下拉选择接口")
    @GetMapping(value = "/getPersonSeletList")
    public ResultMsg<List<PersonSeletDTO>> getPersonSeletList() {
        return ResultMsg.SUCCESS(ucompPersonInfoService.getPersonSeletList());
    }

    @ApiOperation(value = "退场申请人员下拉选择接口")
    @GetMapping(value = "/getEntryPersonSelectList")
    public ResultMsg<List<EntryPersonSelectDTO>> getEntryPersonSelectList() {
        return ResultMsg.SUCCESS(uompAdmissionPersonService.getEntryPersonSelectList());
    }

    @ApiOperation(value = "根据退场申请id修改关联的驻场人员的退场时间")
    @PostMapping(value = "/updateOutTimeByExitId")
    public ResultMsg<String> updateOutTimeByExitId(@RequestParam(value = "exitId") String exitId) {
        uompExitAdmissionRelationService.updateOutTimeByExitId(exitId, new Date());
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "通过退场申请id查出相关接收人信息")
    @PostMapping(value = "/getAccountId")
    public ResultMsg<Set<DefaultIdentity>> getAccountId(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(uompExitAcceptInfoService.selectInfoById(id));
    }

    @ApiOperation(value = "查询有效状态的业务组下拉框")
    @PostMapping(value = "/getBusinessList")
    public ResultMsg<List<BaseDTO>> getBusinessList() {
        return ResultMsg.SUCCESS(uompTeamService.selectAllByStatus());
    }

    @ApiOperation(value = "退场接收人员下拉选择接口")
    @RequestMapping(value = "/getAcceptPersonSelectList", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<List<Map<String, String>>> getAcceptPersonSelectList(@RequestParam(value = "id") String id) {
        return ResultMsg.SUCCESS(ucompPersonInfoService.getAcceptPersonSelectList(id));
    }

    @ApiOperation(value = "入场申请-校验入场人员是否反复参与同一个系统")
    @PostMapping(value = "/checkPersonProject")
    public ResultMsg<String> checkPersonProject(@RequestBody CheckPersonProjectVO checkPersonProjectVO) {
        return uompAdmissionPersonService.checkPersonProject(checkPersonProjectVO);
    }

    @ApiOperation(value = "退场申请-通过入场人员id查出账号并停用")
    @RequestMapping(value = "/deactivateAccount", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> deactivateAccount(@RequestParam(value = "personCard") String personCard) {
        ucompPersonInfoService.deactivateAccount(personCard);
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "退场申请-通过身份证号查询人员角色角色权限等信息")
    @RequestMapping(value = "/getPersonInfoByCard", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<PersonInfoDTO> getPersonInfoByCard(@RequestParam(value = "personCard") String personCard) {
        return ResultMsg.SUCCESS(ucompPersonInfoService.getPersonInfoByCard(personCard));
    }

    @ApiOperation(value = "退场申请-删除角色和岗位")
    @PostMapping(value = "/deleteByPersonCard")
    public ResultMsg<String> deleteByPersonCard(@RequestParam(value = "id") String id, @RequestParam(value = "personCard") String personCard) {
        ucompPersonInfoService.deleteByPersonCard(id, personCard);
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "退场申请-插入退场和申请入场人员关系表数据")
    @RequestMapping(value = "/insertExitRelation" , method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> insertExitRelation(@RequestParam(value = "exitId") String exitId, @RequestParam(value = "personCard") String personCard) {
        uompAdmissionPersonService.insertExitRelation(exitId, personCard);
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "退场申请-退场申请校验")
    @RequestMapping(value = "/checkExit", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> checkExit(@RequestParam(value = "personCard") String personCard) {
        return uompAdmissionPersonService.checkExit(personCard);
    }

    @ApiOperation(value = "入场申请-入场申请校验")
    @RequestMapping(value = "/checkEntry", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> checkEntry(@RequestParam(value = "personCard") String personCard) {
        int count = uompAdmissionPersonService.countByPersonCard(personCard);
        if (count > 0) {
            return ResultMsg.ERROR("该人员存在尚未完成的退场申请流程，暂时不可申请入场！");
        }
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "退场申请-退场待办校验")
    @RequestMapping(value = "/checkTodoTask", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> checkTodoTask(@RequestParam(value = "personCard") String personCard) {
        String userId = orgUserService.selectIdByPersonCard(personCard);

        Set<String> userRights = taskIdentityLinkManager.getUserRights(userId);

        int count = bpmTaskService.countBySelective(new ArrayList<>(userRights), userId);

        if (count > 0) {
            return ResultMsg.ERROR("该人员有未完成的待办，暂时不可完成退场申请");
        }

        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "系统多选-入场申请: 入场申请-根据供应商收敛查询系统列表")
    @PostMapping(value = "/getProjectBySupplier")
    public ResponsePageData<ProjectBySupplierDTO> getProjectBySupplier(@RequestBody ProjectBySupplierVO projectBySupplier) {
        return uompAdmissionApplicationService.getProjectBySupplier(projectBySupplier);
    }

    @ApiOperation(value = "入场申请-更新入场申请状态")
    @RequestMapping(value = "/updateStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> updateStatus(@RequestParam(value = "id") String id, @RequestParam(value = "status") String status) {
        uompAdmissionApplicationService.updateStatus(id, status);
        uompAdmissionPersonService.updateStatus(id, status);
        return ResultMsg.SUCCESS();
    }

    @ApiOperation(value = "人员信息总览")
    @PostMapping(value = "/personInfoOverview")
    public ResponsePageData<PersonInfoOverviewDTO> personInfoOverview(@RequestBody PersonListQueryVO vo) {
        if (StringUtils.isEmpty(vo.getId())) {
            ResponsePageData pageResult = new ResponsePageData();
            pageResult.setCode(500);
            pageResult.setMsg("申请id 为必填项");
            return pageResult;
        }
        return uompAdmissionApplicationService.personInfoOverview(vo);
    }

    @ApiOperation(value = "参与系统")
    @GetMapping(value = "/getInvolvedProject")
    public ResultMsg<List<ProjectBySupplierDTO>> getInvolvedProject() {
        return ResultMsg.SUCCESS(uomApplicationSystemManagementService.selectIdAndNameByAll());
    }

    @ApiOperation(value = "查询用户账号状态")
    @PostMapping(value = "/getPersonAccountStatus")
    public ResultMsg<UompAccountApplyPerson> getPersonAccountStatus(@RequestParam(value = "personId") String personId) {
        return ResultMsg.SUCCESS(uompAccountApplyPersonService.getPersonAccountStatus(personId));
    }


    @ApiOperation(value = "更新账号状态")
    @PostMapping(value = "/updateAccountById")
    public ResultMsg<String> updateAccountByPersonId(@RequestParam("id") @ApiParam(value = "行id") String id,
                                                     @RequestParam("accountNum") @ApiParam(value = "账号") String accountNum) {
        return ResultMsg.SUCCESS(uompPersonInfoService.updateAccountByPersonId(id, accountNum));
    }

    @ApiOperation(value = "授权接口")
    @RequestMapping(value = "/empower", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> empower(@RequestParam("ids") @ApiParam(value = "行id集合") String ids) {
        // 通过入场申请id 查询账号申请记录
        List<UompAccountApplyPerson> uompAccountApplyPersonList = uompAccountApplyPersonService.selectAllByApplyIds(ids);
        if (!CollectionUtils.isEmpty(uompAccountApplyPersonList)) {
            uompPersonInfoService.empower(uompAccountApplyPersonList.stream().map(UompAccountApplyPerson::getId).collect(Collectors.joining(",")));
        }
        return getSuccessResult();
    }

    @ApiOperation(value = "自动退场")
    @RequestMapping(value = "/exitAdmissionPerson", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<Boolean> exitAdmissionPerson(@RequestParam(value = "personId", required = false) @ApiParam(value = "用户id") String personId,
                                         @RequestParam(value = "personCard", required = false) @ApiParam(value = "身份证号") String personCard,
                                         @RequestParam("outTime") @ApiParam(value = "退场时间") String outTime) {
        return getSuccessResult(uompAdmissionPersonService.exitAdmissionPerson(personId, personCard, outTime));
    }

    @ApiOperation(value = "根据id查询入场申请详情")
    @RequestMapping(value = "/decryptEntryInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public ResultMsg<String> decryptEntryInfo(@RequestParam(value = "id") String id,
                                                     @RequestParam("attr")String attr) {
        return ResultMsg.SUCCESS(uompAdmissionApplicationService.decryptEntryInfo(id,attr));
    }
}
