package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.core.dao.UompFortressMapper;
import cn.gwssi.ecloud.staffpool.dto.UompFortessDTO;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.gwssi.ecloud.staffpool.core.entity.UompFortress;
import cn.gwssi.ecloud.staffpool.core.manager.UompFortressService;
@Service
public class UompFortressServiceImpl extends BaseManager<String, UompFortress> implements UompFortressService{

    @Autowired
    private UompFortressMapper uompFortressMapper;

    @Override
    public UompFortessDTO selectInfoById(String fort_id) {
        return uompFortressMapper.selectInfoById(fort_id);
    }
}
