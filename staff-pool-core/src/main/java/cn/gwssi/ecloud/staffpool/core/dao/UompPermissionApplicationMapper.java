package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompPermissionApplication;
import cn.gwssi.ecloud.staffpool.dto.UompPermissionListDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;

import java.util.List;

public interface UompPermissionApplicationMapper extends BaseDao<String, UompPermissionApplication> {
    int insertSelective(UompPermissionApplication record);

    List<UompPermissionListDTO> getList(QueryFilter queryFilter);
}