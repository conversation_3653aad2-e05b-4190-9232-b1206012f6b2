package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import cn.gwssi.ecloud.staffpool.core.entity.UompReportFile;
import cn.gwssi.ecloud.staffpool.core.dao.UompReportFileMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompReportFileService;
@Service
public class UompReportFileServiceImpl extends BaseManager<String, UompReportFile> implements UompReportFileService{

    @Resource
    private UompReportFileMapper uompReportFileMapper;

    @Override
    public int insertSelective(UompReportFile record) {
        return uompReportFileMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompReportFile record) {
        return uompReportFileMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateDelFlag(UompReportFile uompReportFile) {
        return uompReportFileMapper.updateDelFlag(uompReportFile);
    }

}
