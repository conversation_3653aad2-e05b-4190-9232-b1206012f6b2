package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.UompSupplierQualificationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierQualificationService;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierQualificationDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UompSupplierQualificationServiceImpl extends BaseManager<String, UompSupplierQualification> implements UompSupplierQualificationService {
    @Autowired
    private UompSupplierQualificationMapper uompSupplierQualificationMapper;

    @Override
    public int insertSelective(UompSupplierQualification record) {
        return uompSupplierQualificationMapper.insertSelective(record);
    }

    @Override
    public ResponsePageData<UompSupplierQualificationDto> getQualificationList(String supplierManagementId, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(
                pageNo != null ? pageNo : 1,
                pageSize != null ? pageSize : 10);
        List<UompSupplierQualificationDto> supplierManagements = uompSupplierQualificationMapper.getQualificationList(supplierManagementId);
        PageInfo<UompSupplierQualificationDto> p = new PageInfo<>(supplierManagements);
        ResponsePageData<UompSupplierQualificationDto> responsePageData = new ResponsePageData<>();
        responsePageData.setCode(200);
        responsePageData.setMsg("success");
        responsePageData.setTotal(p.getTotal());
        responsePageData.setRows(supplierManagements);
        return responsePageData;
    }

    @Override
    public int updateById(UompSupplierQualification qualification) {
        return uompSupplierQualificationMapper.updateById(qualification);
    }

    @Override
    public int deleteById(String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierQualification qualification = new UompSupplierQualification();
        qualification.setId(id);
        qualification.setDelFlag("1");
        qualification.setUpdateOrgId(user.getOrgId());
        qualification.setUpdateTime(new Date());
        qualification.setUpdateBy(user.getUserId());
        return uompSupplierQualificationMapper.updateById(qualification);
    }

}
