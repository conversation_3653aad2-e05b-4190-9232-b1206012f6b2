package cn.gwssi.ecloud.staffpool.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class UompPersonSocialImportDto {
    @ExcelProperty(value = "姓名")
    private String personName;
    @ExcelProperty(value = "身份证号")
    private String personCard;
    @ExcelProperty(value = "关系人姓名")
    private String relaName;

    @ExcelProperty(value = "年龄")
    private String relaAge;

    @ExcelProperty(value = "国籍")
    private String national;

    @ExcelProperty(value = "政治面貌")
    private String politicsStatus;

    @ExcelProperty(value = "与本人关系")
    private String relationWithMyself;

    @ExcelProperty(value = "联系方式")
    private String relaTel;

    @ExcelProperty(value = "居住地")
    private String relaAddress;

    @ExcelProperty(value = "工作单位及职务")
    private String relaPost;
}