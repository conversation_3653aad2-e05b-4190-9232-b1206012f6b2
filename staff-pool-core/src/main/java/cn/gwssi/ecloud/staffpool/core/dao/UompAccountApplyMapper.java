package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApply;
import cn.gwssi.ecloud.staffpool.dto.UompAccountApplyDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompAccountApplyMapper extends BaseDao<String, UompAccountApply> {
    int insertSelective(UompAccountApply record);

    List<UompAccountApplyDTO> accountNumList(QueryFilter queryFilter);

    UompAccountApply selectInfoOneByPersonId(@Param("personId") String personId);
}