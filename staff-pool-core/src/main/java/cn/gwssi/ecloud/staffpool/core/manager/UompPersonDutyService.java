package cn.gwssi.ecloud.staffpool.core.manager;


import cn.gwssi.ecloud.staffpool.core.entity.UompPersonDuty;
import cn.gwssi.ecloud.staffpool.dto.UompPersonDutyDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

/**
 * 人员值班表(UompPersonDuty)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-29 17:25:49
 */
public interface UompPersonDutyService extends Manager<String, UompPersonDuty> {


    List<UompPersonDutyDto> getDutyList(String startDate, String endDate, String[] personIds, Integer pageNo, Integer pageSize, List<String> createBys);

    Integer getDutyListTotal(String startDate, String endDate, String[] personIds, List<String> createBys);

    List<UompPersonDutyDto> selectByDateAndPersonId(String[] dates, String[] personIds);

    int insertSelective(UompPersonDuty uompPersonDuty);
    int updateByPrimaryKeySelective(UompPersonDuty uompPersonDuty);
}

