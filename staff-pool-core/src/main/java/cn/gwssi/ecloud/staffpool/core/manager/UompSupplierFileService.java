package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile;
import cn.gwssi.ecloud.staffpool.dto.FilePatentDto;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierFileDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompSupplierFileService extends Manager<String, UompSupplierFile> {

    ResponsePageData<UompSupplierFileDto> getFileList(String supplierManagementId, String fileName, Integer pageNo, Integer pageSize);

    int deleteById(String id);

    int updateBySupplierManagementId(String supplierManagementId);

    int insertSelective(UompSupplierFile record);

    List<FilePatentDto> getFileListPatent(String supplierManagementId);
}
