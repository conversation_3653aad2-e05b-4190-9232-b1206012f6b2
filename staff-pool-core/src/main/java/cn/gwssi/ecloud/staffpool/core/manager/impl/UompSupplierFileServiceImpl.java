package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.dao.SysDataDictMapper;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierFileService;
import cn.gwssi.ecloud.staffpool.dto.FilePatentDto;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierFileDto;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.gwssi.ecloud.staffpool.core.dao.UompSupplierFileMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile;

import java.util.Date;
import java.util.List;

@Service
public class UompSupplierFileServiceImpl extends BaseManager<String, UompSupplierFile> implements UompSupplierFileService {

    @Autowired
    private UompSupplierFileMapper uompSupplierFileMapper;
    @Autowired
    private SysDataDictMapper dataDictMapper;

    @Override
    public int insertSelective(UompSupplierFile record) {
        return uompSupplierFileMapper.insertSelective(record);
    }

    @Override
    public ResponsePageData<UompSupplierFileDto> getFileList(String supplierManagementId, String fileName, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(
                pageNo != null ? pageNo : 1,
                pageSize != null ? pageSize : 10);
        uompSupplierFileMapper.getFileList(supplierManagementId, fileName);
        List<UompSupplierFileDto> supplierManagements = uompSupplierFileMapper.getFileList(supplierManagementId, fileName);
        PageInfo<UompSupplierFileDto> p = new PageInfo<>(supplierManagements);
        ResponsePageData<UompSupplierFileDto> responsePageData = new ResponsePageData<>();
        responsePageData.setCode(200);
        responsePageData.setMsg("success");
        responsePageData.setTotal(p.getTotal());
        responsePageData.setRows(supplierManagements);
        return responsePageData;
    }

    @Override
    public int deleteById(String id) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierFile file = new UompSupplierFile();
        file.setId(id);
        file.setDelFlag("1");
        file.setUpdateOrgId(user.getOrgId());
        file.setUpdateTime(new Date());
        file.setUpdateBy(user.getUserId());
        return uompSupplierFileMapper.updateById(file);
    }

    @Override
    public int updateBySupplierManagementId(String supplierManagementId) {
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();

        UompSupplierFile file = new UompSupplierFile();
        file.setSupplierManagementId(supplierManagementId);
        file.setDelFlag("1");
        file.setUpdateOrgId(user.getOrgId());
        file.setUpdateTime(new Date());
        file.setUpdateBy(user.getUserId());
        return uompSupplierFileMapper.updateBySupplierManagementId(file);
    }

    @Override
    public List<FilePatentDto> getFileListPatent(String supplierManagementId) {
        List<FilePatentDto> filePatentDtos = dataDictMapper.selectPatent();
        for (FilePatentDto patentDto : filePatentDtos) {
            patentDto.setFileList(uompSupplierFileMapper.getFileListPatent(supplierManagementId, patentDto.getFILE_TYPE()));
        }
        return filePatentDtos;
    }

}
