package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="uomp_person_educational")
@Data
public class UompPersonEducational extends BaseModel {
    @ApiModelProperty(value="")
    private String personId;

    @ApiModelProperty(value="")
    private String educationBeginTime;

    @ApiModelProperty(value="")
    private String educationEndTime;

    @ApiModelProperty(value="")
    private String school;

    @ApiModelProperty(value="")
    private String major;

    @ApiModelProperty(value="")
    private String educationBackground;

    @ApiModelProperty(value="")
    private String fileInfo;

    @ApiModelProperty(value="")
    private String createOrgId;

    @ApiModelProperty(value="")
    private String updateOrgId;

    @ApiModelProperty(value="")
    private String delFlag;

    @ApiModelProperty(value="")
    private String educationForm;

    @ApiModelProperty(value="")
    private String certificateNum;

    private static final long serialVersionUID = 1L;
}