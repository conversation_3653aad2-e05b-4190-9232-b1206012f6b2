package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompAccountApplyPerson;
import cn.gwssi.ecloud.staffpool.dto.BaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompAccountApplyPersonService extends Manager<String, UompAccountApplyPerson> {

    UompAccountApplyPerson selectApplyInfoById(QueryFilter queryFilter);

    void upDateAuthorizationStatusByIds(List<String> idList);

    BaseDTO selectSystemByUserId(String userId);

    UompAccountApplyPerson getPersonAccountStatus(String personId);

    int insertSelective(UompAccountApplyPerson record);

    List<UompAccountApplyPerson> selectAllByApplyIds(String applyIds);

    UompAccountApplyPerson selectInfoByPersonId(String userId);
}
