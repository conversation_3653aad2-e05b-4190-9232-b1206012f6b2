package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

public class CmdbCommResource extends BaseModel {
    @ApiModelProperty(value="")
    
    private String id;

    @ApiModelProperty(value="")
    
    private String instId;

    @ApiModelProperty(value="")
    
    private String baselineId;

    @ApiModelProperty(value="")
    
    private String groupId;

    @ApiModelProperty(value="")
    
    private String modelId;

    @ApiModelProperty(value="")
    
    private String resourceNo;

    @ApiModelProperty(value="")
    
    private String resourceName;

    @ApiModelProperty(value="")
    
    private String deviceModel;

    @ApiModelProperty(value="")
    
    private String resourceState;

    @ApiModelProperty(value="")
    
    private String producer;

    @ApiModelProperty(value="")
    
    private String deviceDutyor;

    @ApiModelProperty(value="")
    
    private Date tendDateStart;

    @ApiModelProperty(value="")
    
    private Date tendDateEnd;

    @ApiModelProperty(value="")
    
    private String deviceUnitName;

    @ApiModelProperty(value="")
    
    private String deviceUnitId;

    @ApiModelProperty(value="")
    
    private String supplierId;

    @ApiModelProperty(value="")
    
    private String supplierName;

    @ApiModelProperty(value="")
    
    private String contractId;

    @ApiModelProperty(value="")
    
    private String contractName;

    @ApiModelProperty(value="")
    
    private String projectId;

    @ApiModelProperty(value="")
    
    private String projectName;

    @ApiModelProperty(value="")
    
    private String deviceState;

    @ApiModelProperty(value="")
    
    private String ipAddr;

    @ApiModelProperty(value="")
    
    private Date submitTime;

    @ApiModelProperty(value="")
    
    private String auditState;

    @ApiModelProperty(value="")
    
    private String auditResult;

    @ApiModelProperty(value="")
    
    private Date auditEndTime;

    @ApiModelProperty(value="")
    
    private String auditer;

    @ApiModelProperty(value="")
    
    private String auditerid;

    @ApiModelProperty(value="")
    
    private String ifNotice;

    @ApiModelProperty(value="")
    
    private Date noticeTime;

    @ApiModelProperty(value="")
    
    private String fileStr;

    @ApiModelProperty(value="")
    
    private String auditOpin;

    @ApiModelProperty(value="")
    
    private BigDecimal devicePrice;

    @ApiModelProperty(value="")
    
    private String otherCols;

    @ApiModelProperty(value="")
    
    private String metadata1;

    @ApiModelProperty(value="")
    
    private String metadata2;

    @ApiModelProperty(value="")
    
    private String metadata3;

    @ApiModelProperty(value="")
    
    private String metadata4;

    @ApiModelProperty(value="")
    
    private String metadata5;

    @ApiModelProperty(value="")
    
    private String extends1;

    @ApiModelProperty(value="")
    
    private String extends2;

    @ApiModelProperty(value="")
    
    private String extends3;

    @ApiModelProperty(value="")
    
    private String extends4;

    @ApiModelProperty(value="")
    
    private String extends5;

    @ApiModelProperty(value="")
    
    private String extends6;

    @ApiModelProperty(value="")
    
    private String extends7;

    @ApiModelProperty(value="")
    
    private String extends8;

    @ApiModelProperty(value="")
    
    private String extends9;

    @ApiModelProperty(value="")
    
    private String extends10;

    @ApiModelProperty(value="")
    
    private String extends11;

    @ApiModelProperty(value="")
    
    private String extends12;

    @ApiModelProperty(value="")
    
    private String extends13;

    @ApiModelProperty(value="")
    
    private String extends14;

    @ApiModelProperty(value="")
    
    private String extends15;

    @ApiModelProperty(value="")
    
    private String extends16;

    @ApiModelProperty(value="")
    
    private String extends17;

    @ApiModelProperty(value="")
    
    private String extends18;

    @ApiModelProperty(value="")
    
    private String extends19;

    @ApiModelProperty(value="")
    
    private String extends20;

    @ApiModelProperty(value="")
    
    private Date extendsDate1;

    @ApiModelProperty(value="")
    
    private Date extendsDate2;

    @ApiModelProperty(value="")
    
    private Date extendsDate3;

    @ApiModelProperty(value="")
    
    private Date extendsDate4;

    @ApiModelProperty(value="")
    
    private Date extendsDate5;

    @ApiModelProperty(value="")
    
    private Long extendsInt1;

    @ApiModelProperty(value="")
    
    private Long extendsInt2;

    @ApiModelProperty(value="")
    
    private Long extendsInt3;

    @ApiModelProperty(value="")
    
    private Long extendsInt4;

    @ApiModelProperty(value="")
    
    private Long extendsInt5;

    @ApiModelProperty(value="")
    
    private String createBy;

    @ApiModelProperty(value="")
    
    private String updateBy;

    @ApiModelProperty(value="")
    
    private Date createTime;

    @ApiModelProperty(value="")
    
    private Date updateTime;

    @ApiModelProperty(value="")
    
    private String delFlag;

    @ApiModelProperty(value="")
    
    private String macAddr;

    @ApiModelProperty(value="")
    
    private String orderNo;

    private static final long serialVersionUID = 1L;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getBaselineId() {
        return baselineId;
    }

    public void setBaselineId(String baselineId) {
        this.baselineId = baselineId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getResourceNo() {
        return resourceNo;
    }

    public void setResourceNo(String resourceNo) {
        this.resourceNo = resourceNo;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getResourceState() {
        return resourceState;
    }

    public void setResourceState(String resourceState) {
        this.resourceState = resourceState;
    }

    public String getProducer() {
        return producer;
    }

    public void setProducer(String producer) {
        this.producer = producer;
    }

    public String getDeviceDutyor() {
        return deviceDutyor;
    }

    public void setDeviceDutyor(String deviceDutyor) {
        this.deviceDutyor = deviceDutyor;
    }

    public Date getTendDateStart() {
        return tendDateStart;
    }

    public void setTendDateStart(Date tendDateStart) {
        this.tendDateStart = tendDateStart;
    }

    public Date getTendDateEnd() {
        return tendDateEnd;
    }

    public void setTendDateEnd(Date tendDateEnd) {
        this.tendDateEnd = tendDateEnd;
    }

    public String getDeviceUnitName() {
        return deviceUnitName;
    }

    public void setDeviceUnitName(String deviceUnitName) {
        this.deviceUnitName = deviceUnitName;
    }

    public String getDeviceUnitId() {
        return deviceUnitId;
    }

    public void setDeviceUnitId(String deviceUnitId) {
        this.deviceUnitId = deviceUnitId;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDeviceState() {
        return deviceState;
    }

    public void setDeviceState(String deviceState) {
        this.deviceState = deviceState;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult;
    }

    public Date getAuditEndTime() {
        return auditEndTime;
    }

    public void setAuditEndTime(Date auditEndTime) {
        this.auditEndTime = auditEndTime;
    }

    public String getAuditer() {
        return auditer;
    }

    public void setAuditer(String auditer) {
        this.auditer = auditer;
    }

    public String getAuditerid() {
        return auditerid;
    }

    public void setAuditerid(String auditerid) {
        this.auditerid = auditerid;
    }

    public String getIfNotice() {
        return ifNotice;
    }

    public void setIfNotice(String ifNotice) {
        this.ifNotice = ifNotice;
    }

    public Date getNoticeTime() {
        return noticeTime;
    }

    public void setNoticeTime(Date noticeTime) {
        this.noticeTime = noticeTime;
    }

    public String getFileStr() {
        return fileStr;
    }

    public void setFileStr(String fileStr) {
        this.fileStr = fileStr;
    }

    public String getAuditOpin() {
        return auditOpin;
    }

    public void setAuditOpin(String auditOpin) {
        this.auditOpin = auditOpin;
    }

    public BigDecimal getDevicePrice() {
        return devicePrice;
    }

    public void setDevicePrice(BigDecimal devicePrice) {
        this.devicePrice = devicePrice;
    }

    public String getOtherCols() {
        return otherCols;
    }

    public void setOtherCols(String otherCols) {
        this.otherCols = otherCols;
    }

    public String getMetadata1() {
        return metadata1;
    }

    public void setMetadata1(String metadata1) {
        this.metadata1 = metadata1;
    }

    public String getMetadata2() {
        return metadata2;
    }

    public void setMetadata2(String metadata2) {
        this.metadata2 = metadata2;
    }

    public String getMetadata3() {
        return metadata3;
    }

    public void setMetadata3(String metadata3) {
        this.metadata3 = metadata3;
    }

    public String getMetadata4() {
        return metadata4;
    }

    public void setMetadata4(String metadata4) {
        this.metadata4 = metadata4;
    }

    public String getMetadata5() {
        return metadata5;
    }

    public void setMetadata5(String metadata5) {
        this.metadata5 = metadata5;
    }

    public String getExtends1() {
        return extends1;
    }

    public void setExtends1(String extends1) {
        this.extends1 = extends1;
    }

    public String getExtends2() {
        return extends2;
    }

    public void setExtends2(String extends2) {
        this.extends2 = extends2;
    }

    public String getExtends3() {
        return extends3;
    }

    public void setExtends3(String extends3) {
        this.extends3 = extends3;
    }

    public String getExtends4() {
        return extends4;
    }

    public void setExtends4(String extends4) {
        this.extends4 = extends4;
    }

    public String getExtends5() {
        return extends5;
    }

    public void setExtends5(String extends5) {
        this.extends5 = extends5;
    }

    public String getExtends6() {
        return extends6;
    }

    public void setExtends6(String extends6) {
        this.extends6 = extends6;
    }

    public String getExtends7() {
        return extends7;
    }

    public void setExtends7(String extends7) {
        this.extends7 = extends7;
    }

    public String getExtends8() {
        return extends8;
    }

    public void setExtends8(String extends8) {
        this.extends8 = extends8;
    }

    public String getExtends9() {
        return extends9;
    }

    public void setExtends9(String extends9) {
        this.extends9 = extends9;
    }

    public String getExtends10() {
        return extends10;
    }

    public void setExtends10(String extends10) {
        this.extends10 = extends10;
    }

    public String getExtends11() {
        return extends11;
    }

    public void setExtends11(String extends11) {
        this.extends11 = extends11;
    }

    public String getExtends12() {
        return extends12;
    }

    public void setExtends12(String extends12) {
        this.extends12 = extends12;
    }

    public String getExtends13() {
        return extends13;
    }

    public void setExtends13(String extends13) {
        this.extends13 = extends13;
    }

    public String getExtends14() {
        return extends14;
    }

    public void setExtends14(String extends14) {
        this.extends14 = extends14;
    }

    public String getExtends15() {
        return extends15;
    }

    public void setExtends15(String extends15) {
        this.extends15 = extends15;
    }

    public String getExtends16() {
        return extends16;
    }

    public void setExtends16(String extends16) {
        this.extends16 = extends16;
    }

    public String getExtends17() {
        return extends17;
    }

    public void setExtends17(String extends17) {
        this.extends17 = extends17;
    }

    public String getExtends18() {
        return extends18;
    }

    public void setExtends18(String extends18) {
        this.extends18 = extends18;
    }

    public String getExtends19() {
        return extends19;
    }

    public void setExtends19(String extends19) {
        this.extends19 = extends19;
    }

    public String getExtends20() {
        return extends20;
    }

    public void setExtends20(String extends20) {
        this.extends20 = extends20;
    }

    public Date getExtendsDate1() {
        return extendsDate1;
    }

    public void setExtendsDate1(Date extendsDate1) {
        this.extendsDate1 = extendsDate1;
    }

    public Date getExtendsDate2() {
        return extendsDate2;
    }

    public void setExtendsDate2(Date extendsDate2) {
        this.extendsDate2 = extendsDate2;
    }

    public Date getExtendsDate3() {
        return extendsDate3;
    }

    public void setExtendsDate3(Date extendsDate3) {
        this.extendsDate3 = extendsDate3;
    }

    public Date getExtendsDate4() {
        return extendsDate4;
    }

    public void setExtendsDate4(Date extendsDate4) {
        this.extendsDate4 = extendsDate4;
    }

    public Date getExtendsDate5() {
        return extendsDate5;
    }

    public void setExtendsDate5(Date extendsDate5) {
        this.extendsDate5 = extendsDate5;
    }

    public Long getExtendsInt1() {
        return extendsInt1;
    }

    public void setExtendsInt1(Long extendsInt1) {
        this.extendsInt1 = extendsInt1;
    }

    public Long getExtendsInt2() {
        return extendsInt2;
    }

    public void setExtendsInt2(Long extendsInt2) {
        this.extendsInt2 = extendsInt2;
    }

    public Long getExtendsInt3() {
        return extendsInt3;
    }

    public void setExtendsInt3(Long extendsInt3) {
        this.extendsInt3 = extendsInt3;
    }

    public Long getExtendsInt4() {
        return extendsInt4;
    }

    public void setExtendsInt4(Long extendsInt4) {
        this.extendsInt4 = extendsInt4;
    }

    public Long getExtendsInt5() {
        return extendsInt5;
    }

    public void setExtendsInt5(Long extendsInt5) {
        this.extendsInt5 = extendsInt5;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getMacAddr() {
        return macAddr;
    }

    public void setMacAddr(String macAddr) {
        this.macAddr = macAddr;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}