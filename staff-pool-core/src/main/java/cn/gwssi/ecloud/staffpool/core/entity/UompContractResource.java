package cn.gwssi.ecloud.staffpool.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同关联资源表
 */
@ApiModel(value = "合同关联资源信息", description = "uomp_contract_resource")
@Data
public class UompContractResource implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 合同id
    */
    @ApiModelProperty(value="合同id")
    private String contractManagementId;

    /**
    * 资源ID
    */
    @ApiModelProperty(value="资源ID")
    private String cInstId;

    /**
    * 资源名称
    */
    @ApiModelProperty(value="资源名称")
    private String ciName;

    /**
    * 品牌
    */
    @ApiModelProperty(value="品牌")
    private String brand;

    /**
    * 型号
    */
    @ApiModelProperty(value="型号")
    private String brandModel;

    /**
    * cpu架构
    */
    @ApiModelProperty(value="cpu架构")
    private String cpuFramework;

    /**
    * 用途
    */
    @ApiModelProperty(value="用途")
    private String useds;

    /**
    * 操作系统
    */
    @ApiModelProperty(value="操作系统")
    private String os;

    /**
    * 操作系统版本
    */
    @ApiModelProperty(value="操作系统版本")
    private String osVersion;

    /**
    * 所属机房
    */
    @ApiModelProperty(value="所属机房")
    private String machineRoom;

    /**
    * 所属机柜
    */
    @ApiModelProperty(value="所属机柜")
    private String cabinet;

    private static final long serialVersionUID = 1L;
}