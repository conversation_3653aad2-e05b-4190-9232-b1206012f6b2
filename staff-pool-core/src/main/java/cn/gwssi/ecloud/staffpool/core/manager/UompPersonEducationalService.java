package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonEducational;
import cn.gwssi.ecloud.staffpool.dto.UompPersonEducationalDto;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;

public interface UompPersonEducationalService extends Manager<String, UompPersonEducational> {
    int insertSelective(UompPersonEducational record);

    int updateByPrimaryKeySelective(UompPersonEducational record);

    int updateByPersonId(UompPersonEducational record);

    int updateByPersonIds(String orgId, String[] personIds);

    List<UompPersonEducationalDto> selectByPersonIds(List<String> personIds);

    void deleteByPersonId(String personId);
}
