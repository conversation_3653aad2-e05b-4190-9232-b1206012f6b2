package cn.gwssi.ecloud.staffpool.util;

import cn.gwssi.ecloud.staffpool.core.dao.OrgRelationMapper;
import cn.gwssi.ecloud.staffpool.core.dao.UompDesensitizationMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompDesensitization;
import cn.gwssi.ecloud.staffpool.dto.OrgPostBaseDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.org.api.model.IUserRole;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DesRuleUtil {

    @Resource
    private UompDesensitizationMapper uompDesensitizationMapper;
    @Resource
    private OrgRelationMapper orgRelationMapper;

    public List<UompDesensitization> getDesRule(String tableName, String desRuleParam, String instId, String createBy) {
        //返回需要脱敏的集合
        List<UompDesensitization> resultRuleList = new ArrayList<>();
        //本人拥有角色id集合
        List<String> userRoleIdList = new ArrayList<>();
        //本人拥有岗位id集合
        List<String> userPostIdList = new ArrayList<>();
        UserDTO user = (UserDTO) ContextUtil.getCurrentUser();
        String userId = user!= null?user.getUserId():"";
        //本人角色
        List<IUserRole> roles = user!= null?user.getRoles():new ArrayList<>();
        userRoleIdList = roles.stream().map(item -> item.getRoleId()).distinct().collect(Collectors.toList());
        //查询本人所拥有的岗位(不含业务组)
        List<OrgPostBaseDTO> orgPostBaseDTOList = orgRelationMapper.selectOwerListByUserId(userId);
        userPostIdList = orgPostBaseDTOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        //1.查询配置全局启用状态，如果全局是不启用的则直接返回
        String allEnable = uompDesensitizationMapper.selectAllEnable();
        //如果为空，或者不等于 0-是 的时候则跳过
        if (!"0".equals(allEnable)) {
            return resultRuleList;
        }

        //2.查询脱敏配置
        //如果des_field_code有值，则查询对象下的某些字段，没有则是该对象下所有配置字段
        //只查询启用状态的即可
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("IS_ENABLE", "0", QueryOP.EQUAL);
        if (StringUtils.isNotEmpty(tableName)) {
            queryFilter.addFilter("DES_OBJ_CODE", tableName, QueryOP.EQUAL);
            if (StringUtils.isNotEmpty(desRuleParam)) {
                queryFilter.addFilter("DES_FIELD_CODE", desRuleParam, QueryOP.IN);
            }
        }
        List<UompDesensitization> list = uompDesensitizationMapper.selectAllConfig(queryFilter);
        for (UompDesensitization it : list) {
            boolean isShow = true;
            //校验每条脱敏规则的明文显示是否符合该人，如果符合明文则无需添加该条脱敏规则
            //1)角色明文过滤
            String plaintextRoleJson = it.getPlaintextRoleJson();
            if (StringUtils.isNotEmpty(plaintextRoleJson)) {
                List<Object> plaintextRoleList = JSONObject.parseObject(plaintextRoleJson, List.class);
                if (!CollectionUtils.isEmpty(plaintextRoleList)) {
                    for (Object obj : plaintextRoleList) {
                        //如果本人拥有该明文显示角色，则直接跳过，不用添加该条规则
                        Map<String, Object> map = (Map<String, Object>) JSONObject.toJSON(obj);
                        if (userRoleIdList.contains((String) map.get("id"))) {
                            isShow = false;
                            break;
                        }
                    }
                }
            }
            //如果上边校验过了，继续查，没过直接不用查了
            if (isShow) {
                //2)岗位明文过滤
                String plaintextPostJson = it.getPlaintextPostJson();
                if (StringUtils.isNotEmpty(plaintextPostJson)) {
                    List<Object> plaintextPostList = JSONObject.parseObject(plaintextPostJson, List.class);
                    if (!CollectionUtils.isEmpty(plaintextPostList)) {
                        for (Object obj : plaintextPostList) {
                            //如果本人拥有该明文显示岗位，则直接跳过，不用添加该条规则
                            Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                            if (userPostIdList.contains((String) map.get("id"))) {
                                isShow = false;
                                break;
                            }
                        }
                    }
                }
            }
            //如果上边校验过了，继续查，没过直接不用查了
            if (isShow) {
                //3)人员明文过滤
                String plaintextUserJson = it.getPlaintextUserJson();
                if (StringUtils.isNotEmpty(plaintextUserJson)) {
                    List<Object> plaintextUserList = JSONObject.parseObject(plaintextUserJson, List.class);
                    if (!CollectionUtils.isEmpty(plaintextUserList)) {
                        for (Object obj : plaintextUserList) {
                            //如果明文人员包含本人，则跳过
                            Map<String, Object> map = (Map) JSONObject.toJSON(obj);
                            if (userId.equals((String) map.get("id"))) {
                                isShow = false;
                                break;
                            }
                        }
                    }
                }
            }

            if (isShow) {
                //4)录入人可见判断（只有详情中才判断录入人可见，所以有create_by是详情查看，列表的规则校验无需传这个参数）
                if (StringUtils.isNotEmpty(createBy)) {
                    String isCreater = it.getIsCreater();
                    if ("0".equals(isCreater)) {
                        //如果录入人是登录人，则无需脱敏
                        if (userId.equals(createBy)) {
                            isShow = false;
                        }
                    }
                }
            }
            if (isShow) {
                //如果有业务id，则证明是详情查询，这时候需要校验录入人可见，经办人可见等
                if (StringUtils.isNotEmpty(instId)) {
                    //5)经办人可见判断
                    String isOperator = it.getIsOperator();
                    if ("0".equals(isOperator)) {
                        //校验方式 0- sql脚本
                        if ("0".equals(it.getOperatorMode())) {
                            String sql = it.getOperatorScript();
                            if (StringUtils.isNotEmpty(sql)) {
                                //todo 暂时固定写法，将业务id，和人员id，替换成对应值; 固定返回值是数量
                                sql = sql.replace("#{user_id}", "'" + userId + "'").replace("#{biz_id}", "'" + instId + "'");
                                //执行该sql(如果sql报错，则直接跳过该明文校验，按照需要脱密算)
                                try {
                                    int num = uompDesensitizationMapper.selectWz(sql);
                                    //如果执行数据量大于0，则经办人有当前用户，无需脱密
                                    if (num > 0) {
                                        isShow = false;
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            }

            if (isShow) {
                //封装脱敏规则
                UompDesensitization ud = new UompDesensitization();
                ud.setDesFieldCode(it.getDesFieldCode());
                ud.setDesRuleMode(it.getDesRuleMode());
                ud.setDesRuleJson(it.getDesRuleJson());
                ud.setDesRuleRegx(it.getDesRuleRegx());
                ud.setSensitiveReplaceWords(it.getSensitiveReplaceWords());
                ud.setSensitiveWords(it.getSensitiveWords());
                resultRuleList.add(ud);
            }
        }
        return resultRuleList;
    }
}
