package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.api.model.TempApplyVO;
import cn.gwssi.ecloud.staffpool.base.ResponsePageData;
import cn.gwssi.ecloud.staffpool.core.entity.UompTempAdmission;
import cn.gwssi.ecloud.staffpool.dto.InfoByNameDTO;
import cn.gwssi.ecloud.staffpool.dto.PercentageDTO;
import cn.gwssi.ecloud.staffpool.dto.TempApplyDTO;
import cn.gwssi.ecloud.staffpool.dto.UompTempAdmissionInfoDTO;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.manager.Manager;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface UompTempAdmissionService extends Manager<String, UompTempAdmission> {

    int insertSelective(UompTempAdmission record);

    int updateByPersonNameAndCard(UompTempAdmission record);

    Integer countByRealVisitTime(String time);

    ResponsePageData<TempApplyDTO>  getTempApplyList(TempApplyVO tempApplyVO);

    Integer deleteTempApply(String id);

    InfoByNameDTO getInfoByName(String personName);

    Boolean addBlackList(String id, String reason);

    UompTempAdmissionInfoDTO getInfoById(String id);

    Boolean checkBlackByCard(String personCard);

    ResponsePageData<TempApplyDTO> personInfoOverview(TempApplyVO tempApplyVO);

    List<TempApplyDTO> exportInfo(TempApplyVO tempApplyVO);

    ResultMsg<String> upload(MultipartFile file) throws Exception;

    List<PercentageDTO> tempPersonTrend();

    /**
     * 获取指定入场时间内的临时驻场数
     * @param realVisitTimeStart 开始时间
     * @param realVisitTimeEnd 结束时间
     * @return
     */
    Integer countByrealVisitTime(String realVisitTimeStart, String realVisitTimeEnd);
}
