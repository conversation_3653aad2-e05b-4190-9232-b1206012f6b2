package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import java.util.Date;

public class UompSupplierPersonnel extends BaseModel {

    private String supplierManagementId;

    private String personnelId;

    private String personnelName;

    private String personnelPost;

    private String personnelType;

    private String personnelTel;

    private String createOrgId;

    private String updateOrgId;

    private String delFlag;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplierManagementId() {
        return supplierManagementId;
    }

    public void setSupplierManagementId(String supplierManagementId) {
        this.supplierManagementId = supplierManagementId;
    }

    public String getPersonnelId() {
        return personnelId;
    }

    public void setPersonnelId(String personnelId) {
        this.personnelId = personnelId;
    }

    public String getPersonnelName() {
        return personnelName;
    }

    public void setPersonnelName(String personnelName) {
        this.personnelName = personnelName;
    }

    public String getPersonnelPost() {
        return personnelPost;
    }

    public void setPersonnelPost(String personnelPost) {
        this.personnelPost = personnelPost;
    }

    public String getPersonnelType() {
        return personnelType;
    }

    public void setPersonnelType(String personnelType) {
        this.personnelType = personnelType;
    }

    public String getPersonnelTel() {
        return personnelTel;
    }

    public void setPersonnelTel(String personnelTel) {
        this.personnelTel = personnelTel;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierManagementId=").append(supplierManagementId);
        sb.append(", personnelId=").append(personnelId);
        sb.append(", personnelName=").append(personnelName);
        sb.append(", personnelPost=").append(personnelPost);
        sb.append(", personnelType=").append(personnelType);
        sb.append(", personnelTel=").append(personnelTel);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", createOrgId=").append(createOrgId);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateOrgId=").append(updateOrgId);
        sb.append(", delFlag=").append(delFlag);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UompSupplierPersonnel other = (UompSupplierPersonnel) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSupplierManagementId() == null ? other.getSupplierManagementId() == null : this.getSupplierManagementId().equals(other.getSupplierManagementId()))
            && (this.getPersonnelId() == null ? other.getPersonnelId() == null : this.getPersonnelId().equals(other.getPersonnelId()))
            && (this.getPersonnelName() == null ? other.getPersonnelName() == null : this.getPersonnelName().equals(other.getPersonnelName()))
            && (this.getPersonnelPost() == null ? other.getPersonnelPost() == null : this.getPersonnelPost().equals(other.getPersonnelPost()))
            && (this.getPersonnelType() == null ? other.getPersonnelType() == null : this.getPersonnelType().equals(other.getPersonnelType()))
            && (this.getPersonnelTel() == null ? other.getPersonnelTel() == null : this.getPersonnelTel().equals(other.getPersonnelTel()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateOrgId() == null ? other.getCreateOrgId() == null : this.getCreateOrgId().equals(other.getCreateOrgId()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getUpdateOrgId() == null ? other.getUpdateOrgId() == null : this.getUpdateOrgId().equals(other.getUpdateOrgId()))
            && (this.getDelFlag() == null ? other.getDelFlag() == null : this.getDelFlag().equals(other.getDelFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierManagementId() == null) ? 0 : getSupplierManagementId().hashCode());
        result = prime * result + ((getPersonnelId() == null) ? 0 : getPersonnelId().hashCode());
        result = prime * result + ((getPersonnelName() == null) ? 0 : getPersonnelName().hashCode());
        result = prime * result + ((getPersonnelPost() == null) ? 0 : getPersonnelPost().hashCode());
        result = prime * result + ((getPersonnelType() == null) ? 0 : getPersonnelType().hashCode());
        result = prime * result + ((getPersonnelTel() == null) ? 0 : getPersonnelTel().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateOrgId() == null) ? 0 : getCreateOrgId().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateOrgId() == null) ? 0 : getUpdateOrgId().hashCode());
        result = prime * result + ((getDelFlag() == null) ? 0 : getDelFlag().hashCode());
        return result;
    }
}