package cn.gwssi.ecloud.staffpool.core.manager;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractResource;
import cn.gwssi.ecloudframework.base.manager.Manager;

import java.util.List;
import java.util.Map;

public interface UompContractResourceService extends Manager<String, UompContractResource> {

    int deleteByPrimaryKey(String id);

    int insert(UompContractResource record);

    int insertSelective(UompContractResource record);

    UompContractResource selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(UompContractResource record);

    int updateByPrimaryKey(UompContractResource record);

    List<UompContractResource> getResourceListByContractId(String contractId);

    void deleteByContractId(String contractId);

    void deleteByIdList(List<String> idList);

    List<String> selectListByCmIdAndCiId(List<Map<String, String>> paramList);

    void insertBatch(List<UompContractResource> resourceInsertList);

    void updateBatchByCmIdAndCiId(List<UompContractResource> resourceUpdateList);
}
