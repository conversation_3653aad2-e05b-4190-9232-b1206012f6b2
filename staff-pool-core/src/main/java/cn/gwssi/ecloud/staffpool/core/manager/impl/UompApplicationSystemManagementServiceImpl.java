package cn.gwssi.ecloud.staffpool.core.manager.impl;

import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.api.model.UompApplicationSystemManagementSave;
import cn.gwssi.ecloud.staffpool.core.dao.UompAdmissionPersonMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemRelation;
import cn.gwssi.ecloud.staffpool.core.entity.UompContractManagement;
import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierManagement;
import cn.gwssi.ecloud.staffpool.core.manager.UompApplicationSystemRelationService;
import cn.gwssi.ecloud.staffpool.core.manager.UompContractManagementService;
import cn.gwssi.ecloud.staffpool.core.manager.UompSupplierManagementService;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloud.staffpool.util.DateUtils;
import cn.gwssi.ecloud.staffpool.util.IntervalDay;
import cn.gwssi.ecloudbpm.module.cmdb.api.service.ICMDBService;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.activiti.engine.impl.json.JsonObjectConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.gwssi.ecloud.staffpool.core.dao.UompApplicationSystemManagementMapper;
import cn.gwssi.ecloud.staffpool.core.entity.UompApplicationSystemManagement;
import cn.gwssi.ecloud.staffpool.core.manager.UompApplicationSystemManagementService;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class UompApplicationSystemManagementServiceImpl extends BaseManager<String, UompApplicationSystemManagement> implements UompApplicationSystemManagementService {

    @Resource
    private UompApplicationSystemManagementMapper uompApplicationSystemManagementMapper;
    @Autowired
    private UompContractManagementService uompContractManagementService;
    @Autowired
    private UompApplicationSystemRelationService uompApplicationSystemRelationService;
    @Resource
    private UompAdmissionPersonMapper uompAdmissionPersonMapper;
    @Resource
    private IntervalDay intervalDay;

    @Resource
    private ICMDBService icmdbService;
    @Resource
    private UompSupplierManagementService uompSupplierManagementService;

    private SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.yyyyMMddHHmm);

    @Override
    public int insertSelective(UompApplicationSystemManagement record) {
        return uompApplicationSystemManagementMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKey(UompApplicationSystemManagement record) {
        return uompApplicationSystemManagementMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageResult getSystemList(QueryFilter queryFilter) {
        List<UompApplicationSystemManagement> list = uompApplicationSystemManagementMapper.query(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            List<UompApplicationSystemManagementListDTO> dtoList = new ArrayList<>();
            PageResult pageResult = new PageResult(list);
            for (UompApplicationSystemManagement system : list) {
                Date onlineTime = system.getOnlineTime();
                UompApplicationSystemManagementListDTO dto = new UompApplicationSystemManagementListDTO();
                dto.setId(system.getId());
                dto.setApplicationSystemName(system.getApplicationSystemName());
                dto.setDepartName(system.getDepartName());
                dto.setIsHeart("0".equals(system.getIsHeart()) ? "否" : "是");
                dto.setSystemStatus("0".equals(system.getSystemStatus()) ? "使用中" : "已停用");
                //隐藏上线时间
//                dto.setOnlineTime(sdf.format(onlineTime));
                dto.setPrincipalName(system.getPrincipalName());
                // 根据服务商id查询服务商名称 防止服务商名称更新
                UompSupplierManagement uompSupplierManagement = uompSupplierManagementService.get(system.getSupplierId());
                if (null != uompSupplierManagement) {
                    dto.setSupplierName(uompSupplierManagement.getSupplierName());
                }
                dtoList.add(dto);
            }
            pageResult.setRows(dtoList);
            return pageResult;
        }
        return new PageResult(new ArrayList());
    }

    @Override
    public UompApplicationSystemManagementDTO getSystemInfoById(String id) {
        UompApplicationSystemManagement uompApplicationSystemManagement = uompApplicationSystemManagementMapper.get(id);
        if (null == uompApplicationSystemManagement) {
            throw new BusinessMessage("应用系统信息不存在！");
        }
        UompApplicationSystemManagementDTO dto = new UompApplicationSystemManagementDTO();
        BeanUtils.copyProperties(uompApplicationSystemManagement, dto);
        // 根据服务商id查询服务商名称、负责人、电话  没有查询到就是服务商已删除，相关信息至空
        UompSupplierManagement uompSupplierManagement = uompSupplierManagementService.get(dto.getSupplierId());
        if (null != uompSupplierManagement) {
            dto.setSupplierName(uompSupplierManagement.getSupplierName());
            dto.setSupplierDepartName(uompSupplierManagement.getRespName());
            dto.setSupplierTel(uompSupplierManagement.getTel());
        } else {
            dto.setSupplierName(null);
            dto.setSupplierDepartName(null);
            dto.setSupplierTel(null);
        }
        // 查询应用系统关联合同绑定列表
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        // 未删除
        queryFilter.addFilter("ucm.DEL_FLAG", "0", QueryOP.EQUAL);
        // 类型：contract（合同）
        queryFilter.addFilter("uasr.RELATION_TYPE", "contract", QueryOP.EQUAL);
        // 应用系统
        queryFilter.addFilter("uasr.APPLICATION_SYSTEM_MANAGEMENT_ID", id, QueryOP.EQUAL);
        // 质保结束时间倒叙
        queryFilter.addFieldSort("ucm.QUALITY_END_DAY", "desc");
        List<UompContractManagement> list = uompContractManagementService.bindingContractListBySystemId(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            List<UompContractManagementSystemDTO> contractList = new ArrayList<>();
            for (UompContractManagement contractManagement : list) {
                UompContractManagementSystemDTO contractManagementSystem = new UompContractManagementSystemDTO();
                BeanUtils.copyProperties(contractManagement, contractManagementSystem);
                contractList.add(contractManagementSystem);
            }
            dto.setContractList(contractList);
        }
        // 根据供应商查询驻场人员
        PersonListQueryVO personListQueryVO = new PersonListQueryVO();
        personListQueryVO.setIfSupplier("1");
        personListQueryVO.setEntryStatus("0");
        personListQueryVO.setSupplierId(uompApplicationSystemManagement.getSupplierId());
        personListQueryVO.setInvolvedProjectList(Arrays.asList(uompApplicationSystemManagement.getId().split(",")));
        List<PersonListDTO> personListDTOList = uompAdmissionPersonMapper.selectAllBySelective(personListQueryVO);
        dto.setPersonListDTOList(personListDTOList);
        // 距离运维天数
        List<UompContractManagementSystemDTO> contractList = dto.getContractList();
        if (CollectionUtils.isNotEmpty(contractList)) {
            Date startDate = new Date();
            Date endDate = contractList.get(0).getQualityEndDay();
            if (endDate != null) {
                dto.setOutDays(intervalDay.compareDays(startDate, endDate));
            }
        }
        return dto;
    }

    @Override

    public UompApplicationSystemManagementDTO getSystemInfoByName(String name) {
        UompApplicationSystemManagement uompApplicationSystemManagement = uompApplicationSystemManagementMapper.selectIdByApplicationSystemName(name);
        if (null == uompApplicationSystemManagement) {
            throw new BusinessMessage("应用系统信息不存在！");
        }
        UompApplicationSystemManagementDTO dto = new UompApplicationSystemManagementDTO();
        BeanUtils.copyProperties(uompApplicationSystemManagement, dto);
        // 根据服务商id查询服务商名称、负责人、电话  没有查询到就是服务商已删除，相关信息至空
        UompSupplierManagement uompSupplierManagement = uompSupplierManagementService.get(dto.getSupplierId());
        if (null != uompSupplierManagement) {
            dto.setSupplierName(uompSupplierManagement.getSupplierName());
            dto.setSupplierDepartName(uompSupplierManagement.getRespName());
            dto.setSupplierTel(uompSupplierManagement.getTel());
        } else {
            dto.setSupplierName(null);
            dto.setSupplierDepartName(null);
            dto.setSupplierTel(null);
        }
        // 查询应用系统关联合同绑定列表
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        // 未删除
        queryFilter.addFilter("ucm.DEL_FLAG", "0", QueryOP.EQUAL);
        // 类型：contract（合同）
        queryFilter.addFilter("uasr.RELATION_TYPE", "contract", QueryOP.EQUAL);
        // 应用系统
        queryFilter.addFilter("uasr.APPLICATION_SYSTEM_MANAGEMENT_ID", uompApplicationSystemManagement.getId(), QueryOP.EQUAL);
        // 质保结束时间倒叙
        queryFilter.addFieldSort("ucm.QUALITY_END_DAY", "desc");
        List<UompContractManagement> list = uompContractManagementService.bindingContractListBySystemId(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            List<UompContractManagementSystemDTO> contractList = new ArrayList<>();
            for (UompContractManagement contractManagement : list) {
                UompContractManagementSystemDTO contractManagementSystem = new UompContractManagementSystemDTO();
                BeanUtils.copyProperties(contractManagement, contractManagementSystem);
                contractList.add(contractManagementSystem);
            }
            dto.setContractList(contractList);
        }
        // 根据供应商查询驻场人员
        PersonListQueryVO personListQueryVO = new PersonListQueryVO();
        personListQueryVO.setIfSupplier("1");
        personListQueryVO.setEntryStatus("0");
        personListQueryVO.setSupplierId(uompApplicationSystemManagement.getSupplierId());
        personListQueryVO.setInvolvedProjectList(Arrays.asList(uompApplicationSystemManagement.getId().split(",")));
        List<PersonListDTO> personListDTOList = uompAdmissionPersonMapper.selectAllBySelective(personListQueryVO);
        dto.setPersonListDTOList(personListDTOList);
        // 距离运维天数
        List<UompContractManagementSystemDTO> contractList = dto.getContractList();
        if (CollectionUtils.isNotEmpty(contractList)) {
            Date startDate = new Date();
            Date endDate = contractList.get(0).getQualityEndDay();
            if (endDate != null) {
                dto.setOutDays(intervalDay.compareDays(startDate, endDate));
            }
        }
        return dto;
    }

    @Override
    public PageResult unbindingContractList(QueryFilter queryFilter) {
        List<UompContractManagement> list = uompContractManagementService.unbindingContractList(queryFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            PageResult pageResult = new PageResult(list);
            List<UompContractManagementSystemDTO> contractList = new ArrayList<>();
            for (UompContractManagement contractManagement : list) {
                UompContractManagementSystemDTO contractManagementSystem = new UompContractManagementSystemDTO();
                BeanUtils.copyProperties(contractManagement, contractManagementSystem);
                contractList.add(contractManagementSystem);
            }
            pageResult.setRows(contractList);
            return pageResult;
        }
        return new PageResult(new ArrayList());
    }

    @Override
    public String save(UompApplicationSystemManagementSave uompApplicationSystemManagementSave) {
        // 当前登录用户信息
        IUser user = ContextUtil.getCurrentUser();
        String userId = user.getUserId();
        String orgId = user.getOrgId();
        // 应用系统名称
        String systemName = uompApplicationSystemManagementSave.getApplicationSystemName();
        // 判断应用系统名称唯一性
        QueryFilter queryFilter = new DefaultQueryFilter(true);
        queryFilter.addFilter("DEL_FLAG", "0", QueryOP.EQUAL);
        queryFilter.addFilter("APPLICATION_SYSTEM_NAME", systemName, QueryOP.EQUAL);
        List<UompApplicationSystemManagement> list = uompApplicationSystemManagementMapper.query(queryFilter);
        // 合同关联
        List<String> idList = uompApplicationSystemManagementSave.getIdList();
        // 主键
        String id = uompApplicationSystemManagementSave.getId();
        if (StringUtils.isNotEmpty(id)) { //走更新
            if (CollectionUtils.isNotEmpty(list)) {
                UompApplicationSystemManagement systemManagement = list.get(0);
                if (list.size() > 1 || (list.size() == 1 && !systemManagement.getId().equals(id))) {
                    throw new BusinessMessage("应用系统名称已存在！");
                }
            }
            UompApplicationSystemManagement systemManagement = uompApplicationSystemManagementMapper.get(id);
            BeanUtils.copyProperties(uompApplicationSystemManagementSave, systemManagement);
            systemManagement.setUpdateBy(userId);
            systemManagement.setUpdateTime(new Date());
            systemManagement.setUpdateOrgId(orgId);
            uompApplicationSystemManagementMapper.update(systemManagement);
            // 删除应用系统-合同关联关系
            UompApplicationSystemRelation systemRelation = new UompApplicationSystemRelation();
            systemRelation.setApplicationSystemManagementId(id);
            systemRelation.setDelFlag("1");
            systemRelation.setUpdateBy(userId);
            systemRelation.setUpdateTime(new Date());
            systemRelation.setUpdateOrgId(orgId);
            uompApplicationSystemRelationService.updateDelFlag(systemRelation);
            // 添加新的应用系统与合同的关联关系
            this.updateContractInfo(idList, id, systemName, userId, orgId);
        } else { // 走添加
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessMessage("应用系统名称已存在！");
            }
            id = IdUtil.getSuid();
            UompApplicationSystemManagement uompApplicationSystemManagement = new UompApplicationSystemManagement();
            BeanUtils.copyProperties(uompApplicationSystemManagementSave, uompApplicationSystemManagement);
            uompApplicationSystemManagement.setId(id);
            uompApplicationSystemManagement.setCreateBy(userId);
            uompApplicationSystemManagement.setCreateTime(new Date());
            uompApplicationSystemManagement.setCreateOrgId(orgId);
            uompApplicationSystemManagement.setDelFlag("0");
            uompApplicationSystemManagementMapper.insertSelective(uompApplicationSystemManagement);
            this.updateContractInfo(idList, id, systemName, userId, orgId);
        }
        return id;
    }

    /**
     * 更新应用系统-合同关联关系、更新合同用用系统信息
     *
     * @param idList     合同id集合
     * @param id         应用系统id
     * @param systemName 应用系统名称
     * @param userId     用户id
     * @param orgId      运维组织id
     **/
    private void updateContractInfo(List<String> idList, String id, String systemName, String userId, String orgId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            for (String key : idList) {
                // 添加应用系统-合同关联表
                UompApplicationSystemRelation relation = new UompApplicationSystemRelation();
                String rid = IdUtil.getSuid();
                relation.setId(rid);
                relation.setApplicationSystemManagementId(id);
                relation.setRelationId(key);
                relation.setRelationType("contract");
                relation.setCreateBy(userId);
                relation.setCreateTime(new Date());
                relation.setCreateOrgId(orgId);
                relation.setDelFlag("0");
                uompApplicationSystemRelationService.insertSelective(relation);
                // 修改合同的应用系统信息
                UompContractManagement uompContractManagement = uompContractManagementService.get(key);
                // 合同添加应用系统信息
                uompContractManagement.setProjectManagementId(id);
                uompContractManagement.setProjectManagementName(systemName);
                uompContractManagement.setUpdateBy(userId);
                uompContractManagement.setUpdateTime(new Date());
                uompContractManagement.setUpdateOrgId(orgId);
                uompContractManagementService.updateByPrimaryKey(uompContractManagement);
            }
        }
    }

    @Override
    public List<ProjectBySupplierDTO> selectIdAndNameByAll() {
        return uompApplicationSystemManagementMapper.selectIdAndNameByAll();
    }

    @Override
    public void bindApplicationSystem(JSONObject applicationSystem) {
        UompApplicationSystemManagement uompApplicationSystemManagement = new UompApplicationSystemManagement();
        uompApplicationSystemManagement.setId(applicationSystem.getString("id"));
        uompApplicationSystemManagement.setApplicationSystemName(applicationSystem.getString("ci_name"));
        uompApplicationSystemManagement.setDepartName(applicationSystem.getString("responsible_org"));
        uompApplicationSystemManagement.setSupplierDepartName(applicationSystem.getString("responsible_org"));
        uompApplicationSystemManagement.setSupplierTel(applicationSystem.getString("responsible_contact_info"));

        uompApplicationSystemManagement.setPrincipalName(applicationSystem.getString("responsible_person"));
        uompApplicationSystemManagement.setIsHeart(applicationSystem.getString("is_core"));
        uompApplicationSystemManagement.setIsHeart("是".equals(applicationSystem.getString("is_core")) ? "1" : "0");
        uompApplicationSystemManagement.setSystemStatus("使用中".equals(applicationSystem.getString("application_state")) ? "0" : "1");
        String supplierId = applicationSystem.getString("supplier_id");
        UompSupplierManagement supplierManagement = uompSupplierManagementService.get(supplierId);
        uompApplicationSystemManagement.setSupplierName(supplierManagement.getSupplierName());
        uompApplicationSystemManagement.setSupplierId(supplierId);
        uompApplicationSystemManagement.setDelFlag("0");
        if (Objects.isNull(uompApplicationSystemManagementMapper.get(uompApplicationSystemManagement.getId()))) {
            uompApplicationSystemManagementMapper.insertSelective(uompApplicationSystemManagement);
        } else {
            uompApplicationSystemManagementMapper.update(uompApplicationSystemManagement);
        }
    }
}
