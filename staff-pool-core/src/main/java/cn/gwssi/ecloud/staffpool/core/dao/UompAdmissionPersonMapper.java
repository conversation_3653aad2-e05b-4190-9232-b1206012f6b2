package cn.gwssi.ecloud.staffpool.core.dao;
import java.util.Collection;

import cn.gwssi.ecloud.staffpool.api.model.PersonListQueryVO;
import cn.gwssi.ecloud.staffpool.core.entity.UompAdmissionPerson;
import cn.gwssi.ecloud.staffpool.dto.*;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface UompAdmissionPersonMapper extends BaseDao<String, UompAdmissionPerson> {

    int insertSelective(UompAdmissionPerson record);

    List<EntryPersonSelectDTO> selectAllByEntryPersonSelect(@Param("ifSupplier") String ifSupplier, @Param("supplierId") String supplierId);

    String selectPersonCardByApplyId(String id);

    List<String> selectEngagementProjectIdByPersonCard(String personCard);

    Integer countByPersonCard(String personCard);

    Integer countByInTime(String time);

    int updateById(UompAdmissionPerson updated);

    int updateByApplyId(UompAdmissionPerson updated);

    List<String> selectIdByPersonCard(String personCard);

    List<ProjectListBean> selectEngagementProjectByPersonCard(String personCard);

    int updateOutApplyStatusByIdIn(@Param("applyPersonIdList") List<String> applyPersonIdList);

    int updateOutTimeByIdIn(@Param("dateTime") Date dateTime, @Param("applyPersonIdList") List<String> applyPersonIdList);

    List<PersonListDTO> selectAllBySelective(PersonListQueryVO personListQueryVO);
    List<PersonListDTO> selectAllBySelectivePage(PersonListQueryVO personListQueryVO);

    List<String> selectIdBySelective(PersonListQueryVO personListQueryVO);

    EntryApplyDTO selectAllById(String id);

    List<PersonInfoOverviewDTO> selectAllByPersonCard(@Param("personCard") String personCard, @Param("createBy") String createBy);

    List<PersonListDTO> selectAllBySelectiveExport(PersonListQueryVO personListQueryVO);

    List<String> selectEngagementProjectIdByPersonCardAndApplyStatus(String personCard);

    Integer countTotalPersonLocation();

    List<PercentageDTO> getPersonInTime(@Param("workingCompanyId") String workingCompanyId, @Param("workingCompany") String workingCompany);

    Integer countTotalByApplyStatus(@Param("inTimeBegin") String inTimeBegin, @Param("inTimeEnd") String inTimeEnd, @Param("workingCompanyId") String workingCompanyId, @Param("workingCompany") String workingCompany);

    Integer countTotalByApplyStatusNew(@Param("inTimeBegin") String inTimeBegin, @Param("inTimeEnd") String inTimeEnd, @Param("workingCompanyId") String workingCompanyId, @Param("workingCompany") String workingCompany);

    List<PercentageDTO> getPersonLocation();

    UompAdmissionPerson selectOneByPersonIdAndPersonCard(@Param("personId")String personId,@Param("personCard")String personCard);

    List<UompAdmissionPerson> selectAllByIdIn(@Param("idCollection")Collection<String> idCollection);

    Integer countByInTimeRange(@Param("inTimeStart")String inTimeStart,@Param("inTimeEnd")String inTimeEnd);

    List<PersonListDTO> selectAllByPersonCardIn(@Param("personCardList")List<String> personCardList);

    List<PersonListDTO> selectAllByPersonAdmissionIds(@Param("admissionIds")List<String> admissionIds);

    List<PercentageDTO> getPersonInTimeNew();

    void encryptPersonCard(@Param("personCard")String personCard,@Param("id")String id);
}