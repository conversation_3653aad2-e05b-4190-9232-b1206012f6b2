package cn.gwssi.ecloud.staffpool.base;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class ErrorResponseData<T> extends ResponseData<T> {

    private Date timestamp;
    private String path;
    private String exceptionClazz;


    public ErrorResponseData(String message) {
        super(ResponseCodeEnums.BAD_REQUEST.getValue(), message, null);
    }

    public ErrorResponseData(Integer code, String message) {
        super(code, message, null);
    }

    public ErrorResponseData(Class<? extends Throwable> _class, Integer code, String message) {
        super(code, message, null);
        this.exceptionClazz = _class.getName();
    }

    public ErrorResponseData(Integer code, String message, Date timestamp, String path) {
        super(code, message, null);
        this.timestamp = timestamp;
        this.path = path;
    }

    public ErrorResponseData(Class<? extends Throwable> _class, Integer code, String message, Date timestamp, String path) {
        super(code, message, null);
        this.exceptionClazz = _class.getName();
        this.timestamp = timestamp;
        this.path = path;
    }

    public ErrorResponseData(Integer code, String message, T object) {
        super(code, message, object);
    }

    public ErrorResponseData(Integer code, String message, Date timestamp) {
        super(code, message, null);
        this.timestamp = timestamp;
    }

    public ErrorResponseData(Class<? extends Exception> _class, Integer code, String message, T object) {
        super(code, message, object);
        this.exceptionClazz = _class.getName();
    }
}
