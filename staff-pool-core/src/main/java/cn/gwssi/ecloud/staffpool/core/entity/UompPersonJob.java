package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "uomp_person_job")
@Data
public class UompPersonJob extends BaseModel {
    @NotEmpty(message = "人员id不能为空")
    @ApiModelProperty(value="人员id")
    private String personId;

    @ApiModelProperty(value = "工作开始时间")
    @NotEmpty(message = "工作开始时间不能为空")
    private String jobBeginTime;

    @ApiModelProperty(value = "工作截至时间")
    @NotEmpty(message = "工作截至时间不能为空")
    private String jobEndTime;

    @ApiModelProperty(value = "公司名称")
    @NotEmpty(message = "公司名称不能为空")
    private String companyName;

    @ApiModelProperty(value = "工作职位")
    @NotEmpty(message = "工作职位不能为空")
    private String jobPosition;

    @ApiModelProperty(value = "\n" +
            "工作描述\n")
    private String jobDescribe;

    @ApiModelProperty(value = "")
    private String fileInfo;

    @ApiModelProperty(value = "")
    private String createOrgId;

    @ApiModelProperty(value = "")
    private String updateOrgId;

    @ApiModelProperty(value = "")
    private String delFlag;

    private static final long serialVersionUID = 1L;
}