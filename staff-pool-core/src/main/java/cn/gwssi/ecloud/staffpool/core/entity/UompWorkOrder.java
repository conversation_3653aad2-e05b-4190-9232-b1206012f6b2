package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
    * 工单表
    */
@ApiModel(value="工单表")
@Data
public class UompWorkOrder extends BaseModel {

    /**
    * 请求ID（事件、问题、变更等业务ID）
    */
    @ApiModelProperty(value="请求ID（事件、问题、变更等业务ID）")
    private String requestId;

    /**
    * 工单类型
    */
    @ApiModelProperty(value="工单类型")
    private String orderType;

    /**
    * 工单编号
    */
    @ApiModelProperty(value="工单编号")
    private String orderNo;

    /**
    * 工单级别
    */
    @ApiModelProperty(value="工单级别")
    private String orderLevel;

    /**
    * 工单标题
    */
    @ApiModelProperty(value="工单标题")
    private String orderTitle;

    /**
    * 工单状态
    */
    @ApiModelProperty(value="工单状态")
    private String orderState;

    /**
    * 服务分类
    */
    @ApiModelProperty(value="服务分类")
    private String serviceType;

    /**
    * 涉及资产ID
    */
    @ApiModelProperty(value="涉及资产ID")
    private String resourceId;

    /**
    * 办理人姓名
    */
    @ApiModelProperty(value="办理人姓名")
    private String handler;

    /**
    * 办理人ID
    */
    @ApiModelProperty(value="办理人ID")
    private String handlerId;

    /**
    * 办理组
    */
    @ApiModelProperty(value="办理组")
    private String handlerGroup;

    /**
    * 办理组ID
    */
    @ApiModelProperty(value="办理组ID")
    private String handlerGroupId;

    /**
    * 申报人姓名
    */
    @ApiModelProperty(value="申报人姓名")
    private String applicant;

    /**
    * 申报人电话
    */
    @ApiModelProperty(value="申报人电话")
    private String applicantTel;

    /**
    * 环节计时时间
    */
    @ApiModelProperty(value="环节计时时间")
    private Date linkStartTime;

    /**
    * 流程计时时间
    */
    @ApiModelProperty(value="流程计时时间")
    private Date flowStartTime;

    /**
    * 环节到期时间
    */
    @ApiModelProperty(value="环节到期时间")
    private Date linkOverTime;

    /**
    * 流程到期时间
    */
    @ApiModelProperty(value="流程到期时间")
    private Date flowOverTime;

    /**
    * 流程实例ID
    */
    @ApiModelProperty(value="流程实例ID")
    private String instId;

    /**
    * 处理完成时间
    */
    @ApiModelProperty(value="处理完成时间")
    private Date finishTime;

    /**
    * 是否挂起(1是0否，非计时9)
    */
    @ApiModelProperty(value="是否挂起(1是0否，非计时9)")
    private Short ifPause;

    /**
    * 是否需要反馈（0否1是2完成）
    */
    @ApiModelProperty(value="是否需要反馈（0否1是2完成）")
    private String ifFeedback;

    /**
    * 创建人机构ID
    */
    @ApiModelProperty(value="创建人机构ID")
    private String createOrgId;

    /**
    * 删除标记(0有效1无效)
    */
    @ApiModelProperty(value="删除标记(0有效1无效)")
    private String delFlag;

    /**
    * 登记人姓名
    */
    @ApiModelProperty(value="登记人姓名")
    private String registrant;

    /**
    * 登记人ID
    */
    @ApiModelProperty(value="登记人ID")
    private String registrantId;

    /**
    * 是否拆分子请求
    */
    @ApiModelProperty(value="是否拆分子请求")
    private String ifSplitChild;

    /**
    * 总时限
    */
    @ApiModelProperty(value="总时限")
    private Long timeLimit;

    /**
    * 挂起时间
    */
    @ApiModelProperty(value="挂起时间")
    private Date pauseTime;

    /**
    * 暂停时限汇总
    */
    @ApiModelProperty(value="暂停时限汇总")
    private Long pauseSum;

    /**
    * 临期期限
    */
    @ApiModelProperty(value="临期期限")
    private Long remindLimit;

    /**
    * 父流程实例ID
    */
    @ApiModelProperty(value="父流程实例ID")
    private String parentInstId;

    /**
    * 所属项目
    */
    @ApiModelProperty(value="所属项目")
    private String projectId;

    /**
    * 是否重点关注(1是0否默认0)
    */
    @ApiModelProperty(value="是否重点关注(1是0否默认0)")
    private String ifVip;

    /**
    * 修改字段记录
    */
    @ApiModelProperty(value="修改字段记录")
    private String modifyRecord;

    /**
    * 重启次数
    */
    @ApiModelProperty(value="重启次数")
    private Short restart;

    /**
    * 关联业务ID,比如告警ID
    */
    @ApiModelProperty(value="关联业务ID,比如告警ID")
    private String busId;

    /**
    * 是否来自接口
    */
    @ApiModelProperty(value="是否来自接口")
    private String ifFromApi;

    /**
    * 工单反馈响应状态 (1是0否默认0)
    */
    @ApiModelProperty(value="工单反馈响应状态 (1是0否默认0)")
    private String responseStatus;

    /**
    * 工单类别
    */
    @ApiModelProperty(value="工单类别")
    private String orderKind;

    /**
    * 登记部门
    */
    @ApiModelProperty(value="登记部门")
    private String applicantOrgName;

    /**
    * 登记部门ID
    */
    @ApiModelProperty(value="登记部门ID")
    private String applicantOrgId;

    @ApiModelProperty(value="")
    private String orderDesc;

    @ApiModelProperty(value="")
    private String applicantRoom;
}