package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.OrgRelation;
import cn.gwssi.ecloud.staffpool.dto.ManagerDTO;
import cn.gwssi.ecloud.staffpool.dto.OrgPostBaseDTO;
import cn.gwssi.ecloud.staffpool.dto.PostDTO;
import cn.gwssi.ecloud.staffpool.dto.UompAcceptInfoDTO;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrgRelationMapper extends BaseDao<String, OrgRelation> {

    int countByUserIdAndRole(@Param("userId") String userId, @Param("alias") String alias);

    List<OrgPostBaseDTO> selectOwerListByUserId(@Param("userId") String userId);

    String selectGroupByUserId(@Param("userId") String userId);

    String selectGroupIdByUserId(@Param("userId") String userId);

    List<ManagerDTO> selectNumByStatus();

    List<UompAcceptInfoDTO> selectUserInfoByUserId(String userId);

    List<PostDTO> selectNoOwerPostList(QueryFilter queryFilter);
}
