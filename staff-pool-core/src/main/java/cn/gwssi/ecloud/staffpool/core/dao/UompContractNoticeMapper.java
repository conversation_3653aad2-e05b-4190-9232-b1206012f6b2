package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompContractNotice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompContractNoticeMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(UompContractNotice record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UompContractNotice record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    UompContractNotice selectByPrimaryKey(String id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UompContractNotice record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UompContractNotice record);

    List<UompContractNotice> getListByContractIds(@Param("contractIds") List<String> contractIds);
}