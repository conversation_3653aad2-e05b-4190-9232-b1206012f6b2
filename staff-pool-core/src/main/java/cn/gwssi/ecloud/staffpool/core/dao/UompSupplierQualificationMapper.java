package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierQualification;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierQualificationDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;

import java.util.List;

public interface UompSupplierQualificationMapper extends BaseDao<String, UompSupplierQualification> {

    int insertSelective(UompSupplierQualification record);

    List<UompSupplierQualificationDto> getQualificationList(String supplierManagementId);

    int updateById(UompSupplierQualification qualification);
}