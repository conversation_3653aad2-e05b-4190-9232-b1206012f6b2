package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="uomp_training_record_person")
@Data
public class UompTrainingRecordPerson extends BaseModel {
    

    @ApiModelProperty(value="")
    private String trainingRecordId;

    @ApiModelProperty(value="")
    private String trainingPersonId;

    @ApiModelProperty(value="")
    private String trainingPersonName;

    private static final long serialVersionUID = 1L;
}