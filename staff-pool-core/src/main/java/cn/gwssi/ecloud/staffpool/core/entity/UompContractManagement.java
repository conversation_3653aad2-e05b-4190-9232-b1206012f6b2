package cn.gwssi.ecloud.staffpool.core.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

@ApiModel(value = "合同管理信息")
public class UompContractManagement extends BaseModel {

    @ApiModelProperty(value = "编码")
    private String contractCode;

    @ApiModelProperty(value = "名称")
    private String contractName;

    @ApiModelProperty(value = "签订日期")
    private Date signingDate;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "甲方名称")
    private String partyAName;

    @ApiModelProperty(value = "甲方负责人名称")
    private String partyAPrincipalName;

    @ApiModelProperty(value = "乙方名称")
    private String partyBName;

    @ApiModelProperty(value = "乙方id")
    private String partyBId;

    @ApiModelProperty(value = "乙方负责人名称")
    private String partyBPrincipalName;

    @ApiModelProperty(value = "丙方名称")
    private String partyCName;

    @ApiModelProperty(value = "丙方负责人名称")
    private String partyCPrincipalName;

    @ApiModelProperty(value = "合同内容")
    private String contractContent;

    @ApiModelProperty(value = "应用系统id")
    private String projectManagementId;

    @ApiModelProperty(value = "应用系统名称")
    private String projectManagementName;

    @ApiModelProperty(value = "责任部门id")
    private String dutyDepartId;

    @ApiModelProperty(value = "责任部门名称")
    private String dutyDepartName;

    @ApiModelProperty(value = "付款日期")
    private Date payTime;

    @ApiModelProperty(value = "付款情况")
    private String payStatus;

    @ApiModelProperty(value = "服务等级协议id")
    private String serviceLevelId;

    @ApiModelProperty(value = "服务等级协议名称")
    private String serviceLevelName;

    @ApiModelProperty(value = "质保/维保开始日")
    private Date qualityBeginDay;

    @ApiModelProperty(value = "质保/维保结束日")
    private Date qualityEndDay;

    @ApiModelProperty(value = "质保内容")
    private String qualityContent;

    @ApiModelProperty(value = "甲方负责人电话")
    private String partyAPrincipalTel;

    @ApiModelProperty(value = "甲方项目经理")
    private String partyAManager;

    @ApiModelProperty(value = "甲方项目经理电话")
    private String partyAManagerTel;

    @ApiModelProperty(value = "乙方负责人电话")
    private String partyBPrincipalTel;

    @ApiModelProperty(value = "")
    private String partyBManager;

    @ApiModelProperty(value = "乙方项目经理电话")
    private String partyBManagerTel;

    @ApiModelProperty(value = "销售经理")
    private String salesManager;

    @ApiModelProperty(value = "销售经理电话")
    private String salesManagerTel;

    @ApiModelProperty(value = "售前经理")
    private String preSalesManager;

    @ApiModelProperty(value = "售前经理电话")
    private String preSalesManagerTel;

    @ApiModelProperty(value = "丙方联系人")
    private String partyCPerson;

    @ApiModelProperty(value = "丙方联系人电话")
    private String partyCPersonTel;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "录入人id")
    private String entryId;

    @ApiModelProperty(value = "录入人名称")
    private String entryName;

    @ApiModelProperty(value = "创建人机构id")
    private String createOrgId;

    @ApiModelProperty(value = "更新人机构id")
    private String updateOrgId;

    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    private List<UompPartyInfo> partyInfoList;

    private Float contractAmount;

    public String getPartyBId() {
        return partyBId;
    }

    public void setPartyBId(String partyBId) {
        this.partyBId = partyBId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public Date getSigningDate() {
        return signingDate;
    }

    public void setSigningDate(Date signingDate) {
        this.signingDate = signingDate;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public String getPartyAName() {
        return partyAName;
    }

    public void setPartyAName(String partyAName) {
        this.partyAName = partyAName;
    }

    public String getPartyAPrincipalName() {
        return partyAPrincipalName;
    }

    public void setPartyAPrincipalName(String partyAPrincipalName) {
        this.partyAPrincipalName = partyAPrincipalName;
    }

    public String getPartyBName() {
        return partyBName;
    }

    public void setPartyBName(String partyBName) {
        this.partyBName = partyBName;
    }

    public String getPartyBPrincipalName() {
        return partyBPrincipalName;
    }

    public void setPartyBPrincipalName(String partyBPrincipalName) {
        this.partyBPrincipalName = partyBPrincipalName;
    }

    public String getPartyCName() {
        return partyCName;
    }

    public void setPartyCName(String partyCName) {
        this.partyCName = partyCName;
    }

    public String getPartyCPrincipalName() {
        return partyCPrincipalName;
    }

    public void setPartyCPrincipalName(String partyCPrincipalName) {
        this.partyCPrincipalName = partyCPrincipalName;
    }

    public String getContractContent() {
        return contractContent;
    }

    public void setContractContent(String contractContent) {
        this.contractContent = contractContent;
    }

    public String getProjectManagementId() {
        return projectManagementId;
    }

    public void setProjectManagementId(String projectManagementId) {
        this.projectManagementId = projectManagementId;
    }

    public String getProjectManagementName() {
        return projectManagementName;
    }

    public void setProjectManagementName(String projectManagementName) {
        this.projectManagementName = projectManagementName;
    }

    public String getDutyDepartId() {
        return dutyDepartId;
    }

    public void setDutyDepartId(String dutyDepartId) {
        this.dutyDepartId = dutyDepartId;
    }

    public String getDutyDepartName() {
        return dutyDepartName;
    }

    public void setDutyDepartName(String dutyDepartName) {
        this.dutyDepartName = dutyDepartName;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getServiceLevelId() {
        return serviceLevelId;
    }

    public void setServiceLevelId(String serviceLevelId) {
        this.serviceLevelId = serviceLevelId;
    }

    public String getServiceLevelName() {
        return serviceLevelName;
    }

    public void setServiceLevelName(String serviceLevelName) {
        this.serviceLevelName = serviceLevelName;
    }

    public Date getQualityBeginDay() {
        return qualityBeginDay;
    }

    public void setQualityBeginDay(Date qualityBeginDay) {
        this.qualityBeginDay = qualityBeginDay;
    }

    public Date getQualityEndDay() {
        return qualityEndDay;
    }

    public void setQualityEndDay(Date qualityEndDay) {
        this.qualityEndDay = qualityEndDay;
    }

    public String getQualityContent() {
        return qualityContent;
    }

    public void setQualityContent(String qualityContent) {
        this.qualityContent = qualityContent;
    }

    public String getPartyAPrincipalTel() {
        return partyAPrincipalTel;
    }

    public void setPartyAPrincipalTel(String partyAPrincipalTel) {
        this.partyAPrincipalTel = partyAPrincipalTel;
    }

    public String getPartyAManager() {
        return partyAManager;
    }

    public void setPartyAManager(String partyAManager) {
        this.partyAManager = partyAManager;
    }

    public String getPartyAManagerTel() {
        return partyAManagerTel;
    }

    public void setPartyAManagerTel(String partyAManagerTel) {
        this.partyAManagerTel = partyAManagerTel;
    }

    public String getPartyBPrincipalTel() {
        return partyBPrincipalTel;
    }

    public void setPartyBPrincipalTel(String partyBPrincipalTel) {
        this.partyBPrincipalTel = partyBPrincipalTel;
    }

    public String getPartyBManager() {
        return partyBManager;
    }

    public void setPartyBManager(String partyBManager) {
        this.partyBManager = partyBManager;
    }

    public String getPartyBManagerTel() {
        return partyBManagerTel;
    }

    public void setPartyBManagerTel(String partyBManagerTel) {
        this.partyBManagerTel = partyBManagerTel;
    }

    public String getSalesManager() {
        return salesManager;
    }

    public void setSalesManager(String salesManager) {
        this.salesManager = salesManager;
    }

    public String getPreSalesManager() {
        return preSalesManager;
    }

    public void setPreSalesManager(String preSalesManager) {
        this.preSalesManager = preSalesManager;
    }

    public String getPartyCPerson() {
        return partyCPerson;
    }

    public void setPartyCPerson(String partyCPerson) {
        this.partyCPerson = partyCPerson;
    }

    public String getPartyCPersonTel() {
        return partyCPersonTel;
    }

    public void setPartyCPersonTel(String partyCPersonTel) {
        this.partyCPersonTel = partyCPersonTel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEntryId() {
        return entryId;
    }

    public void setEntryId(String entryId) {
        this.entryId = entryId;
    }

    public String getEntryName() {
        return entryName;
    }

    public void setEntryName(String entryName) {
        this.entryName = entryName;
    }

    public String getCreateOrgId() {
        return createOrgId;
    }

    public void setCreateOrgId(String createOrgId) {
        this.createOrgId = createOrgId;
    }

    public String getUpdateOrgId() {
        return updateOrgId;
    }

    public void setUpdateOrgId(String updateOrgId) {
        this.updateOrgId = updateOrgId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getSalesManagerTel() {
        return salesManagerTel;
    }

    public void setSalesManagerTel(String salesManagerTel) {
        this.salesManagerTel = salesManagerTel;
    }

    public String getPreSalesManagerTel() {
        return preSalesManagerTel;
    }

    public void setPreSalesManagerTel(String preSalesManagerTel) {
        this.preSalesManagerTel = preSalesManagerTel;
    }

    public List<UompPartyInfo> getPartyInfoList() {
        return partyInfoList;
    }

    public void setPartyInfoList(List<UompPartyInfo> partyInfoList) {
        this.partyInfoList = partyInfoList;
    }

    public Float getContractAmount() {
        return contractAmount;
    }

    public void setContractAmount(Float contractAmount) {
        this.contractAmount = contractAmount;
    }
}