package cn.gwssi.ecloud.staffpool.service.risk.evaluate;

import cn.gwssi.ecloud.staffpool.core.entity.UompPersonTechnology;
import cn.gwssi.ecloud.staffpool.vo.UompPersonInfoVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 资质过期
 */
@Component
public class ExpiredQualificationEvaluate extends RiskEvaluateChain{

    private static final Logger LOG = LoggerFactory.getLogger(ExpiredQualificationEvaluate.class);

    @Override
    public void evaluate(UompPersonInfoVo uompPersonInfoVo) {
        List<UompPersonTechnology> techList = uompPersonInfoVo.getTechList();
        for (UompPersonTechnology uompPersonTechnology : techList) {
            String endTime = uompPersonTechnology.getEndTime();
            if ("永久有效".equals(endTime)){
                continue;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String nowDay = simpleDateFormat.format(new Date());
            if (endTime.compareTo(nowDay)<=0){
                // TODO: 2025/6/25 插入风险表
                break;
            }
        }
        LOG.info("------用户{}计算资质过期------",uompPersonInfoVo.getPersonName());

    }

    @Override
    public String alias() {
        return "expiredQualification";
    }
}
