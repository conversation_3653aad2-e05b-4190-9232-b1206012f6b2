package cn.gwssi.ecloud.staffpool.core.dao;

import cn.gwssi.ecloud.staffpool.core.entity.UompSupplierFile;
import cn.gwssi.ecloud.staffpool.dto.SupplierFile;
import cn.gwssi.ecloud.staffpool.dto.UompSupplierFileDto;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UompSupplierFileMapper extends BaseDao<String, UompSupplierFile> {

    int insertSelective(UompSupplierFile record);

    int updateById(UompSupplierFile file);

    int updateBySupplierManagementId(UompSupplierFile file);

    void removeBySupplierManagementId(UompSupplierFile file);

    List<UompSupplierFileDto> getFileList(@Param("supplierManagementId") String supplierManagementId, @Param("fileName") String fileName);

    List<SupplierFile> getFileListPatent(@Param("supplierManagementId") String supplierManagementId, @Param("fileType") String fileType);
}