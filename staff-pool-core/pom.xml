<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ecloud-module-staff-pool</artifactId>
        <groupId>cn.gwssi.ecloud</groupId>
        <version>0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>staff-pool-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>staff-pool-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-db</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>wf-api</artifactId>
        </dependency>
        <!--通知消息-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>message-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>sys-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>wf-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-custom-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-custom-core</artifactId>
        </dependency>
        <!-- 模板服务-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>template-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <!--        lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-core</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
<!--        cmdb-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>cmdb-api</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>