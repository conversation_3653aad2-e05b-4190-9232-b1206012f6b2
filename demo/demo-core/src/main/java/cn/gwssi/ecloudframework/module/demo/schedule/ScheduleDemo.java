package cn.gwssi.ecloudframework.module.demo.schedule;

import cn.gwssi.ecloudframework.base.api.aop.annotion.ECloudScheduled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ScheduleDemo {

    protected Logger LOG = LoggerFactory.getLogger(this.getClass());

    @ECloudScheduled(cron="0/30 * * * * ?")
    public void myJob(){
        LOG.debug("Hello schedule for local");
    }


}
