package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.constant.BaseStatusCode;
import cn.gwssi.ecloudframework.base.api.exception.BusinessError;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * IStatusCode状态码统一定义接口
 *
 * BusinessError：系统异常,常常用于强制捕获的异常的包装
 * BusinessException：业务逻辑异常，常常为可预料异常，此异常常常是开发时，非法操作信息提示。比如 流程表单丢失
 * BusinessMessage：业务消息，通常用于业务代码反馈，非系统异常
 */
@CatchErr
@RestController
@RequestMapping("/module/demo/error2/")
public class ErrorDemoController2 extends ControllerTools {

    /**
     * 原生不特殊处理 500
     * @return
     */
    @RequestMapping("/1")
    public ResultMsg<String> test1() {
        throw new BusinessException(BaseStatusCode.TABLE_NOT_FOUND) ;
       // return getSuccessResult("ok") ;
    }

    /**
     * @CatchErr 会抓去当前方法的异常
     *   write2errorlog是否把错误记录到错误日志表
     * BusinessException和BusinessError异常会改变httpCode
     * BusinessMessage异常 httpCode是200
     * @return
     */
    @RequestMapping("/2")
    public ResultMsg<String> test2() {
        throw new BusinessError(BaseStatusCode.NO_ACCESS) ;
        //return getSuccessResult("ok") ;
    }

    /**
     * BusinessMessage httpCode=200
     * @return
     */
    @RequestMapping("/3")
    public ResultMsg<String> test3() {
        throw new BusinessMessage(BaseStatusCode.PARAM_ILLEGAL) ;
        //return getSuccessResult("ok") ;
    }

}
