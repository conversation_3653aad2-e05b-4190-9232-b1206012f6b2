package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.module.demo.jms.message.DemoMessage;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.api.jms.JmsConfig;
import cn.gwssi.ecloudframework.sys.api.jms.model.DefaultJmsDTO;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.ecloudframework.sys.api.jms.producer.JmsProducer;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/module/demo/jms/")
public class JMSDemoController extends ControllerTools {

    @Resource
    JmsConfig jmsConfig;
    @Resource
    JmsProducer producer ;

    @RequestMapping("/sendDemoMessage")
    public ResultMsg<String> send() throws Exception {
        DemoMessage message = new DemoMessage("hello world~") ;
        DefaultJmsDTO dto = new DefaultJmsDTO("demo", message) ;
        producer.sendToQueue(dto);
        return getSuccessResult("ok", "发送成功");
    }

    @RequestMapping("/sendEmail")
    public ResultMsg<String> sendEmail() throws Exception {
        //接收人
        List<SysIdentity> receivers=new ArrayList<>(0);
        DefaultIdentity defaultIdentity=new DefaultIdentity();
        defaultIdentity.setId("2");
        defaultIdentity.setType(SysIdentity.TYPE_USER);
        receivers.add(defaultIdentity);
        NotifyMessage notifyMessage = new NotifyMessage(jmsConfig.getName(), "邮件发送测试信息",null,receivers);
        DefaultJmsDTO dto = new DefaultJmsDTO("email", notifyMessage) ;
        producer.sendToQueue(dto);
        return getSuccessResult("ok", "发送成功");
    }

    @GetMapping("/sendInner/{type}")
    public ResultMsg<String> sendInner(@PathVariable(value = "type",required = true) Integer type) throws Exception {
        //发送人
        UserDTO userDTO=new UserDTO();
        userDTO.setFullname("李学永");
        //接收人
        List<SysIdentity> receivers=new ArrayList<>(0);
        DefaultIdentity defaultIdentity=new DefaultIdentity();
        defaultIdentity.setId("2");
        receivers.add(defaultIdentity);
        NotifyMessage notifyMessage=new NotifyMessage(jmsConfig.getName(),"消息内容",userDTO,receivers);
        //消息概要
        Map<String,Object> map=new HashMap<>(0);
        //type 1:待办任务  2：新闻  3：日程  4：值班  5：会议
        if(type==1){
            //待办消息
            map.clear();
            map.put("title","消息摘要");
            map.put("detailUrl","消息详情url");
            map.put("detailId","消息详情id");
            map.put("head","消息的二级类型");
            map.put("type","消息类型");
            map.put("statue","待办状态");
            notifyMessage.setExtendVars(map);
            notifyMessage.setTag("待办任务");
            DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
            producer.sendToQueue(jmsDTO);
            LOG.info("待办任务消息发送完毕");
        }else if(type==2){
            //新闻消息
            map.clear();
            map.put("title","消息摘要");
            map.put("detailUrl","消息详情url");
            map.put("detailId","消息详情id");
            map.put("head","消息的二级类型");
            map.put("type","消息类型");
            notifyMessage.setExtendVars(map);
            notifyMessage.setTag("公司新闻");
            DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
            producer.sendToQueue(jmsDTO);
            LOG.info("新闻消息发送完毕");
        }else if(type==3){
            //日程消息
            map.clear();
            map.put("title","消息摘要");
            map.put("detailUrl","消息详情url");
            map.put("detailId","消息详情id");
            map.put("head","消息的二级类型");
            map.put("type","消息类型");
            map.put("scheduleName","日程名称");
            map.put("startTime","2020-08-17 16:01:55");
            map.put("endTime","2020-08-17 16:01:59");
            map.put("requestMethod","GET");
            notifyMessage.setExtendVars(map);
            notifyMessage.setTag("日程预约");
            DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
            producer.sendToQueue(jmsDTO);
            LOG.info("日程预约消息发送完毕");
        }else if(type==4){
            //值班消息
            map.clear();
            map.put("title","消息摘要");
            map.put("detailUrl","消息详情url");
            map.put("detailId","消息详情id");
            map.put("head","消息的二级类型");
            map.put("type","消息类型");
            map.put("dutyEndTime","2020-08-17 16:01:59");
            map.put("dutyStartTime","2020-08-17 16:01:55");
            map.put("dutySchedulinTitle","值班标题");
            notifyMessage.setExtendVars(map);
            notifyMessage.setTag("值班安排");
            DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
            producer.sendToQueue(jmsDTO);
            LOG.info("值班安排消息发送完毕");
        }else if(type==5){
            //会议消息
            map.clear();
            map.put("title","消息摘要");
            map.put("detailUrl","消息详情url");
            map.put("detailId","消息详情id");
            map.put("head","消息的二级类型");
            map.put("type","消息类型");
            map.put("startTime","2020-08-17 16:01:55");
            map.put("endTime","2020-08-17 16:01:59");
            map.put("meetingRoomName","会议室名称");
            map.put("meetingName","会议名称");
            map.put("requestMethod","GET");
            notifyMessage.setExtendVars(map);
            notifyMessage.setTag("会议预约");
            DefaultJmsDTO jmsDTO=new DefaultJmsDTO("inner",notifyMessage);
            producer.sendToQueue(jmsDTO);
            LOG.info("会议预约消息发送完毕");
        }else{
            LOG.info("未识别的消息类型");
        }
        return getSuccessResult("ok", "发送成功");
    }

}
