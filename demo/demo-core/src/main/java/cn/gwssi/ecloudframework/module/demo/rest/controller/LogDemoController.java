package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudframework.base.api.aop.annotion.OperateLog;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @OperateLog 注解：开启日志记录，服务启动时需要注入有
 * 日志相关表（sys_log_operate,sys_log_operate_config）的数据源，MQ和异步方式均是使用注入的数据源来存储数据；
 * 目前有MQ和异步两种方式记录日志，如果使用RocketMQ方式，需要配置tag为log。
 *
 * <AUTHOR>
 * @date 2020/10/15 11:46
 */
@RestController
@RequestMapping("/module/demo/log")
public class LogDemoController extends ControllerTools {

    /**
     * 记录操作日志，writeRequest，true：写入请求参数（默认）；false：不写入请求参数；
     * writeResponse：true：写入返回结果（默认）；false：不写入返回结果
     *
     * @return String
     * <AUTHOR>
     * @date 2020/10/15 15:00
     */
    @RequestMapping("test")
    @OperateLog(writeRequest = true,writeResponse = true)
    public ResultMsg<String> test(){
        return getSuccessResult();
    }

}
