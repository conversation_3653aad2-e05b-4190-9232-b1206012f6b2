package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudbpm.module.document.api.platform.IDocumentPlatFormService;
import cn.gwssi.ecloudbpm.module.document.api.model.IBpmRemindLogDoc;
import cn.gwssi.ecloudbpm.module.project.api.service.IProjectManagerService;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.module.demo.core.model.Demo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/module/demo/callService/")
public class CallServiceDemoController extends BaseController<Demo> {

    @Autowired
    IDocumentPlatFormService iDocumentPlatFormService ;

    @Autowired
    IProjectManagerService iProjectManagerService ;

    @RequestMapping("/callDocuemnt")
    public PageResult callDocuemnt(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return iDocumentPlatFormService.documentListJson(0,1,"412988137379201025");
    }


    @RequestMapping("/callProject")
    public PageResult callProject(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<String> a = iProjectManagerService.getIdsByName("a");
        return new PageResult(a) ;
    }

    @Override
    protected String getModelDesc() {
        return "demo";
    }
}
