package cn.gwssi.ecloudframework.module.demo.script;
import cn.gwssi.ecloudframework.module.demo.api.bpm.IDemoScriptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;


/**
 * demo脚本服务
 * <AUTHOR>
 */
@Service
public class DemoScriptServiceImp implements IDemoScriptService {

    protected Logger log = LoggerFactory.getLogger(this.getClass());
    @Override
    public void demoScript(String instanceId, String defId) {
        log.info("测试-------------------常用脚本demo,instId={},defId={}",instanceId,defId);
    }
}
