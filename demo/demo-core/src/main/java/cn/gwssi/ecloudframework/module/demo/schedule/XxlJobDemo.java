package cn.gwssi.ecloudframework.module.demo.schedule;


import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@JobHandler(value = "eCloudDemoJob")
@Component
public class XxlJobDemo extends IJobHandler {

    protected Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("eCloud demo job in the running, param is {}", param);
        LOG.debug("Hello XXL-JOB :)");
        return IJobHandler.SUCCESS;
    }
}
