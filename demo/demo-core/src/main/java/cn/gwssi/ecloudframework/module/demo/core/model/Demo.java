package cn.gwssi.ecloudframework.module.demo.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import cn.gwssi.ecloudframework.module.demo.api.model.IDemo;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.spring.annotation.FastJsonView;

import java.util.Date;

/**
 * 正文对象
 */
public class Demo extends BaseModel implements IDemo {

    protected String id ;
    protected String name ;
    protected String sex ;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String id() {
        return id;
    }

    @Override
    public String name() {
        return name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }
}
