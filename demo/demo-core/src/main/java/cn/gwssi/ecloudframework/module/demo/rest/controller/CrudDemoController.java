package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdGenerator;
import cn.gwssi.ecloudframework.base.core.util.RequestContext;
import cn.gwssi.ecloudframework.base.db.datasource.DbContextHolder;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.rest.BaseController;
import cn.gwssi.ecloudframework.module.demo.core.manager.DemoManager;
import cn.gwssi.ecloudframework.module.demo.core.model.Demo;
import cn.gwssi.ecloudframework.module.demo.core.model.DemoDto;
import cn.gwssi.ecloudframework.sys.api.jms.model.DefaultJmsDTO;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * 获取一条数据：
 *      /module/demo/crud/get
 * 增加&修改：
 *      /module/demo/crud/save
 * 删除：
 *      /module/demo/crud/remove
 * 查询列表：
 *     /module/demo/crud/listJson   通用表达式⬇️
 *           name_$VLK=02&sort=name_&order=ASC  ----->  name_ like '%02%' order by name_ ASC
 *           name_$VEQ=02  ----->  name_ = '02'
 *          id_ $NLT=5  ----->  id_ < 5
 *          id_ $NGT=5  ----->  id_ > 5
 *          ...  --->  QueryOP
 */
@RestController
@RequestMapping("/module/demo/crud/")
public class CrudDemoController extends BaseController<Demo> {

    @Autowired
    DemoManager demoManager ;


    /**
     * <pre>
     *   通用表达式入参：
     * 	1.参数字段命名规则。
     * 	a:参数名称^参数类型+条件 eg：a^VEQ 则表示，a字段是varchar类型，条件是eq ^后第一个参数为数据类型
     * 	b:参数名字^参数类型  eg：b^V则表示，b字段是varchar类型 用于sql拼参数
     * 	2.在这里构建的逻辑都是and逻辑。
     * 3.参数类型:V :字符串 varchar N:数字number D:日期date
     * 条件参数 枚举：QueryOP
     *
     * Test: http://127.0.0.1:8088/module/demo/crud/getListDemo?offset=0&limit=3&supportMobile$N=0&sort=name_&order=ASC
     *参数传递规则：字段名$字段类型（V，N，D）条件参数（QueryOP）
     *  处理逻辑：
     *      cn.gwssi.ecloudframework.base.rest.util.RequestUtil#handleRequestParam(javax.servlet.http.HttpServletRequest, cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter)
     *  排序：
     *  sort=name_  排序字段名称
     *  order=ASC   排序方法
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("/getListDemo")
    public PageResult getListDemo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        QueryFilter queryFilter = getQueryFilter(request);
        return new PageResult(demoManager.getListDemo(queryFilter));
    }


    /**
     * 通用表达式+混合入参
     * @param priority
     * @return
     * @throws Exception
     */
    @RequestMapping("/getListDemo2/{priority}")
    public PageResult getListDemo2(
            @PathVariable(required = false) String priority,
            @RequestParam(name = "xxxName", required = false) String xxxName,
            @RequestBody(required = false) DemoDto demoDto) throws Exception {

        //处理通用表达式
        QueryFilter queryFilter = getQueryFilter();

        //处理自定义参数
        queryFilter.addParamsFilter("priority", priority);  //PathVariable
        queryFilter.addParamsFilter("name", xxxName);       //RequestParam
        queryFilter.addParamsFilter("demoDto", demoDto);    //RequestBody

        return new PageResult(demoManager.getListDemo(queryFilter));
    }

    @CatchErr
    @RequestMapping("/changeDS")
    public ResultMsg<String> test4() {
        DbContextHolder.setDataSource("logDS","dmsql");
        List<Demo> all = demoManager.getAll();
        return getSuccessResult("ok") ;
    }

//    @RequestMapping("/batchCreate")
//    public void batchCreate() throws Exception {
//        List<Demo> demoList = new ArrayList<>() ;
//        for(int i=0; i < 5; i++){
//            Demo d = new Demo() ;
//            d.setId(System.currentTimeMillis()+""+i);
//            d.setName("hello~"+i);
//            demoList.add(d) ;
//        }
//        demoManager.batchCreate(demoList);
//    }

    @Override
    protected String getModelDesc() {
        return "demo";
    }
}
