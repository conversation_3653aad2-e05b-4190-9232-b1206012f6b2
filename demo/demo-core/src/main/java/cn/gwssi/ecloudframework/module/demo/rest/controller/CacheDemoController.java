package cn.gwssi.ecloudframework.module.demo.rest.controller;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.cache.ICache;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/module/demo/cache/")
public class CacheDemoController extends ControllerTools {

    @Resource
    ICache iCache ;

    @RequestMapping("/set")
    public ResultMsg<String> set() {
        iCache.add("demo", "hello world");
        return getSuccessResult("ok") ;
    }

    @RequestMapping("/get")
    public ResultMsg<String> get() {
        return getSuccessResult(iCache.getByKey("demo").toString()) ;
    }

}
