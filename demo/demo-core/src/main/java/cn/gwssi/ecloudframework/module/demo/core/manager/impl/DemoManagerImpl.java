package cn.gwssi.ecloudframework.module.demo.core.manager.impl;


import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.demo.core.dao.DemoDao;
import cn.gwssi.ecloudframework.module.demo.core.manager.DemoManager;
import cn.gwssi.ecloudframework.module.demo.core.model.Demo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("demoManager")
public class DemoManagerImpl extends BaseManager<String, Demo> implements DemoManager {

    @Resource
    DemoDao demoDao ;

    @Override
    public List<Demo> getListDemo(QueryFilter queryFilter) {
        return demoDao.getDemoList(queryFilter) ;
    }

}
