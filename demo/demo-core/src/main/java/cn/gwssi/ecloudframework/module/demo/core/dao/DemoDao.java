package cn.gwssi.ecloudframework.module.demo.core.dao;

import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.demo.core.model.Demo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface DemoDao extends BaseDao<String, Demo> {

    List<Demo> getDemoList(QueryFilter queryFilter) ;
}
