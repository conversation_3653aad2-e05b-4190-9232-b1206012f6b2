<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.demo.core.dao.DemoDao">
	<resultMap id="Demo" type="cn.gwssi.ecloudframework.module.demo.core.model.Demo">
		<id property="id" column="id_" jdbcType="VARCHAR"/>
		<id property="name" column="name_" jdbcType="VARCHAR"/>
		<id property="sex" column="sex_" jdbcType="VARCHAR"/>
	</resultMap>

	<insert id="create" parameterType="cn.gwssi.ecloudframework.module.demo.core.model.Demo">
		INSERT INTO module_demo
		(id_,name_)
		VALUES 
		(#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}
		 )
	</insert>


<!--	<update id="batchCreate"  parameterType="java.util.List">-->
<!--		<foreach collection="list" item="item"  separator=";">-->
<!--			INSERT INTO module_demo-->
<!--			(id_,name_)-->
<!--			VALUES-->
<!--			(#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}-->
<!--			)-->
<!--		</foreach>-->
<!--	</update>-->



	<select id="get" parameterType="java.lang.String" resultMap="Demo">
		SELECT * FROM module_demo
		WHERE
		id_=#{id}
	</select>

	<select id="query" parameterType="java.util.Map" resultMap="Demo">
		SELECT
		 id_,name_,sex_
		FROM module_demo
		<where>
			<if test="whereSql!=null">
				${whereSql}
			</if>
		</where>
		<if test="orderBySql!=null">
			ORDER BY ${orderBySql}
		</if>
		<if test="orderBySql==null">
			ORDER BY id_ DESC
		</if>
	</select>

	<update id="update" parameterType="cn.gwssi.ecloudframework.module.demo.core.model.Demo">
		UPDATE module_demo SET
		id_= #{id,jdbcType=VARCHAR},
		name_= #{name,jdbcType=VARCHAR}
		WHERE
		id_=#{id}
	</update>
	<delete id="remove" parameterType="java.lang.String">
		DELETE FROM module_demo
		WHERE
		id_=#{id}
	</delete>

	<select id="getDemoList" parameterType="java.util.Map" resultMap="Demo">
		select MODULE_DEMO.* from MODULE_DEMO
			left join BPM_TASK
			on MODULE_DEMO.ID_ = BPM_TASK.ID_
			WHERE BPM_TASK.STATUS_ = 'NORMAL'
			<if test="supportMobile!=null">
				AND SUPPORT_MOBILE_ = #{supportMobile}
			</if>
			<if test="name!=null">
				AND BPM_TASK.NAME_ = #{name}
			</if>
			<if test="priority!=null">
				AND BPM_TASK.PRIORITY_ = #{priority}
			</if>
			<if test="demoDto!=null and demoDto.defId!=null">
				AND BPM_TASK.DEF_ID_ = #{demoDto.defId}
			</if>
			<if test="orderBySql!=null">
				ORDER BY ${orderBySql}
			</if>
			<if test="orderBySql==null">
				ORDER BY id_ DESC
			</if>
	</select>
</mapper>