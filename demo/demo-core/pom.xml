<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>demo</artifactId>
        <groupId>cn.gwssi.ecloud</groupId>
        <version>0.3-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>demo-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>demo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-db</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>wf-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>sys-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>document-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>project-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
    </dependencies>
</project>