<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ecloud-module-efficiency</artifactId>
        <groupId>cn.gwssi.ecloud</groupId>
        <version>0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>efficiency-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>2.1.4.RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>efficiency-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-db</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>wf-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-goffice-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>component-j2cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>sys-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
    </dependencies>
</project>
