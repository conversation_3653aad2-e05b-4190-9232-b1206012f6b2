package cn.gwssi.ecloudframework.module.efficiency.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 资源指标数据实体类
 */
@Data
public class ResourceIndex extends BaseModel {
    
    /**
     * 主键
     */
    private String id;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 服务器群组
     */
    private String serverGroup;

    /**
     * 资源ID
     */
    private String cInstId;

    /**
     * 资源名称
     */
    private String ciName;

    /**
     * 资源IP
     */
    private String ipAddress;

    /**
     * CPU使用率
     */
    private Float cpuRate;

    /**
     * 内存使用率
     */
    private Float memoryRate;

    /**
     * 磁盘使用率
     */
    private Float diskRate;

    /**
     * 接收时间
     */
    private Date receiveTime;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 当日状态
     */
    private String sameDayStatus;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 采集频率
     */
    private Integer collectionFreq;

    /**
     * 扩展字段1
     */
    private String ext1;
}
