package cn.gwssi.ecloudframework.module.efficiency.core.manager.impl;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudframework.base.core.util.BeanCopierUtils;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.ResourceEfficiencyDao;
import cn.gwssi.ecloudframework.module.efficiency.core.dao.RuleServerRelationDao;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceEfficiencyManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源效能规则管理实现类
 */
@Service
public class ResourceEfficiencyManagerImpl extends BaseManager<String, ResourceEfficiency>
        implements ResourceEfficiencyManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceEfficiencyManagerImpl.class);
    
    @Resource
    private ResourceEfficiencyDao resourceEfficiencyDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public PageResult<ResourceEfficiency> list(PageQuery pageQuery, ResourceEfficiencyDTO rule) {
        PageHelperUtils.startPageAndOrderBy(pageQuery, "create_time desc");
        List<ResourceEfficiency> list = resourceEfficiencyDao.list(rule);
        return new PageResult<>(list);
    }

    @Override
    @Transactional
    public ResourceEfficiency saveOrUpdate(ResourceEfficiencyDTO ruleDTO) {
        try {
            ResourceEfficiency rule = BeanCopierUtils.transformBean(ruleDTO, ResourceEfficiency.class);
            
            // 处理服务器群组列表转JSON
            if (ruleDTO.getServerGroupList() != null && !ruleDTO.getServerGroupList().isEmpty()) {
                rule.setServerGroups(objectMapper.writeValueAsString(ruleDTO.getServerGroupList()));
            }
            
            // 验证规则配置
            if (!validateRule(rule)) {
                throw new RuntimeException("规则配置验证失败");
            }
            
            if (StringUtils.isBlank(rule.getId())) {
                // 新增
                resourceEfficiencyDao.create(rule);
            } else {
                // 更新
                resourceEfficiencyDao.update(rule);
            }
            
            return rule;
        } catch (Exception e) {
            LOGGER.error("保存规则失败", e);
            throw new RuntimeException("保存规则失败：" + e.getMessage());
        }
    }

    @Override
    public ResourceEfficiency getById(String id) {
        return resourceEfficiencyDao.get(id);
    }

    @Override
    public ResourceEfficiency getByRuleName(String ruleName) {
        return resourceEfficiencyDao.getByRuleName(ruleName);
    }

//    @Override
//    @Transactional
//    public void saveBatch(List<ResourceEfficiency> rules) {
//        if (rules != null && !rules.isEmpty()) {
//            resourceEfficiencyDao.insertBatch(rules);
//        }
//    }

    @Override
    @Transactional
    public ResourceEfficiency updateRuleStatus(String ruleId, Integer status) {
        ResourceEfficiency rule = resourceEfficiencyDao.get(ruleId);
        if (rule != null) {
            // 设置规则状态（假设实体类有 status 字段）
            rule.setStatus(status);
            resourceEfficiencyDao.update(rule);
        }
        return rule;
    }

    @Override
    @Transactional
    public int delete(List<String> ruleIds) {
        return resourceEfficiencyDao.delete(ruleIds);
    }

    @Override
    public List<ResourceEfficiency> getEnabledRules() {
        return resourceEfficiencyDao.getEnabledRules();
    }

    @Override
    public boolean validateRule(ResourceEfficiency rule) {
        if (rule == null) {
            return false;
        }
        
        // 验证规则名称
        if (StringUtils.isBlank(rule.getRuleName())) {
            return false;
        }
        
        // 验证阈值配置
        if (rule.getCpuLowThreshold() == null || rule.getCpuHighThreshold() == null ||
            rule.getMemoryLowThreshold() == null || rule.getMemoryHighThreshold() == null ||
            rule.getDiskLowThreshold() == null || rule.getDiskHighThreshold() == null) {
            return false;
        }
        
        // 验证阈值范围
        if (rule.getCpuLowThreshold() >= rule.getCpuHighThreshold() ||
            rule.getMemoryLowThreshold() >= rule.getMemoryHighThreshold() ||
            rule.getDiskLowThreshold() >= rule.getDiskHighThreshold()) {
            return false;
        }
        
        // 验证时间格式
        if (StringUtils.isBlank(rule.getStartTime()) || StringUtils.isBlank(rule.getEndTime())) {
            return false;
        }
        
        return true;
    }

//    @Override
//    @Transactional
//    public ResourceEfficiency copyRule(String sourceRuleId, String newRuleName) {
//        try {
//            ResourceEfficiency sourceRule = resourceEfficiencyDao.get(sourceRuleId);
//            if (sourceRule == null) {
//                throw new RuntimeException("源规则不存在");
//            }
//
//            ResourceEfficiency newRule = BeanCopierUtils.transformBean(sourceRule, ResourceEfficiency.class);
//            newRule.setId(null);
//            newRule.setRuleName(newRuleName);
//
//            resourceEfficiencyDao.create(newRule);
//            return newRule;
//        } catch (Exception e) {
//            LOGGER.error("复制规则失败", e);
//            throw new RuntimeException("复制规则失败：" + e.getMessage());
//        }
//    }
}
