package cn.gwssi.ecloudframework.module.efficiency.core.model;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 服务器磁盘指标实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ServerDiskIndex extends BaseModel {
    
    /**
     * 关联的资源指标ID
     */
    private String resourceIndexId;
    
    /**
     * 磁盘名称
     */
    private String diskName;
    
    /**
     * 磁盘使用率
     */
    private Float diskRate;
    
    /**
     * 服务器群组
     */
    private String serverGroup;
    
    /**
     * 资源ID
     */
    private String cInstId;
    
    /**
     * 资源名称
     */
    private String ciName;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 接收时间
     */
    private Date receiveTime;
}
