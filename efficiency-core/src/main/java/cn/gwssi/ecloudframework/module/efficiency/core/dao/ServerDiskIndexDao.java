package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ServerDiskIndex;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 服务器磁盘指标数据访问接口
 */
public interface ServerDiskIndexDao extends BaseDao<String, ServerDiskIndex> {
    
    /**
     * 根据资源指标ID查询磁盘数据
     * @param resourceIndexId 资源指标ID
     * @return 磁盘数据列表
     */
    List<ServerDiskIndex> getByResourceIndexId(@Param("resourceIndexId") String resourceIndexId);
    
    /**
     * 根据资源ID和时间范围查询磁盘数据
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 磁盘数据列表
     */
    List<ServerDiskIndex> getByCInstIdAndTimeRange(@Param("cInstId") String cInstId, 
                                                   @Param("startTime") Date startTime, 
                                                   @Param("endTime") Date endTime);
    
    /**
     * 批量插入磁盘数据
     * @param diskIndexList 磁盘数据列表
     */
    void insertBatch(@Param("list") List<ServerDiskIndex> diskIndexList);
    
    /**
     * 根据资源指标ID删除磁盘数据
     * @param resourceIndexId 资源指标ID
     * @return 删除的记录数
     */
    int deleteByResourceIndexId(@Param("resourceIndexId") String resourceIndexId);
    
    /**
     * 根据资源指标ID列表批量删除磁盘数据
     * @param resourceIndexIds 资源指标ID列表
     * @return 删除的记录数
     */
    int deleteByResourceIndexIds(@Param("resourceIndexIds") List<String> resourceIndexIds);
    
    /**
     * 计算磁盘阈值统计
     * @param cInstId 资源ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param diskHighThreshold 磁盘高阈值
     * @param diskLowThreshold 磁盘低阈值
     * @return 统计结果
     */
    Map<String, Object> sumDiskThresholdStats(@Param("cInstId") String cInstId,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime,
                                              @Param("diskHighThreshold") Integer diskHighThreshold,
                                              @Param("diskLowThreshold") Integer diskLowThreshold);
    
    /**
     * 清理历史数据
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    int cleanHistoryData(@Param("beforeTime") Date beforeTime);
}
