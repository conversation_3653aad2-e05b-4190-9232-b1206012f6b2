package cn.gwssi.ecloudframework.module.efficiency.core.dao;

import cn.gwssi.ecloudframework.base.dao.BaseDao;
import cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 规则服务器关系DAO接口
 */
@MapperScan
public interface RuleServerRelationDao extends BaseDao<String, RuleServerRelation> {
    
    /**
     * 获取规则服务器关系列表
     * @param relation 查询参数
     * @return 关系列表
     */
    List<RuleServerRelationDTO> list(RuleServerRelationDTO relation);

    /**
     * 批量插入关系数据
     * @param relations 关系数据列表
     */
    void insertBatch(List<RuleServerRelation> relations);

    /**
     * 批量更新关系数据
     * @param relations 关系数据列表
     */
    void updateBatch(List<RuleServerRelation> relations);

    /**
     * 根据规则ID获取关联的服务器列表
     * @param ruleId 规则ID
     * @return 服务器关系列表
     */
    List<RuleServerRelation> getByRuleId(@Param("ruleId") String ruleId);

    /**
     * 根据资源ID获取关联的规则列表
     * @param cInstId 资源ID
     * @return 规则关系列表
     */
    List<RuleServerRelation> getByCInstId(@Param("cInstId") String cInstId);

    /**
     * 根据规则ID删除关系
     * @param ruleId 规则ID
     * @return 删除的记录数
     */
    int deleteByRuleId(@Param("ruleId") String ruleId);

    /**
     * 根据规则ID列表批量删除关系
     * @param ruleIds 规则ID列表
     * @return 删除的记录数
     */
    int deleteByRuleIds(@Param("ruleIds") List<String> ruleIds);

    /**
     * 根据资源ID删除关系
     * @param cInstId 资源ID
     * @return 删除的记录数
     */
    int deleteByCInstId(@Param("cInstId") String cInstId);

    /**
     * 检查规则和服务器的关系是否存在
     * @param ruleId 规则ID
     * @param cInstId 资源ID
     * @return 是否存在
     */
    boolean existsRelation(@Param("ruleId") String ruleId, @Param("cInstId") String cInstId);

    /**
     * 根据服务器群组获取服务器列表
     * @param serverGroup 服务器群组
     * @return 服务器关系列表
     */
    List<RuleServerRelation> getByServerGroup(@Param("serverGroup") String serverGroup);
}
