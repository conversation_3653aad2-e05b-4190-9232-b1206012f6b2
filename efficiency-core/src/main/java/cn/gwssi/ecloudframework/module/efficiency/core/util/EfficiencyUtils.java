package cn.gwssi.ecloudframework.module.efficiency.core.util;

import cn.gwssi.ecloudframework.module.efficiency.api.constant.EfficiencyConstants;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 系统能效工具类
 */
public class EfficiencyUtils {
    
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_TIME_FORMAT);
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_DATE_FORMAT);
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat(EfficiencyConstants.DEFAULT_DATETIME_FORMAT);

    /**
     * 解析业务时间
     * @param date 日期
     * @param timeStr 时间字符串（HH:mm格式）
     * @return 解析后的时间
     */
    public static Date parseBusinessTime(Date date, String timeStr) {
        if (date == null || StringUtils.isBlank(timeStr)) {
            return null;
        }
        
        try {
            String dateStr = DATE_FORMAT.format(date);
            String datetimeStr = dateStr + " " + timeStr + ":00";
            return DATETIME_FORMAT.parse(datetimeStr);
        } catch (ParseException e) {
            throw new RuntimeException("解析业务时间失败：" + timeStr, e);
        }
    }

}
