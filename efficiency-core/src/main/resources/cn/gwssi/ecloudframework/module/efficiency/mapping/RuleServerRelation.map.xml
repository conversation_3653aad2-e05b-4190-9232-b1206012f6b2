<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.efficiency.core.dao.RuleServerRelationDao">
    <resultMap id="RuleServerRelation" type="cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="resourceType" column="resource_type" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="RuleServerRelationDTO" type="cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/>
        <result property="serverGroup" column="server_group" jdbcType="VARCHAR"/>
        <result property="cInstId" column="c_inst_id" jdbcType="VARCHAR"/>
        <result property="ciName" column="ci_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="resourceType" column="resource_type" jdbcType="VARCHAR"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_id, server_group, c_inst_id, ci_name, ip_address, resource_type,
        create_user, create_by, create_time, update_user, update_by, update_time
    </sql>

    <select id="get" parameterType="java.lang.String" resultMap="RuleServerRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM s_rule_server_relation
        WHERE id = #{id}
    </select>

    <select id="list" parameterType="cn.gwssi.ecloudframework.module.efficiency.api.model.RuleServerRelationDTO" 
            resultMap="RuleServerRelationDTO">
        SELECT rsr.<include refid="Base_Column_List"/>, re.rule_name
        FROM s_rule_server_relation rsr
        LEFT JOIN s_resource_efficiency re ON rsr.rule_id = re.id
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND rsr.rule_id = #{ruleId}
            </if>
            <if test="cInstId != null and cInstId != ''">
                AND rsr.c_inst_id = #{cInstId}
            </if>
            <if test="ciName != null and ciName != ''">
                AND rsr.ci_name LIKE CONCAT('%', #{ciName}, '%')
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                AND rsr.ip_address LIKE CONCAT('%', #{ipAddress}, '%')
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                AND rsr.server_group = #{serverGroup}
            </if>
            <if test="resourceType != null and resourceType != ''">
                AND rsr.resource_type = #{resourceType}
            </if>
        </where>
        ORDER BY rsr.create_time DESC
    </select>

    <select id="getByRuleId" parameterType="java.lang.String" resultMap="RuleServerRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM s_rule_server_relation
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND rule_id = #{ruleId}
            </if>
        </where>
        ORDER BY ci_name
    </select>

    <select id="getByCInstId" parameterType="java.lang.String" resultMap="RuleServerRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM s_rule_server_relation
        <where>
            <if test="cInstId != null and cInstId != ''">
                AND c_inst_id = #{cInstId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getByServerGroup" parameterType="java.lang.String" resultMap="RuleServerRelation">
        SELECT <include refid="Base_Column_List"/>
        FROM s_rule_server_relation
        <where>
            <if test="serverGroup != null and serverGroup != ''">
                AND server_group = #{serverGroup}
            </if>
        </where>
        ORDER BY ci_name
    </select>

    <select id="existsRelation" resultType="boolean">
        SELECT COUNT(*) &gt; 0
        FROM s_rule_server_relation
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND rule_id = #{ruleId}
            </if>
            <if test="cInstId != null and cInstId != ''">
                AND c_inst_id = #{cInstId}
            </if>
        </where>
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation">
        INSERT INTO s_rule_server_relation (
            id, rule_id, server_group, c_inst_id, ci_name, ip_address, resource_type,
            create_user, create_by, create_time, update_user, update_by, update_time
        ) VALUES (
            #{id}, #{ruleId}, #{serverGroup}, #{cInstId}, #{ciName}, #{ipAddress}, #{resourceType},
            #{createUser}, #{createBy}, #{createTime}, #{updateUser}, #{updateBy}, #{updateTime}
        )
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.RuleServerRelation">
        UPDATE s_rule_server_relation
        <set>
            <if test="ruleId != null and ruleId != ''">
                rule_id = #{ruleId},
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                server_group = #{serverGroup},
            </if>
            <if test="cInstId != null and cInstId != ''">
                c_inst_id = #{cInstId},
            </if>
            <if test="ciName != null and ciName != ''">
                ci_name = #{ciName},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address = #{ipAddress},
            </if>
            <if test="resourceType != null and resourceType != ''">
                resource_type = #{resourceType},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM s_rule_server_relation WHERE id = #{id}
    </delete>

    <delete id="deleteByRuleId" parameterType="java.lang.String">
        DELETE FROM s_rule_server_relation WHERE rule_id = #{ruleId}
    </delete>

    <delete id="deleteByRuleIds" parameterType="java.util.List">
        DELETE FROM s_rule_server_relation WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <delete id="deleteByCInstId" parameterType="java.lang.String">
        DELETE FROM s_rule_server_relation WHERE c_inst_id = #{cInstId}
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO s_rule_server_relation (
            id, rule_id, server_group, c_inst_id, ci_name, ip_address, resource_type,
            create_user, create_by, create_time, update_user, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.ruleId}, #{item.serverGroup}, #{item.cInstId}, #{item.ciName}, #{item.ipAddress}, #{item.resourceType},
             #{item.createUser}, #{item.createBy}, #{item.createTime}, #{item.updateUser}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE s_rule_server_relation SET
                rule_id = #{item.ruleId},
                server_group = #{item.serverGroup},
                c_inst_id = #{item.cInstId},
                ci_name = #{item.ciName},
                ip_address = #{item.ipAddress},
                resource_type = #{item.resourceType},
                update_user = #{item.updateUser},
                update_by = #{item.updateBy},
                update_time = #{item.updateTime}
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>
