<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.ecloudframework.module.efficiency.core.dao.ServerDiskIndexDao">

    <resultMap id="ServerDiskIndex" type="cn.gwssi.ecloudframework.module.efficiency.core.model.ServerDiskIndex">
        <id column="id" property="id"/>
        <result column="resource_index_id" property="resourceIndexId"/>
        <result column="disk_name" property="diskName"/>
        <result column="disk_rate" property="diskRate"/>
        <result column="server_group" property="serverGroup"/>
        <result column="c_inst_id" property="cInstId"/>
        <result column="ci_name" property="ciName"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="receive_time" property="receiveTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, resource_index_id, disk_name, disk_rate, server_group, c_inst_id, ci_name, ip_address, receive_time
    </sql>

    <select id="get" parameterType="java.lang.String" resultMap="ServerDiskIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_server_disk_index
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
        </where>
    </select>

    <select id="list" resultMap="ServerDiskIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_server_disk_index sdi
        <where>
            <if test="resourceIndexId != null and resourceIndexId != ''">
                AND sdi.resource_index_id = #{resourceIndexId}
            </if>
            <if test="diskName != null and diskName != ''">
                AND sdi.disk_name = #{diskName}
            </if>
            <if test="cInstId != null and cInstId != ''">
                AND sdi.c_inst_id = #{cInstId}
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                AND sdi.server_group = #{serverGroup}
            </if>
            <if test="receiveTime != null">
                AND sdi.receive_time = #{receiveTime}
            </if>
        </where>
        ORDER BY sdi.receive_time DESC, sdi.disk_name
    </select>

    <select id="getByResourceIndexId" parameterType="java.lang.String" resultMap="ServerDiskIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_server_disk_index
        <where>
            <if test="resourceIndexId != null and resourceIndexId != ''">
                AND resource_index_id = #{resourceIndexId}
            </if>
        </where>
        ORDER BY disk_name
    </select>

    <select id="getByCInstIdAndTimeRange" resultMap="ServerDiskIndex">
        SELECT <include refid="Base_Column_List"/>
        FROM s_server_disk_index
        <where>
            <if test="cInstId != null and cInstId != ''">
                AND c_inst_id = #{cInstId}
            </if>
            <if test="startTime != null">
                AND receive_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND receive_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY receive_time DESC, disk_name
    </select>

    <select id="sumDiskThresholdStats" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN disk_rate &gt;= #{diskHighThreshold} THEN 1 END) AS diskHighCount,
            COUNT(CASE WHEN disk_rate &lt;= #{diskLowThreshold} THEN 1 END) AS diskLowCount,
            COUNT(*) AS totalDiskCount
        FROM s_server_disk_index
        <where>
            <if test="cInstId != null and cInstId != ''">
                AND c_inst_id = #{cInstId}
            </if>
            <if test="startTime != null">
                AND receive_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND receive_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <insert id="create" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ServerDiskIndex">
        INSERT INTO s_server_disk_index (
            id, resource_index_id, disk_name, disk_rate, server_group, c_inst_id, ci_name, ip_address, receive_time
        ) VALUES (
            #{id}, #{resourceIndexId}, #{diskName}, #{diskRate}, #{serverGroup}, #{cInstId}, #{ciName}, #{ipAddress}, #{receiveTime}
        )
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO s_server_disk_index (
            id, resource_index_id, disk_name, disk_rate, server_group, c_inst_id, ci_name, ip_address, receive_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.resourceIndexId}, #{item.diskName}, #{item.diskRate}, #{item.serverGroup}, 
             #{item.cInstId}, #{item.ciName}, #{item.ipAddress}, #{item.receiveTime})
        </foreach>
    </insert>

    <update id="update" parameterType="cn.gwssi.ecloudframework.module.efficiency.core.model.ServerDiskIndex">
        UPDATE s_server_disk_index
        <set>
            <if test="resourceIndexId != null and resourceIndexId != ''">
                resource_index_id = #{resourceIndexId},
            </if>
            <if test="diskName != null and diskName != ''">
                disk_name = #{diskName},
            </if>
            <if test="diskRate != null">
                disk_rate = #{diskRate},
            </if>
            <if test="serverGroup != null and serverGroup != ''">
                server_group = #{serverGroup},
            </if>
            <if test="cInstId != null and cInstId != ''">
                c_inst_id = #{cInstId},
            </if>
            <if test="ciName != null and ciName != ''">
                ci_name = #{ciName},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address = #{ipAddress},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="remove" parameterType="java.lang.String">
        DELETE FROM s_server_disk_index WHERE id = #{id}
    </delete>

    <delete id="deleteByResourceIndexId" parameterType="java.lang.String">
        DELETE FROM s_server_disk_index WHERE resource_index_id = #{resourceIndexId}
    </delete>

    <delete id="deleteByResourceIndexIds" parameterType="java.util.List">
        DELETE FROM s_server_disk_index WHERE resource_index_id IN
        <foreach collection="resourceIndexIds" item="resourceIndexId" open="(" separator="," close=")">
            #{resourceIndexId}
        </foreach>
    </delete>

    <delete id="cleanHistoryData" parameterType="java.util.Date">
        DELETE FROM s_server_disk_index WHERE receive_time &lt; #{beforeTime}
    </delete>

</mapper>
