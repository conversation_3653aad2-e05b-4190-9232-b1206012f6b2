package cn.gwssi.ecloud;

import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceResultDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceResultManager;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class ResourceResultControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResourceResultManager resourceResultManager;

    @Test
    public void chain() throws Exception {
        this.testGetResultList();
        this.testGetEfficiencyReport();
        this.testGetLoadResources();
        this.testCleanHistoryData();
    }

    @Test
    public void testGetResultList() throws Exception {
        ResourceResultDTO queryDTO = new ResourceResultDTO();
        queryDTO.setServerGroup("测试服务器组ABC");
        queryDTO.setCiName("测试服务器ABC");

        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "10")
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.rows").exists())
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.total").isNumber())
                .andReturn();
    }

    @Test
    public void testGetEfficiencyReport() throws Exception {
        MvcResult reportResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/report")
                                .param("startDate", "2023-01-01")
                                .param("endDate", "2023-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
    }

    @Test
    public void testGetLoadResources() throws Exception {
        MvcResult loadResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/load-resources")
                                .param("startDate", "2023-01-01")
                                .param("endDate", "2023-12-31")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.highLoadResources").exists())
                .andExpect(jsonPath("$.data.highLoadResources").isArray())
                .andExpect(jsonPath("$.data.lowLoadResources").exists())
                .andExpect(jsonPath("$.data.lowLoadResources").isArray())
                .andExpect(jsonPath("$.data.highLoadCount").exists())
                .andExpect(jsonPath("$.data.highLoadCount").isNumber())
                .andExpect(jsonPath("$.data.lowLoadCount").exists())
                .andExpect(jsonPath("$.data.lowLoadCount").isNumber())
                .andReturn();
    }

    @Test
    public void testCleanHistoryData() throws Exception {
        MvcResult cleanResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/clean")
                                .param("beforeDate", "2023-01-01")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
    }

    @Test
    public void testGetEfficiencyReportWithDifferentDateRange() throws Exception {
        // 测试不同的日期范围
        MvcResult reportResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/report")
                                .param("startDate", "2023-06-01")
                                .param("endDate", "2023-06-30")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
    }

    @Test
    public void testGetLoadResourcesWithShortDateRange() throws Exception {
        // 测试短时间范围的负荷资源查询
        MvcResult loadResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/result/load-resources")
                                .param("startDate", "2023-12-01")
                                .param("endDate", "2023-12-07")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andReturn();
    }

    @Test
    public void testGetResultListWithComplexQuery() throws Exception {
        // 测试复杂查询条件
        ResourceResultDTO queryDTO = new ResourceResultDTO();
        queryDTO.setServerGroup("测试服务器组ABC");
        queryDTO.setCiName("测试服务器ABC");
        queryDTO.setApplicationName("测试应用ABC");
        queryDTO.setCpuHighThreshold("是");
        queryDTO.setMemoryHighThreshold("否");
        queryDTO.setDiskHighThreshold("是");

        // 测试复杂查询条件
        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/result/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "20")
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.rows").exists())
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.total").isNumber())
                .andReturn();
    }
}
