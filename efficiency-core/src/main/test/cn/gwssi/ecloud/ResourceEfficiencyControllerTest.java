package cn.gwssi.ecloud;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceEfficiencyDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceEfficiencyManager;
import cn.gwssi.ecloudframework.module.efficiency.core.model.ResourceEfficiency;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest
@AutoConfigureMockMvc
public class ResourceEfficiencyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResourceEfficiencyManager resourceEfficiencyManager;

    private String savedRuleId;

    @Test
    public void chain() throws Exception {
        this.testSaveRule();
        this.testGetRuleList();
        this.testGetRuleDetail();
        this.testValidateRuleName();
        this.testUpdateRuleStatus();
        this.testDeleteRule();
    }

    @Test
    public void testSaveRule() throws Exception {
        ResourceEfficiencyDTO ruleDTO = new ResourceEfficiencyDTO();
        ruleDTO.setRuleName("测试规则ABC");
        ruleDTO.setCpuLowThreshold(20);
        ruleDTO.setCpuHighThreshold(80);
        ruleDTO.setMemoryLowThreshold(30);
        ruleDTO.setMemoryHighThreshold(85);
        ruleDTO.setDiskLowThreshold(40);
        ruleDTO.setDiskHighThreshold(90);
        ruleDTO.setStartTime("09:00");
        ruleDTO.setEndTime("18:00");
        ruleDTO.setHighRate(70);
        ruleDTO.setLowRate(30);
        ruleDTO.setServerGroups("测试服务器组ABC");
        ruleDTO.setStatus(1);

        MvcResult createResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/rule/save")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(ruleDTO))
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data").isNotEmpty())
                .andReturn();

        String responseContent = createResult.getResponse().getContentAsString();
        this.savedRuleId = (String) JSONPath.read(responseContent, "$.data");
    }

    @Test
    public void testGetRuleList() throws Exception {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNo(1);
        pageQuery.setPageSize(10);

        ResourceEfficiencyDTO queryDTO = new ResourceEfficiencyDTO();
        queryDTO.setRuleName("测试规则ABC");

        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/rule/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "10")
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.rows").exists())
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.total").isNumber())
                .andReturn();
    }

    @Test
    public void testGetRuleDetail() throws Exception {
        String testId = this.savedRuleId != null ? this.savedRuleId : "test-rule-id";

        MvcResult getResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/rule/get/" + testId)
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.ruleName").value("测试规则ABC"))
                .andExpect(jsonPath("$.data.serverGroups").value("测试服务器组ABC"))
                .andReturn();
    }

    @Test
    public void testValidateRuleName() throws Exception {
        MvcResult validateResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/module/efficiency/rule/validate/name")
                                .param("ruleName", "测试规则ABC")
                                .param("excludeId", this.savedRuleId)
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data").isBoolean())
                .andReturn();
    }

    @Test
    public void testUpdateRuleStatus() throws Exception {
        String testId = this.savedRuleId != null ? this.savedRuleId : "test-rule-id";

        MvcResult statusResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/rule/status/" + testId)
                                .param("status", "0")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();
    }

    @Test
    public void testDeleteRule() throws Exception {
        String testId = this.savedRuleId != null ? this.savedRuleId : "test-rule-id";
        List<String> ids = Arrays.asList(testId);

        MvcResult deleteResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/rule/delete")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(ids))
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();
    }
}
