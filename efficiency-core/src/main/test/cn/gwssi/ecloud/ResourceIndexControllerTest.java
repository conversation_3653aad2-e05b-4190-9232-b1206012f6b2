package cn.gwssi.ecloud;

import cn.gwssi.ecloudframework.module.efficiency.api.model.ResourceIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.api.model.ServerDiskIndexDTO;
import cn.gwssi.ecloudframework.module.efficiency.core.manager.ResourceIndexManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class ResourceIndexControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResourceIndexManager resourceIndexManager;

    private String savedIndexId;

    @Test
    public void chain() throws Exception {
        this.testSaveIndex();
        this.testBatchSaveIndex();
        this.testGetIndexList();
        this.testCleanHistoryData();
        this.testDeleteIndex();
    }

    @Test
    public void testSaveIndex() throws Exception {
        ResourceIndexDTO indexDTO = new ResourceIndexDTO();
        indexDTO.setRuleId("test-rule-id-ABC");
        indexDTO.setServerGroup("测试服务器组ABC");
        indexDTO.setCInstId("test-server-001-ABC");
        indexDTO.setCiName("测试服务器ABC");
        indexDTO.setIpAddress("*************");
        indexDTO.setCpuRate(65.5f);
        indexDTO.setMemoryRate(75.2f);
        indexDTO.setDiskRate(45.8f);
        indexDTO.setReceiveTime(new Date());
        indexDTO.setSendTime(new Date());
        indexDTO.setSameDayStatus("正常");
        indexDTO.setApplicationName("测试应用ABC");
        indexDTO.setCollectionFreq(5);
        indexDTO.setExt1("扩展信息ABC");

        // 添加磁盘数据
        ServerDiskIndexDTO diskIndex1 = new ServerDiskIndexDTO();
        diskIndex1.setDiskName("C:");
        diskIndex1.setDiskRate(45.8f);
        diskIndex1.setServerGroup("测试服务器组ABC");
        diskIndex1.setCInstId("test-server-001-ABC");
        diskIndex1.setCiName("测试服务器ABC");
        diskIndex1.setIpAddress("*************");
        diskIndex1.setReceiveTime(new Date());

        ServerDiskIndexDTO diskIndex2 = new ServerDiskIndexDTO();
        diskIndex2.setDiskName("D:");
        diskIndex2.setDiskRate(32.1f);
        diskIndex2.setServerGroup("测试服务器组ABC");
        diskIndex2.setCInstId("test-server-001-ABC");
        diskIndex2.setCiName("测试服务器ABC");
        diskIndex2.setIpAddress("*************");
        diskIndex2.setReceiveTime(new Date());

        indexDTO.setDiskIndexList(Arrays.asList(diskIndex1, diskIndex2));

        MvcResult createResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/index/save")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(indexDTO))
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data").isNotEmpty())
                .andReturn();

        String responseContent = createResult.getResponse().getContentAsString();
        this.savedIndexId = (String) JSONPath.read(responseContent, "$.data");
    }

    @Test
    public void testBatchSaveIndex() throws Exception {
        ResourceIndexDTO indexDTO1 = new ResourceIndexDTO();
        indexDTO1.setRuleId("test-rule-id-ABC");
        indexDTO1.setServerGroup("测试服务器组ABC");
        indexDTO1.setCInstId("test-server-002-ABC");
        indexDTO1.setCiName("测试服务器2ABC");
        indexDTO1.setIpAddress("*************");
        indexDTO1.setCpuRate(55.3f);
        indexDTO1.setMemoryRate(68.7f);
        indexDTO1.setDiskRate(38.9f);
        indexDTO1.setReceiveTime(new Date());
        indexDTO1.setSendTime(new Date());
        indexDTO1.setSameDayStatus("正常");
        indexDTO1.setApplicationName("测试应用ABC");
        indexDTO1.setCollectionFreq(5);

        ResourceIndexDTO indexDTO2 = new ResourceIndexDTO();
        indexDTO2.setRuleId("test-rule-id-ABC");
        indexDTO2.setServerGroup("测试服务器组ABC");
        indexDTO2.setCInstId("test-server-003-ABC");
        indexDTO2.setCiName("测试服务器3ABC");
        indexDTO2.setIpAddress("*************");
        indexDTO2.setCpuRate(72.1f);
        indexDTO2.setMemoryRate(81.4f);
        indexDTO2.setDiskRate(52.6f);
        indexDTO2.setReceiveTime(new Date());
        indexDTO2.setSendTime(new Date());
        indexDTO2.setSameDayStatus("正常");
        indexDTO2.setApplicationName("测试应用ABC");
        indexDTO2.setCollectionFreq(5);

        List<ResourceIndexDTO> indexList = Arrays.asList(indexDTO1, indexDTO2);

        MvcResult batchResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/index/batch/save")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(indexList))
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
    }

    @Test
    public void testGetIndexList() throws Exception {
        ResourceIndexDTO queryDTO = new ResourceIndexDTO();
        queryDTO.setServerGroup("测试服务器组ABC");

        MvcResult listResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/index/list")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(JSON.toJSONString(queryDTO))
                                .param("pageNo", "1")
                                .param("pageSize", "10")
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.rows").exists())
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.total").exists())
                .andExpect(jsonPath("$.total").isNumber())
                .andReturn();
    }

    @Test
    public void testCleanHistoryData() throws Exception {
        MvcResult cleanResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/module/efficiency/index/clean")
                                .param("beforeTime", "2023-01-01 00:00:00")
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andReturn();
    }

    @Test
    public void testDeleteIndex() throws Exception {
        String testId = this.savedIndexId != null ? this.savedIndexId : "test-index-id";

        MvcResult deleteResult = mockMvc.perform(
                        MockMvcRequestBuilders.delete("/module/efficiency/index/delete/" + testId)
                                .contentType(MediaType.APPLICATION_JSON)
                ).andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();
    }
}
