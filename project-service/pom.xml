<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>newgwuomp-backend</artifactId>
        <groupId>cn.gwssi.ecloud</groupId>
        <version>0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>project-service</artifactId>
    <repositories>
        <repository>
            <id>beacon</id>
            <name>beacon</name>
            <url>http://nexus.e.cloud:8081/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>beacon-central</id>
            <name>beacon-central</name>
            <url>http://nexus.e.cloud:8081/repository/maven-central/</url>
        </repository>
        <repository>
            <id>beacon-snapshot</id>
            <name>beacon-snapshot</name>
            <url>http://nexus.e.cloud:8081/repository/maven-snapshots/</url>
        </repository>
    </repositories>
    <dependencies>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>eworker-ui</artifactId>
            <version>develop.467</version>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>micro-portal-normal-ui</artifactId>
            <version>master.158</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-base-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-security-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-sysmq-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>form-rest</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>bus-rest</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4-runtime</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-sys-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-wf-starter</artifactId>
        </dependency>

        <!-- API 服务 -->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-api-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-api-bpm</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>component-j2cache</artifactId>
        </dependency>

        <!--运维资产模块-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>operation-assets-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>operation-assets-core</artifactId>
        </dependency>
        <!--告警模块-->
<!--        &lt;!&ndash;监控模块&ndash;&gt;-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>monitor-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>monitor-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>ecloud-api-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <!-- 大屏管理-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>visual-core</artifactId>
        </dependency>
        <!-- 日志管理 -->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>log-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>log-core</artifactId>
        </dependency>
        <!-- kv-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>kv-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>kv-core</artifactId>
        </dependency>
        <!-- 前端公共服务-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>frontend-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>frontend-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-custom-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>org-custom-core</artifactId>
        </dependency>

        <!-- 智能手表-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>bracelet-core</artifactId>
        </dependency>

        <!-- 人员管理-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>staff-pool-core</artifactId>
        </dependency>

        <!-- 模板服务-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>template-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>message-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>component-j2cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>base-rest-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>security-rest-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>sys-rest-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>bus-rest-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>form-rest-platform</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>wf-rest-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.6.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.6.1</version>
        </dependency>
        <!--数据运维-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>db-assets-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dms-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>restclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>news-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>news-core</artifactId>
        </dependency>
        <!-- 常用意见 -->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>opinion-core</artifactId>
        </dependency>

        <!-- itsm模块 -->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>itsm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>itsm-core</artifactId>
        </dependency>


        <!-- 日程管理-->
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>schedule-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>schedule-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>sso-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>cmdb-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>app-e-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dialog-flow-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dialog-flow</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dialog-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>dialog-llm-core</artifactId>
        </dependency>
        <!-- chat-->
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.ecloud</groupId>-->
<!--            <artifactId>chat-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.ecloud</groupId>-->
<!--            <artifactId>chat-rag</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.ecloud</groupId>-->
<!--            <artifactId>omlog-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.ecloud</groupId>-->
<!--            <artifactId>km-core</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>cn.gwssi.ecloud</groupId>-->
<!--            <artifactId>km-api</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>inspection-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>inspection-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>efficiency-api</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.gwssi.ecloud</groupId>
            <artifactId>efficiency-core</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
