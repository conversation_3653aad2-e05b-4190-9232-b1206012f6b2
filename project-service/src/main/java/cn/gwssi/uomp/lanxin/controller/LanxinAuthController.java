package cn.gwssi.uomp.lanxin.controller;

import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.jwt.JWTService;
import cn.gwssi.ecloudframework.org.api.model.dto.UserDTO;
import cn.gwssi.ecloudframework.org.api.service.UserService;
import cn.gwssi.uomp.lanxin.constant.LxPlatFormStatusCode;
import cn.gwssi.uomp.lanxin.dao.LanxinMapper;
import cn.gwssi.uomp.lanxin.model.V1UsersFetchData;
import cn.gwssi.uomp.lanxin.service.ILxMessageService;
import cn.gwssi.uomp.lanxin.service.LxApiOauthService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/lanxin")
public class LanxinAuthController {

    private Logger logger = LoggerFactory.getLogger(LanxinAuthController.class);

    @Autowired
    private LxApiOauthService lxApiOauthService;

    @Resource
    private UserService userService;

    @Resource
    private JWTService jwtService;

    @Resource
    private LanxinMapper lanxinMapper;

    @Autowired
    private ILxMessageService lxMessageService;


    @ApiOperation("获取权限验证参数")
    @GetMapping(value = "/config/auth")
    public ResultMsg<JSONObject> getConfigAuthData(@RequestParam("url") String url) {
        return ResultMsg.SUCCESS(lxApiOauthService.buildConfigAuthData(url));
    }

    // 需要传对应的租户 id
    @ApiOperation("获取用户 token")
    @GetMapping(value = "/user/token")
    public ResultMsg<String> getUserToken(@ApiParam("用户 code") @RequestParam String code) throws IOException {
        UserToken userToken = null;

        // 从蓝信获取用户信息
        V1UsersFetchData userBasicData = this.getUserInfoFromLanxin(code);
        String mobilePhone = userBasicData.getMobilePhone().getNumber();
        // 获取 eworker token
        userToken = this.getEWorkerToken(mobilePhone);

        // 绑定蓝信跟系统的用户关系
        String userId = userToken.getUserId();
        String lxUserId = userBasicData.getStaffId();
        lanxinMapper.updateUserById(userId,lxUserId);

        return ResultMsg.SUCCESS("Bearer-"+userToken.getToken());
    }

    // 需要传对应的租户 id
    @ApiOperation("根据 用户名、手机号、邮箱模糊查询蓝信用户信息")
    @GetMapping(value = "/user/getLxUserInfo")
    public ResultMsg<List<Map>> getLxUserInfo(@RequestParam String param) throws Exception {
        // 从蓝信获取用户信息
        return ResultMsg.SUCCESS(lxMessageService.getLxUserInfo(param));
    }


    private V1UsersFetchData getUserInfoFromLanxin(String code) {
        // 获取蓝信的用户token
        String userToken = lxApiOauthService.getUserToken(code);
        // 获取用户基本信息
        JSONObject userBasic = lxApiOauthService.getUserBasic(userToken);
        logger.info("蓝信用户基本信息:{}", userBasic.toJSONString());
        V1UsersFetchData userBasicData = userBasic.toJavaObject(V1UsersFetchData.class);

//        // 获取用户详细信息
//        String staffId = userBasic.getString("staffId");
//        JSONObject userDetail = lxUserService.getUserDetail(staffId);
//        log.info("用户详细信息:{}", userDetail.toJSONString());
        return userBasicData;
    }


    private UserToken getEWorkerToken(String mobile) {
        String audience = "mobile";
//        String audience = "pc";
        List<UserDTO> usersByMobiles = (List<UserDTO>) userService.getUsersByMobiles(mobile);
        if (CollectionUtils.isEmpty(usersByMobiles)) {
            throw new BusinessMessage("用户不存在", LxPlatFormStatusCode.LOGIN_ERROR);
        }
        UserToken userToken = new UserToken();
        userToken.setUserId(usersByMobiles.get(0).getUserId());
        try {

            String token = jwtService.generateToken(StringUtils.join(new Serializable[]{usersByMobiles.get(0).getAccount()}), audience);
            userToken.setToken(token);
            return userToken;
        } catch (BadCredentialsException e) {
            throw new BusinessMessage("账号或密码错误", LxPlatFormStatusCode.LOGIN_ERROR);
        } catch (DisabledException e) {
            throw new BusinessMessage("帐号已禁用", LxPlatFormStatusCode.LOGIN_ERROR);
        } catch (LockedException e) {
            throw new BusinessMessage("帐号已锁定", LxPlatFormStatusCode.LOGIN_ERROR);
        } catch (AccountExpiredException e) {
            throw new BusinessMessage("帐号已过期", LxPlatFormStatusCode.LOGIN_ERROR);
        } catch (Exception ex) {
            logger.error("登录异常", ex);
            throw new BusinessException(LxPlatFormStatusCode.LOGIN_ERROR, ex);
        }
    }

    @Data
    private static class UserToken {
        private String userId;

        private String token;
    }

}
