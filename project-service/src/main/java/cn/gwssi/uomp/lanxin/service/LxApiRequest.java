package cn.gwssi.uomp.lanxin.service;

import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
public class LxApiRequest {
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 网络层面发起请求
     *
     * @param builder
     * @return
     */
    public JSONObject doRequest(UriComponentsBuilder builder, HttpHeaders headers, Object params, HttpMethod method) {
        HttpEntity<?> entity = null;
        if (params != null) {
            entity = new HttpEntity<>(params, headers);
        } else {
            entity = new HttpEntity<>(headers);
        }
        log.info("lanxin request begin:[{}]", builder.build().toUri().toString());
        long beginTime = System.currentTimeMillis();
        ResponseEntity<JSONObject> result = restTemplate.exchange(builder.build().encode().toUri(), method, entity, JSONObject.class);
        if (result.getStatusCode() == HttpStatus.OK) {
            log.info("lanxin request success,time:[{}ms],data:[{}]", System.currentTimeMillis() - beginTime, result.getBody());
        } else {
            log.error("lanxin request fail,time:[{}ms],statusCode:[{}],data:[{}]", System.currentTimeMillis() - beginTime, result.getStatusCode(), result.getBody());
            throw new BusinessException("lanxin request fail");
        }
        return result.getBody();
    }

    /**
     * 网络层面发起请求
     *
     * @param builder
     * @return
     */
    public JSONObject doRequest1(UriComponentsBuilder builder, HttpHeaders headers, Object params, HttpMethod method) {
        HttpEntity<?> entity = null;
        if (params != null) {
            entity = new HttpEntity<>(params, headers);
        } else {
            entity = new HttpEntity<>(headers);
        }
        log.info("lanxin request begin:[{}]", builder.build().toUri().toString());
        long beginTime = System.currentTimeMillis();
//        ResponseEntity<JSONObject> result = restTemplate.exchange(builder.build().encode().toUri(), method, entity, JSONObject.class);
        Object result = restTemplate.exchange(builder.build().encode().toUri(), method, entity, String.class);
//        if (result.getStatusCode() == HttpStatus.OK) {
//            log.info("lanxin request success,time:[{}ms],data:[{}]", System.currentTimeMillis() - beginTime, result.getBody());
//        } else {
//            log.error("lanxin request fail,time:[{}ms],statusCode:[{}],data:[{}]", System.currentTimeMillis() - beginTime, result.getStatusCode(), result.getBody());
//            throw new BusinessException("lanxin request fail");
//        }
//        return result.getBody();
        System.out.println(11);
        return new JSONObject();
    }

}
