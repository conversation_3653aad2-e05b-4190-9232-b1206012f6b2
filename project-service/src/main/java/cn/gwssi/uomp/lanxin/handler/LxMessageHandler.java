package cn.gwssi.uomp.lanxin.handler;

import cn.gwssi.ecloudframework.sys.api.jms.JmsHandler;
import cn.gwssi.ecloudframework.sys.api.jms.model.JmsDTO;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;
import cn.gwssi.uomp.lanxin.model.LxSendMessageResultDTO;
import cn.gwssi.uomp.lanxin.service.ILxMessageService;
import cn.gwssi.uomp.lanxin.service.LxMessageConverter;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 蓝信应用号消息处理器
 * 适配 eCloud 通用消息体版
 *
 * <AUTHOR>
@Slf4j
@Component("lanXinDefaultHandler")
public class LxMessageHandler implements JmsHandler<NotifyMessage> {

    @Autowired
    private ILxMessageService lxMessageService;

    @Autowired
    private LxMessageConverter lxMessageConverter;

    @Override
    public String getType() {
        return "lanXinDefault";
    }

    public String getTitle() {
        return "标准版蓝信消息";
    }

    @Override
    public boolean handlerMessage(JmsDTO<NotifyMessage> message, String messageId) {
        NotifyMessage notifyMessage = message.getData();
        log.info("蓝信应用号消息队列-收到消息 messageId [{}], messageBody: {}", messageId, JSONObject.toJSONString(notifyMessage));

        // 处理 notifyMessage 中的扩展 map 数据
        Map<String, Object> extendVars = notifyMessage.getExtendVars();
        if (extendVars == null) {
            log.error("蓝信应用号消息队列-消息处理异常,消息内容缺失");
            return false;
        }

        // 消息转换-转换成蓝信需要的格式
        LxMessageCreateModel messageData = lxMessageConverter.convert(notifyMessage);

        // 发送蓝信应用号消息
        LxSendMessageResultDTO imSendMessageResultDTO = lxMessageService.sendOaMessage(messageData);
//        String result = lxMessageService.sendOaMessage1();

        log.info("蓝信应用号消息队列-处理完成 messageId [{}],result:{}", messageId, JSONObject.toJSONString(imSendMessageResultDTO));
        return true;
    }

}
