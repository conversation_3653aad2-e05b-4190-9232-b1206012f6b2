package cn.gwssi.uomp.lanxin.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 蓝信消息处理器加载类型
 * 处理器匹配优先级 MESSAGE_CONVERTER_NAME > TAG > APP_ID
 */
@Getter
@AllArgsConstructor
public enum LxMessageConverterTypeEnum {

    /**
     * 根据消息扩展字段中指定的过滤器名称(lxMessageConverter)进行加载
     */
    MESSAGE_CONVERTER_NAME,
    /**
     * 根据消息中的 tag 加载消息处理器
     */
    TAG,

    /**
     * 根据消息扩展字段中的 appId 加载消息处理器
     */
    APP_ID;

    public String getMessageConverterKey(String value) {
        return this.name() + ":" + value;
    }
}
