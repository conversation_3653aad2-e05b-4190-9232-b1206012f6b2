package cn.gwssi.uomp.lanxin.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 蓝信错误码
 */
@Getter
@AllArgsConstructor
public enum LanxinErrorCode {

    OK(0, "OK"),
    SERVICE_NOT_AVAILABLE(10000, "API 服务不可用"),
    APP_TOKEN_LOST(-1, "缺少 APP_TOKEN"),
    USER_TOKEN_LOST(-2, "缺少 USER_TOKEN"),
    APP_TOKEN_INVALID(-3, "无效的 APP_TOKEN"),
    USER_TOKEN_INVALID(-4, "无效的 USER_TOKEN"),
    URI_ERROR(-5, "API 请求路径错误"),
    JSON_SERIALIZATION_ERROR(-100, "JSON序列化错误"),
    JSON_DESERIALIZATION_ERROR(-101, "JSON反序列化错误");

    private int code;
    private String message;

}
