package cn.gwssi.uomp.lanxin.service;


import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;
import cn.gwssi.uomp.lanxin.model.LxSendMessageResultDTO;

import java.util.List;
import java.util.Map;

public interface ILxMessageService {

    /**
     * 发送应用消息
     * 包含的逻辑
     * 1. appId 换成蓝信的 appId
     * 2. userIdList 由 OA 的换成蓝信 的
     *
     * @return
     */
    LxSendMessageResultDTO sendOaMessage(LxMessageCreateModel messageData);

    String sendOaMessage1();

    String sendBotMsg(String address) throws Exception;

    void updateLanxinUser();

    List<Map> getLxUserInfo(String param) throws Exception;

}
