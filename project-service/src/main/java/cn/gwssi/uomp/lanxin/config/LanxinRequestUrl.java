package cn.gwssi.uomp.lanxin.config;

public interface LanxinRequestUrl {

    /**
     * 获取应用访问 TOKEN
     * https://DOMAIN.XXX/v1/apptoken/create?grant_type=client_credential&appid=APPID&secret=APPSECRET
     */
    String OAUTH_APP_TOKEN = "/v1/apptoken/create";

    /**
     * 获取 js TOKEN
     * https://DOMAIN.XXX/v1/jsapitoken/create?app_token=API_ACCESS_TOKEN&user_token=USER_TOKEN
     */
    String OAUTH_JS_TOKEN = "/v1/jsapitoken/create";

    /**
     * 获取人员访问 code
     * https://授权domain/oauth2/authorize?appid=APPID&response_type=code&scope=SCOPE&state=STATE&redirect_uri=REDIRECT_URI
     */
    String OAUTH_USER_CODE = "/oauth2/authorize";

    /**
     * 获取人员访问 TOKEN
     * https://DOMAIN.XXX/v1/usertoken/create?app_token=API_ACCESS_TOKEN&grant_type=authorization_code&code=CODE&redirect_uri=REDIRECT_URI
     */
    String OAUTH_USER_TOKEN = "/v1/usertoken/create";


    /**
     * 获取人员基本信息
     * https://DOMAIN.XXX/v1/users/fetch?app_token=API_ACCESS_TOKEN&user_token=USER_TOKEN
     */

    String OAUTH_USER_BASIC = "/v1/users/fetch";


    /**
     * 获取人员详细信息
     * https://apigw-example.domain/v1/staffs/:staffid/infor/fetch?app_token=APP_ACCESS_TOKEN&user_token=USER_TOKEN
     */
    String STAFFS_USER_DETAIL = "/v1/staffs/%s/infor/fetch";

    /**
     * 创建用户
     * https://apigw-example.domain/v1/staffs/create?app_token=APP_ACCESS_TOKEN&user_token=USER_TOKEN
     */
    String STAFFS_CREATE = "/v1/staffs/create";

    /**
     * 发送消息
     * https://apigw-example.domain/v1/messages/create?app_token=APP_ACCESS_TOKEN&user_token=USER_TOKEN
     */
    String MESSAGE_CREATE = "/v1/messages/create";

    /**
     * 更新消息
     * https://apigw-example.domain/v1/messages/dynamic/update?app_token=APP_TOKEN&user_token=USER_TOKEN
     */
    String MESSAGE_UPDATE = "/v1/messages/dynamic/update";

    /**
     * https://apigw-example.domain/v2/staffs/id_mapping/fetch?app_token=APP_TOKEN&org_id=ORG_ID&id_type=ID_TYPE&id_value=ID_VALUE
     * 通过手机号、邮箱获取人员唯一标识
     */
    String STAFFID_GET = "/v2/staffs/id_mapping/fetch";

    /**
     * 搜索人员   根据姓名、手机号等信息搜索人员，一般用于选人控件快速定位人员。搜索人员-V2版本接口支持按分支限定搜索范围，在指定的分支范围内搜索人员。
     * https://apigw-example.domain/v2/staffs/search?app_token=APP_ACCESS_TOKEN&user_token=USER_TOKEN&user_id=USER_ID&page=PAGE&page_size=PAGE_SIZE
     */
    String SEARCH_POST = "/v2/staffs/search";


    /**
     * 机器人发送webhook群消息
     * https://apigw-example.domain/v1/bot/hook/messages/create?app_token=APP_ACCESS_TOKEN&hook_token=HOOK_TOKEN
     */
    String BOT_HOOK_POST = "/v1/bot/hook/messages/create";

    /**
     * 根据分支ID 获取分支成员列表，目前只返回当前分支下的成员，不含子分支的成员
     * https://apigw-example.domain/v1/departments/:departmentid/staffs/fetch?app_token=APP_TOKEN&page=PAGE_OFFSET&page_size=PAGE_SIZE
     */
    String FETCH_BY_DEPARTMENT_GET = "/v1/departments/:departmentid/staffs/fetch";

    /**
     * 根据分支ID 获取分支详情信息
     * https://apigw-example.domain/v1/departments/:departmentid/fetch?app_token=APP_TOKEN
     */
    String DEPARTMENTS_DETAIL_GET = "/v1/departments/%s/fetch";
}
