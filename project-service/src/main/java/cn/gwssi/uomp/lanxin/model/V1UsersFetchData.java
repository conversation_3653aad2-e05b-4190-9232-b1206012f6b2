package cn.gwssi.uomp.lanxin.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class V1UsersFetchData {
    public static final String SERIALIZED_NAME_AVATAR_ID = "avatarId";
    @SerializedName("avatarId")
    private String avatarId;
    public static final String SERIALIZED_NAME_AVATAR_URL = "avatarUrl";
    @SerializedName("avatarUrl")
    private String avatarUrl;
    public static final String SERIALIZED_NAME_DEPARTMENT = "department";
    @SerializedName("department")
    private List<Department> department = null;
    public static final String SERIALIZED_NAME_EMAIL = "email";
    @SerializedName("email")
    private String email;
    public static final String SERIALIZED_NAME_EMPLOYEE_NUMBER = "employeeNumber";
    @SerializedName("employeeNumber")
    private String employeeNumber;
    public static final String SERIALIZED_NAME_EXTERNAL_ID = "externalId";
    @SerializedName("externalId")
    private String externalId;
    public static final String SERIALIZED_NAME_LOGIN_NAME = "loginName";
    @SerializedName("loginName")
    private String loginName;
    public static final String SERIALIZED_NAME_MOBILE_PHONE = "mobilePhone";
    @SerializedName("mobilePhone")
    private MobilePhone mobilePhone;
    public static final String SERIALIZED_NAME_NAME = "name";
    @SerializedName("name")
    private String name;
    public static final String SERIALIZED_NAME_ORG_ID = "orgId";
    @SerializedName("orgId")
    private String orgId;
    public static final String SERIALIZED_NAME_ORG_NAME = "orgName";
    @SerializedName("orgName")
    private String orgName;
    public static final String SERIALIZED_NAME_STAFF_ID = "staffId";
    @SerializedName("staffId")
    private String staffId;

    public V1UsersFetchData() {
    }

    public V1UsersFetchData avatarId(String avatarId) {
        this.avatarId = avatarId;
        return this;
    }

    @Nullable
    @ApiModelProperty("头像ID")
    public String getAvatarId() {
        return this.avatarId;
    }

    public void setAvatarId(String avatarId) {
        this.avatarId = avatarId;
    }

    public V1UsersFetchData avatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
        return this;
    }

    @Nullable
    @ApiModelProperty("头像地址")
    public String getAvatarUrl() {
        return this.avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public V1UsersFetchData department(List<Department> department) {
        this.department = department;
        return this;
    }

    public V1UsersFetchData addDepartmentItem(Department departmentItem) {
        if (this.department == null) {
            this.department = new ArrayList();
        }

        this.department.add(departmentItem);
        return this;
    }

    @Nullable
    @ApiModelProperty("部门信息")
    public List<Department> getDepartment() {
        return this.department;
    }

    public void setDepartment(List<Department> department) {
        this.department = department;
    }

    public V1UsersFetchData email(String email) {
        this.email = email;
        return this;
    }

    @Nullable
    @ApiModelProperty("邮箱地址")
    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public V1UsersFetchData employeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
        return this;
    }

    @Nullable
    @ApiModelProperty("员工号")
    public String getEmployeeNumber() {
        return this.employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public V1UsersFetchData externalId(String externalId) {
        this.externalId = externalId;
        return this;
    }

    @Nullable
    @ApiModelProperty("外部数据源人员唯一ID")
    public String getExternalId() {
        return this.externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public V1UsersFetchData loginName(String loginName) {
        this.loginName = loginName;
        return this;
    }

    @Nullable
    @ApiModelProperty("登录用户名")
    public String getLoginName() {
        return this.loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public V1UsersFetchData mobilePhone(MobilePhone mobilePhone) {
        this.mobilePhone = mobilePhone;
        return this;
    }

    @Nullable
    @ApiModelProperty("")
    public MobilePhone getMobilePhone() {
        return this.mobilePhone;
    }

    public void setMobilePhone(MobilePhone mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public V1UsersFetchData name(String name) {
        this.name = name;
        return this;
    }

    @Nullable
    @ApiModelProperty("人员名称")
    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public V1UsersFetchData orgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    @Nullable
    @ApiModelProperty("组织ID")
    public String getOrgId() {
        return this.orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public V1UsersFetchData orgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    @Nullable
    @ApiModelProperty("组织名称")
    public String getOrgName() {
        return this.orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public V1UsersFetchData staffId(String staffId) {
        this.staffId = staffId;
        return this;
    }

    @Nullable
    @ApiModelProperty("人员ID")
    public String getStaffId() {
        return this.staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            V1UsersFetchData v1UsersFetchData = (V1UsersFetchData)o;
            return Objects.equals(this.avatarId, v1UsersFetchData.avatarId) && Objects.equals(this.avatarUrl, v1UsersFetchData.avatarUrl) && Objects.equals(this.department, v1UsersFetchData.department) && Objects.equals(this.email, v1UsersFetchData.email) && Objects.equals(this.employeeNumber, v1UsersFetchData.employeeNumber) && Objects.equals(this.externalId, v1UsersFetchData.externalId) && Objects.equals(this.loginName, v1UsersFetchData.loginName) && Objects.equals(this.mobilePhone, v1UsersFetchData.mobilePhone) && Objects.equals(this.name, v1UsersFetchData.name) && Objects.equals(this.orgId, v1UsersFetchData.orgId) && Objects.equals(this.orgName, v1UsersFetchData.orgName) && Objects.equals(this.staffId, v1UsersFetchData.staffId);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.avatarId, this.avatarUrl, this.department, this.email, this.employeeNumber, this.externalId, this.loginName, this.mobilePhone, this.name, this.orgId, this.orgName, this.staffId});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class V1UsersFetchData {\n");
        sb.append("    avatarId: ").append(this.toIndentedString(this.avatarId)).append("\n");
        sb.append("    avatarUrl: ").append(this.toIndentedString(this.avatarUrl)).append("\n");
        sb.append("    department: ").append(this.toIndentedString(this.department)).append("\n");
        sb.append("    email: ").append(this.toIndentedString(this.email)).append("\n");
        sb.append("    employeeNumber: ").append(this.toIndentedString(this.employeeNumber)).append("\n");
        sb.append("    externalId: ").append(this.toIndentedString(this.externalId)).append("\n");
        sb.append("    loginName: ").append(this.toIndentedString(this.loginName)).append("\n");
        sb.append("    mobilePhone: ").append(this.toIndentedString(this.mobilePhone)).append("\n");
        sb.append("    name: ").append(this.toIndentedString(this.name)).append("\n");
        sb.append("    orgId: ").append(this.toIndentedString(this.orgId)).append("\n");
        sb.append("    orgName: ").append(this.toIndentedString(this.orgName)).append("\n");
        sb.append("    staffId: ").append(this.toIndentedString(this.staffId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
