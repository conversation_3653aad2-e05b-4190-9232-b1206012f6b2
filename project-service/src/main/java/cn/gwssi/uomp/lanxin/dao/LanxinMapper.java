package cn.gwssi.uomp.lanxin.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface LanxinMapper {

    //查询所有的机构
    List<Map<String,Object>> getAllOrg();

    //根据机构id和手机号更新扩展字段
    void updateUser(@Param("orgId")String orgId,@Param("mobile")String mobile,@Param("lanxinUserId")String lanxinUserId);

    List<String> getLxUserIds(@Param("userIdList") List<String> userIdList);

    //根据userId 根据扩展字段
    void updateUserById(@Param("userId")String userId,@Param("lxUserId")String lxUserId);

    //查询所有的用户
    List<Map<String,Object>> getAllUser();

    //插入蓝信错误日志表
    void insertLanxinError(@Param("id")String id,@Param("userId")String userId,@Param("userName")String userName,@Param("errorType")String errorType,@Param("errorInfo")String errorInfo,@Param("createBy")String createBy,@Param("delFlag")String delFlag);

    //
    Map<String,String> getMobileAndLxUserId(@Param("userId")String userId);

}
