package cn.gwssi.uomp.lanxin.service;


import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.uomp.lanxin.constant.LxMessageConverterTypeEnum;
import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;

/**
 * 蓝信消息转换器
 * 应用号消息转换器
 */
public interface LxMessageConverter {

    /**
     * 通过什么过滤消息，消息体中的 tag 还是 appId 还是过滤器的名称
     *
     * @return
     */
    LxMessageConverterTypeEnum filterBy();

    /**
     * 值
     * 过滤消息时进行匹配的值
     * 当 filterBy = TAG 时，该值是消息中的 NotifyMessage 中的 tag 的值
     * 当 filterBy = APP_ID 时，该值是消息中的 NotifyMessage 中的 extendVars 中的 appId 的值
     * 当 filterBy = MESSAGE_CONVERTER_NAME 时，该值是消息中的 NotifyMessage 中的 extendVars 中的 imMessageConverter 的值
     *
     * @return
     */
    String filterValue();

    /**
     * 转换消息
     *
     * @param notifyMessage
     * @return
     */
    LxMessageCreateModel convert(NotifyMessage notifyMessage);
}
