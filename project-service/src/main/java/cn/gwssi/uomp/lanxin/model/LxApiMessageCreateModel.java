package cn.gwssi.uomp.lanxin.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "蓝信应用消息入参")
@Data
@NoArgsConstructor
public class LxApiMessageCreateModel {

    @ApiModelProperty(value = "发送的消息格式，支持以下几种：text，linkCard，appCard", required = true)
    @NotEmpty
    private String msgType;

    @ApiModelProperty("msgId-被修改的消息ID")
    private String msgId;

    @ApiModelProperty(value = "消息体", required = true)
    private MsgDataBean msgData;

    @ApiModelProperty(value = "接收者人员列表")
    private List<String> userIdList;

//    @ApiModelProperty(value = "接收者分支列表")
//    private List<String> departmentIdList;

    @ApiModel(description = "消息数据体")
    @NoArgsConstructor
    @Data
    public static class MsgDataBean {

        @ApiModelProperty("普通文本消息")
        private TextBean text;

        @ApiModelProperty("链接卡片，适用于简单链接跳转")
        private LinkCardBean linkCard;

        @ApiModelProperty("办公卡片，支持复杂格式，适用于稍复杂办公场景消息类型")
        private AppCardBean appCard;

        @ApiModelProperty("办公卡片，更新卡片状态")
        private AppCardBean appCardUpdateMsg;

//        @ApiModelProperty("图文消息")
//        private List<AppArticlesBean> appArticles;
//
//        @ApiModelProperty("公文卡片，适用于正式公文类消息")
//        private List<DocumentBean> document;

//        @ApiModelProperty("系统消息")
//        private TextBean system;

        @ApiModel(description = "普通文本消息")
        @NoArgsConstructor
        @Data
        public static class TextBean {
            @ApiModelProperty(value = "消息内容", required = true)
            private String content;

//            @ApiModel(description = "消息 @")
//            @NoArgsConstructor
//            @Data
//            public static class ReminderBean {
//                @ApiModelProperty("消息 @全体成员")
//                private boolean all;
//                @ApiModelProperty("消息 @人员列表")
//                private List<String> userIds;
//            }
        }

        @ApiModel(description = "链接卡片")
        @NoArgsConstructor
        @Data
        public static class LinkCardBean {

            @ApiModelProperty(value = "卡片标题", required = true)
            private String title;

            @ApiModelProperty("卡片描述，可不填")
            private String description;

            @ApiModelProperty("卡片消息中展示的图片链接，可不填")
            private String iconLink;

            @ApiModelProperty(value = "卡片链接", required = true)
            private String link;

            @ApiModelProperty(value = "PC端卡片链接，可不填")
            private String pcLink;

            @ApiModelProperty("卡片来源名称，可不填")
            private String fromName;

            @ApiModelProperty("卡片来源图片链接，一般是公司Logo，可不填")
            private String fromIconLink;
        }

        @ApiModel(description = "应用卡片")
        @NoArgsConstructor
        @Data
        public static class AppCardBean {

            @ApiModelProperty("页眉标题，可不填")
            private String headTitle;

            @ApiModelProperty("应用的title图片media的openId，可不填")
            private String headIconId;

            @ApiModelProperty("icon的网络地址，可不填")
            private String headIconUrl;

            @ApiModelProperty(value = "文本标题（长度限制200*3个字节）", required = true)
            private String bodyTitle;

            @ApiModelProperty("文本副标题，两行（接口限制400*3字节），可不填")
            private String bodySubTitle;

            @ApiModelProperty("人员ID，用于在卡片消息中展示消息发送者头像，可不填")
            private String staffId;

            @ApiModelProperty("松散内容，八行（1000*3字节），默认首行缩进2个单位（2em），可不填")
            private String bodyContent;

            @ApiModelProperty("0个或多个键值对，可不填")
            private List<FieldsBean> fields;

            @ApiModelProperty("链接，最多3对，可不填")
            private List<LinksBean> links;

            @ApiModelProperty("本条消息的跳转地址，可不填")
            private String cardLink;

            @ApiModelProperty("PC端跳转地址，可不填")
            private String pcCardLink;

            @ApiModelProperty("是否动态卡片消息")
            @JSONField(name = "isDynamic")
            private Boolean isDynamic;

            @ApiModelProperty("动态卡片的动态信息区")
            private JSONObject headStatusInfo;
            @ApiModelProperty
            @JSONField(name = "isLastUpdate")
            private Boolean isLastUpdate;

            @ApiModel(description = "键值对，上限10对")
            @NoArgsConstructor
            @Data
            public static class FieldsBean {
                @ApiModelProperty("key值可单独存在（长度不超过6个汉字）")
                private String key;

                @ApiModelProperty("value值不可单独存在，两行（接口限制64字）")
                private String value;
            }

            @ApiModel(description = "键值对，上限10对")
            @NoArgsConstructor
            @Data
            public static class LinksBean {
                @ApiModelProperty("key值可单独存在（长度不超过6个汉字）")
                private String title;

                @ApiModelProperty("value值不可单独存在，两行（接口限制64字）")
                private String url;
            }
        }

//        @ApiModel(description = "图文消息")
//        @NoArgsConstructor
//        @Data
//        public static class AppArticlesBean {
//
//            @ApiModelProperty(value = "图片链接", required = true)
//            private String imgUrl;
//
//            @ApiModelProperty(value = "标题", required = true)
//            private String title;
//
//            @ApiModelProperty("摘要")
//            private String summary;
//
//            @ApiModelProperty(value = "内容地址", required = true)
//            private String url;
//
//            @ApiModelProperty("微应用跳转参数，其他应用忽略（或填空）")
//            private String attach;
//        }
//
//        @ApiModel(description = "公文消息")
//        @NoArgsConstructor
//        @Data
//        public static class DocumentBean {
//
//            @ApiModelProperty(value = "主标题", required = true)
//            private String title;
//
//            @ApiModelProperty(value = "副标题")
//            private String subTitle;
//
//            @ApiModelProperty(value = "内容标题")
//            private String contentTitle;
//
//            @ApiModelProperty(value = "链接地址", required = true)
//            private String url;
//        }


    }
}
