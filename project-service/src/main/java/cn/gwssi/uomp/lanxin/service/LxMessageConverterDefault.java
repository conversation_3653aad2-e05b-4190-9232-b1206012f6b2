package cn.gwssi.uomp.lanxin.service;


import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTask;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.uomp.lanxin.config.LanXinProperties;
import cn.gwssi.uomp.lanxin.constant.LxMessageConverterTypeEnum;
import cn.gwssi.uomp.lanxin.constant.LxMsgTypeEnum;
import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认的 im 消息转换器
 */
@Slf4j
@Component(LxMessageConverterDefault.FILTER_VALUE)
public class LxMessageConverterDefault extends LxApiOauthService implements LxMessageConverter{

    public static final LxMessageConverterTypeEnum FILTER_TYPE = LxMessageConverterTypeEnum.MESSAGE_CONVERTER_NAME;
    public static final String FILTER_VALUE = "lxMessageConverterDefault";

    @Autowired
    private LanXinProperties lanXinProperties;

    @Resource
    BpmTaskManager bpmTaskManager;

    @Override
    public LxMessageConverterTypeEnum filterBy() {
        return FILTER_TYPE;
    }

    @Override
    public String filterValue() {
        return FILTER_VALUE;
    }

//    @Override
//    public LxMessageCreateModel convert(NotifyMessage notifyMessage) {
//        // 在这里进行消息的组装
//        LxMessageCreateModel messageData = JSONObject.parseObject(JSONObject.toJSONString(notifyMessage.getExtendVars()), LxMessageCreateModel.class);
//        messageData.setUserIdList(notifyMessage.getReceivers().stream().map(SysIdentity::getId).collect(Collectors.toList()));
//        String linkId = (String) notifyMessage.getExtendVars().get("linkId");
//        if (StringUtil.isNotEmpty(linkId)) {
//            messageData.getMsgData().getAppCard().setIsDynamic(true);
//            JSONObject headStatusInfo = new JSONObject();
//            headStatusInfo.put("description", "<div style=\"color:#5A83E9\">待审批</div>");
//            headStatusInfo.put("colour", "#FADD14");
//            messageData.getMsgData().getAppCard().setHeadStatusInfo(headStatusInfo);
//            messageData.getMsgData().getAppCard().setCardLink(messageData.getMsgData().getAppCard().getCardLink().replaceAll("LinkReplace", linkId));
//        }
//
//        return messageData;
//    }


    @Override
    public LxMessageCreateModel convert(NotifyMessage notifyMessage) {
        // 在这里进行消息的组装
        LxMessageCreateModel messageData = JSONObject.parseObject(JSONObject.toJSONString(notifyMessage.getExtendVars()), LxMessageCreateModel.class);
        //设置appid
        messageData.setAppId(getLanXinProperties().getAppId());
        //设置接收人
        List<SysIdentity> receivers = notifyMessage.getReceivers();
        List<String> userIdList = receivers.stream().map(SysIdentity::getId).collect(Collectors.toList());
        messageData.setUserIdList(userIdList);
        //设置消息类型
        messageData.setMsgType(LxMsgTypeEnum.APP_CARD.getKey());

        String linkId = (String) notifyMessage.getExtendVars().get("linkId");
        String instId = (String) notifyMessage.getExtendVars().get("instId");
        List<BpmTask> list = bpmTaskManager.getByInstId(instId);
        String taskId = "";
        if(CollectionUtil.isNotEmpty(list)){
            taskId = list.get(0).getId();
        }
        log.info("taskId = "+taskId);
        if (StringUtil.isNotEmpty(linkId) && StringUtil.isNotEmpty(messageData.getMsgData().getAppCard().getCardLink())) {
            messageData.getMsgData().getAppCard().setCardLink(messageData.getMsgData().getAppCard().getCardLink().replaceAll("LinkReplace", linkId).replaceAll("IpUrlReplace",lanXinProperties.getBpmUrl()).replaceAll("TaskReplace",taskId));
        }else if (StringUtil.isNotEmpty(messageData.getMsgData().getAppCard().getCardLink())){
            messageData.getMsgData().getAppCard().setCardLink(messageData.getMsgData().getAppCard().getCardLink().replaceAll("IpUrlReplace",lanXinProperties.getBpmUrl()));
        }
        return messageData;
    }
}
