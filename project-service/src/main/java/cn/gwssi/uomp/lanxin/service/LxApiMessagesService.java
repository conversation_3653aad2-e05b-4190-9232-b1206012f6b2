package cn.gwssi.uomp.lanxin.service;

import cn.gwssi.uomp.lanxin.config.LanxinRequestUrl;
import cn.gwssi.uomp.lanxin.model.LxApiMessageCreateModel;
import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 蓝信消息
 */
@Service
public class LxApiMessagesService extends LxApiOauthService {

    /**
     * 发送蓝信应用号消息
     *
     * @param messageCreateModel
     * @return
     */
    public JSONObject createMessage(LxMessageCreateModel messageCreateModel) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.MESSAGE_CREATE)
                .queryParam("app_token", appToken);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return doRequest(builder, headers, JSONObject.toJSON(messageCreateModel), HttpMethod.POST);
    }

    public JSONObject updateMessage(LxApiMessageCreateModel messageCreateModel) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.MESSAGE_UPDATE)
                .queryParam("app_token", appToken);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return doRequest(builder, headers, JSONObject.toJSON(messageCreateModel), HttpMethod.POST);
    }

    //根据手机号查询用户信息的接口(调用的搜索人员接口)
    public JSONObject searchLanxinUserByMobile(List<String> mobileList,String code) {
        String appToken = this.getAppToken();
//        String code = getLanxinCode(appToken);

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.SEARCH_POST)
                .queryParam("app_token",appToken)
                .queryParam("user_token",getLanxinToken(code));
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        //组装参数
        JSONObject param = new JSONObject();
        JSONObject searchScope = new JSONObject();
        searchScope.put("sectorIds",mobileList);
        param.put("keyword","mobilePhone");
        param.put("recursive",false);
        param.put("searchScope",searchScope);

        JSONObject jsonObject = doRequest(builder, headers, param, HttpMethod.POST);
        return jsonObject;
    }


    //获取人员免登授权码
    public String getLanxinCode(String appToken){
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getAuthDomain() + LanxinRequestUrl.OAUTH_USER_CODE)
                .queryParam("appid", getLanXinProperties().getAppId())
                .queryParam("response_type","code")
                .queryParam("scope","basic_userinfor")
                .queryParam("state","3da9d9f1-6756-11ea-8b95-0242ac115010")
                .queryParam("redirect_uri","http://10.99.36.231/micro-portal");
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        Object jsonObject = doRequest1(builder, headers, null, HttpMethod.GET);
        return "jsonObject";
    }

    //获取人员token
    public String getLanxinToken(String code){
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.OAUTH_USER_TOKEN)
                .queryParam("app_token", getLanxinAppToken())
                .queryParam("grant_type","authorization_code")
                .queryParam("code",code);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        JSONObject jsonObject = doRequest(builder, headers, null, HttpMethod.GET);
        return jsonObject.getString("userToken");
    }


    //获取app token
    public String getLanxinAppToken(){
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.OAUTH_APP_TOKEN)
                .queryParam("appid", getLanXinProperties().getAppId())
                .queryParam("grant_type","client_credential")
                .queryParam("secret",getLanXinProperties().getAppSecret());
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        JSONObject jsonObject = doRequest(builder, headers, null, HttpMethod.GET);

        return jsonObject.getString("appToken");

    }

    //根据机构id和手机号获取用户id
    public String getLxUserIdByMobile(String orgId,String mobile){
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.STAFFID_GET)
                .queryParam("app_token", getLanxinAppToken())
                .queryParam("org_id",orgId)
                .queryParam("id_type","mobile")
                .queryParam("id_value",mobile);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        JSONObject jsonObject = doRequest(builder, headers, null, HttpMethod.GET);
        return jsonObject.getString("staffId");
    }


    /**
     * 发送webhook群消息
     * @return
     */
    public JSONObject createWebhookMessage(JSONObject jsonObject,String address) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(address)
                .queryParam("app_token", appToken);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return doRequest(builder, headers, jsonObject, HttpMethod.POST);
    }

    /**
     * 根据分支机构获取分支机构下的人员
     * @return
     */
    public List<Map<String,Object>> fetchByDepartment(String departmentid) throws Exception{

        String address = getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.FETCH_BY_DEPARTMENT_GET;
        //替换参数
        address = address.replace(":departmentid",departmentid);

        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(address)
                .queryParam("app_token", appToken);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        //因为这个接口分页100条，若还有数据，接着调
        List<Map<String,Object>> result = new ArrayList<>();
        Boolean isHasMore = true;
        if(isHasMore){
            JSONObject jsonObject = doRequest(builder, headers, null, HttpMethod.GET);
            result.addAll((Collection<? extends Map<String, Object>>) jsonObject.get("staffs"));
            isHasMore = jsonObject.getBoolean("hasMore");
        }
        return result;
    }

    /**
     * 模糊搜索蓝信人员
     * @return
     */
    public JSONObject searchLxUser(String param,String userId) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.SEARCH_POST)
                .queryParam("app_token",appToken)
                .queryParam("user_id",userId);
        HttpHeaders headers = getDefaultHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        //组装参数
        JSONObject paramJson = new JSONObject();
        paramJson.put("keyword",param);
        paramJson.put("recursive",true);

        JSONObject jsonObject = doRequest(builder, headers, paramJson, HttpMethod.POST);
        return jsonObject;
    }

    /**
     * 获取人员详细信息——蓝信
     * @return
     */
    public JSONObject fetchInfo(String userId) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(getLanXinProperties().getApiGwDomain() + String.format(LanxinRequestUrl.STAFFS_USER_DETAIL, userId))
                .queryParam("app_token", appToken);
        JSONObject userDetail = doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
        //处理机构 详情接口只返回了用户最后一级的机构 此处再调用获取分支详情接口 获取全部的分支机构
        if(userDetail!=null && userDetail.getJSONArray("departments") !=null && userDetail.getJSONArray("departments").size()>0) {
            JSONArray deptArray = userDetail.getJSONArray("departments");
            JSONObject dept = (JSONObject) deptArray.get(0);
            JSONObject deptInfo = this.getDepartInfo(dept.getString("id"));
            if(deptInfo!=null && deptInfo.getJSONArray("ancestorDepartments")!=null) {
                deptArray.addAll(deptInfo.getJSONArray("ancestorDepartments"));
            }
        }
        return userDetail;
    }

    /**
     * 根据部门分支ID获取部门分支详情
     * @return
     */
    public JSONObject getDepartInfo(String deptId) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(getLanXinProperties().getApiGwDomain() + String.format(LanxinRequestUrl.DEPARTMENTS_DETAIL_GET, deptId))
                .queryParam("app_token", appToken);
        return doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
    }


}
