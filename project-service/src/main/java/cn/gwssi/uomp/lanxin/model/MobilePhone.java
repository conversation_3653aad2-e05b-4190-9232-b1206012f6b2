package cn.gwssi.uomp.lanxin.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import javax.annotation.Nullable;
import java.util.Objects;

public class MobilePhone {
    public static final String SERIALIZED_NAME_COUNTRY_CODE = "countryCode";
    @SerializedName("countryCode")
    private String countryCode;
    public static final String SERIALIZED_NAME_NUMBER = "number";
    @SerializedName("number")
    private String number;

    public MobilePhone() {
    }

    public MobilePhone countryCode(String countryCode) {
        this.countryCode = countryCode;
        return this;
    }

    @Nullable
    @ApiModelProperty("手机号国家码")
    public String getCountryCode() {
        return this.countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public MobilePhone number(String number) {
        this.number = number;
        return this;
    }

    @Nullable
    @ApiModelProperty("手机号")
    public String getNumber() {
        return this.number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            MobilePhone mobilePhone = (MobilePhone)o;
            return Objects.equals(this.countryCode, mobilePhone.countryCode) && Objects.equals(this.number, mobilePhone.number);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.countryCode, this.number});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class MobilePhone {\n");
        sb.append("    countryCode: ").append(this.toIndentedString(this.countryCode)).append("\n");
        sb.append("    number: ").append(this.toIndentedString(this.number)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}

