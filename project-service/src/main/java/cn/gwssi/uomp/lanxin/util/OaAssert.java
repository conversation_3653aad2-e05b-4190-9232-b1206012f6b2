package cn.gwssi.uomp.lanxin.util;

import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

/**
 * 数据校验
 *
 * <AUTHOR>
 */
public abstract class OaAssert {

    /**
     * 断言不为 Blank，为 Blank 则报错
     *
     * @param str
     * @param message
     */
    public static void isNotBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言不为 Empty，为 Empty 则报错
     *
     * @param str
     * @param message
     */
    public static void isNotEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言不为 null,为 null 抛出异常
     *
     * @param object
     * @param message
     */
    public static void isNotNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言为 false,不为 false 则报错
     *
     * @param flag
     * @param message
     */
    public static void isFalse(boolean flag, String message) {
        if (flag) {
            throw new BusinessException(message);
        }
    }

    /**
     * 断言为 true,不为 true 则报错
     *
     * @param flag
     * @param message
     */
    public static void isTrue(boolean flag, String message) {
        if (!flag) {
            throw new BusinessException(message);
        }
    }
}
