package cn.gwssi.uomp.lanxin.service;


import cn.gwssi.ecloudbpm.module.news.utils.UuidUtils;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.lanxin.config.LanXinProperties;
import cn.gwssi.uomp.lanxin.constant.LxMsgTypeEnum;
import cn.gwssi.uomp.lanxin.dao.LanxinMapper;
import cn.gwssi.uomp.lanxin.model.LxMessageCreateModel;
import cn.gwssi.uomp.lanxin.model.LxSendMessageResultDTO;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LxMessageServiceImpl implements ILxMessageService {

    @Autowired
    private LxApiMessagesService lxApiMessagesService;

    @Autowired
    private LanxinMapper lanxinMapper;

    @Autowired
    private LanXinProperties lanXinProperties;

    @Override
    public LxSendMessageResultDTO sendOaMessage(LxMessageCreateModel messageCreateModel) {
        log.info("发送蓝信应用号消息-入参:{}", JSONObject.toJSONString(messageCreateModel));
        // 检查消息内容
        boolean checkMessageDataSuccess = this.checkMessageData(messageCreateModel);
        if (!checkMessageDataSuccess) {
            return new LxSendMessageResultDTO();
        }

        // 用户 id 转化为蓝信的用户 id
        List<String> lxUserIds = lanxinMapper.getLxUserIds(messageCreateModel.getUserIdList());
        if (messageCreateModel.getUserIdList().size() != lxUserIds.size()) {
            log.warn("发送蓝信应用号消息-部分 OA 用户无对应的蓝信账号:OA {}, 蓝信{}", lxUserIds, lxUserIds);
        }
        messageCreateModel.setUserIdList(lxUserIds);

//        if (messageCreateModel.getMsgType().equals(LxMsgTypeEnum.APP_CARD.getKey())) {
//            String userId = messageCreateModel.getMsgData().getAppCard().getStaffId();
//            if (StringUtils.isNotEmpty(userId)) {
//                LxUser lxUser = lxUserService.getByUserId(userId);
//                if (lxUser != null) {
//                    messageCreateModel.getMsgData().getAppCard().setStaffId(lxUser.getLxUserId());
//                } else {
//                    log.warn("发送蓝信应用号消息-OA 用户无对应的蓝信账号:OA {}", userId);
//                }
//            }
//        }

        // 发送蓝信消息
        JSONObject message = lxApiMessagesService.createMessage(messageCreateModel);
        log.info("发送蓝信应用号消息-成功:{}", message.toJSONString());
        return new LxSendMessageResultDTO();
    }


    private boolean checkMessageData(LxMessageCreateModel messageCreateModel) {
        LxMessageCreateModel.MsgDataBean msgData = messageCreateModel.getMsgData();

        if (messageCreateModel.getMsgType().equals(LxMsgTypeEnum.TEXT.getKey())) {
            if (msgData.getText() == null) {
                log.error("发送蓝信应用号消息-参数异常:text 不能为空");
                // throw new BusinessException("text 不能为空");
            }
        } else if (messageCreateModel.getMsgType().equals(LxMsgTypeEnum.LINK_CARD.getKey())) {
            if (msgData.getLinkCard() == null) {
                log.error("发送蓝信应用号消息-参数异常:linkCard 不能为空");
                // throw new BusinessException("linkCard 不能为空");
            }
        } else if (messageCreateModel.getMsgType().equals(LxMsgTypeEnum.APP_CARD.getKey())) {
            if (msgData.getAppCard() == null) {
                log.error("发送蓝信应用号消息-参数异常:appCard 不能为空");
                //throw new BusinessException("appCard 不能为空");
            }
        } else {
            log.error("发送蓝信应用号消息-参数异常:不支持该消息类型[{}]", messageCreateModel.getMsgType());
            //throw new BusinessException("不支持该消息类型");
        }
        return true;
    }

    public String sendOaMessage1() {

        LxMessageCreateModel.MsgDataBean msgDataBean = new LxMessageCreateModel.MsgDataBean();
        LxMessageCreateModel.MsgDataBean.AppCardBean appCardBean = new LxMessageCreateModel.MsgDataBean.AppCardBean();
        appCardBean.setBodyTitle("<div style=\"font-size:14pt;text-align:left\">中国系统运维管理平台系统</div>");
        appCardBean.setBodyContent("<div style=\"color:#5A83E9;text-align:left;text-indent: 0em\">您收到一个服务请求，请及时处理</div>");
        LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean fieldsBean = new LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean();
        fieldsBean.setKey("编号");
        fieldsBean.setValue("1111111111111");
        LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean fieldsBean1 = new LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean();
        fieldsBean1.setKey("标题");
        fieldsBean1.setValue("韩红双提交了云资源申请流程");
        LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean fieldsBean2 = new LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean();
        fieldsBean2.setKey("分类");
        fieldsBean2.setValue("云资源申请流程");
        LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean fieldsBean3 = new LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean();
        fieldsBean3.setKey("请求人");
        fieldsBean3.setValue("韩洪双");
        List<LxMessageCreateModel.MsgDataBean.AppCardBean.FieldsBean> fieldsBeanList = new ArrayList<>();
        fieldsBeanList.add(fieldsBean);
        fieldsBeanList.add(fieldsBean1);
        fieldsBeanList.add(fieldsBean2);
        fieldsBeanList.add(fieldsBean3);
        appCardBean.setFields(fieldsBeanList);
        msgDataBean.setAppCard(appCardBean);
        LxMessageCreateModel messageCreateModel = new LxMessageCreateModel();
        messageCreateModel.setAppId("5637120-10493952");
        messageCreateModel.setMsgType(LxMsgTypeEnum.APP_CARD.getKey());
        messageCreateModel.setMsgData(msgDataBean);

        log.info("发送蓝信应用号消息-入参:{}", JSONObject.toJSONString(messageCreateModel));
        // 检查消息内容
        boolean checkMessageDataSuccess = this.checkMessageData(messageCreateModel);
        if (!checkMessageDataSuccess) {
            return "失败";
        }

        // 用户 id 转化为蓝信的用户 id
        List<String> lxUserIds = new ArrayList<>();
        lxUserIds.add("5637120-vghnocD8X7PaClev93IVVv9CR9p0W");
        lxUserIds.add("5637120-9vhAac5Rezx7TXb20rhwwRxsBjwD5");
        messageCreateModel.setUserIdList(lxUserIds);


        // 发送蓝信消息
        JSONObject message = lxApiMessagesService.createMessage(messageCreateModel);
        log.info("发送蓝信应用号消息-成功:{}", message.toJSONString());
        return message.toJSONString();
    }

    //机器人发送群消息
    public String sendBotMsg(String address) throws Exception{
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msgType", LxMsgTypeEnum.TEXT.getKey());

        JSONObject text = new JSONObject();
        text.put("content","这是一条测试消息");
        JSONObject msgData = new JSONObject();
        msgData.put("text",text);
        jsonObject.put("msgData",msgData);

        // 发送蓝信消息
        System.out.println(jsonObject.toJSONString());
        JSONObject message = lxApiMessagesService.createWebhookMessage(jsonObject,address);
        log.info("发送蓝信应用号消息-成功:{}", message.toJSONString());
        return message.toJSONString();
    }


//    //根据本地配置同步蓝信人员
//    public void updateLanxinUser(){
//        //需求让把蓝信得机构id存到机构得编码字段中，把蓝信得用户存到用户详情表中的扩展字段中。找不到对应的api，自己写一个sql来查询和更新机构和用户
//        //查询到有蓝信机构的机构（蓝信机构id存在编码字段中）
//        List<Map<String, Object>> allOrg = lanxinMapper.getAllOrg();
//        if(CollectionUtil.isEmpty(allOrg)){return;}
//        //查询到机构下的用户信息
//        for (Map<String, Object> org : allOrg) {
//            String orgId = ObjectUtils.isEmpty(org.get("ID_"))?"":org.get("ID_").toString();
//            String lxOrgId = ObjectUtils.isEmpty(org.get("CODE_"))?"":org.get("CODE_").toString();
//            //调蓝信接口获取这个机构下的所有的人员
//            if(StringUtils.isEmpty(lxOrgId)){
//                continue;
//            }
//            List<Map<String,Object>> lanxinUser = null;
//            try {
//                lanxinUser = lxApiMessagesService.fetchByDepartment(lxOrgId);
//            } catch (Exception e) {
//                e.printStackTrace();
//                continue;
//            }
//            //根据机构id和手机号更新用户详情表中的扩展字段，将查到的蓝信的用户信息放到扩展字段中
//            if(CollectionUtil.isEmpty(lanxinUser)){
//                continue;
//            }
//            for (Map<String,Object> map :lanxinUser) {
//                String lanxinUserId = ObjectUtils.isEmpty(map.get("id"))?"":map.get("id").toString();
//                String mobile = ObjectUtils.isEmpty(map.get("mobile"))?"":map.get("mobile").toString();
////                String lxUser = map.toString();
//                lanxinMapper.updateUser(orgId,mobile,lanxinUserId);
//            }
//
//        }
//
//    }

    //根据本地配置同步蓝信人员 -- 根据手机号和appid中的机构id查
    public void updateLanxinUser(){
        //查询到所有的没有同步的人员
        List<Map<String, Object>> allUser = lanxinMapper.getAllUser();
        if(CollectionUtil.isEmpty(allUser)){return;}

        String appId = lanXinProperties.getAppId();
        log.info("appId = "+appId);
        if(StringUtils.isEmpty(appId)){
            return;
        }
        String lxOrgId = appId.split("-")[0];
        //查询到机构下的用户信息
        for (Map<String, Object> user : allUser) {
            String userId = ObjectUtils.isEmpty(user.get("ID_"))?"":user.get("ID_").toString();
            String userName = ObjectUtils.isEmpty(user.get("FULLNAME_"))?"":user.get("FULLNAME_").toString();
            String mobile = ObjectUtils.isEmpty(user.get("MOBILE_"))?"":user.get("MOBILE_").toString();
            if(StringUtils.isEmpty(mobile)){
                String error = "用户没有手机号";
                lanxinMapper.insertLanxinError(UuidUtils.getUuid(),userId,userName,"1",error,null,null);
                continue;
            }
            //根据手机号和机构id调蓝信接口获取用户信息
            try {
                String lxUserId = lxApiMessagesService.getLxUserIdByMobile(lxOrgId, mobile);
                if(StringUtils.isEmpty(lxUserId)){
                    String error = "根据手机号没有查到蓝信用户";
                    lanxinMapper.insertLanxinError(UuidUtils.getUuid(),userId,userName,"1",error,null,null);
                    continue;
                }
                //将查到的蓝信的用户信息放到扩展字段中
                lanxinMapper.updateUserById(userId,lxUserId);
            }catch (Exception e){
                lanxinMapper.insertLanxinError(UuidUtils.getUuid(),userId,userName,"1",e.getMessage(),null,null);
                continue;
            }

        }

    }



    /**
     *  根据 用户名、手机号、邮箱模糊查询蓝信用户信息
     */
    public List<Map> getLxUserInfo(String param) throws Exception{

        Map<String,String> user = lanxinMapper.getMobileAndLxUserId(ContextUtil.getCurrentUserId());

        String lxUserId = user.get("extend1");
        String mobile = user.get("mobile");

        String appId = lanXinProperties.getAppId();
        String lxOrgId = appId.split("-")[0];

        //若登录用户没有在本系统缓存蓝信的用户id，根据其手机号查
        if(StringUtils.isEmpty(lxUserId)){
            //若没有手机号，报错
            if(StringUtils.isEmpty(mobile)){
                throw new Exception("当前用户没有手机号");
            } else {
                //转换手机号为蓝信所用的格式
                mobile = "86-" + mobile;
            }

            lxUserId = lxApiMessagesService.getLxUserIdByMobile(lxOrgId, mobile);
        }

        //根据输入的参数模糊查询蓝信用户的信息
        JSONObject jsonObject = lxApiMessagesService.searchLxUser(param,lxUserId);
        List<Map> staffInfo = JSON.parseArray(jsonObject.getString("staffInfo"), Map.class);
        /* 此处循环不合适 改为选中之后再调取详细信息接口获取数据
        for (Map<String,Object> info : staffInfo) {
            //根据蓝信id查用户的详情，取其中的机构
            JSONObject lxInfo = lxApiMessagesService.fetchInfo(info.get("staffId").toString());
            info.put("orgName",lxInfo.get("orgName"));//人员所在组织机构名称
            info.put("departments",lxInfo.get("departments"));//人员所在分支列表
            info.put("loginName",lxInfo.get("loginName"));//用户名
        }
        */

        return staffInfo;
    }



}
