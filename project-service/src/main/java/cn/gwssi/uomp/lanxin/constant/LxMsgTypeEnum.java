package cn.gwssi.uomp.lanxin.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 蓝信应用号消息类型
 */
@Getter
@AllArgsConstructor
public enum LxMsgTypeEnum {


    TEXT("text"),
    LINK_CARD("linkCard"),
    APP_CARD("appCard");

    private String key;


    public static LxMsgTypeEnum getType(String key) {
        for (LxMsgTypeEnum enums : LxMsgTypeEnum.values()) {
            if (enums.key.equals(key)) {
                return enums;
            }
        }
        return null;
    }
}
