package cn.gwssi.uomp.lanxin.service;

import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.uomp.lanxin.config.LanXinProperties;
import cn.gwssi.uomp.lanxin.config.LanxinRequestUrl;
import cn.gwssi.uomp.lanxin.constant.LanxinErrorCode;
import cn.gwssi.uomp.lanxin.constant.LxTokenCache;
import cn.gwssi.uomp.lanxin.util.LanxinUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 蓝信授权相关接口
 */
@Slf4j
@Service
public class LxApiOauthService extends LxApiRequest {

    @Autowired
    private LanXinProperties lanXinProperties;

    /**
     * 刷新 app token
     *
     * @return
     */
    public String refreshAppToken() {
        LxTokenCache.removeAppToken(lanXinProperties.getAppId());
        return this.getAppToken();
    }

    /**
     * 获取 token，已过期则重新获取
     *
     * @return
     */
    public String getAppToken() {
        String appToken = LxTokenCache.getAppToken(lanXinProperties.getAppId());
        if (StringUtils.isEmpty(appToken)) {
            JSONObject appTokenResponse = buildAppToken();
            appToken = appTokenResponse.getString("appToken");
            Integer expiresIn = appTokenResponse.getInteger("expiresIn");
            LxTokenCache.saveAppToken(lanXinProperties.getAppId(), appToken, expiresIn);
        }
        return appToken;
    }

    public String getJsApiToken() {
        String jsApiToken = LxTokenCache.getJsToken(lanXinProperties.getAppId());
        if (StringUtils.isEmpty(jsApiToken)) {
            JSONObject jsApiTokenResponse = buildJsApiToken();
            jsApiToken = jsApiTokenResponse.getString("jsApiToken");
            Integer expiresIn = jsApiTokenResponse.getInteger("expiresIn");
            LxTokenCache.saveJsToken(lanXinProperties.getAppId(), jsApiToken, expiresIn);
        }
        return jsApiToken;
    }

    public String getUserToken(String code) {
        // todo user_token缓存 7200s
        String appToken = getAppToken();
        return buildUserToken(appToken, code).getString("user_token");
    }


    private JSONObject buildAppToken() {
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(lanXinProperties.getApiGwDomain() + LanxinRequestUrl.OAUTH_APP_TOKEN)
                .queryParam("appid", lanXinProperties.getAppId())
                .queryParam("secret", lanXinProperties.getAppSecret())
                .queryParam("grant_type", "client_credential");
        return this.doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
    }

    private JSONObject buildJsApiToken() {
        String appToken = getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(lanXinProperties.getApiGwDomain() + LanxinRequestUrl.OAUTH_JS_TOKEN)
                .queryParam("app_token", appToken);
        //.queryParam("user_token", userToken);
        return this.doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
    }

    private JSONObject buildUserToken(String appToken, String code) {
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(lanXinProperties.getApiGwDomain() + LanxinRequestUrl.OAUTH_USER_TOKEN)
                .queryParam("app_token", appToken)
                .queryParam("code", code)
                .queryParam("grant_type", "authorization_code");
        return this.doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
    }

    public HttpHeaders getDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        List<MediaType> accepts = new ArrayList<>(1);
        accepts.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(accepts);
        return headers;
    }


    public JSONObject buildConfigAuthData(String url) {
        int timestamp = LanxinUtils.getCurrentTimestamp();
        String nonceStr = LanxinUtils.generateNonceStr();
        JSONObject configData = new JSONObject();
        configData.put("appId", lanXinProperties.getAppId());
        configData.put("timestamp", timestamp);
        configData.put("nonceStr", nonceStr);
        try {
            configData.put("signature", this.genSignature(timestamp, nonceStr, url));
        } catch (Exception e) {
            log.error("SHA1 加密失败：{},错误消息：{}", JSONObject.toJSONString(configData), e.getMessage());
            throw new BusinessException("SHA1 加密失败");
        }
        return configData;
    }

    /**
     * 生成签名
     *
     * @param timestamp
     * @param nonceStr
     * @param url
     * @return
     * @throws Exception
     */
    private String genSignature(int timestamp, String nonceStr, String url) throws Exception {
        /**
         * 将这些参数使用URL键值对的格式 （即 key1=value1&key2=value2…）拼接成字符串string1。
         * 将value替换成当前值，然后对string1作 sha1 加密即可。
         * js_api_token=JSAPITOKEN&noncestr=NONCESTR&timestamp=TIMESTAMP&url=URL
         */
        String jsApiToken = getJsApiToken();
        StringBuffer buffer = new StringBuffer();
        buffer.append("js_api_token=").append(jsApiToken);
        buffer.append("&noncestr=").append(nonceStr);
        buffer.append("&timestamp=").append(timestamp);
        buffer.append("&url=").append(url);
        return LanxinUtils.shaEncode(buffer.toString());
    }


    @Override
    public JSONObject doRequest(UriComponentsBuilder builder, HttpHeaders headers, Object params, HttpMethod method) {
        JSONObject responseData = super.doRequest(builder, headers, params, method);
        Integer errCodeInt = responseData.getInteger("errCode");
        String errMsg = responseData.getString("errMsg");
        if (errCodeInt == null) {
            throw new BusinessException(errMsg);
        }

        int errorCode = errCodeInt.intValue();

        if (errorCode == LanxinErrorCode.OK.getCode()) {
            return responseData.getJSONObject("data");
        }
        //-1 缺少 APP_TOKEN
        if (errorCode == LanxinErrorCode.APP_TOKEN_LOST.getCode()) {
            builder.queryParam("app_token", getAppToken());
        }
        //- 3 无效的 APP_TOKEN
        if (errorCode == LanxinErrorCode.APP_TOKEN_INVALID.getCode()) {
            String appToken = this.refreshAppToken();
            builder.replaceQueryParam("app_token", appToken);
        }

        //如果时因为token 问题则进行重试
        JSONObject responseDataR = super.doRequest(builder, headers, params, method);
        errCodeInt = responseDataR.getInteger("errCode");
        errMsg = responseDataR.getString("errMsg");
        if (errCodeInt != null && errCodeInt.intValue() == LanxinErrorCode.OK.getCode()) {
            return responseDataR.getJSONObject("data");
        } else {
            log.error("lanxin request fail:{}", responseDataR.toJSONString());
            throw new BusinessException(errMsg);
        }
    }

    public LanXinProperties getLanXinProperties() {
        return lanXinProperties;
    }

    public JSONObject getUserBasic(String userToken) {
        String appToken = this.getAppToken();
        UriComponentsBuilder builder = UriComponentsBuilder
                .fromUriString(getLanXinProperties().getApiGwDomain() + LanxinRequestUrl.OAUTH_USER_BASIC)
                .queryParam("app_token", appToken)
                .queryParam("user_token", userToken);
        return doRequest(builder, getDefaultHeaders(), null, HttpMethod.GET);
    }
}
