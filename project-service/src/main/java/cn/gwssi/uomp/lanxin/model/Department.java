package cn.gwssi.uomp.lanxin.model;


import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.Objects;

public class Department {
    public static final String SERIALIZED_NAME_EXTERNAL_ID = "externalId";
    @SerializedName("externalId")
    private String externalId;
    public static final String SERIALIZED_NAME_ID = "id";
    @SerializedName("id")
    private String id;
    public static final String SERIALIZED_NAME_NAME = "name";
    @SerializedName("name")
    private String name;
    public static final String SERIALIZED_NAME_ORDER = "order";
    @SerializedName("order")
    private BigDecimal order;

    public Department() {
    }

    public Department externalId(String externalId) {
        this.externalId = externalId;
        return this;
    }

    @Nullable
    @ApiModelProperty("外部数据源人员唯一ID")
    public String getExternalId() {
        return this.externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public Department id(String id) {
        this.id = id;
        return this;
    }

    @Nullable
    @ApiModelProperty("所在分支ID")
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Department name(String name) {
        this.name = name;
        return this;
    }

    @Nullable
    @ApiModelProperty("所在分支名称")
    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Department order(BigDecimal order) {
        this.order = order;
        return this;
    }

    @Nullable
    @ApiModelProperty("人员排序值")
    public BigDecimal getOrder() {
        return this.order;
    }

    public void setOrder(BigDecimal order) {
        this.order = order;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            Department department = (Department)o;
            return Objects.equals(this.externalId, department.externalId) && Objects.equals(this.id, department.id) && Objects.equals(this.name, department.name) && Objects.equals(this.order, department.order);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.externalId, this.id, this.name, this.order});
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Department {\n");
        sb.append("    externalId: ").append(this.toIndentedString(this.externalId)).append("\n");
        sb.append("    id: ").append(this.toIndentedString(this.id)).append("\n");
        sb.append("    name: ").append(this.toIndentedString(this.name)).append("\n");
        sb.append("    order: ").append(this.toIndentedString(this.order)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
