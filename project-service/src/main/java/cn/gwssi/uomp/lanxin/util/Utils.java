package cn.gwssi.uomp.lanxin.util;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

public class Utils {
    public static byte[] getPKCS7Padding(String replyMsg, int blockSize) throws UnsupportedEncodingException {
        int dVal = blockSize - replyMsg.getBytes("utf-8").length % blockSize;

        for (int i = 0; i < dVal; ++i) {
            replyMsg = replyMsg + (char) (dVal % 255);
        }

        return replyMsg.getBytes("utf-8");
    }

    public static byte[] getPKCS7UnPadding(byte[] encrpBytes) {
        int elength = encrpBytes.length;
        int cnt = encrpBytes[elength - 1];
        if (cnt < 1 || cnt > 32) {
            cnt = 0;
        }
        return Arrays.copyOfRange(encrpBytes, 0, elength - cnt);
    }
}

