package cn.gwssi.uomp.lanxin.util;

import java.security.MessageDigest;
import java.util.Arrays;

public class Sha1 {
    public Sha1() {
    }

    public static String generateSignature(String token, String timeStamp, String nonce, String secretStr) throws Exception {
        String[] arrayStrs = new String[]{token, timeStamp, nonce, secretStr};
        Arrays.sort(arrayStrs);
        String sTemp = "";

        for (int i = 0; i < arrayStrs.length; ++i) {
            String s = arrayStrs[i];
            sTemp = sTemp + s;
        }

        MessageDigest md = MessageDigest.getInstance("SHA-1");
        md.update(sTemp.getBytes());
        byte[] digest = md.digest();
        String sign = "";
        for (int i = 0; i < digest.length; ++i) {
            byte b = digest[i];
            sign = sign + String.format("%02x", b);
        }
        return sign;
    }
}
