package cn.gwssi.uomp.lanxin.config;

import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 蓝信配置信息--让改成在系统参数中配置
 */
@Data
@Component
public class LanXinProperties {

    @Value("${lanxin.org-id:}")
    private String orgId;

//    @Value("${lanxin.app-id:}")
    private String appId = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_APPID");

//    @Value("${lanxin.app-secret:}")
    private String appSecret = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_APP_SECRET");

//    @Value("${lanxin.api-gw-domain:}")
    private String apiGwDomain = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_GW_DOMAIN");

//    @Value("${lanxin.auth-domain:}")
    private String authDomain = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_AUTH_DOMAIN");

    @Value("${lanxin.callback-aes-key:}")
    private String backAesKey;

    @Value("${lanxin.callback-sign-token:}")
    private String backSignToken;

//    @Value("${lanxin.msg-url:}")
    private String bpmUrl = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_BPM_URL");


    public String getAppId(){
        this.appId = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_APPID");
        return appId;
    }

    public String getAppSecret(){
        this.appSecret = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_APP_SECRET");
        return appSecret;
    }

    public String getApiGwDomain(){
        this.apiGwDomain = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_GW_DOMAIN");
        return apiGwDomain;
    }

    public String getAuthDomain(){
        this.authDomain = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_AUTH_DOMAIN");
        return authDomain;
    }

    public String getBpmUrl(){
        this.bpmUrl = SysPropertyUtil.getByAlias("UOMP_LX_CONFIG_BPM_URL");
        return bpmUrl;
    }
}
