package cn.gwssi.uomp.lanxin.controller;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.uomp.lanxin.service.ILxMessageService;
import cn.gwssi.uomp.lanxin.service.LxApiMessagesService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


@RestController
@RequestMapping("/lanxin/test")
public class TestLanxinController{

    @Autowired
    private LxApiMessagesService lxApiMessagesService;

    @Autowired
    private ILxMessageService lxMessageService;

    @RequestMapping("/getUserByMobile")
    public ResultMsg<String> getUserByMobile(@RequestParam("code") String code) {
        List<String> list = new ArrayList<>();
        list.add("86-18715652112");
        list.add("86-13911222771");
        list.add("86-18618183915");
        JSONObject result = lxApiMessagesService.searchLanxinUserByMobile(list,code);
        return ResultMsg.SUCCESS(result.toJSONString());
    }


    @RequestMapping("/sendMsg")
    public ResultMsg<String> sendMsg() {
        String result = lxMessageService.sendOaMessage1();
        return ResultMsg.SUCCESS(result);
    }

    @RequestMapping("/sendHookMsg")
    public ResultMsg<String> sendHookMsg(@RequestParam("address") String address) throws Exception{
        String result = lxMessageService.sendBotMsg(address);
        return ResultMsg.SUCCESS(result);
    }

    @RequestMapping("/updateUser")
    public ResultMsg<String> updateUser() {
        lxMessageService.updateLanxinUser();
        return ResultMsg.SUCCESS();
    }
}
