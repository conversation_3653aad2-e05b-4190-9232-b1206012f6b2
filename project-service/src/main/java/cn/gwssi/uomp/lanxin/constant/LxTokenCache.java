package cn.gwssi.uomp.lanxin.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class LxTokenCache {

    private static Map<String, LxToken> appTokenMap;

    private static Map<String, LxToken> jsTokenMap;

    static {
        appTokenMap = new HashMap<>();
        jsTokenMap = new HashMap<>();
    }

    public static String getAppToken(String appId) {
        return getToken(appId, appTokenMap);
    }

    public static String getJsToken(String appId) {
        return getToken(appId, jsTokenMap);
    }

    public static LxToken saveAppToken(String appId, String appToken, int expiresIn) {
        if (StringUtils.isEmpty(appToken)) {
            return null;
        }
        return saveToken(appId, appToken, appTokenMap, expiresIn);
    }

    public static LxToken saveJsToken(String appId, String jsToken, int expiresIn) {
        if (StringUtils.isEmpty(jsToken)) {
            return null;
        }
        return saveToken(appId, jsToken, jsTokenMap, expiresIn);
    }

    public static void removeAppToken(String appId) {
        appTokenMap.remove(appId);
    }

    public static void removeJsToken(String appId) {
        jsTokenMap.remove(appId);
    }


    private static LxToken saveToken(String appId, String token, Map tokenMap, int expiresIn) {
        LxToken lxToken = new LxToken();
        lxToken.setToken(token);
        // 减去 60s ,防止因为时间差导致的失效问题
        lxToken.setExpiredTime(getCurrentTime() + expiresIn - 60);
        tokenMap.put(appId, lxToken);
        return lxToken;
    }

    private static String getToken(String appId, Map<String, LxToken> tokenMap) {
        LxToken appToken = tokenMap.get(appId);
        if (appToken == null) {
            return null;
        }
        // 过期
        if (appToken.getExpiredTime() <= getCurrentTime()) {
            appTokenMap.remove(appId);
            return null;
        }
        return appToken.getToken();
    }

    private static long getCurrentTime() {
        return System.currentTimeMillis() / 1000;
    }

    public static class LxToken {

        private String token;

        private long expiredTime;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public long getExpiredTime() {
            return expiredTime;
        }

        public void setExpiredTime(long expiredTime) {
            this.expiredTime = expiredTime;
        }
    }
}
