package cn.gwssi.uomp.script;

import cn.gwssi.ecloudbpm.wf.core.model.TaskIdentityLink;
import cn.gwssi.ecloudframework.sys.api.groovy.IScript;
import cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.dao.GwBpmTaskDao;
import cn.gwssi.uomp.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;


@Component
public class CommonScipt implements IScript {
    @Autowired
    private HttpServletRequest request;
    @Resource
    private GwBpmTaskDao gwBpmTaskDao;
    /**
     * <AUTHOR>
     * @Description 在groovy腳本中返回request
     * @Date 15:46 2022/6/11
     * @param
     * @return
     **/
    public HttpServletRequest getRequest(){
        return request;
    }

    /**
     * <AUTHOR>
     * @Description //返回当前登录用户机构ID
     * @Date 16:56 2022/7/11
     * @return java.lang.String
     **/
    public String getCurrentGroupId(){
        return  ContextUtil.getCurrentGroupId();
    }
    /**
     * <AUTHOR>
     * @Description //返回剩余时限
     * @Date 10:48 2023/4/17
     * @param endTime
     * @return java.lang.String
     **/
    public String getTimeLimit(Date endTime) {
        return TimeUtils.getTime(new Date(),endTime, Calendar.MINUTE,0);
    }
    /**
     * <AUTHOR>
     * @Description //判断当前候选人是否是组
     * @Date 18:02 2023/5/23
     * @param instid
     * @return java.lang.String
     **/
    public String judgeIfGroup(String instid) {
        List<TaskIdentityLink> taskIdentityLinks = gwBpmTaskDao.getByInstId(instid);
        //候选人是组的时候 过滤掉保存 退回 同意、搁置按钮
        if(taskIdentityLinks!=null && taskIdentityLinks.size()>0 && "post".equals(taskIdentityLinks.get(0).getType())) {
            return "1";
        } else {
            return "2";
        }
    }
    /**
     * <AUTHOR>
     * @Description //判断当前候选人是否是组
     * @Date 18:02 2023/5/23
     * @param instid
     * @return map
     **/
    public Map<String,String> getIdentitys(String instid) {
        List<TaskIdentityLink> taskIdentityLinks = gwBpmTaskDao.getByInstId(instid);
        Map<String,String> resultData = new HashMap<>();
        if(taskIdentityLinks.size()>1) {
            StringBuilder names = new StringBuilder();
            //候选人多个 拼接一下名称
            for (TaskIdentityLink taskIdentityLink :taskIdentityLinks) {
                names.append(taskIdentityLink.getIdentityName());
                names.append(",");
            }
            resultData.put("name",names.substring(0,names.length()-1));
            resultData.put("type","more");
        } else if (taskIdentityLinks.size() == 1){
            resultData.put("name",taskIdentityLinks.get(0).getIdentityName());
            resultData.put("id",taskIdentityLinks.get(0).getIdentity());
            resultData.put("type","single");
        } else {

        }
        return resultData;

    }
    /**
     * <AUTHOR>
     * @Description //返回当前用户所在业务组的组长,如果在多个组，则返回多个组的组长
     * @Date 10:02 2023/10/24
     * @return
     * @return java.util.Set<cn.gwssi.ecloudframework.sys.api.model.SysIdentity>
     **/
    public Set<SysIdentity> getCurrentTeamLeader() {
        Set<SysIdentity> sysIdentities = new HashSet();
        List<DefaultIdentity> list = gwBpmTaskDao.getCurrentTeamLeader(ContextUtil.getCurrentUserId());
        for (DefaultIdentity it:list) {
            sysIdentities.add(it);
        }
        return sysIdentities;
    }

    /**
     * <AUTHOR>
     * @Description //当前用户所在业务组的XX主管,比如发布主管
     * @Date 10:44 2023/10/24
     * @return
     * @return java.util.Set<cn.gwssi.ecloudframework.sys.api.model.SysIdentity>
     **/
    public Set<SysIdentity> getCurrentTeamManager(String postname) {
        Set<SysIdentity> sysIdentities = new HashSet();
        List<DefaultIdentity> list = gwBpmTaskDao.getCurrentTeamManager(ContextUtil.getCurrentUserId(),postname);
        for (DefaultIdentity it:list) {
            sysIdentities.add(it);
        }
        return sysIdentities;
    }
}
