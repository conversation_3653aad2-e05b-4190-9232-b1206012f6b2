package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 时限暂停记录表
 * @TableName UOMP_TIMELIMIT_PAUSE
 */
@Data
public class UompTimelimitPause extends BaseModel{
    /**
     * 主键ID
     */
    private String id;

    /**
     * 业务请求ID
     */
    private String requestId;

    /**
     * 时限类型
     */
    private String limitType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 用时汇总
     */
    private Integer limitSum;

    /**
     * 汇总单位
     */
    private String limitUnit;

    /**
     * 时限状态（已完成/暂停中）
     */
    private String limitState;

    /**
     * 流程实例ID
     */
    private String instId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人机构ID
     */
    private String createOrgId;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}