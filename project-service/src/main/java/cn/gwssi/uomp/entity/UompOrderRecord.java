package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.api.model.IDModel;
import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

@Data
public class UompOrderRecord extends BaseModel implements IDModel {
    private static final long serialVersionUID = 2725101851065659865L;
    private String id;
    private String requestType;
    private String requestId;
    private Date startTime;
    private Date endTime;
    private String handlerType;
    private String handlerOrg;
    private String handlerOrgId;
    private String handlerGroup;
    private String handlerGroupId;
    private String handler;
    private String handlerNo;
    private String handlerId;
    private String handleCode;
    private String handleName; //处理人姓名
    private String handleOpin; //处理意见
    private Long duration; //处理时长
    private String instId;
    private String createBy;
    private Date createTime;
    private String createOrgId;
    private String updateBy;
    private Date updateTime;
    private String delFlag;
    private String taskId;
    private String taskName;//环节名称
    private Integer pauseNum;//暂停分钟数
    private String pauseType;
}
