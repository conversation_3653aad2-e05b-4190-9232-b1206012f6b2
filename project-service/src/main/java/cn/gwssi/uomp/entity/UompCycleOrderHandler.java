package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 周期性工任务接收对象表
 * @TableName UOMP_CYCLE_ORDER_HANDLER
 */
@Data
public class UompCycleOrderHandler implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 配置ID
     */
    private String manageId;

    /**
     * 接收者类型
     */
    private String handlerType;

    /**
     * 接收者ID
     */
    private String handlerId;

    /**
     * 接收者名称
     */
    private String handler;

    /**
     * 接收者名称机构id
     */
    private String handlerOrgId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}