package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息发送记录表
 * @TableName UOMP_MSG_SEND_RECORD
 */
@Data
public class UompMsgSendRecord implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 消息配置ID
     */
    private String msgId;

    /**
     * 业务数据ID
     */
    private String busId;

    /**
     * 接收者名称
     */
    private String userName;

    /**
     * 接收者ID
     */
    private String userId;

    /**
     * 发送消息内容
     */
    private String msgContent;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 下次发送时间
     */
    private Date nextSendTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}