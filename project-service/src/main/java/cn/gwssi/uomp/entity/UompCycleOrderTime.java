package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 周期性工任务执行周期配置表
 * @TableName UOMP_CYCLE_ORDER_TIME
 */
@Data
public class UompCycleOrderTime implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 配置ID
     */
    private String manageId;

    /**
     * 任务周期
     */
    private String execType;

    /**
     * 执行周期-天
     */
    private Integer execDay;

    /**
     * 执行周期-时
     */
    private String execHour;
    /**
     * 执行周期-分
     */
    private String execMinute;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}