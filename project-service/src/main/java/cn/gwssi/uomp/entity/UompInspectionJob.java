package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 巡检任务执行记录表
 * @TableName UOMP_INSPECTION_JOB
 */
@Data
public class UompInspectionJob extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * XXL定时任务ID
     */
    private String jobId;

    /**
     * 巡检类型
     */
    private String inspType;

    /**
     * 巡检计划ID
     */
    private String planId;

    /**
     * 巡检任务名称
     */
    private String jobName;

    /**
     * 计划巡检时间
     */
    private Date inspPlanTime;

    /**
     * 巡检完成时间
     */
    private Date completeTime;

    /**
     * 巡检结论
     */
    private String inspResult;

    /**
     * 完成状态
     */
    private String completeState;

    /**
     * 巡检状态
     */
    private String inspState;

    /**
     * 巡检模板ID
     */
    private String templateId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

    /**
     * 定时任务执行cron表达式
     */
    private String jobCron;

    /**
     * 工单标记（1未转2已转）
     */
    private String orderFlag;

    /**
     * 告警标记（1未生成2已生成）
     */
    private String alarmFlag;

    /**
     * 任务接收者ID
     */
    private String receiverId;

    /**
     * 任务最终执行人名称
     */
    private String executor;

    /**
     * 任务最终执行人ID
     */
    private String executorid;


}