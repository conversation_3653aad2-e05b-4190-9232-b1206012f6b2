package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息提醒邮件配置表
 * @TableName UOMP_MSG_MAIL
 */
@Data
public class UompMsgMail implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 发件人邮箱
     */
    private String mailAddress;

    /**
     * 邮箱密码
     */
    private String mailPassword;

    /**
     * 发送用户名
     */
    private String mailNickname;

    /**
     * SMTP服务器
     */
    private String mailHost;

    /**
     * 端口
     */
    private String mailPort;

    /**
     * 是否使用 SSL安全连接true/false
     */
    private String mailSsl;

    /**
     * 测试接收邮箱
     */
    private String mailReceiveAddress;

    /**
     * 测试邮件内容
     */
    private String mailReceiveContent;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人机构ID
     */
    private String createOrgId;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}