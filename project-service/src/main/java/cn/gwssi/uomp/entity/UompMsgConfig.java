package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公共-消息提醒配置表
 * @TableName UOMP_MSG_CONFIG
 */
@Data
public class UompMsgConfig implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 消息分类
     */
    private String msgType;

    /**
     * SLA协议ID
     */
    private String slaid;

    /**
     * SLA协议ID
     */
    private String priid;

    /**
     * 工单级别
     */
    private String orderLevel;

    /**
     * 告警类型
     */
    private String notifyType;

    /**
     * 目标时间
     */
    private Integer timeLimit;

    /**
     * 时间单位
     */
    private String timeUnit;

    /**
     * 日志告警级别
     */
    private String notifyLevel;

    /**
     * 消息名称
     */
    private String msgName;

    /**
     * 消息图标ICON
     */
    private String msgIcon;

    /**
     * 消息描述
     */
    private String msgDesc;

    /**
     * 发送对象
     */
    private String notifyObject;

    /**
     * 发送对象中文
     */
    private String notifyObjectName;

    /**
     * 指定人员名称
     */
    private String notifyPerson;

    /**
     * 指定人员ID
     */
    private String notifyPersonIds;

    /**
     * 执行频次
     */
    private String execFreq;

    /**
     * 是否支持重复发送(1是0否)
     */
    private String ifRepeat;

    /**
     * 重复类型（日周月）
     */
    private String repeatType;

    /**
     * 业务对象KEY
     */
    private String busKey;

    /**
     * 条件脚本
     */
    private String condScript;

    /**
     * 消息内容
     */
    private String msgCont;

    /**
     * 消息类型
     */
    private String sendType;

    /**
     * 消息类型中文
     */
    private String sendTypeName;

    /**
     * 站内消息URL
     */
    private String innerUrl;

    /**
     * 手机消息URL
     */
    private String phoneUrl;

    /**
     * 即时通讯消息URL
     */
    private String imUrl;

    /**
     * 短信消息URL
     */
    private String smsUrl;

    /**
     * 邮件消息URL
     */
    private String emailUrl;

    /**
     * 公众号消息URL
     */
    private String offAccountUrl;

    /**
     * 诚信IM URL
     */
    private String cxUrl;

    /**
     * 小程序URL
     */
    private String smrUrl;

    /**
     * 是否静默
     */
    private String ifSilence;

    /**
     * 静默时效类型(0时间段1周期)
     */
    private String silenceType;

    /**
     * 静默时间-起始
     */
    private Date silenceDateStart;

    /**
     * 静默时间截至
     */
    private Date silenceDateEnd;

    /**
     * 静默周期
     */
    private String silenceCycle;

    /**
     * 静默资产ID
     */
    private String resourceId;

    /**
     * 状态（0有效1无效）
     */
    private String msgState;

    /**
     * 启用时间
     */
    private Date pubDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

    /**
     * 关联的模板id
     */
    private String msgId;

    /**
     * 模板名称
     */
    private String configName;

    /**
     * 模板描述
     */
    private String configMemo;

    private String timeType;


    /**
     * 蓝信群组
     */
    private String notifyLxGroupChat;


}