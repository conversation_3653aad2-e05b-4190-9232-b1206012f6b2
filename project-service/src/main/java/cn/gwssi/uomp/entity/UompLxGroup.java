package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.*;

/**
 * 蓝信群组配置表
 *
 * @TableName UOMP_LX_GROUP
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UompLxGroup extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 群组类型
     */
    private String groupType;

    /**
     * 业务组id
     */
    private String teamId;

    /**
     * 业务组名称
     */
    private String teamName;

    /**
     * 机器人地址
     */
    private String robotAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}