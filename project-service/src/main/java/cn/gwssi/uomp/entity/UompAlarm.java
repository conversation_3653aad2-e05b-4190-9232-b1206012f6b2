package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.*;

import java.util.Date;

/**
 * 告警信息表
 *
 * @TableName UOMP_ALARM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UompAlarm extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 告警id
     */
    private String alarmId;

    /**
     * 告警主机ip
     */
    private String ipInstance;

    /**
     * 一级配置分组id
     */
    private String groupId;

    /**
     * 一级配置分组名称
     */
    private String groupName;

    /**
     * 资产id
     */
    private String resourceId;

    /**
     * 资产名称
     */
    private String resourceName;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 资产编号
     */
    private String resourceNo;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警级别
     */
    private String alarmLevel;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 工单id
     */
    private String orderId;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单标题
     */
    private String orderTitle;

    /**
     * 工单流程实例id
     */
    private String instId;

    /**
     * 告警状态 0-待处置 1-处置中 2-已处置
     */
    private String alarmStatus;

    /**
     * 告警时间
     */
    private Date alarmTime;

    /**
     * 恢复时间
     */
    private Date handleTime;

    /**
     * 标记误报 0-未误报 1-误报
     */
    private String isMisinformation;

    /**
     * 处理时间
     */
    private Date disposeTime;

    /**
     * 处理人
     */
    private String disposePerson;

    /**
     * 处理人方法 1-转工单 2-误报 3-忽略
     */
    private String disposeMethod;

    /**
     * 处理原因
     */
    private String disposeReason;

    /**
     * 备注
     */
    private String disposeRemark;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

}