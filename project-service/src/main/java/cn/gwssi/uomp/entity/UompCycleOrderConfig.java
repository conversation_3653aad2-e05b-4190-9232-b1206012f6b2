package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 周期性工单配置表
 * @TableName UOMP_CYCLE_ORDER_CONFIG
 */
@Data
public class UompCycleOrderConfig implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 工单类型
     */
    private String orderType;

    /**
     * 工单名称
     */
    private String orderTitle;

    /**
     * 工单描述
     */
    private String orderDesc;

    /**
     * 事件分类ID
     */
    private String slaId;

    /**
     * 事件分类ID
     */
    private String slaName;

    /**
     * 事件分类名称
     */
    private String eventType;

    /**
     * 事件级别ID
     */
    private String eventLevelId;

    /**
     * 事件级别名称
     */
    private String eventLevel;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板JSON
     */
    private String templateJson;

    /**
     * 模板版本号
     */
    private String templateVersion;

    /**
     * 任务执行周期
     */
    private String execType;

    /**
     * 执行周期-周
     */
    private String execWeek;

    /**
     * 执行周期-月-方式
     */
    private String execMonthType;

    /**
     * 执行周期说明
     */
    private String execRemark;

    /**
     * XXL定时任务ID
     */
    private String jobId;

    /**
     * CRON表达式
     */
    private String cronCode;

    /**
     * 是否跳过节假日(1是0否)
     */
    private String ifSkipHoliday;

    /**
     * 计划状态
     */
    private String planState;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 计划截至时间
     */
    private Date planEndTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

    /**
     * 执行周期列表
     */
    private List<UompCycleOrderTime> timeList;

    /**
     * 执行对象列表
     */
    private List<UompCycleOrderHandler> handlerList;
    /**
     * 流程定义ID
     */
    private String flowDefId;
    /**
     * 流程定义key
     */
    private String flowDefKey;

}