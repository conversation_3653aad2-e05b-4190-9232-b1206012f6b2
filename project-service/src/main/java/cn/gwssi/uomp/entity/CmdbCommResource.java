package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 公共资源表   

 * @TableName CMDB_COMM_RESOURCE
 */
@Data
public class CmdbCommResource implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 流程实例ID
     */
    private String instId;

    /**
     * 基线版本ID
     */
    private String baselineId;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 资产编号
     */
    private String resourceNo;

    /**
     * 资产名称
     */
    private String resourceName;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 资产状态
     */
    private String resourceState;

    /**
     * 生产厂商
     */
    private String producer;

    /**
     * 设备责任人
     */
    private String deviceDutyor;

    /**
     * 维保日期开始
     */
    private Date tendDateStart;

    /**
     * 维保日期截至
     */
    private Date tendDateEnd;

    /**
     * 所属部门
     */
    private String deviceUnitName;

    /**
     * 所属部门ID
     */
    private String deviceUnitId;

    /**
     * 供应商ID
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 所属合同ID
     */
    private String contractId;

    /**
     * 所属合同名称
     */
    private String contractName;

    /**
     * 所属项目ID
     */
    private String projectId;

    /**
     * 所属项目名称
     */
    private String projectName;

    /**
     * 设备状态
     */
    private String deviceState;

    /**
     * IP地址
     */
    private String ipAddr;

    /**
     * 提交审核时间
     */
    private Date submitTime;

    /**
     * 审核状态0暂存1待审核2审核通过3审核不通过
     */
    private String auditState;

    /**
     * 审核结论
     */
    private String auditResult;

    /**
     * 审核完成时间
     */
    private Date auditEndTime;

    /**
     * 审核人名称
     */
    private String auditer;

    /**
     * 审核人ID
     */
    private String auditerid;

    /**
     * 是否发布公告
     */
    private String ifNotice;

    /**
     * 公告时间
     */
    private Date noticeTime;

    /**
     * 附件信息
     */
    private String fileStr;

    /**
     * 审核意见
     */
    private String auditOpin;

    /**
     * 设备购买金额
     */
    private BigDecimal devicePrice;

    /**
     * 元数据扩展字段1
     */
    private String metadata1;

    /**
     * 元数据扩展字段2
     */
    private String metadata2;

    /**
     * 元数据扩展字段3
     */
    private String metadata3;

    /**
     * 元数据扩展字段4
     */
    private String metadata4;

    /**
     * 元数据扩展字段5
     */
    private String metadata5;

    /**
     * 扩展字段1
     */
    private String extends1;

    /**
     * 扩展字段2
     */
    private String extends2;

    /**
     * 扩展字段3
     */
    private String extends3;

    /**
     * 扩展字段4
     */
    private String extends4;

    /**
     * 扩展字段5
     */
    private String extends5;

    /**
     * 扩展字段6
     */
    private String extends6;

    /**
     * 扩展字段7
     */
    private String extends7;

    /**
     * 扩展字段8
     */
    private String extends8;

    /**
     * 扩展字段9
     */
    private String extends9;

    /**
     * 扩展字段10
     */
    private String extends10;

    /**
     * 扩展字段11
     */
    private String extends11;

    /**
     * 扩展字段12
     */
    private String extends12;

    /**
     * 扩展字段13
     */
    private String extends13;

    /**
     * 扩展字段14
     */
    private String extends14;

    /**
     * 扩展字段15
     */
    private String extends15;

    /**
     * 扩展字段16
     */
    private String extends16;

    /**
     * 扩展字段17
     */
    private String extends17;

    /**
     * 扩展字段18
     */
    private String extends18;

    /**
     * 扩展字段19
     */
    private String extends19;

    /**
     * 扩展字段20
     */
    private String extends20;

    /**
     * 扩展日期1
     */
    private Date extendsDate1;

    /**
     * 扩展日期2
     */
    private Date extendsDate2;

    /**
     * 扩展日期3
     */
    private Date extendsDate3;

    /**
     * 扩展日期4
     */
    private Date extendsDate4;

    /**
     * 扩展日期5
     */
    private Date extendsDate5;

    /**
     * 扩展数字1
     */
    private Integer extendsInt1;

    /**
     * 扩展数字2
     */
    private Integer extendsInt2;

    /**
     * 扩展数字3
     */
    private Integer extendsInt3;

    /**
     * 扩展数字4
     */
    private Integer extendsInt4;

    /**
     * 扩展数字5
     */
    private Integer extendsInt5;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 修改人员ID
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志(0:无效，1:有效)
     */
    private String delFlag;

    /**
     * 其他字段
     */
    private String otherCols;

}