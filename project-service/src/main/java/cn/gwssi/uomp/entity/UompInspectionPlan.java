package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 巡检计划管理
 * @TableName UOMP_INSPECTION_PLAN
 */
@Data
public class UompInspectionPlan extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * XXL定时任务ID
     */
    private String jobId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板JSON
     */
    private String templateJson;

    /**
     * 模板版本号
     */
    private String templateVersion;

    /**
     * 执行方式
     */
    private String execType;

    /**
     * 巡检类型
     */
    private String inspType;

    /**
     * 执行周期-周
     */
    private String execWeek;

    /**
     * 执行周期-月-方式
     */
    private String execMonthType;

    /**
     * 执行周期说明
     */
    private String execRemark;

    /**
     * CRON表达式
     */
    private String cronCode;

    /**
     * 计划状态
     */
    private String planState;

    /**
     * 任务接收人ID
     */
    private String receiverId;

    /**
     * 任务接收人名称
     */
    private String receiverName;

    /**
     * 任务接收人JSON
     */
    private String receiver;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 计划截至时间
     */
    private Date planEndTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;
    /**
     * 执行时间子表
     */
    private List<UompInspectExecTime> execTimeList;

    /**
     * 是否跳过节假日（1是0否）
     */
    private String ifSkipHoliday;

}