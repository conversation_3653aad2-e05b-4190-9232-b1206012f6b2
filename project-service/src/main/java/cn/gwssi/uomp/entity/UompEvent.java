package cn.gwssi.uomp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 事件工单请求表
 * @TableName UOMP_EVENT
 */
@Data
public class UompEvent implements Serializable {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单ID
     */
    private String orderId;

    /**
     * 父工单ID
     */
    private String parentOrderId;

    /**
     * SLAID
     */
    private String slaId;

    /**
     * SLA名称
     */
    private String slaName;

    /**
     * SLA级别(事件级别)
     */
    private String slaLevel;

    /**
     * 申报人姓名
     */
    private String applicant;

    /**
     * 申报人所属部门
     */
    private String applicantOrg;

    /**
     * 申报人联系电话
     */
    private String applicantTel;

    /**
     * 申报人部门ID
     */
    private String applicantOrgId;

    /**
     * 申报人部门名称
     */
    private String applicantOrgName;

    /**
     * 事件登记人ID
     */
    private String registrantId;

    /**
     * 事件登记人姓名
     */
    private String registrant;

    /**
     * 事件登记人部门
     */
    private String registrantOrg;

    /**
     * 事件登记人编号
     */
    private String registrantNo;

    /**
     * 事件级别
     */
    private String eventLevel;

    /**
     * 事件标题
     */
    private String eventTitle;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * 服务分类
     */
    private String serviceType;

    /**
     * 涉及资产ID
     */
    private String resourceId;

    /**
     * 所属项目
     */
    private String projectId;

    /**
     * 附件信息
     */
    private String fileStr;

    /**
     * 是否需要反馈
     */
    private String ifFeedback;

    /**
     * 反馈时间
     */
    private Date feedbackTime;

    /**
     * 是否拆分子事件
     */
    private String ifSplitChild;

    /**
     * 期望解决时间
     */
    private Date expectTime;

    /**
     * SLA协议最晚解决时间
     */
    private Date slaOverTime;

    /**
     * 环节到期时间
     */
    private Date linkOverTime;

    /**
     * 流程到期时间
     */
    private Date flowOverTime;

    /**
     * 是否挂起(1是0否)
     */
    private String ifPause;

    /**
     * 流程实例ID
     */
    private String instId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 冗余字段1
     */
    private String extends1;

    /**
     * 冗余字段2
     */
    private String extends2;

    /**
     * 冗余字段3
     */
    private String extends3;

    /**
     * 冗余字段4
     */
    private String extends4;

    /**
     * 冗余字段5
     */
    private String extends5;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人机构ID
     */
    private String createOrgId;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

    /**
     * 运维组ID
     */
    private String teamId;

    /**
     * 是否VIP（1是0否）
     */
    private String ifVip;

    /**
     * 事件类别
     */
    private String eventType;

}