package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

@Data
public class UompInspectExecTime extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 计划ID
     */
    private String planId;

    /**
     * 巡检类型
     */
    private String inspType;

    /**
     * 执行周期-天
     */
    private int execDay;
    /**
     * 执行周期-时
     */
    private String execHour;
    /**
     * 执行周期-分
     */
    private String execMinute;
    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;
}
