package cn.gwssi.uomp.entity;

import cn.gwssi.ecloudframework.base.core.model.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 工单表
 * @TableName UOMP_WORK_ORDER
 */
@Data
public class UompWorkOrder extends BaseModel {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 请求ID（事件、问题、变更等业务ID）
     */
    private String requestId;

    /**
     * 工单类型
     */
    private String orderType;

    /**
     * 工单编号
     */
    private String orderNo;

    /**
     * 工单级别
     */
    private String orderLevel;

    /**
     * 工单标题
     */
    private String orderTitle;

    /**
     * 工单状态
     */
    private String orderState;

    /**
     * 服务分类
     */
    private String serviceType;

    /**
     * 涉及资产ID
     */
    private String resourceId;

    /**
     * 办理人姓名
     */
    private String handler;

    /**
     * 办理人ID
     */
    private String handlerId;

    /**
     * 办理组
     */
    private String handlerGroup;

    /**
     * 办理组ID
     */
    private String handlerGroupId;

    /**
     * 申报人姓名
     */
    private String applicant;

    /**
     * 申报人电话
     */
    private String applicantTel;

    /**
     * 环节计时时间
     */
    private Date linkStartTime;

    /**
     * 流程计时时间
     */
    private Date flowStartTime;

    /**
     * 环节到期时间
     */
    private Date linkOverTime;

    /**
     * 流程到期时间
     */
    private Date flowOverTime;

    /**
     * 总时限
     */
    private Integer timeLimit;

    /**
     * 是否挂起(1是0否)
     */
    private Integer ifPause;

    /**
     * 挂起时间
     */
    private Date pauseTime;

    /**
     * 暂停时限汇总
     */
    private Integer pauseSum;

    /**
     * 临期期限
     */
    private Integer remindLimit;

    /**
     * 流程实例ID
     */
    private String instId;

    /**
     * 处理完成时间
     */
    private Date finishTime;

    /**
     * 是否需要反馈
     */
    private String ifFeedback;

    /**
     * 是否拆分子请求
     */
    private String ifSplitChild;

    /**
     * 父工单流程实例ID
     */
    private String parentInstId;

    /**
     * 登记人姓名
     */
    private String registrant;

    /**
     * 登记人ID
     */
    private String registrantId;
    /**
     * 所属项目ID
     */
    private String projectId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人机构ID
     */
    private String createOrgId;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0有效1无效)
     */
    private String delFlag;

    private static final long serialVersionUID = 1L;

    /**
     * slaid
     */
    private String slaId;

    /**
     * slaid
     */
    private String priId;

    /**
     * ifVip
     */
    private String ifVip;

    /**
     * 重启次数
     */
    private Integer restart;

}