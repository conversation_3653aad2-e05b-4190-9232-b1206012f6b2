package cn.gwssi.uomp.constants;

/**
 * <AUTHOR>
 * @Description //运维管理平台常量类
 * @Date 9:32 2023/5/12
 **/
public class CommonConstants {

    //是否 1是0否
    public static final String YES = "1";//是
    public static final String NO = "0";//否


    public static final String WORK_ORDER_FLOW_KEY_EVENT = "UOMP_FLOW_EVENT";//事件流程key
    public static final String WORK_ORDER_FLOW_KEY_ISSUES = "UOMP_FLOW_ISSUES";//问题流程key
    public static final String WORK_ORDER_FLOW_KEY_ALTER = "UOMP_FLOW_ALTER";//变更流程key
    public static final String WORK_ORDER_FLOW_KEY_PUBLISH = "UOMP_FLOW_PUBLISH";//发布流程key
    public static final String WORK_ORDER_FLOW_KEY_CAPACITY = "UOMP_FLOW_CAPACITY";//容量流程


    //处理状态 0-未处置 1-处置中 2-已处置
    public static final String NO_HANDLE = "0";
    public static final String IN_HANDLE = "1";
    public static final String YES_HANDLE = "2";

    public static final String INTERFACE_STATUS_SUCCESS_CODE = "200"; //成功的状态码
}
