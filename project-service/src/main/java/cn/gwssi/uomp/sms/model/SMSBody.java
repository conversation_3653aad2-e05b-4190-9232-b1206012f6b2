package cn.gwssi.uomp.sms.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class SMSBody implements Serializable {

    /**
     * 发送方应用编码
     */
    private String senderAppCode;

    /**
     * 邮件或手机号集合
     */
    private List<String> addrlist = new ArrayList<>();

    /**
     * 发送者名称
     */
    private String sender;

    /**
     * 消息体
     */
    private String messagebody;

    /**
     * 标题
     */
    private String title;

    /**
     * 消息类型：1：邮件；2：短信
     */
    private String messageType;

    public String getSenderAppCode() {
        return senderAppCode;
    }

    public void setSenderAppCode(String senderAppCode) {
        this.senderAppCode = senderAppCode;
    }

    public List<String> getAddrlist() {
        return addrlist;
    }

    public void setAddrlist(List<String> addrlist) {
        this.addrlist = addrlist;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getMessagebody() {
        return messagebody;
    }

    public void setMessagebody(String messagebody) {
        this.messagebody = messagebody;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
}
