package cn.gwssi.uomp.enums;

import lombok.Getter;

@Getter
public enum NotifyType {
    ALARM("1","告警"),
    NEAR("2","临期"),
    OVERTIME("3","超期");

    final String code;
    final String name;

    NotifyType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (NotifyType notifyType : NotifyType.values()) {
            if (notifyType.getCode().equals(code)) {
                return notifyType.getName();
            }
        }
        return null;
    }

}
