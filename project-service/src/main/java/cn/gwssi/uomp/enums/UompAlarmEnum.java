package cn.gwssi.uomp.enums;

import com.alibaba.druid.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 代码集枚举类
 * <AUTHOR>
 * @Date 2023/6/28 14:49
 * @Version 1.0
 */
public interface UompAlarmEnum {

    /**
     * 告警类型
     */
    @Getter
    @AllArgsConstructor
    enum AlertTypeEnum {
        ZJZT("server_is_down", "主机状态"),
        H3ZBWD("hh3c_temperture_over_threshold", "华三主板温度"),
        CPUSYL("cpu_usage_over_threshold", "CPU使用率"),
        NCSYL("memory_usage_over_threshold", "内存使用率"),
        CPRL("root_partition_usage_over_threshold", "磁盘容量");

        final String code;
        final String name;

        public static String getNameByCode(String value) {
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            for (AlertTypeEnum alertTypeEnum : AlertTypeEnum.values()) {
                if (value.equals(alertTypeEnum.getCode())) {
                    return alertTypeEnum.getName();
                }
            }
            return null;
        }
    }


    /**
     * 告警级别
     */
    @Getter
    @AllArgsConstructor
    enum AlertLevelEnum {
        WARNING("warning", "警告告警"),
        CRITICAL("critical", "严重告警"),
        EMERGENCY("emergency", "紧急告警");

        final String code;
        final String name;

        public static String getNameByCode(String value) {
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            for (AlertLevelEnum alertLevelEnum : AlertLevelEnum.values()) {
                if (value.equals(alertLevelEnum.getCode())) {
                    return alertLevelEnum.getName();
                }
            }
            return null;
        }
    }

}
