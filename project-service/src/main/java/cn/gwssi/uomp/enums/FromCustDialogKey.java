package cn.gwssi.uomp.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description //系统自定义对话框key
 * @Date 9:31 2023/09/14
 **/
@Getter
public enum FromCustDialogKey {
    MULTI_PERSON("G_USER_C","人员多选","multi-person"),
    SINGLE_PERSON("G_USER_R","人员单选","single-person"),
    MULTI_ORG("G_ORG_C","机构多选","multi-org"),
    SINGLE_ORG("G_ORG_R","机构单选","single-org");

    final String code;
    final String name;
    final String key;

    FromCustDialogKey(String code, String name,String key) {
        this.code = code;
        this.name = name;
        this.key = key;
    }

    public static String getCodeByKey(String key) {
        for (FromCustDialogKey dateUnit : FromCustDialogKey.values()) {
            if (dateUnit.getKey().equals(key)) {
                return dateUnit.getCode();
            }
        }
        return null;
    }

}
