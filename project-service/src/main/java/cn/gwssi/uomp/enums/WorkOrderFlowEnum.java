package cn.gwssi.uomp.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description //工作流定义ID和流程key
 * @Date 9:22 2023/08/03
 **/
@Getter
public enum WorkOrderFlowEnum {

    EVENT("1","UOMP_FLOW_EVENT","882509503841959937"),
    ISSUES("2","UOMP_FLOW_ISSUES","882509612029313025"),
    ISSUES_PATENT("3","UOMP_FLOW_ISSUES_PATENT","890344781067583489"), //专利的问题流程
    EVENT_PATENT("11","UOMP_FLOW_EVENT_PATENT","890344798944755713"); //专利的事件流程

    private String orderType;
    private String flowKey;
    private String defId;

    WorkOrderFlowEnum(String orderType,String flowKey,String defId) {
        this.orderType = orderType;
        this.flowKey = flowKey;
        this.defId = defId;
    }

    public static String getFlowDefId(String orderType) {
        for (WorkOrderFlowEnum tableTypeEnum : WorkOrderFlowEnum.values()) {
            if (tableTypeEnum.orderType.equals(orderType)) {
                    return tableTypeEnum.defId;
            }
        }
        return null;
    }

    public static String getFlowDefKey(String orderType) {
        for (WorkOrderFlowEnum tableTypeEnum : WorkOrderFlowEnum.values()) {
            if (tableTypeEnum.orderType.equals(orderType)) {
                return tableTypeEnum.flowKey;
            }
        }
        return null;
    }
}
