package cn.gwssi.uomp.enums;

import lombok.Getter;

@Getter
public enum DateUnit {
    MINUTE("minute","分"),
    HOUR("hour","时"),
    DAY("day","天"),
    WEEK("week","周"),
    MONTH("month","月"),
    YEAR("year","年");

    final String code;
    final String name;

    DateUnit(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (DateUnit dateUnit : DateUnit.values()) {
            if (dateUnit.getCode().equals(code)) {
                return dateUnit.getName();
            }
        }
        return null;
    }

}
