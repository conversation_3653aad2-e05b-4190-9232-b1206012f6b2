package cn.gwssi.uomp.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description //工单类型枚举类
 * @Date 20:55 2023/08/31
 **/
@Getter
public enum WorkOrderType {
    EVENT("1","事件"),
    ISSUES("2","问题"),
    ALTER("3","变更"),
    PUBLISH("4","发布"),
    CAPACITY("5","容量");

    final String code;
    final String name;

    WorkOrderType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (WorkOrderType dateUnit : WorkOrderType.values()) {
            if (dateUnit.getCode().equals(code)) {
                return dateUnit.getName();
            }
        }
        return null;
    }

}
