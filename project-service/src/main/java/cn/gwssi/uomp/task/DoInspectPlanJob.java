package cn.gwssi.uomp.task;

import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.uomp.service.UompInspectionPlanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.HashMap;
import java.util.Map;

@JobHandler(value = "doInspectPlanJob")
@Component
@Slf4j
public class DoInspectPlanJob extends IJobHandler {
    @Autowired
    UompInspectionPlanService uompInspectionPlanService;
    /**
     * <AUTHOR>
     * @Description //自动执行定时任务
     * @Date 11:31 2023/09/27
     * @param
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public ReturnT<String> execute(String param) throws Exception {
        //uompInspectionPlanService.doJob(param);
        MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
        Map<String, Object> interParam = new HashMap<>();
        interParam.put("id",param);
        return apiService.execute("POST", "/cn.gwssi/uomp/inspect/task/dojob",interParam);
    }
}
