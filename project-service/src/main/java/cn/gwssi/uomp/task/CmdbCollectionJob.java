package cn.gwssi.uomp.task;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.HashMap;
import java.util.Map;

@JobHandler(value = "cmdbCollectionJob")
@Component
@Slf4j
public class CmdbCollectionJob extends I<PERSON>ob<PERSON>andler {

    @Autowired
    MagicAPIService apiService;
   /**
    * <AUTHOR>
    * @Description //探针采集
    * @Date 18:01 2023/7/25
    * @param param
    * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
    **/
    public ReturnT<String> execute(String param) throws Exception {
        Map<String,Object> context = new HashMap();
        JSONObject obj1=apiService.execute("POST","/cmdb/collection/probe_job",context);
        return new ReturnT<>(obj1.toJSONString());
    }
}
