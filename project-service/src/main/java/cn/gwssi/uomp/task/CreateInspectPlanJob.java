package cn.gwssi.uomp.task;

import cn.gwssi.uomp.service.UompInspectionPlanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@JobHandler(value = "createInspectPlanJob")
@Component
@Slf4j
public class CreateInspectPlanJob extends IJobHandler {
    @Autowired
    UompInspectionPlanService uompInspectionPlanService;
    /**
     * <AUTHOR>
     * @Description //巡检计划管理：定时扫描 生成每日定时任务
     * @Date 11:31 2023/09/27
     * @param
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public ReturnT<String> execute(String param) throws Exception {
        uompInspectionPlanService.createJob();
        return null;
    }
}
