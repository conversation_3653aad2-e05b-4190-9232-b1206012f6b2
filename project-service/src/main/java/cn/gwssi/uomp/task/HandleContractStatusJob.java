package cn.gwssi.uomp.task;

import cn.gwssi.uomp.dao.UompContractMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@JobHandler(value = "handleContractStatusJob")
@Component
public class HandleContractStatusJob extends IJobHandler {

    protected Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private UompContractMapper uompContractMapper;

    /**
     * 执行定时任务
     *
     * @param s
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2023/7/3 11:45
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        LOG.info("------------------------------处理合同状态定时任务开始执行--------------------------------------{}", s);
        try {
            Date date = new Date();

            //查询合同数据(只处理非 暂存和失效 状态的合同数据)
            String[] statusArr = {"1", "5"};
            List<String> statusList = Arrays.asList(statusArr);
            List<Map<String, Object>> contractList = uompContractMapper.selectListByNotStatus(statusList);

            //查询每个合同对应的关联资产列表数据
            for (Map<String, Object> map : contractList) {
                String contractId = String.valueOf(map.get("id"));

                //查看该合同维保结束日期是否过期，过期直接置为失效(为空不做判断,继续往下判断资产)
                if (map.get("quality_end_day") != null){
                    String qualityEndDay = String.valueOf(map.get("quality_end_day"));
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date qualityEndDayTime = sdf.parse(qualityEndDay + " 23:59:59");
                    if (date.after(qualityEndDayTime)){
                        uompContractMapper.updateStatusById(contractId, "5");
                        //修改完直接不判断资产了
                        continue;
                    }
                }

                //根据合同id查询相关的资产数据
                List<Map<String, Object>> propertyList = uompContractMapper.selectPropertyListByContractId(contractId);

                //处理一下资产状态(没有维保时间的按正常处理)
                for (Map<String, Object> propertyMap : propertyList) {
                    Date startTime = (Date) propertyMap.get("startTime");
                    Date endTime = (Date) propertyMap.get("endTime");

                    //status : 1-正常 2-过保 3-临期
                    if (startTime == null || endTime == null) {
                        propertyMap.put("status", "1");
                    } else {
                        //结束时间按照23:59:59算
                        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        endTime = sdf2.parse(sdf1.format(endTime) + " 23:59:59");

                        //超过结束时间就是过保，开始时间大于当前时间，结束时间小于结束时间，且7天内的就是临期，剩下统一为正常
                        if (date.after(endTime)) {
                            propertyMap.put("status", "2");
                        } else if ((date.after(startTime) || date.equals(startTime)) && (date.before(endTime) || date.equals(endTime))) {
                            long diff = endTime.getTime() - date.getTime();
                            long day = diff / (24 * 60 * 60 * 1000);
                            if (day < 7) {
                                propertyMap.put("status", "3");
                            } else {
                                propertyMap.put("status", "1");
                            }
                        } else {
                            propertyMap.put("status", "1");
                        }
                    }
                }

                //根据资产数据判断合同状态是否更新 合同状态 1-暂存 2-生效 3-正常  4-临期 5-失效 6-过保
                //1.如果没有资产，状态变更为正常
                if (CollectionUtils.isEmpty(propertyList)) {
                    uompContractMapper.updateStatusById(contractId, "3");
                } else {
                    boolean flag = true;
                    //2.非废弃资产有过保,则合同状态为过保;
                    for (Map<String, Object> propertyMap : propertyList) {
                        String status = String.valueOf(propertyMap.get("status"));
                        if ("2".equals(status)) {
                            uompContractMapper.updateStatusById(contractId, "6");
                            flag = false;
                            break;
                        }
                    }
                    //3.非废弃资产无过保有临期，则合同状态为临期
                    //如果没有过保的数据，就查看是否有临期数据，flag = true 就是没有过保数据
                    if (flag) {
                        for (int i = 0; i < propertyList.size(); i++) {
                            Map<String, Object> propertyMap = propertyList.get(i);
                            String status = String.valueOf(propertyMap.get("status"));
                            if ("3".equals(status)) {
                                uompContractMapper.updateStatusById(contractId, "4");
                                break;
                            }
                            //4.非废弃资产均正常，则合同状态为正常
                            //如果最后一次循环还没break，证明该批次都是正常，所以修改为正常
                            if (i == propertyList.size() - 1) {
                                uompContractMapper.updateStatusById(contractId, "3");
                            }
                        }
                    }
                }
            }
        } catch (ParseException e) {
            return new ReturnT<>(500, "执行失败:" + e);
        }

        LOG.info("------------------------------处理合同状态定时任务执行结束--------------------------------------{}", s);
        return new ReturnT<>("执行成功");
    }
}
