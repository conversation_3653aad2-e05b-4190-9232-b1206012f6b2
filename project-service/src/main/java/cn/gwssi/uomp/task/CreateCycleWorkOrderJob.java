package cn.gwssi.uomp.task;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.module.orgCustom.api.service.UserCustomService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.entity.UompCycleOrderConfig;
import cn.gwssi.uomp.entity.UompCycleOrderHandler;
import cn.gwssi.uomp.entity.UompCycleOrderTime;
import cn.gwssi.uomp.service.UompWorkOrderService;
import cn.gwssi.uomp.utils.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JobHandler(value = "createCycleWorkOrderJob")
@Component
@Slf4j
public class CreateCycleWorkOrderJob extends IJobHandler {
    @Resource
    UompWorkOrderService uompWorkOrderService;
    @Resource
    private UserCustomService userService;
    /**
     * <AUTHOR>
     * @Description //周期性工单：定时扫描 生成每日定时任务
     * @Date 10:08 2023/12/01
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     **/
    public ReturnT<String> execute(String param) throws Exception {
        List<UompCycleOrderConfig> list = uompWorkOrderService.getCycleOrderConfigList(param);
        //然后循环判断 当天是否有任务要执行
        //如果有任务要执行 则生成定时任务
        //如果手动执行，则删除生成的XXL定时任务
        for(UompCycleOrderConfig config : list) {
            ContextUtil.setCurrentUser(userService.getUserById(config.getCreateBy()));
            if("1".equals(config.getExecType())) {
                //周巡检 检查今天星期几是否满足
                if(config.getExecWeek() == null || !config.getExecWeek().contains(DateUtils.getWeekDays(null)+"")) {
                    continue;
                }
            } else {
                //月巡检 检查今天日期是否满足
                //如果指定本月最后一天执行，则判断当前日期是否为本月最后一天 否则不执行
                if("2".equals(config.getExecType()) && "0".equals(config.getExecMonthType()) && !DateUtils.ifLastDayOfMonth()) {
                    continue;
                }
            }
            String datestr = DateUtils.getCurrentDateStr("yyyyMMdd");
            for(UompCycleOrderTime execTime : config.getTimeList()) {
                Calendar calendar = Calendar.getInstance();
                int newDay = calendar.get(Calendar.DAY_OF_MONTH); //当前天
                //如果非今天执行 则不执行
                if(!"0".equals(execTime.getExecType()) && execTime.getExecDay()!=null && newDay != execTime.getExecDay()) {
                    continue;
                }
                //如果定时任务执行时 时间已经超过计划预计时间 则不生成工单
                if(!DateUtils.checkIfOverNowTime(execTime.getExecHour(),execTime.getExecMinute())) {
                    continue;
                }
                //计划执行时间
                String execHour = execTime.getExecHour();
                if(StringUtil.isNotEmpty(execTime.getExecMinute())) {
                    execHour = execHour + ":" + execTime.getExecMinute() + ":00";
                } else {
                    execHour = execHour + ":00:00";
                }
                String plan_time_str = DateUtils.getCurrentDateStr("yyyy-MM-dd") + " " + execHour;
                //生成工单
                JSONObject flowparam = new JSONObject();
                flowparam.put("USER_ID",ContextUtil.getCurrentUserId()); //以谁的身份发起流程 现在默认以配置创建人身份发起流程
                flowparam.put("ORDER_TYPE",config.getOrderType());
                flowparam.put("EVENT_TITLE",config.getOrderTitle() + "[" + datestr + execTime.getExecHour() + "]");
                flowparam.put("EVENT_DESC",config.getOrderDesc());
                flowparam.put("APPLICANT",ContextUtil.getCurrentUserName());
                flowparam.put("APPLICANT_TEL","");
                flowparam.put("SLA_ID",config.getSlaId());
                flowparam.put("SLA_NAME",config.getSlaName());
                flowparam.put("EVENT_LEVEL",config.getEventLevelId());
                flowparam.put("SLA_LEVEL",config.getEventLevel());
                flowparam.put("EVENT_TYPE",config.getEventType());//故障 还是 请求
                flowparam.put("IF_FEEDBACK","0");
                flowparam.put("IF_VIP","0");
                flowparam.put("IF_SECURITY","0");
                flowparam.put("IF_IMPORTANT","0");
                flowparam.put("PROJECT_ID","");
                flowparam.put("FLOW_DEF_ID",config.getFlowDefId());
                flowparam.put("FLOW_DEF_KEY",config.getFlowDefKey());

                for(UompCycleOrderHandler handler:config.getHandlerList()) {
                    //根据配置ID和计划执行时间判断是否重复，重复则不在生成
                    int count = uompWorkOrderService.findOrderByTimeAndConfigId(config.getId(),plan_time_str,handler.getHandlerId());
                    if(count > 0) {
                        continue;
                    }
                    JSONObject nodeUser = new JSONObject();
                    nodeUser.put("type",handler.getHandlerType());
                    nodeUser.put("id",handler.getHandlerId());
                    nodeUser.put("name",handler.getHandler());
                    nodeUser.put("orgId",handler.getHandlerOrgId());
                    flowparam.put("nodeUsers",nodeUser);
                    try{
                        ResultMsg resultMsg = uompWorkOrderService.createWorkOrder(flowparam,config.getOrderType());
                        //记录日志
                        String instid = (String)resultMsg.getData();
                        Map<String,String> record = new HashMap<>();
                        record.put("inst_id",instid);
                        record.put("event_title",config.getOrderTitle());
                        record.put("manage_id",config.getId());
                        record.put("id", IdUtil.getSuid());
                        record.put("create_by", ContextUtil.getCurrentUserId());
                        record.put("plan_exec_time", plan_time_str);
                        record.put("handler_id", handler.getHandlerId());
                        record.put("handler", handler.getHandler());
                        record.put("handler_type", handler.getHandlerType());
                        uompWorkOrderService.insertCycleRecord(record);
                    } catch (Exception e) {
                        log.error("周期性任务执行失败，错误信息：",e);

                    }
                }
            }
        }
        return null;
    }
}
