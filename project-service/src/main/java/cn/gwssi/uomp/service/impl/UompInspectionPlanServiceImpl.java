package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.uomp.dao.UompInspectionPlanMapper;
import cn.gwssi.uomp.entity.UompInspectExecTime;
import cn.gwssi.uomp.entity.UompInspectionJob;
import cn.gwssi.uomp.entity.UompInspectionPlan;
import cn.gwssi.uomp.service.UompInspectionJobService;
import cn.gwssi.uomp.service.UompInspectionPlanService;
import cn.gwssi.uomp.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class UompInspectionPlanServiceImpl implements UompInspectionPlanService{
    @Autowired
    UompInspectionPlanMapper planMapper;
    @Autowired
    UompInspectionJobService jobService;
    /**
     * <AUTHOR>
     * @Description //创建每日计划
     * @Date 10:50 2023/10/10
     **/
    public void createJob() {
        //先查启用状态下的 计划列表
        List<UompInspectionPlan> planList = this.getUsedPlanList();
        //然后循环判断 当天是否有任务要执行
        //如果有任务要执行 则生成定时任务
        //如果手动执行，则删除生成的XXL定时任务
        for(UompInspectionPlan plan : planList) {
            if("1".equals(plan.getInspType())) {
                //周巡检 检查今天星期几是否满足
                if(plan.getExecWeek() == null || !plan.getExecWeek().contains(DateUtils.getWeekDays(null)+"")) {
                    continue;
                }
            } else {
                //月巡检 检查今天日期是否满足
                //如果指定本月最后一天执行，则判断当前日期是否为本月最后一天 否则不执行
                if("0".equals(plan.getExecMonthType()) && !DateUtils.ifLastDayOfMonth()) {
                    continue;
                }
            }
            jobService.createJobByPlan(plan);
        }
    }
    /**
     * <AUTHOR>
     * @Description //获取启用的且在计划时间内的计划列表
     * @Date 10:51 2023/10/10
     * @return java.util.List<cn.gwssi.uomp.entity.UompInspectionPlan>
     **/
    public List<UompInspectionPlan> getUsedPlanList() {
        List<UompInspectionPlan> planList =  planMapper.getUsedPlanList();
        //子表处理
        for(UompInspectionPlan plan : planList) {
            List<UompInspectExecTime> timeList =  planMapper.getExecTimeList(plan.getId());
            plan.setExecTimeList(timeList);
        }
        return planList;
    }
    /**
     * <AUTHOR>
     * @Description /自动执行计划
     * @Date 14:50 2023/10/10
     **/
    public void doJob(String param) {
        UompInspectionJob job = jobService.selectByPrimaryKey(param);
        //处理检查单逻辑

        //更新状态为已完成
        UompInspectionJob newjob = new UompInspectionJob();
        newjob.setId(param);
        newjob.setCompleteTime(new Date());// 完成时间
        newjob.setCompleteState("2");//完成状态 2已完成
        jobService.updateByPrimaryKeySelective(newjob);
        //调用API里的函数，删除定时任务
        MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
        Map<String, Object> interParam = new HashMap<>();
        interParam.put("job_id", job.getJobId());
        apiService.invoke("/xxl/removeJob",interParam);
    }
}




