package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.sys.core.manager.SysAuthorizationManager;
import cn.gwssi.ecloudframework.sys.core.model.SysAuthorization;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.dao.UompResourceMapper;
import cn.gwssi.uomp.entity.CmdbCommResource;
import cn.gwssi.uomp.service.UompResourceService;
import cn.gwssi.uomp.utils.ValidateUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Clob;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
*
*/
@Service
@Slf4j
public class UompResourceServiceImpl implements UompResourceService {
    @Autowired
    UompResourceMapper uompResourceMapper;
    @Autowired
    SysAuthorizationManager sysAuthorizationManager;

    public void insertResource(Map<String,Object> param) {
        uompResourceMapper.insertResource(param);
    }

    public int insertResourceByList(List<Map<String, Object>> paramList) {
        int successnum = 0;
        //循环处理 有则更新 无则插入
        List<Map<String, Object>> noResNoList = new ArrayList<>();
        for (Map<String, Object> param : paramList) {
            //如果资源编号为空 则单独处理 默认插入
            if(StringUtil.isEmpty((String)param.get("RESOURCE_NO"))){
                noResNoList.add(param);
            } else {
                successnum = successnum + uompResourceMapper.insertOrUpdateResource(param);
            }
        }
        if(noResNoList.size() > 0) {
            successnum = successnum + uompResourceMapper.insertResourceByList(noResNoList);
        }
        return successnum;
    }

    public List<Map<String,String>> getListById(Map<String,Object> param) {
        return uompResourceMapper.getListById(param);
    }

    public List<String> getAllUserRights(String userId) {
        List<String> rightsList =  uompResourceMapper.getAllUserRights(ContextUtil.getCurrentUserId());
        rightsList.add(userId + "-user");
        return rightsList;
    }

    public Map<String,String> getModelData(String baseline_id) {

        return uompResourceMapper.getModelData(baseline_id);
    }

    public List<Map<String, Object>> getColConfigList(String baseline_id) {
        return uompResourceMapper.getColConfigList(baseline_id);
    }

    public List<String> getResourceNolist() {
        return uompResourceMapper.getResourceNolist();
    }

    public List<CmdbCommResource> getUnCheckResourceList(CmdbCommResource resource){
        return uompResourceMapper.getUnCheckResourceList(resource);
    }

    public void saveSysAuthorization(List<Map<String, Object>> paramList){
        List<Map<String,String>> teamlist = uompResourceMapper.getTeamList(paramList);
        Map<String,String> team = new HashMap<>();
        for(Map<String,String> it : teamlist) {
            if(!team.containsKey(it.get("id"))){
                team.put(it.get("name"),it.get("id"));
            }
        }
        for (Map<String,Object> item : paramList) {
            if(StringUtil.isEmpty((String) item.get("RIGHTS_IDENTITY_NAME_"))) {
                continue;
            }
            if(team.get(item.get("RIGHTS_IDENTITY_NAME_")) == null) {
                continue;
            }
            CmdbCommResource cmdbCommResource = this.getResourceByNo((String) item.get("RESOURCE_NO"),(String) item.get("MODEL_ID"));
            SysAuthorization sysAuthorization = new SysAuthorization();
            if(cmdbCommResource!=null && StringUtil.isNotEmpty((String) item.get("RESOURCE_NO"))){
                sysAuthorization.setRightsTarget(cmdbCommResource.getId());
            } else {
                sysAuthorization.setRightsTarget((String)item.get("ID"));
            }
            sysAuthorization.setRightsIdentity(team.get(item.get("RIGHTS_IDENTITY_NAME_")));//业务组ID
            sysAuthorization.setRightsIdentityName((String)item.get("RIGHTS_IDENTITY_NAME_"));//业务组名称
            sysAuthorization.setRightsType("team");
            sysAuthorization.setRightsObject("CMDB_COMM_RESOURCE");
            List<SysAuthorization> sysAuthorizationList = new ArrayList<>();
            sysAuthorizationList.add(sysAuthorization);
            sysAuthorizationManager.createAll(sysAuthorizationList,sysAuthorization.getRightsTarget(), "CMDB_COMM_RESOURCE");

        }

    }

    public List<Map<String, Object>> getAllResourceList() {
        List<Map<String, Object>> reslist = uompResourceMapper.getAllResourceList();
        Map<String,List> config = new HashMap<>();
        for(Map<String,Object> dataMap:reslist) {
            List<Map<String, String>> colList = new ArrayList<>();
            if(config.containsKey(dataMap.get("MODEL_KEY"))) {
                colList = config.get(dataMap.get("MODEL_KEY"));
            } else {
                colList = uompResourceMapper.getColMapList((String) dataMap.get("BASELINE_ID"));
                config.put((String) dataMap.get("MODEL_KEY"),colList);
            }
            //循环字段 把映射字段与保存在other_cols的字段解析出来
            boolean if_handler_other = false;
            for (Map<String, String> col:colList) {
                //把json 字段读出来 放在map里边，只操作一次即可
                if("OTHER_COLS".equals(col.get("mapping_col")) && !if_handler_other){
                    try{
                        Clob jsonColb = (Clob) dataMap.get("OTHER_COLS");
                        String json = jsonColb.getSubString(1, (int) jsonColb.length());
                        Map<String,String> otherMap = (Map)JSON.parseObject(json);
                        dataMap.putAll(otherMap);
                        dataMap.remove("OTHER_COLS");
                    }catch (Exception e) {
                        log.error(e.getMessage());
                    }
                    if_handler_other = true;
                } else if("OTHER_COLS".equals(col.get("mapping_col")) && if_handler_other){
                   continue;
                } else if(!StringUtils.equals(col.get("col_key"),col.get("mapping_col"))) {
                    //映射字段取出来 重新存 key是用户定义的key 然后删除原来的扩展key
                    dataMap.put( col.get("col_key"),dataMap.get(col.get("mapping_col")));
                    dataMap.remove(col.get("mapping_col"));
                } else {

                }
            }
        }
        return reslist;
    }

    public int judgeIfHasAuthUpdate(String model_id,String res_no){
        CmdbCommResource cmdbCommResource = this.getResourceByNo(res_no,model_id);
        //数据库无数据 或者 是暂存或不通过状态 可修改
        if(cmdbCommResource == null ||"0,3".contains(cmdbCommResource.getAuditState())) {
            return 0;
        } else if("1".equals(cmdbCommResource.getAuditState())) {
            //待审核 不允许更新
            return 1;
        }else {
            //审核通过的情况下 判断权限
            Map<String,Object> param = new HashMap<>();
            List<String> userRightList = this.getAllUserRights(ContextUtil.getCurrentUserId());
            //先判断是否有权限更新
            param.put("USER_RIGHT_LIST",userRightList);
            param.put("RESOURCE_ID",cmdbCommResource.getId());
            int if_has_auth_update = uompResourceMapper.judgeIfHasAuthUpdate(param);
            if(if_has_auth_update>0) {
                return 0;
            } else {
                return 2; //无权更新
            }
        }
    }
    /**
     * <AUTHOR>
     * @Description //TODO
     * @Date 9:43 2023/09/21
     * @param ctrl_valid_rule
     * @param col_val
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    public boolean validateValue(String ctrl_valid_rule, String col_val) {
        boolean result = true;
        switch (ctrl_valid_rule){
            case "number":
                result = ValidateUtils.isNumber(col_val);
                break;
            case "email":
                result = ValidateUtils.isEmail(col_val);
                break;
            case "url":
                result = ValidateUtils.isUrl(col_val);
                break;
            case "date":
                result = ValidateUtils.isDate(col_val);
                break;
            case "time":
                result = ValidateUtils.isDateTime(col_val);
                break;
            case "chinese":
                result = ValidateUtils.isChinese(col_val);
                break;
            case "qq":
                result = ValidateUtils.isQQNumber(col_val);
                break;
            case "phonenumber":
                result = ValidateUtils.isMobile(col_val);
                break;
            case "digits":
                //整数
                result = ValidateUtils.isInteger(col_val);
                break;
            case "nodigitsstart":
                //不以数字开头
                result = ValidateUtils.isHeadNoNumber(col_val);
                break;
            case "letiable":
                //字母或下划线
                result = ValidateUtils.isLetterOrLine(col_val);
                break;
            case "letiable2":
                //字母数字和下划线
                result = ValidateUtils.isUsername(col_val);
                break;
            case "cardNo":
                //身份证号
                result = ValidateUtils.isIdCard(col_val);
                break;
            case "letirule":
                //以字母开头
                result = ValidateUtils.isHeadLetter(col_val);
                break;
        }
        return result;
    }
    /**
     * <AUTHOR>
     * @Description //错误提示
     * @Date 9:43 2023/09/21
     * @param ctrl_valid_rule
     * @return String
     **/
    public String getValidateMsg(String ctrl_valid_rule) {
        String result = "";
        switch (ctrl_valid_rule){
            case "number":
                result = "请填写数字；";
                break;
            case "email":
                result = "请填写正确的邮件地址；";
                break;
            case "url":
                result = "请填写正确的URL地址；";
                break;
            case "date":
                result = "请填写正确的日期；";
                break;
            case "time":
                result = "请填写正确的时间；";
                break;
            case "chinese":
                result = "请填写中文；";
                break;
            case "qq":
                result = "请填写正确的QQ号码；";
                break;
            case "phonenumber":
                //手机号码
                result = "请填写正确的手机号码；";
                break;
            case "digits":
                //整数
                result = "请填写整数；";
                break;
            case "nodigitsstart":
                //不以数字开头
                result = "不能以数字开头；";
                break;
            case "letiable":
                //字母或下划线
                result = "必须是字母或下划线；";
                break;
            case "letiable2":
                //字母数字和下划线
                result = "必须是字母数字和下划线；";
                break;
            case "cardNo":
                //身份证号
                result = "请填写正确的身份证号；";
                break;
            case "letirule":
                //以字母开头
                result = "必须以字母开头；";
                break;
        }
        return result;
    }
    /**
     * <AUTHOR>
     * @Description //根据资源编号和模型ID获取唯一数据
     * @Date 15:06 2023/10/18
     * @param resource_no
     * @param model_id
     * @return cn.gwssi.uomp.entity.CmdbCommResource
     **/
    private CmdbCommResource getResourceByNo(String resource_no,String model_id){
        CmdbCommResource resource = new CmdbCommResource();
        resource.setResourceNo(resource_no);
        resource.setModelId(model_id);
        return uompResourceMapper.getResourceByNo(resource);
    }
}
