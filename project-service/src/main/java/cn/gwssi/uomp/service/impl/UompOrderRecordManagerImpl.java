package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudbpm.wf.api.constant.ActionType;
import cn.gwssi.ecloudbpm.wf.api.constant.EventType;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.BaseActionCmd;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.FlowRequestParam;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.TaskActionCmd;
import cn.gwssi.ecloudbpm.wf.api.engine.context.BpmContext;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmDefinitionManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmInstanceManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskOpinionManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmDefinition;
import cn.gwssi.ecloudbpm.wf.core.model.BpmInstance;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTask;
import cn.gwssi.ecloudbpm.wf.core.model.TaskIdentityLink;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefualtTaskActionCmd;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.manager.impl.BaseManager;
import cn.gwssi.ecloudframework.base.rest.util.LogOperateUtil;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.constants.CommonConstants;
import cn.gwssi.uomp.dao.UompOrderRecordDao;
import cn.gwssi.uomp.entity.UompOrderRecord;
import cn.gwssi.uomp.service.UompOrderRecordManager;
import cn.gwssi.uomp.utils.DateUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Clob;
import java.util.*;

@Component
@Slf4j
public class UompOrderRecordManagerImpl extends BaseManager<String, UompOrderRecord> implements UompOrderRecordManager {

    @Resource
    UompOrderRecordDao uompOrderRecordDao;
    @Resource
    private BpmInstanceManager bpmInstanceManager;
    @Resource
    BpmTaskManager bpmTaskManager;
    @Resource
    BpmTaskOpinionManager bpmTaskOpinionManager;
    @Resource
    private BpmDefinitionManager bpmDefinitionManager;

    public void updateOrderRecordByTaskId(UompOrderRecord uompOrderRecord) {
        uompOrderRecordDao.updateOrderRecordByTaskId(uompOrderRecord);
    }

   public UompOrderRecord getByTaskId(String taskId){
       return uompOrderRecordDao.getRecordByTaskId(taskId);
    }

    public void insertOrderRecordByActionCmd(BaseActionCmd actionCmd,int limit) {
        //查询上次操作结束日期 作为本次操作开始日期
        UompOrderRecord lastDate = uompOrderRecordDao.getLastHandleTime(actionCmd.getInstanceId());
        UompOrderRecord uompOrderRecord = formatObj(actionCmd);
        uompOrderRecord.setTaskId(null);
        uompOrderRecord.setStartTime(lastDate.getEndTime());
        uompOrderRecord.setHandlerId(ContextUtil.getCurrentUserId());
        uompOrderRecord.setHandlerGroupId(lastDate.getHandlerGroupId());
        JSONObject conf = actionCmd.getExtendConf();
        //搁置原因字段
        if(conf!=null && StringUtil.isNotEmpty(conf.getString("pause_type"))) {
            uompOrderRecord.setPauseType(conf.getString("pause_type"));
            String pause_reason = conf.getString("pause_name");
            StringBuilder sbr = new StringBuilder();
            sbr.append("搁置原因：" + pause_reason);
            if(StringUtil.isNotEmpty(uompOrderRecord.getHandleOpin())) {
                sbr.append("<br>");
                sbr.append("搁置说明：" + uompOrderRecord.getHandleOpin());
            }
            uompOrderRecord.setHandleOpin(sbr.toString());
        }
        BpmTask task = bpmTaskManager.get(actionCmd.getTaskId());
        if(task!=null) {
            uompOrderRecord.setTaskName(task.getName());
        }
        uompOrderRecordDao.create(uompOrderRecord);

        //本次操作日期 更新为当前未结束操作的转入日期
        UompOrderRecord updateRecord = new UompOrderRecord();
        updateRecord.setInstId(uompOrderRecord.getInstId());
        updateRecord.setStartTime(uompOrderRecord.getEndTime());
        updateRecord.setPauseNum(limit);
        uompOrderRecordDao.updateOrderRecordByInstid(updateRecord);

    }

    public UompOrderRecord formatObj(BaseActionCmd actionCmd) {
        UompOrderRecord uompOrderRecord = new UompOrderRecord();
        uompOrderRecord.setTaskId(actionCmd.getTaskId());
        uompOrderRecord.setInstId(actionCmd.getInstanceId());
        uompOrderRecord.setDelFlag(CommonConstants.NO);
        uompOrderRecord.setStartTime(new Date());
        uompOrderRecord.setEndTime(new Date());
        uompOrderRecord.setHandleCode(actionCmd.getActionName());
        uompOrderRecord.setHandleName(actionCmd.getDoActionName());
        uompOrderRecord.setHandleOpin(actionCmd.getOpinion());
        uompOrderRecord.setCreateOrgId(ContextUtil.getCurrentGroupId());
        uompOrderRecord.setHandler(ContextUtil.getCurrentUserName());
        return uompOrderRecord;
    }

    public void updateWorkOrder(SysIdentity sysIdentity, String instanceId,String name){
        //根据流程实例ID查询候选人信息
        List<TaskIdentityLink> identList = uompOrderRecordDao.getInstIndentityList(instanceId);
        StringBuilder names = new StringBuilder();
        StringBuilder ids = new StringBuilder();
        StringBuilder orgids = new StringBuilder();
        for(TaskIdentityLink link:identList) {
            names.append(link.getIdentityName());
            names.append(",");
            ids.append(link.getIdentity());
            ids.append(",");
            orgids.append(link.getOrgId());
            orgids.append(",");
        }
        if(names.indexOf(",")>0) {
            names.setLength(names.length()-1);
            ids.setLength(ids.length()-1);
            orgids.setLength(orgids.length()-1);
        }
        Map<String,String> param = new HashMap<>();
        param.put("inst_id",instanceId);
        if("user".equals(sysIdentity.getType())){
            param.put("handler",names.toString());
            param.put("handler_id",ids.toString());
            param.put("handler_group_id",orgids.toString()); //工单候选人 orgid即业务组ID
            param.put("handler_group",null);
            param.put("order_state","1");
        } else {
            param.put("handler",null);
            param.put("handler_id",null);
            param.put("handler_group",names.toString());
            param.put("handler_group_id",ids.toString());
            param.put("order_state","3");//指派到组 更新工单状态是未指派
        }
        uompOrderRecordDao.updateWorkOrder(param);
    }

    public void deleteByTaskId(String taskid) {
        uompOrderRecordDao.deleteByTaskId(taskid);
    }

    public void updateOrderRecordStart(UompOrderRecord uompOrderRecord) {
        uompOrderRecordDao.updateOrderRecordStart(uompOrderRecord);
    }

    public Map<String,String> getEventDataByInstId(String instid) {
        return uompOrderRecordDao.getEventDataByInstId(instid);
    }

    /**
     * <AUTHOR>
     * @Description //TODO
     * @Date 16:08 2023/7/29
     * @return
     * @return java.lang.String
     **/
    public String getModifyRecord(DefualtTaskActionCmd taskModel,String hisOpinion){
        JSONObject data = taskModel.getBusData();
        if(data == null) {
            return "";
        }
        if(taskModel.getOpinion() == null){
            taskModel.setOpinion("");
        }
        Iterator<String> it=data.keySet().iterator();
        UompOrderRecord record = new UompOrderRecord();
        record.setTaskId(taskModel.getTaskId());
        record.setInstId(taskModel.getInstanceId());
        try {
            while (it.hasNext()) {
                String objKey = it.next();
                if (data.getJSONObject(objKey) != null) {
                    JSONObject mainData = data.getJSONObject(objKey);
                    JSONObject order = mainData.getJSONObject("UOMP_WORK_ORDER");
                    String modifyRecord = order.getString("MODIFY_RECORD");
                    if(StringUtil.isEmpty(modifyRecord)) {
                        break;
                    } else {
                        Map<String,String> colNameMap = new HashMap<>();
                        StringBuilder colSbr = new StringBuilder();
                        //拼接查询字段
                        JSONArray array = JSONArray.parseArray(modifyRecord);
                        for (int i=0;i<array.size();i++){
                            JSONObject colObj = (JSONObject) array.get(i);
                            String col_name = colObj.getString("col_name");
                            String col_key = colObj.getString("col_key");
                            colNameMap.put(col_key,col_name);
                            colSbr.append(col_key);
                            colSbr.append(",");
                        }
                        colNameMap.put("task_id",taskModel.getTaskId());
                        colNameMap.put("inst_id",taskModel.getInstanceId());
                        colNameMap.put("table_name",objKey);
                        colNameMap.put("col_list",colSbr.toString());
                        if(StringUtil.isEmpty(colSbr.toString())) {
                            break;
                        }
                        StringBuilder sbr = new StringBuilder();
                        List<Map> changeList = uompOrderRecordDao.getBusDataChangeList(colNameMap);
                        if(changeList !=null && changeList.size()>0) {
                            for(Map map:changeList) {
                                sbr.append(DateUtils.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
                                sbr.append("  修改了" + colNameMap.get(map.get("col_key")));
                                sbr.append("，");
                                //临时方法 待优化 todo
                                Clob oldClobData = (Clob)  map.get("old_data");;
                                Clob newClobData = (Clob)  map.get("new_data");;
                                String oldData = oldClobData.getSubString(1,(int) oldClobData.length());
                                String newData = newClobData.getSubString(1,(int) newClobData.length());
                                if("PROJECT_ID".equals(map.get("col_key"))) {
                                    sbr.append("旧值为：\"" + this.getProjectNames(oldData) + "\"，");
                                    sbr.append("新值为：\"" + this.getProjectNames(newData) + "\"");
                                } else if ("RESOURCE_ID".equals(map.get("col_key"))) {
                                    sbr.append("旧值为：\"" + this.getResourceNames(oldData) + "\"，");
                                    sbr.append("新值为：\"" + this.getResourceNames(newData) + "\"");
                                } else {
                                    sbr.append("旧值为：\"" + (oldData==null?"":oldData) + "\"，");
                                    sbr.append("新值为：\"" + (newData==null?"":newData) + "\"");
                                }
                                sbr.append("<br>");
                            }

                        } else if (ActionType.SAVE.getKey().equals(taskModel.getActionName())){
                            Map<String,String> oldDataMap = uompOrderRecordDao.getBusDataByInstId(colNameMap);
                            sbr.append(StringUtil.isEmpty(taskModel.getOpinion())?"":(taskModel.getOpinion() + "<br>"));
                            int i = 0;
                            for (String key : oldDataMap.keySet()) {
                                //新值与旧值不一致 则计入记录
                                if(!Objects.equals(oldDataMap.get(key),mainData.getString(key))) {
                                    if(i > 0) {
                                        sbr.append("<br>");
                                    }
                                    sbr.append(DateUtils.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
                                    sbr.append("  修改了" + colNameMap.get(key));
                                    sbr.append("，");
                                    //临时方法 待优化 todo
                                    if("PROJECT_ID".equals(key)) {
                                        sbr.append("旧值为：\"" + this.getProjectNames(oldDataMap.get(key)) + "\"，");
                                        sbr.append("新值为：\"" + this.getProjectNames((String) mainData.get(key)) + "\"");
                                    } else if ("RESOURCE_ID".equals(key)) {
                                        sbr.append("旧值为：\"" + this.getResourceNames(oldDataMap.get(key)) + "\"，");
                                        sbr.append("新值为：\"" + this.getResourceNames((String) mainData.get(key)) + "\"");
                                    } else {
                                        sbr.append("旧值为：\"" + (oldDataMap.get(key)==null?"":oldDataMap.get(key)) + "\"，");
                                        sbr.append("新值为：\"" + (mainData.get(key)==null?"":mainData.get(key)) + "\"");
                                    }
                                    i++;
                                }
                            }
                        } else {
                            //其他情况
                        }
                        if(StringUtil.isEmpty(sbr.toString())) {
                            break;
                        }
                        //历史意见
                        if(StringUtil.isNotEmpty(hisOpinion)){
                            return hisOpinion + "<br>" + sbr.toString();
                        } else {
                            return sbr.toString();
                        }

                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return taskModel.getOpinion() + (StringUtil.isNotEmpty(hisOpinion)?hisOpinion:"");
    }

    private String getProjectNames(String ids) {
        if(StringUtil.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            return uompOrderRecordDao.getProjectNames(list);
        }
        return "";
    }

    private String getResourceNames(String ids) {
        if(StringUtil.isNotEmpty(ids)) {
            List<String> list = Arrays.asList(ids.split(","));
            return uompOrderRecordDao.getResourceNames(list);
        }
        return "";
    }

    /**
     * <AUTHOR>
     * @Description //全局脚本插入工单日志
     * @Date 9:10 2023/08/15
     * @param actionCmd
     * @param eventType
     * @return
     **/
    public void insertOrderRecordByScript(BaseActionCmd actionCmd,String eventType) {
        //判断是否是工单流程 不是的话则不做处理
        BpmInstance bpmInstance = (BpmInstance) actionCmd.getBpmInstance();
        if(bpmInstance == null) {
            bpmInstance = bpmInstanceManager.get(actionCmd.getInstanceId());
        }
        if(bpmInstance.getDefKey() == null || !bpmInstance.getDefKey().startsWith("UOMP_FLOW")) {
            return;
        }
        if (eventType == EventType.TASK_CREATE_EVENT.getKey()) {// 任务创建
            TaskActionCmd taskActionCmd = (TaskActionCmd) actionCmd;
            UompOrderRecord uompOrderRecord = this.formatObj(actionCmd);
            uompOrderRecord.setEndTime(null);
            //uompOrderRecord.setHandleName(null);
            //更新处理人
            List<SysIdentity> sysIdentityList = actionCmd.getBpmIdentity(actionCmd.getNodeId());
            if(sysIdentityList != null && sysIdentityList.size()>0) {
                SysIdentity sysIdentity = sysIdentityList.get(0);
                //更新工单信息 20240103改为API更新 解决多任务多候选人问题
                //this.updateWorkOrder(sysIdentity,actionCmd.getInstanceId(),this.getSysIdentityName(sysIdentityList));
                this.updateWorkOrder(sysIdentity,actionCmd.getInstanceId(),null);
                if("post".equals(sysIdentity.getType())) {
                    uompOrderRecord.setHandlerType("post");
                    uompOrderRecord.setHandlerGroup(this.getSysIdentityName(sysIdentityList));
                    uompOrderRecord.setHandlerGroupId(sysIdentity.getId());
                } else {
                    uompOrderRecord.setHandlerType("user");
                    uompOrderRecord.setHandler(this.getSysIdentityName(sysIdentityList));
                    uompOrderRecord.setHandlerId(sysIdentity.getId());
                    uompOrderRecord.setHandlerOrgId(sysIdentity.getOrgId());
                    uompOrderRecord.setHandlerGroupId(sysIdentity.getOrgId());//工单类特殊 ORGID 即业务组ID
                }
                uompOrderRecord.setTaskName(taskActionCmd.getBpmTask().getName());
                this.create(uompOrderRecord);
            }

        } else if (eventType == EventType.TASK_PRE_COMPLETE_EVENT.getKey()){// 任务完成
            TaskActionCmd taskActionCmd = (TaskActionCmd) actionCmd;
            if(StringUtils.equals(actionCmd.getOpinion(), "开始节点跳过")) {
                this.deleteByTaskId(actionCmd.getTaskId());
                //更新启动节点任务名称
                UompOrderRecord record = new UompOrderRecord();
                record.setTaskName(taskActionCmd.getBpmTask().getName());
                record.setInstId(actionCmd.getInstanceId());
                this.updateOrderRecordStart(record);
                return;
            }
            UompOrderRecord uompOrderRecord2 = this.getByTaskId(actionCmd.getTaskId());
            if(uompOrderRecord2 == null ) {
                uompOrderRecord2 = this.formatObj(actionCmd);
                uompOrderRecord2.setEndTime(null);
                this.create(uompOrderRecord2);
            } else {
                uompOrderRecord2.setEndTime(new Date());
                uompOrderRecord2.setHandleCode(actionCmd.getActionName());
                uompOrderRecord2.setHandleName(actionCmd.getDoActionName()==null?uompOrderRecord2.getHandleName():actionCmd.getDoActionName());
                String modifyRecord = this.getModifyRecord((DefualtTaskActionCmd) taskActionCmd,uompOrderRecord2.getHandleOpin());
                if(modifyRecord.contains("投票不通过")) {
                    uompOrderRecord2.setHandleName("不通过");
                }
                uompOrderRecord2.setHandleOpin(modifyRecord.replaceAll("投票","审核").replaceAll("会签","审核").replaceAll("票选","审核").replaceAll("审核反对","审核不通过"));
                if(actionCmd.getActionName().contains("sign")) {
                    //会签用户名处理
                    String handerName = uompOrderRecordDao.getSignHandlerName(uompOrderRecord2.getTaskId());
                    uompOrderRecord2.setHandler(StringUtil.isEmpty(handerName)?ContextUtil.getCurrentUserName():handerName);
                } else {
                    uompOrderRecord2.setHandler(ContextUtil.getCurrentUserName());
                }
                uompOrderRecord2.setHandlerId(ContextUtil.getCurrentUserId());
                uompOrderRecord2.setUpdateBy(ContextUtil.getCurrentUserId());
                uompOrderRecord2.setUpdateTime(new Date());
                //更新处理人
                List<SysIdentity> sysIdentityList = actionCmd.getBpmIdentity(actionCmd.getNodeId());
                if(sysIdentityList != null && sysIdentityList.size()>0 && uompOrderRecord2.getHandleName() !=null) {
                    SysIdentity sysIdentity = sysIdentityList.get(0);
                    uompOrderRecord2.setHandleName(uompOrderRecord2.getHandleName() + "至" + this.getSysIdentityName(sysIdentityList));
                }
                //计算用时
                //uompOrderRecord2.setDuration(TimeUtils.calcTimeLimitByMinute(uompOrderRecord2.getStartTime(),uompOrderRecord2.getEndTime()));
                this.update(uompOrderRecord2);
            }
        } else if(eventType == EventType.MANUAL_END.getKey()) {
            // 流程终止
            UompOrderRecord uompOrderRecord2 = this.getByTaskId(actionCmd.getTaskId());
            uompOrderRecord2.setEndTime(new Date());
            uompOrderRecord2.setHandleCode(actionCmd.getActionName());
            uompOrderRecord2.setHandleName(actionCmd.getDoActionName()==null?uompOrderRecord2.getHandleName():actionCmd.getDoActionName());
            uompOrderRecord2.setHandleOpin(actionCmd.getOpinion());
            uompOrderRecord2.setHandler(ContextUtil.getCurrentUserName());
            uompOrderRecord2.setHandlerId(ContextUtil.getCurrentUserId());
            uompOrderRecord2.setUpdateBy(ContextUtil.getCurrentUserId());
            uompOrderRecord2.setUpdateTime(new Date());
            this.update(uompOrderRecord2);
        } else if (eventType == EventType.PRE_SAVE_BUS_EVENT.getKey()) {
            //保存记录日志
            if(ActionType.SAVE.getKey().equals(actionCmd.getActionName())) {
                TaskActionCmd taskActionCmd = (TaskActionCmd) BpmContext.getActionModel();
                UompOrderRecord uompOrderRecord2 = this.getByTaskId(actionCmd.getTaskId());
                String modifyRecord = this.getModifyRecord((DefualtTaskActionCmd) taskActionCmd,uompOrderRecord2.getHandleOpin());
                if(StringUtil.isNotEmpty(modifyRecord)) {
                    uompOrderRecord2.setHandleOpin(modifyRecord);
                    this.updateOrderRecordByTaskId(uompOrderRecord2);
                }
            }
        }
        else {
            // 流程启动
            UompOrderRecord uompOrderRecord = this.formatObj(actionCmd);
            Map<String, List<SysIdentity>> identitiesMap = actionCmd.getBpmIdentities();
            if(identitiesMap !=null && identitiesMap.size()>0) {
                String keys = (String) identitiesMap.keySet().toArray()[0];
                List<SysIdentity> sysIdentities =  identitiesMap.get(keys);
                //更新处理人
                if(sysIdentities != null && sysIdentities.size()>0 ) {
                    SysIdentity sysIdentity = sysIdentities.get(0);
                    uompOrderRecord.setHandleName(uompOrderRecord.getHandleName() + "至" + this.getSysIdentityName(sysIdentities));
                }
            }
            uompOrderRecord.setTaskName("启动");
            this.create(uompOrderRecord);
        }

    }

    public String getSysIdentityName(List<SysIdentity> sysIdentityList){
        StringBuilder sbr = new StringBuilder();
        sysIdentityList.forEach(it->{
            sbr.append(it.getName());
            sbr.append(",");
        });
        if(StringUtil.isNotEmpty(sbr.toString())) {
            return sbr.toString().substring(0,sbr.length()-1);
        } else {
            return sbr.toString();
        }
    }
    /**
     * <AUTHOR>
     * @Description //给操作日志设置参数
     * @Date 20:50 2023/08/31
     **/
    public void setLogParam(BaseActionCmd cmd) {
        if(ActionType.START.getKey().equals(cmd.getActionName())) {
            LogOperateUtil.setValue("action", "提交");
        } else {
            LogOperateUtil.setValue("action", cmd.getDoActionName());
        }
        try {
            BpmInstance bpmInstance = (BpmInstance) cmd.getBpmInstance();
            if(bpmInstance == null) {
                bpmInstance = bpmInstanceManager.get(cmd.getInstanceId());
            }
            BpmDefinition bpm = bpmDefinitionManager.get(bpmInstance.getDefId());
            LogOperateUtil.setValue("function", bpm.getDesc()); //功能描述 复用工作流描述
            LogOperateUtil.setValue("title", bpmInstance.getDefName() + "：" + bpmInstance.getSubject());

        }catch (Exception e) {
            log.error("操作日志设置参数报错：{}",e.getMessage());
        }
    }
    /**
     * <AUTHOR>
     * @Description //对流程返回结果格式化
     * @Date 9:47 2023/09/01
     * @return java.lang.String
     **/
    public String getDoActionResult(FlowRequestParam flowParam,String result) {
        if(result!=null && !result.contains("执行操作成功")) {
            return result;
        }
        StringBuilder resultSbr = new StringBuilder();
        resultSbr.append("执行操作成功");
        try{
            //转派到组 修改提示返回信息
            flowParam.getDestination();
            JSONObject nodeUsers = flowParam.getNodeUsers();
            if(nodeUsers != null) {
                if (nodeUsers.getJSONArray(flowParam.getDestination()) != null) {
                    JSONArray array = nodeUsers.getJSONArray(flowParam.getDestination());
                    StringBuilder sbr = new StringBuilder();
                    for(int i=0;i<array.size();i++) {
                        JSONObject user = (JSONObject) array.get(i);
                        if(StringUtil.isEmpty(user.getString("name"))) {
                            continue;
                        }
                        sbr.append(user.getString("name"));
                        sbr.append(",");
                    }
                    if(StringUtil.isNotEmpty(sbr.toString())) {
                        resultSbr.append("，已发送给" + sbr.toString().substring(0,sbr.length()-1));
                    }
                }
            }
        }catch (Exception e) {
            log.error("doAction返回值个性化处理失败：{}",e.getMessage());
        }
        return resultSbr.toString();
    }
}
