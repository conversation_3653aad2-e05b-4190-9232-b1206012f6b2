package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.org.api.service.UserService;
import cn.gwssi.uomp.constants.CommonConstants;
import cn.gwssi.uomp.dao.UompAlarmMapper;
import cn.gwssi.uomp.dao.UompAlarmMonitoringMapper;
import cn.gwssi.uomp.dao.UompResourceMapper;
import cn.gwssi.uomp.dao.UompWorkOrderMapper;
import cn.gwssi.uomp.entity.UompAlarm;
import cn.gwssi.uomp.entity.UompWorkOrder;
import cn.gwssi.uomp.enums.UompAlarmEnum;
import cn.gwssi.uomp.service.UompAlarmService;
import cn.gwssi.uomp.service.UompWorkOrderService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class UompAlarmServiceImpl implements UompAlarmService {

    @Autowired
    private UompResourceMapper uompResourceMapper;
    @Autowired
    private UompAlarmMapper uompAlarmMapper;
    @Autowired
    private UompAlarmMonitoringMapper uompAlarmMonitoringMapper;
    @Autowired
    private UompWorkOrderService uompWorkOrderService;
    @Autowired
    private UompWorkOrderMapper uompWorkOrderMapper;
    @Autowired
    private UserService userService;

    @Override
    public void acceptAlarmMessage(Map<String, Object> paramMap) throws Exception {
        ArrayList<UompAlarm> uompAlarmList = new ArrayList<>();

        ArrayList<UompAlarm> uompAlarmUpdateList = new ArrayList<>();

        //查看自动转工单配置信息表
        List<Map<String, String>> autoConfigList = uompAlarmMonitoringMapper.getAutoConfigList();
        //查看规则管理表中 中英文名称对应关系
        List<Map<String, String>> ruleList = uompAlarmMonitoringMapper.getRuleList();
        HashMap<String, String> ruleMap = new HashMap<>();
        for (Map<String, String> map : ruleList) {
            ruleMap.put(map.get("code"), map.get("name"));
        }

        //查看规则库中启动状态的规则名称
        List<String> ruleEnNameList = uompAlarmMonitoringMapper.getRuleEnNameList();

        //获取告警列表信息
        List<Map<String, Object>> alerts = (List<Map<String, Object>>) paramMap.get("alerts");

        for (Map<String, Object> alert : alerts) {
            //判断"status",firing 是新增的告警信息，resolved的是恢复的，根据fingerprint去判断修改恢复时间endsAt
            String status = alert.get("status").toString();
            if ("firing".equals(status)) {
                Map<String, Object> labels = (Map<String, Object>) alert.get("labels");
                Map<String, Object> annotations = (Map<String, Object>) alert.get("annotations");
                //告警级别
                String severity = labels.get("severity").toString();
                //告警类型
                String alertname = labels.get("alertname").toString();
                //如果告警类型在规则库中没有，且不是启用状态的，就不添加到告警规则表中
                if (!ruleEnNameList.contains(alertname)){
                    continue;
                }
                //告警主题
                String summary = annotations.get("summary").toString();
                //告警主机ip
                String instance = labels.get("instance").toString();
                //存在域名的情况，如果包含http，就只比对ip
                String ip = "";
                String port = "";
                if (instance.contains("http")) {
                    ip = instance;
                } else {
                    //比对ip + 端口
                    List<String> ipList = Arrays.asList(instance.split(":"));
                    if (ipList.size() > 1) {
                        ip = ipList.get(0);
                        port = ipList.get(1);
                    } else {
                        ip = ipList.get(0);
                    }
                }

                //告警消息id
                String fingerprint = alert.get("fingerprint").toString();
                //告警时间
                String startsAt = alert.get("startsAt").toString();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.CHINA);
//                sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
                Date startTime = sdf.parse(startsAt);

                //2023-8-2 通过告警监控管理表UOMP_ALARM_MONITORING 去比对IP和端口。只有该表中存在且监控中的告警信息才收集
                //存在交换机的情况。交换机类别的没有端口。port = ''(也存域名的情况，域名情况的也没有port)
                String resourceId = uompAlarmMonitoringMapper.selectResourceIdByIp(ip, port);
                //如果resourceId不为空，则说明该ip在监控中
                if (StringUtils.isNotBlank(resourceId)) {
                    //通过resourceId查出CMDB详情
                    Map<String, String> resourceInfo = uompResourceMapper.selectInfoById(resourceId);
                    //如果查不到(审核通过)资源数据，证明该ip没涵盖到系统中，过滤掉该条告警数据
                    if (resourceInfo != null) {
                        //根据path中截取第二个id为一级分层id，查出相应名称
                        String pathStr = resourceInfo.get("pathStr");
                        List<String> pathList = Arrays.asList(pathStr.split("\\."));
                        String groupId = pathList.get(1);
                        String groupName = uompResourceMapper.selectTreeNameById(groupId);
                        String resourceNo = resourceInfo.get("resourceNo");
                        String resourceName = resourceInfo.get("resourceName");
                        String deviceModel = resourceInfo.get("deviceModel");

                        //封装告警数据(初始化)
                        String id = IdUtil.getSuid();
                        UompAlarm uompAlarm = UompAlarm.builder()
                                .id(id)
                                .alarmId(fingerprint)
                                .ipInstance(instance)
                                .groupId(groupId)
                                .groupName(groupName)
                                .resourceId(resourceId)
                                .resourceName(resourceName)
                                .resourceNo(resourceNo)
                                .deviceModel(deviceModel)
                                .alarmType(alertname)
                                .alarmLevel(severity)
                                .alarmContent(summary)
                                .alarmStatus(CommonConstants.NO_HANDLE)
                                .alarmTime(startTime)
                                .delFlag("0")
                                .build();

                        //针对告警信息进行相关验证处理
                        //1.判断uomp_alarm 表中是否存在 fingerprint(alarm_id) 的数据。不存在判断一下自动转工单
                        //2.存在的情况，判断是否是 待处置(0) 状态，
                        //3.如果不是 待处置 状态 就根据状态将该alarm_id之前的数据相关字段赋值到该新的告警信息中
                        //4.如果是待处置状态的，就判断自动转工单表是否有相关规则
                        //5.如果是匹配上自动转工单，则调用转工单接口后封装一些数据
                        //6.如果未匹配上自动转工单，则直接插入即可
                        UompAlarm uompAlarmInfo = uompAlarmMapper.selectInfoByAlarmId(fingerprint);
                        if (uompAlarmInfo == null) {
                            //判断是否自动转工单
                            checkIsOrder(uompAlarm, autoConfigList, true, ruleMap);
                        } else {
                            //之前存在告警信息，判断处置状态,根据状态进行处理
                            if (!uompAlarmInfo.getAlarmStatus().equals(CommonConstants.NO_HANDLE)) {
                                copyAlarmData(uompAlarmInfo, uompAlarm);
                            } else {
                                checkIsOrder(uompAlarm, autoConfigList, false, ruleMap);
                            }
                        }

                        uompAlarmList.add(uompAlarm);
                    }
                }
            }
            //告警已经恢复的数据
            if ("resolved".equals(status)) {
                //告警id
                String fingerprint = alert.get("fingerprint").toString();
                //告警时间
                String endsAt = alert.get("endsAt").toString();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.CHINA);
//                sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
                Date endTime = sdf.parse(endsAt);
                UompAlarm uompAlarm = UompAlarm.builder()
                        .alarmId(fingerprint)
                        .handleTime(endTime)
                        .build();
                uompAlarmUpdateList.add(uompAlarm);
            }
        }

        //插入告警信息
        if (CollectionUtils.isNotEmpty(uompAlarmList)) {
            uompAlarmMapper.insertBatch(uompAlarmList);
        }

        //恢复告警信息时间
        if (CollectionUtils.isNotEmpty(uompAlarmUpdateList)) {
            for (UompAlarm uompAlarm : uompAlarmUpdateList) {
                uompAlarmMapper.updateHandleTimeByAlarmId(uompAlarm);
            }
        }
    }

    /**
     * @param alarm
     * @return java.util.List<cn.gwssi.uomp.entity.UompAlarm>
     * <AUTHOR>
     * @Description //获取未处理的 X级别的告警数据
     * @Date 15:43 2023/7/19
     **/
    public List<UompAlarm> getUnCheckList(UompAlarm alarm) {
        return uompAlarmMapper.getUnCheckList(alarm);
    }

    @Override
    public void handleSilence() {
        //获取api调用工具
        MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
        //获取当前周几
        LocalDateTime endDate = LocalDateTime.now();
        String week = endDate.getDayOfWeek().getValue() + "";

        //查看所有启动状态，且是周期性的静默规则数据
        List<Map<String, String>> silenceList = uompAlarmMapper.getSilence();
        for (Map<String, String> map : silenceList) {
            String silentWeek = map.get("silentWeek");
            String id = map.get("id");

            //判断当前规则中是否还有今天是周几，如果符合周几则调用prometheus接口
            if (silentWeek.contains(week)) {
                Map<String, Object> param = new HashMap<>();
                param.put("id", id);
                apiService.execute("POST", "/monitor/alarmMonitoring/makeSilentJsonById", param);
            }
        }

    }


    /**
     * 判断是否自动转工单
     *
     * @param uompAlarm      告警数据
     * @param autoConfigList 自动转工单配置信息
     * @param isFirst        用于判断该批次alarmId之前是否已经有告警消息了，有的话此时如果转工单了则将之前该批次的状态一同修改
     */
    private void checkIsOrder(UompAlarm uompAlarm, List<Map<String, String>> autoConfigList, boolean isFirst, Map<String, String> ruleMap) {
        if (CollectionUtils.isNotEmpty(autoConfigList)) {
            for (Map<String, String> map : autoConfigList) {
                String alarmLevel = map.get("alarmLevel");
                //判断告警等级是否符合，符合的自动转工单
                if (alarmLevel.equals(uompAlarm.getAlarmLevel())) {
                    JSONObject jsonObject = makeJson(map, uompAlarm, ruleMap);

                    //调用自动转工单api,事件工单 order_type = 1
                    ResultMsg resultMsg = uompWorkOrderService.createWorkOrder(jsonObject, "1");
                    //如果流程启动失败则按照未处置插入
                    if ("200".equals(resultMsg.getCode())) {
                        //返回流程实例id，查出对应的工单id，标题，编号等数据，封装回uompAlarm,并将状态改为处置中(1)
                        String instId = String.valueOf(resultMsg.getData());
                        //根据实例id查出工单信息
                        UompWorkOrder workOrder = uompWorkOrderMapper.getWorkOrderByInstid(instId);

                        uompAlarm.setAlarmStatus(CommonConstants.IN_HANDLE);
                        uompAlarm.setInstId(instId);
                        uompAlarm.setOrderId(workOrder.getId());
                        uompAlarm.setOrderNo(workOrder.getOrderNo());
                        uompAlarm.setOrderTitle(workOrder.getOrderTitle());
                        //method 1 转工单
                        uompAlarm.setDisposeMethod("1");
                        uompAlarm.setDisposePerson("系统自动生成");

                        //判断该告警id是不是第一次传递，是的话就不用修改之前数据的状态
                        if (!isFirst) {
                            uompAlarmMapper.updateOrderByAlarmId(uompAlarm);
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 拷贝字段数据
     *
     * @param source
     * @param target
     */
    private void copyAlarmData(UompAlarm source, UompAlarm target) {
        target.setAlarmStatus(source.getAlarmStatus());
        target.setDisposeMethod(source.getDisposeMethod());
        target.setDisposePerson(source.getDisposePerson());
        target.setDisposeReason(source.getDisposeReason());
        target.setDisposeRemark(source.getDisposeRemark());
        target.setDisposeTime(source.getDisposeTime());
        target.setOrderId(source.getOrderId());
        target.setOrderNo(source.getOrderNo());
        target.setOrderTitle(source.getOrderTitle());
        target.setInstId(source.getInstId());
    }


    /**
     * 组装自动工单接口所需json数据
     *
     * @param map
     * @param uompAlarm
     * @param ruleMap
     * @return
     */
    private JSONObject makeJson(Map<String, String> map, UompAlarm uompAlarm, Map<String, String> ruleMap) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = sdf.format(uompAlarm.getAlarmTime());
        String alarmLevel = UompAlarmEnum.AlertLevelEnum.getNameByCode(uompAlarm.getAlarmLevel());

        String alarmType = ruleMap.get(uompAlarm.getAlarmType());
        if (StringUtils.isBlank(alarmType)) {
            alarmType = uompAlarm.getAlarmType();
        }

        HashMap<String, Object> jsonMap = new HashMap<>();

        jsonMap.put("ORDER_TYPE", "1");
        //告警时间+告警主机+告警ip+告警类型+告警级别
        String title = time + uompAlarm.getResourceName() + uompAlarm.getIpInstance() + alarmType + alarmLevel;
        jsonMap.put("EVENT_TITLE", title);
        jsonMap.put("EVENT_DESC", uompAlarm.getAlarmContent());
        jsonMap.put("APPLICANT", "系统自动生成");

        //电话取业务组组长的电话(多个组长得话取一个有电话的即可，没有组长或者都没有电话则置为“-”)
        String groupId = map.get("bizGroupId");
        String mobile = "-";
        String groupLeaderIds = uompAlarmMonitoringMapper.getGroupLeaderIds(groupId);
        if (StringUtils.isNotBlank(groupLeaderIds)) {
            List<String> userIdList = Arrays.asList(groupLeaderIds.split(","));
            for (String userId : userIdList) {
                IUser user = userService.getUserById(userId);
                if (StringUtils.isNotBlank(user.getMobile())) {
                    mobile = user.getMobile();
                    break;
                }
            }
        }

        jsonMap.put("APPLICANT_TEL", mobile);
        jsonMap.put("SLA_ID", map.get("eventTypeId"));
        jsonMap.put("SLA_NAME", map.get("eventTypeName"));
        jsonMap.put("EVENT_LEVEL", map.get("eventLevelId"));
        jsonMap.put("SLA_LEVEL", map.get("eventLevelName"));
        jsonMap.put("EVENT_TYPE", map.get("eventTypeCode"));
        jsonMap.put("IF_FEEDBACK", "0");
        jsonMap.put("RESOURCE_ID", uompAlarm.getResourceId());
        //组装转派组数据
        HashMap<String, String> groupMap = new HashMap<>();
        groupMap.put("id", groupId);
        groupMap.put("name", map.get("bizGroupName"));
        groupMap.put("orgId", groupId);
        groupMap.put("type", "post");
        groupMap.put("team_id", groupId);

        jsonMap.put("nodeUsers", groupMap);

        return JSON.parseObject(JSON.toJSONString(jsonMap));
    }
}
