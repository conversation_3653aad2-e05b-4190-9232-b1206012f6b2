package cn.gwssi.uomp.service;


import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.BaseActionCmd;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.uomp.entity.UompCycleOrderConfig;
import cn.gwssi.uomp.entity.UompWorkOrder;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

/**
*
*/
public interface UompWorkOrderService {

    void updateByInstid(UompWorkOrder workOrder);

    UompWorkOrder getWorkOrderByInstid(String instid);

    List<UompWorkOrder> getUnOverOrderList(UompWorkOrder workOrder);

    ResultMsg createWorkOrder(JSONObject obj, String order_type);

    ResultMsg createWorkOrderPatent(JSONObject obj, String order_type);

    void btnScript(BaseActionCmd cmd,String type);

    String selectSla(String sla_id, String sla_level);

    ResultMsg manualEndFlow(JSONObject object);

    String getInstId(String detailId);

    String getDetailUrl(NotifyMessage notifyMessage);

    List<UompCycleOrderConfig> getCycleOrderConfigList(String param);

    void insertCycleRecord(Map<String, String> record);

    int findOrderByTimeAndConfigId(String id, String datestr,String handler_id);
}
