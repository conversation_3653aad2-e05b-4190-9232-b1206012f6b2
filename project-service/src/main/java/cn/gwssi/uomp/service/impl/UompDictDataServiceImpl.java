package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudbpm.form.manager.FormCustDialogManager;
import cn.gwssi.ecloudbpm.form.model.FormCustDialog;
import cn.gwssi.ecloudbpm.form.model.custdialog.FormCustDialogReturnField;
import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.uomp.dao.UompDictDataMapper;
import cn.gwssi.uomp.enums.FromCustDialogKey;
import cn.gwssi.uomp.service.UompDictDataService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UompDictDataServiceImpl implements UompDictDataService {

    @Autowired
    UompDictDataMapper uompDictDataMapper;
    @Autowired
    FormCustDialogManager formCustDialogManager;

    public String getNameByDictKey(String dict, String key) {
        return uompDictDataMapper.getNameByDictKey(dict,key);
    }
    public String getKeyByDictName(String dict, String name) {
        if(StringUtil.isNotEmpty(name)) {
            String[] names  = name.split(",");
            List<String> nameList = Arrays.asList(names);
            return uompDictDataMapper.getKeyByDictName(dict,nameList);
        } else {
            return "";
        }
    }
    /**
     * <AUTHOR>
     * @Description //自定义对话框数据拼接处理
     * @Date 14:51 2023/09/13
     * @param ctrl_type
     * @param ctrl_config
     * @return java.util.Map<java.lang.String,java.lang.String>
     **/
    public Map<String, String> getCustDialogData(String ctrl_type, String ctrl_config, String col_val){
        Map<String, String> data = new HashMap<>();
        List<JSONObject> dataList = new ArrayList<>();
        //系统默认的自定义对话框 人员多选单选 机构多选单选等
        if(!"cust-dialog".equals(ctrl_type)) {
            ctrl_config = FromCustDialogKey.getCodeByKey(ctrl_type);
        }
        //先查询出自定对话框信息
        FormCustDialog formCustDialog = formCustDialogManager.getByKey(ctrl_config);
        if(formCustDialog==null) {
            return data;
        }
        //根据返回配置 查询到name对于的实际字段名称
        List<FormCustDialogReturnField> list2 = formCustDialog.getReturnFields();
        //拼接查询条件 获取key
        String filterName = "";
        String filterId = "";
        for (FormCustDialogReturnField field:list2) {
            if("name".equals(field.getReturnName())){
                filterName = field.getColumnName();
            }
            if("id".equals(field.getReturnName())){
                filterId = field.getColumnName();
            }
        }
        String idstr = "";
        String namestr = "";
        if(StringUtil.isNotEmpty(col_val)) {
            String[] names  = col_val.split(",");
            List<String> nameList = Arrays.asList(names);
            Map<String,Object> param = new HashMap<>();
            param.put("pageNo",1);
            param.put("pagSize",10);
            param.put("sort","");
            param.put("order","");
            if("interface".equals(formCustDialog.getDataSource())) {
                String apiInfo = ((String) formCustDialog.getOtherConfig().get("apiInfo"));
                String url = apiInfo.substring(0,apiInfo.indexOf("("));
                MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
                for (String name:nameList) {
                    param.put(filterName,name);
                    List<Map> list = new ArrayList<>();
                    PageResult result = apiService.execute("POST", url, param);
                    if(result.getRows() != null) {
                        list = result.getRows();
                    }
                    if(list!=null && list.size()>0) {
                        Map obj = list.get(0);
                        idstr = idstr + obj.get(filterId) + ",";
                        namestr = namestr + obj.get(filterName) + ",";
                        Map newObj = new HashMap();
                        //拼接json 接口返回的多余字段不要
                        for (FormCustDialogReturnField field:list2) {
                            newObj.put(field.getReturnName(),obj.get(field.getColumnName()));
                        }
                        dataList.add(JSONObject.parseObject(JSON.toJSONString(newObj)));
                    }
                }
            } else {
                //表或者视图 则拼接SQL 直接查询
                param.put("table_name",formCustDialog.getObjName());
                param.put("id",filterId);
                param.put("name",filterName);
                param.put("col_val",col_val);
                List<Map<String,String>> mapList = uompDictDataMapper.getFormCustDialogDataBySQL(param);
                mapList = mapList.stream().distinct().collect(Collectors.toList());
                if(mapList!=null && mapList.size()>0) {
                    for(Map<String,String> it : mapList) {
                        idstr = idstr + it.get("id") + ",";
                        namestr = namestr + it.get("name") + ",";
                        JSONObject obj = JSONObject.parseObject(JSON.toJSONString(it));
                        dataList.add(obj);
                    }
                }
            }
        }
        data.put("id",idstr==""?"":idstr.substring(0,idstr.length()-1));
        data.put("name",namestr==""?"":namestr.substring(0,namestr.length()-1));
        if("cust-dialog".equals(ctrl_type)) {
            data.put("json",JSONArray.toJSONString(dataList));
        } else {
            data.put("json",data.get("id") + "|" + data.get("name"));
        }
        //循环调用接口 查询数据 拼接ID、NAME、JSON
        return data;

    }
}
