package cn.gwssi.uomp.service;

import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.BaseActionCmd;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.FlowRequestParam;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefualtTaskActionCmd;
import cn.gwssi.ecloudframework.base.manager.Manager;
import cn.gwssi.ecloudframework.sys.api.model.SysIdentity;
import cn.gwssi.uomp.entity.UompOrderRecord;

import java.util.Map;

public interface UompOrderRecordManager extends Manager<String, UompOrderRecord> {
    /**
     * <AUTHOR>
     * @Description //根据任务ID更新记录表
     * @Date 15:12 2023/5/12
     **/
    void updateOrderRecordByTaskId(UompOrderRecord uompOrderRecord);

    UompOrderRecord getByTaskId(String taskId);

    void insertOrderRecordByActionCmd(BaseActionCmd actionCmd,int limit);

    UompOrderRecord formatObj(BaseActionCmd actionCmd);

    void updateWorkOrder(SysIdentity sysIdentity, String instanceId,String name);

    void deleteByTaskId(String taskId);

    void updateOrderRecordStart(UompOrderRecord record);

    Map<String, String> getEventDataByInstId(String id);

    String getModifyRecord(DefualtTaskActionCmd taskModel,String hisOpinion);

    void insertOrderRecordByScript(BaseActionCmd actionCmd,String type);

    void setLogParam(BaseActionCmd actionCmd);

    String getDoActionResult(FlowRequestParam param,String result);
}
