package cn.gwssi.uomp.service;



import cn.gwssi.uomp.entity.CmdbCommResource;

import java.util.List;
import java.util.Map;

/**
*
*/
public interface UompResourceService {

    void insertResource(Map<String,Object> param);

    List<Map<String, String>> getListById(Map<String,Object> param);

    List<String> getAllUserRights(String userId);

    Map<String, String> getModelData(String baseline_id);

    List<Map<String, Object>> getColConfigList(String baseline_id);

    int insertResourceByList(List<Map<String, Object>> paramList);

    List<String> getResourceNolist();

    List<CmdbCommResource> getUnCheckResourceList(CmdbCommResource resource);

    void saveSysAuthorization(List<Map<String, Object>> paramList);

    List<Map<String, Object>> getAllResourceList();

    int judgeIfHasAuthUpdate(String model_id,String res_no);

    boolean validateValue(String ctrl_valid_rule, String col_val);

    String getValidateMsg(String ctrl_valid_rule);
}
