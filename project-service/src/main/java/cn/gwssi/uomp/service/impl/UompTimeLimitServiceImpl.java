package cn.gwssi.uomp.service.impl;

import cn.gwssi.uomp.dao.UompTimelimitPauseMapper;
import cn.gwssi.uomp.entity.UompTimelimitPause;
import cn.gwssi.uomp.service.UompTimeLimitPauseService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
@Service
public class UompTimeLimitServiceImpl implements UompTimeLimitPauseService {

    @Resource
    UompTimelimitPauseMapper uompTimelimitPauseMapper;

    @Override
    public void create(UompTimelimitPause record) {
        uompTimelimitPauseMapper.create(record);
    }

    @Override
    public void udpate(UompTimelimitPause record) {
        uompTimelimitPauseMapper.updateByPrimaryKey(record);
    }

    public void updateLimitPauseByInstid(UompTimelimitPause record) {
        uompTimelimitPauseMapper.updateLimitPauseByInstid(record);
    }

    public UompTimelimitPause getByInstid(String instanceId) {
        return uompTimelimitPauseMapper.getByLimitPauseInstid(instanceId);
    }

    public int getPauseSum (String instanceId) {
        return uompTimelimitPauseMapper.getPauseSum(instanceId);
    }
}
