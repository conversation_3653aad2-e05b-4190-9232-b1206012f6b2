package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudframework.base.core.id.IdUtil;
import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.module.orgCustom.api.service.UserCustomService;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.constants.CommonConstants;
import cn.gwssi.uomp.dao.UompInspectionJobMapper;
import cn.gwssi.uomp.entity.UompInspectExecTime;
import cn.gwssi.uomp.entity.UompInspectionJob;
import cn.gwssi.uomp.entity.UompInspectionPlan;
import cn.gwssi.uomp.service.UompInspectionJobService;
import cn.gwssi.uomp.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Service
@Slf4j
public class UompInspectionJobServiceImpl  implements UompInspectionJobService{

    @Resource
    UompInspectionJobMapper jobMapper;
    @Resource
    private UserCustomService userService;

    public void updateByPrimaryKeySelective(UompInspectionJob job) {
        jobMapper.updateByPrimaryKeySelective(job);
    }
    public UompInspectionJob selectByPrimaryKey(String id) {
        return jobMapper.selectByPrimaryKey(id);
    }

    /**
     * <AUTHOR>
     * @Description //根据巡检计划 生成每日巡检任务以及XXL定时任务
     * @Date 17:27 2023/10/09
     * @param plan
     **/
    public void createJobByPlan(UompInspectionPlan plan){
        if(plan.getExecTimeList()!=null && !plan.getExecTimeList().isEmpty()) {
            //根据模板ID查询一下处置方式
            String disposal_method = jobMapper.getDisposalMethodById(plan.getTemplateId());
            if(disposal_method==null) {
                disposal_method = "";
            }
            ContextUtil.setCurrentUser(userService.getUserById(plan.getCreateBy()));
            UompInspectionJob job = new UompInspectionJob();
            job.setInspType(plan.getInspType());
            job.setJobName(plan.getPlanName());
            job.setInspState(plan.getExecType());
            job.setPlanId(plan.getId());
            for(UompInspectExecTime execTime:plan.getExecTimeList()) {
                Calendar calendar = Calendar.getInstance();
                int newDay = calendar.get(Calendar.DAY_OF_MONTH); //当前天
                //如果非今天执行 则不执行
                if(execTime.getExecDay()!=0 && newDay != execTime.getExecDay()) {
                    continue;
                }
                //如果定时任务执行时 时间已经超过计划预计时间 则不生成任务
                if(!DateUtils.checkIfOverNowTime(execTime.getExecHour(),execTime.getExecMinute())) {
                    continue;
                }
                //计划执行时间
                String execHour = execTime.getExecHour();
                if(StringUtil.isNotEmpty(execTime.getExecMinute())) {
                    execHour = execHour + ":" + execTime.getExecMinute() + ":00";
                } else {
                    execHour = execHour + ":00:00";
                }
                String datestr = DateUtils.getCurrentDateStr("yyyy-MM-dd") + " " + execHour;
                try {
                    //手动巡检 且 计划设置了跳过节假日的情况下
                    if("手动巡检".equals(plan.getExecType()) && CommonConstants.YES.equals(plan.getIfSkipHoliday())){
                        datestr = DateUtils.getFirstWorkDay(null,"yyyy-MM-dd") + " " + execHour;
                    }
                    job.setInspPlanTime(DateUtils.getFormatDate(datestr,"yyyy-MM-dd HH:mm:ss"));
                } catch (ParseException e) {
                    log.error("根据计划创建时时间格式化失败，错误原因：{}" ,e);
                    continue;
                }
                //先根据时间和计划ID查询一下 是否已存在，已存在则忽略 不存在则新建任务
                String cron = DateUtils.createCron(execTime.getExecHour(),execTime.getExecMinute());
                int count = jobMapper.findJobByCronAndPlanId(plan.getId(),datestr);
                if(count == 0) {
                    //调用API里的函数，生成XXL定时任务 获取任务ID
                    String job_id = IdUtil.getSuid();
                    String xxljobid = "";
                    //手动巡检无须创建定时任务
                    if(!"手动巡检".equals(plan.getExecType())) {
                        MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
                        Map<String, Object> interParam = new HashMap<>();
                        interParam.put("job_name", plan.getPlanName());
                        interParam.put("job_cron", cron);
                        interParam.put("email", ContextUtil.getCurrentUser().getEmail());
                        interParam.put("exec_param",job_id);
                        interParam.put("job_state",1);//默认启动
                        xxljobid = apiService.invoke("/xxl/addJob",interParam);
                    }
                    job.setJobCron(cron);
                    job.setJobId(xxljobid);
                    job.setTemplateId(plan.getTemplateId());
                    job.setDelFlag("0");
                    job.setCompleteState("1"); //未完成
                    job.setId(job_id);
                    job.setOrderFlag(disposal_method.contains("0")?"1":"0"); //是否需要转工单
                    job.setAlarmFlag(disposal_method.contains("1")?"1":"0"); //是否需要生成告警
                    job.setReceiverId(plan.getReceiverId());
                    jobMapper.create(job);
                }
            }
        }

    }
}




