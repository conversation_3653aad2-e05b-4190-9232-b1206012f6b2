package cn.gwssi.uomp.service.impl;

import cn.gwssi.ecloudbpm.wf.api.constant.ActionType;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.BaseActionCmd;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.FlowRequestParam;
import cn.gwssi.ecloudbpm.wf.api.engine.data.BpmFlowDataAccessor;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmInstanceManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmInstance;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefaultInstanceActionCmd;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefualtTaskActionCmd;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.module.orgCustom.api.service.UserCustomService;
import cn.gwssi.ecloudframework.sys.api.jms.model.msg.NotifyMessage;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import cn.gwssi.uomp.constants.CommonConstants;
import cn.gwssi.uomp.dao.UompCycleOrderConfigMapper;
import cn.gwssi.uomp.dao.UompWorkOrderMapper;
import cn.gwssi.uomp.entity.*;
import cn.gwssi.uomp.enums.WorkOrderFlowEnum;
import cn.gwssi.uomp.service.UompOrderRecordManager;
import cn.gwssi.uomp.service.UompTimeLimitPauseService;
import cn.gwssi.uomp.service.UompWorkOrderService;
import cn.gwssi.uomp.utils.TimeUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
*
*/
@Service
public class UompWorkOrderServiceImpl implements UompWorkOrderService{
    @Autowired
    UompWorkOrderMapper uompWorkOrderMapper;
    @Autowired
    BpmFlowDataAccessor bpmFlowDataAccessor;
    @Resource
    private BpmInstanceManager bpmInstanceManager;
    @Resource
    private UompOrderRecordManager uompOrderRecordManager;
    @Resource
    private UompTimeLimitPauseService uompTimeLimitPauseService;
    @Resource
    private UserCustomService userService;
    @Resource
    private UompCycleOrderConfigMapper uompCycleOrderConfigMapper;
    public void updateByInstid(UompWorkOrder workOrder) {
        uompWorkOrderMapper.updateByInstid(workOrder);
    }
    /**
     * <AUTHOR>
     * @Description //获取未完成的工单列表
     * @Date 9:50 2023/7/18
     * @return java.util.List<cn.gwssi.uomp.entity.UompWorkOrder>
     **/
    public List<UompWorkOrder> getUnOverOrderList(UompWorkOrder workOrder) {
        return uompWorkOrderMapper.getUnOverOrderList(workOrder);
    }

    public UompWorkOrder getWorkOrderByInstid(String instid) {
        return uompWorkOrderMapper.getWorkOrderByInstid(instid);
    }

    /**
     * <AUTHOR>
     * @Description //创建并启动工单
     * @Date 9:27 2023/08/03
     * @param
     * @return
     **/
    public ResultMsg createWorkOrder(JSONObject paramObj, String order_type){
        //设置用户
        String user_id = paramObj.getString("USER_ID");
        if(StringUtil.isEmpty(user_id)){
            user_id = SysPropertyUtil.getByAlias("UOMP_ORDER_CREATE_DEFAULT_USER_ID");
        }
        ContextUtil.setCurrentUser(this.userService.getUserById(user_id));
        if(StringUtil.isEmpty(order_type)){
            order_type = WorkOrderFlowEnum.EVENT.getOrderType();
        }
        FlowRequestParam flowParam = new FlowRequestParam();
        flowParam.setAction(ActionType.START.getKey());//按钮key
        flowParam.setActionName("转派");//按钮名称
        if(StringUtil.isNotEmpty(paramObj.getString("FLOW_DEF_ID"))) {
            flowParam.setDefId(paramObj.getString("FLOW_DEF_ID"));//流程定义ID
            flowParam.setDefKey(paramObj.getString("FLOW_DEF_KEY"));//流程定义key
        } else{
            flowParam.setDefId(WorkOrderFlowEnum.getFlowDefId(order_type));//流程定义ID
            flowParam.setDefKey(WorkOrderFlowEnum.getFlowDefKey(order_type));//流程定义key
        }
        flowParam.setStartOrgId(ContextUtil.getCurrentGroup().getGroupId());//启动流程时组织id
        //候选人处理

        JSONObject nodeUser = new JSONObject();
        JSONArray userArry = new JSONArray();
        userArry.add(paramObj.getJSONObject("nodeUsers"));
        if(WorkOrderFlowEnum.EVENT.getOrderType().equals(order_type) || WorkOrderFlowEnum.EVENT_PATENT.getOrderType().equals(order_type)) {
            nodeUser.put("UOMP_FLOW_EVENT-UserTask2",userArry);
        } else if (WorkOrderFlowEnum.ISSUES.getOrderType().equals(order_type) || WorkOrderFlowEnum.ISSUES_PATENT.getOrderType().equals(order_type)){
            nodeUser.put("UOMP_FLOW_ISSUES-UserTask2",userArry);
        } else {} //待处理

        flowParam.setNodeUsers(nodeUser);
        flowParam.setData(getData(paramObj,order_type));//表单数据
        return getResultMsg(flowParam,0);
    }

    /**
     * @Description //创建并启动工单-专利问题工单
     **/
    public ResultMsg createWorkOrderPatent(JSONObject paramObj, String order_type){
        //设置用户
        String user_id = paramObj.getString("USER_ID");
        if(StringUtil.isEmpty(user_id)){
            user_id = SysPropertyUtil.getByAlias("UOMP_ORDER_CREATE_DEFAULT_USER_ID");
        }
        ContextUtil.setCurrentUser(this.userService.getUserById(user_id));
        if(StringUtil.isEmpty(order_type)){
            order_type = WorkOrderFlowEnum.EVENT.getOrderType();
        }
        FlowRequestParam flowParam = new FlowRequestParam();
        flowParam.setAction(ActionType.START.getKey());//按钮key
        flowParam.setActionName("转派");//按钮名称
        flowParam.setDefId(WorkOrderFlowEnum.getFlowDefId(order_type));//流程定义ID
        flowParam.setDefKey(WorkOrderFlowEnum.getFlowDefKey(order_type));//流程定义key
        flowParam.setStartOrgId(ContextUtil.getCurrentGroup().getGroupId());//启动流程时组织id
        //候选人处理

        JSONObject nodeUser = new JSONObject();
        JSONArray userArry = new JSONArray();
        for (Object o : paramObj.getJSONArray("nodeUsers")) {
            String aa = JSON.toJSONString(o);
            JSONObject jsonObject = JSONObject.parseObject(aa);
            userArry.add(jsonObject);
        }
        nodeUser.put("UOMP_FLOW_ISSUES-UserTask2",userArry);

        flowParam.setNodeUsers(nodeUser);
        flowParam.setData(getData(paramObj,order_type));//表单数据
        return getResultMsg(flowParam,0);
    }

    /**
     * <AUTHOR>
     * @Description //初始化busdata
     * @Date 9:28 2023/08/03
     * @param
     * @return JSONObject
     **/
    public JSONObject getData(JSONObject data,String order_type) {
        data.remove("nodeUsers");
        //拼接data
        JSONObject flowData = new JSONObject();
        //工单子表
        JSONObject orderObj = new JSONObject();
        orderObj.put("DEL_FLAG", CommonConstants.NO);
        if(StringUtil.isEmpty(data.getString("IF_FROM_API"))) {
            orderObj.put("IF_FROM_API", CommonConstants.YES);
        }
        orderObj.put("RESTART",0);
        //处理信息子表
        JSONObject handerObj = new JSONObject();
        handerObj.put("DEL_FLAG", CommonConstants.NO);
        data.put("DEL_FLAG", CommonConstants.NO);
        data.put("UOMP_WORK_ORDER",orderObj);
        //1事件 2问题
        if(WorkOrderFlowEnum.EVENT.getOrderType().equals(order_type) || WorkOrderFlowEnum.EVENT_PATENT.getOrderType().equals(order_type)) {
            data.put("UOMP_EVENT_HANDLLING",handerObj);
            data.put("TASK_NAME","UOMP_FLOW_EVENT-UserTask2");
            flowData.put("UOMP_EVENT",data);
        } else if (WorkOrderFlowEnum.ISSUES.getOrderType().equals(order_type)){
            data.put("UOMP_ISSUES_HANDLLING",handerObj);
            data.put("TASK_NAME","UOMP_FLOW_ISSUES-UserTask2");
            flowData.put("UOMP_ISSUES",data);
        }else if (WorkOrderFlowEnum.ISSUES_PATENT.getOrderType().equals(order_type)){
            data.put("UOMP_ISSUES_HANDLLING",handerObj);
            data.put("TASK_NAME","UOMP_FLOW_ISSUES-UserTask2");
            flowData.put("UOMP_ISSUES",data);
        } else {
            //未来扩展
        }
        return flowData;
    }

   /**
    * <AUTHOR>
    * @Description //搁置按钮 取消搁置按钮脚本
    * @Date 9:22 2023/08/15
    * @param cmd
    * @param type
    * @return
    **/
    public void btnScript(BaseActionCmd cmd, String type) {
        BpmInstance bpmInstance = bpmInstanceManager.get(cmd.getInstanceId());
        if("putAside".equals(type)) {   //搁置
            bpmInstance.setIsForbidden(BpmInstance.INSTANCE_FORBIDDEN);
            bpmInstanceManager.update(bpmInstance);
            // 更新业务数据
            //更新工单表是否挂起状态和挂起时间
            UompWorkOrder workOrder = new UompWorkOrder();
            workOrder.setOrderState("2");//搁置
            workOrder.setInstId(cmd.getInstanceId());
            workOrder.setIfPause(Integer.parseInt(CommonConstants.YES));
            workOrder.setPauseTime(new Date());
            this.updateByInstid(workOrder);
            //插入搁置记录
            uompOrderRecordManager.insertOrderRecordByActionCmd(cmd,0);
            UompTimelimitPause pauseHis = uompTimeLimitPauseService.getByInstid(cmd.getInstanceId());
            if(pauseHis == null) {
                //插入暂停时限记录
                UompTimelimitPause pause = new UompTimelimitPause();
                pause.setInstId(cmd.getInstanceId());
                pause.setStartTime(new Date());
                pause.setLimitState(CommonConstants.NO);
                pause.setDelFlag(CommonConstants.NO);
                uompTimeLimitPauseService.create(pause);
            } else {
                pauseHis.setStartTime(new Date());
                uompTimeLimitPauseService.udpate(pauseHis);
            }
        } else if("reAside".equals(type)){  //取消搁置
            bpmInstance.setIsForbidden(BpmInstance.INSTANCE_NO_FORBIDDEN);
            bpmInstanceManager.update(bpmInstance);
            //获取历史暂停时限
            //int pauseSum = uompTimeLimitPauseService.getPauseSum(cmd.getInstanceId());
            //更新暂停时限记录
            UompTimelimitPause pause = uompTimeLimitPauseService.getByInstid(cmd.getInstanceId());
            pause.setEndTime(new Date());
            pause.setLimitState(CommonConstants.YES);
            pause.setLimitSum((int) TimeUtils.calcTimeLimitByMinute(pause.getStartTime(),pause.getEndTime()));
            uompTimeLimitPauseService.updateLimitPauseByInstid(pause);
            //更新工单表是否挂起状态为否
            UompWorkOrder workOrder = this.getWorkOrderByInstid(cmd.getInstanceId());
            workOrder.setIfPause(Integer.parseInt(CommonConstants.NO));
            workOrder.setOrderState("1");//处理中
            //更新流程到期日期 根据暂停时限往后顺延
            if(workOrder.getFlowOverTime() != null) {
                workOrder.setFlowOverTime(TimeUtils.getTimeByMinute(workOrder.getFlowOverTime(),pause.getLimitSum()));
            }
            //更新流程暂停时间汇总
            workOrder.setPauseSum(pause.getLimitSum());
            this.updateByInstid(workOrder);
            //插入搁置记录
            uompOrderRecordManager.insertOrderRecordByActionCmd(cmd,pause.getLimitSum());
        } else {}
    }

    public String selectSla(String sla_id, String sla_level) {
        return uompWorkOrderMapper.selectSla(sla_id,sla_level);
    }
    public ResultMsg manualEndFlow(JSONObject paramObj){
        //设置用户
        String user_id = paramObj.getString("USER_ID");
        if(StringUtil.isEmpty(user_id)){
            user_id = SysPropertyUtil.getByAlias("UOMP_ORDER_CREATE_DEFAULT_USER_ID");
        }
        ContextUtil.setCurrentUser(this.userService.getUserById(user_id));
        String inst_id = paramObj.getString("INST_ID");
        if(StringUtil.isEmpty(inst_id)) {
            inst_id = uompWorkOrderMapper.getInstIdByOrderNo((String)paramObj.get("ORDER_NO"));
        }
        String task_id = uompWorkOrderMapper.getTaskId(inst_id);
        FlowRequestParam flowParam = new FlowRequestParam();
        flowParam.setAction(ActionType.MANUALEND.getKey());//按钮key
        flowParam.setActionName(ActionType.MANUALEND.getName());//按钮名称
        flowParam.setInstanceId(inst_id);
        flowParam.setTaskId(task_id);
        flowParam.setOpinion(paramObj.getString("OPINION"));
        return getResultMsg(flowParam,1);
    }
    public ResultMsg manualEndFlow(String user_id,String inst_id,String opinion){
        //设置用户

        if(StringUtil.isEmpty(user_id)){
            user_id = SysPropertyUtil.getByAlias("UOMP_ORDER_CREATE_DEFAULT_USER_ID");
        }
        ContextUtil.setCurrentUser(this.userService.getUserById(user_id));
        String task_id = uompWorkOrderMapper.getTaskId(inst_id);
        FlowRequestParam flowParam = new FlowRequestParam();
        flowParam.setAction(ActionType.MANUALEND.getKey());//按钮key
        flowParam.setActionName(ActionType.MANUALEND.getName());//按钮名称
        flowParam.setInstanceId(inst_id);
        flowParam.setTaskId(task_id);
        flowParam.setOpinion(opinion);
        return getResultMsg(flowParam,1);
    }
    @NotNull
    private ResultMsg getResultMsg(FlowRequestParam flowParam,int type) {
        String resultMsg = "";
        String instid = "";
        if(type == 0) {
            DefaultInstanceActionCmd instanceActionCmd = new DefaultInstanceActionCmd(flowParam);
            resultMsg = instanceActionCmd.executeCmd();
            instid = instanceActionCmd.getInstanceId();
        } else {
            DefualtTaskActionCmd taskModel = new DefualtTaskActionCmd(flowParam);
            resultMsg = taskModel.executeCmd();
            instid = taskModel.getInstanceId();
        }

        ResultMsg result = new ResultMsg<>();
        result.setMsg(resultMsg);
        if(resultMsg !=null && resultMsg.contains("执行操作成功")) {
            result.setData(instid);
            result.setOk(true);
            result.setCode("200");
        } else {
            result.setData(instid);
            result.setOk(false);
            result.setCode("500");
        }
        return result;
    }

    public String getInstId(String taskid) {
        return uompWorkOrderMapper.getInstId(taskid);
    }

    public String getDetailUrl(NotifyMessage notifyMessage){
        JSONObject jsonObject = new JSONObject();
        StringBuilder sbr = new StringBuilder();
        String inst_id = (String) notifyMessage.getExtendVars().get("inst_id");
        if(StringUtil.isEmpty(inst_id)) {
            inst_id = uompWorkOrderMapper.getInstId((String)notifyMessage.getExtendVars().get("detailId"));
        }
        sbr.append("/gwuomp-main-ui/bpm/task.html?");
        sbr.append("id=" + notifyMessage.getExtendVars().get("detailId"));
        String linkId = (String) notifyMessage.getExtendVars().get("linkId");
        if(StringUtil.isEmpty(linkId)) {
            linkId = uompWorkOrderMapper.getLinkId(inst_id,(String)notifyMessage.getExtendVars().get("detailId"));
        }
        sbr.append("&taskLinkId=" + linkId);
        sbr.append("&instanceId=" + inst_id);
        if(StringUtil.isNotEmpty((String) notifyMessage.getExtendVars().get("formMainType"))) {
            sbr.append("&formMainType=" + notifyMessage.getExtendVars().get("formMainType"));
        }
        if(StringUtil.isNotEmpty((String) notifyMessage.getExtendVars().get("person_info_id"))) {
            sbr.append("&person_info_id=" + notifyMessage.getExtendVars().get("person_info_id"));
        }
        jsonObject.put("type","1");
        jsonObject.put("url",sbr.toString());
        return jsonObject.toJSONString();
    }
    /**
     * <AUTHOR>
     * @Description //读取周期性工单配置列表
     * @Date 15:16 2023/12/01
     * @return java.util.List<cn.gwssi.uomp.entity.UompCycleOrderConfig>
     **/
    public List<UompCycleOrderConfig> getCycleOrderConfigList(String param){
        List<UompCycleOrderConfig> list = uompCycleOrderConfigMapper.getConfigList(param);
        //子表处理
        for(UompCycleOrderConfig config : list) {
            List<UompCycleOrderTime> timeList =  uompCycleOrderConfigMapper.getExecTimeList(config.getId());
            List<UompCycleOrderHandler> handlerList = uompCycleOrderConfigMapper.getObjectList(config.getId());
            config.setTimeList(timeList);
            config.setHandlerList(handlerList);
        }
        return list;
    }

    public void insertCycleRecord(Map<String,String> param){
        uompWorkOrderMapper.insertCycleRecord(param);
    }

    public int findOrderByTimeAndConfigId(String id,String timestr, String handler_id){
        return uompWorkOrderMapper.findOrderByTimeAndConfigId(id,timestr,handler_id);
    }
}
