package cn.gwssi.uomp.service;

import cn.gwssi.uomp.entity.UompAlarm;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UompAlarmService {

    void acceptAlarmMessage(Map<String, Object> paramMap) throws Exception;

    List<UompAlarm> getUnCheckList(UompAlarm alarm);

    /**
     * 执行静默周期性查看（查看静默规则中，启动状态，周期性的规则。并调用prometheus接口）
     */
    void handleSilence();
}
