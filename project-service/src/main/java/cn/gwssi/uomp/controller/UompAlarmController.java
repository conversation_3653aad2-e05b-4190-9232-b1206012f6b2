package cn.gwssi.uomp.controller;

import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.uomp.service.UompAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api("告警信息")
@RestController
@RequestMapping(value = "/alarm", produces = "application/json;charset=UTF-8")
public class UompAlarmController {

    @Autowired
    private UompAlarmService uompAlarmService;

    @ApiOperation(value = "接收告警信息")
    @PostMapping("/acceptAlarmMessage")
    public ResultMsg acceptAlarmMessage(@RequestBody Map<String,Object> paramMap) throws Exception {
        uompAlarmService.acceptAlarmMessage(paramMap);
        return ResultMsg.SUCCESS();
    }
}
