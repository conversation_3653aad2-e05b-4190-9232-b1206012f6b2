package cn.gwssi.uomp.controller;

import cn.gwssi.ecloudframework.base.api.aop.annotion.OperateLog;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import cn.gwssi.uomp.service.UompResourceService;
import cn.gwssi.uomp.service.UompWorkOrderService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@Api(
        tags = "工单接口"
)
@RestController
@RequestMapping(value = "/openapi/order", produces = "application/json;charset=UTF-8")
@Slf4j
public class UompWorkOrderController extends ControllerTools {
    @Autowired
    UompWorkOrderService orderService;
    @Autowired
    UompResourceService uompResourceService;

    @ApiOperation(value = "安管创建工单")
    @OperateLog
    @PostMapping("/createForDssp")
    public ResultMsg createWorkOrder(@RequestBody JSONObject object){
        String orderType = object.getString("ORDER_TYPE");//工单类型
        //处理候选人
        JSONObject nodeUser = object.getJSONObject("nodeUsers");
        //为空默认取系统参数配置的业务组
        if(nodeUser==null || nodeUser.isEmpty()) {
            String teamStr =  SysPropertyUtil.getByAlias("UOMP_ORDER_DEFAULT_HANDLER_GROUP");
            object.put("nodeUsers",JSONObject.parseObject(teamStr));
        }
        //处理优先级
        String slaConfig = SysPropertyUtil.getByAlias("UOMP_ORDER_DEFAULT_SLA");
        JSONObject slaConfObj = JSONObject.parseObject(slaConfig);
        String sla_level = object.getString("SLA_LEVEL");
        object.putAll(slaConfObj);
        if(StringUtil.isNotEmpty(sla_level)) {
            String sla_id = orderService.selectSla(slaConfObj.getString("SLA_ID"),sla_level);
            object.put("EVENT_LEVEL",sla_id);
            object.put("SLA_LEVEL",sla_level);
        }
        object.put("IF_FROM_API","2"); //2来自安管
        return orderService.createWorkOrder(object,orderType);
    }

    @ApiOperation(value = "撤销工单")
    @OperateLog
    @PostMapping("/manualEndWorkOrder")
    public ResultMsg manualEndWorkOrder(@RequestBody JSONObject object){
        if(StringUtil.isEmpty((String) object.get("INST_ID"))) {
            return ResultMsg.ERROR("流程实例ID不能为空");
        }
        return orderService.manualEndFlow(object);
    }

    @ApiOperation(value = "获取所有审核通过配置信息")
    @OperateLog
    @GetMapping("/getAllResourceList")
    public ResultMsg getAllResourceList() {
        List<Map<String,Object>> list = uompResourceService.getAllResourceList();
        return ResultMsg.SUCCESS(list);
    }
}
