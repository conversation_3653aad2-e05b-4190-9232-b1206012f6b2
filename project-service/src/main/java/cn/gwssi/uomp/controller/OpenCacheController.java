package cn.gwssi.uomp.controller;

import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.aop.annotion.OperateLog;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.cache.ICache;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.ssssssss.magicapi.core.config.MagicConfiguration;

import javax.annotation.Resource;

/**
 * Description: 清理缓存功能
 */
@RestController
@RequestMapping("/openapi/cache")
public class OpenCacheController extends ControllerTools {
    protected Logger LOG = LoggerFactory.getLogger(this.getClass());
    /**
     *
     */
    @Resource
    ICache iCache;

    @RequestMapping("clearAll")
    @OperateLog
    @CatchErr("清除缓存失败")
    public ResultMsg<String> clearCache(@RequestParam(value = "type", required = false) String type) {
        boolean ifDefault = true;
        boolean ifToken = true;
        boolean ifApi = true;
        boolean iflogConfig = true;
        if (StringUtils.isNotEmpty(type)) {
            ifDefault = false;
            ifToken = false;
            ifApi = false;
            iflogConfig = false;
            if (type.contains("default")) {
                ifDefault = true;
            }
            if (type.contains("token")) {
                ifToken = true;
            }
            if (type.contains("api")) {
                ifApi = true;
            }
            if (type.contains("logConfig")) {
                iflogConfig = true;
            }
        }
        //iCache.clearAll();
        if (ifDefault) {
            iCache.clearRegion("default");
            LOG.info("---------------清理默认缓存成功----------------");
        }
        if (ifToken) {
            iCache.clearRegion("jwtToken:jwt");
            LOG.info("---------------清理jwtToken缓存成功----------------");
        }
        if (ifApi) {
            MagicConfiguration.getMagicResourceService().refresh();
            LOG.info("---------------清理api缓存成功----------------");
        }
        if (iflogConfig) {
            iCache.clearRegion("logconfig");
            LOG.info("---------------清理日志配置缓存成功----------------");
        }
        return getSuccessResult("成功清除缓存");
    }
}
