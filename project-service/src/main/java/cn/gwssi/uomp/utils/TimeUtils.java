package cn.gwssi.uomp.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {
    //Date类型转Calendar类型
    public static Calendar dataToCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * <AUTHOR>
     * @Description //获取N小时后的时间
     * @Date 14:26 2023/4/13
     * @param date 开始时间
     * @param hours 小时
     * @return java.lang.String
     **/
    public static String getTimeByHours(Date date,int hours) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = dataToCalendar(date);
        calendar.add(Calendar.HOUR_OF_DAY,hours);
        return dateFormat.format(calendar.getTime());
    }
    /**
     * <AUTHOR>
     * @Description //获取分钟后的时间
     * @Date 14:26 2023/4/13
     * @param date 开始时间
     * @param minute 分钟
     * @return 格式化的时间串
     **/
    public static String getTimeStrByMinute(Date date,int minute) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = dataToCalendar(date);
        calendar.add(Calendar.MINUTE,minute);
        return dateFormat.format(calendar.getTime());
    }

    /**
     * <AUTHOR>
     * @Description //获取分钟后的时间
     * @Date 14:26 2023/4/13
     * @param date 开始时间
     * @param minute 分钟
     * @return Date
     **/
    public static Date getTimeByMinute(Date date,int minute) {
        Calendar calendar = dataToCalendar(date);
        calendar.add(Calendar.MINUTE,minute);
        return calendar.getTime();
    }

    //获取时间差方法
    /**
     * <AUTHOR>
     * @Description //获取时间差
     * @Date 14:26 2023/4/13
     * @param startTime
     * @param endTime
     * @param calcType 计算方式 按小时 or 按分钟 or 按秒
     * @param pauseLimit 暂停分钟数
     * @return java.lang.String
     **/
    public static String getTime( Date startTime, Date endTime, int calcType, int pauseLimit) {
        //这样得到的差值是微秒级别
        long diff = endTime.getTime() - startTime.getTime();
        if(diff == 0) {
            return null;
        }
        //如果存在暂停的，则减去暂停时限
        if(pauseLimit > 0 ) {
            diff = diff - (pauseLimit*60*1000);
        }
        //当前系统时间转Calendar类型
        //Calendar currentTimes = dataToCalendar(endTime);
        //查询的数据时间转Calendar类型
        //Calendar firstTimes = dataToCalendar(startTime);
//        int year = currentTimes.get(Calendar.YEAR) - firstTimes.get(Calendar.YEAR);//获取年
//        int month = currentTimes.get(Calendar.MONTH) - firstTimes.get(Calendar.MONTH);//获取月
//        int day = currentTimes.get(Calendar.DAY_OF_MONTH) - firstTimes.get(Calendar.DAY_OF_MONTH);//获取日
//        if (day < 0) {
//            month -= 1;
//            currentTimes.add(Calendar.MONTH, -1);
//            day = day + currentTimes.getActualMaximum(Calendar.DAY_OF_MONTH);
//        }
//        if (month < 0) {
//            month = (month + 12) % 12;
//            year--;
//        }
        /* //如果需要转换成年月日时分秒 解开下列注释
        long days = diff / (1000 * 60 * 60 * 24);//天数
        long hours = (diff - days * (1000 * 60 * 60 * 24)) / (1000 * 60 * 60); //获取时
        long minutes = (diff - days * (1000 * 60 * 60 * 24) - hours * (1000 * 60 * 60)) / (1000 * 60);  //获取分钟
        long s = (diff / 1000 - days * 24 * 60 * 60 - hours * 60 * 60 - minutes * 60);//获取秒
        String CountTime = year + "年" + month + "月" + day + "天" + hours + "小时" + minutes + "分" + s + "秒";
        */
        //不转化成年月日形式
        StringBuilder timelimitStr = new StringBuilder();
        long hours = diff / (1000 * 60 * 60); //获取时
        long minutes = (diff -  hours * (1000 * 60 * 60)) / (1000 * 60);  //获取分钟
        long second = (diff / 1000 -  hours * 60 * 60 - minutes * 60);//获取秒
        /*if(hours < 0 || minutes < 0 || second < 0) {
            timelimitStr.append("已超期");
        } else {
            timelimitStr.append("剩余");
        }*/
        timelimitStr.append(hours != 0 ? Math.abs(hours) + "小时": "");
        timelimitStr.append(minutes != 0 ? Math.abs(minutes) + "分钟": "");
        timelimitStr.append(second != 0 ? Math.abs(second) + "秒": "");
        return timelimitStr.toString();
    }
    /**
     * <AUTHOR>
     * @Description //计算两个时间之前的分钟差
     * @Date 14:08 2023/5/9
     * @param startTime
     * @param endTime
     * @return long
     **/
    public static long calcTimeLimitByMinute(Date startTime,Date endTime) {
        //这样得到的差值是微秒级别
        long diff = endTime.getTime() - startTime.getTime();
        double minutes = diff / (1000d * 60d);  //获取分钟
        return Math.round(minutes);
    }

}
