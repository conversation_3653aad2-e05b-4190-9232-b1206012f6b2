package cn.gwssi.ecloud.base.samples.config;

import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
//@EnableSwagger2
public class Swagger2Config {

    @Value("${SWAGGER_CONFIG_ENABLE:false}")
    private boolean swaggerShow;

    @Bean
    public Docket createSysRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.base"))
                .paths(PathSelectors.any()).build().groupName("BPM-BASE接口").pathMapping("/");
    }

    @Bean
    public Docket createSecurityRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.security"))
                .paths(PathSelectors.any()).build().groupName("BPM系统认证接口").pathMapping("/");
    }

    @Bean
    public Docket createOrgRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.org"))
                .paths(PathSelectors.any()).build().groupName("BPM用户接口").pathMapping("/");
    }

    @Bean
    public Docket createBaseRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.sys"))
                .paths(PathSelectors.any()).build().groupName("BPM系统接口").pathMapping("/");
    }

    @Bean
    public Docket createOpenapiRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.module.openapi"))
                .paths(PathSelectors.any()).build().groupName("BPMOpenApi接口").pathMapping("/");
    }

    @Bean
    public Docket createMessageRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.module.message"))
                .paths(PathSelectors.any()).build().groupName("BPM消息接口").pathMapping("/");
    }

    @Bean
    public Docket createWfRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.wf"))
                .paths(PathSelectors.any()).build().groupName("BPM工作流引擎").pathMapping("/");
    }

    @Bean
    public Docket createFormRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.form"))
                .paths(PathSelectors.any()).build().groupName("BPM表单").pathMapping("/");
    }

    @Bean
    public Docket createBusRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.bus"))
                .paths(PathSelectors.any()).build().groupName("BPM业务对象及实体").pathMapping("/");
    }

    @Bean
    public Docket createDocumentRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.module.document"))
                .paths(PathSelectors.any()).build().groupName("公文管理").pathMapping("/");
    }

    @Bean
    public Docket createTemplateRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.module.template"))
                .paths(PathSelectors.any()).build().groupName("模版管理").pathMapping("/");
    }

    @Bean
    public Docket createVisualRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.module.visual"))
                .paths(PathSelectors.any()).build().groupName("大屏管理").pathMapping("/");
    }



    @Bean
    public Docket createFrontendRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudbpm.module.frontend"))
                .paths(PathSelectors.any()).build().groupName("前端公共配置管理").pathMapping("/");
    }

    @Bean
    public Docket createStaffPoolRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloud.staffpool"))
                .paths(PathSelectors.any()).build().groupName("人员管理").pathMapping("/");
    }

    @Bean
    public Docket createItsmRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloudframework.module.itsm"))
                .paths(PathSelectors.any()).build().groupName("ITSM模块").pathMapping("/");
    }

    @Bean
    public Docket createBraceletRestApiForAuth() {
        return new Docket(DocumentationType.SWAGGER_2).enable(swaggerShow).apiInfo(apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("cn.gwssi.ecloud.bracelet"))
                .paths(PathSelectors.any()).build().groupName("智能手表").pathMapping("/");
    }


    private ApiInfo apiInfo() {
        return new ApiInfoBuilder().title("Gwssi eCloud OA2.0")
                .description("协同办公平台平台接口文档")
                .termsOfServiceUrl("")
                .version("1.0").build();
    }
}
