package cn.gwssi.ecloud.base.samples.config;

import liquibase.change.custom.CustomTaskChange;
import liquibase.database.Database;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.CustomChangeException;
import liquibase.exception.SetupException;
import liquibase.exception.ValidationErrors;
import liquibase.resource.ResourceAccessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Statement;

public class LiquibaseDMAdapterTask implements CustomTaskChange {

    private String sqlFile ;

    public String getSqlFile() {
        return sqlFile;
    }

    public void setSqlFile(String sqlFile) {
        this.sqlFile = sqlFile;
    }

    @Override
    public void execute(Database database) throws CustomChangeException {

        Resource resource = new ClassPathXmlApplicationContext().getResource(sqlFile);
        JdbcConnection connection = (JdbcConnection) database.getConnection() ;

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            String sql;
            while ((sql = reader.readLine()) != null) {
                if (StringUtils.isNotEmpty(sql)) {
                    sql = sql.replaceAll("`", "");
                    try (Statement statement = connection.createStatement()) {
                        statement.executeUpdate(sql);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getConfirmationMessage() {
        return sqlFile+" 执行完成！";
    }

    @Override
    public void setUp() throws SetupException {

    }

    @Override
    public void setFileOpener(ResourceAccessor resourceAccessor) {

    }

    @Override
    public ValidationErrors validate(Database database) {
        return null;
    }

}
