package cn.gwssi.ecloud.base.samples.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.regex.Pattern;

public class SQLInjectionFilter implements Filter {

    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            "[;'\"\\)\\[\\+\\-]|(--)|\\b(select|delete|drop|insert|update|union|exec|truncae|alter|create|show)\\b",
            Pattern.CASE_INSENSITIVE
    );
    public static final String ORDER_BY = "orderBy";

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String orderByParam = httpRequest.getParameter(ORDER_BY);
        if (orderByParam != null && SQL_INJECTION_PATTERN.matcher(orderByParam).find()) {
            httpResponse.sendError(HttpServletResponse.SC_BAD_REQUEST, "URL中orderBy参数值非法");
            return;
        }
        if ("POST".equalsIgnoreCase(httpRequest.getMethod()) || "PUT".equalsIgnoreCase(httpRequest.getMethod())) {
            String contentType = httpRequest.getContentType();
            if (contentType != null && contentType.contains("application/json")) {
                CachedBodyHttpServletRequestWrapper wrapperRequest = new CachedBodyHttpServletRequestWrapper(httpRequest);
                String requestBody = new String(wrapperRequest.getCachedBody());

                // 解析 JSON
                JsonNode jsonNode = objectMapper.readTree(requestBody);
                if (jsonNode.has(ORDER_BY)) {
                    String orderByValue = jsonNode.get(ORDER_BY).asText();
                    if (SQL_INJECTION_PATTERN.matcher(orderByValue).find()) {
                        httpResponse.sendError(HttpServletResponse.SC_BAD_REQUEST, "orderBy参数值非法");
                        return;
                    }
                }

                //继续执行过滤链 并使用包装后的 request
                filterChain.doFilter(wrapperRequest, response);
                return;
            }
        }

        // 继续执行过滤链
        filterChain.doFilter(request, response);
    }

}
