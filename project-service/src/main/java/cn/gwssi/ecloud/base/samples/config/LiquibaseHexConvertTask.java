package cn.gwssi.ecloud.base.samples.config;

import cn.hutool.core.util.HexUtil;
import liquibase.change.custom.CustomTaskChange;
import liquibase.database.Database;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.CustomChangeException;
import liquibase.exception.SetupException;
import liquibase.exception.ValidationErrors;
import liquibase.resource.ResourceAccessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LiquibaseHexConvertTask implements CustomTaskChange {

    public static final String regex =  "'#@_@#START#@_@#(.*?)#@_@#END#@_@#'";

    private String sqlFile ;

    public String getSqlFile() {
        return sqlFile;
    }

    public void setSqlFile(String sqlFile) {
        this.sqlFile = sqlFile;
    }

    @Override
    public void execute(Database database) throws CustomChangeException {

        Resource resource = new ClassPathXmlApplicationContext().getResource(sqlFile);
        JdbcConnection connection = (JdbcConnection) database.getConnection() ;
        boolean isDM = database.getDatabaseProductName().startsWith("DM") ;

        Pattern pattern = Pattern.compile(regex);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            String sql;
            while ((sql = reader.readLine()) != null) {
                if (StringUtils.isNotEmpty(sql)) {
                    Matcher matcher = pattern.matcher(sql);
                    List<String> args = new ArrayList<String>();
                    while (matcher.find()) {
                        String extractedText = matcher.group(1);
                        //16进制转换
                        args.add(HexUtil.decodeHexStr(extractedText));
                        sql = sql.replaceFirst(regex, "?");
                    }
                    if (isDM) {
                        sql = sql.replaceAll("`", "");
                    }
                    try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                        for (int i = 0; i < args.size(); i++) {
                            preparedStatement.setString(i + 1, args.get(i));
                        }
                        preparedStatement.executeUpdate();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getConfirmationMessage() {
        return sqlFile+" 执行完成！";
    }

    @Override
    public void setUp() throws SetupException {

    }

    @Override
    public void setFileOpener(ResourceAccessor resourceAccessor) {

    }

    @Override
    public ValidationErrors validate(Database database) {
        return null;
    }

}
