package cn.gwssi.ecloud.base.samples.config;

import cn.gwssi.ecloud.base.samples.utils.AESUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.*;

import java.util.Properties;

public class DecryptEnvironmentPostProcessor implements EnvironmentPostProcessor {
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        Properties props = new Properties();
        // 加密密码前缀为 "ENC("，后缀为 ")"
        MutablePropertySources propertySources = environment.getPropertySources();
        for (PropertySource<?> propertySource : propertySources) {
            if (propertySource instanceof EnumerablePropertySource) {
                EnumerablePropertySource<?> enumerablePropertySource = (EnumerablePropertySource<?>) propertySource;
                String[] propertyNames = enumerablePropertySource.getPropertyNames();
                // 遍历所有配置key:value
                for (String propertyName : propertyNames) {
                    String propertyVal = environment.getProperty(propertyName);
                    if (propertyVal != null && propertyVal.startsWith("ENC(") && propertyVal.endsWith(")")) {
                        // 解析得到加密的数据
                        String encryptedValue = propertyVal.substring(4, propertyVal.length() - 1);
                        // 调用自定义工具类解密
                        try {
                            String decryptedValue = AESUtil.decrypt(encryptedValue, "<EMAIL>");
                            // 保存需要替换的配置
                            props.put(propertyName, decryptedValue);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
        // 添加解密后的属性到环境中
        if (!props.isEmpty()) {
            PropertiesPropertySource pps = new PropertiesPropertySource("decryptedProperties", props);
            environment.getPropertySources().addFirst(pps);
        }
    }
}