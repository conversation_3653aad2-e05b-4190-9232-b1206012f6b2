package cn.gwssi.ecloud.base.samples.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<SQLInjectionFilter> sqlInjectionFilterFilterRegistration() {
        FilterRegistrationBean<SQLInjectionFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new SQLInjectionFilter());
        registrationBean.addUrlPatterns("/*");
        return registrationBean;
    }
}
