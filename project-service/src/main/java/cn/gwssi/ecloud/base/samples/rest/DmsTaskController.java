package cn.gwssi.ecloud.base.samples.rest;

import cn.gwssi.ecloudbpm.goffice.common.model.PageQuery;
import cn.gwssi.ecloudbpm.goffice.common.utils.page.PageHelperUtils;
import cn.gwssi.ecloudbpm.wf.core.manager.TaskIdentityLinkManager;
import cn.gwssi.ecloudframework.base.db.model.page.PageResult;
import cn.gwssi.ecloudframework.module.dms.core.dao.DmsTaskMapper;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/dms/task")
public class DmsTaskController {

    @Resource
    private TaskIdentityLinkManager taskIdentityLinkManager;
    @Resource
    private DmsTaskMapper dmsTaskMapper;

    @PostMapping("todos")
    public PageResult getTodoList(@RequestBody PageQuery pageQuery) {

        Set<String> userRights = taskIdentityLinkManager.getUserRights(ContextUtil.getCurrentUserId());
        PageHelperUtils.startPageAndOrderBy(pageQuery, null);

        List<Map<String, String>> todoList = dmsTaskMapper.getTodoList(userRights, ContextUtil.getCurrentUserId());

        return new PageResult(todoList);
    }

    @PostMapping("done")
    public PageResult getDoneList(@RequestBody PageQuery pageQuery) {

        PageHelperUtils.startPageAndOrderBy(pageQuery, null);
        List<Map<String, String>> doneList = dmsTaskMapper.getDoneList(ContextUtil.getCurrentUserId());

        return new PageResult(doneList);
    }
}
