package cn.gwssi.ecloudbpm.wf.rest;

import cn.gwssi.ecloudbpm.form.api.model.FormType;
import cn.gwssi.ecloudbpm.service.BpmSomeService;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.FlowRequestParam;
import cn.gwssi.ecloudbpm.wf.api.engine.data.BpmFlowDataAccessor;
import cn.gwssi.ecloudbpm.wf.api.engine.data.result.BpmFlowData;
import cn.gwssi.ecloudbpm.wf.api.engine.data.result.BpmFlowInstanceData;
import cn.gwssi.ecloudbpm.wf.api.engine.data.result.FlowData;
import cn.gwssi.ecloudbpm.wf.api.exception.BpmStatusCode;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmDefinitionManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskOpinionManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmDefinition;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTaskOpinion;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefaultInstanceActionCmd;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.aop.annotion.OperateLog;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.AppUtil;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.base.rest.util.RequestUtil;
import cn.gwssi.uomp.service.UompOrderRecordManager;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/bpm/gwuomp/instance")
public class GwBpmInstanceController extends ControllerTools {
    @Resource
    private BpmFlowDataAccessor bpmFlowDataAccessor;
    @Resource
    private UompOrderRecordManager uompOrderRecordManager;
    @Resource
    private BpmDefinitionManager bpmDefinitionManager;
    @Autowired
    private BpmTaskOpinionManager bpmTaskOpinionManager;
    @Autowired
    private BpmSomeService bpmSomeService;


    /**
     * 处理实例类型的动作
     *
     * @void
     */
    @RequestMapping(value = "doAction", method = {RequestMethod.POST})
    @CatchErr(write2errorlog = true)
    @OperateLog
    @ApiOperation(value = "执行流程实例相关动作", notes = "流程启动，流程保存草稿，草稿启动，催办，人工终止等流程实例相关的动作请求入口")
    public ResultMsg<String> doAction(@RequestBody FlowRequestParam flowParam) {
        if (StringUtils.equals(flowParam.getAction(), "turnStart")) {
            flowParam.setAction("start");
        }
        DefaultInstanceActionCmd instanceCmd = new DefaultInstanceActionCmd(flowParam);
        String actionName = instanceCmd.executeCmd();
        return this.getSuccessResult(instanceCmd.getInstanceId(), actionName);
    }


    @RequestMapping(
            value = {"getInstanceData"},
            method = {RequestMethod.POST, RequestMethod.GET}
    )
    @CatchErr
    @OperateLog
    @ApiOperation(value = "流程实例数据", notes = "获取流程实例相关数据，包含实例信息，业务数据，表单权限、表单数据、表单内容等")
    @ApiImplicitParams({@ApiImplicitParam(paramType = "form", dataType = "String", name = "instanceId", value = "流程实例ID"),
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "readonly", value = "是否只读实例", defaultValue = "false"),
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "defId", value = "流程定义ID，启动时使用"),
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "flowKey", value = "流程定义Key，启动时使用,与DefId二选一"),
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "formType", value = "表单类型", defaultValue = "pc")})
    public ResultMsg<FlowData> getInstanceData(HttpServletRequest request) {
        String instanceId = request.getParameter("instanceId");
        Boolean readonly = RequestUtil.getBoolean(request, "readonly", false);
        String defId = request.getParameter("defId");
        String flowKey = RequestUtil.getString(request, "flowKey");
        String nodeId = RequestUtil.getString(request, "nodeId");
        String btnJson = RequestUtil.getString(request, "btnList");
        String excludeBtnJson = RequestUtil.getString(request, "excludeBtnList");
        String taskId = RequestUtil.getString(request, "taskId");
        String busDataId = RequestUtil.getString(request, "busDataId");
        String busDataKey = RequestUtil.getString(request, "busDataKey");
        if (StringUtils.isNotEmpty(taskId) && (StringUtils.startsWith(taskId, "-") || StringUtils.equals("0", taskId))) {
            taskId = null;
        }

        if (StringUtil.isEmpty(defId) && StringUtil.isNotEmpty(flowKey)) {
            BpmDefinition def = this.bpmDefinitionManager.getByKey(flowKey);
            if (def == null) {
                throw new BusinessException("流程定义查找失败！ flowKey： " + flowKey, BpmStatusCode.DEF_LOST);
            }

            defId = def.getId();
        }

        if (StringUtils.isNotEmpty(taskId) && StringUtils.isEmpty(nodeId)) {
            BpmTaskOpinion bpmTaskOpinion = this.bpmTaskOpinionManager.getByTaskId(taskId);
            if (bpmTaskOpinion != null) {
                nodeId = bpmTaskOpinion.getTaskKey();
            }
        }

        String formType = RequestUtil.getString(request, "formType", FormType.PC.value());
        if (StringUtil.isNotEmpty(nodeId)) {
            BpmFlowInstanceData instanceData = this.bpmFlowDataAccessor.getInstanceData(instanceId, FormType.fromValue(formType), nodeId, taskId);
            return this.getSuccessResult(instanceData);
        } else {
            BpmFlowData data = this.bpmFlowDataAccessor.getStartFlowData(defId, instanceId, taskId, FormType.fromValue(formType), readonly);
            String[] excludeBtnList;
            if (StringUtil.isNotEmpty(btnJson)) {
                excludeBtnList = btnJson.split(",");
                String[] finalExcludeBtnList = excludeBtnList;
                data.setButtonList((List)data.getButtonList().stream().filter((btn) -> {
                    return ArrayUtils.indexOf(finalExcludeBtnList, btn.getAlias()) != -1;
                }).collect(Collectors.toList()));
            }

            if (StringUtil.isNotEmpty(excludeBtnJson)) {
                excludeBtnList = excludeBtnJson.split(",");
                String[] finalExcludeBtnList1 = excludeBtnList;
                data.setButtonList((List)data.getButtonList().stream().filter((btn) -> {
                    return ArrayUtils.indexOf(finalExcludeBtnList1, btn.getAlias()) == -1;
                }).collect(Collectors.toList()));
            }

            if (StringUtil.isNotEmpty(busDataId)) {
                JSONObject busData = this.bpmSomeService.loadBusData(busDataId, busDataKey);
                JSONObject instBusData = data.getData();
                if (instBusData != null) {
                    instBusData.putAll(busData);
                } else {
                    data.setData(busData);
                }
            }

//            //脱敏转换
//            if (data.getData() != null){
//                //获取流程key
//                BpmFlowInstanceData flowInstanceData = (BpmFlowInstanceData) data;
//                String defKey = flowInstanceData.getInstance().getDefKey();
//
//                MagicAPIService apiService = AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);
//                Map<String, Object> param = new HashMap<>();
//                param.put("inst_key", defKey);
//                param.put("data", data.getData());
//                param.put("ins_id", instanceId);
//                data.setData(apiService.invoke("/desensitize/instanceDesRule", param));
//            }

            return this.getSuccessResult(data);
        }
    }

}
