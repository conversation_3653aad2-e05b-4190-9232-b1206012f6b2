package cn.gwssi.ecloudbpm.wf.service;

import cn.gwssi.ecloudbpm.wf.api.model.inst.IBpmInstance;
import cn.gwssi.ecloudbpm.wf.api.model.nodedef.Button;
import cn.gwssi.ecloudbpm.wf.api.model.task.IBpmTask;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmInstanceManager;
import cn.gwssi.ecloudbpm.wf.core.manager.TaskIdentityLinkManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmInstance;
import cn.gwssi.ecloudbpm.wf.core.model.TaskIdentityLink;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.ecloudframework.sys.util.SysPropertyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class DoSomeService {
    @Resource
    private TaskIdentityLinkManager taskIdentityLinkManager;
    @Resource
    private BpmInstanceManager bpmInstanceManager;

    /**
     * 搁置和取消搁置按钮显示逻辑
     */
    public List<Button> checkPutAside(List<Button> buttonList, IBpmTask bpmTask) {
        BpmInstance bpmInstance = bpmInstanceManager.get(bpmTask.getInstId());
        String excludeBtn = "reAside";
        // 流程搁置状态，不显示同意按钮
        if (Objects.equals(bpmInstance.getIsForbidden(), IBpmInstance.INSTANCE_FORBIDDEN)) {
            buttonList = buttonList.stream().filter(btn -> StringUtils.equals(excludeBtn, btn.getAlias())).collect(Collectors.toList());
        } else {
            buttonList = buttonList.stream().filter(btn -> !StringUtils.equals(excludeBtn, btn.getAlias())).collect(Collectors.toList());
        }
        return buttonList;
    }

    /**
     * 锁定按钮
     *
     * @return
     */
    public List<Button> checkTurnGroup(List<Button> buttonList, IBpmTask bpmTask) {
        List<TaskIdentityLink> taskIdentityLinks = taskIdentityLinkManager.getByTaskId(bpmTask.getId());
        boolean if_group = false;
        //候选人是组的时候 过滤掉保存 退回 同意、搁置按钮
        if(taskIdentityLinks!=null && taskIdentityLinks.size()>0 && "post".equals(taskIdentityLinks.get(0).getType())) {
            if_group = true;
        }
        AtomicBoolean if_admin = new AtomicBoolean(true);
        //组长干预指派 按钮仅保留转派
        taskIdentityLinks.forEach( it-> {
            if(StringUtils.equals(it.getType(),"user") && StringUtils.equals(it.getIdentity(),ContextUtil.getCurrentUserId())) {
                if_admin.set(false);
            }
        });
        if(if_admin.get() || if_group) {
            String excludeBtn = SysPropertyUtil.getByAlias("UOMP_ORDER_EXCLUDE_BTN");
            buttonList = buttonList.stream().filter(btn -> StringUtils.indexOf(excludeBtn, btn.getAlias()) == -1).collect(Collectors.toList());
        }
        return buttonList;
    }
}
