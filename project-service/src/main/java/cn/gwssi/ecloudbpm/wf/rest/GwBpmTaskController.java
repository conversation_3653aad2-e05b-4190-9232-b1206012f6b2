package cn.gwssi.ecloudbpm.wf.rest;

import cn.gwssi.ecloudbpm.form.api.model.FormType;
import cn.gwssi.ecloudbpm.wf.api.engine.action.cmd.FlowRequestParam;
import cn.gwssi.ecloudbpm.wf.api.engine.data.BpmFlowDataAccessor;
import cn.gwssi.ecloudbpm.wf.api.engine.data.result.BpmFlowTaskData;
import cn.gwssi.ecloudbpm.wf.api.engine.data.result.FlowData;
import cn.gwssi.ecloudbpm.wf.api.model.nodedef.Button;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefualtTaskActionCmd;
import cn.gwssi.ecloudbpm.wf.service.DoSomeService;
import cn.gwssi.ecloudframework.base.api.aop.annotion.CatchErr;
import cn.gwssi.ecloudframework.base.api.aop.annotion.OperateLog;
import cn.gwssi.ecloudframework.base.api.response.impl.ResultMsg;
import cn.gwssi.ecloudframework.base.core.util.StringUtil;
import cn.gwssi.ecloudframework.base.rest.ControllerTools;
import cn.gwssi.ecloudframework.module.itsm.core.constants.Constants;
import cn.gwssi.ecloudframework.module.orgCustom.core.manager.OrgRelationCustomManager;
import cn.gwssi.ecloudframework.module.orgCustom.core.model.OrgRelationCustom;
import cn.gwssi.ecloudframework.org.api.model.IUser;
import cn.gwssi.ecloudframework.sys.util.ContextUtil;
import cn.gwssi.uomp.service.UompOrderRecordManager;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("/bpm/gwuomp/task")
public class GwBpmTaskController extends ControllerTools {
    @Resource
    private BpmFlowDataAccessor bpmFlowDataAccessor;
    @Resource
    private DoSomeService doSomeService;
    @Resource
    private OrgRelationCustomManager orgRelationCustomManager;

    @RequestMapping(value = "getTaskData", method = {RequestMethod.POST, RequestMethod.GET})
    @CatchErr(write2errorlog = true)
    @OperateLog
    @ApiOperation(value = "获取流程任务相关数据", notes = "获取任务的业务数据、表单、按钮、权限等信息，为了渲染展示任务页面")
    public ResultMsg<FlowData> getTaskData(@RequestParam @ApiParam(value = "任务ID", required = true) String taskId,
                                           @RequestParam(required = false) @ApiParam(value = "表单类型", defaultValue = "pc") String formType,
                                           @RequestParam(required = false) @ApiParam(value = "任务候选人关系id") String taskLinkId,
                                           @RequestParam(required = false) @ApiParam("排除的按钮,多个按钮用逗号拼接") String excludeBtnJson){
        if (StringUtil.isEmpty(formType)) {
            formType = FormType.PC.value();
        }
        BpmFlowTaskData data = (BpmFlowTaskData) bpmFlowDataAccessor.getFlowTaskData(taskId, taskLinkId, FormType.fromValue(formType));
        if (StringUtil.isNotEmpty(excludeBtnJson)) {
            String[] excludeBtnList = excludeBtnJson.split(",");
            data.setButtonList((List)data.getButtonList().stream().filter((btn) -> {
                return ArrayUtils.indexOf(excludeBtnList, btn.getAlias()) == -1;
            }).collect(Collectors.toList()));
        }
        List<Button> buttonList = data.getButtonList();
        //只有工单才需要排除按钮
        if(data.getDefExtendConfig() !=null) {
            try{
                JSONObject extendConfig = JSONObject.parseObject(data.getDefExtendConfig());
                if("workPiece".equals(extendConfig.getString("formMainType"))) {
                    // 检查搁置
                    data.setButtonList(doSomeService.checkPutAside(buttonList,data.getTask()));
//                    // 检查转派
//                    data.setButtonList(doSomeService.checkTurnGroup(data.getButtonList(),data.getTask()));
                }
            }catch (Exception e) {
                log.error("扩展属性格式化报错：{}",e);
            }
        }
        return getSuccessResult(data);
    }

    @RequestMapping(value = "doAction", method = {RequestMethod.POST})
    @CatchErr(write2errorlog = true)
    @OperateLog
    @ApiOperation(value = "执行任务相关动作", notes = "执行任务相关的动作 如：同意，驳回，反对，锁定，解锁，转办，会签任务等相关操作")
    public ResultMsg<String> doAction(@RequestBody FlowRequestParam flowParam){
        // 查看流程搁置状态 检查下个节点是否是结束节点
//        if (StringUtils.equals(flowParam.getAction(), "submitAudit")) {
//            flowParam.setAction("agree");
//        }
        DefualtTaskActionCmd taskModel = new DefualtTaskActionCmd(flowParam);
        //超管指派人员 判断一下角色
        if(StringUtils.equals(flowParam.getAction(), "turnGroup") || checkWorkOrderRoot(flowParam.getData())) {
            List<OrgRelationCustom> userRole = orgRelationCustomManager.getUserRole(ContextUtil.getCurrentUserId());
            boolean flag = userRole.stream().anyMatch(role -> role.getRoleAlias().equals("ITSM_GROUP LEADER"));
            if (flag){
                taskModel.setIgnoreAuthentication(true);
            }
        }
        String result = taskModel.executeCmd();

        return getSuccessResult(result);
    }

    public boolean checkWorkOrderRoot(JSONObject jsonObjectData){
        boolean isOrderRoot = false;
        JSONObject rootJSONObject = (JSONObject) JSONPath.eval(jsonObjectData,"$.");
        if(rootJSONObject!=null){
            Set<String> rootNodeSet = rootJSONObject.keySet();
            if(!CollectionUtils.isEmpty(rootNodeSet)){
                String rootName="";
                for(String root:rootNodeSet){
                    rootName = root;
                    break;
                }
                if(StringUtils.isNotBlank(rootName)){
                    isOrderRoot =  StringUtils.startsWith(rootName,"WORK_ORDER");

                }
            }
        }
        return isOrderRoot;
    }
}
