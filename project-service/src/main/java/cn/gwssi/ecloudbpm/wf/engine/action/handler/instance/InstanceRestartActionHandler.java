//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package cn.gwssi.ecloudbpm.wf.engine.action.handler.instance;

import cn.gwssi.ecloudbpm.bus.api.model.IBusinessPermission;
import cn.gwssi.ecloudbpm.wf.act.service.ActInstanceService;
import cn.gwssi.ecloudbpm.wf.api.constant.ActionType;
import cn.gwssi.ecloudbpm.wf.api.constant.InstanceStatus;
import cn.gwssi.ecloudbpm.wf.api.constant.OpinionStatus;
import cn.gwssi.ecloudbpm.wf.api.engine.action.handler.BuiltinActionHandler;
import cn.gwssi.ecloudbpm.wf.api.engine.context.BpmContext;
import cn.gwssi.ecloudbpm.wf.api.exception.BpmStatusCode;
import cn.gwssi.ecloudbpm.wf.api.model.def.IBpmDefinition;
import cn.gwssi.ecloudbpm.wf.api.model.inst.IBpmInstance;
import cn.gwssi.ecloudbpm.wf.api.model.nodedef.BpmNodeDef;
import cn.gwssi.ecloudbpm.wf.api.model.task.IBpmTask;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmDefinitionManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmInstanceManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskOpinionManager;
import cn.gwssi.ecloudbpm.wf.core.manager.BpmTaskStackManager;
import cn.gwssi.ecloudbpm.wf.core.model.BpmInstance;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTaskOpinion;
import cn.gwssi.ecloudbpm.wf.core.model.BpmTaskStack;
import cn.gwssi.ecloudbpm.wf.engine.action.cmd.DefaultInstanceActionCmd;
import cn.gwssi.ecloudbpm.wf.engine.data.handle.IBpmBusDataHandle;
import cn.gwssi.ecloudframework.base.api.exception.BusinessException;
import cn.gwssi.ecloudframework.base.api.exception.BusinessMessage;
import cn.gwssi.ecloudframework.base.api.query.QueryFilter;
import cn.gwssi.ecloudframework.base.api.query.QueryOP;
import cn.gwssi.ecloudframework.base.db.model.query.DefaultQueryFilter;
import cn.hutool.core.collection.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component("instanceRestartActionHandler")
public class InstanceRestartActionHandler implements BuiltinActionHandler<DefaultInstanceActionCmd> {
    @Autowired
    private BpmInstanceManager g;
    @Autowired
    private ActInstanceService n;
    @Autowired
    private BpmDefinitionManager a;
    @Resource
    private IBpmBusDataHandle u;
    @Resource
    private BpmTaskStackManager p;
    @Resource
    private BpmTaskOpinionManager o;

    public InstanceRestartActionHandler() {
    }

    public boolean isDisplay(boolean readOnly, BpmNodeDef bpmNodeDef, IBpmInstance bpmInstance, IBpmTask bpmTask) {
        if (bpmInstance != null && BpmContext.currentUserIsBpmAdmin()) {
            return bpmInstance != null ? false : this.b(bpmInstance);
        } else {
            return false;
        }
    }

    private boolean b(IBpmInstance bpmInstance) {
        return StringUtils.equalsAny(bpmInstance.getStatus(), new CharSequence[]{InstanceStatus.STATUS_END.getKey(), InstanceStatus.STATUS_MANUAL_END.getKey(), InstanceStatus.STATUS_REVOKE.getKey(), InstanceStatus.STATUS_RECOVER.getKey()});
    }

    public void execute(DefaultInstanceActionCmd model) {
        if (!model.getIgnoreAuthentication()) {
            throw new BusinessMessage("操作受限，您没有该操作权限", BpmStatusCode.NO_PERMISSION);
        } else {
            BpmInstance bpmInstance = (BpmInstance)this.g.get(model.getInstanceId());
            if (bpmInstance == null) {
                throw new BusinessMessage("操作流程实例不存在");
            } else if (!this.b(bpmInstance)) {
                throw new BusinessMessage("操作流程实例状态已在运行中");
            } else {
                String destination = model.getDestination();
                bpmInstance.setActInstId((String)null);
                model.setBizDataMap(this.u.getInstanceData((IBusinessPermission)null, bpmInstance));
                model.setBpmInstance(bpmInstance);
                model.setBpmDefinition((IBpmDefinition)this.a.get(bpmInstance.getDefId()));
                QueryFilter queryFilter = new DefaultQueryFilter(true);
                queryFilter.addFilter("inst_id_", bpmInstance.getId(), QueryOP.EQUAL);
                queryFilter.addFilter("action_name_", "end", QueryOP.EQUAL);
                queryFilter.addFieldSort("id_", "desc");
                List<BpmTaskStack> bpmTaskStacks = this.p.query(queryFilter);
                if (CollectionUtil.isEmpty(bpmTaskStacks)) {
                    throw new BusinessException("重启失败，查询执行栈：instId:" + bpmInstance.getId());
                } else {
                    BpmTaskStack bpmTaskStack = (BpmTaskStack)bpmTaskStacks.get(0);
                    bpmTaskStack.setNodeId(bpmInstance.getSuperNodeId());
                    model.setExecutionStack(bpmTaskStack);
                    if (!StringUtils.equals(bpmInstance.getParentInstId(), "0")) {
                        BpmContext.setThreadDynamictaskStack(bpmTaskStack.getNodeId(), bpmTaskStack);
                    }

                    try {
                        BpmContext.setActionModel(model);
                        String actInstId;
                        if (StringUtils.isEmpty(destination)) {
                            actInstId = this.n.startProcessInstance(bpmInstance.getActDefId(), bpmInstance.getBizKey(), model.getActionVariables());
                        } else {
                            actInstId = this.n.startProcessInstance(bpmInstance, model.getActionVariables(), new String[]{destination});
                        }

                        bpmInstance.setActInstId(actInstId);
                        bpmInstance.setStatus(InstanceStatus.STATUS_RUNNING.getKey());
                        bpmInstance.setDuration((Long)null);
                        bpmInstance.setEndTime((Date)null);
                        this.g.update(bpmInstance);
                        queryFilter = new DefaultQueryFilter();
                        queryFilter.addFilter("inst_id_", bpmInstance.getId(), QueryOP.EQUAL);
                        queryFilter.addFilter("status_", Arrays.asList("end", "manualEnd", "start"), QueryOP.IN);
                        queryFilter.addFieldSort("id_", "desc");
                        List<BpmTaskOpinion> opinions = this.o.query(queryFilter);
                        BpmTaskOpinion restartOPinion = (BpmTaskOpinion)opinions.get(0);
                        restartOPinion.setStatus(OpinionStatus.RESTART.getKey());
                        this.o.update(restartOPinion);
                        BpmTaskOpinion reEndOPinion = (BpmTaskOpinion)opinions.get(1);
                        if (StringUtils.equalsAny(reEndOPinion.getStatus(), new CharSequence[]{"end", "manualEnd"})) {
                            reEndOPinion.setStatus(OpinionStatus.RE_END.getKey());
                            this.o.update(reEndOPinion);
                        }
                    } finally {
                        BpmContext.cleanTread();
                    }

                }
            }
        }
    }

    public ActionType getActionType() {
        return ActionType.INSTANCE_RESTART;
    }

    public int getSn() {
        return 9;
    }

    public Boolean isSupport(BpmNodeDef nodeDef) {
        return Boolean.FALSE;
    }

    public Boolean isDefault() {
        return Boolean.TRUE;
    }

    public String getConfigPage() {
        return "/bpm/instance/instanceRestartOpinionDialog.html";
    }

    public String getDefaultGroovyScript() {
        return null;
    }

    public String getDefaultBeforeScript() {
        return null;
    }
}
