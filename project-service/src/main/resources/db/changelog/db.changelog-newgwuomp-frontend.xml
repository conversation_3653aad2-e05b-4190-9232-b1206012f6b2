<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="frontend-common-1" author="liboyang" failOnError="false">
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/frontend-common.sql"/>
        </customChange>
    </changeSet>

    <changeSet id="frontend-page-1" author="liboyang" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/frontend/page/page-sys-tree.sql"/>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/page/PAGE-HEX.sql"/>
        </customChange>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/page/page-AUTH-TEST.sql"/>
        </customChange>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/page/page-COMMON.sql"/>
        </customChange>
    </changeSet>

<!--    <changeSet id="frontend-page-update-1" author="wangkui" failOnError="false">-->
<!--        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">-->
<!--            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/page/PAGE-HEX-UPDATE.sql"/>-->
<!--        </customChange>-->
<!--    </changeSet>-->

    <changeSet id="frontend-layout-1" author="liboyang" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/frontend/layout/layout-sys-tree.sql"/>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/layout/componentManage.sql"/>
        </customChange>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/layout/columnManage.sql"/>
        </customChange>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/layout/portalManage.sql"/>
        </customChange>
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/frontend/layout/availableModuleManage.sql"/>
        </customChange>
    </changeSet>

</databaseChangeLog>