<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="staff-bpm-1-main" author="renjiahao" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-sys-tree.sql"/>
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-bus-table.sql"/>
    </changeSet>
    <changeSet id="staff-bpm-1-base" author="renjiahao" failOnError="false">
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/staff/staff-form.sql"/>
        </customChange>
    </changeSet>

    <changeSet id="staff-bpm-2.1" author="jinghaiyang" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-dialog.sql"/>
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-dict.sql"/>
    </changeSet>

<!--    統一在db.changelog-newgwuomp-frontend.xml初始化 如有修改在各自模塊中-->
<!--    <changeSet id="staff-bpm-4" author="jinghaiyang" failOnError="false">-->
<!--        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">-->
<!--            <param name="sqlFile" value="classpath:/db/changelog/sql/staff/staff-frontend.sql"/>-->
<!--        </customChange>-->
<!--    </changeSet>-->

    <changeSet id="staff-bpm-5" author="renjiahao" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-template.sql"/>
    </changeSet>

    <changeSet id="staff-bpm-6" author="renjiahao" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/staff/staff-serialno.sql"/>
    </changeSet>

    <changeSet id="staff-bpm-7" author="renjiahao" failOnError="false">
        <customChange class="cn.gwssi.ecloud.base.samples.config.LiquibaseHexConvertTask">
            <param name="sqlFile" value="classpath:/db/changelog/sql/staff/staff-bpm.sql"/>
        </customChange>
    </changeSet>

    <changeSet id="staff-bpm-8" author="renjiahao" failOnError="false">
        <sql>INSERT INTO uomp_person_config (ID, CONFIG_INFO, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG, CONFIG_TYPE) VALUES ('886827932417261569', '{"jbxx":"基本信息","jybj":"教育背景","gzbj":"工作背景","jszz":"技术资质","shgx":"社会关系","wfzjl":"无犯罪记录","cgzj":"出国（境）证件","crjjl":"出入境记录"}', '883427513034866689', '2023-08-08 18:21:09', '887411250504925185', '2024-02-29 15:33:25', '0', '1')</sql>
        <sql>INSERT INTO uomp_person_config (ID, CONFIG_INFO, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG, CONFIG_TYPE) VALUES ('890309747048185857', '{"gysxx":"供应商信息","cyxm":"参与项目","gyszz":"供应商资质","pzxx":"配置信息","fj":"附件"}', '887411250504925185', '2023-10-24 14:56:52', '887411250504925185', '2024-02-29 15:33:25', '0', '2')</sql>
    </changeSet>
</databaseChangeLog>
