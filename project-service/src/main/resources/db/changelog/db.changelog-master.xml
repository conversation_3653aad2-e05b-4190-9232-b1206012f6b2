<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <property name="clob.type" value="clob" dbms="oracle,dmsql,kingbase"/>
    <property name="clob.type" value="longtext" dbms="mysql"/>
    <property name="blob.type" value="blob" dbms="oracle,dmsql,kingbase"/>
    <property name="blob.type" value="longblob" dbms="mysql"/>
    <property name="value.colum" value="&quot;value&quot;" dbms="oracle,dmsql,kingbase"/>
    <property name="value.colum" value="`value`" dbms="mysql"/>
    <property name="option.colum" value="&quot;option&quot;" dbms="oracle,dmsql,kingbase"/>
    <property name="option.colum" value="`option`" dbms="mysql"/>
    <property name="name.colum" value="&quot;name&quot;" dbms="oracle,dmsql,kingbase"/>
    <property name="name.colum" value="`name`" dbms="mysql"/>
    <property name="comment.colum" value="&quot;comment&quot;" dbms="oracle,dmsql,kingbase"/>
    <property name="comment.colum" value="comment" dbms="mysql"/>
    <property name="key.colum" value="&quot;key&quot;" dbms="oracle,dmsql,kingbase"/>
    <property name="key.colum" value="key" dbms="mysql"/>
    <property name="groupConcat.colum" value="wm_concat" dbms="oracle,dmsql,kingbase"/>
    <property name="groupConcat.colum" value="group_concat" dbms="mysql"/>

    <!--  1.  初始化平台数据-->
    <!--    ecloud-framework -->
    <include file="classpath:/db/changelog/changes/db.changelog-base.xml"/>
   <!-- ecloud-framework sys -->
    <include file="classpath:/db/changelog/changes/db.changelog-sys.xml"/>
   <!--用户管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-org-custom.xml"/>
   <!--流程BPM-->
    <include file="classpath:/db/changelog/changes/db.changelog-wf-bpm.xml"/>
   <!--安全管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-security.xml"/>
   <!--流程引擎-->
    <include file="classpath:/db/changelog/changes/db.changelog-wf-act.xml"/>
   <!--实体对象管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-bus.xml"/>
   <!--表单管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-form.xml"/>
    <!--API服务初始化-->
    <include file="classpath:/db/changelog/changes/db.changelog-api.xml"/>
    <!--消息初始化-->
    <include file="classpath:/db/changelog/changes/changelog-message.xml"/>

    <!--  2.  初始化模块数据-->
    <!-- dms -->
    <include file="classpath:/db/changelog/changes/changelog-dms-tables.xml"/>
    <include file="classpath:/db/changelog/changes/changelog-dms-init.xml"/>
    <!--cmdb -->
    <include file="classpath:/db/changelog/changes/db.changelog-cmdb.xml"/>
    <include file="classpath:/db/changelog/changes/changelog-cmdb-init.xml"/>
    <!--告警-->
    <include file="classpath:/db.changelog.changes/changelog-monitor.xml"/>
    <include file="classpath:/db.changelog.changes/changelog-monitor-dict.xml"/>
    <!--应用管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-app-e.xml"/>
    <!--大屏管理-->
    <include file="classpath:/db/changelog/changes/db.changelog-visual.xml"/>
    <!--模板管理-->
    <include file="classpath:/db/changelog/changes/changelog-template.xml"/>
    <!-- 前端公共服务-->
    <include file="classpath:/db/changelog/changes/changelog-frontend.xml" />
    <!-- 机器人 -->
<!--    <include file="classpath:/db/changelog/changes/db.changelog-chat.xml"/>-->
<!--    <include file="classpath:/db/changelog/changes/db.changelog-dialog.xml"/>-->
    <!-- LLM -->
<!--    <include file="classpath:/db/changelog/changes/db.changelog-dialog-llm.xml"/>-->
    <!-- 知识管理 -->
<!--    <include file="classpath:/db/changelog/changes/db.changelog-km.xml"/>-->

    <!--  3.  初始化产品数据-->
    <!--运管菜单-->
    <include file="classpath:/db/changelog/db.changelog-newgwuomp-menu.xml"/>
    <!--运管功能-->
    <include file="classpath:/db/changelog/db.changelog-function.xml"/>
    <!--运管角色-->
    <include file="classpath:/db/changelog/db.changelog-role.xml"/>
    <!--运管子系统-->
    <include file="classpath:/db/changelog/db.changelog-subsystem.xml"/>
    <!--运管DMS流程-->
    <include file="classpath:/db/changelog/db.changelog-dms-bpm.xml"/>
    <!--业务设计器-->
    <include file="classpath:/db/changelog/db.changelog-newgwuomp-frontend.xml"/>


    <!--运管ITSM模块基础表-->
    <include file="classpath:/db/changelog/changes/db.changelog-itsm.xml"/>
    <!--运管ITSM模块流程-->
    <include file="classpath:/db/changelog/db.changelog-itsm-bpm.xml"/>

    <include file="classpath:/db/changelog/changes/changelog-staff-pool.xml"/>

    <include file="classpath:/db/changelog/db.changelog-staff-bpm.xml"/>
    <!-- 智能手表-->
    <include file="classpath:/db/changelog/changes/changelog-bracelet.xml" />
    <!-- CMDB模块-->
    <include file="classpath:/db/changelog/db.changelog-cmdb-bpm.xml"/>
<!--    &lt;!&ndash; 监控告警模块&ndash;&gt;-->
    <include file="classpath:/db/changelog/db.changelog-monitor-bpm.xml"/>

    <!-- 大屏-->
    <include file="classpath:/db/changelog/db.changelog-newgwuomp-dp.xml"/>
    <!-- 日程SQL初始化-->
    <include file="classpath:/db/changelog/changes/db.changelog-schedule.xml"/>
<!--    &lt;!&ndash;机器人数据&ndash;&gt;-->
<!--    <include file="classpath:/db/changelog/db.changelog-data-chat.xml"/>-->
<!--    &lt;!&ndash; 知识管理&ndash;&gt;-->
<!--    <include file="classpath:/db/changelog/db.changelog-km-bpm.xml"/>-->

    <!--    运管公共数据-->
    <include file="classpath:/db/changelog/db.changelog-newgwuomp-common.xml"/>

    <!--巡检管理-->
    <include file="classpath:/db/changelog/changes/changelog-inspection.xml"/>


    <!-- 值班排班SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-duty.xml"/>-->
    <!-- 办公用品SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-office.xml"/>-->
    <!-- 考勤管理SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-attendance.xml"/>-->
    <!-- 会议SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-meeting.xml"/>-->
    <!-- 日程SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-schedule.xml"/>-->
    <!-- 项目管理SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-project.xml"/>-->
    <!-- 督查督办SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-supervise.xml"/>-->
    <!-- 新闻SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-news.xml"/>-->
    <!-- 车辆管理SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-vehicle.xml"/>-->
    <!-- 人事管理辆管理SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-human-resource.xml"/>-->
    <!-- 任务管理SQL初始化-->
    <!--    <include file="classpath:/db/changelog/changes/db.changelog-task.xml"/>-->
    <!-- 文档管理初始化-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-doc.xml"/>-->

    <!-- 人员库-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-staff-pool.xml" />-->
    <!-- 人员库-->
    <!--    <include file="classpath:/db/changelog/changes/changelog-monitor.xml" />-->

</databaseChangeLog>