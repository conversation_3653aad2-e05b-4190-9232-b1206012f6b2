<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="newgwuomp-common-2-base" author="xieshi" failOnError="false">
        <sql>
            CREATE OR REPLACE VIEW user_group_org AS  SELECT u.id_ AS  "ID_",u.fullname_ AS  "FULLNAME_",u.account_ AS  "ACCOUNT_",g.group_id_ AS  "ORG_ID",o.name_ AS  "ORG_NAME" FROM org_user AS u INNER  JOIN org_relation AS g ON u.id_ = g.user_id_ INNER  JOIN org_group AS o ON o.id_ = g.group_id_ WHERE  (g.type_ = 'groupUser' AND g.is_master_ = '1' );

            CREATE OR REPLACE VIEW v_user_org_role AS  SELECT tuser.id_ AS  "userId",tuser.fullname_ AS  "userName",tuser.account_ AS  "ACCOUNT",tuser.sn_ AS  "sn",torg.groupid AS  "orgId",torg.groupname AS  "orgName",torg.grouppath AS  "orgPath",trole.roleid AS  "roleId",trole.rolename AS  "roleName",trole.roleCode AS  "roleCode",tpost.postid AS  "postId",tpost.postname AS  "postName",tpost.postcode AS  "postCode",tuser.mobile_ AS  "userMobile" FROM org_user AS tuser LEFT  JOIN ( SELECT relationorg.user_id_ AS  "user_id_",tgroup.name_ AS  "groupname",tgroup.id_ AS  "groupid",tgroup.path_ AS  "grouppath" FROM org_relation AS relationorg INNER  JOIN org_group AS tgroup ON relationorg.group_id_ = tgroup.id_ WHERE relationorg.type_ = 'groupUser') AS torg ON tuser.id_ = torg.user_id_ LEFT  JOIN ( SELECT relationrole.user_id_ AS  "user_id_",${groupConcat.colum}(trole.name_) AS  "rolename",${groupConcat.colum}(trole.id_) AS  "roleid",${groupConcat.colum}(trole.alias_) AS  "roleCode" FROM org_relation AS relationrole INNER  JOIN org_role AS trole ON relationrole.group_id_ = trole.id_ WHERE  (relationrole.type_ = 'userRole' AND trole.alias_ IN ('DMS_ROLE_SUPER_ADMIN', 'DMS_ROLE_GENERAL_USER', 'DMS_ROLE_SECURITY_ADMIN', 'DMS_ROLE_AUDIT_ADMIN', 'DMS_ROLE_SYSTEM_ADMIN') ) GROUP BY relationrole.user_id_) AS trole ON tuser.id_ = trole.user_id_ LEFT  JOIN ( SELECT relationpost.user_id_ AS  "user_id_",${groupConcat.colum}(tpost.name_) AS  "postname",${groupConcat.colum}(tpost.id_) AS  "postid",${groupConcat.colum}(tpost.code_) AS  "postcode" FROM org_relation AS relationpost INNER  JOIN org_post AS tpost ON relationpost.group_id_ = tpost.id_ WHERE  (relationpost.type_ = 'postUser' AND relationpost.is_master_ != '1' ) GROUP BY relationpost.user_id_) AS tpost ON tuser.id_ = tpost.user_id_ WHERE  (tuser.status_ = '1' AND tuser.active_status_ = '1' );

            CREATE OR REPLACE VIEW v_ctrl_config_dict AS  SELECT "SYS_DATA_DICT".NAME_ AS  "NAME_","SYS_DATA_DICT".KEY_ AS  "KEY_" FROM SYS_DATA_DICT WHERE  ("SYS_DATA_DICT".TYPE_ID_ = '881787371070160897' AND "SYS_DATA_DICT".DELETE_FLAG_ = '0' AND "SYS_DATA_DICT".DICT_TYPE_ = 'dict' ) UNION  ALL  SELECT "FORM_CUST_DIALOG".NAME_ AS  "NAME_","FORM_CUST_DIALOG".KEY_ AS  "KEY_" FROM FORM_CUST_DIALOG WHERE "FORM_CUST_DIALOG".TYPE_ID_ = '884093110759981057';
        </sql>
    </changeSet>
    <changeSet id="newgwuomp-common-3" author="renjiahao" failOnError="false">
        <createTable tableName="iframe_form" remarks="表单自定义标签">
            <column name="ID" type="varchar(64)" remarks="主键ID">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="IFRAMESRC" type="${blob.type}" remarks="申请表ID"/>
            <column name="YMCSHXX" type="varchar(64)" remarks="页面初始化信息"/>
            <column name="LZSJ" type="varchar(64)" remarks="流转数据"/>
            <column name="BZ_ID" type="varchar(64)" remarks="业务id"/>
            <column name="BZ_MESSAGE" type="varchar(64)" remarks="业务数据"/>
            <column name="STATUS" type="varchar(64)" remarks="状态"/>
        </createTable>
    </changeSet>

    <changeSet id="newgwuomp-common-4" author="renjiahao" failOnError="false">
        <sqlFile path="classpath:/db/changelog/sql/iframe-common.sql"/>
    </changeSet>

    <changeSet id="message-cust" author="renjiahao" failOnError="false">
        <addColumn tableName="app_station_letter" >
            <column name="alert_level" type="varchar(4)" remarks="告警等级"/>
        </addColumn>
        <addColumn tableName="app_station_letter" >
            <column name="order_level" type="varchar(4)" remarks="工单级别"/>
        </addColumn>
        <addColumn tableName="app_station_letter" >
            <column name="flow_type" type="varchar(64)" remarks="工单类型"/>
        </addColumn>
        <addColumn tableName="app_station_letter" >
            <column name="is_vip" type="varchar(2)" remarks="工单VIP"/>
        </addColumn>
    </changeSet>

    <changeSet id="newgwuomp-common-5" author="renjiahao" failOnError="false">
        <sql>INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('881784191580635137', 'G_FORM_UOMP', '统一运维管理平台', '', '6', '0', '881784191580635137.', 37, '', '2023-04-19 10:04:32', '881565440330956801', NULL)</sql>
        <sql>INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('881783503337291777', 'GWUOMP', '统一运维管理平台', '', '9', '0', '881783503337291777.', 35, NULL, '2023-04-19 09:42:40', '881565440330956801', NULL)</sql>
        <sql>INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('881783575638704129', 'GWUOMP', '统一运维管理平台', '', '5', '0', '881783575638704129.', 36, NULL, '2023-04-19 09:44:57', '881565440330956801', NULL)</sql>
    </changeSet>



    <changeSet id="newgwuomp-common-6" author="renjiahao" failOnError="false">
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868671491014657', NULL, 'APPLY_FLOW_STATUS', '申请历史_流程状态', 'APPLY_FLOW_STATUS', '881787371070160897', 0, 'dict', '0', '2024-06-13 17:23:49', '884277011806158849', '2024-08-09 16:50:02', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868680766717953', '900868671491014657', '  ', '全部', 'APPLY_FLOW_STATUS', '881787371070160897', 0, 'node', '0', '2024-06-13 17:24:07', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868691072122881', '900868671491014657', 'running', '运行中', 'APPLY_FLOW_STATUS', '881787371070160897', 1, 'node', '0', '2024-06-13 17:24:26', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868700746285057', '900868671491014657', 'draft', '草稿', 'APPLY_FLOW_STATUS', '881787371070160897', 2, 'node', '0', '2024-06-13 17:24:45', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868710650085377', '900868671491014657', 'end', '终止', 'APPLY_FLOW_STATUS', '881787371070160897', 3, 'node', '0', '2024-06-13 17:25:04', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868723279134721', '900868671491014657', 'manualend', '人工终止', 'APPLY_FLOW_STATUS', '881787371070160897', 4, 'node', '0', '2024-06-13 17:25:28', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868733841965057', '900868671491014657', 'back', '驳回', 'APPLY_FLOW_STATUS', '881787371070160897', 5, 'node', '0', '2024-06-13 17:25:48', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
        <sql>INSERT INTO sys_data_dict (id_, parent_id_, key_, name_, dict_key_, type_id_, sn_, dict_type_, delete_flag_, create_time_, create_by_, update_time_, update_by_) VALUES ('900868744797487105', '900868671491014657', 'revoker', '撤销', 'APPLY_FLOW_STATUS', '881787371070160897', 6, 'node', '0', '2024-06-13 17:26:09', '884277011806158849', '2024-08-09 16:50:06', '888412408706498561')</sql>
    </changeSet>

    <changeSet id="newgwuomp-common-7" author="liboyang" failOnError="false">
        <sql>update sys_tree a set type_id_ = '1_0' where not exists (select 1 from sys_tree_node b where a.type_id_ = b.id_ )</sql>
        <sql>update sys_properties a set group_ = '3_0' where not exists (select 1 from sys_tree_node b where a.group_ = b.id_ )</sql>
    </changeSet>

</databaseChangeLog>