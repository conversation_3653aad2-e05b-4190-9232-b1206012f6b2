<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="role-1-base" author="weipengxiang" failOnError="false">
        <!-- 初始化数据 -->
        <sql>
            INSERT INTO ORG_USER(ID_, FULLNAME_, ACCOUNT_, PASSWORD_, EMAIL_, MOBILE_, WEIXIN_, ADDRESS_, PHOTO_, SEX_, FROM_, STATUS_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_, SN_, TELEPHONE_, ACTIVE_STATUS_, SECRET_LEVEL_, TYPE_, DESC_) VALUES ('2', '默认用户', 'user', '66a0037150a4c28d0291c14889a352bd36b7d26b3b699697970d4808d129d93c', '', '', 'test', '', '', '0', 'system', 1, '2022-02-08 12:00:00', 1, '2022-02-08 12:00:00', '1', 2000, '', 1, null, '1', null);
            INSERT INTO ORG_GROUP(ID_, NAME_, PARENT_ID_, SN_, CODE_, TYPE_, DESC_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_, PATH_, SIMPLE_) VALUES ('1', '默认机构', '0', 1, '1', '1', 'eCloudBPM', null, null, '2022-02-08 12:00:00', '1', '1', '');

            INSERT INTO ORG_RELATION(ID_, GROUP_ID_, USER_ID_, IS_MASTER_, STATUS_, TYPE_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_, SN_, HAS_CHILD_) VALUES ('1', '1', '2', 1, 1, 'groupUser', '2022-02-08 12:00:00', '1', '2022-02-08 12:00:00', '1', null, null);
            INSERT INTO ORG_RELATION(ID_, GROUP_ID_, USER_ID_, IS_MASTER_, STATUS_, TYPE_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_, SN_, HAS_CHILD_) VALUES ('2', '1', '2', 1, 1, 'userRole', '2022-02-08 12:00:00', '1', '2022-02-08 12:00:00', '1', null, null);

            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('1', '运管平台基础用户', 'G_ROLE_GENERAL_USER', 1, '运管平台非授权功能，构建用户，必须有该角色', '2022-02-08 12:00:00', '1', '2024-02-02 16:19:15', '881705974336847873', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('2', '系统管理员', 'G_ROLE_ADMIN', 1, '系统管理员：管理系统菜单、账户、流程设置等', '2022-02-08 12:00:00', '1', '2024-06-27 11:03:04', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('883922336350208001', '配置工程师', 'CMDB_ENGINEER', 1, '配置工程师可进行配置模型设计、配置模型提审、配置变更提审、配置模型发布；配置信息入库录入、配置信息入库的提审', NULL, NULL, '2024-06-27 11:27:29', '900401038581825537', '881706042616446977', '', NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('883922348374228993', '配置主管', 'CMDB_MANAGER', 1, '配置主管可进行配置模型审核、配置信息入库的审核', NULL, NULL, NULL, '884051084090277889', '881706042616446977', '', NULL, NULL);
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('883922355141214209', '配置运维人员', 'CMDB_DEVOPS', 1, '运维人员可对分配权限的配置信息进行维护', NULL, NULL, NULL, '884051084090277889', '881706042616446977', '', NULL, NULL);
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('884013230218280961', '人员管理管理员', 'G_ROLE_PEOPLE_ADMIN', 0, '', '2023-06-07 15:03:46', '883427513034866689', '2023-06-07 15:03:46', '884051084090277889', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('884051820522504193', '总包商运维经理', 'G_ROLE_MANAGER', 1, '总包的负责人，运维事项的整体负责人', '2023-06-08 11:30:31', '882462575167012865', '2024-06-27 11:25:14', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('884232756371390465', 'DMS_超级管理员', 'DMS_ROLE_SUPER_ADMIN', 1, '数据运维（DMS）模块的超级管理员角色，拥有最高权限', '2023-06-12 11:22:19', '884051084090277889', '2024-03-25 11:56:13', '884232918960963585', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('884232809161424897', 'DMS_普通用户', 'DMS_ROLE_GENERAL_USER', 1, '数据运维（DMS）模块的普通用户角色；与系统默认的普通用户有区别，该角色仅限于数据运维模块的部分功能、资源权限。', '2023-06-12 11:24:00', '884051084090277889', '2024-03-25 11:46:23', '884232918960963585', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('884232831414829057', 'DMS_安全员', 'DMS_ROLE_SECURITY_ADMIN', 1, '数据运维（DMS）模块的安全管理员角色', '2023-06-12 11:24:42', '884051084090277889', '2024-03-25 11:59:41', '884232918960963585', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('885999057825169409', '运维人员', 'YWRY_ROLE', 0, '普通运维人员基础权限', '2023-07-21 11:11:32', '881842054313803777', '2023-11-14 14:49:28', '884051084090277889', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('887411228960358401', '安全管理员', 'G_SEC_ADMIN', 1, '用户授权', '2023-08-21 15:23:14', '882462575167012865', '2023-11-14 16:15:24', '881842054313803777', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('887500730465255425', '甲方运维负责人', 'G_ROLE_CUSTOMER_LEADER', 1, '甲方运管领导可对人员相关流程进行审批、总管备品备件', '2023-08-23 14:48:25', '883787046541328385', '2024-06-15 15:50:30', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('888224604349792257', 'JumpServer用户', 'JS_ROLE_USER', 1, 'JumpServer用户', '2023-09-08 14:19:45', '881706042616446977', '2023-09-08 14:19:45', '881706042616446977', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('888726289785028609', 'CICD管理员', 'CICD_MANAGER', 1, '拥有配置流水线、编辑流水线、删除JOB、分配用户、环境配置的权限', '2023-09-19 16:07:54', '881705933694566401', '2023-09-19 16:08:44', '881705933694566401', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('897234671196176385', 'DMS_审计员', 'DMS_ROLE_AUDIT_ADMIN', 1, '数据运维（DMS）模块的审计管理员角色', '2024-03-25 12:02:03', '884232918960963585', '2024-03-25 12:06:28', '884232918960963585', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('897234755072819201', 'DMS_管理员', 'DMS_ROLE_SYSTEM_ADMIN', 1, '数据运维（DMS）模块的系统管理员角色', '2024-03-25 12:04:43', '884232918960963585', '2024-03-25 12:04:43', '884232918960963585', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('9', '分权管理员', 'G_ROLE_DEC_ADMIN', 0, 'sa-root用户可以分权给对应分权管理员，维护所在机构的角色、岗位、人员、流程等功能', '2022-02-08 12:00:00', '1', '2023-08-16 10:25:17', '881705974336847873', NULL, NULL, NULL, NULL);
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('900186058662608897', '帮助台', 'ITSM_HELP', 1, '一线运维人员', '2024-05-29 15:44:08', '899640573259415553', '2024-06-27 11:11:28', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('900186068634042369', '服务台', 'ITSM_SERVICE', 1, '二线运维人员', '2024-05-29 15:44:27', '899640573259415553', '2024-06-27 11:11:39', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('900368637632708609', 'ITSM普通用户', 'ITSM_NORMAL', 1, '非运维组织的普通用户（OA用户）--该类用户，仅在自助服务门户发起请求、跟踪和进行服务评价', '2024-06-02 16:28:10', '887493067513790465', '2024-06-27 11:10:28', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('900556547660185601', '甲方运维领导', 'JFYWLD', 0, '暂时未使用，人员和工单为使用，使用的，【甲方运维负责人】', '2024-06-06 20:01:40', '887493067513790465', '2024-06-17 14:13:22', '884051084090277889', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('900956661824880641', '服务商运维负责人', 'G_ROLE_SEVICEMANAGER', 1, '运维服务商的负责人', '2024-06-15 16:00:57', '900401038581825537', '2024-06-27 11:11:57', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('901081518958968833', '运维组长', 'ITSM_GROUP LEADER', 1, '运维小组的负责人，如：应用组组长', '2024-06-18 10:10:03', '887493067513790465', '2024-06-27 11:12:21', '900401038581825537', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('907162370408185857', '文昌管理员', 'G_ROLE_ADMIN_WENCHANG', 1, '维护文昌RAG', '2024-10-30 15:55:07', '900176461248856065', '2024-10-30 15:55:07', '900176461248856065', NULL, NULL, NULL, '');
            INSERT INTO org_role (id_, name_, alias_, enabled_, description, create_time_, create_by_, update_time_, update_by_, type_id_, type_name_, sn_, org_id_) VALUES ('909642054464700417', '安全监管员', 'G_ROLE_SAFETY', 1, '对数据运维进行监管', '2024-12-24 09:42:09', '904398989459718145', '2024-12-24 09:43:55', '904398989459718145', NULL, NULL, NULL, '');

            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('884095359724290049', '运维经理', 'UOMP_MANAGER', '2', '用于人员信息审核、人员账号申请、人员入场申请、人员退场申请、临时入场申请。', NULL, '2023-06-09 10:34:36', '882462575167012865', '2023-06-16 13:34:39', '883787046541328385', '', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('884095374637137921', '甲方负责人', 'UOMP_PARTY_A', '2', '用于人员入场申请、临时入场申请、退场申请。', NULL, '2023-06-09 10:35:04', '882462575167012865', '2023-06-16 11:19:51', '883787046541328385', '', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('884880188868919297', '问题主管', 'wtzg', '2', '负责问题工单级别变更审核', NULL, '2023-06-26 18:23:38', '881565440330956801', '2023-07-07 14:47:26', '881565440330956801', '2', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('884908043845763073', '变更主管', 'bgzg', '2', '', NULL, '2023-06-27 09:09:08', '881565440330956801', '2023-07-07 14:47:23', '881565440330956801', '2', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('884920706381905921', '容量主管', 'rlzg', '2', '', NULL, '2023-06-27 15:51:39', '881565440330956801', '2023-07-07 14:47:20', '881565440330956801', '2', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('885331638346579969', '运维组长', 'ywzz', '2', '', NULL, '2023-07-06 17:34:50', '882463804894478337', '2023-07-06 17:34:50', '882463804894478337', '1', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('886766925433012225', '发布主管', 'fbzg', '2', '', NULL, '2023-08-07 10:01:23', '881705933694566401', '2023-08-07 10:01:23', '881705933694566401', '2', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('900131869694623745', '帮助台（一线）', 'ITSM_HELP', '2', '', NULL, '2024-05-28 11:01:31', '899640573259415553', '2024-05-28 11:56:15', '887493067513790465', '4', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('900133575387512833', '服务台（二线）', 'ITSM_SERVICE', '2', '', NULL, '2024-05-28 11:55:44', '887493067513790465', '2024-05-28 11:55:44', '887493067513790465', '4', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('900510993842962433', '甲方运维领导', 'ITSM_A_LEADER', '2', '', NULL, '2024-06-05 19:53:33', '899640573259415553', '2024-06-05 19:53:33', '899640573259415553', '', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('908478927901032449', '配置主管', 'pzzg', '2', '', NULL, '2024-11-28 17:27:21', '899905408480575489', '2024-11-28 17:27:21', '899905408480575489', '1', '');
            INSERT INTO org_post (id_, name_, code_, is_civil_servant_, desc_, level_, create_time_, create_by_, update_time_, update_by_, type_, org_id_) VALUES ('910291931129446401', '业务司局审批负责人', 'ywsjspfzr', '2', '数据运维申请权限时要用到，勿删', NULL, '2025-01-07 18:01:10', '910052541297852417', '2025-01-08 10:12:45', '910052541297852417', '4', '');

        </sql>
    </changeSet>
</databaseChangeLog>