INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('882793612345606145', 'htxm', '合同项目列表', '新增合同页面中的合同项目', NULL, 'list', 'dataSourceDefault', '', 'table', 'UOMP_PROJECT_MANAGEMENT', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"PROJECT_CODE","formatter":"","showName":"项目编号"},{"columnName":"PROJECT_NAME","formatter":"","showName":"项目名称"},{"columnName":"MANGER_NAME","formatter":"","showName":"项目经理名称"},{"columnName":"PROJECT_STATUS","formatter":"","showName":"状态"}]', '[{"columnName":"PROJECT_CODE","condition":"EQ","dbType":"varchar","showName":"项目编号","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_NAME","condition":"EQ","dbType":"varchar","showName":"项目名称","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_STATUS","showName":"状态","dbType":"varchar","condition":"NE","value":{"ctrlType":"inputText","text":"1"},"valueSource":"fixedValue"}]', '[{"columnName":"ID","returnName":"ID"},{"columnName":"PROJECT_CODE","returnName":"PROJECT_CODE"},{"columnName":"PROJECT_NAME","returnName":"PROJECT_NAME"}]', '[{"columnName":"PROJECT_STATUS","sortType":"asc"}]', 'database', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"htxm","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"ID"}', '882793436525625345', '2023-05-11 16:53:10', '882463815557971969', '2023-05-24 09:34:50', '882463815557971969');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('883733392089153537', 'htxmlb_new', '合同项目列表', '新增合同页面中的合同项目', NULL, 'list', 'dataSourceDefault', '', 'view', 'V_UOMP_PROJECT_MANAGEMENT', 1, 10, 1000, 746, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"PROJECT_CODE","formatter":"","showName":"项目编号"},{"columnName":"PROJECT_NAME","formatter":"","showName":"项目名称"},{"columnName":"MANGER_NAME","formatter":"","showName":"项目经理名称"},{"columnName":"PROJECT_STATUS_STR","formatter":"","showName":"状态"}]', '[{"columnName":"PROJECT_CODE","condition":"EQ","dbType":"varchar","showName":"项目编号","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_NAME","condition":"EQ","dbType":"varchar","showName":"项目名称","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_STATUS_STR","condition":"NE","dbType":"varchar","showName":"状态","value":{"ctrlType":"inputText","text":"未启动"},"valueSource":"fixedValue"}]', '[{"columnName":"ID","returnName":"ID"},{"columnName":"PROJECT_CODE","returnName":"PROJECT_CODE"},{"columnName":"PROJECT_NAME","returnName":"PROJECT_NAME"}]', '[{"columnName":"PROJECT_STATUS","sortType":"asc"}]', 'database', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"htxmlb_new","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""}', '882793436525625345', '2023-06-01 10:47:57', '882463804894478337', '2023-12-22 14:57:21', '882510075765719041');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('887277725721034753', 'uomp_supplier', '服务商', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'b93078fc9cf641f49d4cb69bee304bfe', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"supplierName","formatter":"","showName":"服务商名称"},{"columnName":"creditCode","formatter":"","showName":"社会代码"},{"columnName":"shortName","formatter":"","showName":"简称"}]', '[{"columnName":"supplierName","condition":"EQ","dbType":"String","showName":"服务商名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"supplierName","returnName":"name"},{"columnName":"id","returnName":"id"},{"columnName":"respName","returnName":"respName"},{"columnName":"respTel","returnName":"respTel"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_supplier","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/supplier/getList_dialog(供应商列表查询_自定义对话框)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/supplier/getList_dialog","method":"post-form","name":"供应商列表查询_自定义对话框","desc":""},"apiReqColumns":[{"name":"supplierName","comment":"服务商名称","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"id","type":"String","required":false,"primary":false},{"name":"supplierName","comment":"服务商名称","type":"String","required":false,"primary":false},{"name":"shortName","comment":"简称","type":"String","required":false,"primary":false},{"name":"creditCode","comment":"社会代码","type":"String","required":false,"primary":false},{"name":"respName","comment":"负责人","type":"String","required":false,"primary":false},{"name":"respTel","comment":"负责人电话","type":"String","required":false,"primary":false},{"name":"tel","comment":"联系电话","type":"String","required":false,"primary":false}]}}', '884093110759981057', '2023-08-18 16:39:17', '881565459776798721', '2024-06-20 11:09:49', '895971860662976513');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('887277768341454849', 'uomp_contract', '合同', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'bfc39d106757426789b43a191a3988f0', 1, 10, 1000, 815, 0, 0, '{"showFilter":false,"checkStrictly":false}', '[{"columnName":"contract_code","showName":"编码","formatter":""},{"columnName":"contract_name","showName":"名称","formatter":""},{"columnName":"signing_date","showName":"签订日期","formatter":""}]', '[{"columnName":"contract_name","showName":"名称","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"contract_name","returnName":"name"},{"columnName":"id","returnName":"id"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_contract","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/contract/getContractList_dialog(合同管理查询列表接口_对话框)","primaryKey":""}', '884093110759981057', '2023-08-18 16:40:38', '881565459776798721', '2023-08-18 16:40:38', '881565459776798721');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('892982166708289537', 'htxmlb_mult', '合同项目列表', '新增合同页面中的合同项目-多选', NULL, 'list', 'dataSourceDefault', '', 'view', 'V_UOMP_PROJECT_MANAGEMENT', 1, 10, 1000, 746, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"PROJECT_CODE","formatter":"","showName":"项目编号"},{"columnName":"PROJECT_NAME","formatter":"","showName":"项目名称"},{"columnName":"MANGER_NAME","formatter":"","showName":"项目经理名称"},{"columnName":"PROJECT_STATUS_STR","formatter":"","showName":"状态"}]', '[{"columnName":"PROJECT_CODE","condition":"EQ","dbType":"varchar","showName":"项目编号","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_NAME","condition":"EQ","dbType":"varchar","showName":"项目名称","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"PROJECT_STATUS_STR","condition":"NE","dbType":"varchar","showName":"状态","value":{"ctrlType":"inputText","text":"未启动"},"valueSource":"fixedValue"}]', '[{"columnName":"ID","returnName":"ID"},{"columnName":"PROJECT_CODE","returnName":"PROJECT_CODE"},{"columnName":"PROJECT_NAME","returnName":"PROJECT_NAME"}]', '[{"columnName":"PROJECT_STATUS","sortType":"asc"}]', 'database', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"htxmlb_mult","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""}', '882793436525625345', '2023-12-22 14:58:35', '882510075765719041', '2023-12-22 14:59:42', '882510075765719041');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899915441879121921', 'uomp_project_mult_people', '应用系统', '入场申请-应用系统', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"projectName","formatter":"","showName":"名称"},{"columnName":"departName","showName":"主责部门","formatter":""}]', '[{"columnName":"projectName","condition":"EQ","dbType":"String","showName":"名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"supplierId","condition":"EQ","dbType":"String","showName":"供应商id","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"projectName","returnName":"name"},{"columnName":"departName","returnName":"departName"},{"columnName":"departId","returnName":"departId"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_project_mult_people","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/entryApply/getProjectBySupplier(入场申请-根据供应商收敛查询项目列表)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/entryApply/getProjectBySupplier","method":"post-json","name":"入场申请-根据供应商收敛查询项目列表","desc":"入场申请-根据供应商收敛查询项目列表"},"apiReqColumns":[{"name":"pageNo","comment":"","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"","type":"Number","required":false,"primary":false},{"name":"projectName","comment":"项目名称","type":"String","required":false,"primary":false},{"name":"supplierId","comment":"供应商id","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"主键","type":"String","required":false,"primary":false},{"name":"projectCode","comment":"编码","type":"String","required":false,"primary":false},{"name":"projectName","comment":"名称","type":"String","required":false,"primary":false},{"name":"projectStage","comment":"阶段","type":"String","required":false,"primary":false},{"name":"projectStatus","comment":"状态","type":"String","required":false,"primary":false},{"name":"departId","comment":"主责部门","type":"String","required":false,"primary":false},{"name":"departName","comment":"主责部门","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-23 16:21:28', '899640318233673729', '2024-06-12 14:26:07', '899640318233673729');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('900002388253343745', 'newgwuomp_ system_list', '应用系统选择', '合同管理-获取应用系统列表', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 746, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"applicationSystemName","formatter":"","showName":"应用系统名称"},{"columnName":"supplierName","formatter":"","showName":"服务商"}]', '[{"columnName":"applicationSystemName","condition":"EQ","dbType":"String","showName":"应用系统名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"applicationSystemName","returnName":"applicationSystemName"},{"columnName":"id","returnName":"id"},{"columnName":"supplierName","returnName":"supplierName"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"newgwuomp_ system_list","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/system/getSystemList(应用系统列表)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/system/getSystemList","method":"post-form","name":"应用系统列表","desc":"获取应用系统列表"},"apiReqColumns":[{"name":"pageNo","comment":"页码","type":"String","required":false,"primary":false},{"name":"pageSize","comment":"每页数量","type":"String","required":false,"primary":false},{"name":"applicationSystemName","comment":"应用系统名称","type":"String","required":false,"primary":false},{"name":"departName","comment":"运维部门名称","type":"String","required":false,"primary":false},{"name":"systemStatus","comment":"系统状态，逗号隔开","type":"String","required":false,"primary":false},{"name":"principalName","comment":"负责人名称","type":"String","required":false,"primary":false}],"columns":[{"name":"applicationSystemName","comment":"应用系统名称","type":"String","required":false,"primary":false},{"name":"departName","comment":"运维部门名称","type":"String","required":false,"primary":false},{"name":"id","comment":"","type":"String","required":false,"primary":false},{"name":"isHeart","comment":"是否核心应用（1：是   0：否）","type":"String","required":false,"primary":false},{"name":"onlineTime","comment":"上线时间","type":"String","required":false,"primary":false},{"name":"supplierName","comment":"服务商","type":"String","required":false,"primary":false},{"name":"systemStatus","comment":"状态（0：使用中  1已停用）","type":"String","required":false,"primary":false},{"name":"principalId","comment":"负责人名称","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-25 14:25:25', '899640318233673729', '2024-05-28 14:57:40', '883016099668951041');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('900550361191284737', 'ywzzjgdx', '运维组织机构', '', NULL, 'tree', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"id":"id","pid":"parentId","pidInitVal":"","pidInitValScript":false,"showColumn":"groupName","showFilter":true}', '[]', '[]', '[{"columnName":"id","returnName":"id"},{"columnName":"name","returnName":"name"},{"columnName":"orgGroupId","returnName":"orgGroupId"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"ywzzjgdx","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/org/group/getOrgTreePerson(获取运维组织机构树)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/org/group/getOrgTreePerson","method":"get","name":"获取运维组织机构树","desc":""},"apiReqColumns":[],"columns":[{"name":"name","comment":"运维组名称","type":"String","required":false,"primary":false},{"name":"id","comment":"运维组ID","type":"String","required":false,"primary":false},{"name":"parentId","comment":"父ID","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"orgGroupId","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-06-06 16:45:00', '883016099668951041', '2025-01-20 14:57:22', '910290324114898945');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('901407979943755777', 'uomp_qualified_personnel', '人员选择', '从人员信息库选择审批通过，背调非不合格，不在黑名单的数据。', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"personName","formatter":"","showName":"用户名"},{"columnName":"orgGroupName","formatter":"","showName":"系统运维组"},{"columnName":"workingCompany","showName":"就职公司","formatter":""}]', '[]', '[{"columnName":"id","returnName":"id"},{"columnName":"personName","returnName":"name"},{"columnName":"orgGroupId","returnName":"orgGroupId"},{"columnName":"sex","returnName":"sex"},{"columnName":"mobile","returnName":"mobile"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_qualified_personnel","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/account/noAccountPeronList(添加账号人员选择列表)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/account/noAccountPeronList","method":"get","name":"添加账号人员选择列表","desc":""},"apiReqColumns":[],"columns":[{"name":"id","comment":"用户ID","type":"String","required":false,"primary":false},{"name":"personName","comment":"用户名","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"系统运维组ID","type":"String","required":false,"primary":false},{"name":"sex","comment":"性别","type":"String","required":false,"primary":false},{"name":"mobile","comment":"手机","type":"String","required":false,"primary":false},{"name":"workingCompany","comment":"就职公司","type":"String","required":false,"primary":false},{"name":"orgGroupName","comment":"系统运维组","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-06-25 15:07:58', '883016099668951041', '2024-12-10 17:32:15', '882462575167012865');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('901488437779496961', 'uomp_select_position', '选择岗位', '通过系统用户id筛选未被添加的岗位', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"岗位名称"},{"columnName":"desc","formatter":"","showName":"岗位描述"}]', '[{"columnName":"orgUserId","condition":"EQ","dbType":"String","showName":"系统用户id","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"postName","condition":"EQ","dbType":"String","showName":"岗位名称","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"name","returnName":"name"},{"columnName":"desc","returnName":"desc"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_select_position","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/account/noPostList(岗位列表接口)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/account/noPostList","method":"get","name":"岗位列表接口","desc":""},"apiReqColumns":[{"name":"orgUserId","comment":"系统用户id","type":"String","required":false,"primary":false},{"name":"postName","comment":"岗位名称","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"岗位ID","type":"String","required":false,"primary":false},{"name":"name","comment":"岗位名称","type":"String","required":false,"primary":false},{"name":"desc","comment":"岗位描述","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-06-27 09:45:39', '883016099668951041', '2024-12-24 10:38:42', '882462575167012865');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('903945354653204481', 'gwuomp_address_book_selection', '人员选择', '通讯录人员选择（数据与通讯录一致）', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"personName","showName":"姓名","formatter":""}]', '[{"columnName":"orgGroupId","showName":"运维组id","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"groupId"},"valueSource":"primaryTableColumn"},{"columnName":"personName","showName":"姓名","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"personName","returnName":"personName"},{"columnName":"tel","returnName":"tel"},{"columnName":"entryStatus","returnName":"entryStatus"},{"columnName":"orgGroupName","returnName":"orgGroupName"},{"columnName":"id","returnName":"id"},{"columnName":"serviceLocation","returnName":"serviceLocation"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"custApi","displayFields":[{"columnName":"id","formatter":"","showName":"id"},{"columnName":"parentId","formatter":"","showName":"父ID"},{"columnName":"name","formatter":"","showName":"运维组名"}],"dsKey":"dataSourceDefault","height":0,"key":"gwuomp_address_book_selection","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/org/group/getOrgTreePerson(获取运维组织)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/org/group/getOrgTreePerson","method":"get","name":"获取运维组织","desc":""},"apiReqColumns":[],"columns":[{"name":"name","comment":"运维组名","type":"String","required":false,"primary":false},{"name":"groupId","comment":"运维组ID","type":"String","required":false,"primary":false},{"name":"children","comment":"子数据","type":"String","required":false,"primary":false},{"name":"id","comment":"id","type":"String","required":false,"primary":false},{"name":"parentId","comment":"父ID","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"orgGroupId","type":"String","required":false,"primary":false}]}},"page":true,"pageSize":10,"primaryTablePercent":40,"returnFields":[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"},{"columnName":"orgGroupId","returnName":"orgGroupId"}],"sortFields":[],"style":"tree","system":false,"treeConfig":{"checkStrictly":false,"id":"id","pid":"parentId","pidInitValScript":false,"showColumn":"name","showFilter":false},"width":0}', 'doubleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/contacts/getContactsList(通讯录)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/contacts/getContactsList","method":"post-form","name":"通讯录","desc":""},"apiReqColumns":[{"name":"orgGroupId","comment":"运维组id","type":"String","required":false,"primary":false},{"name":"personName","comment":"人员名字","type":"String","required":false,"primary":false}],"columns":[{"name":"personName","comment":"人员名字","type":"String","required":false,"primary":false},{"name":"tel","comment":"联系方式","type":"String","required":false,"primary":false},{"name":"entryStatus","comment":"驻场状态","type":"String","required":false,"primary":false},{"name":"orgGroupName","comment":"运维组名称","type":"String","required":false,"primary":false},{"name":"id","comment":"id","type":"String","required":false,"primary":false},{"name":"serviceLocation","comment":"驻场地点","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-08-20 15:28:56', '883016099668951041', '2024-08-20 15:33:08', '883016099668951041');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('907250164581269505', 'newgwuomp_system_list_new', '应用系统', '入场申请-应用系统-CMDB', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"ci_name","formatter":"","showName":"名称"},{"columnName":"responsible_org","formatter":"","showName":"主责部门"}]', '[{"columnName":"attributeName","condition":"EQ","dbType":"String","showName":"参数名","value":{"ctrlType":"inputText","text":"ci_name"},"valueSource":"defaultValue"},{"columnName":"attributeValue","condition":"EQ","dbType":"String","showName":"名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"pageSize","condition":"EQ","dbType":"String","showName":"页面数量","value":{"ctrlType":"inputText","text":"10"},"valueSource":"defaultValue"}]', '[{"columnName":"ci_name","returnName":"name"},{"columnName":"responsible_org","returnName":"departName"},{"columnName":"id","returnName":"id"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"newgwuomp_system_list_new","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false(入场申请-应用系统-CMDB)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false","method":"get","name":"入场申请-应用系统-CMDB","desc":"入场申请-应用系统-CMDB"},"apiReqColumns":[{"name":"attributeName","comment":"参数名","type":"String","required":false,"primary":false},{"name":"attributeValue","comment":"参数值","type":"String","required":false,"primary":false},{"name":"pageSize","comment":"页面数量","type":"String","required":false,"primary":false}],"columns":[{"name":"ci_name","comment":"资源名称","type":"String","required":false,"primary":false},{"name":"id","comment":"id","type":"String","required":false,"primary":false},{"name":"responsible_org","comment":"主责部门","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-11-01 14:26:01', '883016099668951041', '2024-11-22 09:59:44', '883016099668951041');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899767898882965505', 'G_POST_RY', '岗位', '账户申请-选择岗位', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"名称"},{"columnName":"desc","formatter":"","showName":"描述"}]', '[{"columnName":"name","condition":"EQ","dbType":"String","showName":"名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"name","returnName":"name"},{"columnName":"desc","returnName":"desc"},{"columnName":"id","returnName":"id"},{"columnName":"code","returnName":"code"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"G_POST_RY","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/account/getPostByOrgType(账号申请-查询所有岗位)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/account/getPostByOrgType","method":"post-form","name":"账号申请-查询所有岗位","desc":"查询所有岗位（带机构类型权限）"},"apiReqColumns":[{"name":"pageNo","comment":"","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"","type":"Number","required":false,"primary":false},{"name":"name","comment":"名称","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"主键","type":"String","required":false,"primary":false},{"name":"code","comment":"编码","type":"String","required":false,"primary":false},{"name":"name","comment":"名称","type":"String","required":false,"primary":false},{"name":"desc","comment":"描述","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-20 10:11:12', '899640318233673729', '2024-05-22 14:09:24', '899640318233673729');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899767451222802433', 'uomp_role_ry', '角色', '账户申请-选择角色', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"名称"},{"columnName":"desc","formatter":"","showName":"描述"}]', '[{"columnName":"name","condition":"EQ","dbType":"String","showName":"名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"name","returnName":"name"},{"columnName":"desc","returnName":"desc"},{"columnName":"id","returnName":"id"},{"columnName":"alias","returnName":"alias"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_role_ry","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/account/getRoleByOrgType(账号申请-查询所有角色)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/account/getRoleByOrgType","method":"post-form","name":"账号申请-查询所有角色","desc":"查询所有角色（带机构类型权限）"},"apiReqColumns":[{"name":"pageNo","comment":"","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"","type":"Number","required":false,"primary":false},{"name":"name","comment":"名称","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"主键","type":"String","required":false,"primary":false},{"name":"alias","comment":"别名","type":"String","required":false,"primary":false},{"name":"name","comment":"名称","type":"String","required":false,"primary":false},{"name":"desc","comment":"描述","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-20 09:56:58', '899640318233673729', '2024-06-11 14:59:12', '899640318233673729');
INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899633978958938113', 'gwuomp_account_apply', '选择账号申请人员', '账户申请-选择账号申请人员', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 900, 600, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"personName","formatter":"","showName":"姓名"},{"columnName":"workingCompany","formatter":"","showName":"所在公司"},{"columnName":"technicalDirection","formatter":"","showName":"技术方向"},{"columnName":"maintenanceGroupName","formatter":"","showName":"参与运维组"},{"columnName":"engagementProjectName","formatter":"","showName":"应用系统"}]', '[]', '[{"columnName":"id","returnName":"id"},{"columnName":"personName","returnName":"person_name"},{"columnName":"entryDate","returnName":"entry_date"},{"columnName":"engagementProjectJson","returnName":"engagement_project_json"},{"columnName":"engagementProjectId","returnName":"engagement_project_id"},{"columnName":"engagementProjectName","returnName":"engagement_project_name"},{"columnName":"maintenanceGroupId","returnName":"maintenance_group_id"},{"columnName":"maintenanceGroupName","returnName":"maintenance_group_name"},{"columnName":"maintenanceGroupJson","returnName":"maintenance_group_json"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"gwuomp_account_apply","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/module/staffpool/personInfo/getNoAccountPeronList(账号申请-查询审批通过且尚未分配账号的人员信息接口)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"/module/staffpool/personInfo/getNoAccountPeronList","method":"post-form","name":"账号申请-查询审批通过且尚未分配账号的人员信息接口","desc":"账号申请-查询审批通过且尚未分配账号的人员信息接口"},"apiReqColumns":[{"name":"pageNo","comment":"页码","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"每页数量","type":"Number","required":false,"primary":false}],"columns":[{"name":"id","comment":"人员id","type":"String","required":false,"primary":false},{"name":"personName","comment":"姓名","type":"String","required":false,"primary":false},{"name":"entryDate","comment":"入职日期","type":"String","required":false,"primary":false},{"name":"workingCompany","comment":"入职公司","type":"String","required":false,"primary":false},{"name":"technicalDirection","comment":"技术方向","type":"String","required":false,"primary":false},{"name":"engagementProjectId","comment":"参与项目id集合","type":"String","required":false,"primary":false},{"name":"engagementProjectName","comment":"参与项目名称集合","type":"String","required":false,"primary":false},{"name":"engagementProjectJson","comment":"参与项目json回显","type":"String","required":false,"primary":false},{"name":"maintenanceGroupId","comment":"参加运维组id集合","type":"String","required":false,"primary":false},{"name":"maintenanceGroupName","comment":"参加运维组名称集合","type":"String","required":false,"primary":false},{"name":"maintenanceGroupJson","comment":"参加运维组json回显","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-17 11:14:00', '899132526127218689', '2024-06-11 10:26:51', '899640318233673729');

INSERT INTO form_cust_dialog(id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899865099005067265', 'G_USER_SELECT', '用户', '权限申请-选择用户', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"userName","formatter":"","showName":"名称"},{"columnName":"userAccount","formatter":"","showName":"账号"},{"columnName":"maintenanceGroupName","formatter":"","showName":"运维组名称"}]', '[{"columnName":"status","condition":"EQ","dbType":"String","showName":"状态 1-启用 0-禁用","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"userName","condition":"EQ","dbType":"String","showName":"用户名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"pathStr","condition":"EQ","dbType":"String","showName":"路径","value":{"ctrlType":"inputText","text":"path"},"valueSource":"primaryTableColumn"}]', '[{"columnName":"userName","returnName":"userName"},{"columnName":"maintenanceGroupName","returnName":"maintenanceGroupName"},{"columnName":"userId","returnName":"userId"},{"columnName":"existingPositions","returnName":"existingPositions"},{"columnName":"existingRoles","returnName":"existingRoles"},{"columnName":"systemName","returnName":"systemName"},{"columnName":"existingSystem","returnName":"existingSystem"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"custApi","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"G_USER_SELECT","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/permission/getTreeByOrgType(根据当前登录人的机构类型获取机构树)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/permission/getTreeByOrgType","method":"post-form","name":"根据当前登录人的机构类型获取机构树","desc":"根据当前登陆人的机构类型获取机构树"},"apiReqColumns":[],"columns":[{"name":"id","comment":"","type":"String","required":false,"primary":false},{"name":"name","comment":"","type":"String","required":false,"primary":false},{"name":"parentId","comment":"","type":"String","required":false,"primary":false},{"name":"path","comment":"","type":"String","required":false,"primary":false}]}},"page":true,"pageSize":10,"primaryTablePercent":40,"returnFields":[{"columnName":"id","returnName":"id"},{"columnName":"parentId","returnName":"parentId"},{"columnName":"path","returnName":"path"}],"sortFields":[],"style":"tree","system":false,"treeConfig":{"checkStrictly":false,"id":"id","pid":"parentId","pidInitVal":"0","pidInitValScript":false,"showColumn":"name","showFilter":false},"width":0}', 'doubleTable', 'leftRightTop', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/permission/getPersonList(用户选择接口)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/permission/getPersonList","method":"post-form","name":"用户选择接口","desc":"人员权限申请-用户选择接口"},"apiReqColumns":[{"name":"pageNo","comment":"页码","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"每页数量","type":"Number","required":false,"primary":false},{"name":"status","comment":"状态 1-启用 0-禁用","type":"String","required":false,"primary":false},{"name":"pathStr","comment":"路径","type":"String","required":false,"primary":false},{"name":"userName","comment":"用户名称","type":"String","required":false,"primary":false}],"columns":[{"name":"userId","comment":"用户id","type":"String","required":false,"primary":false},{"name":"userName","comment":"用户名称","type":"String","required":false,"primary":false},{"name":"maintenanceGroupName","comment":"运维组名称","type":"String","required":false,"primary":false},{"name":"userAccount","comment":"账号","type":"String","required":false,"primary":false},{"name":"groupName","comment":"所属机构","type":"String","required":false,"primary":false},{"name":"existingRoles","comment":"已有角色","type":"String","required":false,"primary":false},{"name":"existingPositions","comment":"已有岗位","type":"String","required":false,"primary":false},{"name":"systemName","comment":"应用系统名称","type":"String","required":false,"primary":false},{"name":"existingSystem","comment":"应用系统json","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-22 13:41:06', '899640318233673729', '2024-11-29 17:19:20', '899640318233673729');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899867025713135617', 'UNOWNED_ROLES', '角色选择', '权限申请-角色（未拥有角色-选择需要申请的角色）', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"角色名称"},{"columnName":"code","formatter":"","showName":"角色编码"},{"columnName":"description","formatter":"","showName":"描述"}]', '[{"columnName":"userId","condition":"EQ","dbType":"String","showName":"用户id","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"name","returnName":"name"},{"columnName":"code","returnName":"code"},{"columnName":"id","returnName":"id"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UNOWNED_ROLES","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/permission/getNoRoleByUserId(获取本人没有的角色)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/permission/getNoRoleByUserId","method":"post-form","name":"获取本人没有的角色","desc":"获取本人没有的角色"},"apiReqColumns":[{"name":"pageNo","comment":"页码","type":"Number","required":false,"primary":false},{"name":"pageSize","comment":"每页数量","type":"Number","required":false,"primary":false},{"name":"userId","comment":"用户id","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"角色id","type":"String","required":false,"primary":false},{"name":"name","comment":"角色名称","type":"String","required":false,"primary":false},{"name":"code","comment":"角色编码","type":"String","required":false,"primary":false},{"name":"description","comment":"描述","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-22 14:42:21', '899640318233673729', '2024-06-05 20:16:27', '899640318233673729');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES ('899867861726527489', 'UNOWNED_POSITION', '岗位选择', '权限申请-岗位（未拥有的岗位-选择需要申请的岗位）', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"岗位名称"},{"columnName":"code","formatter":"","showName":"岗位编码"},{"columnName":"description","formatter":"","showName":"描述"}]', '[{"columnName":"userId","condition":"EQ","dbType":"String","showName":"用户id","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"name","returnName":"name"},{"columnName":"code","returnName":"code"},{"columnName":"id","returnName":"id"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UNOWNED_POSITION","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/permission/getNoPostByUserId(获取本人没有的岗位)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/permission/getNoPostByUserId","method":"post-form","name":"获取本人没有的岗位","desc":"获取本人没有的岗位"},"apiReqColumns":[{"name":"pageNo","comment":"页码","type":"String","required":false,"primary":false},{"name":"pageSize","comment":"每页数量","type":"String","required":false,"primary":false},{"name":"userId","comment":"用户id","type":"String","required":false,"primary":false}],"columns":[{"name":"id","comment":"岗位id","type":"String","required":false,"primary":false},{"name":"name","comment":"岗位名称","type":"String","required":false,"primary":false},{"name":"code","comment":"岗位编码","type":"String","required":false,"primary":false},{"name":"description","comment":"描述","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-05-22 15:08:56', '899640318233673729', '2024-06-11 15:05:11', '899640318233673729');

INSERT INTO form_cust_dialog (ID_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_) VALUES ('907250819395485697', 'newgwuomp_service_report_system_list', '应用系统选择', '应用系统选择-服务报告', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 746, 0, 0, '{“checkStrictly“:false,“pidInitValScript“:false,“showFilter“:false}', '[{“columnName“:“ci_name“,“formatter“:““,“showName“:“应用系统名称“},{“columnName“:“service_provider“,“formatter“:““,“showName“:“服务商“}]', '[{“columnName“:“attributeName“,“condition“:“EQ“,“dbType“:“String“,“showName“:“参数名“,“value“:{“ctrlType“:“inputText“,“text“:“ci_name“},“valueSource“:“defaultValue“},{“columnName“:“attributeValue“,“condition“:“EQ“,“dbType“:“String“,“showName“:“应用系统名称“,“value“:{“ctrlType“:“inputText“,“text“:“inputText“},“valueSource“:“param“},{“columnName“:“pageSize“,“condition“:“EQ“,“dbType“:“String“,“showName“:“页面数量“,“value“:{“ctrlType“:“inputText“,“text“:“10“},“valueSource“:“defaultValue“}]', '[{“columnName“:“ci_name“,“returnName“:“name“},{“columnName“:“service_provider“,“returnName“:“supplierName“},{“columnName“:“id“,“returnName“:“id“}]', '[]', 'custApi', '{“conditionFields“:[],“dataSource“:“database“,“displayFields“:[],“dsKey“:“dataSourceDefault“,“height“:0,“key“:“newgwuomp_service_report_system_list“,“multiple“:false,“objName“:““,“objType“:“table“,“otherConfig“:{“selectedFormatter“:““,“searchIgnorePrimaryTable“:false,“apiInfo“:““,“primaryKey“:““,“custApiConfig“:{“baseInfo“:{“fullPath“:““,“method“:“get“,“name“:““,“desc“:““},“apiReqColumns“:[],“columns“:[]}},“page“:true,“pageSize“:10,“primaryTablePercent“:50,“returnFields“:[],“sortFields“:[],“style“:“list“,“system“:false,“treeConfig“:{“checkStrictly“:false,“pidInitValScript“:false,“showFilter“:false},“width“:0}', 'singleTable', 'leftRight', '{“selectedFormatter“:““,“searchIgnorePrimaryTable“:false,“apiInfo“:“module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false(应用系统-CMDB)“,“primaryKey“:““,“custApiConfig“:{“baseInfo“:{“fullPath“:“module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false“,“method“:“get“,“name“:“应用系统-CMDB“,“desc“:“应用系统-CMDB“},“apiReqColumns“:[{“name“:“attributeName“,“comment“:“参数名“,“type“:“String“,“required“:false,“primary“:false},{“name“:“attributeValue“,“comment“:“参数值“,“type“:“String“,“required“:false,“primary“:false},{“name“:“pageSize“,“comment“:“页面数量“,“type“:“String“,“required“:false,“primary“:false}],“columns“:[{“name“:“ci_name“,“comment“:“资源名称“,“type“:“String“,“required“:false,“primary“:false},{“name“:“id“,“comment“:“id“,“type“:“String“,“required“:false,“primary“:false},{“name“:“service_provider“,“comment“:“服务商“,“type“:“String“,“required“:false,“primary“:false}]}}', '884093110759981057', '2024-11-01 14:46:50', '883016099668951041', '2025-04-01 15:48:22', '914046492657844225');
INSERT INTO form_cust_dialog (ID_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, CREATE_TIME_, CREATE_BY_, UPDATE_TIME_, UPDATE_BY_) VALUES ('913583549805756417', 'newgwuomp_service_provider_system_list', '应用系统选择', '应用系统选择-服务商管理', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 746, 0, 0, '{“checkStrictly“:false,“pidInitValScript“:false,“showFilter“:false}', '[{“columnName“:“ci_name“,“formatter“:““,“showName“:“应用系统名称“},{“columnName“:“service_provider“,“formatter“:““,“showName“:“服务商“}]', '[{“columnName“:“attributeName“,“condition“:“EQ“,“dbType“:“String“,“showName“:“参数名“,“value“:{“ctrlType“:“inputText“,“text“:“ci_name“},“valueSource“:“defaultValue“},{“columnName“:“attributeValue“,“condition“:“EQ“,“dbType“:“String“,“showName“:“应用系统名称“,“value“:{“ctrlType“:“inputText“,“text“:“inputText“},“valueSource“:“param“},{“columnName“:“pageSize“,“condition“:“EQ“,“dbType“:“String“,“showName“:“页面数量“,“value“:{“ctrlType“:“inputText“,“text“:“10“},“valueSource“:“defaultValue“}]', '[{“columnName“:“ci_name“,“returnName“:“ci_name“},{“columnName“:“id“,“returnName“:“id“},{“columnName“:“service_provider“,“returnName“:“service_provider“},{“columnName“:“modelTypeId“,“returnName“:“modelTypeId“},{“columnName“:“deploy_path“,“returnName“:“deploy_path“},{“columnName“:“is_core“,“returnName“:“is_core“},{“columnName“:“modelId“,“returnName“:“modelId“},{“columnName“:“responsible_person“,“returnName“:“responsible_person“},{“columnName“:“application_state“,“returnName“:“application_state“},{“columnName“:“om_person“,“returnName“:“om_person“},{“columnName“:“ip_address“,“returnName“:“ip_address“},{“columnName“:“url“,“returnName“:“url“},{“columnName“:“is_high_avliable“,“returnName“:“is_high_avliable“},{“columnName“:“monitor_status“,“returnName“:“monitor_status“},{“columnName“:“responsible_contact_info“,“returnName“:“responsible_contact_info“},{“columnName“:“port“,“returnName“:“port“},{“columnName“:“om_responsible_contact_info“,“returnName“:“om_responsible_contact_info“},{“columnName“:“domain“,“returnName“:“domain“},{“columnName“:“application_no“,“returnName“:“application_no“},{“columnName“:“rank“,“returnName“:“rank“},{“columnName“:“responsible_org“,“returnName“:“responsible_org“}]', '[]', 'custApi', '{“conditionFields“:[],“dataSource“:“database“,“displayFields“:[],“dsKey“:“dataSourceDefault“,“height“:0,“key“:“newgwuomp_service_provider_system_list“,“multiple“:false,“objName“:““,“objType“:“table“,“otherConfig“:{“selectedFormatter“:““,“searchIgnorePrimaryTable“:false,“apiInfo“:““,“primaryKey“:““,“custApiConfig“:{“baseInfo“:{“fullPath“:““,“method“:“get“,“name“:““,“desc“:““},“apiReqColumns“:[],“columns“:[]}},“page“:true,“pageSize“:10,“primaryTablePercent“:50,“returnFields“:[],“sortFields“:[],“style“:“list“,“system“:false,“treeConfig“:{“checkStrictly“:false,“pidInitValScript“:false,“showFilter“:false},“width“:0}', 'singleTable', 'leftRight', '{“selectedFormatter“:““,“searchIgnorePrimaryTable“:false,“apiInfo“:“module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false(应用系统-CMDB)“,“primaryKey“:““,“custApiConfig“:{“baseInfo“:{“fullPath“:“module/staffpool/system/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false“,“method“:“get“,“name“:“应用系统-CMDB“,“desc“:“应用系统-CMDB“},“apiReqColumns“:[{“name“:“attributeName“,“comment“:“参数名“,“type“:“String“,“required“:false,“primary“:false},{“name“:“attributeValue“,“comment“:“参数值“,“type“:“String“,“required“:false,“primary“:false},{“name“:“pageSize“,“comment“:“页面数量“,“type“:“String“,“required“:false,“primary“:false}],“columns“:[{“name“:“ci_name“,“comment“:“资源名称“,“type“:“String“,“required“:false,“primary“:false},{“name“:“id“,“comment“:“id“,“type“:“String“,“required“:false,“primary“:false},{“name“:“service_provider“,“comment“:“服务商“,“type“:“String“,“required“:false,“primary“:false},{“name“:“modelTypeId“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“deploy_path“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“is_core“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“modelId“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“responsible_person“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“application_state“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“om_person“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“ip_address“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“url“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“is_high_avliable“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“monitor_status“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“responsible_contact_info“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“port“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“om_responsible_contact_info“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“domain“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“application_no“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“rank“,“comment“:““,“type“:“String“,“required“:false,“primary“:false},{“name“:“responsible_org“,“comment“:““,“type“:“String“,“required“:false,“primary“:false}]}}', '884093110759981057', '2024-11-01 14:46:50', '883016099668951041', '2025-04-01 17:47:10', '914046492657844225');
