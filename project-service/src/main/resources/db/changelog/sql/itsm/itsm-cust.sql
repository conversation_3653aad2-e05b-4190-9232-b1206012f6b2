INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('1', '0', '', '告警处理服务', '告警处理服务', '', '', '', '1', 14, '', '', '', '', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('2', '1', '告警处理服务', '告警处置', '服务器、数据库、网络设备、中间件、应用的告警处置服务', '服务器、数据库、网络设备、中间件、应用的告警处置服务', '', '', '1', 1, '', '', '[]', '', '', '', '0');


INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755432230913', '[{"compareType":"in","compareTime":"","addHours":8,"beginTime":"06:45","endTime":"23:59","name":"区间","$uuid":"zBv1zplaK"}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755434328065', '[{"compareType":"in","compareTime":"","addHours":72,"beginTime":"00:00","endTime":"23:59","name":"全部时段","$uuid":"esxAEva2g"}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755436949505', '[{"compareType":"all","compareTime":"","addHours":8,"beginTime":"00:00","endTime":"23:59","name":"8点","$uuid":"LEF3pqTWN"}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755439046657', '[{"compareType":"in","compareTime":"","addHours":48,"beginTime":"10:00","endTime":"18:00","name":"10点到18点服务级别协议","$uuid":"VheP8Svgx"}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755440619521', '[{"compareType":"in","compareTime":"","addHours":1,"beginTime":"00:00","endTime":"01:00","name":"区间","$uuid":"TOGAXi5yh"}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755442192385', '[{"compareType":"in","compareTime":"","addHours":"72","beginTime":"","endTime":""}]');
INSERT INTO module_it_service_template (ID, CONFIG_JSON) VALUES ('911999755444289537', '[{"compareType":"in","compareTime":"","addHours":"72","beginTime":"","endTime":""}]');


INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900097894423986177', '0', '', '终端服务', '终端服务', '', '', '', '1', 1, '', '', '', '', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900137799643561985', '0', '', '应用服务', '应用服务', '', '', '', '1', 3, '', '', '', '', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264662708256769', '0', '', '网络服务', '网络服务', '', '', '', '1', 2, '', '', '', '', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264668439248897', '0', '', '安全服务', '安全服务', '', '', '', '1', 4, NULL, NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264676110630913', '0', '', '数据服务', '数据服务', '', '', '', '1', 5, NULL, NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264681235021825', '0', '', '资源服务', '资源服务', '', '', '', '1', 6, NULL, NULL, NULL, NULL, NULL, NULL, '1');


INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900099402980392961', '900097894423986177', '终端服务', '笔记本维修', '笔记本相关问题，如无法开机、开机蓝屏、开机黑屏、进水维修等。', '笔记本相关问题，如无法开机、开机蓝屏、开机黑屏、进水维修等。', '', '', '0', 1, '', 'base:bpm-PC', '', '900827909565972481', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900142240824819713', '900097894423986177', '终端服务', '安装相关软件', '常用软件安装需求。', '常用软件安装需求。', '', '', '0', 3, '', 'base:bpm-column1', '', '900827909565972481', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900143223949230081', '900097894423986177', '终端服务', '打印机维修', '打印机相关问题，如打印机卡纸、打印机缺墨、打印机故障等。', '打印机相关问题，如打印机卡纸、打印机缺墨、打印机故障等。', '', '', '1', 2, '', 'base:bpm-off-information', '', '910052202810179585', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264780443418625', '900097894423986177', '终端服务', '重装系统', '重装系统。', '重装系统。', '', '', '1', 5, '', 'base:bpm-select2', '[{"name":"禅道公司","id":"900827909565972481","respName":"张凌云","respTel":"13401195611"}]', '900827909565972481', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('900264787980582913', '900097894423986177', '终端服务', '密码修改', '电脑密码忘记，密码修改，密码重置等。', '电脑密码忘记，密码修改，密码重置等。', '', '', '0', 4, '', 'base:bpm-a-Keymanagement', '', '900827909565972481', '', '', '1');
INSERT INTO module_it_service (ID, PARENT_ID, TYPE_, NAME_, DESC_, CONTENT_, FREQUENCY_, PRICE_, STATUS_, SN_, SYSTEM_JSON, ICON_, SERVICE_PROVIDER_JSON, SERVICE_PROVIDER_ID, ORG_JSON, ORG_ID, IS_USED) VALUES ('911919714538094593', '900097894423986177', '终端服务', '操作系统问题', '操作系统相关问题', '解决操作系统的相关问题', '高', '100', '1', 6, '', 'base:bpm-workflow', '', '911871747749314561', '', '', '1');


INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('910105478794575873', 'IT_SERVICE', '900099402980392961', 'all', 'user', '所有人', 'user-all', '2025-01-03 15:14:01', '910058511092678657');
INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('910105494546284545', 'IT_SERVICE', '900264787980582913', 'all', 'user', '所有人', 'user-all', '2025-01-03 15:14:31', '910058511092678657');
INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('910105485801684993', 'IT_SERVICE', '900143223949230081', 'all', 'user', '所有人', 'user-all', '2025-01-22 11:07:45', '910058511092678657');
INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('910105491087556609', 'IT_SERVICE', '900142240824819713', 'all', 'user', '所有人', 'user-all', '2025-03-04 10:55:29', '912782687235735553');
INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('910105498218397697', 'IT_SERVICE', '900264780443418625', 'all', 'user', '所有人', 'user-all', '2025-03-04 10:55:33', '912782687235735553');
INSERT INTO sys_authorization (rights_id_, rights_object_, rights_target_, rights_type_, rights_identity_, rights_identity_name_, rights_permission_code_, rights_create_time_, rights_create_by_) VALUES ('912815436343541761', 'IT_SERVICE', '911919714538094593', 'all', 'user', '所有人', 'user-all', '2025-03-04 11:01:15', '912782687235735553');
