INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('886553135335604225', 'UOMP_ALARM_RULE_SELECTOR', '告警规则选择对话框', '告警规则选择对话框', NULL, 'list', 'dataSourceDefault', '', 'table', '79d606bfbe3b4e658512f307f72232d1', 1, 10, 900, 615, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"名称"},{"columnName":"en_name","formatter":"","showName":"规则别名"}]', '[]', '[{"columnName":"name","returnName":"name"},{"columnName":"en_name","returnName":"en_name"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_ALARM_RULE_SELECTOR","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/monitor/alarmMonitoring/getRuleList(告警静默策略-告警规则查询接口)","primaryKey":""}', '886451657680879617', '2023-08-02 16:45:11', '883427513034866689', '2023-08-02 16:54:56', '883427513034866689');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('886635230374920193', 'UOMP_VALID_BIZ_GROUP', '有效的业务组（单选）', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'd31b93d15ebe4b6395dcd6d6249dfe40', 1, 10, 1000, 746, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"业务组名称"}]', '[]', '[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_VALID_BIZ_GROUP","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/team/vaildList(有效的业务组列表)","primaryKey":""}', '886451657680879617', '2023-08-04 12:14:54', '882462575167012865', '2023-11-14 15:34:38', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('888813549244121089', 'UOMP_ASSOCIATED_LIBRARY', '监控管理-关联指标库的分组信息', '', NULL, 'list', 'dataSourceDefault', '', 'table', '1dfd4cbb6aaf4b82ac80141736539015', 0, 10, 900, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":true}', '[{"columnName":"tree_name","formatter":"","showName":"名称"}]', '[]', '[{"columnName":"tree_id","returnName":"tree_id"},{"columnName":"tree_name","returnName":"tree_name"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_ASSOCIATED_LIBRARY","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/monitor/alarmMonitoring/getTreeNodeHaveIndex(监控管理-关联指标库的分组信息)","primaryKey":""}', '886451657680879617', '2023-09-21 14:21:48', '882510075765719041', '2023-09-21 15:00:36', '882510075765719041');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('888994774989668353', 'UOMP_INDEX_SELECTOR', '指标库选择对话框', '指标库选择对话框', NULL, 'list', 'dataSourceDefault', '', 'table', '0e1c43defe144228ac278f4518eefa1e', 1, 10, 1000, 815, 0, 0, '{"showFilter":false,"checkStrictly":false}', '[{"columnName":"index_name","showName":"指标库名称","formatter":""},{"columnName":"index_name_en","showName":"指标库英文名","formatter":""}]', '[{"columnName":"index_name","showName":"指标库名称","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"rule_base_id","returnName":"id"},{"columnName":"index_name","returnName":"name"},{"columnName":"index_name_en","returnName":"name_en"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_INDEX_SELECTOR","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/monitor/alarmMonitoring/getRuleBaseByProbe(告警规则管理-查询用途含有告警的指标库数据)","primaryKey":""}', '886451657680879617', '2023-09-25 14:22:49', '883427513034866689', '2023-09-25 14:22:49', '883427513034866689');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('886635230374920194', 'UOMP_VALID_BIZ_GROUP_C', '有效的业务组（复选）', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'd31b93d15ebe4b6395dcd6d6249dfe40', 1, 10, 1000, 746, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"业务组名称"}]', '[]', '[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"},{"columnName":"id","returnName":"org_id"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_VALID_BIZ_GROUP_C","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/team/vaildList(有效的业务组列表)","primaryKey":""}', '886451657680879617', '2023-08-04 12:14:54', '882462575167012865', '2023-12-05 10:43:08', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('903448708675796993', 'gjzyxz', '告警资源选择（多选）', '', NULL, 'list', 'dataSourceDefault', '', 'table', '', 0, 10, 1000, 815, 1, 1, '{"showFilter":false,"checkStrictly":false}', '[{"columnName":"ciName","showName":"资源名称","formatter":""},{"columnName":"port","showName":"端口号","formatter":""},{"columnName":"ipAddress","showName":"IP","formatter":""}]', '[{"columnName":"modelTypeId","showName":"告警对象：1-服务器 2-应用 3-数据库 4-中间件 5-网络设备","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"cInstId","returnName":"id"},{"columnName":"ciName","returnName":"name"},{"columnName":"ipAddress","returnName":"ipAddress"},{"columnName":"port","returnName":"port"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"gjzyxz","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/alarm/getAllInst(获取某告警对象下的全部资源)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/alarm/getAllInst","method":"get","name":"获取某告警对象下的全部资源","desc":""},"apiReqColumns":[{"name":"modelTypeId","comment":"告警对象：1-服务器 2-应用 3-数据库 4-中间件 5-网络设备","type":"String","required":false,"primary":false}],"columns":[{"name":"cInstId","comment":"资源id","type":"String","required":false,"primary":false},{"name":"ciName","comment":"资源名称","type":"String","required":false,"primary":false},{"name":"ipAddress","comment":"IP","type":"String","required":false,"primary":false},{"name":"port","comment":"端口号","type":"String","required":false,"primary":false}]}}', '886451657680879617', '2024-08-09 16:20:59', '895971860662976513', '2024-08-09 16:20:59', '895971860662976513');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('903448759299997697', 'gjzyxz_d', '告警资源选择（单选）', '', NULL, 'list', 'dataSourceDefault', '', 'table', '', 0, 10, 1000, 815, 1, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"ciName","formatter":"","showName":"资源名称"},{"columnName":"ipAddress","formatter":"","showName":"IP"},{"columnName":"port","formatter":"","showName":"端口号"}]', '[{"columnName":"modelTypeId","condition":"EQ","dbType":"String","showName":"告警对象：1-服务器 2-应用 3-数据库 4-中间件 5-网络设备","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"ip","showName":"IP","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"ciName","returnName":"ciName"},{"columnName":"ipAddress","returnName":"ipAddress"},{"columnName":"port","returnName":"port"},{"columnName":"cInstId","returnName":"cInstId"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"gjzyxz_d","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/alarm/getAllInst(获取某告警对象下的全部资源)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/alarm/getAllInst","method":"get","name":"获取某告警对象下的全部资源","desc":""},"apiReqColumns":[{"name":"modelTypeId","comment":"告警对象：1-服务器 2-应用 3-数据库 4-中间件 5-网络设备","type":"String","required":false,"primary":false},{"name":"name","comment":"资源名称","type":"String","required":false,"primary":false},{"name":"ip","comment":"IP","type":"String","required":false,"primary":false}],"columns":[{"name":"cInstId","comment":"资源id","type":"String","required":false,"primary":false},{"name":"ciName","comment":"资源名称","type":"String","required":false,"primary":false},{"name":"ipAddress","comment":"IP","type":"String","required":false,"primary":false},{"name":"port","comment":"端口号","type":"String","required":false,"primary":false}]}}', '886451657680879617', '2024-08-09 16:22:36', '895971860662976513', '2024-08-15 16:18:34', '895971860662976513');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('903448899558572033', 'ywzryxz_yw', '运维组人员选择', '', NULL, 'list', 'dataSourceDefault', '', 'table', '', 0, 10, 1000, 815, 1, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"personName","formatter":"","showName":"姓名"},{"columnName":"tel","formatter":"","showName":"用户手机号"}]', '[{"columnName":"groupId","condition":"EQ","dbType":"String","showName":"","value":{"ctrlType":"inputText","text":"id"},"valueSource":"primaryTableColumn"},{"columnName":"status","condition":"EQ","dbType":"String","showName":"","value":{"ctrlType":"inputText","text":"1"},"valueSource":"defaultValue"}]', '[{"columnName":"personName","returnName":"user_name"},{"columnName":"tel","returnName":"user_mobile"},{"columnName":"account","returnName":"account"},{"columnName":"userId","returnName":"user_id"},{"columnName":"orgUserId","returnName":"orgUserId"},{"columnName":"groupId","returnName":"org_id"},{"columnName":"groupName","returnName":"org_name"},{"columnName":"userId","returnName":"id"},{"columnName":"personName","returnName":"name"},{"columnName":"orgGroupId","returnName":"orgGroupId"},{"columnName":"email","returnName":"user_email"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"custApi","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"ywzryxz_yw","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/org/group/getOrgTreePerson(获取运维组)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/org/group/getOrgTreePerson","method":"get","name":"获取运维组","desc":""},"apiReqColumns":[],"columns":[{"name":"name","comment":"运维组名称","type":"String","required":false,"primary":false},{"name":"id","comment":"运维组ID","type":"String","required":false,"primary":false},{"name":"parentId","comment":"父ID","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"组织ID","type":"String","required":false,"primary":false}]}},"page":true,"pageSize":10,"primaryTablePercent":45,"returnFields":[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"}],"sortFields":[],"style":"tree","system":false,"treeConfig":{"checkStrictly":false,"id":"id","pid":"parentId","pidInitValScript":false,"showColumn":"groupName","showFilter":false},"width":0}', 'doubleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/account/list(账号管理列表)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/account/list","method":"post-form","name":"账号管理列表","desc":""},"apiReqColumns":[{"name":"groupId","comment":"","type":"String","required":false,"primary":false},{"name":"status","comment":"","type":"String","required":false,"primary":false}],"columns":[{"name":"account","comment":"账号","type":"String","required":false,"primary":false},{"name":"personName","comment":"姓名","type":"String","required":false,"primary":false},{"name":"userId","comment":"用户id","type":"String","required":false,"primary":false},{"name":"tel","comment":"用户手机号","type":"String","required":false,"primary":false},{"name":"orgUserId","comment":"系统用户id","type":"String","required":false,"primary":false},{"name":"groupId","comment":"运维组织id","type":"String","required":false,"primary":false},{"name":"groupName","comment":"运维组织名称","type":"String","required":false,"primary":false},{"name":"workingCompany","comment":"就职公司名称","type":"String","required":false,"primary":false},{"name":"workingCompanyId","comment":"就职公司id","type":"String","required":false,"primary":false},{"name":"entryStatus","comment":"驻场服务状态","type":"String","required":false,"primary":false},{"name":"status","comment":"账号状态 ","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"组织id","type":"String","required":false,"primary":false},{"name":"email","comment":"用户邮箱","type":"String","required":false,"primary":false}]}}', '886451657680879617', '2024-08-09 16:27:03', '895971860662976513', '2024-08-29 11:19:36', '895971860662976513');
