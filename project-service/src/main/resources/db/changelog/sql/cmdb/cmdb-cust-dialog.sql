INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('884104715075321857', 'UOMP_RESOURCE_ALL_PAGE', '所有资源', '', NULL, 'list', 'dataSourceDefault', '', 'table', '96f39d3fccb44e2aa1462d5acf3204ad', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"resource_no","formatter":"","showName":"资源编号"},{"columnName":"resource_name","formatter":"","showName":"资源名称"}]', '[{"columnName":"resource_name","condition":"EQ","dbType":"String","showName":"资产名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"resource_id","condition":"EQ","dbType":"String","showName":"资源ID","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"group_id","showName":"分组ID","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"resource_name","returnName":"name"},{"columnName":"resource_name","returnName":"resource_name"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_RESOURCE_ALL_PAGE","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/cmdb/res/resourceAll(13所有配置信息)","primaryKey":""}', '884093110759981057', '2023-06-09 15:32:00', '881565440330956801', '2023-10-12 11:24:30', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('885329380188356609', 'model_update_list', '模型变更列表', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'cdde0f86f070401b941b4897be7d84d7', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"group_name","formatter":"","showName":"分组"},{"columnName":"name","formatter":"","showName":"模型名称"},{"columnName":"team_name","formatter":"","showName":"业务组"}]', '[]', '[{"columnName":"group_name","returnName":"group_name"},{"columnName":"baseline_id","returnName":"baseline_id"},{"columnName":"baseline_audit","returnName":"baseline_audit"},{"columnName":"baseline","returnName":"baseline"},{"columnName":"id","returnName":"id"},{"columnName":"team_id","returnName":"team_id"},{"columnName":"name","returnName":"name"},{"columnName":"model_status","returnName":"model_status"},{"columnName":"baseline_status","returnName":"baseline_status"},{"columnName":"team_name","returnName":"team_name"},{"columnName":"use_counts","returnName":"use_counts"},{"columnName":"model_status_cn","returnName":"model_status_cn"},{"columnName":"baseline_status_cn","returnName":"baseline_status_cn"},{"columnName":"operator","returnName":"operator"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"model_update_list","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/cmdb/config/list_Dialog(配置列表_自定义对话框)","primaryKey":""}', '884093110759981057', '2023-07-06 16:23:03', '881565459776798721', '2023-07-06 16:23:10', '881565459776798721');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('886142709422096385', 'UOMP_CURRENT_MODEL_TREE', '当前登陆人有权限的模型树', '', NULL, 'tree', 'dataSourceDefault', '', 'table', 'c2cfed795d33468e8b365a2a45d182da', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"id":"id","pid":"parentId","pidInitValScript":false,"showColumn":"name","showFilter":true}', '[]', '[{"columnName":"name","showName":"名称","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"key","returnName":"key"},{"columnName":"baseline_id","returnName":"baseline_id"},{"columnName":"name","returnName":"name"},{"columnName":"parentId","returnName":"parentId"},{"columnName":"treeId","returnName":"treeId"},{"columnName":"path","returnName":"path"},{"columnName":"type","returnName":"type"},{"columnName":"sn","returnName":"sn"},{"columnName":"model_status","returnName":"model_status"},{"columnName":"baseline_status","returnName":"baseline_status"},{"columnName":"icon","returnName":"icon"}]', '[{"columnName":"sn","sortType":"asc"}]', 'interface', '{"conditionFields":[],"dataSource":"interface","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_CURRENT_MODEL_TREE","multiple":false,"objName":"c2cfed795d33468e8b365a2a45d182da","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/cmdb/comm/getCurrentModel(获取当前登陆人有权限的配置模型)","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"tree","system":false,"treeConfig":{"checkStrictly":false,"id":"id","pid":"parentId","pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'upDown', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/cmdb/comm/getCurrentModel(获取当前登陆人有权限的配置模型)","primaryKey":""}', '884093110759981057', '2023-07-24 15:18:05', '881565440330956801', '2023-07-24 17:19:24', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('887277668175708161', 'uomp_project', '项目', '', NULL, 'list', 'dataSourceDefault', '', 'table', '1b35d3e38c8e4837889542de6d1e9e0f', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"project_name","formatter":"","showName":"名称"},{"columnName":"project_code","formatter":"","showName":"编码"},{"columnName":"project_stage","formatter":"","showName":"阶段"}]', '[{"columnName":"project_name","condition":"EQ","dbType":"String","showName":"项目名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"project_name","returnName":"name"},{"columnName":"project_code","returnName":"code"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_project","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/project/getProjectList_dialog(项目管理列表查询接口_对话框)","primaryKey":""}', '884093110759981057', '2023-08-18 16:37:27', '881565459776798721', '2023-09-01 16:38:59', '881705933694566401');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('887592943223832577', 'UOMP_POST_NOTEAM_C', '岗位查询（不含业务组）', '岗位查询（不含业务组）,多选', NULL, 'list', 'dataSourceDefault', '', 'view', 'UOMP_VIEW_POST_NOTEAM', 1, 10, 800, 815, 1, 1, '{"showFilter":false,"checkStrictly":false}', '[{"columnName":"NAME_","showName":"岗位名称","formatter":""},{"columnName":"DESC_","showName":"岗位描述","formatter":""}]', '[{"columnName":"NAME_","showName":"岗位名称","dbType":"varchar","condition":"LK","value":{"ctrlType":"inputText","text":""},"valueSource":"param"},{"columnName":"CODE_","showName":"岗位编码","dbType":"varchar","condition":"EQ","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"ID_","returnName":"id"},{"columnName":"CODE_","returnName":"code"},{"columnName":"NAME_","returnName":"name"},{"columnName":"DESC_","returnName":"description"}]', '[]', 'database', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_POST_NOTEAM_C","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"ID_"}', '884093110759981057', '2023-08-25 15:39:47', '881565440330956801', '2023-08-25 15:39:47', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('887909663180324865', 'uomp_project_more', '项目多选', '', NULL, 'list', 'dataSourceDefault', '', 'table', '1b35d3e38c8e4837889542de6d1e9e0f', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"project_code","formatter":"","showName":"编码"},{"columnName":"project_name","formatter":"","showName":"名称"},{"columnName":"project_stage","formatter":"","showName":"阶段"}]', '[{"columnName":"project_name","condition":"EQ","dbType":"String","showName":"项目名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"depart_id","showName":"主管部门id","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":""},"valueSource":"param"}]', '[{"columnName":"id","returnName":"id"},{"columnName":"project_name","returnName":"project_name"},{"columnName":"project_code","returnName":"project_code"},{"columnName":"depart_id","returnName":"depart_id"},{"columnName":"depart_name","returnName":"depart_name"},{"columnName":"project_name","returnName":"name"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_project_more","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/project/getProjectList_dialog(项目管理列表查询接口_对话框)","primaryKey":""}', '884093110759981057', '2023-09-01 15:28:02', '881705933694566401', '2023-09-21 09:39:17', '883427513034866689');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('890439095989305345', 'UOMP_GROUP_C', '业务组-复选', '', NULL, 'list', 'dataSourceDefault', '', 'table', 'd4fe3df46ec34fcd9b563fa4f141c2e9', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"name","formatter":"","showName":"业务组名称"}]', '[{"columnName":"name","showName":"业务组名称","dbType":"String","condition":"EQ","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"UOMP_GROUP_C","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/team/list(03业务组列表)","primaryKey":""}', '884093110759981057', '2023-10-27 11:36:32', '881565440330956801', '2023-10-27 11:37:20', '881565440330956801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('908656564018085889', 'newgwuomp_service_report_system_form', '应用系统 表单', '', NULL, 'list', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"ci_name","formatter":"","showName":"应用名称"},{"columnName":"service_provider","formatter":"","showName":"服务商"}]', '[{"columnName":"attributeValue","condition":"EQ","dbType":"String","showName":"应用系统名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"attributeName","condition":"EQ","dbType":"String","showName":"参数名称","value":{"ctrlType":"inputText","text":"ci_name"},"valueSource":"defaultValue"}]', '[{"columnName":"ci_name","returnName":"applicationSystemName"},{"columnName":"service_provider","returnName":"supplierName"},{"columnName":"id","returnName":"id"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"newgwuomp_service_report_system_form","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false(从CMDB获取应用)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/cmdb/instListByParam?noPage=false&modelName=application&isAuth=false","method":"get","name":"从CMDB获取应用","desc":""},"apiReqColumns":[{"name":"attributeName","comment":"参数名称","type":"String","required":false,"primary":false},{"name":"attributeValue","comment":"参数值","type":"String","required":false,"primary":false}],"columns":[{"name":"ci_name","comment":"应用名称","type":"String","required":false,"primary":false},{"name":"id","comment":"id","type":"String","required":false,"primary":false},{"name":"service_provider","comment":"服务商","type":"String","required":false,"primary":false}]}}', '884093110759981057', '2024-12-02 15:34:15', '884051250302156801', '2024-12-02 15:35:20', '884051250302156801');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('910874606855782401', 'OAM_ORG_C', '运维机构多选', 'CMDB模型权限选择机构', NULL, 'tree', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 1, '{"checkStrictly":true,"id":"id","pid":"parentId","pidInitValScript":false,"showColumn":"groupName","showFilter":true}', '[]', '[]', '[{"columnName":"name","returnName":"name"},{"columnName":"id","returnName":"id"},{"columnName":"orgGroupId","returnName":"orgGroupId"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"OAM_ORG_C","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"module/staffpool/org/group/getOrgTreePerson(获取运维组织机构树)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"module/staffpool/org/group/getOrgTreePerson","method":"get","name":"获取运维组织机构树","desc":""},"apiReqColumns":[],"columns":[{"name":"name","comment":"运维组名称","type":"String","required":false,"primary":false},{"name":"id","comment":"运维组ID","type":"String","required":false,"primary":false},{"name":"parentId","comment":"父ID","type":"String","required":false,"primary":false},{"name":"orgGroupId","comment":"orgGroupId","type":"String","required":false,"primary":false}]}}', '884093110759981057', '2025-01-20 14:43:56', '910290270479712257', '2025-01-20 14:45:17', '910290270479712257');
INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('887276357516001281', 'uomp_role_c', '角色选择', '用于账号申请表单设计器中角色选择', NULL, 'list', 'dataSourceDefault', '', 'table', 'ORG_ROLE', 1, 10, 800, 600, 1, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"NAME_","formatter":"","showName":"名称"},{"columnName":"DESCRIPTION","formatter":"","showName":"描述"}]', '[{"columnName":"NAME_","condition":"LK","dbType":"varchar","showName":"名称","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"DESCRIPTION","condition":"EQ","dbType":"varchar","showName":"描述","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"},{"columnName":"ENABLED_","condition":"EQ","dbType":"number","showName":"状态：0禁用，1启用","value":{"ctrlType":"inputText","text":"1"},"valueSource":"fixedValue"}]', '[{"columnName":"NAME_","returnName":"name"},{"columnName":"ALIAS_","returnName":"code"},{"columnName":"DESCRIPTION","returnName":"description"},{"columnName":"ID_","returnName":"id"}]', '[]', 'database', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"uomp_role_c","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"ID_"}', '884093110759981057', '2023-08-18 15:55:47', '883427513034866689', '2024-11-08 09:39:11', '888452830126931969');

