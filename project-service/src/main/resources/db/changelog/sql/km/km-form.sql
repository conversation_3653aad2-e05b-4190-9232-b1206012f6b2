INSERT INTO bus_object (id_, key_, name_, desc_, relation_json_, group_id_, group_name_, persistence_type_, per_type_config_, overall_arrangement_, create_time_, create_by_, update_time_, update_by_, diagram_json_, save_index_, org_id_) VALUES('891037354702143489', 'zswh', '知识维护', NULL, '{"children":[{"children":[],"dataFlowLinks":[],"fks":[{"from":"KNOWLEDGE_ID","type":"parentField","value":"KNOWLEDGE_ID"}],"tableComment":"","tableKey":"KM_SORT_RELEVANCE","type":"oneToMany"},{"children":[],"dataFlowLinks":[],"fks":[{"from":"KNOWLEDGE_ID","type":"parentField","value":"KN<PERSON>LEDGE_ID"}],"tableComment":"","tableKey":"KM_CONTENT","type":"oneToOne"}],"dataFlowLinks":[],"tableComment":"","tableKey":"KM_INFO","type":"main"}', '881784117248131073', '知识库管理', 'db', NULL, NULL, '2023-11-09 16:34:40', '887411276895485953', '2023-11-29 16:09:58', '887411276895485953', '{"cells":[{"type":"gwssi.customRec","optionHeight":30,"questionHeight":45,"minWidth":200,"position":{"x":150,"y":70},"size":{"width":200,"height":735},"angle":0,"question":"KM_INFO","options":[{"id":"KNOWLEDGE_ID","text_left":"KNOWLEDGE_ID","text_right":"varchar"},{"id":"KNOWLEDGE_CODE","text_left":"KNOWLEDGE_CODE","text_right":"varchar"},{"id":"KNOWLEDGE_NAME","text_left":"KNOWLEDGE_NAME","text_right":"varchar"},{"id":"LABEL","text_left":"LABEL","text_right":"varchar"},{"id":"OPERATOR","text_left":"OPERATOR","text_right":"varchar"},{"id":"KNOWLEDGE_STATUS","text_left":"KNOWLEDGE_STATUS","text_right":"varchar"},{"id":"LIKE_COUNT","text_left":"LIKE_COUNT","text_right":"number"},{"id":"FAVORITE_COUNT","text_left":"FAVORITE_COUNT","text_right":"number"},{"id":"PUBLISH_DATE","text_left":"PUBLISH_DATE","text_right":"date"},{"id":"CREATE_ID","text_left":"CREATE_ID","text_right":"varchar"},{"id":"CREATE_DATE","text_left":"CREATE_DATE","text_right":"date"},{"id":"UPDATE_ID","text_left":"UPDATE_ID","text_right":"varchar"},{"id":"UPDATE_DATE","text_left":"UPDATE_DATE","text_right":"date"},{"id":"DELETE_FLAG","text_left":"DELETE_FLAG","text_right":"varchar"},{"id":"REVISE_DATE","text_left":"REVISE_DATE","text_right":"date"},{"id":"KM_DESCRIBE","text_left":"KM_DESCRIBE","text_right":"varchar"},{"id":"FILE","text_left":"FILE","text_right":"varchar"},{"id":"process_id","text_left":"process_id","text_right":"varchar"},{"id":"OPERATOR_ID","text_left":"OPERATOR_ID","text_right":"varchar"},{"id":"KNOWLEDGE_TYPE","text_left":"KNOWLEDGE_TYPE","text_right":"varchar"},{"id":"PUBLISH_USER","text_left":"PUBLISH_USER","text_right":"varchar"},{"id":"EXTEND_ID","text_left":"EXTEND_ID","text_right":"varchar"},{"id":"MAIN_KM_ID","text_left":"MAIN_KM_ID","text_right":"varchar"}],"id":"f52e7c88-6b58-415d-9053-bcceca0af9c0","z":12,"attrs":{".image-db-status":{"xlinkHref":"./static/img/star.svg"},".options":{"refY":45},".question-text":{"text":"KM_INFO"},".option-KNOWLEDGE_ID":{"transform":"translate(0, 0)","dynamic":true},".option-KNOWLEDGE_ID .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_ID .option-text-left":{"text":"KNOWLEDGE_ID","dynamic":true,"refY":15},".option-KNOWLEDGE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_CODE":{"transform":"translate(0, 30)","dynamic":true},".option-KNOWLEDGE_CODE .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_CODE .option-text-left":{"text":"KNOWLEDGE_CODE","dynamic":true,"refY":15},".option-KNOWLEDGE_CODE .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_NAME":{"transform":"translate(0, 60)","dynamic":true},".option-KNOWLEDGE_NAME .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_NAME .option-text-left":{"text":"KNOWLEDGE_NAME","dynamic":true,"refY":15},".option-KNOWLEDGE_NAME .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-LABEL":{"transform":"translate(0, 90)","dynamic":true},".option-LABEL .option-rect":{"height":30,"dynamic":true},".option-LABEL .option-text-left":{"text":"LABEL","dynamic":true,"refY":15},".option-LABEL .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-OPERATOR":{"transform":"translate(0, 120)","dynamic":true},".option-OPERATOR .option-rect":{"height":30,"dynamic":true},".option-OPERATOR .option-text-left":{"text":"OPERATOR","dynamic":true,"refY":15},".option-OPERATOR .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_STATUS":{"transform":"translate(0, 150)","dynamic":true},".option-KNOWLEDGE_STATUS .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_STATUS .option-text-left":{"text":"KNOWLEDGE_STATUS","dynamic":true,"refY":15},".option-KNOWLEDGE_STATUS .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-LIKE_COUNT":{"transform":"translate(0, 180)","dynamic":true},".option-LIKE_COUNT .option-rect":{"height":30,"dynamic":true},".option-LIKE_COUNT .option-text-left":{"text":"LIKE_COUNT","dynamic":true,"refY":15},".option-LIKE_COUNT .option-text-right":{"text":"number","dynamic":true,"refY":15},".option-FAVORITE_COUNT":{"transform":"translate(0, 210)","dynamic":true},".option-FAVORITE_COUNT .option-rect":{"height":30,"dynamic":true},".option-FAVORITE_COUNT .option-text-left":{"text":"FAVORITE_COUNT","dynamic":true,"refY":15},".option-FAVORITE_COUNT .option-text-right":{"text":"number","dynamic":true,"refY":15},".option-PUBLISH_DATE":{"transform":"translate(0, 240)","dynamic":true},".option-PUBLISH_DATE .option-rect":{"height":30,"dynamic":true},".option-PUBLISH_DATE .option-text-left":{"text":"PUBLISH_DATE","dynamic":true,"refY":15},".option-PUBLISH_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-CREATE_ID":{"transform":"translate(0, 270)","dynamic":true},".option-CREATE_ID .option-rect":{"height":30,"dynamic":true},".option-CREATE_ID .option-text-left":{"text":"CREATE_ID","dynamic":true,"refY":15},".option-CREATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-CREATE_DATE":{"transform":"translate(0, 300)","dynamic":true},".option-CREATE_DATE .option-rect":{"height":30,"dynamic":true},".option-CREATE_DATE .option-text-left":{"text":"CREATE_DATE","dynamic":true,"refY":15},".option-CREATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-UPDATE_ID":{"transform":"translate(0, 330)","dynamic":true},".option-UPDATE_ID .option-rect":{"height":30,"dynamic":true},".option-UPDATE_ID .option-text-left":{"text":"UPDATE_ID","dynamic":true,"refY":15},".option-UPDATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-UPDATE_DATE":{"transform":"translate(0, 360)","dynamic":true},".option-UPDATE_DATE .option-rect":{"height":30,"dynamic":true},".option-UPDATE_DATE .option-text-left":{"text":"UPDATE_DATE","dynamic":true,"refY":15},".option-UPDATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-DELETE_FLAG":{"transform":"translate(0, 390)","dynamic":true},".option-DELETE_FLAG .option-rect":{"height":30,"dynamic":true},".option-DELETE_FLAG .option-text-left":{"text":"DELETE_FLAG","dynamic":true,"refY":15},".option-DELETE_FLAG .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-REVISE_DATE":{"transform":"translate(0, 420)","dynamic":true},".option-REVISE_DATE .option-rect":{"height":30,"dynamic":true},".option-REVISE_DATE .option-text-left":{"text":"REVISE_DATE","dynamic":true,"refY":15},".option-REVISE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-KM_DESCRIBE":{"transform":"translate(0, 450)","dynamic":true},".option-KM_DESCRIBE .option-rect":{"height":30,"dynamic":true},".option-KM_DESCRIBE .option-text-left":{"text":"KM_DESCRIBE","dynamic":true,"refY":15},".option-KM_DESCRIBE .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-FILE":{"transform":"translate(0, 480)","dynamic":true},".option-FILE .option-rect":{"height":30,"dynamic":true},".option-FILE .option-text-left":{"text":"FILE","dynamic":true,"refY":15},".option-FILE .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-process_id":{"transform":"translate(0, 510)","dynamic":true},".option-process_id .option-rect":{"height":30,"dynamic":true},".option-process_id .option-text-left":{"text":"process_id","dynamic":true,"refY":15},".option-process_id .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-OPERATOR_ID":{"transform":"translate(0, 540)","dynamic":true},".option-OPERATOR_ID .option-rect":{"height":30,"dynamic":true},".option-OPERATOR_ID .option-text-left":{"text":"OPERATOR_ID","dynamic":true,"refY":15},".option-OPERATOR_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_TYPE":{"transform":"translate(0, 570)","dynamic":true},".option-KNOWLEDGE_TYPE .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_TYPE .option-text-left":{"text":"KNOWLEDGE_TYPE","dynamic":true,"refY":15},".option-KNOWLEDGE_TYPE .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-PUBLISH_USER":{"transform":"translate(0, 600)","dynamic":true},".option-PUBLISH_USER .option-rect":{"height":30,"dynamic":true},".option-PUBLISH_USER .option-text-left":{"text":"PUBLISH_USER","dynamic":true,"refY":15},".option-PUBLISH_USER .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-EXTEND_ID":{"transform":"translate(0, 630)","dynamic":true},".option-EXTEND_ID .option-rect":{"height":30,"dynamic":true},".option-EXTEND_ID .option-text-left":{"text":"EXTEND_ID","dynamic":true,"refY":15},".option-EXTEND_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-MAIN_KM_ID":{"transform":"translate(0, 660)","dynamic":true},".option-MAIN_KM_ID .option-rect":{"height":30,"dynamic":true},".option-MAIN_KM_ID .option-text-left":{"text":"MAIN_KM_ID","dynamic":true,"refY":15},".option-MAIN_KM_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15}}},{"type":"gwssi.customRec","optionHeight":30,"questionHeight":45,"minWidth":200,"position":{"x":580,"y":120},"size":{"width":200,"height":285},"angle":0,"question":"KM_SORT_RELEVANCE","options":[{"id":"KM_SORT_RELEVANCE_ID","text_left":"KM_SORT_RELEVANCE_ID","text_right":"varchar"},{"id":"KM_SORT_ID","text_left":"KM_SORT_ID","text_right":"varchar"},{"id":"KNOWLEDGE_ID","text_left":"KNOWLEDGE_ID","text_right":"varchar"},{"id":"CREATE_ID","text_left":"CREATE_ID","text_right":"varchar"},{"id":"CREATE_DATE","text_left":"CREATE_DATE","text_right":"date"},{"id":"UPDATE_ID","text_left":"UPDATE_ID","text_right":"varchar"},{"id":"UPDATE_DATE","text_left":"UPDATE_DATE","text_right":"date"},{"id":"DELETE_FLAG","text_left":"DELETE_FLAG","text_right":"varchar"}],"id":"184e4266-f752-457d-817d-89099c484f69","z":13,"attrs":{".options":{"refY":45},".question-text":{"text":"KM_SORT_RELEVAN…"},".option-KM_SORT_RELEVANCE_ID":{"transform":"translate(0, 0)","dynamic":true},".option-KM_SORT_RELEVANCE_ID .option-rect":{"height":30,"dynamic":true},".option-KM_SORT_RELEVANCE_ID .option-text-left":{"text":"KM_SORT_RELEVANCE…","dynamic":true,"refY":15},".option-KM_SORT_RELEVANCE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KM_SORT_ID":{"transform":"translate(0, 30)","dynamic":true},".option-KM_SORT_ID .option-rect":{"height":30,"dynamic":true},".option-KM_SORT_ID .option-text-left":{"text":"KM_SORT_ID","dynamic":true,"refY":15},".option-KM_SORT_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_ID":{"transform":"translate(0, 60)","dynamic":true},".option-KNOWLEDGE_ID .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_ID .option-text-left":{"text":"KNOWLEDGE_ID","dynamic":true,"refY":15},".option-KNOWLEDGE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-CREATE_ID":{"transform":"translate(0, 90)","dynamic":true},".option-CREATE_ID .option-rect":{"height":30,"dynamic":true},".option-CREATE_ID .option-text-left":{"text":"CREATE_ID","dynamic":true,"refY":15},".option-CREATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-CREATE_DATE":{"transform":"translate(0, 120)","dynamic":true},".option-CREATE_DATE .option-rect":{"height":30,"dynamic":true},".option-CREATE_DATE .option-text-left":{"text":"CREATE_DATE","dynamic":true,"refY":15},".option-CREATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-UPDATE_ID":{"transform":"translate(0, 150)","dynamic":true},".option-UPDATE_ID .option-rect":{"height":30,"dynamic":true},".option-UPDATE_ID .option-text-left":{"text":"UPDATE_ID","dynamic":true,"refY":15},".option-UPDATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-UPDATE_DATE":{"transform":"translate(0, 180)","dynamic":true},".option-UPDATE_DATE .option-rect":{"height":30,"dynamic":true},".option-UPDATE_DATE .option-text-left":{"text":"UPDATE_DATE","dynamic":true,"refY":15},".option-UPDATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-DELETE_FLAG":{"transform":"translate(0, 210)","dynamic":true},".option-DELETE_FLAG .option-rect":{"height":30,"dynamic":true},".option-DELETE_FLAG .option-text-left":{"text":"DELETE_FLAG","dynamic":true,"refY":15},".option-DELETE_FLAG .option-text-right":{"text":"varchar","dynamic":true,"refY":15}}},{"type":"standard.Link","source":{"id":"f52e7c88-6b58-415d-9053-bcceca0af9c0"},"target":{"id":"184e4266-f752-457d-817d-89099c484f69"},"id":"f589da26-c9bf-415c-884e-a1295fd0f97a","z":14,"labels":[{"attrs":{"text":{"text":"{"tableKey":"KM_SORT_RELEVANCE","tableComment":"","fks":[{"type":"parentField","value":"KNOWLEDGE_ID","from":"KNOWLEDGE_ID"}],"type":"oneToMany"}","fill":"transparent"},"rect":{"visibility":"hidden"}}},{"attrs":{"text":{"text":"1"}},"position":{"distance":25,"offset":-10}},{"attrs":{"text":{"text":"1..N"}},"position":{"distance":-35,"offset":-15}}],"attrs":{}},{"type":"gwssi.customRec","optionHeight":30,"questionHeight":45,"minWidth":200,"position":{"x":-200,"y":80},"size":{"width":200,"height":345},"angle":0,"question":"KM_CONTENT","options":[{"id":"CONTENT_ID","text_left":"CONTENT_ID","text_right":"varchar"},{"id":"KNOWLEDGE_ID","text_left":"KNOWLEDGE_ID","text_right":"varchar"},{"id":"CONTENT","text_left":"CONTENT","text_right":"clob"},{"id":"CONTENT_HTML","text_left":"CONTENT_HTML","text_right":"clob"},{"id":"CREATE_ID","text_left":"CREATE_ID","text_right":"varchar"},{"id":"CREATE_DATE","text_left":"CREATE_DATE","text_right":"date"},{"id":"UPDATE_ID","text_left":"UPDATE_ID","text_right":"varchar"},{"id":"UPDATE_DATE","text_left":"UPDATE_DATE","text_right":"date"},{"id":"DELETE_FLAG","text_left":"DELETE_FLAG","text_right":"varchar"},{"id":"KM_TYPE","text_left":"KM_TYPE","text_right":"varchar"}],"id":"eb089030-6032-4427-a66f-6860268e110d","z":15,"attrs":{".options":{"refY":45},".question-text":{"text":"KM_CONTENT"},".option-CONTENT_ID":{"transform":"translate(0, 0)","dynamic":true},".option-CONTENT_ID .option-rect":{"height":30,"dynamic":true},".option-CONTENT_ID .option-text-left":{"text":"CONTENT_ID","dynamic":true,"refY":15},".option-CONTENT_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KNOWLEDGE_ID":{"transform":"translate(0, 30)","dynamic":true},".option-KNOWLEDGE_ID .option-rect":{"height":30,"dynamic":true},".option-KNOWLEDGE_ID .option-text-left":{"text":"KNOWLEDGE_ID","dynamic":true,"refY":15},".option-KNOWLEDGE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-CONTENT":{"transform":"translate(0, 60)","dynamic":true},".option-CONTENT .option-rect":{"height":30,"dynamic":true},".option-CONTENT .option-text-left":{"text":"CONTENT","dynamic":true,"refY":15},".option-CONTENT .option-text-right":{"text":"clob","dynamic":true,"refY":15},".option-CONTENT_HTML":{"transform":"translate(0, 90)","dynamic":true},".option-CONTENT_HTML .option-rect":{"height":30,"dynamic":true},".option-CONTENT_HTML .option-text-left":{"text":"CONTENT_HTML","dynamic":true,"refY":15},".option-CONTENT_HTML .option-text-right":{"text":"clob","dynamic":true,"refY":15},".option-CREATE_ID":{"transform":"translate(0, 120)","dynamic":true},".option-CREATE_ID .option-rect":{"height":30,"dynamic":true},".option-CREATE_ID .option-text-left":{"text":"CREATE_ID","dynamic":true,"refY":15},".option-CREATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-CREATE_DATE":{"transform":"translate(0, 150)","dynamic":true},".option-CREATE_DATE .option-rect":{"height":30,"dynamic":true},".option-CREATE_DATE .option-text-left":{"text":"CREATE_DATE","dynamic":true,"refY":15},".option-CREATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-UPDATE_ID":{"transform":"translate(0, 180)","dynamic":true},".option-UPDATE_ID .option-rect":{"height":30,"dynamic":true},".option-UPDATE_ID .option-text-left":{"text":"UPDATE_ID","dynamic":true,"refY":15},".option-UPDATE_ID .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-UPDATE_DATE":{"transform":"translate(0, 210)","dynamic":true},".option-UPDATE_DATE .option-rect":{"height":30,"dynamic":true},".option-UPDATE_DATE .option-text-left":{"text":"UPDATE_DATE","dynamic":true,"refY":15},".option-UPDATE_DATE .option-text-right":{"text":"date","dynamic":true,"refY":15},".option-DELETE_FLAG":{"transform":"translate(0, 240)","dynamic":true},".option-DELETE_FLAG .option-rect":{"height":30,"dynamic":true},".option-DELETE_FLAG .option-text-left":{"text":"DELETE_FLAG","dynamic":true,"refY":15},".option-DELETE_FLAG .option-text-right":{"text":"varchar","dynamic":true,"refY":15},".option-KM_TYPE":{"transform":"translate(0, 270)","dynamic":true},".option-KM_TYPE .option-rect":{"height":30,"dynamic":true},".option-KM_TYPE .option-text-left":{"text":"KM_TYPE","dynamic":true,"refY":15},".option-KM_TYPE .option-text-right":{"text":"varchar","dynamic":true,"refY":15}}},{"type":"standard.Link","source":{"id":"f52e7c88-6b58-415d-9053-bcceca0af9c0"},"target":{"id":"eb089030-6032-4427-a66f-6860268e110d"},"id":"4968d1a8-3df4-42a5-b4d6-34cde5f185df","z":16,"labels":[{"attrs":{"text":{"text":"{"tableKey":"KM_CONTENT","tableComment":"","fks":[{"type":"parentField","value":"KNOWLEDGE_ID","from":"KNOWLEDGE_ID"}],"type":"oneToOne"}","fill":"transparent"},"rect":{"visibility":"hidden"}}},{"attrs":{"text":{"text":"1"}},"position":{"distance":25,"offset":-10}},{"attrs":{"text":{"text":"1"}},"position":{"distance":-35,"offset":-10}}],"attrs":{}}]}', '0', NULL);


INSERT INTO form_def (id_, type_, key_, name_, desc_, group_id_, group_name_, bo_key_, bo_name_, html_, create_time_, create_by_, creator_, update_time_, update_by_, updator_, version_, delete_, org_id_, lock_time_, lock_by_, modify_desc_) VALUES('891037832991735809', 'pc_iview', 'zswh', '知识维护', '知识维护', '881784191580635137', '统一运维管理平台', 'zswh', '知识维护', '<script>window.custFormComponentMixin = {
  data: function () {
    return {
      whxxShow: false,
      renewNum: 0,
      beforeData: {},
      afterData: {},
      nodeId: '''',
      isTask: window.location.pathname.includes(''/task.html'')
    };
  },
  computed: {
    showAuditComparison: function(){
      let flag = this.isTask && this.nodeId === ''zskwh-UserTask2'' && this.afterData.KNOWLEDGE_NAME;
      let flag_1 = this.isTask && this.nodeId === ''zswh_cq-UserTask2''
      return  flag||flag_1
    }
  },
  created: function(){
    console.log("混入对象的自定义钩子被调用");
    this.getKmData();
    this.getWhxxShow();
    this.getDiffByProId();
    this.getWorkOrderData();
    this.getBeforeData()
  },
  mounted: function () {
    // var _this = this;
    // var formMain = this.getFormMain(this);
    // if (formMain.instance && formMain.instanceStatus == "end") {
    //   formMain.buttons.unshift({
    //     afterScript: "",
    //     alias: "instanceRestart",
    //     beforeScript: "",
    //     configPage: "",
    //     groovyScript: "",
    //     name: "修改",
    //   });
    // }
    var formMain = this.getFormMain(this);
    this.nodeId = formMain.nodeId;
    var radios = formMain.radioButtons;
    const searchData = window.location.search.split(''&'');
    const _this = this;
    let kmId = ''''
    searchData.forEach(e => {
      if (e.includes(''kmId='')) kmId = e.split(''='')[1];
    })
    const getShow = Vue.abHttpUtil.get("uomp/km_info/isMainKm", {kmid:kmId});
    getShow.then(function(res){
      if(formMain.instanceStatus == ''end''&&res.data){
        radios.push(
          {
            id: "historicalVersion",
            name: "历史版本",
          },
          {
            id: "referenceRecord",
            name: "引用记录"
          }
        );
      }
    })
    var style = document.createElement("style");
    style.textContent = ".piece-all-modal .ivu-modal { width: 80% !important;}";
    document.head.appendChild(style);
  },
  methods:{
    getWorkOrderData() {
      const searchData = window.location.search.split(''&'');
      const isStart = window.location.pathname.includes(''start.html'');
      const _this = this;
      let workOrderId = '''';
      searchData.forEach(e => {
        if (e.includes(''workOrderId='')) workOrderId = e.split(''='')[1];
      })
      if (isStart && workOrderId) {
        const params = {
          instanceId: workOrderId
        }
        const getInstanceData = Vue.abHttpUtil.get("bpm/instance/getInstanceData", params);
        getInstanceData.then(function(res) {
          if (res.isOk) {
            if(res.data.data.UOMP_ISSUES) {
              const workOrderData = res.data.data.UOMP_ISSUES;
              _this.setDataValue(''zswh'', ''KNOWLEDGE_NAME'', workOrderData.ISSUES_TITLE);
              _this.setDataValue(''zswh'', ''FILE'', workOrderData.FILE_STR);
              _this.setDataValue(''_OneToOne_KM_CONTENT'', ''CONTENT_HTML'', workOrderData.ISSUES_DESC + ''<hr>'' + (workOrderData.UOMP_ISSUES_HANDLLING.HANDLE_LOG || ''''));
              _this.getComponent(''_OneToOne_KM_CONTENT'',''CONTENT_HTML'').setContent(workOrderData.ISSUES_DESC + ''<hr>'' + (workOrderData.UOMP_ISSUES_HANDLLING.HANDLE_LOG || ''''))
              _this.setDataValue(''zswh'', ''FILE'', workOrderData.FILE_STR);
            }
            if(res.data.data.UOMP_EVENT) {
              const workOrderData = res.data.data.UOMP_EVENT;
              _this.setDataValue(''zswh'', ''KNOWLEDGE_NAME'', workOrderData.EVENT_TITLE);
              _this.setDataValue(''_OneToOne_KM_CONTENT'', ''CONTENT_HTML'', workOrderData.EVENT_DESC + ''<hr>'' + (workOrderData.UOMP_EVENT_HANDLLING.HANDLE_LOG || ''''));
              _this.getComponent(''_OneToOne_KM_CONTENT'',''CONTENT_HTML'').setContent( workOrderData.EVENT_DESC + ''<hr>'' + (workOrderData.UOMP_EVENT_HANDLLING.HANDLE_LOG || ''''))
              _this.setDataValue(''zswh'', ''FILE'', workOrderData.FILE_STR);
            }
            if(res.data.data.UOMP_ALTER) {
              const workOrderData = res.data.data.UOMP_ALTER;
              _this.setDataValue(''zswh'', ''KNOWLEDGE_NAME'', workOrderData.ALTER_TITLE);
              _this.setDataValue(''_OneToOne_KM_CONTENT'', ''CONTENT_HTML'', workOrderData.ALTER_DESC + ''<hr>'');
              _this.getComponent(''_OneToOne_KM_CONTENT'',''CONTENT_HTML'').setContent(workOrderData.ALTER_DESC + ''<hr>'')
              _this.setDataValue(''zswh'', ''FILE'', workOrderData.FILE_STR);
            }
            console.log(''看知识维护回显工单的数据：'', this.customForm.data.zswh)
          }
        }, function(status) {
          // obj.$Message.error(''获取工单数据失败'' , status);
        })
      }
    },
    //更新的时候需要把之前的值赋上
    getBeforeData(){
      const searchData = window.location.search.split(''&'');
      const isStart = window.location.pathname.includes(''bpm/start.html'');
      const _this = this;
      let kmId = ''''
      searchData.forEach(e => {
        if (e.includes(''zswh.MAIN_KM_ID='')) kmId = e.split(''='')[1];
      })
      if(isStart && kmId){
        const params = {
          KM_ID: kmId
        }
        const getByKmData = Vue.abHttpUtil.post("uomp/km_info/queryByKmId", params);
        getByKmData.then(function(res) {
          if (res.isOk) {
            _this.setDataValue(''zswh'', ''KNOWLEDGE_NAME'', res.data.KNOWLEDGE_NAME);
            _this.setDataValue(''_OneToOne_KM_CONTENT'', ''CONTENT_HTML'', res.data.CONTENT_HTML);
            _this.getComponent(''_OneToOne_KM_CONTENT'',''CONTENT_HTML'').setContent(res.data.CONTENT_HTML)
          }
        })
      }
    },
    // 新增节点，有kmid值，则获导入的数据，并给表单赋值
    getKmData() {
      const searchData = window.location.search.split(''&'');
      const isStart = window.location.pathname.includes(''bpm/start.html'');
      const _this = this;
      let kmId = ''''
      searchData.forEach(e => {
        if (e.includes(''kmId='')) kmId = e.split(''='')[1];
      })
      if (isStart && kmId) {
        const params = {
          KM_ID: kmId
        }
        const getByKmData = Vue.abHttpUtil.post("uomp/km_info/queryByKmId", params);
        getByKmData.then(function(res) {
          if (res.isOk) {
            if(!res.data.processId&&kmId){
              //处理导入数据的特殊问题
              _this.setDataValue(''zswh'', ''KNOWLEDGE_ID'', res.data.KNOWLEDGE_ID);
              _this.setDataValue(''zswh'', ''KNOWLEDGE_TYPE'', res.data.KNOWLEDGE_TYPE)
              _this.getComponent(''zswh'',''KNOWLEDGE_TYPE'').val = res.data.KNOWLEDGE_TYPE.split('','')
              _this.getComponent(''zswh'',''KNOWLEDGE_TYPE'').setValue(res.data.KNOWLEDGE_TYPE)
            }
            _this.setDataValue(''zswh'', ''KNOWLEDGE_NAME'', res.data.KNOWLEDGE_NAME);
            // this.customForm.data.zswh.KM_CONTENT.CONTENT_HTML = res.data.CONTENT_HTML;
            _this.setDataValue(''_OneToOne_KM_CONTENT'', ''CONTENT_HTML'', res.data.CONTENT_HTML);
            _this.getComponent(''_OneToOne_KM_CONTENT'',''CONTENT_HTML'').setContent(res.data.CONTENT_HTML)
            if (res.data.LABEL) {
              this.customForm.data.zswh.LABEL = res.data.LABEL;
              const labelData = res.data.LABEL.split('','');
              labelData.forEach(e => {
                this.customForm.getComponent(''zswh'', ''LABEL'').options.push({
                  txt: e,
                  key: e
                });
              })
            }
          }
        }, function(status) {
          // obj.$Message.error(''获取变更前后数据失败'' , status);
        });
      }
    },
    getDiffByProId() {
      if (!this.isTask) return
      let _this = this;
      const searchData = window.location.search.split(''&'');
      let  processId = '''';
      searchData.forEach(e => {
        if(e.includes(''instanceId='')) processId = e.split(''='')[1];
      });
      const params = {
        process_id: processId
      };
      const get = Vue.abHttpUtil.get("uomp/km_info/getDiffByProId", params);
      get.then(function(res) {
        _this.beforeData = res.data.before;
        _this.afterData = res.data.after;
      }, function(status) {
        obj.$Message.error(''获取变更前后数据失败'' , status);
      });
    },
    getRenewNum() {
      let _this = this;
      const searchData = window.location.search.split(''&'');
      let  km_id = '''';
      searchData.forEach(e => {
        if(e.includes(''kmId='')) km_id = e.split(''='')[1];
      });
      const params = {
        km_id,
      };
      const get = Vue.abHttpUtil.get("uomp/km_info/queryHistoryInfo", params);
      get.then(function(res) {
        _this.renewNum = res.total;
      }, function(status) {
        obj.$Message.error(''获取更新次数失败'' , status);
      });
    },
    getWhxxShow() {
      var formMain = this.getFormMain(this);
      if(window.location.href.includes(''bpm/instance.html'') && formMain.instanceStatus == ''end'') {
        this.whxxShow = true;
        this.getRenewNum();
      } else {
        this.whxxShow = false;
      }
    }
  }
};
</script>
<gw-css class="gw-components" id="gw-css-form_style" column-function="css" type="form_common_css" csskey="form_style">公共样式：form公共样式(form_style)</gw-css>
<style form-id="zswh"></style>
<div class="form-main">
<div style="cursor: pointer;" class="sub-title uomp-form-title margin-t-0">基本信息</div>
<table class="table-style" border="1">
<tbody>
<tr>
<td class="td-style td-w20" style="text-align: right;">编号</td>
<td class="td-style2 td-w30"><gw-serialno column-id="890990116516921345" column-comment="知识编码" column-key="KNOWLEDGE_CODE" bo-key="zswh" is-title="" native-bo-key="zswh" table-key="KM_INFO" column-type="serialno" field-type="varchar" column-length="50" column-placeholder="" column-defaultvalue="" column-options="auto" column-valid-rule="" column-serialno-alias="UOMP_KM_INFO_NO" column-validate="[]" column-watch-rule="" column-valid-length="" column-created-fun="obj.setPermission(''r'')" column-script="true" doc-domain="" column-style="" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-number "></span> 知识编码|流水号</gw-text></gw-serialno></td>
<td class="td-style td-w20" style="text-align: right;"><strong class="req">*</strong>名称</td>
<td class="td-style2"><gw-onetext column-id="890990116520067073" column-comment="知识名称" column-key="KNOWLEDGE_NAME" bo-key="zswh" is-title="" native-bo-key="zswh" table-key="KM_INFO" column-type="onetext" field-type="varchar" column-length="300" column-placeholder="" column-defaultvalue="" column-valid-rule="required" column-validate="[{&quot;rule&quot;:&quot;function (v) {\\n    console.log(v, v.length);\\n    if (v.length &gt; 100) {\\n        return false;\\n\\t\\t}\\n    return true;\\n}&quot;,&quot;msg&quot;:&quot;名称不得多于100个字，请缩减名称&quot;}]" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="true" doc-domain="" column-style="" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-onetext "></span> 知识名称|单行文本</gw-text></gw-onetext></td>
</tr>
<tr>
<td class="td-style" style="text-align: right;"><strong class="req">*</strong>知识分类</td>
<td class="td-style2"><gw-cascader column-id="891078555401191425" column-comment="知识分类" column-key="KNOWLEDGE_TYPE" bo-key="zswh" is-title="" native-bo-key="zswh" table-key="KM_INFO" column-type="cascader" field-type="varchar" column-length="64" column-placeholder="" column-defaultvalue="" column-valid-rule="required" column-validate="[]" column-watch-rule="" column-valid-length="" column-created-fun="const searchData = window.location.search.split(''&amp;'');
let repositoryId = '''';
let kmSortId = '''';
searchData.forEach(e =&gt; {
  if(e.includes(''kmSortId='')) kmSortId = e.split(''='')[1];
  if(e.includes(''repositoryId='')) repositoryId = e.split(''='')[1];
});
const params = {
  repositoryId: repositoryId
};
function getData(data) {
  data.forEach(e =&gt; {
    // e.children = e.childrens;
    if(e.childrens.length &amp;&amp; e.childrens.length &gt; 0) {
      e.children = e.childrens;
      getData(e.childrens);
    }
  });
}
// 新增时，有全部范围 当前选中为默认选中, 采用kmSortId
if(repositoryId &amp;&amp; kmSortId &amp;&amp; obj.val.length &lt;= 0) {
  let get = Vue.abHttpUtil.get(&quot;km_sort_info/queryKmSortTree&quot;, params);
  get.then(function(res) {
    getData([res.data]);
    obj.sourceData = [res.data];
  }, function(status) {
    obj.$Message.error(''提交失败'' , status);
  });
  // 根据默认选中数据  获取根节点到当前节点的code值
  const queryCodeByIdParams = {
    sort_id: kmSortId
  }
  let queryCodeById = Vue.abHttpUtil.get(&quot;uomp/km_info/queryCodeById&quot;, queryCodeByIdParams);
  queryCodeById.then(function(res) {
    if(!obj.parent.data.zswh.KNOWLEDGE_TYPE){
      obj.val = res.data.split('','')
      obj.setValue(res.data);
    }
  }, function(status) {
    obj.$Message.error(''获取默认选中数据失败'' , status);
  });
  // 有全部范围，但是已经有选中的数据了
} else if (repositoryId &amp;&amp; obj.val.length &gt; 0) {
  let get = Vue.abHttpUtil.get(&quot;km_sort_info/queryKmSortTree&quot;, params);
  get.then(function(res) {
    getData([res.data]);
    obj.sourceData = [res.data];
  }, function(status) {
    obj.$Message.error(''获取全部知识分类数据失败'' , status);
  });
} else {
  const querySortIdByCodeParams = {
    KNOWLEDGE_TYPE: obj.val[0]
  };
  let querySortIdByCode = Vue.abHttpUtil.get(&quot;uomp/km_info/querySortIdByCode&quot;, querySortIdByCodeParams);
  querySortIdByCode.then(function(res) {
    const queryKmSortTreeParams = {
      repositoryId: res.data
    };
    let queryKmSortTree = Vue.abHttpUtil.get(&quot;km_sort_info/queryKmSortTree&quot;, queryKmSortTreeParams);
    queryKmSortTree.then(function(res) {
      getData([res.data]);
      obj.sourceData = [res.data];
    }, function(status) {
      obj.$Message.error(''获取知识分类失败'' , status);
    });
  }, function(status) {
    obj.$Message.error(''获取知识分类失败'' , status);
  });
}
" column-script="true" doc-domain="" column-style="" column-extends="{&quot;baseUrl&quot;:&quot;&quot;,&quot;source&quot;:&quot; &quot;,&quot;root&quot;:&quot;&quot;,&quot;processing&quot;:&quot;&quot;}" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-extend "></span> 知识分类|级联选择器</gw-text></gw-cascader></td>
<td class="td-style " style="text-align: right;">标签</td>
<td class="td-style2 "><gw-vue-custom-tag tag-name="gw-app-zswh-label" properties="{
  &quot;zd_key&quot;: &quot;LABEL&quot;,
    &quot;stbm&quot;: &quot;KM_INFO&quot;,
      &quot;dx_key&quot;: &quot;zswh&quot;
}
" tag-change="" tag-id="397050921495304886" column-function="vue-custom-tag" hidefocus="true" title="点击标签，右侧面板修改标签属性。" tabindex="0"><gw-text class="tips"> 自定义标签 ：gw-app-zswh-label | tagID ：397050921495304886</gw-text></gw-vue-custom-tag></td>
</tr>
<tr>
<td class="td-style " style="text-align: right;">附件上传</td>
<td class="td-style2 "><gw-file column-id="890990116549427201" column-comment="附件" column-key="FILE" bo-key="zswh" is-title="" native-bo-key="zswh" table-key="KM_INFO" column-type="file" field-type="varchar" column-length="3000" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-file-format="" column-file-size="20M" column-file-total-size="" column-validate="[]" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" doc-domain="" column-style="" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-fj "></span> 附件|附件上传</gw-text></gw-file></td>
<td class="td-style " style="text-align: right;">知识来源</td>
<td class="td-style2 "><gw-vue-custom-tag tag-name="gw-zhwh-select-km" properties="" tag-id="384921246359133539" column-function="vue-custom-tag" hidefocus="true" title="点击标签，右侧面板修改标签属性。" tabindex="0"><gw-text class="tips"> 自定义标签 ：gw-zhwh-select-km | tagID ：384921246359133539 </gw-text></gw-vue-custom-tag></td>
</tr>
<tr>
<td class="td-style " style="text-align: right;"><strong class="req">*</strong>知识描述</td>
<td class="td-style2 " colspan="3">
<div><gw-tinymce column-id="891037059784900609" column-comment="知识描述" column-key="CONTENT_HTML" bo-key="_OneToOne_KM_CONTENT" is-title="" native-bo-key="zswh" table-key="KM_CONTENT" column-type="tinymce" field-type="clob" column-length="0" column-placeholder="" column-defaultvalue="" column-valid-rule="required" column-validate="[]" column-watch-rule="function (newValue, oldvalue, obj) {
  if(newValue &amp;&amp; [&quot;b&quot;,&quot;w&quot;].includes(obj.parent.permission.zswh.KM_CONTENT.CONTENT_HTML)) {
    obj.parent.data.zswh.KM_CONTENT.CONTENT = newValue.replace(/&lt;[^&gt;]+&gt;/g, &quot;&quot;);
    obj.parent.data.zswh.KM_CONTENT.KM_TYPE = ''05'';
    console.log(''看-知识维护-知识描述-监听-判断内'', obj.parent.data.zswh.KM_CONTENT.CONTENT_HTML)
  }
}
" column-valid-length="" column-created-fun="console.log(''看-知识描述-初始化：'', obj.parent.data.zswh.KM_CONTENT.CONTENT_HTML);" column-script="true" doc-domain="" column-style="" column-extends="{&quot;width&quot;:&quot;&quot;,&quot;height&quot;:&quot;&quot;,&quot;uploadUrl&quot;:&quot;&quot;,&quot;fontSize&quot;:&quot;18px&quot;}" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-extend "></span> 富文本带标签|富文本&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</gw-text></gw-tinymce></div>
</td>
</tr>
</tbody>
</table>
<div v-if="whxxShow">
<div style="cursor: pointer;" class="sub-title uomp-form-title margin-t-0">维护信息</div>
<table class="table-style" border="1">
<tbody>
<tr>
<td class="td-style td-w20" style="text-align: right;">点赞次数</td>
<td class="td-style2 td-w30"><gw-onetext column-id="890990116528455681" column-comment="点赞数" column-key="LIKE_COUNT" bo-key="zswh" native-bo-key="zswh" table-key="KM_INFO" column-type="onetext" field-type="number" column-length="0" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-validate="&quot;&quot;" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-onetext "></span> 点赞数|单行文本</gw-text></gw-onetext></td>
<td class="td-style td-w20" style="text-align: right;">收藏次数</td>
<td class="td-style2"><gw-onetext column-id="890990116530028545" column-comment="收藏数" column-key="FAVORITE_COUNT" bo-key="zswh" native-bo-key="zswh" table-key="KM_INFO" column-type="onetext" field-type="number" column-length="0" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-validate="&quot;&quot;" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-onetext "></span> 收藏数|单行文本</gw-text></gw-onetext></td>
</tr>
<tr>
<td class="td-style td-w20" style="text-align: right;">操作人</td>
<td class="td-style2"><gw-onetext column-id="890990116524261377" column-comment="操作人" column-key="OPERATOR" bo-key="zswh" native-bo-key="zswh" table-key="KM_INFO" column-type="onetext" field-type="varchar" column-length="50" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-validate="&quot;&quot;" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-onetext "></span> 操作人|单行文本</gw-text></gw-onetext></td>
<td class="td-style td-w20" style="text-align: right;">创建时间</td>
<td class="td-style2 td-w30"><gw-date column-id="890990116536320001" column-comment="创建时间" column-key="CREATE_DATE" bo-key="zswh" native-bo-key="zswh" table-key="KM_INFO" column-type="date" field-type="date" column-length="50" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-date-format="yyyy-MM-dd HH:mm:ss" column-validate="&quot;&quot;" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-sj "></span> 创建时间|日期选择器</gw-text></gw-date></td>
</tr>
<tr>
<td class="td-style td-w20" style="text-align: right;">发布时间</td>
<td class="td-style2" colspan="3"><gw-date column-id="890990116532125697" column-comment="发布时间" column-key="PUBLISH_DATE" bo-key="zswh" native-bo-key="zswh" table-key="KM_INFO" column-type="date" field-type="date" column-length="50" column-placeholder="" column-defaultvalue="" column-valid-rule="" column-date-format="yyyy-MM-dd HH:mm:ss" column-validate="&quot;&quot;" column-watch-rule="" column-valid-length="" column-created-fun="" column-script="false" relation-type="oneToOne" class="gw-components"><gw-text><span class="icon iconfont icon-page-sj "></span> 发布时间|日期选择器</gw-text></gw-date></td>
<!--<td class="td-style td-w20" style="text-align: right;">更新次数</td>
<td class="td-style2">
<div style="background: #f2f2f2; padding-left: 10px; height: 32px; line-height: 32px;">{{renewNum}}</div>
</td>--></tr>
</tbody>
</table>
</div>
<div v-if="showAuditComparison">
<div style="cursor: pointer;" class="sub-title uomp-form-title margin-t-0">审核比对</div>
<div v-if="afterData.KNOWLEDGE_NAME"><gw-vue-custom-tag tag-name="gw-app-compare-table" properties="" tag-change="" tag-id="496602904261778382" column-function="vue-custom-tag" hidefocus="true" title="点击标签，右侧面板修改标签属性。" tabindex="0"><gw-text class="tips"> 自定义标签 ：gw-app-compare-table | tagID ：496602904261778382 </gw-text></gw-vue-custom-tag>
<div></div>
&nbsp;</div>
</div>
</div><vue-render name="预编译vue组件">function anonymous(
) {
with(this){return _c(''i-form'',{ref:"customIForm",staticClass:"zswh gw-custom-i-form-wrapper",attrs:{"id":"gw-custom-i-form","model":data,"rules":ruleValidate}},[_c(''gw-css'',{staticClass:"gw-components",attrs:{"id":"gw-css-form_style","column-function":"css","type":"form_common_css","csskey":"form_style"}},[_v("公共样式：form公共样式(form_style)")]),_v(" "),_c(''div'',{staticClass:"form-main"},[_c(''div'',{staticClass:"sub-title uomp-form-title margin-t-0",staticStyle:{"cursor":"pointer"}},[_v("基本信息")]),_v(" "),_c(''table'',{staticClass:"table-style",attrs:{"border":"1"}},[_c(''tbody'',[_c(''tr'',[_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("编号")]),_v(" "),_c(''td'',{staticClass:"td-style2 td-w30"},[_c(''gw-serialno'',{staticClass:"gw-components",attrs:{"column-id":"890990116516921345","column-comment":"知识编码","column-key":"KNOWLEDGE_CODE","bo-key":"zswh","is-title":"","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"serialno","field-type":"varchar","column-length":"50","column-placeholder":"","column-defaultvalue":"","column-options":"auto","column-valid-rule":"","column-serialno-alias":"UOMP_KM_INFO_NO","column-validate":"[]","column-watch-rule":"","column-valid-length":"","column-created-fun":"obj.setPermission(''r'')","column-script":"true","doc-domain":"","column-style":"","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-number "}),_v(" 知识编码|流水号")])],1)],1),_v(" "),_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_c(''strong'',{staticClass:"req"},[_v("*")]),_v("名称")]),_v(" "),_c(''td'',{staticClass:"td-style2"},[_c(''gw-onetext'',{staticClass:"gw-components",attrs:{"column-id":"890990116520067073","column-comment":"知识名称","column-key":"KNOWLEDGE_NAME","bo-key":"zswh","is-title":"","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"onetext","field-type":"varchar","column-length":"300","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"required","column-validate":"[{\\"rule\\":\\"function (v) {\\\\n    console.log(v, v.length);\\\\n    if (v.length > 100) {\\\\n        return false;\\\\n\\\\t\\\\t}\\\\n    return true;\\\\n}\\",\\"msg\\":\\"名称不得多于100个字，请缩减名称\\"}]","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"true","doc-domain":"","column-style":"","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-onetext "}),_v(" 知识名称|单行文本")])],1)],1)]),_v(" "),_c(''tr'',[_c(''td'',{staticClass:"td-style",staticStyle:{"text-align":"right"}},[_c(''strong'',{staticClass:"req"},[_v("*")]),_v("知识分类")]),_v(" "),_c(''td'',{staticClass:"td-style2"},[_c(''gw-cascader'',{staticClass:"gw-components",attrs:{"column-id":"891078555401191425","column-comment":"知识分类","column-key":"KNOWLEDGE_TYPE","bo-key":"zswh","is-title":"","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"cascader","field-type":"varchar","column-length":"64","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"required","column-validate":"[]","column-watch-rule":"","column-valid-length":"","column-created-fun":"const searchData = window.location.search.split(''&'');\\nlet repositoryId = '''';\\nlet kmSortId = '''';\\nsearchData.forEach(e => {\\n  if(e.includes(''kmSortId='')) kmSortId = e.split(''='')[1];\\n  if(e.includes(''repositoryId='')) repositoryId = e.split(''='')[1];\\n});\\nconst params = {\\n  repositoryId: repositoryId\\n};\\nfunction getData(data) {\\n  data.forEach(e => {\\n    // e.children = e.childrens;\\n    if(e.childrens.length && e.childrens.length > 0) {\\n      e.children = e.childrens;\\n      getData(e.childrens);\\n    }\\n  });\\n}\\n// 新增时，有全部范围 当前选中为默认选中, 采用kmSortId\\nif(repositoryId && kmSortId && obj.val.length <= 0) {\\n  let get = Vue.abHttpUtil.get(\\"km_sort_info/queryKmSortTree\\", params);\\n  get.then(function(res) {\\n    getData([res.data]);\\n    obj.sourceData = [res.data];\\n  }, function(status) {\\n    obj.$Message.error(''提交失败'' , status);\\n  });\\n  // 根据默认选中数据  获取根节点到当前节点的code值\\n  const queryCodeByIdParams = {\\n    sort_id: kmSortId\\n  }\\n  let queryCodeById = Vue.abHttpUtil.get(\\"uomp/km_info/queryCodeById\\", queryCodeByIdParams);\\n  queryCodeById.then(function(res) {\\n    if(!obj.parent.data.zswh.KNOWLEDGE_TYPE){\\n      obj.val = res.data.split('','')\\n      obj.setValue(res.data);\\n    }\\n  }, function(status) {\\n    obj.$Message.error(''获取默认选中数据失败'' , status);\\n  });\\n  // 有全部范围，但是已经有选中的数据了\\n} else if (repositoryId && obj.val.length > 0) {\\n  let get = Vue.abHttpUtil.get(\\"km_sort_info/queryKmSortTree\\", params);\\n  get.then(function(res) {\\n    getData([res.data]);\\n    obj.sourceData = [res.data];\\n  }, function(status) {\\n    obj.$Message.error(''获取全部知识分类数据失败'' , status);\\n  });\\n} else {\\n  const querySortIdByCodeParams = {\\n    KNOWLEDGE_TYPE: obj.val[0]\\n  };\\n  let querySortIdByCode = Vue.abHttpUtil.get(\\"uomp/km_info/querySortIdByCode\\", querySortIdByCodeParams);\\n  querySortIdByCode.then(function(res) {\\n    const queryKmSortTreeParams = {\\n      repositoryId: res.data\\n    };\\n    let queryKmSortTree = Vue.abHttpUtil.get(\\"km_sort_info/queryKmSortTree\\", queryKmSortTreeParams);\\n    queryKmSortTree.then(function(res) {\\n      getData([res.data]);\\n      obj.sourceData = [res.data];\\n    }, function(status) {\\n      obj.$Message.error(''获取知识分类失败'' , status);\\n    });\\n  }, function(status) {\\n    obj.$Message.error(''获取知识分类失败'' , status);\\n  });\\n}\\n","column-script":"true","doc-domain":"","column-style":"","column-extends":"{\\"baseUrl\\":\\"\\",\\"source\\":\\" \\",\\"root\\":\\"\\",\\"processing\\":\\"\\"}","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-extend "}),_v(" 知识分类|级联选择器")])],1)],1),_v(" "),_c(''td'',{staticClass:"td-style ",staticStyle:{"text-align":"right"}},[_v("标签")]),_v(" "),_c(''td'',{staticClass:"td-style2 "},[_c(''gw-vue-custom-tag'',{attrs:{"tag-name":"gw-app-zswh-label","properties":"{\\n  \\"zd_key\\": \\"LABEL\\",\\n    \\"stbm\\": \\"KM_INFO\\",\\n      \\"dx_key\\": \\"zswh\\"\\n}\\n","tag-change":"","tag-id":"397050921495304886","column-function":"vue-custom-tag","hidefocus":"true","title":"点击标签，右侧面板修改标签属性。","tabindex":"0"}},[_c(''gw-text'',{staticClass:"tips"},[_v(" 自定义标签 ：gw-app-zswh-label | tagID ：397050921495304886")])],1)],1)]),_v(" "),_c(''tr'',[_c(''td'',{staticClass:"td-style ",staticStyle:{"text-align":"right"}},[_v("附件上传")]),_v(" "),_c(''td'',{staticClass:"td-style2 "},[_c(''gw-file'',{staticClass:"gw-components",attrs:{"column-id":"890990116549427201","column-comment":"附件","column-key":"FILE","bo-key":"zswh","is-title":"","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"file","field-type":"varchar","column-length":"3000","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-file-format":"","column-file-size":"20M","column-file-total-size":"","column-validate":"[]","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","doc-domain":"","column-style":"","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-fj "}),_v(" 附件|附件上传")])],1)],1),_v(" "),_c(''td'',{staticClass:"td-style ",staticStyle:{"text-align":"right"}},[_v("知识来源")]),_v(" "),_c(''td'',{staticClass:"td-style2 "},[_c(''gw-vue-custom-tag'',{attrs:{"tag-name":"gw-zhwh-select-km","properties":"","tag-id":"384921246359133539","column-function":"vue-custom-tag","hidefocus":"true","title":"点击标签，右侧面板修改标签属性。","tabindex":"0"}},[_c(''gw-text'',{staticClass:"tips"},[_v(" 自定义标签 ：gw-zhwh-select-km | tagID ：384921246359133539 ")])],1)],1)]),_v(" "),_c(''tr'',[_c(''td'',{staticClass:"td-style ",staticStyle:{"text-align":"right"}},[_c(''strong'',{staticClass:"req"},[_v("*")]),_v("知识描述")]),_v(" "),_c(''td'',{staticClass:"td-style2 ",attrs:{"colspan":"3"}},[_c(''div'',[_c(''gw-tinymce'',{staticClass:"gw-components",attrs:{"column-id":"891037059784900609","column-comment":"知识描述","column-key":"CONTENT_HTML","bo-key":"_OneToOne_KM_CONTENT","is-title":"","native-bo-key":"zswh","table-key":"KM_CONTENT","column-type":"tinymce","field-type":"clob","column-length":"0","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"required","column-validate":"[]","column-watch-rule":"function (newValue, oldvalue, obj) {\\n  if(newValue && [\\"b\\",\\"w\\"].includes(obj.parent.permission.zswh.KM_CONTENT.CONTENT_HTML)) {\\n    obj.parent.data.zswh.KM_CONTENT.CONTENT = newValue.replace(/<[^>]+>/g, \\"\\");\\n    obj.parent.data.zswh.KM_CONTENT.KM_TYPE = ''05'';\\n    console.log(''看-知识维护-知识描述-监听-判断内'', obj.parent.data.zswh.KM_CONTENT.CONTENT_HTML)\\n  }\\n}\\n","column-valid-length":"","column-created-fun":"console.log(''看-知识描述-初始化：'', obj.parent.data.zswh.KM_CONTENT.CONTENT_HTML);","column-script":"true","doc-domain":"","column-style":"","column-extends":"{\\"width\\":\\"\\",\\"height\\":\\"\\",\\"uploadUrl\\":\\"\\",\\"fontSize\\":\\"18px\\"}","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-extend "}),_v(" 富文本带标签|富文本           ")])],1)],1)])])])]),_v(" "),(whxxShow)?_c(''div'',[_c(''div'',{staticClass:"sub-title uomp-form-title margin-t-0",staticStyle:{"cursor":"pointer"}},[_v("维护信息")]),_v(" "),_c(''table'',{staticClass:"table-style",attrs:{"border":"1"}},[_c(''tbody'',[_c(''tr'',[_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("点赞次数")]),_v(" "),_c(''td'',{staticClass:"td-style2 td-w30"},[_c(''gw-onetext'',{staticClass:"gw-components",attrs:{"column-id":"890990116528455681","column-comment":"点赞数","column-key":"LIKE_COUNT","bo-key":"zswh","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"onetext","field-type":"number","column-length":"0","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-validate":"\\"\\"","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-onetext "}),_v(" 点赞数|单行文本")])],1)],1),_v(" "),_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("收藏次数")]),_v(" "),_c(''td'',{staticClass:"td-style2"},[_c(''gw-onetext'',{staticClass:"gw-components",attrs:{"column-id":"890990116530028545","column-comment":"收藏数","column-key":"FAVORITE_COUNT","bo-key":"zswh","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"onetext","field-type":"number","column-length":"0","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-validate":"\\"\\"","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-onetext "}),_v(" 收藏数|单行文本")])],1)],1)]),_v(" "),_c(''tr'',[_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("操作人")]),_v(" "),_c(''td'',{staticClass:"td-style2"},[_c(''gw-onetext'',{staticClass:"gw-components",attrs:{"column-id":"890990116524261377","column-comment":"操作人","column-key":"OPERATOR","bo-key":"zswh","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"onetext","field-type":"varchar","column-length":"50","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-validate":"\\"\\"","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-onetext "}),_v(" 操作人|单行文本")])],1)],1),_v(" "),_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("创建时间")]),_v(" "),_c(''td'',{staticClass:"td-style2 td-w30"},[_c(''gw-date'',{staticClass:"gw-components",attrs:{"column-id":"890990116536320001","column-comment":"创建时间","column-key":"CREATE_DATE","bo-key":"zswh","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"date","field-type":"date","column-length":"50","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-date-format":"yyyy-MM-dd HH:mm:ss","column-validate":"\\"\\"","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-sj "}),_v(" 创建时间|日期选择器")])],1)],1)]),_v(" "),_c(''tr'',[_c(''td'',{staticClass:"td-style td-w20",staticStyle:{"text-align":"right"}},[_v("发布时间")]),_v(" "),_c(''td'',{staticClass:"td-style2",attrs:{"colspan":"3"}},[_c(''gw-date'',{staticClass:"gw-components",attrs:{"column-id":"890990116532125697","column-comment":"发布时间","column-key":"PUBLISH_DATE","bo-key":"zswh","native-bo-key":"zswh","table-key":"KM_INFO","column-type":"date","field-type":"date","column-length":"50","column-placeholder":"","column-defaultvalue":"","column-valid-rule":"","column-date-format":"yyyy-MM-dd HH:mm:ss","column-validate":"\\"\\"","column-watch-rule":"","column-valid-length":"","column-created-fun":"","column-script":"false","relation-type":"oneToOne"}},[_c(''gw-text'',[_c(''span'',{staticClass:"icon iconfont icon-page-sj "}),_v(" 发布时间|日期选择器")])],1)],1)])])])]):_e(),_v(" "),(showAuditComparison)?_c(''div'',[_c(''div'',{staticClass:"sub-title uomp-form-title margin-t-0",staticStyle:{"cursor":"pointer"}},[_v("审核比对")]),_v(" "),(afterData.KNOWLEDGE_NAME)?_c(''div'',[_c(''gw-vue-custom-tag'',{attrs:{"tag-name":"gw-app-compare-table","properties":"","tag-change":"","tag-id":"496602904261778382","column-function":"vue-custom-tag","hidefocus":"true","title":"点击标签，右侧面板修改标签属性。","tabindex":"0"}},[_c(''gw-text'',{staticClass:"tips"},[_v(" 自定义标签 ：gw-app-compare-table | tagID ：496602904261778382 ")])],1),_v(" "),_c(''div''),_v("\\n ")],1):_e()]):_e()])],1)}
}</vue-render>', '2023-11-09 16:49:53', '890029309079060481', NULL, '2025-03-12 10:39:10', '910290324114898945', NULL, 73, 0, '', NULL, NULL, '1-18');
