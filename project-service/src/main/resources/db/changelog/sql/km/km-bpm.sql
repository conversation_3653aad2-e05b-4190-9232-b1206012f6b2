INSERT INTO bpm_definition (id_, name_, desc_, type_id_, status_, act_def_id_, act_model_id_, act_deploy_id_, version_, main_def_id_, is_main_, create_by_, create_time_, create_org_id_, update_by_, update_time_, support_mobile_, def_setting_, rev_, content_des_, content_id_, org_id_, lock_time_, lock_by_, order_, key_) VALUES('891072946254643201', '知识库维护', '', '891024393716105217', 'draft', 'zskwh:2:891072946141921281', '891072946172329985', '891072946093686785', 2, '891072946254643201', 'Y', '887411276895485953', '2023-11-10 11:26:06', NULL, '910290270479712257', '2025-03-21 15:46:20', 0, '{"nodeMap":{"zskwh-UserTask1":{"nodeName":"填写知识信息","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[{"rule":"","description":"  [用户]发起人;\\n","$$hashKey":"0AY","calcs":[{"pluginType":"user","extract":"no","description":"发起人","logicCal":"or","source":"start","vars":""}]}]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"taskComplete":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport java.util.UUID;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\n//退回修改需要保存关联关系\\n//   def context = new HashMap();\\n// \\tcontext.put(\\"extend_id\\",zswh.get(''EXTEND_ID''))\\n// \\tcontext.put(\\"sort_codes\\",zswh.get(''KNOWLEDGE_TYPE''))\\n// \\tapiService.execute(\\"POST\\",\\"/uomp/km_info/addSortRele\\",context)\\nkmScript.addSortRele(zswh.get(''EXTEND_ID''),zswh.get(''KNOWLEDGE_TYPE''))"}},"mobileForm":{"showName":""},"nodeType":"UserTask","nodeId":"zskwh-UserTask1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":false,"freeSelectUser":"nodeUser","freeSelectNode":true,"backUserMode":"history","requiredOpinion":false,"showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"提交","alias":"agree","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"140"},{"afterScript":"","groovyScript":"","name":"暂存","alias":"save","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"141"}]},"zskwh-EndNoneEvent1":{"nodeName":"发布","form":{},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"endEvent":""}},"mobileForm":{"showName":""},"nodeType":"EndNoneEvent","nodeId":"zskwh-EndNoneEvent1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":true,"freeSelectUser":"no","backUserMode":"history","showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"},{\\"id\\":\\"historicalVersion\\",\\"name\\":\\"历史版本\\"},{\\"id\\":\\"referenceRecord\\",\\"name\\":\\"引用记录\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"搁置","alias":"putAside","beforeScript":"","configPage":"","$$hashKey":"07R"},{"afterScript":"","groovyScript":"","name":"提交审核","alias":"submitAudit","beforeScript":"","configPage":"","$$hashKey":"07S"},{"afterScript":"","groovyScript":"","name":"转派启动","alias":"turnStart","beforeScript":"","configPage":"","$$hashKey":"07T"},{"afterScript":"","groovyScript":"","name":"取消搁置","alias":"reAside","beforeScript":"","configPage":"","$$hashKey":"07U"},{"afterScript":"","groovyScript":"","name":"转派","alias":"turnGroup","beforeScript":"","configPage":"","$$hashKey":"07V"}]},"zskwh-StartNoneEvent1":{"nodeName":"开始","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"startEvent":""}},"mobileForm":{"showName":"","name":"","formValue":"","type":"INNER"},"nodeType":"StartNoneEvent","nodeId":"zskwh-StartNoneEvent1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":false,"freeSelectUser":"nodeUser","freeSelectNode":true,"backUserMode":"history","requiredOpinion":false,"showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"提交","alias":"start","beforeScript":"","configPage":"","$$hashKey":"0KL"},{"afterScript":"","groovyScript":"","name":"暂存","alias":"draft","beforeScript":"","$$hashKey":"0KM"}]},"zskwh-UserTask2":{"nodeName":"主管审批","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[{"rule":"","description":"  [组]角色:安全监管员;\\n","$$hashKey":"0AV","calcs":[{"groupName":"安全监管员","pluginType":"group","extract":"extract","typeName":"角色","description":"角色:安全监管员","logicCal":"or","source":"start","vars":"","type":"role","groupKey":"G_ROLE_SAFETY"}]}]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"taskComplete":""}},"mobileForm":{"showName":""},"nodeType":"UserTask","nodeId":"zskwh-UserTask2","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":true,"freeSelectUser":"nodeUser","freeSelectNode":true,"extendConfig":"{\\"opinionBarActive\\":true,\\"requiredOpinion\\":{\\"reject\\":true}}","backUserMode":"history","requiredOpinion":true,"showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"同意","alias":"agree","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"0P5"},{"afterScript":"","groovyScript":"","name":"拒绝","alias":"reject","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"0P6"}]}},"flow":{"dataModelList":[{"code":"zswh","name":"知识维护","$$hashKey":"07L"}],"nodeInitList":[{"whenSave":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport cn.gwssi.ecloud.components.config.utils.IdUtils;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())\\nzswh.put(''DELETE_FLAG'',''1'')\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\nzswh.put(''EXTEND_ID'',IdUtils.getUUID32())//知识分类的关联id\\nzswh.put(''LIKE_COUNT'',0)\\nzswh.put(''FAVORITE_COUNT'',0)\\n\\n\\n//暂存时需要保存关联关系\\ndef context = new HashMap();\\n\\t// context.put(\\"extend_id\\",zswh.get(''EXTEND_ID''))\\n\\t// context.put(\\"sort_codes\\",zswh.get(''KNOWLEDGE_TYPE''))\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/addSortRele\\",context)\\n\\tkmScript.addSortRele(zswh.get(''EXTEND_ID''),zswh.get(''KNOWLEDGE_TYPE''))\\n\\nif (submitActionName == ''start'') {//审核中\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''04'')\\n}else if (submitActionName == ''draft''){//暂存\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''01'')\\n}\\n\\n\\n","beforeShow":"import cn.gwssi.ecloudframework.sys.util.ContextUtil\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())\\nzswh.getChildren(''KM_CONTENT'')CREATE_DATE\\n/* zswh.getChildren(''KM_CONTENT'')  获取子表数据： List<IBusinessData> 子表字段：”CREATE_ID“ 请根据实际情况获取子表数据*/\\n\\n","nodeId":"zskwh-StartNoneEvent1","desc":"开始"},{"whenSave":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport java.util.UUID;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())\\nzswh.put(''DELETE_FLAG'',''1'')\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\n\\nif (submitActionName == ''agree'') {//审核中\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''04'')\\n}else if (submitActionName == ''draft''){//暂存\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''01'')\\n}\\n","beforeShow":"import cn.gwssi.ecloudframework.sys.util.ContextUtil\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())","nodeId":"zskwh-UserTask1","desc":"填写"},{"whenSave":"import org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport java.time.format.DateTimeFormatter;\\nimport java.time.LocalDateTime;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\nzswh.put(''REVISE_DATE'',new Date())\\n//驳回的数据重新记录\\nif (submitActionName == ''reject'') {//驳回\\n  zswh.put(''KNOWLEDGE_STATUS'',''05'');//待修改\\n  // def context = new HashMap();\\n  // context.put(\\"id\\",zswh.get(''KNOWLEDGE_ID''))\\n  // context.put(\\"contentHtml\\",zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT_HTML\\"))\\n  // context.put(\\"content\\",zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT\\"))\\n  // apiService.execute(\\"POST\\",\\"/uomp/km_info/addInfoById\\",context)\\n  kmScript.addInfoById(zswh.get(''KNOWLEDGE_ID''),zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT_HTML\\"),zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT\\"))\\n}\\nif (submitActionName == ''agree''){\\n\\tzswh.put(''PUBLISH_USER'',ContextUtil.getCurrentUserName())//发布人\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''02'')//发布\\n\\tzswh.put(''PUBLISH_DATE'',new Date())//发布时间\\n\\tzswh.put(''REVISE_DATE'',new Date())\\n\\t//添加历史记录\\n  DateTimeFormatter sf = DateTimeFormatter.ofPattern(\\"yyyy-MM-dd HH:mm:ss\\");\\n\\tpubdate = LocalDateTime.now().format(sf);\\n\\t// def context = new HashMap();\\n\\t// context.put(\\"km_id\\",zswh.get(''KNOWLEDGE_ID''))\\n  // context.put(\\"id\\",zswh.get(''KNOWLEDGE_ID''))\\n\\t// context.put(\\"operator\\",zswh.get(''OPERATOR''))\\n  // context.put(\\"pubdate\\",pubdate)\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/addHistoryInfo\\",context)\\n  kmScript.addHistoryInfo(zswh.get(''KNOWLEDGE_ID''),zswh.get(''KNOWLEDGE_ID''),zswh.get(''OPERATOR''),pubdate)\\n  \\n  //同步es\\n  // def context = new HashMap();\\n\\t// context.put(\\"kmids\\",zswh.get(''KNOWLEDGE_ID''))\\n  // context.put(\\"createId\\",ContextUtil.getCurrentUserId())\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/syncDataToEsPublish\\",context)\\n  kmScript.syncDataToEsPublish(zswh.get(''KNOWLEDGE_ID''),ContextUtil.getCurrentUserId())\\n}","nodeId":"zskwh-UserTask2","desc":"主管审批"}],"plugins":{"globalScript":[],"reminder":[],"nodeMessage":[{"htmlTemplate":"<p>您有新的待办需要审批:${bpmInstance.subject}</p><p>&lt;#if isTask&gt;</p><p>上一节点：${submitTaskname}</p><p>处理人：${currentUser.fullname} ，处理结果 ：${submitActionDesc}</p><p>提交意见：${submitOpinion}</p><p>&lt;/#if&gt;</p>","msgType":"email","userRules":[],"html":"<p>您有新的待办需要审批:{待办标题}</p><p>上一环节：{用户任务1}，{小王} 处理结果&nbsp;：{同意/反对}</p><p>意见：{阿什顿发阿什顿发}</p>","event":"postTaskCreate","$$hashKey":"04V","desc":"任务待办通知"},{"htmlTemplate":"<p>您的申请的流程已经审批完结{subject}<br/></p>","msgType":"email","userRules":[{"rule":"","description":"  [用户]发起人;\\n","$$hashKey":"004","calcs":[{"pluginType":"user","extract":"no","description":"发起人","logicCal":"or","source":"start","vars":""}]}],"html":"<p>您发起的流程已经完成审批<br/>${bpmInstance.subject}</p>","event":"end","$$hashKey":"04S","nodeId":"","desc":"流程结束通知发起人"}],"carbonCopy":[],"taskAgency":{},"taskSkip":{"skipTypeArr":"firstNodeSkip"},"multInst":[]},"v":"ecloudbpm","globalMobileForm":{"showName":"","name":"","formValue":"","type":"INNER"},"variableList":[],"instanceBtnList":[{"supportScript":true,"name":"重启","alias":"instanceRestart","$$hashKey":"079","status":2}],"properties":{"supportMobile":0,"subjectRule":"{zswh.KNOWLEDGE_NAME}（{zswh.KNOWLEDGE_CODE}）","logSubmitChangeData":true,"allowExecutorEmpty":false,"nodeSortRule":[],"labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]","opinion":{"nodesSorted":[],"nodes":[],"ruleArr":[],"nodeOrder":"default"},"allowRecall":true,"logSubmitData":true,"officialDocumentEnable":true,"requiredOpinion":true,"status":"draft"},"globalForm":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"}},"__existNode":{"zskwh-StartNoneEvent1":true,"zskwh-UserTask1":true,"zskwh-UserTask2":true,"zskwh-EndNoneEvent1":true},"bpmDefinition":{"actDefId":"zskwh:2:891072946141921281","actDeployId":"891072946093686785","actModelId":"891072946172329985","createBy":"887411276895485953","createTime":"2023-11-10 11:26:06","desc":"","id":"891072946254643201","isMain":"Y","key":"zskwh","mainDefId":"891072946254643201","name":"知识库维护","order":1,"orgId":"","rev":316,"status":"draft","supportMobile":0,"typeId":"891024393716105217","updateBy":"910290270479712257","updateTime":"2025-03-14 17:02:25","version":2}}', 317, NULL, NULL, '', NULL, NULL, 1, 'zskwh');
INSERT INTO bpm_definition (id_, name_, desc_, type_id_, status_, act_def_id_, act_model_id_, act_deploy_id_, version_, main_def_id_, is_main_, create_by_, create_time_, create_org_id_, update_by_, update_time_, support_mobile_, def_setting_, rev_, content_des_, content_id_, org_id_, lock_time_, lock_by_, order_, key_) VALUES('891850262248423425', ' 知识维护（重启）', '', '891024393716105217', 'draft', 'zswh_cq:1:891850812575711233', '891850262252093441', '891850812527476737', 1, '891850262248423425', 'Y', '887411276895485953', '2023-11-27 15:16:18', NULL, '910290270479712257', '2025-03-21 17:08:19', 0, '{"nodeMap":{"zswh_cq-StartNoneEvent1":{"nodeName":"重启","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"startEvent":""}},"mobileForm":{"showName":"","name":"","formValue":"","type":"INNER"},"nodeType":"StartNoneEvent","nodeId":"zswh_cq-StartNoneEvent1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":false,"freeSelectUser":"nodeUser","freeSelectNode":true,"backUserMode":"history","requiredOpinion":false,"showTaskSysIdentityRule":["user","org"],"backMode":"normal"},"btnList":[{"afterScript":"","groovyScript":"","name":"提交","alias":"start","beforeScript":"","$$hashKey":"08N"}]},"zswh_cq-EndNoneEvent1":{"nodeName":"结束","form":{},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{}},"mobileForm":{"showName":""},"nodeType":"EndNoneEvent","nodeId":"zswh_cq-EndNoneEvent1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":true,"freeSelectUser":"no","backUserMode":"history","showTaskSysIdentityRule":["user","org"],"backMode":"normal"},"btnList":[{"afterScript":"","groovyScript":"","name":"搁置","alias":"putAside","beforeScript":"","configPage":"","$$hashKey":"0HW"},{"afterScript":"","groovyScript":"","name":"提交审核","alias":"submitAudit","beforeScript":"","configPage":"","$$hashKey":"0HX"},{"afterScript":"","groovyScript":"","name":"转派启动","alias":"turnStart","beforeScript":"","configPage":"","$$hashKey":"0HY"},{"afterScript":"","groovyScript":"","name":"取消搁置","alias":"reAside","beforeScript":"","configPage":"","$$hashKey":"0HZ"},{"afterScript":"","groovyScript":"","name":"转派","alias":"turnGroup","beforeScript":"","configPage":"","$$hashKey":"0I0"}]},"zswh_cq-UserTask2":{"nodeName":"主管审批","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[{"rule":"","description":"  [组]角色:安全监管员;\\n","$$hashKey":"0AU","calcs":[{"groupName":"安全监管员","pluginType":"group","extract":"extract","typeName":"角色","description":"角色:安全监管员","logicCal":"or","source":"start","vars":"","type":"role","groupKey":"G_ROLE_SAFETY"}]}]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"taskComplete":""}},"mobileForm":{"showName":"","name":"","formValue":"","type":"INNER"},"nodeType":"UserTask","nodeId":"zswh_cq-UserTask2","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":true,"freeSelectUser":"nodeUser","freeSelectNode":true,"extendConfig":"{\\"opinionBarActive\\":true,\\"requiredOpinion\\":{\\"reject\\":true}}","backUserMode":"history","requiredOpinion":true,"showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"同意","alias":"agree","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"0AU"},{"supportScript":true,"name":"拒绝","alias":"reject","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"0AV","status":2}]},"zswh_cq-UserTask1":{"nodeName":"填写信息","form":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"},"plugins":{"dynamicTask":{"needSupervise":false,"isEnabled":false,"isParallel":true,"reset":true,"interfaceName":"","dynamicType":"user","canBeRecycledEnd":false},"formulas":[],"formScript":[],"leaderTask":{"signLeaderTask":false},"ruleSkip":[],"userAssign":{"ruleList":[{"rule":"","description":"  [用户]发起人;\\n","$$hashKey":"1PH","calcs":[{"pluginType":"user","extract":"no","description":"发起人","logicCal":"or","source":"start","vars":""}]}]},"signTask":{"voteAmount":51,"needSupervise":false,"voteType":"PERCENT","needAllSign":false,"reset":true,"signMultiTask":false,"opposedAction":"oppose"},"nodeScript":{"taskComplete":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport java.util.UUID;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\n//退回修改需要保存关联关系\\n//   def context = new HashMap();\\n// \\tcontext.put(\\"extend_id\\",zswh.get(''EXTEND_ID''))\\n// \\tcontext.put(\\"sort_codes\\",zswh.get(''KNOWLEDGE_TYPE''))\\n// \\tapiService.execute(\\"POST\\",\\"/uomp/km_info/addSortRele\\",context)\\nkmScript.addSortRele(zswh.get(''EXTEND_ID''),zswh.get(''KNOWLEDGE_TYPE''))"}},"mobileForm":{"showName":"","name":"","formValue":"","type":"INNER"},"nodeType":"UserTask","nodeId":"zswh_cq-UserTask1","propertie":{"allowBatchActions":false,"allowSysIdentityMultiple":false,"freeSelectUser":"nodeUser","freeSelectNode":true,"backUserMode":"history","requiredOpinion":false,"showTaskSysIdentityRule":["user","org"],"backMode":"normal","labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]"},"btnList":[{"afterScript":"","groovyScript":"","name":"提交","alias":"agree","beforeScript":"","configPage":"/bpm/task/taskOpinionDialog.html","$$hashKey":"0IQ"}]}},"flow":{"dataModelList":[{"code":"zswh","name":"知识维护","$$hashKey":"0O5"}],"nodeInitList":[{"whenSave":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport cn.gwssi.ecloud.components.config.utils.IdUtils;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())\\nzswh.put(''DELETE_FLAG'',''1'')\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\nzswh.put(''EXTEND_ID'',IdUtils.getUUID32())\\n// def context = new HashMap();//添加关联关系\\n// \\tcontext.put(\\"extend_id\\",zswh.get(''EXTEND_ID''))\\n// \\tcontext.put(\\"sort_codes\\",zswh.get(''KNOWLEDGE_TYPE''))\\n// \\tapiService.execute(\\"POST\\",\\"/uomp/km_info/addSortRele\\",context)\\nkmScript.addSortRele(zswh.get(''EXTEND_ID''),zswh.get(''KNOWLEDGE_TYPE''))\\n//修改主流程的状态为更新中\\n// def context1 = new HashMap();//添加关联关系\\n// \\tcontext1.put(\\"KNOWLEDGE_ID\\",zswh.get(''MAIN_KM_ID''))\\n// \\tcontext1.put(\\"KNOWLEDGE_STATUS\\",''06'')\\n// apiService.execute(\\"GET\\",\\"/uomp/km_info/updateState\\",context1)\\nkmScript.updateState(zswh.get(''MAIN_KM_ID''),''06'')\\n\\nif (submitActionName == ''start'') {//审核中\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''04'')\\n}else if (submitActionName == ''draft''){//暂存\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''01'')\\n}\\n\\n\\n","nodeId":"zswh_cq-StartNoneEvent1","desc":"重启流程"},{"whenSave":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport cn.gwssi.ecloud.components.config.utils.IdUtils;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\n\\ndef apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\nzswh.put(''CREATE_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''CREATE_DATE'',new Date())\\nzswh.put(''DELETE_FLAG'',''1'')\\nzswh.put(''OPERATOR'',ContextUtil.getCurrentUserName())\\nzswh.put(''process_id'',bpmInstance.getId())\\nzswh.put(''OPERATOR_ID'',ContextUtil.getCurrentUserId())\\nzswh.put(''REVISE_DATE'',new Date())\\n\\nif (submitActionName == ''agree'') {//审核中\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''04'')\\n}else if (submitActionName == ''save''){//暂存\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''01'')\\n}\\n\\n\\n","nodeId":"zswh_cq-UserTask1","desc":"数据填写"},{"whenSave":"import cn.gwssi.ecloudframework.sys.util.ContextUtil;\\nimport cn.gwssi.ecloud.components.config.utils.IdUtils;\\nimport org.ssssssss.magicapi.core.service.MagicAPIService;  \\nimport cn.gwssi.ecloudframework.base.core.util.AppUtil;\\nimport java.time.format.DateTimeFormatter;\\nimport java.time.LocalDateTime;\\nimport cn.gwssi.ecloud.components.km.rest.controller.KmScript;\\n\\n// def apiService= AppUtil.getImplInstanceArray(MagicAPIService.class).get(0);\\ndef kmScript = AppUtil.getImplInstanceArray(KmScript.class).get(0);\\n\\nzswh.put(''REVISE_DATE'',new Date())\\n//驳回的数据重新记录\\nif (submitActionName == ''reject'') {//驳回\\n  zswh.put(''KNOWLEDGE_STATUS'',''05'');//待修改\\n//   def context = new HashMap();\\n//   context.put(\\"id\\",zswh.get(''KNOWLEDGE_ID''))\\n//   context.put(\\"contentHtml\\",zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT_HTML\\"))\\n//   context.put(\\"content\\",zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT\\"))\\n//  apiService.execute(\\"POST\\",\\"/uomp/km_info/addInfoById\\",context)\\nkmScript.addInfoById(zswh.get(''KNOWLEDGE_ID''),zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT_HTML\\"),zswh.getChildren(''KM_CONTENT'')[0].get(\\"CONTENT\\"))\\n}\\nif (submitActionName == ''agree''){\\n\\tzswh.put(''PUBLISH_USER'',ContextUtil.getCurrentUserName())//发布人\\n\\tzswh.put(''KNOWLEDGE_STATUS'',''02'')//发布\\n\\tzswh.put(''PUBLISH_DATE'',new Date())//发布时间\\n\\t//添加历史记录\\n  DateTimeFormatter sf = DateTimeFormatter.ofPattern(\\"yyyy-MM-dd HH:mm:ss\\");\\n\\tpubdate = LocalDateTime.now().format(sf);\\n\\t// def context1 = new HashMap();\\n\\t// context1.put(\\"km_id\\",zswh.get(''MAIN_KM_ID''))\\n\\t// context1.put(\\"operator\\",zswh.get(''OPERATOR''))\\n  // context1.put(\\"pubdate\\",pubdate)\\n  // context1.put(\\"id\\",zswh.get(''KNOWLEDGE_ID''))\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/addHistoryInfo\\",context1)\\n  kmScript.addHistoryInfo(zswh.get(''MAIN_KM_ID''),zswh.get(''KNOWLEDGE_ID''),zswh.get(''OPERATOR''),pubdate)\\n  //将更新后的数据赋给主流程\\n  // def context2 = new HashMap();\\n\\t// context2.put(\\"km_id\\",zswh.get(''KNOWLEDGE_ID''))\\n\\t// context2.put(\\"main_km_id\\",zswh.get(''MAIN_KM_ID''))\\n  // context2.put(\\"pubdate\\",pubdate)\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/renewPushData\\",context2)\\n  kmScript.renewPushData(zswh.get(''KNOWLEDGE_ID''),zswh.get(''MAIN_KM_ID''),pubdate)\\n  \\n   //同步es\\n  // def context = new HashMap();\\n\\t// context.put(\\"kmids\\",zswh.get(''MAIN_KM_ID''))\\n  // context.put(\\"createId\\",ContextUtil.getCurrentUserId())\\n\\t// apiService.execute(\\"POST\\",\\"/uomp/km_info/syncDataToEsPublish\\",context)\\n  kmScript.syncDataToEsPublish(zswh.get(''MAIN_KM_ID''),ContextUtil.getCurrentUserId())\\n}","nodeId":"zswh_cq-UserTask2","desc":"主管审批"}],"plugins":{"globalScript":[],"reminder":[],"nodeMessage":[{"htmlTemplate":"<p>您有新的待办需要审批:${bpmInstance.subject}</p><p>&lt;#if isTask&gt;</p><p>上一节点：${submitTaskname}</p><p>处理人：${currentUser.fullname} ，处理结果 ：${submitActionDesc}</p><p>提交意见：${submitOpinion}</p><p>&lt;/#if&gt;</p>","msgType":"email","userRules":[],"html":"<p>您有新的待办需要审批:{待办标题}</p><p>上一环节：{用户任务1}，{小王} 处理结果&nbsp;：{同意/反对}</p><p>意见：{阿什顿发阿什顿发}</p>","event":"postTaskCreate","$$hashKey":"04V","desc":"任务待办通知"},{"htmlTemplate":"<p>您的申请的流程已经审批完结{subject}<br/></p>","msgType":"email","userRules":[{"rule":"","description":"  [用户]发起人;\\n","$$hashKey":"004","calcs":[{"pluginType":"user","extract":"no","description":"发起人","logicCal":"or","source":"start","vars":""}]}],"html":"<p>您发起的流程已经完成审批<br/>${bpmInstance.subject}</p>","event":"end","$$hashKey":"04S","nodeId":"","desc":"流程结束通知发起人"}],"carbonCopy":[],"taskAgency":{},"taskSkip":{"skipTypeArr":"firstNodeSkip"},"multInst":[]},"v":"ecloudbpm","variableList":[],"instanceBtnList":[],"properties":{"supportMobile":0,"subjectRule":"{zswh.KNOWLEDGE_NAME}（{zswh.KNOWLEDGE_CODE}）","logSubmitChangeData":true,"allowExecutorEmpty":false,"nodeSortRule":[],"labels":"[{\\"id\\":\\"form\\",\\"name\\":\\"知识信息\\"},{\\"id\\":\\"flowHistory\\",\\"name\\":\\"办理记录\\"},{\\"id\\":\\"image\\",\\"name\\":\\"流程图\\"}]","opinion":{"nodesSorted":[],"nodes":[],"ruleArr":[],"nodeOrder":"default"},"allowRecall":true,"logSubmitData":true,"officialDocumentEnable":true,"requiredOpinion":true,"status":"draft"},"globalForm":{"showName":"知识维护(zswh)","name":"知识维护","formValue":"zswh","type":"INNER"}},"__existNode":{"zswh_cq-StartNoneEvent1":true,"zswh_cq-UserTask1":true,"zswh_cq-UserTask2":true,"zswh_cq-EndNoneEvent1":true},"bpmDefinition":{"actDefId":"zswh_cq:1:891850812575711233","actDeployId":"891850812527476737","actModelId":"891850262252093441","createBy":"887411276895485953","createTime":"2023-11-27 15:16:18","desc":"","id":"891850262248423425","isMain":"Y","key":"zswh_cq","mainDefId":"891850262248423425","name":" 知识维护（重启）","order":1,"orgId":"","rev":98,"status":"draft","supportMobile":0,"typeId":"891024393716105217","updateBy":"910290270479712257","updateTime":"2025-03-14 17:01:31","version":1}}', 99, NULL, NULL, '', NULL, NULL, 1, 'zswh_cq');


INSERT INTO act_re_deployment (id_, name_, category_, tenant_id_, deploy_time_) VALUES('891072946093686785', 'zskwh', NULL, '886768918381199361', '2023-11-10 11:26:05');
INSERT INTO act_re_deployment (id_, name_, category_, tenant_id_, deploy_time_) VALUES('891850812527476737', 'zswh_cq', NULL, '886768918381199361', '2023-11-27 15:33:48');


INSERT INTO act_re_procdef (id_, rev_, category_, name_, key_, version_, deployment_id_, resource_name_, dgrm_resource_name_, description_, has_start_form_key_, has_graphical_notation_, suspension_state_, tenant_id_) VALUES('zskwh:2:891072946141921281', 1, 'http://www.activiti.org/processdef', '知识库维护', 'zskwh', 2, '891072946093686785', 'zskwh.bpmn20.xml', 'zskwh.zskwh.png', NULL, 0, 1, 1, '886768918381199361');
INSERT INTO act_re_procdef (id_, rev_, category_, name_, key_, version_, deployment_id_, resource_name_, dgrm_resource_name_, description_, has_start_form_key_, has_graphical_notation_, suspension_state_, tenant_id_) VALUES('zswh_cq:1:891850812575711233', 1, 'http://www.activiti.org/processdef', '知识维护（重启）', 'zswh_cq', 1, '891850812527476737', 'zswh_cq.bpmn20.xml', 'zswh_cq.zswh_cq.png', NULL, 0, 1, 1, '886768918381199361');


INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891072946093686801', 1, 'zskwh.bpmn20.xml', '891072946093686785', 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, 0);
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891072946140348417', 1, 'zskwh.zskwh.png', '891072946093686785', 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
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891072946176000001', 23, 'source', NULL, 0x7B227265736F757263654964223A22383931303732393436313732333239393835222C2270726F70657274696573223A7B2270726F636573735F6964223A227A736B7768222C226E616D65223A22E79FA5E8AF86E5BA93E7BBB4E68AA4222C22646F63756D656E746174696F6E223A22222C2270726F636573735F617574686F72223A22222C2270726F636573735F76657273696F6E223A22222C2270726F636573735F6E616D657370616365223A22687474703A2F2F7777772E61637469766974692E6F72672F70726F63657373646566222C22657865637574696F6E6C697374656E657273223A22222C226576656E746C697374656E657273223A22222C227369676E616C646566696E6974696F6E73223A22222C226D657373616765646566696E6974696F6E73223A22227D2C227374656E63696C223A7B226964223A2242504D4E4469616772616D227D2C226368696C64536861706573223A5B7B227265736F757263654964223A227369642D39324341334444412D383245342D343532412D423843432D434638324135323831304346222C2270726F70657274696573223A7B226F766572726964656964223A227A736B77682D53746172744E6F6E654576656E7431222C226E616D65223A22E5BC80E5A78B222C22646F63756D656E746174696F6E223A22222C22657865637574696F6E6C697374656E657273223A22222C22696E69746961746F72223A22222C22666F726D6B6579646566696E6974696F6E223A22222C22666F726D70726F70657274696573223A22227D2C227374656E63696C223A7B226964223A2253746172744E6F6E654576656E74227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D45374238413343422D413743362D344430422D394444422D443146454633364234344436227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3132302C2279223A3331307D2C2275707065724C656674223A7B2278223A39302C2279223A3238307D7D2C22646F636B657273223A5B5D7D2C7B227265736F757263654964223A227369642D45333239324639442D303635312D343033352D394637302D453437393430383837323936222C2270726F70657274696573223A7B226F766572726964656964223A227A736B77682D557365725461736B31222C226E616D65223A22E5A1ABE58699E79FA5E8AF86E4BFA1E681AF222C22646F63756D656E746174696F6E223A22222C226173796E6368726F6E6F7573646566696E6974696F6E223A2266616C7365222C226578636C7573697665646566696E6974696F6E223A2266616C7365222C22657865637574696F6E6C697374656E657273223A22222C226D756C7469696E7374616E63655F74797065223A224E6F6E65222C226D756C7469696E7374616E63655F63617264696E616C697479223A22222C226D756C7469696E7374616E63655F636F6C6C656374696F6E223A22222C226D756C7469696E7374616E63655F7661726961626C65223A22222C226D756C7469696E7374616E63655F636F6E646974696F6E223A22222C226973666F72636F6D70656E736174696F6E223A2266616C7365222C22757365727461736B61737369676E6D656E74223A22222C22666F726D6B6579646566696E6974696F6E223A22222C2264756564617465646566696E6974696F6E223A22222C227072696F72697479646566696E6974696F6E223A22222C22666F726D70726F70657274696573223A22222C227461736B6C697374656E657273223A22227D2C227374656E63696C223A7B226964223A22557365725461736B227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D42443645413346422D304533412D344645392D423641412D443231454438303246424631227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3334302C2279223A3333357D2C2275707065724C656674223A7B2278223A3234302C2279223A3235357D7D2C22646F636B657273223A5B5D7D2C7B227265736F757263654964223A227369642D45374238413343422D413743362D344430422D394444422D443146454633364234344436222C2270726F70657274696573223A7B226F766572726964656964223A22222C226E616D65223A22222C22646F63756D656E746174696F6E223A22222C22636F6E646974696F6E73657175656E6365666C6F77223A22222C22657865637574696F6E6C697374656E657273223A22222C2264656661756C74666C6F77223A2266616C7365227D2C227374656E63696C223A7B226964223A2253657175656E6365466C6F77227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D45333239324639442D303635312D343033352D394637302D453437393430383837323936227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3233392E3835393337352C2279223A3239357D2C2275707065724C656674223A7B2278223A3132302E3435333132352C2279223A3239357D7D2C22646F636B657273223A5B7B2278223A31352C2279223A31357D2C7B2278223A35302C2279223A34307D5D2C22746172676574223A7B227265736F757263654964223A227369642D45333239324639442D303635312D343033352D394637302D453437393430383837323936227D7D2C7B227265736F757263654964223A227369642D31463234443438412D363838442D343031372D383742302D383144424143303937354136222C2270726F70657274696573223A7B226F766572726964656964223A227A736B77682D557365725461736B32222C226E616D65223A22E4B8BBE7AEA1E5AEA1E689B9222C22646F63756D656E746174696F6E223A22222C226173796E6368726F6E6F7573646566696E6974696F6E223A2266616C7365222C226578636C7573697665646566696E6974696F6E223A2266616C7365222C22657865637574696F6E6C697374656E657273223A22222C226D756C7469696E7374616E63655F74797065223A224E6F6E65222C226D756C7469696E7374616E63655F63617264696E616C697479223A22222C226D756C7469696E7374616E63655F636F6C6C656374696F6E223A22222C226D756C7469696E7374616E63655F7661726961626C65223A22222C226D756C7469696E7374616E63655F636F6E646974696F6E223A22222C226973666F72636F6D70656E736174696F6E223A2266616C7365222C22757365727461736B61737369676E6D656E74223A22222C22666F726D6B6579646566696E6974696F6E223A22222C2264756564617465646566696E6974696F6E223A22222C227072696F72697479646566696E6974696F6E223A22222C22666F726D70726F70657274696573223A22222C227461736B6C697374656E657273223A22227D2C227374656E63696C223A7B226964223A22557365725461736B227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D41454644313033452D393241452D343734372D393741452D363237304638384334443546227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3539352C2279223A3333357D2C2275707065724C656674223A7B2278223A3439352C2279223A3235357D7D2C22646F636B657273223A5B5D7D2C7B227265736F757263654964223A227369642D42443645413346422D304533412D344645392D423641412D443231454438303246424631222C2270726F70657274696573223A7B226F766572726964656964223A22222C226E616D65223A22222C22646F63756D656E746174696F6E223A22222C22636F6E646974696F6E73657175656E6365666C6F77223A22222C22657865637574696F6E6C697374656E657273223A22222C2264656661756C74666C6F77223A2266616C7365227D2C227374656E63696C223A7B226964223A2253657175656E6365466C6F77227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D31463234443438412D363838442D343031372D383742302D383144424143303937354136227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3439342E313935333132352C2279223A3239357D2C2275707065724C656674223A7B2278223A3334302E383034363837352C2279223A3239357D7D2C22646F636B657273223A5B7B2278223A35302C2279223A34307D2C7B2278223A35302C2279223A34307D5D2C22746172676574223A7B227265736F757263654964223A227369642D31463234443438412D363838442D343031372D383742302D383144424143303937354136227D7D2C7B227265736F757263654964223A227369642D35443234323233372D334235352D344146452D414439342D393438423046463131324444222C2270726F70657274696573223A7B226F766572726964656964223A227A736B77682D456E644E6F6E654576656E7431222C226E616D65223A22E58F91E5B883222C22646F63756D656E746174696F6E223A22222C22657865637574696F6E6C697374656E657273223A22227D2C227374656E63696C223A7B226964223A22456E644E6F6E654576656E74227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3737382C2279223A3330397D2C2275707065724C656674223A7B2278223A3735302C2279223A3238317D7D2C22646F636B657273223A5B5D7D2C7B227265736F757263654964223A227369642D41454644313033452D393241452D343734372D393741452D363237304638384334443546222C2270726F70657274696573223A7B226F766572726964656964223A22222C226E616D65223A22222C22646F63756D656E746174696F6E223A22222C22636F6E646974696F6E73657175656E6365666C6F77223A22222C22657865637574696F6E6C697374656E657273223A22222C2264656661756C74666C6F77223A2266616C7365227D2C227374656E63696C223A7B226964223A2253657175656E6365466C6F77227D2C226368696C64536861706573223A5B5D2C226F7574676F696E67223A5B7B227265736F757263654964223A227369642D35443234323233372D334235352D344146452D414439342D393438423046463131324444227D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A3734392E333132352C2279223A3239357D2C2275707065724C656674223A7B2278223A3539352E363137313837352C2279223A3239357D7D2C22646F636B657273223A5B7B2278223A35302C2279223A34307D2C7B2278223A31342C2279223A31347D5D2C22746172676574223A7B227265736F757263654964223A227369642D35443234323233372D334235352D344146452D414439342D393438423046463131324444227D7D5D2C22626F756E6473223A7B226C6F7765725269676874223A7B2278223A313230302C2279223A313035307D2C2275707065724C656674223A7B2278223A302C2279223A307D7D2C227374656E63696C736574223A7B2275726C223A227374656E63696C736574732F62706D6E322E302F62706D6E322E302E6A736F6E222C226E616D657370616365223A22687474703A2F2F62336D6E2E6F72672F7374656E63696C7365742F62706D6E322E3023227D2C227373657874656E73696F6E73223A5B5D7D, NULL);
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891072946244681729', 21, 'source-extra', NULL, 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
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891850262256287745', 5, 'source', NULL, 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
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891850812527476753', 1, 'zswh_cq.bpmn20.xml', '891850812527476737', 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, 0);
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891850812573089793', 1, 'zswh_cq.zswh_cq.png', '891850812527476737', 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
INSERT INTO act_ge_bytearray (id_, rev_, name_, deployment_id_, bytes_, generated_) VALUES('891850812646490113', 4, 'source-extra', NULL, 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


INSERT INTO act_re_model (id_, rev_, name_, key_, category_, create_time_, last_update_time_, version_, meta_info_, deployment_id_, editor_source_value_id_, editor_source_extra_value_id_, tenant_id_) VALUES('891072946172329985', 322, '知识库维护', 'zskwh', NULL, '2023-11-10 11:26:06', '2025-03-21 15:46:20', 1, '{"name":"知识库维护","revision":1,"key":"zskwh","description":""}', '891072946093686785', '891072946176000001', '891072946244681729', '886768918381199361');
INSERT INTO act_re_model (id_, rev_, name_, key_, category_, create_time_, last_update_time_, version_, meta_info_, deployment_id_, editor_source_value_id_, editor_source_extra_value_id_, tenant_id_) VALUES('891850262252093441', 99, '知识维护（重启）', 'zswh_cq', NULL, '2023-11-27 15:16:18', '2025-03-21 17:08:19', 1, '{"name":"知识维护（重启）","revision":1,"key":"zswh_cq","description":""}', NULL, '891850262256287745', '891850812646490113', '886768918381199361');
