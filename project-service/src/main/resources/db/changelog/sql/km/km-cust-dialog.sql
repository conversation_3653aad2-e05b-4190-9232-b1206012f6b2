INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('892618957399785473', 'zswhlb', '知识维护列表', '', NULL, 'list', 'dataSourceDefault', '', 'table', '5bf697909e6c45308764a5f3f3a363cc', 1, 10, 1360, 815, 0, 1, '{"checkStrictly":false,"pidInitValScript":false,"showFilter":false}', '[{"columnName":"knowledgeCode","formatter":"","showName":"知识编号"},{"columnName":"knowledgeName","formatter":"","showName":"知识名称"},{"columnName":"operator","formatter":"","showName":"操作人"},{"columnName":"operationDate","formatter":"","showName":"操作时间"},{"columnName":"publishDate","formatter":"","showName":"发布时间"}]', '[{"columnName":"kmSortId","condition":"EQ","dbType":"String","showName":"知识分类id","value":{"ctrlType":"inputText","text":"KM_SORT_ID"},"valueSource":"primaryTableColumn"},{"columnName":"keyword","condition":"EQ","dbType":"String","showName":"关键字","value":{"ctrlType":"inputText","text":"inputText"},"valueSource":"param"}]', '[{"columnName":"knowledgeCode","returnName":"knowledgeCode"},{"columnName":"knowledgeName","returnName":"knowledgeName"},{"columnName":"operator","returnName":"operator"},{"columnName":"operationDate","returnName":"operationDate"},{"columnName":"publishDate","returnName":"publishDate"},{"columnName":"knowledgeStatus","returnName":"knowledgeStatus"},{"columnName":"knowledgeId","returnName":"knowledgeId"},{"columnName":"processId","returnName":"processId"}]', '[]', 'interface', '{"conditionFields":[],"dataSource":"interface","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"zswhlb","multiple":false,"objName":"56bae66c325c40f9a60c27bcae03890b","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/km_info/queryKmSortAll(23查询所有知识分类平铺)","primaryKey":""},"page":true,"pageSize":10,"primaryTablePercent":30,"returnFields":[{"columnName":"KM_SORT_ID","returnName":"KM_SORT_ID"},{"columnName":"P_ID","returnName":"P_ID"},{"columnName":"KM_SORT_NAME","returnName":"KM_SORT_NAME"},{"columnName":"KM_SORT_CODE","returnName":"KM_SORT_CODE"}],"sortFields":[],"style":"tree","system":false,"treeConfig":{"checkStrictly":false,"id":"KM_SORT_ID","pid":"P_ID","pidInitValScript":false,"showColumn":"KM_SORT_NAME","showFilter":false},"width":0}', 'doubleTable', 'leftRight', '{"selectedFormatter":"return row.knowledgeName;","searchIgnorePrimaryTable":false,"apiInfo":"/uomp/km_info/queryPubKm(21查询已发布的知识)","primaryKey":""}', '892609068900286465', '2023-12-14 14:32:28', '887411276895485953', '2024-01-15 11:02:59', '881695277245267969');
