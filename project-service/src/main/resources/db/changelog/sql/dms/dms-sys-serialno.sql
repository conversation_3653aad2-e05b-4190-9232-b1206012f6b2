INSERT INTO sys_serialno (id_, name_, alias_, regulation_, gen_type_, no_length_, cur_date_, init_value_, cur_value_, step_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('884231286569304065', '实例权限申请', 'DMS_INST_AUTH_APPLY', '【权限申请】{yyyy}-{NO}', 3, 4, '2025', 1, 24, 1, '884231139971563521', '2023-06-12 10:35:36', '884051084090277889', '2023-12-05 18:56:21', '888452830126931969');
INSERT INTO sys_serialno (id_, name_, alias_, regulation_, gen_type_, no_length_, cur_date_, init_value_, cur_value_, step_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('888760325705302017', '数据变更-工单号', 'DMS_DATA_CHANGE', '{yyyy}{MM}{DD}{NO}', 0, 3, NULL, 1, 55, 1, '884231139971563521', '2023-09-20 10:09:52', '888412408706498561', '2024-08-02 14:35:57', '884232918960963585');
INSERT INTO sys_serialno (id_, name_, alias_, regulation_, gen_type_, no_length_, cur_date_, init_value_, cur_value_, step_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('892339562112614401', '数据结构-工单编号', 'DMS_DATA_STRUCT', '{yyyy}{MM}{DD}{NO}', 0, 3, NULL, 1, 24, 1, '884231139971563521', '2023-12-08 10:30:44', '888671746008809473', '2024-08-02 14:35:37', '884232918960963585');
INSERT INTO sys_serialno (id_, name_, alias_, regulation_, gen_type_, no_length_, cur_date_, init_value_, cur_value_, step_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('893529320742977537', '结构同步编号', 'DMS_STRUCT_SYNC', '{yyyy}{MM}{DD}{NO}', 0, 5, NULL, 1, 118, 1, '884231139971563521', '2024-01-03 16:52:08', '888671746008809473', '2024-02-22 10:40:33', '884051250302156801');
INSERT INTO sys_serialno (id_, name_, alias_, regulation_, gen_type_, no_length_, cur_date_, init_value_, cur_value_, step_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('897322421565521921', '数据库导出', 'DMS_DATA_EXPORT', '{yyyy}{MM}{DD}{NO}', 0, 5, NULL, 1, 61, 1, '884231139971563521', '2024-03-27 10:31:34', '887455923614777345', '2024-08-02 14:35:45', '884232918960963585');
