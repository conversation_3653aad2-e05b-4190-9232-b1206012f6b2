INSERT INTO form_cust_dialog (id_, key_, name_, desc_, modify_desc_, style_, ds_key_, ds_name_, obj_type_, obj_name_, page_, page_size_, width_, height_, system_, multiple_, tree_config_json_, display_fields_json_, condition_fields_json_, return_fields_json_, sort_fields_json_, data_source_, primary_table_config_json_, data_relation_, dialog_layout_, other_config_json_, type_id_, create_time_, create_by_, update_time_, update_by_) VALUES('908737792316538881', 'THREAT_SECURITY_EVENT_TYPE', '风控-安全事件等级', '', NULL, 'tree', 'dataSourceDefault', '', 'table', '', 1, 10, 1000, 815, 0, 0, '{"showFilter":true,"checkStrictly":false,"pid":"parentId","id":"id","showColumn":"name"}', '[]', '[]', '[{"columnName":"key","returnName":"key"},{"columnName":"name","returnName":"name"}]', '[]', 'custApi', '{"conditionFields":[],"dataSource":"database","displayFields":[],"dsKey":"dataSourceDefault","height":0,"key":"THREAT_SECURITY_EVENT_TYPE","multiple":false,"objName":"","objType":"table","otherConfig":{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"","method":"get","name":"","desc":""},"apiReqColumns":[],"columns":[]}},"page":true,"pageSize":10,"primaryTablePercent":50,"returnFields":[],"sortFields":[],"style":"list","system":false,"treeConfig":{"checkStrictly":false,"pidInitValScript":false,"showFilter":false},"width":0}', 'singleTable', 'leftRight', '{"selectedFormatter":"","searchIgnorePrimaryTable":false,"apiInfo":"sys/sysTreeNode/getNodes?treeKey=THREAT_SECURITY_EVENT_TYPE(安全事件等级)","primaryKey":"","custApiConfig":{"baseInfo":{"fullPath":"sys/sysTreeNode/getNodes?treeKey=THREAT_SECURITY_EVENT_TYPE","method":"get","name":"安全事件等级","desc":""},"apiReqColumns":[],"columns":[{"name":"key","comment":"","type":"String","required":false,"primary":false},{"name":"id","comment":"","type":"String","required":false,"primary":false},{"name":"parentId","comment":"","type":"String","required":false,"primary":false},{"name":"name","comment":"类型","type":"String","required":false,"primary":false}]}}', '899865869605142529', '2024-12-04 10:36:26', '883016099668951041', '2024-12-04 10:36:26', '883016099668951041');
