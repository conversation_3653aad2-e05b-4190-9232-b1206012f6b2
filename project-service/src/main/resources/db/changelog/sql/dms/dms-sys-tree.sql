INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884093130405576705', 'DMS', '数据运维（DMS）', '用于DMS模块的实体管理', '9', '0', '884093130405576705.', 61, NULL, '2023-06-09 09:23:44', '884051084090277889', NULL);
INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884093481715236865', 'DMS', '数据运维（DMS）', '', '2', '0', '884093481715236865.', 62, '', '2023-06-09 09:34:54', '884051084090277889', NULL);
INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884093520966057985', 'DMS', '数据运维（DMS）', '', '6', '0', '884093520966057985.', 63, '', '2023-06-09 09:36:09', '884051084090277889', NULL);
INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884093559474487297', 'DMS', '数据运维（DMS）', '', '4', '0', '884093559474487297.', 9, '', '2023-06-09 09:37:22', '884051084090277889', NULL);
INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884093672996995073', 'DMS', '数据运维（DMS）', '', '5', '0', '884093672996995073.', 65, NULL, '2023-06-09 09:40:58', '884051084090277889', NULL);
INSERT INTO sys_tree_node (id_, key_, name_, desc_, tree_id_, parent_id_, path_, sn_, icon_, create_time_, create_by_, application_name_) VALUES ('884231139971563521', 'DMS', '数据运维（DMS）', '', '8', '882791158678814721', '882791158678814721.884231139971563521.', 3, '', '2023-06-12 10:30:56', '884051084090277889', NULL);
