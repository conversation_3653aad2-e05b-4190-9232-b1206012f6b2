spring:
  datasource:
    #    MySQL 连接
    driver-class-name: ${DB_DRIVER:com.p6spy.engine.spy.P6SpyDriver}
    url: ${DB_URL:***********************************************************************************************************************************************************************************************}
    username: ${DB_USER:root}
    password: ${DB_PASS:mysql}
    dbType: ${DB_TYPE:mysql}
#    url: ${DB_URL:*********************************}
#    username: ${DB_USER:NEWGWUOMP_202504}
#    # password: ${DB_PASS:51RhS6RR70Q4sThg}
#    password: ${DB_PASS:Qwertyuiop1234}
#    dbType: ${DB_TYPE:dmsql}
  # 若使用activemq 则需设置，默认直接使用的Redis做的mq
  #  activemq:
  #    broker-url: tcp://127.0.0.1:61616
  #    in-memory: false
  #    packages:
  #      trust-all: true
  # Redis 配置
  redis:
    host: ${REDIS_HOST:127.0.0.1}
    password: Qwas1212
    port: ${REDIS_PORT:6379}
    database: ${REDIS_DB:5}
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  # 分布式Session共享配置
  session:
    # 会话时长(单位：秒)
    timeout: 1800
    #session存储类型  none , redis，分布式下请使用Redis
    store-type: none
    redis:
      namespace: ecloud:session
  # 启动时sql初始化配置
  liquibase:
    enabled: ${LIQUIBASE_ENABLED:false}
    change-log: classpath:/db/changelog/db.changelog-master.xml
  # dataFormatter 使用 kafka 时配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVER:************:9211}    # Kafka服务地址和端口
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP:my-group} # 消费者组ID
      auto-offset-reset: earliest # 当没有初始偏移量或当前偏移量在日志末尾之外时，该值决定了从哪里开始读取
      maxPollRecords: 500 # 每批拉取的消息数量，默认 500
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      concurrency: 2 # 并发消费者数，默认1。和分区有关系，不要超过分区数
#      ack-mode: manual_immediate # 手动提交偏移量

# 若使用rocketmq则需要配置
rocketmq:
  name-server: ${SIMPLE_MQ_SERVER:172.16.1.113:9876}
  producer:
    group: ${SIMPLE_MQ_NAME:ecloud-oa-single-dev}
    subsystem-group: ${SUBSYSTEM_MQ_GROUP:ecloud-oa-single-subsystem-dev}
    send-message-timeout: 8000
logging:
  level:
    root: info
    cn.gwssi: DEBUG
    cn.gwssi.data.service: WARN
    cn.gwssi.isearch: WARN
    cn.gwssi.ecloud.module.dataFormatter: WARN
  file:
    path: ${LOG_FILE_PATH:./logs}

server:
  servlet:
    context-path: ${CONTEXT_PATH:/}

## activiti job 的定时刷新器，用于 Timer节点
activiti.jobExecutorActivate: ${ACT_JOB:false}

ecloud-api:
  platform:
    enable: ${ECLOUD_API_PLATFORM:false}
  iclient:
    url: ${EAPI_ICLIENT_URL:http://************:9201}
    username: ${EAPI_ICLIENT_USERNAME:gwssi}
    password: ${EAPI_ICLIENT_PASSWORD:root123}

## ecloud 的相关配置
ecloud:
  ##是否开启三员管理员账号功能
  sysThreeMember:
    enabled: ${SYS_THREE_MEMBER_ENABLED:false}
    password: ${SYS_THREE_MEMBER_PASSWORD:8452967605382f30ce61a7e309be222c947e6a5bddc4d3e3f8095fffed1fbf14}
  ## 是否开启JWT
  jwt:
    enabled: true
    header: Authorization
    #鉴权
  security:
    ## 忽略鉴权的URL
    auth-ingores: /health,/login.*,/ui/*,/eworker-ui-micro/*,/index.html,/doc.html,/swagger-*,/v2/api-docs*,/org/wx/login,/org/wx/bindUser,/webjars/*,/org/logout,/openapi/*,/sys/sysProperties/getLoginTypePropertyByAlias,/error,/org/collect/listJson,/org/subsystem/getDetailByAlias,/sys/sysFile/out/pic/*,/eworker-ui-single/*,/goffice-ui-single/*,/micro-portal/*,/module/app/config/app-*,/flow/message/*,/module/oa/article/*,/org/user/retrievePassword/*,/sso/checkSso,/org/login/getPicCode,/module/app/im/pc/version/platform,/pc-download/*,/module/app/im/message/callback/*,/module/alarmInfo/insert,
    ## 忽略防盗链的URL
    csrf-ingores: 127.0.0.1,localhost,192.168.,data-designer.ecloud.work,g1.ecloud.work,************,${CSRF_INGORES:}
    ## 忽略sso校验的URL
    sso-ingores: ${ecloud.security.auth-ingores}
    ## 最大session数量
    maximum-sessions: -1
    ## 是否启用xss
    xss:
      enable: ${SECURITY_XSS_ENABLE:false}
  ## 邮箱发送者的配置
  mail:
    mail-address: ${MAIL_ADDRESS:<EMAIL>}
    nick-name: ${MAIL_NICK_NAME:gwssitest2022}
    password: ${MAIL_PASSWORD:EGMEFHZWNFSCATWD}
    send-host: ${MAIL_SEND_HOST:smtp.163.com}
    send-port: ${MAIL_SEND_PORT:465}
    ssl: ${MAIL_SSL:true}
    templatePath: ${MAIL_TEMPLATEPATH:template/code.html}
  simple-mq:
    ## mq 队列配置：redis ：使用Redis做队列；synchronous: 同步，不使用队列；jms: activemq作为队列 ROCKET：使用rocketmq做队列
    name: ${SIMPLE_MQ_NAME:single-message-boot}
    message-queue-type: ${MESSAGE_QUEUE_TYPE:REDIS}
    rocket-consumer:
      consumer-group: ${SIMPLE_MQ_GROUP:ecloud-oa-single-dev}
      sub-expression: email || inner || sms || mobile || log || im || push
  ## 缓存配置 memory 内存，Redis，二级缓存 j2cache
  cache:
    type: j2cache
  ## @ABScheduled 定时计划是否开启
  schedule:
    enable: false
  ## groovy 引擎黑名单关键词，防止执行危险脚本
  groovy:
    blackKeywords: System,BeanUtil,JdbcTemplate,FileUtil,InputStream,IoUtil,FileWriter,ReflectUtil,ClassUtil
  # 使用时需要在引入maven 依赖 ecloudBPM-component-ldap
  #  ldap:
  #    server: ldap://127.0.0.1:10389/dc=ecloudBPM,dc=cn
  #    manager-d-n: uid=admin,ou=system
  #    manager-password: secret
  #    authentication-type: simple
  ## 上传配置： db 存数据库 ；ordinary 存本地磁盘，需要配置 uploader.ordinary.path存储目录。  具体请看 IUploader 实现类
  uploader:
    default: ${UPLOADER_TYPE:minio}
    minio:
      endpoint: http://172.16.1.113:9000
      accessKey: 6C28SPQM2LLJWA5761CD
      secretKey: sRrcB1OTC1CJK+2P9wsOQyPPPPu2Iqe7xmshUTfI
      bucket: ecloud-oa-dev
      path: /
    ordinary:
      path: ${UPLOADER_ORDINARY_PATH:/data}
  common-column:
    createUser: create_by
    createUserName: create_user
    createTime: create_time
    updateUser: update_by
    updateUserName: update_user
    updateTime: update_time
  log:
    mq:
      enabled: false
    dataSource:
      dbAlias: dataSourceDefault
      dbType: ${DB_TYPE:mysql}
  ## 候选人排序方式
  identity:
    order: ${SYSIDENTITY_ORDER:asc}
  exchange:
    processUrl: ${EXCHANGE_URL:https://oa-alpha.ecloud.work/oa-exchange/openapi/exchange/message/process/send}
    clientId: ${EXCHANGE_CLIENTID:418588903315865601}
    secretKey: ${EXCHANGE_SECRETKEY:f09c8201ce7517288651b15a4075a9bd}
  module:
    message:
      mail:
        address: <EMAIL>
        nickName: gwssitest2022
        password: EGMEFHZWNFSCATWD
        host: smtp.163.com
        port: 465
        ssl: true
  sso:
    enable: ${SSO_ENABLE:false}
    portalUrl: ${PORTAL_URL:http://dev.tenancy.platform.ecloud/eworker-ui-single/run-env/}
    # bamboo
    type: ${SSO_TYPE:epass}
    epass:
      url: ${SSO_EPASS_URL:http://dev.oa.ecloud/oa-single/}
      code-key: ${SSO_OAUTH_CODE_KEY:code}
      client-id: ${SSO_EPASS_CLIENT_ID:872643194760200193}
      client-secret: ${SSO_EPASS_CLIENT_SECRET:516a753bf8d095573d1021b17842e0e7}
      token-key: ${SSO_EPASS_TOKEN_KEY:ssoToken}
## 二级缓存配置
j2cache:
  serialization: java
  default-cache-null-object: true
  sync-ttl-to-redis: true
  L1:
    provider-class: caffeine
    region:
      # 缓存对象数量上限 缓存时间
      default: 10000, 12h
      # jwt token default period of validity
      jwtToken: 1000, 1d
      # 微信 Access Token 默认有效期（移动端整合微信使用）
      WX_ACCESS_TOKEN: 1, 2h
      loginCode: 1000, 5m
      mailCode: 1000, 5m
      ssoCode: 1000, 5m
  L2:
    cache-open: true
    provider-class: net.oschina.j2cache.cache.support.redis.SpringRedisProvider
    storage: redis
    channel: j2cache
  broadcast:
    cache-clean-mode: blend
    provider-class: net.oschina.j2cache.cache.support.redis.SpringRedisPubSubPolicy

## 分布式定时计划，如果开启请配置address ，其他参考http://www.ecloudBPM.cn/zh-cn/docs/schedule.html
xxl:
  job:
    admin:
      ### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
      addresses: ${XXLJOB_ADDRESS:http://*************:8092/xxl-job-admin}
      userName: ${XXLJOB_USERNAME:admin}
      password: ${XXLJOB_PASSWORD:123456}
      executorId: ${XXLJOB_EXECUTORID:10}
      loginUrl: ${xxl.job.admin.addresses}/login
      addUrl: ${xxl.job.admin.addresses}/jobinfo/add
      updateUrl: ${xxl.job.admin.addresses}/jobinfo/update
      deleteUrl: ${xxl.job.admin.addresses}/jobinfo/remove
      startJobUrl: ${xxl.job.admin.addresses}/jobinfo/start
      stopJobUrl: ${xxl.job.admin.addresses}/jobinfo/stop
      triggerJobUrl: ${xxl.job.admin.addresses}/jobinfo/trigger
    #      addresses: ${XXLJOB_ADDRESS:http://************:8092/xxl-job-admin}
    ### xxl-job executor address http://*************:9990/xxl-job-admin
    executor:
      appname: ${XXLJOB_APPNAME:ecloud-gwuomp-job}
      ip:
      port: 9999
      ### xxl-job log path
      logpath: ./run/xxl-job/jobhandler
      ### xxl-job log retention days
      logretentiondays: -1
    ### xxl-job, access token
    accessToken:

# 数据转化
dataFormatter:
  # system：直接调用 controller 接口
  # kafka：从 kafka 推送
  source: ${DATA_SOURCE:system}
  # system: 写入系统使用的数据库
  # es: 写入 es
  target: ${DATA_TARGET:system}
  # kafka 的多个topic，每个topic对应一个 consumer，不用的可以注释掉
  kafka:
    #    topics: prom_alert,prom_metrics,grafana_alert,grafana_metrics
    topics: prom_alert

# iSearch 相关的配置
iSearch:
  url: ${ISEARCH_URL:http://************:9200} # 多个地址用 , 分隔
  username: ${ISEARCH_UN:gwssi} # 集群账号
  password: ${ISEARCH_PW:gwssi_240329_!@_ly} # 集群密码
  path: ${ISEARCH_PATH:iSearch} # xml文件的路径，/src/main/resources/iclient 目录
  connection-timeout: 30000
  read-timeout: 300000
  max-connection: 100
  max-connection-per-route: 50
  max-connection-idle-time: 60

## prometheus参数
prometheus:
  consul:
    url: ${PROMETHEUS_CONSUL_URL:http://*************:8550}
  token:  ${PROMETHEUS_TOKEN:c43528e2-e5c8-5f6a-180d-719f149901d8}
  query:
    url: ${PROMETHEUS_QUERY_URL:http://************:8427/select/0/prometheus}
  admin: ${PROMETHEUS_ADMIN:admin}
  password: ${PROMETHEUS_PASSWORD:ecloud}
  snmp:
    url: ${PROMETHEUS_SNMP_URL:*************}
  type: ${PROMETHEUS_TYPE:vm} #监控实现方式 prometheus , vm
  silence:
    url: ${PROMETHEUS_SILENCE_URL:http://************:9093/api/v2}
  ipmi:
    url: ${PROMETHEUS_IPMI_URL:************}
    port: ${PROMETHEUS_IPMI_PORT:9290}

ocr:
  base-url: ${OCR_BASE_URL:http://localhost:44432}
asr:
  base-url: ${ASR_BASE_URL:http://************:8078}

webhook:
  origin-url: ${WEBHOOK_ORIGIN_URL:http://newgwuomp-backend.newgwuomp-sit:8080}
  split-word: ${WEBHOOK_SPLIT_WORD:/oa-single}

#机器人算法端配置
robot:
  chat-base-url: ${CHAT_BASE_URL:http://************:50010/sentence_embedding/}
  dialog-base-url: ${DIALOG_BASE_URL:http://************:6380/robot_sentence_embedding/}
  rag-base-url: ${RAG_BASE_URL:http://************:9009}
  flow-base-url: ${FLOW_BASE_URL:http://************:6380/robot_sentence_embedding/}
  llm-base-url: ${LLM_BASE_URL:http://***********:9087}
  file-view-url: ${FILE_VIEW_URL:}
  default-robot-id: ${DEFAULT_ROBOT_ID:}
  rag-default-score-threshold: ${RAG_DEFAULT_SCORE_THRESHOLD:}
  default-role-id: ${RAG_DEFAULT_ROLE_ID:}
iSearchDB:
  url: ${ISEARCHDB_URL:http://***********:8200}
  username: ${ISEARCHDB_USER:gwssi}
  password: ${ISEARCHDB_PASS:root123}

log:
  es:
    ip: ${LOG_ES_IP:***********1}
    port: ${LOG_ES_PORT:9200}
    username: ${LOG_ES_USERNAME:}
    password: ${LOG_ES_PASSWORD:}

sms:
  type: ${SMS_TYPE:aliyun}
  jyc:
    port: ${SMS_JYC_PORT:8234}

