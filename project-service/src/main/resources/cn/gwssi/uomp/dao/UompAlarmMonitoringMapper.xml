<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompAlarmMonitoringMapper">


    <select id="selectResourceIdByIp" resultType="java.lang.String">
        select RESOURCE_ID as resourceId from UOMP_ALARM_MONITORING
        where IP_ADDRESS = #{ipAddress} and PORT = #{port} and MONITORING_STATUS = '1' and DEL_FLAG = '0'
        LIMIT 0,1
    </select>


    <select id="getAutoConfigList" resultType="java.util.Map">
        select
        ID as id,
        ALARM_LEVEL as alarmLevel,
        EVENT_TYPE_ID as eventTypeId,
        EVENT_TYPE_NAME as eventTypeName,
        EVENT_TYPE_CODE as eventTypeCode,
        EVENT_LEVEL_ID as eventLevelId,
        EVENT_LEVEL_NAME as eventLevelName,
        BIZ_GROUP_ID as bizGroupId,
        BIZ_GROUP_NAME as bizGroupName
        from UOMP_ALARM_AUTO_CONFIG
        where DEL_FLAG = '0'
    </select>

    <select id="getRuleList" resultType="java.util.Map">
        select
            a.NAME as name,
            a.EN_NAME as code
        from UOMP_ALARM_RULE_MANAGEMENT a
        where a.DEL_FLAG = '0'
    </select>

    <select id="getGroupLeaderIds" resultType="java.lang.String">
        SELECT GROUP_LEADER_IDS FROM UOMP_TEAM WHERE ID = #{id}
    </select>

    <select id="getRuleEnNameList" resultType="java.lang.String">
        select
            a.EN_NAME as code
        from UOMP_ALARM_RULE_MANAGEMENT a
        where a.DEL_FLAG = '0' and ALARM_ENABLE = '0'
    </select>
</mapper>
