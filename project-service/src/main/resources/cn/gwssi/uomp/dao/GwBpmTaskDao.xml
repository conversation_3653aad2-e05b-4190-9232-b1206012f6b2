<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.GwBpmTaskDao">
	<resultMap id="BpmTaskVO" type="cn.gwssi.ecloudbpm.wf.core.vo.BpmTaskVO">
		<id property="id" column="id_" jdbcType="VARCHAR"/>
		<result property="name" column="name_" jdbcType="VARCHAR"/>
		<result property="subject" column="subject_" jdbcType="VARCHAR"/>
		<result property="instId" column="inst_id_" jdbcType="VARCHAR"/>
		<result property="taskId" column="task_id_" jdbcType="VARCHAR"/>
		<result property="nodeId" column="node_id_" jdbcType="VARCHAR"/>
		<result property="defId" column="def_id_" jdbcType="VARCHAR"/>
		<result property="assigneeId" column="assignee_id_" jdbcType="VARCHAR"/>
		<result property="assigneeNames" column="assignee_names_" jdbcType="VARCHAR"/>
		<result property="status" column="status_" jdbcType="VARCHAR"/>
		<result property="priority" column="priority_" jdbcType="NUMERIC"/>
		<result property="dueTime" column="due_time_" jdbcType="TIMESTAMP"/>
		<result property="taskType" column="task_type_" jdbcType="VARCHAR"/>
		<result property="parentId" column="parent_id_" jdbcType="VARCHAR"/>
		<result property="actInstId" column="act_inst_id_" jdbcType="VARCHAR"/>
		<result property="actExecutionId" column="act_execution_id_" jdbcType="VARCHAR"/>
		<result property="typeId" column="type_id_" jdbcType="VARCHAR"/>
		<result property="createTime" column="create_time_" jdbcType="TIMESTAMP"/>
		<result property="createBy" column="create_by_" jdbcType="VARCHAR"/>
		<result property="supportMobile" column="support_mobile_" jdbcType="NUMERIC"/>
		<result property="backNode" column="back_node_" jdbcType="VARCHAR"/>
		<result property="creator" column="creator_" jdbcType="VARCHAR"/>
		<result property="checkTime" column="check_time_" jdbcType="TIMESTAMP"/>
		<result property="checkStatus" column="check_status_" jdbcType="VARCHAR"/>
		<result property="linkId" column="link_id_" jdbcType="VARCHAR"/>
		<result property="instStatus" column="inst_status_" jdbcType="VARCHAR"/>
		<result property="linkTaskType" column="link_task_type_" jdbcType="VARCHAR"/>
		<result property="instCreateTime" column="inst_create_time_" jdbcType="VARCHAR"/>
		<result property="instName" column="inst_name_" jdbcType="VARCHAR"/>
		<result property="nodeTypeKey" column="node_type_key_" jdbcType="VARCHAR"/>
		<result property="nodeTypeName" column="node_type_name_" jdbcType="VARCHAR"/>
		<result property="linkOrgId" column="link_org_id_" jdbcType="VARCHAR"/>
		<result property="linkIdentity" column="link_identity_" jdbcType="VARCHAR"/>
		<result property="linkIdentityName" column="link_identity_name_" jdbcType="VARCHAR"/>
	</resultMap>
	<resultMap id="UompTaskIdentityLink" type="cn.gwssi.ecloudbpm.wf.core.model.TaskIdentityLink">
		<id property="id" column="id_" jdbcType="VARCHAR"/>
		<result property="taskId" column="task_id_" jdbcType="VARCHAR"/>
		<result property="instId" column="inst_id_" jdbcType="VARCHAR"/>
		<result property="type" column="type_" jdbcType="VARCHAR"/>
		<result property="identityName" column="identity_name_" jdbcType="VARCHAR"/>
		<result property="identity" column="identity_" jdbcType="VARCHAR"/>
		<result property="permissionCode" column="permission_code_" jdbcType="VARCHAR"/>
		<result property="taskType" column="task_type_" jdbcType="VARCHAR"/>
		<result property="checkStatus" column="check_status_" jdbcType="VARCHAR"/>
		<result property="checkTime" column="check_time_" jdbcType="TIMESTAMP"/>
		<result property="orgId" column="org_id_" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="getTodoList" parameterType="java.util.Map" resultMap="BpmTaskVO">
		SELECT
			task.id_, task.name_, task.subject_, task.inst_id_, task.task_id_,
			task.act_inst_id_, task.act_execution_id_, task.node_id_, task.def_id_,
			task.assignee_id_, task.status_,
			task.priority_, task.due_time_, task.task_type_, task.parent_id_, task.type_id_,
			task.create_time_, task.create_by_, task.support_mobile_, task.back_node_,
			opinion.creator_,
			instance.STATUS_ inst_status_,
			instance.CREATE_TIME_ inst_create_time_,
			instance.def_name_ inst_name_,
			linkd.check_status_,linkd.check_time_,
			node.KEY_ node_type_key_,
			node.NAME_ node_type_name_,
			linkd.id_ link_id_,linkd.task_type_ link_task_type_
		FROM bpm_task task
		 LEFT JOIN bpm_task_identitylink linkd on task.id_ = linkd.task_id_
		 LEFT JOIN BPM_TASK_OPINION opinion on opinion.task_id_ =  task.id_
		 LEFT JOIN bpm_instance instance on task.inst_id_ = instance.id_
		 LEFT JOIN SYS_TREE_NODE node on task.TYPE_ID_ = node.ID_
		<where>			
			<if test="whereSql!=null">
				${whereSql}
			</if>
			and ((task.assignee_id_ = '0' and linkd.permission_code_ in  <foreach collection="userRights" index="index" item="permissionCode" open="(" separator="," close=")">#{permissionCode}</foreach> )
			or (task.assignee_id_ = #{userId} AND (task.STATUS_ != 'LOCK' OR (task.STATUS_ = 'LOCK' and (linkd.IDENTITY_ = #{userId} or linkd.TYPE_ != 'user') ))))
			AND instance.is_test_data_ = 0
		</where>
		<if test="orderBySql!=null">
			ORDER BY ${orderBySql}
		</if>
		<if test="orderBySql==null">
			ORDER BY task.id_ DESC
		</if>
	</select>
	<select id="getByInstId" parameterType="java.lang.String" resultMap="UompTaskIdentityLink">
		SELECT * FROM bpm_task_identitylink
		WHERE
		INST_ID_ = #{taskId}
	</select>
    <select id="getCurrentTeamLeader" resultType="cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity" parameterType="java.lang.String">
		select g.id_ as id,g.fullname_ || '(' || t.name || ')' as name,t.id as orgId,'user' as "type"
		from uomp_team t
		inner join org_relation r on t.id = r.group_id_
		inner join user_group_org g on instr(t.group_leader_ids,g.ID_ )>0
		where r.user_id_ = #{userid}
		and t.status = '1'
		order by t.ID
	</select>
	<select id="getCurrentTeamManager" resultType="cn.gwssi.ecloudframework.sys.api.model.DefaultIdentity" parameterType="java.lang.String">
		select g.id_ as id,g.fullname_ || '(' || t.name || ')' as name,t.ID as orgId,'user' as "type" from UOMP_TEAM t,ORG_RELATION r
			inner join user_group_org g on g.ID_ = r.USER_ID_
			where t.ID = r.GROUP_ID_
			and t.ID in (
				select b.group_id_ from UOMP_TEAM a,ORG_RELATION b
				where a.ID = b.GROUP_ID_ and b.USER_ID_ = #{userid}
			)
			and r.USER_ID_ in (
				 select user_id_ from org_relation o,org_post p where o.type_='postUser' and o.group_id_ = p.ID_  and p.name_= #{postname}
			)
		order by t.id
	</select>

</mapper>