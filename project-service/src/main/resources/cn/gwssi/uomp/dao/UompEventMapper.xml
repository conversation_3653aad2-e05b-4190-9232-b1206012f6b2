<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompEventMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompEvent">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orderNo" column="ORDER_NO" jdbcType="VARCHAR"/>
            <result property="orderId" column="ORDER_ID" jdbcType="CHAR"/>
            <result property="parentOrderId" column="PARENT_ORDER_ID" jdbcType="VARCHAR"/>
            <result property="slaId" column="SLA_ID" jdbcType="VARCHAR"/>
            <result property="slaName" column="SLA_NAME" jdbcType="VARCHAR"/>
            <result property="slaLevel" column="SLA_LEVEL" jdbcType="VARCHAR"/>
            <result property="applicant" column="APPLICANT" jdbcType="VARCHAR"/>
            <result property="applicantOrg" column="APPLICANT_ORG" jdbcType="VARCHAR"/>
            <result property="applicantTel" column="APPLICANT_TEL" jdbcType="VARCHAR"/>
            <result property="applicantOrgId" column="APPLICANT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="applicantOrgName" column="APPLICANT_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="registrantId" column="REGISTRANT_ID" jdbcType="VARCHAR"/>
            <result property="registrant" column="REGISTRANT" jdbcType="VARCHAR"/>
            <result property="registrantOrg" column="REGISTRANT_ORG" jdbcType="VARCHAR"/>
            <result property="registrantNo" column="REGISTRANT_NO" jdbcType="VARCHAR"/>
            <result property="eventLevel" column="EVENT_LEVEL" jdbcType="VARCHAR"/>
            <result property="eventTitle" column="EVENT_TITLE" jdbcType="VARCHAR"/>
            <result property="eventDesc" column="EVENT_DESC" jdbcType="VARCHAR"/>
            <result property="eventTime" column="EVENT_TIME" jdbcType="TIMESTAMP"/>
            <result property="serviceType" column="SERVICE_TYPE" jdbcType="VARCHAR"/>
            <result property="resourceId" column="RESOURCE_ID" jdbcType="VARCHAR"/>
            <result property="projectId" column="PROJECT_ID" jdbcType="VARCHAR"/>
            <result property="fileStr" column="FILE_STR" jdbcType="VARCHAR"/>
            <result property="ifFeedback" column="IF_FEEDBACK" jdbcType="VARCHAR"/>
            <result property="feedbackTime" column="FEEDBACK_TIME" jdbcType="TIMESTAMP"/>
            <result property="ifSplitChild" column="IF_SPLIT_CHILD" jdbcType="VARCHAR"/>
            <result property="expectTime" column="EXPECT_TIME" jdbcType="TIMESTAMP"/>
            <result property="slaOverTime" column="SLA_OVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="linkOverTime" column="LINK_OVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="flowOverTime" column="FLOW_OVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="ifPause" column="IF_PAUSE" jdbcType="VARCHAR"/>
            <result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="extends1" column="EXTENDS1" jdbcType="VARCHAR"/>
            <result property="extends2" column="EXTENDS2" jdbcType="VARCHAR"/>
            <result property="extends3" column="EXTENDS3" jdbcType="VARCHAR"/>
            <result property="extends4" column="EXTENDS4" jdbcType="VARCHAR"/>
            <result property="extends5" column="EXTENDS5" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="teamId" column="TEAM_ID" jdbcType="VARCHAR"/>
            <result property="ifVip" column="IF_VIP" jdbcType="VARCHAR"/>
            <result property="eventType" column="EVENT_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ORDER_NO,ORDER_ID,
        PARENT_ORDER_ID,SLA_ID,SLA_NAME,
        SLA_LEVEL,APPLICANT,APPLICANT_ORG,
        APPLICANT_TEL,APPLICANT_ORG_ID,APPLICANT_ORG_NAME,
        REGISTRANT_ID,REGISTRANT,REGISTRANT_ORG,
        REGISTRANT_NO,EVENT_LEVEL,EVENT_TITLE,
        EVENT_DESC,EVENT_TIME,SERVICE_TYPE,
        RESOURCE_ID,PROJECT_ID,FILE_STR,
        IF_FEEDBACK,FEEDBACK_TIME,IF_SPLIT_CHILD,
        EXPECT_TIME,SLA_OVER_TIME,LINK_OVER_TIME,
        FLOW_OVER_TIME,IF_PAUSE,INST_ID,
        REMARK,EXTENDS1,EXTENDS2,
        EXTENDS3,EXTENDS4,EXTENDS5,
        CREATE_BY,CREATE_TIME,CREATE_ORG_ID,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG,
        TEAM_ID,IF_VIP,EVENT_TYPE
    </sql>
</mapper>
