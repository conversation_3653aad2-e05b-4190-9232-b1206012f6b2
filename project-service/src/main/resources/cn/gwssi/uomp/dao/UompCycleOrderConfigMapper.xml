<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompCycleOrderConfigMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompCycleOrderConfig">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orderType" column="ORDER_TYPE" jdbcType="VARCHAR"/>
            <result property="orderTitle" column="ORDER_TITLE" jdbcType="VARCHAR"/>
            <result property="orderDesc" column="ORDER_DESC" jdbcType="VARCHAR"/>
            <result property="slaId" column="SLA_ID" jdbcType="VARCHAR"/>
            <result property="slaName" column="SLA_NAME" jdbcType="VARCHAR"/>
            <result property="eventType" column="EVENT_TYPE" jdbcType="VARCHAR"/>
            <result property="eventLevelId" column="EVENT_LEVEL_ID" jdbcType="VARCHAR"/>
            <result property="eventLevel" column="EVENT_LEVEL" jdbcType="VARCHAR"/>
            <result property="templateId" column="TEMPLATE_ID" jdbcType="VARCHAR"/>
            <result property="templateName" column="TEMPLATE_NAME" jdbcType="VARCHAR"/>
            <result property="templateJson" column="TEMPLATE_JSON" jdbcType="VARCHAR"/>
            <result property="templateVersion" column="TEMPLATE_VERSION" jdbcType="VARCHAR"/>
            <result property="execType" column="EXEC_TYPE" jdbcType="VARCHAR"/>
            <result property="execWeek" column="EXEC_WEEK" jdbcType="VARCHAR"/>
            <result property="execMonthType" column="EXEC_MONTH_TYPE" jdbcType="VARCHAR"/>
            <result property="execRemark" column="EXEC_REMARK" jdbcType="VARCHAR"/>
            <result property="jobId" column="JOB_ID" jdbcType="VARCHAR"/>
            <result property="cronCode" column="CRON_CODE" jdbcType="VARCHAR"/>
            <result property="ifSkipHoliday" column="IF_SKIP_HOLIDAY" jdbcType="VARCHAR"/>
            <result property="planState" column="PLAN_STATE" jdbcType="VARCHAR"/>
            <result property="planStartTime" column="PLAN_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="planEndTime" column="PLAN_END_TIME" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="flowDefId" column="FLOW_DEF_ID" jdbcType="VARCHAR"/>
            <result property="flowDefKey" column="FLOW_DEF_KEY" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="handlerMap" type="cn.gwssi.uomp.entity.UompCycleOrderHandler">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="manageId" column="MANAGE_ID" jdbcType="VARCHAR"/>
        <result property="handlerType" column="HANDLER_TYPE" jdbcType="VARCHAR"/>
        <result property="handlerId" column="HANDLER_ID" jdbcType="VARCHAR"/>
        <result property="handler" column="HANDLER" jdbcType="VARCHAR"/>
        <result property="handlerOrgId" column="HANDLER_ORG_ID" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="timeMap" type="cn.gwssi.uomp.entity.UompCycleOrderTime">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="manageId" column="MANAGE_ID" jdbcType="VARCHAR"/>
        <result property="execType" column="EXEC_TYPE" jdbcType="VARCHAR"/>
        <result property="execDay" column="EXEC_DAY" jdbcType="DECIMAL"/>
        <result property="execHour" column="EXEC_HOUR" jdbcType="VARCHAR"/>
        <result property="execMinute" column="EXEC_MINUTE" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ORDER_TYPE,ORDER_TITLE,
        ORDER_DESC,SLA_ID,SLA_NAME,EVENT_TYPE,
        EVENT_LEVEL_ID,EVENT_LEVEL,TEMPLATE_ID,
        TEMPLATE_NAME,TEMPLATE_JSON,TEMPLATE_VERSION,
        EXEC_TYPE,EXEC_WEEK,EXEC_MONTH_TYPE,
        EXEC_REMARK,JOB_ID,CRON_CODE,
        IF_SKIP_HOLIDAY,PLAN_STATE,PLAN_START_TIME,
        PLAN_END_TIME,CREATE_BY,CREATE_TIME,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG,FLOW_DEF_ID,FLOW_DEF_KEY
    </sql>

    <select id="getConfigList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
         from UOMP_CYCLE_ORDER_CONFIG
         where DEL_FLAG = '0'
        <if test="param != null and param!='' ">
            and id = #{param}
        </if>
        and PLAN_STATE = '1' and
        to_date(to_char(sysdate,'yyyy-MM-dd'),'yyyy-MM-dd') between PLAN_START_TIME and PLAN_END_TIME
    </select>

    <select id="getObjectList" parameterType="java.lang.String" resultMap="handlerMap">
        select ID, MANAGE_ID, decode(HANDLER_TYPE,'team','post',HANDLER_TYPE) as HANDLER_TYPE, HANDLER_ID, HANDLER,HANDLER_ORG_ID, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
        from UOMP_CYCLE_ORDER_HANDLER
        where MANAGE_ID = #{manageId} and del_flag = '0'
    </select>

    <select id="getExecTimeList"  parameterType="java.lang.String" resultMap="timeMap">
        select ID, MANAGE_ID, EXEC_TYPE, EXEC_DAY, EXEC_HOUR,EXEC_MINUTE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
        from UOMP_CYCLE_ORDER_TIME
        where MANAGE_ID = #{manageId} and del_flag = '0'
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_CYCLE_ORDER_CONFIG
        where  ID = #{id,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from UOMP_CYCLE_ORDER_CONFIG
        where  ID = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompCycleOrderConfig" useGeneratedKeys="true">
        insert into UOMP_CYCLE_ORDER_CONFIG
        ( ID,ORDER_TYPE,ORDER_TITLE
        ,ORDER_DESC,SLA_ID,EVENT_TYPE
        ,EVENT_LEVEL_ID,EVENT_LEVEL,TEMPLATE_ID
        ,TEMPLATE_NAME,TEMPLATE_JSON,TEMPLATE_VERSION
        ,EXEC_TYPE,EXEC_WEEK,EXEC_MONTH_TYPE
        ,EXEC_REMARK,JOB_ID,CRON_CODE
        ,IF_SKIP_HOLIDAY,PLAN_STATE,PLAN_START_TIME
        ,PLAN_END_TIME,CREATE_BY,CREATE_TIME
        ,UPDATE_BY,UPDATE_TIME,DEL_FLAG
        )
        values (#{id,jdbcType=VARCHAR},#{orderType,jdbcType=VARCHAR},#{orderTitle,jdbcType=VARCHAR}
        ,#{orderDesc,jdbcType=VARCHAR},#{slaId,jdbcType=VARCHAR},#{eventType,jdbcType=VARCHAR}
        ,#{eventLevelId,jdbcType=VARCHAR},#{eventLevel,jdbcType=VARCHAR},#{templateId,jdbcType=VARCHAR}
        ,#{templateName,jdbcType=VARCHAR},#{templateJson,jdbcType=VARCHAR},#{templateVersion,jdbcType=VARCHAR}
        ,#{execType,jdbcType=VARCHAR},#{execWeek,jdbcType=VARCHAR},#{execMonthType,jdbcType=VARCHAR}
        ,#{execRemark,jdbcType=VARCHAR},#{jobId,jdbcType=VARCHAR},#{cronCode,jdbcType=VARCHAR}
        ,#{ifSkipHoliday,jdbcType=VARCHAR},#{planState,jdbcType=VARCHAR},#{planStartTime,jdbcType=TIMESTAMP}
        ,#{planEndTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{delFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompCycleOrderConfig" useGeneratedKeys="true">
        insert into UOMP_CYCLE_ORDER_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">ID,</if>
                <if test="orderType != null">ORDER_TYPE,</if>
                <if test="orderTitle != null">ORDER_TITLE,</if>
                <if test="orderDesc != null">ORDER_DESC,</if>
                <if test="slaId != null">SLA_ID,</if>
                <if test="eventType != null">EVENT_TYPE,</if>
                <if test="eventLevelId != null">EVENT_LEVEL_ID,</if>
                <if test="eventLevel != null">EVENT_LEVEL,</if>
                <if test="templateId != null">TEMPLATE_ID,</if>
                <if test="templateName != null">TEMPLATE_NAME,</if>
                <if test="templateJson != null">TEMPLATE_JSON,</if>
                <if test="templateVersion != null">TEMPLATE_VERSION,</if>
                <if test="execType != null">EXEC_TYPE,</if>
                <if test="execWeek != null">EXEC_WEEK,</if>
                <if test="execMonthType != null">EXEC_MONTH_TYPE,</if>
                <if test="execRemark != null">EXEC_REMARK,</if>
                <if test="jobId != null">JOB_ID,</if>
                <if test="cronCode != null">CRON_CODE,</if>
                <if test="ifSkipHoliday != null">IF_SKIP_HOLIDAY,</if>
                <if test="planState != null">PLAN_STATE,</if>
                <if test="planStartTime != null">PLAN_START_TIME,</if>
                <if test="planEndTime != null">PLAN_END_TIME,</if>
                <if test="createBy != null">CREATE_BY,</if>
                <if test="createTime != null">CREATE_TIME,</if>
                <if test="updateBy != null">UPDATE_BY,</if>
                <if test="updateTime != null">UPDATE_TIME,</if>
                <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null"> #{id,jdbcType=VARCHAR},</if>
                <if test="orderType != null"> #{orderType,jdbcType=VARCHAR},</if>
                <if test="orderTitle != null"> #{orderTitle,jdbcType=VARCHAR},</if>
                <if test="orderDesc != null"> #{orderDesc,jdbcType=VARCHAR},</if>
                <if test="slaId != null"> #{slaId,jdbcType=VARCHAR},</if>
                <if test="eventType != null"> #{eventType,jdbcType=VARCHAR},</if>
                <if test="eventLevelId != null"> #{eventLevelId,jdbcType=VARCHAR},</if>
                <if test="eventLevel != null"> #{eventLevel,jdbcType=VARCHAR},</if>
                <if test="templateId != null"> #{templateId,jdbcType=VARCHAR},</if>
                <if test="templateName != null"> #{templateName,jdbcType=VARCHAR},</if>
                <if test="templateJson != null"> #{templateJson,jdbcType=VARCHAR},</if>
                <if test="templateVersion != null"> #{templateVersion,jdbcType=VARCHAR},</if>
                <if test="execType != null"> #{execType,jdbcType=VARCHAR},</if>
                <if test="execWeek != null"> #{execWeek,jdbcType=VARCHAR},</if>
                <if test="execMonthType != null"> #{execMonthType,jdbcType=VARCHAR},</if>
                <if test="execRemark != null"> #{execRemark,jdbcType=VARCHAR},</if>
                <if test="jobId != null"> #{jobId,jdbcType=VARCHAR},</if>
                <if test="cronCode != null"> #{cronCode,jdbcType=VARCHAR},</if>
                <if test="ifSkipHoliday != null"> #{ifSkipHoliday,jdbcType=VARCHAR},</if>
                <if test="planState != null"> #{planState,jdbcType=VARCHAR},</if>
                <if test="planStartTime != null"> #{planStartTime,jdbcType=TIMESTAMP},</if>
                <if test="planEndTime != null"> #{planEndTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null"> #{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null"> #{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null"> #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.uomp.entity.UompCycleOrderConfig">
        update UOMP_CYCLE_ORDER_CONFIG
        <set>
                <if test="orderType != null">
                    ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
                </if>
                <if test="orderTitle != null">
                    ORDER_TITLE = #{orderTitle,jdbcType=VARCHAR},
                </if>
                <if test="orderDesc != null">
                    ORDER_DESC = #{orderDesc,jdbcType=VARCHAR},
                </if>
                <if test="slaId != null">
                    SLA_ID = #{slaId,jdbcType=VARCHAR},
                </if>
                <if test="eventType != null">
                    EVENT_TYPE = #{eventType,jdbcType=VARCHAR},
                </if>
                <if test="eventLevelId != null">
                    EVENT_LEVEL_ID = #{eventLevelId,jdbcType=VARCHAR},
                </if>
                <if test="eventLevel != null">
                    EVENT_LEVEL = #{eventLevel,jdbcType=VARCHAR},
                </if>
                <if test="templateId != null">
                    TEMPLATE_ID = #{templateId,jdbcType=VARCHAR},
                </if>
                <if test="templateName != null">
                    TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR},
                </if>
                <if test="templateJson != null">
                    TEMPLATE_JSON = #{templateJson,jdbcType=VARCHAR},
                </if>
                <if test="templateVersion != null">
                    TEMPLATE_VERSION = #{templateVersion,jdbcType=VARCHAR},
                </if>
                <if test="execType != null">
                    EXEC_TYPE = #{execType,jdbcType=VARCHAR},
                </if>
                <if test="execWeek != null">
                    EXEC_WEEK = #{execWeek,jdbcType=VARCHAR},
                </if>
                <if test="execMonthType != null">
                    EXEC_MONTH_TYPE = #{execMonthType,jdbcType=VARCHAR},
                </if>
                <if test="execRemark != null">
                    EXEC_REMARK = #{execRemark,jdbcType=VARCHAR},
                </if>
                <if test="jobId != null">
                    JOB_ID = #{jobId,jdbcType=VARCHAR},
                </if>
                <if test="cronCode != null">
                    CRON_CODE = #{cronCode,jdbcType=VARCHAR},
                </if>
                <if test="ifSkipHoliday != null">
                    IF_SKIP_HOLIDAY = #{ifSkipHoliday,jdbcType=VARCHAR},
                </if>
                <if test="planState != null">
                    PLAN_STATE = #{planState,jdbcType=VARCHAR},
                </if>
                <if test="planStartTime != null">
                    PLAN_START_TIME = #{planStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="planEndTime != null">
                    PLAN_END_TIME = #{planEndTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    CREATE_BY = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
                </if>
        </set>
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.gwssi.uomp.entity.UompCycleOrderConfig">
        update UOMP_CYCLE_ORDER_CONFIG
        set 
            ORDER_TYPE =  #{orderType,jdbcType=VARCHAR},
            ORDER_TITLE =  #{orderTitle,jdbcType=VARCHAR},
            ORDER_DESC =  #{orderDesc,jdbcType=VARCHAR},
            SLA_ID =  #{slaId,jdbcType=VARCHAR},
            EVENT_TYPE =  #{eventType,jdbcType=VARCHAR},
            EVENT_LEVEL_ID =  #{eventLevelId,jdbcType=VARCHAR},
            EVENT_LEVEL =  #{eventLevel,jdbcType=VARCHAR},
            TEMPLATE_ID =  #{templateId,jdbcType=VARCHAR},
            TEMPLATE_NAME =  #{templateName,jdbcType=VARCHAR},
            TEMPLATE_JSON =  #{templateJson,jdbcType=VARCHAR},
            TEMPLATE_VERSION =  #{templateVersion,jdbcType=VARCHAR},
            EXEC_TYPE =  #{execType,jdbcType=VARCHAR},
            EXEC_WEEK =  #{execWeek,jdbcType=VARCHAR},
            EXEC_MONTH_TYPE =  #{execMonthType,jdbcType=VARCHAR},
            EXEC_REMARK =  #{execRemark,jdbcType=VARCHAR},
            JOB_ID =  #{jobId,jdbcType=VARCHAR},
            CRON_CODE =  #{cronCode,jdbcType=VARCHAR},
            IF_SKIP_HOLIDAY =  #{ifSkipHoliday,jdbcType=VARCHAR},
            PLAN_STATE =  #{planState,jdbcType=VARCHAR},
            PLAN_START_TIME =  #{planStartTime,jdbcType=TIMESTAMP},
            PLAN_END_TIME =  #{planEndTime,jdbcType=TIMESTAMP},
            CREATE_BY =  #{createBy,jdbcType=VARCHAR},
            CREATE_TIME =  #{createTime,jdbcType=TIMESTAMP},
            UPDATE_BY =  #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME =  #{updateTime,jdbcType=TIMESTAMP},
            DEL_FLAG =  #{delFlag,jdbcType=VARCHAR}
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>
</mapper>
