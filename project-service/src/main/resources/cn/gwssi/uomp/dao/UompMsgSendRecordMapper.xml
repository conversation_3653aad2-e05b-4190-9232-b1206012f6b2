<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompMsgSendRecordMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompMsgSendRecord">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="msgId" column="MSG_ID" jdbcType="VARCHAR"/>
            <result property="busId" column="BUS_ID" jdbcType="VARCHAR"/>
            <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
            <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
            <result property="msgContent" column="MSG_CONTENT" jdbcType="VARCHAR"/>
            <result property="sendTime" column="SEND_TIME" jdbcType="TIMESTAMP"/>
            <result property="nextSendTime" column="NEXT_SEND_TIME" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,MSG_ID,BUS_ID,USER_NAME,
        USER_ID,MSG_CONTENT,SEND_TIME,
        NEXT_SEND_TIME,CREATE_BY,CREATE_TIME,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_MSG_SEND_RECORD
        where 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from UOMP_MSG_SEND_RECORD
        where 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompMsgSendRecord" useGeneratedKeys="true">
        insert into UOMP_MSG_SEND_RECORD
        ( ID,MSG_ID,BUS_ID,USER_NAME
        ,USER_ID,MSG_CONTENT,SEND_TIME
        ,NEXT_SEND_TIME,CREATE_BY,CREATE_TIME
        ,UPDATE_BY,UPDATE_TIME,DEL_FLAG
        )
        values (#{id,jdbcType=VARCHAR},#{msgId,jdbcType=VARCHAR},#{busId,jdbcType=VARCHAR},#{userName,jdbcType=VARCHAR}
        ,#{userId,jdbcType=VARCHAR},#{msgContent,jdbcType=VARCHAR},#{sendTime,jdbcType=TIMESTAMP}
        ,#{nextSendTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{delFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompMsgSendRecord" useGeneratedKeys="true">
        insert into UOMP_MSG_SEND_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">ID,</if>
                <if test="msgId != null">MSG_ID,</if>
                <if test="userName != null">USER_NAME,</if>
                <if test="userId != null">USER_ID,</if>
                <if test="msgContent != null">MSG_CONTENT,</if>
                <if test="sendTime != null">SEND_TIME,</if>
                <if test="nextSendTime != null">NEXT_SEND_TIME,</if>
                <if test="createBy != null">CREATE_BY,</if>
                <if test="createTime != null">CREATE_TIME,</if>
                <if test="updateBy != null">UPDATE_BY,</if>
                <if test="updateTime != null">UPDATE_TIME,</if>
                <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null"> #{id,jdbcType=VARCHAR},</if>
                <if test="msgId != null"> #{msgId,jdbcType=VARCHAR},</if>
                <if test="userName != null"> #{userName,jdbcType=VARCHAR},</if>
                <if test="userId != null"> #{userId,jdbcType=VARCHAR},</if>
                <if test="msgContent != null"> #{msgContent,jdbcType=VARCHAR},</if>
                <if test="sendTime != null"> #{sendTime,jdbcType=TIMESTAMP},</if>
                <if test="nextSendTime != null"> #{nextSendTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null"> #{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null"> #{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null"> #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.uomp.entity.UompMsgSendRecord">
        update UOMP_MSG_SEND_RECORD
        <set>
                <if test="msgId != null">
                    MSG_ID = #{msgId,jdbcType=VARCHAR},
                </if>
                <if test="busId != null">
                    BUS_ID = #{busId,jdbcType=VARCHAR},
                </if>
                <if test="userName != null">
                    USER_NAME = #{userName,jdbcType=VARCHAR},
                </if>
                <if test="userId != null">
                    USER_ID = #{userId,jdbcType=VARCHAR},
                </if>
                <if test="msgContent != null">
                    MSG_CONTENT = #{msgContent,jdbcType=VARCHAR},
                </if>
                <if test="sendTime != null">
                    SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
                </if>
                <if test="nextSendTime != null">
                    NEXT_SEND_TIME = #{nextSendTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    CREATE_BY = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
                </if>
        </set>
        where
        ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.gwssi.uomp.entity.UompMsgSendRecord">
        update UOMP_MSG_SEND_RECORD
        set 
            ID =  #{id,jdbcType=VARCHAR},
            MSG_ID =  #{msgId,jdbcType=VARCHAR},
            BUS_ID =  #{busId,jdbcType=VARCHAR},
            USER_NAME =  #{userName,jdbcType=VARCHAR},
            USER_ID =  #{userId,jdbcType=VARCHAR},
            MSG_CONTENT =  #{msgContent,jdbcType=VARCHAR},
            SEND_TIME =  #{sendTime,jdbcType=TIMESTAMP},
            NEXT_SEND_TIME =  #{nextSendTime,jdbcType=TIMESTAMP},
            CREATE_BY =  #{createBy,jdbcType=VARCHAR},
            CREATE_TIME =  #{createTime,jdbcType=TIMESTAMP},
            UPDATE_BY =  #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME =  #{updateTime,jdbcType=TIMESTAMP},
            DEL_FLAG =  #{delFlag,jdbcType=VARCHAR}
        where  
    </update>

    <select id="getMsgSendRecord" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_MSG_SEND_RECORD
        where MSG_ID = #{id}
    </select>
    <select id="getMsgSendRecordLast" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_MSG_SEND_RECORD
        where MSG_ID = #{id} and BUS_ID = #{busid}
        order by create_time desc
        limit 1
    </select>
</mapper>
