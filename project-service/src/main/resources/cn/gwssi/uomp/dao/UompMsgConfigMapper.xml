<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompMsgConfigMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompMsgConfig">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="msgType" column="MSG_TYPE" jdbcType="VARCHAR"/>
            <result property="slaid" column="SLAID" jdbcType="VARCHAR"/>
            <result property="priid" column="PRIID" jdbcType="VARCHAR"/>
            <result property="orderLevel" column="ORDER_LEVEL" jdbcType="VARCHAR"/>
            <result property="notifyType" column="NOTIFY_TYPE" jdbcType="VARCHAR"/>
            <result property="timeLimit" column="TIME_LIMIT" jdbcType="DECIMAL"/>
            <result property="timeUnit" column="TIME_UNIT" jdbcType="VARCHAR"/>
            <result property="notifyLevel" column="NOTIFY_LEVEL" jdbcType="VARCHAR"/>
            <result property="msgName" column="MSG_NAME" jdbcType="VARCHAR"/>
            <result property="msgIcon" column="MSG_ICON" jdbcType="VARCHAR"/>
            <result property="msgDesc" column="MSG_DESC" jdbcType="VARCHAR"/>
            <result property="notifyObject" column="NOTIFY_OBJECT" jdbcType="VARCHAR"/>
            <result property="notifyObjectName" column="NOTIFY_OBJECT_NAME" jdbcType="VARCHAR"/>
            <result property="notifyPerson" column="NOTIFY_PERSON" jdbcType="VARCHAR"/>
            <result property="notifyPersonIds" column="NOTIFY_PERSON_IDS" jdbcType="VARCHAR"/>
            <result property="execFreq" column="EXEC_FREQ" jdbcType="VARCHAR"/>
            <result property="ifRepeat" column="IF_REPEAT" jdbcType="VARCHAR"/>
            <result property="repeatType" column="REPEAT_TYPE" jdbcType="VARCHAR"/>
            <result property="busKey" column="BUS_KEY" jdbcType="VARCHAR"/>
            <result property="condScript" column="COND_SCRIPT" jdbcType="VARCHAR"/>
            <result property="msgCont" column="MSG_CONT" jdbcType="VARCHAR"/>
            <result property="sendType" column="SEND_TYPE" jdbcType="VARCHAR"/>
            <result property="sendTypeName" column="SEND_TYPE_NAME" jdbcType="VARCHAR"/>
            <result property="innerUrl" column="INNER_URL" jdbcType="VARCHAR"/>
            <result property="phoneUrl" column="PHONE_URL" jdbcType="VARCHAR"/>
            <result property="imUrl" column="IM_URL" jdbcType="VARCHAR"/>
            <result property="smsUrl" column="SMS_URL" jdbcType="VARCHAR"/>
            <result property="emailUrl" column="EMAIL_URL" jdbcType="VARCHAR"/>
            <result property="offAccountUrl" column="OFF_ACCOUNT_URL" jdbcType="VARCHAR"/>
            <result property="cxUrl" column="CX_URL" jdbcType="VARCHAR"/>
            <result property="smrUrl" column="SMR_URL" jdbcType="VARCHAR"/>
            <result property="ifSilence" column="IF_SILENCE" jdbcType="VARCHAR"/>
            <result property="silenceType" column="SILENCE_TYPE" jdbcType="VARCHAR"/>
            <result property="silenceDateStart" column="SILENCE_DATE_START" jdbcType="TIMESTAMP"/>
            <result property="silenceDateEnd" column="SILENCE_DATE_END" jdbcType="TIMESTAMP"/>
            <result property="silenceCycle" column="SILENCE_CYCLE" jdbcType="VARCHAR"/>
            <result property="resourceId" column="RESOURCE_ID" jdbcType="VARCHAR"/>
            <result property="msgState" column="MSG_STATE" jdbcType="VARCHAR"/>
            <result property="pubDate" column="PUB_DATE" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="msgId" column="MSG_ID" jdbcType="VARCHAR"/>
            <result property="configName" column="CONFIG_NAME" jdbcType="VARCHAR"/>
            <result property="configMemo" column="CONFIG_MEMO" jdbcType="VARCHAR"/>
            <result property="timeType" column="TIME_TYPE" jdbcType="VARCHAR"/>
            <result property="notifyLxGroupChat" column="NOTIFY_LX_GROUP_CHAT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,MSG_TYPE,SLAID,PRIID,
        ORDER_LEVEL,NOTIFY_TYPE,TIME_LIMIT,
        TIME_UNIT,NOTIFY_LEVEL,MSG_NAME,
        MSG_ICON,MSG_DESC,NOTIFY_OBJECT,
        NOTIFY_OBJECT_NAME,NOTIFY_PERSON,NOTIFY_PERSON_IDS,
        EXEC_FREQ,IF_REPEAT,REPEAT_TYPE,
        BUS_KEY,COND_SCRIPT,MSG_CONT,
        SEND_TYPE,SEND_TYPE_NAME,INNER_URL,
        PHONE_URL,IM_URL,SMS_URL,
        EMAIL_URL,OFF_ACCOUNT_URL,CX_URL,
        SMR_URL,IF_SILENCE,SILENCE_TYPE,
        SILENCE_DATE_START,SILENCE_DATE_END,SILENCE_CYCLE,
        RESOURCE_ID,MSG_STATE,PUB_DATE,
        CREATE_BY,CREATE_TIME,UPDATE_BY,
        UPDATE_TIME,DEL_FLAG,MSG_ID,CONFIG_NAME,CONFIG_MEMO,TIME_TYPE,NOTIFY_LX_GROUP_CHAT
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_MSG_CONFIG
        where  ID = #{id,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from UOMP_MSG_CONFIG
        where  ID = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompMsgConfig" useGeneratedKeys="true">
        insert into UOMP_MSG_CONFIG
        ( ID,MSG_TYPE,SLAID
        ,ORDER_LEVEL,NOTIFY_TYPE,TIME_LIMIT
        ,TIME_UNIT,NOTIFY_LEVEL,MSG_NAME
        ,MSG_ICON,MSG_DESC,NOTIFY_OBJECT
        ,NOTIFY_OBJECT_NAME,NOTIFY_PERSON,NOTIFY_PERSON_IDS
        ,EXEC_FREQ,IF_REPEAT,REPEAT_TYPE
        ,BUS_KEY,COND_SCRIPT,MSG_CONT
        ,SEND_TYPE,SEND_TYPE_NAME,INNER_URL
        ,PHONE_URL,IM_URL,SMS_URL
        ,EMAIL_URL,OFF_ACCOUNT_URL,CX_URL
        ,SMR_URL,IF_SILENCE,SILENCE_TYPE
        ,SILENCE_DATE_START,SILENCE_DATE_END,SILENCE_CYCLE
        ,RESOURCE_ID,MSG_STATE,PUB_DATE
        ,CREATE_BY,CREATE_TIME,UPDATE_BY
        ,UPDATE_TIME,DEL_FLAG)
        values (#{id,jdbcType=VARCHAR},#{msgType,jdbcType=VARCHAR},#{slaid,jdbcType=VARCHAR}
        ,#{orderLevel,jdbcType=VARCHAR},#{notifyType,jdbcType=VARCHAR},#{timeLimit,jdbcType=DECIMAL}
        ,#{timeUnit,jdbcType=VARCHAR},#{notifyLevel,jdbcType=VARCHAR},#{msgName,jdbcType=VARCHAR}
        ,#{msgIcon,jdbcType=VARCHAR},#{msgDesc,jdbcType=VARCHAR},#{notifyObject,jdbcType=VARCHAR}
        ,#{notifyObjectName,jdbcType=VARCHAR},#{notifyPerson,jdbcType=VARCHAR},#{notifyPersonIds,jdbcType=VARCHAR}
        ,#{execFreq,jdbcType=VARCHAR},#{ifRepeat,jdbcType=VARCHAR},#{repeatType,jdbcType=VARCHAR}
        ,#{busKey,jdbcType=VARCHAR},#{condScript,jdbcType=VARCHAR},#{msgCont,jdbcType=VARCHAR}
        ,#{sendType,jdbcType=VARCHAR},#{sendTypeName,jdbcType=VARCHAR},#{innerUrl,jdbcType=VARCHAR}
        ,#{phoneUrl,jdbcType=VARCHAR},#{imUrl,jdbcType=VARCHAR},#{smsUrl,jdbcType=VARCHAR}
        ,#{emailUrl,jdbcType=VARCHAR},#{offAccountUrl,jdbcType=VARCHAR},#{cxUrl,jdbcType=VARCHAR}
        ,#{smrUrl,jdbcType=VARCHAR},#{ifSilence,jdbcType=VARCHAR},#{silenceType,jdbcType=VARCHAR}
        ,#{silenceDateStart,jdbcType=TIMESTAMP},#{silenceDateEnd,jdbcType=TIMESTAMP},#{silenceCycle,jdbcType=VARCHAR}
        ,#{resourceId,jdbcType=VARCHAR},#{msgState,jdbcType=VARCHAR},#{pubDate,jdbcType=TIMESTAMP}
        ,#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{delFlag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompMsgConfig" useGeneratedKeys="true">
        insert into UOMP_MSG_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">ID,</if>
                <if test="msgType != null">MSG_TYPE,</if>
                <if test="slaid != null">SLAID,</if>
                <if test="orderLevel != null">ORDER_LEVEL,</if>
                <if test="notifyType != null">NOTIFY_TYPE,</if>
                <if test="timeLimit != null">TIME_LIMIT,</if>
                <if test="timeUnit != null">TIME_UNIT,</if>
                <if test="notifyLevel != null">NOTIFY_LEVEL,</if>
                <if test="msgName != null">MSG_NAME,</if>
                <if test="msgIcon != null">MSG_ICON,</if>
                <if test="msgDesc != null">MSG_DESC,</if>
                <if test="notifyObject != null">NOTIFY_OBJECT,</if>
                <if test="notifyObjectName != null">NOTIFY_OBJECT_NAME,</if>
                <if test="notifyPerson != null">NOTIFY_PERSON,</if>
                <if test="notifyPersonIds != null">NOTIFY_PERSON_IDS,</if>
                <if test="execFreq != null">EXEC_FREQ,</if>
                <if test="ifRepeat != null">IF_REPEAT,</if>
                <if test="repeatType != null">REPEAT_TYPE,</if>
                <if test="busKey != null">BUS_KEY,</if>
                <if test="condScript != null">COND_SCRIPT,</if>
                <if test="msgCont != null">MSG_CONT,</if>
                <if test="sendType != null">SEND_TYPE,</if>
                <if test="sendTypeName != null">SEND_TYPE_NAME,</if>
                <if test="innerUrl != null">INNER_URL,</if>
                <if test="phoneUrl != null">PHONE_URL,</if>
                <if test="imUrl != null">IM_URL,</if>
                <if test="smsUrl != null">SMS_URL,</if>
                <if test="emailUrl != null">EMAIL_URL,</if>
                <if test="offAccountUrl != null">OFF_ACCOUNT_URL,</if>
                <if test="cxUrl != null">CX_URL,</if>
                <if test="smrUrl != null">SMR_URL,</if>
                <if test="ifSilence != null">IF_SILENCE,</if>
                <if test="silenceType != null">SILENCE_TYPE,</if>
                <if test="silenceDateStart != null">SILENCE_DATE_START,</if>
                <if test="silenceDateEnd != null">SILENCE_DATE_END,</if>
                <if test="silenceCycle != null">SILENCE_CYCLE,</if>
                <if test="resourceId != null">RESOURCE_ID,</if>
                <if test="msgState != null">MSG_STATE,</if>
                <if test="pubDate != null">PUB_DATE,</if>
                <if test="createBy != null">CREATE_BY,</if>
                <if test="createTime != null">CREATE_TIME,</if>
                <if test="updateBy != null">UPDATE_BY,</if>
                <if test="updateTime != null">UPDATE_TIME,</if>
                <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null"> #{id,jdbcType=VARCHAR},</if>
                <if test="msgType != null"> #{msgType,jdbcType=VARCHAR},</if>
                <if test="slaid != null"> #{slaid,jdbcType=VARCHAR},</if>
                <if test="orderLevel != null"> #{orderLevel,jdbcType=VARCHAR},</if>
                <if test="notifyType != null"> #{notifyType,jdbcType=VARCHAR},</if>
                <if test="timeLimit != null"> #{timeLimit,jdbcType=DECIMAL},</if>
                <if test="timeUnit != null"> #{timeUnit,jdbcType=VARCHAR},</if>
                <if test="notifyLevel != null"> #{notifyLevel,jdbcType=VARCHAR},</if>
                <if test="msgName != null"> #{msgName,jdbcType=VARCHAR},</if>
                <if test="msgIcon != null"> #{msgIcon,jdbcType=VARCHAR},</if>
                <if test="msgDesc != null"> #{msgDesc,jdbcType=VARCHAR},</if>
                <if test="notifyObject != null"> #{notifyObject,jdbcType=VARCHAR},</if>
                <if test="notifyObjectName != null"> #{notifyObjectName,jdbcType=VARCHAR},</if>
                <if test="notifyPerson != null"> #{notifyPerson,jdbcType=VARCHAR},</if>
                <if test="notifyPersonIds != null"> #{notifyPersonIds,jdbcType=VARCHAR},</if>
                <if test="execFreq != null"> #{execFreq,jdbcType=VARCHAR},</if>
                <if test="ifRepeat != null"> #{ifRepeat,jdbcType=VARCHAR},</if>
                <if test="repeatType != null"> #{repeatType,jdbcType=VARCHAR},</if>
                <if test="busKey != null"> #{busKey,jdbcType=VARCHAR},</if>
                <if test="condScript != null"> #{condScript,jdbcType=VARCHAR},</if>
                <if test="msgCont != null"> #{msgCont,jdbcType=VARCHAR},</if>
                <if test="sendType != null"> #{sendType,jdbcType=VARCHAR},</if>
                <if test="sendTypeName != null"> #{sendTypeName,jdbcType=VARCHAR},</if>
                <if test="innerUrl != null"> #{innerUrl,jdbcType=VARCHAR},</if>
                <if test="phoneUrl != null"> #{phoneUrl,jdbcType=VARCHAR},</if>
                <if test="imUrl != null"> #{imUrl,jdbcType=VARCHAR},</if>
                <if test="smsUrl != null"> #{smsUrl,jdbcType=VARCHAR},</if>
                <if test="emailUrl != null"> #{emailUrl,jdbcType=VARCHAR},</if>
                <if test="offAccountUrl != null"> #{offAccountUrl,jdbcType=VARCHAR},</if>
                <if test="cxUrl != null"> #{cxUrl,jdbcType=VARCHAR},</if>
                <if test="smrUrl != null"> #{smrUrl,jdbcType=VARCHAR},</if>
                <if test="ifSilence != null"> #{ifSilence,jdbcType=VARCHAR},</if>
                <if test="silenceType != null"> #{silenceType,jdbcType=VARCHAR},</if>
                <if test="silenceDateStart != null"> #{silenceDateStart,jdbcType=TIMESTAMP},</if>
                <if test="silenceDateEnd != null"> #{silenceDateEnd,jdbcType=TIMESTAMP},</if>
                <if test="silenceCycle != null"> #{silenceCycle,jdbcType=VARCHAR},</if>
                <if test="resourceId != null"> #{resourceId,jdbcType=VARCHAR},</if>
                <if test="msgState != null"> #{msgState,jdbcType=VARCHAR},</if>
                <if test="pubDate != null"> #{pubDate,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null"> #{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null"> #{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null"> #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.uomp.entity.UompMsgConfig">
        update UOMP_MSG_CONFIG
        <set>
                <if test="msgType != null">
                    MSG_TYPE = #{msgType,jdbcType=VARCHAR},
                </if>
                <if test="slaid != null">
                    SLAID = #{slaid,jdbcType=VARCHAR},
                </if>
                <if test="orderLevel != null">
                    ORDER_LEVEL = #{orderLevel,jdbcType=VARCHAR},
                </if>
                <if test="notifyType != null">
                    NOTIFY_TYPE = #{notifyType,jdbcType=VARCHAR},
                </if>
                <if test="timeLimit != null">
                    TIME_LIMIT = #{timeLimit,jdbcType=DECIMAL},
                </if>
                <if test="timeUnit != null">
                    TIME_UNIT = #{timeUnit,jdbcType=VARCHAR},
                </if>
                <if test="notifyLevel != null">
                    NOTIFY_LEVEL = #{notifyLevel,jdbcType=VARCHAR},
                </if>
                <if test="msgName != null">
                    MSG_NAME = #{msgName,jdbcType=VARCHAR},
                </if>
                <if test="msgIcon != null">
                    MSG_ICON = #{msgIcon,jdbcType=VARCHAR},
                </if>
                <if test="msgDesc != null">
                    MSG_DESC = #{msgDesc,jdbcType=VARCHAR},
                </if>
                <if test="notifyObject != null">
                    NOTIFY_OBJECT = #{notifyObject,jdbcType=VARCHAR},
                </if>
                <if test="notifyObjectName != null">
                    NOTIFY_OBJECT_NAME = #{notifyObjectName,jdbcType=VARCHAR},
                </if>
                <if test="notifyPerson != null">
                    NOTIFY_PERSON = #{notifyPerson,jdbcType=VARCHAR},
                </if>
                <if test="notifyPersonIds != null">
                    NOTIFY_PERSON_IDS = #{notifyPersonIds,jdbcType=VARCHAR},
                </if>
                <if test="execFreq != null">
                    EXEC_FREQ = #{execFreq,jdbcType=VARCHAR},
                </if>
                <if test="ifRepeat != null">
                    IF_REPEAT = #{ifRepeat,jdbcType=VARCHAR},
                </if>
                <if test="repeatType != null">
                    REPEAT_TYPE = #{repeatType,jdbcType=VARCHAR},
                </if>
                <if test="busKey != null">
                    BUS_KEY = #{busKey,jdbcType=VARCHAR},
                </if>
                <if test="condScript != null">
                    COND_SCRIPT = #{condScript,jdbcType=VARCHAR},
                </if>
                <if test="msgCont != null">
                    MSG_CONT = #{msgCont,jdbcType=VARCHAR},
                </if>
                <if test="sendType != null">
                    SEND_TYPE = #{sendType,jdbcType=VARCHAR},
                </if>
                <if test="sendTypeName != null">
                    SEND_TYPE_NAME = #{sendTypeName,jdbcType=VARCHAR},
                </if>
                <if test="innerUrl != null">
                    INNER_URL = #{innerUrl,jdbcType=VARCHAR},
                </if>
                <if test="phoneUrl != null">
                    PHONE_URL = #{phoneUrl,jdbcType=VARCHAR},
                </if>
                <if test="imUrl != null">
                    IM_URL = #{imUrl,jdbcType=VARCHAR},
                </if>
                <if test="smsUrl != null">
                    SMS_URL = #{smsUrl,jdbcType=VARCHAR},
                </if>
                <if test="emailUrl != null">
                    EMAIL_URL = #{emailUrl,jdbcType=VARCHAR},
                </if>
                <if test="offAccountUrl != null">
                    OFF_ACCOUNT_URL = #{offAccountUrl,jdbcType=VARCHAR},
                </if>
                <if test="cxUrl != null">
                    CX_URL = #{cxUrl,jdbcType=VARCHAR},
                </if>
                <if test="smrUrl != null">
                    SMR_URL = #{smrUrl,jdbcType=VARCHAR},
                </if>
                <if test="ifSilence != null">
                    IF_SILENCE = #{ifSilence,jdbcType=VARCHAR},
                </if>
                <if test="silenceType != null">
                    SILENCE_TYPE = #{silenceType,jdbcType=VARCHAR},
                </if>
                <if test="silenceDateStart != null">
                    SILENCE_DATE_START = #{silenceDateStart,jdbcType=TIMESTAMP},
                </if>
                <if test="silenceDateEnd != null">
                    SILENCE_DATE_END = #{silenceDateEnd,jdbcType=TIMESTAMP},
                </if>
                <if test="silenceCycle != null">
                    SILENCE_CYCLE = #{silenceCycle,jdbcType=VARCHAR},
                </if>
                <if test="resourceId != null">
                    RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
                </if>
                <if test="msgState != null">
                    MSG_STATE = #{msgState,jdbcType=VARCHAR},
                </if>
                <if test="pubDate != null">
                    PUB_DATE = #{pubDate,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    CREATE_BY = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
                </if>
        </set>
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.gwssi.uomp.entity.UompMsgConfig">
        update UOMP_MSG_CONFIG
        set 
            MSG_TYPE =  #{msgType,jdbcType=VARCHAR},
            SLAID =  #{slaid,jdbcType=VARCHAR},
            ORDER_LEVEL =  #{orderLevel,jdbcType=VARCHAR},
            NOTIFY_TYPE =  #{notifyType,jdbcType=VARCHAR},
            TIME_LIMIT =  #{timeLimit,jdbcType=DECIMAL},
            TIME_UNIT =  #{timeUnit,jdbcType=VARCHAR},
            NOTIFY_LEVEL =  #{notifyLevel,jdbcType=VARCHAR},
            MSG_NAME =  #{msgName,jdbcType=VARCHAR},
            MSG_ICON =  #{msgIcon,jdbcType=VARCHAR},
            MSG_DESC =  #{msgDesc,jdbcType=VARCHAR},
            NOTIFY_OBJECT =  #{notifyObject,jdbcType=VARCHAR},
            NOTIFY_OBJECT_NAME =  #{notifyObjectName,jdbcType=VARCHAR},
            NOTIFY_PERSON =  #{notifyPerson,jdbcType=VARCHAR},
            NOTIFY_PERSON_IDS =  #{notifyPersonIds,jdbcType=VARCHAR},
            EXEC_FREQ =  #{execFreq,jdbcType=VARCHAR},
            IF_REPEAT =  #{ifRepeat,jdbcType=VARCHAR},
            REPEAT_TYPE =  #{repeatType,jdbcType=VARCHAR},
            BUS_KEY =  #{busKey,jdbcType=VARCHAR},
            COND_SCRIPT =  #{condScript,jdbcType=VARCHAR},
            MSG_CONT =  #{msgCont,jdbcType=VARCHAR},
            SEND_TYPE =  #{sendType,jdbcType=VARCHAR},
            SEND_TYPE_NAME =  #{sendTypeName,jdbcType=VARCHAR},
            INNER_URL =  #{innerUrl,jdbcType=VARCHAR},
            PHONE_URL =  #{phoneUrl,jdbcType=VARCHAR},
            IM_URL =  #{imUrl,jdbcType=VARCHAR},
            SMS_URL =  #{smsUrl,jdbcType=VARCHAR},
            EMAIL_URL =  #{emailUrl,jdbcType=VARCHAR},
            OFF_ACCOUNT_URL =  #{offAccountUrl,jdbcType=VARCHAR},
            CX_URL =  #{cxUrl,jdbcType=VARCHAR},
            SMR_URL =  #{smrUrl,jdbcType=VARCHAR},
            IF_SILENCE =  #{ifSilence,jdbcType=VARCHAR},
            SILENCE_TYPE =  #{silenceType,jdbcType=VARCHAR},
            SILENCE_DATE_START =  #{silenceDateStart,jdbcType=TIMESTAMP},
            SILENCE_DATE_END =  #{silenceDateEnd,jdbcType=TIMESTAMP},
            SILENCE_CYCLE =  #{silenceCycle,jdbcType=VARCHAR},
            RESOURCE_ID =  #{resourceId,jdbcType=VARCHAR},
            MSG_STATE =  #{msgState,jdbcType=VARCHAR},
            PUB_DATE =  #{pubDate,jdbcType=TIMESTAMP},
            CREATE_BY =  #{createBy,jdbcType=VARCHAR},
            CREATE_TIME =  #{createTime,jdbcType=TIMESTAMP},
            UPDATE_BY =  #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME =  #{updateTime,jdbcType=TIMESTAMP},
            DEL_FLAG =  #{delFlag,jdbcType=VARCHAR}
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>
    <select id="getPauseLimit" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nvl(sum(limit_sum),0) as limit from uomp_timelimit_pause where inst_id = #{instId} and limit_state = '1'
    </select>
    <select id="getTeamUser" parameterType="java.lang.String" resultType="java.lang.String">
        select wm_concat(user_id_) as ids  from ORG_RELATION where group_id_=#{teamid}
    </select>
    <select id="getLeaders" parameterType="java.lang.String" resultType="java.lang.String">
        select team_leader_id from uomp_team where id = #{teamid}
    </select>
    <select id="getDeviceUsers" parameterType="java.lang.String" resultType="java.lang.String">
        select wm_concat(id) as ids from (
        select RIGHTS_IDENTITY_ as id from SYS_AUTHORIZATION where RIGHTS_TARGET_ = #{busid} and RIGHTS_TYPE_ = 'user'
        union
        select distinct r.USER_ID_ as id from ORG_RELATION r,SYS_AUTHORIZATION t where t.RIGHTS_IDENTITY_ = r.GROUP_ID_
        and t.RIGHTS_TYPE_ in ('team','role','post') and t.RIGHTS_TARGET_ = #{busid}
        )
    </select>
    <select id="getDeviceGroupLeaders" parameterType="java.lang.String" resultType="java.lang.String">
        select wm_concat(t.GROUP_LEADER_IDS) as ids from UOMP_TEAM t,SYS_AUTHORIZATION s where s.RIGHTS_TARGET_ = #{busid} and s.RIGHTS_TYPE_ = 'team'
        and s.RIGHTS_IDENTITY_ = t.ID
    </select>
    <select id="getUserList" parameterType="java.util.List" resultType="cn.gwssi.ecloudframework.org.core.model.User">
        select id_ as id,id_ as userId,FULLNAME_ as fullname,EMAIL_ as email from ORG_USER where id_ in (
        <foreach collection="ids" item="it" separator=",">
            #{it}
        </foreach>
        )
    </select>
    <select id="getUserById" parameterType="java.lang.String" resultType="cn.gwssi.ecloudframework.org.core.model.User">
        select id_ as id,id_ as userId,FULLNAME_ as fullname,EMAIL_ as email from ORG_USER where id_ = #{id}
    </select>
    <select id="getValidConfigList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_MSG_CONFIG
        where  MSG_STATE = '1'
        and DEL_FLAG = '0'
        and MSG_CONFIG_TYPE = '1'
        and EXEC_FREQ = #{execFreq}
    </select>
</mapper>
