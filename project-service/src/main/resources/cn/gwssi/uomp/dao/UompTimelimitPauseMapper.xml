<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompTimelimitPauseMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompTimelimitPause">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="requestId" column="REQUEST_ID" jdbcType="VARCHAR"/>
            <result property="limitType" column="LIMIT_TYPE" jdbcType="VARCHAR"/>
            <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
            <result property="limitSum" column="LIMIT_SUM" jdbcType="DECIMAL"/>
            <result property="limitUnit" column="LIMIT_UNIT" jdbcType="VARCHAR"/>
            <result property="limitState" column="LIMIT_STATE" jdbcType="VARCHAR"/>
            <result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,REQUEST_ID,LIMIT_TYPE,
        START_TIME,END_TIME,LIMIT_SUM,
        LIMIT_UNIT,LIMIT_STATE,INST_ID,
        CREATE_BY,CREATE_TIME,CREATE_ORG_ID,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_TIMELIMIT_PAUSE
        where  ID = #{id,jdbcType=VARCHAR} 
    </select>

    <select id="getByLimitPauseInstid" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_TIMELIMIT_PAUSE
        where  INST_ID = #{instId,jdbcType=VARCHAR} and LIMIT_STATE = '0'
    </select>

    <select id="getPauseSum" parameterType="java.lang.String" resultType="java.lang.Integer">
        select nvl(sum(limit_sum),0) as limit from uomp_timelimit_pause where inst_id = #{instanceId} and limit_state = '1'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from UOMP_TIMELIMIT_PAUSE
        where  ID = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="create" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompTimelimitPause" useGeneratedKeys="true">
        insert into UOMP_TIMELIMIT_PAUSE
        ( ID,REQUEST_ID,LIMIT_TYPE
        ,START_TIME,END_TIME,LIMIT_SUM
        ,LIMIT_UNIT,LIMIT_STATE,INST_ID
        ,CREATE_BY,CREATE_TIME,CREATE_ORG_ID
        ,UPDATE_BY,UPDATE_TIME,DEL_FLAG
        )
        values (#{id,jdbcType=VARCHAR},#{requestId,jdbcType=VARCHAR},#{limitType,jdbcType=VARCHAR}
        ,#{startTime,jdbcType=TIMESTAMP},#{endTime,jdbcType=TIMESTAMP},#{limitSum,jdbcType=DECIMAL}
        ,#{limitUnit,jdbcType=VARCHAR},#{limitState,jdbcType=VARCHAR},#{instId,jdbcType=VARCHAR}
        ,#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{createOrgId,jdbcType=VARCHAR}
        ,#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{delFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.gwssi.uomp.entity.UompTimelimitPause" useGeneratedKeys="true">
        insert into UOMP_TIMELIMIT_PAUSE
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">ID,</if>
                <if test="requestId != null">REQUEST_ID,</if>
                <if test="limitType != null">LIMIT_TYPE,</if>
                <if test="startTime != null">START_TIME,</if>
                <if test="endTime != null">END_TIME,</if>
                <if test="limitSum != null">LIMIT_SUM,</if>
                <if test="limitUnit != null">LIMIT_UNIT,</if>
                <if test="limitState != null">LIMIT_STATE,</if>
                <if test="instId != null">INST_ID,</if>
                <if test="createBy != null">CREATE_BY,</if>
                <if test="createTime != null">CREATE_TIME,</if>
                <if test="createOrgId != null">CREATE_ORG_ID,</if>
                <if test="updateBy != null">UPDATE_BY,</if>
                <if test="updateTime != null">UPDATE_TIME,</if>
                <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null"> #{id,jdbcType=VARCHAR},</if>
                <if test="requestId != null"> #{requestId,jdbcType=VARCHAR},</if>
                <if test="limitType != null"> #{limitType,jdbcType=VARCHAR},</if>
                <if test="startTime != null"> #{startTime,jdbcType=TIMESTAMP},</if>
                <if test="endTime != null"> #{endTime,jdbcType=TIMESTAMP},</if>
                <if test="limitSum != null"> #{limitSum,jdbcType=DECIMAL},</if>
                <if test="limitUnit != null"> #{limitUnit,jdbcType=VARCHAR},</if>
                <if test="limitState != null"> #{limitState,jdbcType=VARCHAR},</if>
                <if test="instId != null"> #{instId,jdbcType=VARCHAR},</if>
                <if test="createBy != null"> #{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP},</if>
                <if test="createOrgId != null"> #{createOrgId,jdbcType=VARCHAR},</if>
                <if test="updateBy != null"> #{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null"> #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.uomp.entity.UompTimelimitPause">
        update UOMP_TIMELIMIT_PAUSE
        <set>
                <if test="requestId != null">
                    REQUEST_ID = #{requestId,jdbcType=VARCHAR},
                </if>
                <if test="limitType != null">
                    LIMIT_TYPE = #{limitType,jdbcType=VARCHAR},
                </if>
                <if test="startTime != null">
                    START_TIME = #{startTime,jdbcType=TIMESTAMP},
                </if>
                <if test="endTime != null">
                    END_TIME = #{endTime,jdbcType=TIMESTAMP},
                </if>
                <if test="limitSum != null">
                    LIMIT_SUM = #{limitSum,jdbcType=DECIMAL},
                </if>
                <if test="limitUnit != null">
                    LIMIT_UNIT = #{limitUnit,jdbcType=VARCHAR},
                </if>
                <if test="limitState != null">
                    LIMIT_STATE = #{limitState,jdbcType=VARCHAR},
                </if>
                <if test="instId != null">
                    INST_ID = #{instId,jdbcType=VARCHAR},
                </if>
                <if test="createBy != null">
                    CREATE_BY = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createOrgId != null">
                    CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
                </if>
                <if test="updateBy != null">
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
                </if>
        </set>
        where   ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.gwssi.uomp.entity.UompTimelimitPause">
        update UOMP_TIMELIMIT_PAUSE
        set 
            REQUEST_ID =  #{requestId,jdbcType=VARCHAR},
            LIMIT_TYPE =  #{limitType,jdbcType=VARCHAR},
            START_TIME =  #{startTime,jdbcType=TIMESTAMP},
            END_TIME =  #{endTime,jdbcType=TIMESTAMP},
            LIMIT_SUM =  #{limitSum,jdbcType=DECIMAL},
            LIMIT_UNIT =  #{limitUnit,jdbcType=VARCHAR},
            LIMIT_STATE =  #{limitState,jdbcType=VARCHAR},
            INST_ID =  #{instId,jdbcType=VARCHAR},
            UPDATE_BY =  #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME =  sysdate
        where   ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateLimitPauseByInstid" parameterType="cn.gwssi.uomp.entity.UompTimelimitPause">
        update UOMP_TIMELIMIT_PAUSE
        <set>
            <if test="startTime != null">
                START_TIME = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="limitSum != null">
                LIMIT_SUM = #{limitSum,jdbcType=DECIMAL},
            </if>
            LIMIT_STATE = '1',
            UPDATE_TIME = sysdate,
        </set>
        where   inst_id = #{instId,jdbcType=VARCHAR} and LIMIT_STATE = '0'
    </update>
</mapper>
