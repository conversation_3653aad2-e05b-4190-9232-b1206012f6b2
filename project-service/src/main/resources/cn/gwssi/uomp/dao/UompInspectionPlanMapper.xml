<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompInspectionPlanMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompInspectionPlan">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="jobId" column="JOB_ID" jdbcType="VARCHAR"/>
            <result property="planName" column="PLAN_NAME" jdbcType="VARCHAR"/>
            <result property="templateId" column="TEMPLATE_ID" jdbcType="VARCHAR"/>
            <result property="templateName" column="TEMPLATE_NAME" jdbcType="VARCHAR"/>
            <result property="templateJson" column="TEMPLATE_JSON" jdbcType="VARCHAR"/>
            <result property="templateVersion" column="TEMPLATE_VERSION" jdbcType="VARCHAR"/>
            <result property="execType" column="EXEC_TYPE" jdbcType="VARCHAR"/>
            <result property="inspType" column="INSP_TYPE" jdbcType="VARCHAR"/>
            <result property="execWeek" column="EXEC_WEEK" jdbcType="VARCHAR"/>
            <result property="execMonthType" column="EXEC_MONTH_TYPE" jdbcType="VARCHAR"/>
            <result property="execRemark" column="EXEC_REMARK" jdbcType="VARCHAR"/>
            <result property="cronCode" column="CRON_CODE" jdbcType="VARCHAR"/>
            <result property="planState" column="PLAN_STATE" jdbcType="VARCHAR"/>
            <result property="receiverId" column="RECEIVER_ID" jdbcType="VARCHAR"/>
            <result property="receiverName" column="RECEIVER_NAME" jdbcType="VARCHAR"/>
            <result property="receiver" column="RECEIVER" jdbcType="VARCHAR"/>
            <result property="planStartTime" column="PLAN_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="planEndTime" column="PLAN_END_TIME" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="ifSkipHoliday" column="IF_SKIP_HOLIDAY" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="execTimeMap" type="cn.gwssi.uomp.entity.UompInspectExecTime">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="planId" column="PLAN_ID" jdbcType="VARCHAR"/>
        <result property="inspType" column="INSP_TYPE" jdbcType="VARCHAR"/>
        <result property="execDay" column="EXEC_DAY" jdbcType="VARCHAR"/>
        <result property="execHour" column="EXEC_HOUR" jdbcType="VARCHAR"/>
        <result property="execMinute" column="EXEC_MINUTE" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,JOB_ID,PLAN_NAME,
        TEMPLATE_ID,TEMPLATE_NAME,TEMPLATE_JSON,
        TEMPLATE_VERSION,EXEC_TYPE,INSP_TYPE,
        EXEC_WEEK,EXEC_MONTH_TYPE,EXEC_REMARK,
        CRON_CODE,PLAN_STATE,RECEIVER_ID,
        RECEIVER_NAME,RECEIVER,PLAN_START_TIME,
        PLAN_END_TIME,CREATE_BY,CREATE_TIME,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG,IF_SKIP_HOLIDAY
    </sql>
    <select id="getUsedPlanList" resultMap="BaseResultMap">
        select ID, JOB_ID, PLAN_NAME, TEMPLATE_ID, TEMPLATE_NAME, TEMPLATE_JSON, TEMPLATE_VERSION, EXEC_TYPE, INSP_TYPE,
        EXEC_WEEK, EXEC_MONTH_TYPE, EXEC_REMARK, CRON_CODE, PLAN_STATE, RECEIVER_ID, RECEIVER_NAME, RECEIVER, PLAN_START_TIME,
         PLAN_END_TIME, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG,IF_SKIP_HOLIDAY
         from UOMP_INSPECTION_PLAN
         where DEL_FLAG = '0' and PLAN_STATE = '1' and
          to_date(to_char(sysdate,'yyyy-MM-dd'),'yyyy-MM-dd') between PLAN_START_TIME and PLAN_END_TIME
    </select>
    <select id="getExecTimeList"  parameterType="java.lang.String" resultMap="execTimeMap">
        select ID, PLAN_ID, INSP_TYPE, nvl(EXEC_DAY,0) as EXEC_DAY, EXEC_HOUR, nvl(EXEC_MINUTE,0) as EXEC_MINUTE,CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, DEL_FLAG
        from UOMP_INSPECTION_EXEC_TIME
        where PLAN_ID = #{planId} and del_flag = '0'
    </select>
</mapper>
