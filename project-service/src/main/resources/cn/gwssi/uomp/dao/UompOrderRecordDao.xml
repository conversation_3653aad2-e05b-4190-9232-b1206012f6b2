<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompOrderRecordDao">

	<resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompOrderRecord">
		<id property="id" column="ID" jdbcType="VARCHAR"/>
		<result property="requestType" column="REQUEST_TYPE" jdbcType="VARCHAR"/>
		<result property="requestId" column="REQUEST_ID" jdbcType="VARCHAR"/>
		<result property="taskId" column="TASK_ID" jdbcType="VARCHAR"/>
		<result property="taskName" column="TASK_NAME" jdbcType="VARCHAR"/>
		<result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
		<result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
		<result property="handlerType" column="HANDLER_TYPE" jdbcType="VARCHAR"/>
		<result property="handlerOrg" column="HANDLER_ORG" jdbcType="VARCHAR"/>
		<result property="handlerOrgId" column="HANDLER_ORG_ID" jdbcType="VARCHAR"/>
		<result property="handlerGroup" column="HANDLER_GROUP" jdbcType="VARCHAR"/>
		<result property="handlerGroupId" column="HANDLER_GROUP_ID" jdbcType="VARCHAR"/>
		<result property="handler" column="HANDLER" jdbcType="VARCHAR"/>
		<result property="handlerNo" column="HANDLER_NO" jdbcType="VARCHAR"/>
		<result property="handlerId" column="HANDLER_ID" jdbcType="VARCHAR"/>
		<result property="handleCode" column="HANDLE_CODE" jdbcType="VARCHAR"/>
		<result property="handleName" column="HANDLE_NAME" jdbcType="VARCHAR"/>
		<result property="handleOpin" column="HANDLE_OPIN" jdbcType="VARCHAR"/>
		<result property="duration" column="DURATION" jdbcType="DECIMAL"/>
		<result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
		<result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
		<result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
		<result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
		<result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
		<result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
		<result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
		<result property="pauseType" column="PAUSE_TYPE" jdbcType="VARCHAR"/>
	</resultMap>

	<sql id="Base_Column_List">
        ID,REQUEST_TYPE,REQUEST_ID,
        TASK_ID,TASK_NAME,START_TIME,END_TIME,HANDLER_TYPE,
        HANDLER_ORG,HANDLER_ORG_ID,HANDLER_GROUP,
        HANDLER_GROUP_ID,HANDLER,HANDLER_NO,
        HANDLER_ID,HANDLE_CODE,HANDLE_NAME,
        HANDLE_OPIN,DURATION,INST_ID,
        CREATE_BY,CREATE_TIME,CREATE_ORG_ID,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG,PAUSE_TYPE
    </sql>

	<insert id="create" parameterType="cn.gwssi.uomp.entity.UompOrderRecord">
		INSERT INTO UOMP_ORDER_RECORD(
			ID,REQUEST_TYPE,REQUEST_ID,START_TIME,END_TIME,HANDLER_TYPE,
			HANDLER_ORG,HANDLER_ORG_ID,HANDLER_GROUP,HANDLER_GROUP_ID,HANDLER,
			HANDLER_NO,HANDLER_ID,HANDLE_CODE,HANDLE_NAME,HANDLE_OPIN,
			DURATION,INST_ID,CREATE_BY,CREATE_TIME,CREATE_ORG_ID,
			UPDATE_BY,UPDATE_TIME,DEL_FLAG,TASK_ID,TASK_NAME,PAUSE_TYPE
		)VALUES(
		     #{id,jdbcType=VARCHAR}, #{requestType,jdbcType=VARCHAR}, #{requestId,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},#{handlerType,jdbcType=VARCHAR},
			 #{handlerOrg,jdbcType=VARCHAR}, #{handlerOrgId,jdbcType=VARCHAR}, #{handlerGroup,jdbcType=VARCHAR}, #{handlerGroupId,jdbcType=VARCHAR}, #{handler,jdbcType=VARCHAR},
			 #{handlerNo,jdbcType=VARCHAR}, #{handlerId,jdbcType=VARCHAR}, #{handleCode,jdbcType=VARCHAR}, #{handleName,jdbcType=VARCHAR}, #{handleOpin,jdbcType=VARCHAR},
			 #{duration,jdbcType=NUMERIC}, #{instId,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOrgId,jdbcType=VARCHAR},
			 #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR},
			 #{pauseType,jdbcType=VARCHAR}
			)
	</insert>

	<select id="get"  parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List"></include> FROM UOMP_ORDER_RECORD
		WHERE
			id =#{id}
	</select>

	<select id="getRecordByTaskId"  parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List"></include>  FROM UOMP_ORDER_RECORD
		WHERE
			TASK_ID = #{taskId}
			limit 1
	</select>

	<select id="query" parameterType="java.util.Map" resultType="cn.gwssi.uomp.entity.UompOrderRecord">
		SELECT * FROM UOMP_ORDER_RECORD
		<where>
			<if test="whereSql!=null">
				${whereSql}
			</if>
		</where>
		<if test="orderBySql!=null">
			ORDER BY ${orderBySql}
		</if>
		<if test="orderBySql==null">
			ORDER BY id DESC
		</if>
	</select>

	<update id="update" parameterType="cn.gwssi.uomp.entity.UompOrderRecord">
		UPDATE UOMP_ORDER_RECORD
		SET
			REQUEST_TYPE = #{requestType,jdbcType=VARCHAR},
		    REQUEST_ID = #{requestId,jdbcType=VARCHAR},
		    TASK_ID = #{taskId,jdbcType=VARCHAR},
		    TASK_NAME = #{taskName,jdbcType=VARCHAR},
		    START_TIME = #{startTime,jdbcType=TIMESTAMP},
		    END_TIME = #{endTime,jdbcType=TIMESTAMP},
		    HANDLER_TYPE = #{handlerType,jdbcType=VARCHAR},
		    HANDLER_ORG = #{handlerOrg,jdbcType=VARCHAR},
		    HANDLER_ORG_ID = #{handlerOrgId,jdbcType=VARCHAR},
		    HANDLER_GROUP = #{handlerGroup,jdbcType=VARCHAR},
		    HANDLER_GROUP_ID = #{handlerGroupId,jdbcType=VARCHAR},
		    HANDLER = #{handler,jdbcType=VARCHAR},
		    HANDLER_NO = #{handlerNo,jdbcType=VARCHAR},
		    HANDLER_ID = #{handlerId,jdbcType=VARCHAR},
		    HANDLE_CODE = #{handleCode,jdbcType=VARCHAR},
		    HANDLE_NAME = #{handleName,jdbcType=VARCHAR},
		    HANDLE_OPIN = #{handleOpin,jdbcType=VARCHAR},
		    DURATION = #{duration,jdbcType=NUMERIC},
		    INST_ID = #{instId,jdbcType=VARCHAR},
		    CREATE_BY = #{createBy,jdbcType=VARCHAR},
		    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
		    CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
		    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
		    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
		    PAUSE_TYPE = #{pauseType,jdbcType=TIMESTAMP},
		    DEL_FLAG = #{delFlag,jdbcType=VARCHAR}
		WHERE
			ID = #{id,jdbcType=VARCHAR}
	</update>

	<delete id="remove" parameterType="java.lang.String">
		DELETE FROM UOMP_ORDER_RECORD
		WHERE
			ID=#{id}
	</delete>

	<update id="updateOrderRecordByTaskId" parameterType="cn.gwssi.uomp.entity.UompOrderRecord">
		UPDATE UOMP_ORDER_RECORD
		<set>
			<if test="taskName != null">
				TASK_NAME = #{taskName,jdbcType=VARCHAR},
			</if>
			<if test="startTime != null">
				START_TIME = #{startTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="handlerType != null">
				HANDLER_TYPE = #{handlerType,jdbcType=VARCHAR},
			</if>
			<if test="handlerOrg != null">
				HANDLER_ORG = #{handlerOrg,jdbcType=VARCHAR},
			</if>
			<if test="handlerOrgId != null">
				HANDLER_ORG_ID = #{handlerOrgId,jdbcType=VARCHAR},
			</if>
			<if test="handlerGroup != null">
				HANDLER_GROUP = #{handlerGroup,jdbcType=VARCHAR},
			</if>
			<if test="handlerGroupId != null">
				HANDLER_GROUP_ID = #{handlerGroupId,jdbcType=VARCHAR},
			</if>
			<if test="handler != null">
				HANDLER = #{handler,jdbcType=VARCHAR},
			</if>
			<if test="handlerNo != null">
				HANDLER_NO = #{handlerNo,jdbcType=VARCHAR},
			</if>
			<if test="handleName != null">
				HANDLE_NAME = #{handleName,jdbcType=VARCHAR},
			</if>
			<if test="handleOpin != null">
				HANDLE_OPIN = #{handleOpin,jdbcType=VARCHAR},
			</if>
			<if test="duration != null">
				DURATION = #{duration,jdbcType=NUMERIC},
			</if>
			<if test="pauseType != null">
				PAUSE_TYPE = #{pauseType,jdbcType=VARCHAR},
			</if>
			UPDATE_TIME = sysdate,
		</set>
		WHERE
			TASK_ID = #{taskId,jdbcType=VARCHAR}
	</update>
	<update id="updateOrderRecordByInstid" parameterType="cn.gwssi.uomp.entity.UompOrderRecord">
		UPDATE UOMP_ORDER_RECORD
		<set>
			<if test="taskName != null">
				TASK_NAME = #{taskName,jdbcType=VARCHAR},
			</if>
			<if test="startTime != null">
				START_TIME = #{startTime,jdbcType=TIMESTAMP},
			</if>
			<if test="endTime != null">
				END_TIME = #{endTime,jdbcType=TIMESTAMP},
			</if>
			<if test="handlerType != null">
				HANDLER_TYPE = #{handlerType,jdbcType=VARCHAR},
			</if>
			<if test="handlerOrg != null">
				HANDLER_ORG = #{handlerOrg,jdbcType=VARCHAR},
			</if>
			<if test="handlerOrgId != null">
				HANDLER_ORG_ID = #{handlerOrgId,jdbcType=VARCHAR},
			</if>
			<if test="handlerGroup != null">
				HANDLER_GROUP = #{handlerGroup,jdbcType=VARCHAR},
			</if>
			<if test="handlerGroupId != null">
				HANDLER_GROUP_ID = #{handlerGroupId,jdbcType=VARCHAR},
			</if>
			<if test="handler != null">
				HANDLER = #{handler,jdbcType=VARCHAR},
			</if>
			<if test="handlerNo != null">
				HANDLER_NO = #{handlerNo,jdbcType=VARCHAR},
			</if>
			<if test="handleName != null">
				HANDLE_NAME = #{handleName,jdbcType=VARCHAR},
			</if>
			<if test="handleOpin != null">
				HANDLE_OPIN = #{handleOpin,jdbcType=VARCHAR},
			</if>
			<if test="duration != null">
				DURATION = #{duration,jdbcType=NUMERIC},
			</if>
			<if test="pauseNum != null">
				PAUSE_NUM = #{pauseNum,jdbcType=NUMERIC},
			</if>
			<if test="pauseType != null">
				PAUSE_TYPE = #{pauseType,jdbcType=VARCHAR},
			</if>
			UPDATE_TIME = sysdate,
		</set>
		WHERE
		INST_ID = #{instId,jdbcType=VARCHAR} and END_TIME is null and TASK_ID is not null
	</update>
	<update id="updateWorkOrder" parameterType="java.util.HashMap">
		update UOMP_WORK_ORDER
		set
			handler_group = #{handler_group,jdbcType=VARCHAR},
			<if test="handler_group_id != null">
				handler_group_id = #{handler_group_id,jdbcType=VARCHAR},
			</if>
			<if test="order_state != null">
				order_state = decode(order_state,'5','5',#{order_state,jdbcType=VARCHAR}),
			</if>
			pause_sum = nvl(pause_sum,0),
			handler = #{handler,jdbcType=VARCHAR},
			handler_id = #{handler_id,jdbcType=VARCHAR},
			UPDATE_TIME = sysdate
		 where inst_id = #{inst_id}
	</update>
	<select id="getLastHandleTime" parameterType="java.lang.String" resultMap="BaseResultMap">
		select
			<include refid="Base_Column_List"></include>
		 from uomp_order_record where inst_id = #{instanceId} and end_time is not null order by end_time desc limit 1
	</select>
	<delete id="deleteByTaskId" parameterType="java.lang.String">
		delete from uomp_order_record where task_id = #{taskid}
	</delete>
	<update id="updateOrderRecordStart" parameterType="cn.gwssi.uomp.entity.UompOrderRecord">
			UPDATE UOMP_ORDER_RECORD set TASK_NAME = #{taskName}
			where inst_id = #{instId} and task_name = '启动' and task_id is null
	</update>
	<select id="getEventDataByInstId" parameterType="java.lang.String" resultType="java.util.HashMap">
		select SLA_LEVEL,SLA_ID,SLA_NAME,RESOURCE_ID,PROJECT_ID from UOMP_EVENT where INST_ID = #{instid}
	</select>
	<select id="getBusDataByInstId" parameterType="java.util.HashMap" resultType="java.util.HashMap">
		select ${col_list} DEL_FLAG from ${table_name} where inst_id = #{inst_id}
	</select>
	<select id="getProjectNames" resultType="java.lang.String">
		select nvl(wm_concat(PROJECT_NAME),'') from UOMP_PROJECT_MANAGEMENT where ID in
		<foreach collection="list" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>
	<select id="getResourceNames" resultType="java.lang.String">
		select nvl(wm_concat(RESOURCE_NAME),'') from CMDB_COMM_RESOURCE where id in
		<foreach collection="list" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>
	<select id="getBusDataChangeList" resultType="java.util.HashMap" parameterType="java.util.HashMap">
		select TABLE_FIELD_ as col_key,nvl(LAST_DATA_,'') new_data,nvl(PREV_DATA_,'') as old_data from BPM_SUBMIT_DATA_CHANGE_LOG where TASK_ID_ = #{task_id} and TABLE_CODE_ = #{table_name}
		and instr(#{col_list},TABLE_FIELD_) > 0
	</select>
    <select id="getSignHandlerName" resultType="java.lang.String" parameterType="java.lang.String">
		select wm_concat(APPROVER_NAME_) as name from BPM_TASK_OPINION where SIGN_ID_ = #{taskId}
	</select>
	<resultMap id="TaskIdentityLink" type="cn.gwssi.ecloudbpm.wf.core.model.TaskIdentityLink">
		<id property="id" column="id_" jdbcType="VARCHAR"/>
		<result property="taskId" column="task_id_" jdbcType="VARCHAR"/>
		<result property="instId" column="inst_id_" jdbcType="VARCHAR"/>
		<result property="type" column="type_" jdbcType="VARCHAR"/>
		<result property="identityName" column="identity_name_" jdbcType="VARCHAR"/>
		<result property="identity" column="identity_" jdbcType="VARCHAR"/>
		<result property="permissionCode" column="permission_code_" jdbcType="VARCHAR"/>
		<result property="taskType" column="task_type_" jdbcType="VARCHAR"/>
		<result property="checkStatus" column="check_status_" jdbcType="VARCHAR"/>
		<result property="checkTime" column="check_time_" jdbcType="TIMESTAMP"/>
		<result property="orgId" column="org_id_" jdbcType="VARCHAR"/>
	</resultMap>
	<select id="getInstIndentityList" parameterType="java.lang.String" resultMap="TaskIdentityLink">
		select distinct type_,identity_name_,identity_,org_id_ from bpm_task_identitylink where inst_id_ = #{instanceId}
	</select>
</mapper>