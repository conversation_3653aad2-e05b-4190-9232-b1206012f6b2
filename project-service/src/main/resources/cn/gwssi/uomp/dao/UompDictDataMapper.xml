<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompDictDataMapper">
    <select id="getNameByDictKey" parameterType="java.lang.String" resultType="java.lang.String">
        select name_ name from SYS_DATA_DICT where DICT_KEY_=#{dict} and key_=#{key} and rownum=1
    </select>
    <select id="getKeyByDictName" parameterType="java.lang.String" resultType="java.lang.String">
        select wm_concat(key_) as key_  from SYS_DATA_DICT where DICT_KEY_=#{dict} and name_
        in
        <foreach collection="nameList" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>
    <select id="getFormCustDialogDataBySQL" resultType="java.util.HashMap" parameterType="java.util.HashMap">
        select ${id} id,${name} name from ${table_name} where instr(#{col_val},${name})>0
    </select>

</mapper>
