<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompLxGroupMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompLxGroup">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="groupName" column="GROUP_NAME" jdbcType="VARCHAR"/>
        <result property="groupType" column="GROUP_TYPE" jdbcType="VARCHAR"/>
        <result property="teamId" column="TEAM_ID" jdbcType="VARCHAR"/>
        <result property="teamName" column="TEAM_NAME" jdbcType="VARCHAR"/>
        <result property="robotAddress" column="ROBOT_ADDRESS" jdbcType="VARCHAR"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,GROUP_NAME,GROUP_TYPE,TEAM_ID,
        TEAM_NAME,ROBOT_ADDRESS,REMARK,
        STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,
        UPDATE_TIME,DEL_FLAG
    </sql>




    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_LX_GROUP
        where DEL_FLAG = '0' and instr(#{ids},ID)
    </select>
</mapper>
