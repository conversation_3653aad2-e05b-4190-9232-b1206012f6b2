<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompWorkOrderMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompWorkOrder">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="requestId" column="REQUEST_ID" jdbcType="VARCHAR"/>
            <result property="orderType" column="ORDER_TYPE" jdbcType="VARCHAR"/>
            <result property="orderNo" column="ORDER_NO" jdbcType="VARCHAR"/>
            <result property="orderLevel" column="ORDER_LEVEL" jdbcType="VARCHAR"/>
            <result property="orderTitle" column="ORDER_TITLE" jdbcType="VARCHAR"/>
            <result property="orderState" column="ORDER_STATE" jdbcType="VARCHAR"/>
            <result property="serviceType" column="SERVICE_TYPE" jdbcType="VARCHAR"/>
            <result property="resourceId" column="RESOURCE_ID" jdbcType="VARCHAR"/>
            <result property="handler" column="HANDLER" jdbcType="VARCHAR"/>
            <result property="handlerId" column="HANDLER_ID" jdbcType="VARCHAR"/>
            <result property="handlerGroup" column="HANDLER_GROUP" jdbcType="VARCHAR"/>
            <result property="handlerGroupId" column="HANDLER_GROUP_ID" jdbcType="VARCHAR"/>
            <result property="applicant" column="APPLICANT" jdbcType="VARCHAR"/>
            <result property="applicantTel" column="APPLICANT_TEL" jdbcType="VARCHAR"/>
            <result property="linkStartTime" column="LINK_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="flowStartTime" column="FLOW_START_TIME" jdbcType="TIMESTAMP"/>
            <result property="linkOverTime" column="LINK_OVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="flowOverTime" column="FLOW_OVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="timeLimit" column="TIME_LIMIT" jdbcType="DECIMAL"/>
            <result property="ifPause" column="IF_PAUSE" jdbcType="VARCHAR"/>
            <result property="pauseTime" column="PAUSE_TIME" jdbcType="TIMESTAMP"/>
            <result property="pauseSum" column="PAUSE_SUM" jdbcType="DECIMAL"/>
            <result property="remindLimit" column="REMIND_LIMIT" jdbcType="DECIMAL"/>
            <result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
            <result property="finishTime" column="FINISH_TIME" jdbcType="TIMESTAMP"/>
            <result property="ifFeedback" column="IF_FEEDBACK" jdbcType="VARCHAR"/>
            <result property="ifSplitChild" column="IF_SPLIT_CHILD" jdbcType="VARCHAR"/>
            <result property="parentInstId" column="PARENT_INST_ID" jdbcType="VARCHAR"/>
            <result property="registrant" column="REGISTRANT" jdbcType="VARCHAR"/>
            <result property="registrantId" column="REGISTRANT_ID" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="projectId" column="PROJECT_ID" jdbcType="VARCHAR"/>
            <result property="ifVip" column="IF_VIP" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,REQUEST_ID,ORDER_TYPE,
        ORDER_NO,ORDER_LEVEL,ORDER_TITLE,
        ORDER_STATE,SERVICE_TYPE,RESOURCE_ID,
        HANDLER,HANDLER_ID,HANDLER_GROUP,
        HANDLER_GROUP_ID,APPLICANT,APPLICANT_TEL,
        LINK_START_TIME,FLOW_START_TIME,LINK_OVER_TIME,
        FLOW_OVER_TIME,TIME_LIMIT,IF_PAUSE,
        PAUSE_TIME,PAUSE_SUM,REMIND_LIMIT,
        INST_ID,FINISH_TIME,IF_FEEDBACK,
        IF_SPLIT_CHILD,PARENT_INST_ID,REGISTRANT,
        REGISTRANT_ID,CREATE_BY,CREATE_TIME,
        CREATE_ORG_ID,UPDATE_BY,UPDATE_TIME,IF_VIP
        DEL_FLAG
    </sql>
    <update id="updateByInstid" parameterType="cn.gwssi.uomp.entity.UompWorkOrder">
        update UOMP_WORK_ORDER
        <set>
            <if test="ifPause != null">
                IF_PAUSE = #{ifPause,jdbcType=VARCHAR},
            </if>
            <if test="pauseTime != null">
                PAUSE_TIME = #{pauseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pauseSum != null">
                PAUSE_SUM = PAUSE_SUM + #{pauseSum,jdbcType=NUMERIC},
            </if>
            <if test="flowOverTime != null">
                FLOW_OVER_TIME = #{flowOverTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderState != null">
                ORDER_STATE = #{orderState,jdbcType=TIMESTAMP},
            </if>
            <if test="restart != null">
                RESTART = RESTART + 1,
            </if>
            UPDATE_TIME = sysdate,
        </set>
        where  INST_ID = #{instId,jdbcType=VARCHAR}
    </update>
    <select id="getUnOverOrderList" parameterType="cn.gwssi.uomp.entity.UompWorkOrder" resultMap="BaseResultMap">
        select w.id,w.order_no,w.order_title,w.inst_id,w.flow_start_time,w.flow_over_time,w.pause_time,w.time_limit,w.handler_id,w.handler_group_id,
        w.remind_limit,order_level from UOMP_WORK_ORDER w
        join UOMP_EVENT t on t.INST_ID = w.INST_ID
        where w.order_state = '1'
        and w.if_pause ='0'
        and w.order_type = '1'
        and t.event_level = #{priId}
        and w.flow_over_time is not null
    </select>
    <select id="getWorkOrderByInstid" resultMap="BaseResultMap" parameterType="java.lang.String">
        select  <include refid="Base_Column_List" />
         from UOMP_WORK_ORDER where INST_ID = #{instid}
    </select>
    <select id="selectSla" resultType="java.lang.String">
        select ID from UOMP_SLA_PRI where SLA_ID = #{sla_id} and PRI_NAME = #{sla_level}
    </select>
    <select id="getInstIdByOrderNo" resultType="java.lang.String" parameterType="java.lang.String">
        select inst_id from UOMP_WORK_ORDER where ORDER_NO = #{order_no} limit 1
    </select>
    <select id="getTaskId" resultType="java.lang.String" parameterType="java.lang.String">
         select id_ from BPM_TASK where INST_ID_ = #{inst_id}
    </select>
    <select id="getInstId" resultType="java.lang.String" parameterType="java.lang.String">
        select inst_id_ from bpm_task_stack where task_id_ = #{taskid} limit 1
    </select>
    <insert id="insertCycleRecord" parameterType="java.util.HashMap">
        insert into uomp_cycle_order_record(id, manage_id, inst_id,plan_exec_time, order_title, create_by, create_time, update_by, update_time, del_flag,handler_id,handler,handler_type)
        values(#{id}, #{manage_id}, #{inst_id},#{plan_exec_time}, #{order_title}, #{create_by}, sysdate, #{create_by}, sysdate, '0',#{handler_id},#{handler},#{handler_type})
    </insert>
    <select id="findOrderByTimeAndConfigId" resultType="java.lang.Integer">
        select count(0) from uomp_cycle_order_record where manage_id = #{id}
        and plan_exec_time = #{timestr}
        and handler_id = #{handler_id}
    </select>
    <select id="getLinkId" resultType="java.lang.String">
         select id_ from BPM_TASK_IDENTITYLINK where INST_ID_ = #{inst_id} and TASK_ID_ = #{task_id} limit 1
    </select>
</mapper>
