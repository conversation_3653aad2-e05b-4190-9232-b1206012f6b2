<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompMsgMailMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompMsgMail">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="mailAddress" column="MAIL_ADDRESS" jdbcType="VARCHAR"/>
            <result property="mailPassword" column="MAIL_PASSWORD" jdbcType="CHAR"/>
            <result property="mailNickname" column="MAIL_NICKNAME" jdbcType="VARCHAR"/>
            <result property="mailHost" column="MAIL_HOST" jdbcType="VARCHAR"/>
            <result property="mailPort" column="MAIL_PORT" jdbcType="VARCHAR"/>
            <result property="mailSsl" column="MAIL_SSL" jdbcType="VARCHAR"/>
            <result property="mailReceiveAddress" column="MAIL_RECEIVE_ADDRESS" jdbcType="VARCHAR"/>
            <result property="mailReceiveContent" column="MAIL_RECEIVE_CONTENT" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,MAIL_ADDRESS,MAIL_PASSWORD,
        MAIL_NICKNAME,MAIL_HOST,
        MAIL_PORT,MAIL_SSL,MAIL_RECEIVE_ADDRESS,
        MAIL_RECEIVE_CONTENT,
        CREATE_BY,CREATE_TIME,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG
    </sql>

    <select id="getOneOrderCreateTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from UOMP_MSG_MAIL
        order by CREATE_TIME
        limit 1

    </select>
</mapper>
