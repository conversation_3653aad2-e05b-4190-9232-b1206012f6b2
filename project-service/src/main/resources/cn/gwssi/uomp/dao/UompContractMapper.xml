<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompContractMapper">

    <update id="updateStatusById">
        update UOMP_CONTRACT_MANAGEMENT set
        CONTRACT_STATUS = #{status}
        where ID = #{contractId}
    </update>


    <select id="selectListByNotStatus" resultType="java.util.Map">
        select
        ID AS id,
        to_char(QUALITY_END_DAY,'yyyy-MM-dd') as quality_end_day
        from UOMP_CONTRACT_MANAGEMENT
        where DEL_FLAG = '0'
        and CONTRACT_STATUS not in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <select id="selectPropertyListByContractId" resultType="java.util.Map">
        select
        t.id,
        t.tend_date_start as startTime,
        t.tend_date_end as endTime
        from CMDB_COMM_RESOURCE t
        left join CMDB_TABLE_BASE_CONFIG b on t.BASELINE_ID = b.BASELINE_ID
        where t.DEL_FLAG = '0' and t.CONTRACT_ID = #{contractId}
        and t.AUDIT_STATE = '2' and RESOURCE_STATE !='2'
    </select>

</mapper>
