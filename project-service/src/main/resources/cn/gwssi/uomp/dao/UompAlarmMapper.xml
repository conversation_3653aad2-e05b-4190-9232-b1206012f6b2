<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompAlarmMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompAlarm">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="alarmId" column="ALARM_ID" jdbcType="VARCHAR"/>
        <result property="ipInstance" column="IP_INSTANCE" jdbcType="VARCHAR"/>
        <result property="groupId" column="GROUP_ID" jdbcType="VARCHAR"/>
        <result property="groupName" column="GROUP_NAME" jdbcType="VARCHAR"/>
        <result property="resourceId" column="RESOURCE_ID" jdbcType="VARCHAR"/>
        <result property="resourceName" column="RESOURCE_NAME" jdbcType="VARCHAR"/>
        <result property="resourceNo" column="RESOURCE_NO" jdbcType="VARCHAR"/>
        <result property="alarmType" column="ALARM_TYPE" jdbcType="VARCHAR"/>
        <result property="alarmLevel" column="ALARM_LEVEL" jdbcType="VARCHAR"/>
        <result property="alarmContent" column="ALARM_CONTENT" jdbcType="VARCHAR"/>
        <result property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
        <result property="orderNo" column="ORDER_NO" jdbcType="VARCHAR"/>
        <result property="orderTitle" column="ORDER_TITLE" jdbcType="VARCHAR"/>
        <result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
        <result property="alarmStatus" column="ALARM_STATUS" jdbcType="VARCHAR"/>
        <result property="alarmTime" column="ALARM_TIME" jdbcType="TIMESTAMP"/>
        <result property="handleTime" column="HANDLE_TIME" jdbcType="TIMESTAMP"/>
        <result property="isMisinformation" column="IS_MISINFORMATION" jdbcType="VARCHAR"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
        <result property="deviceModel" column="DEVICE_MODEL" jdbcType="VARCHAR"/>
        <result property="disposeTime" column="DISPOSE_TIME" jdbcType="TIMESTAMP"/>
        <result property="disposePerson" column="DISPOSE_PERSON" jdbcType="VARCHAR"/>
        <result property="disposeMethod" column="DISPOSE_METHOD" jdbcType="VARCHAR"/>
        <result property="disposeReason" column="DISPOSE_REASON" jdbcType="VARCHAR"/>
        <result property="disposeRemark" column="DISPOSE_REMARK" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,ALARM_ID,IP_INSTANCE,
        GROUP_ID,GROUP_NAME,RESOURCE_ID,
        RESOURCE_NAME,RESOURCE_NO,ALARM_TYPE,
        ALARM_LEVEL,ALARM_CONTENT,ORDER_ID,
        ORDER_NO,ORDER_TITLE,INST_ID,
        ALARM_STATUS,ALARM_TIME,HANDLE_TIME,
        IS_MISINFORMATION,CREATE_BY,CREATE_TIME,
        UPDATE_BY,UPDATE_TIME,DEL_FLAG,
        DEVICE_MODEL,DISPOSE_TIME,DISPOSE_PERSON,DISPOSE_METHOD,DISPOSE_REASON,DISPOSE_REMARK
    </sql>
    <insert id="insertBatch">
        insert into UOMP_ALARM
        (ID,
        ALARM_ID,
        IP_INSTANCE,
        GROUP_ID,
        GROUP_NAME,
        RESOURCE_ID,
        RESOURCE_NAME,
        RESOURCE_NO,
        DEVICE_MODEL,
        ALARM_TYPE,
        ALARM_LEVEL,
        ALARM_CONTENT,
        ORDER_ID,
        ORDER_NO,
        ORDER_TITLE,
        INST_ID,
        ALARM_STATUS,
        ALARM_TIME,
        HANDLE_TIME,
        IS_MISINFORMATION,
        DISPOSE_TIME,
        DISPOSE_PERSON,
        DISPOSE_METHOD,
        DISPOSE_REASON,
        DISPOSE_REMARK,
        CREATE_BY,
        CREATE_TIME,
        UPDATE_BY,
        UPDATE_TIME,
        DEL_FLAG)
        values
        <foreach collection="uompAlarmList" item="item" separator=",">
            (#{item.id},
            #{item.alarmId},
            #{item.ipInstance},
            #{item.groupId},
            #{item.groupName},
            #{item.resourceId},
            #{item.resourceName},
            #{item.resourceNo},
            #{item.deviceModel},
            #{item.alarmType},
            #{item.alarmLevel},
            #{item.alarmContent},
            #{item.orderId},
            #{item.orderNo},
            #{item.orderTitle},
            #{item.instId},
            #{item.alarmStatus},
            #{item.alarmTime},
            #{item.handleTime},
            #{item.isMisinformation},
            #{item.disposeTime},
            #{item.disposePerson},
            #{item.disposeMethod},
            #{item.disposeReason},
            #{item.disposeRemark},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.delFlag})
        </foreach>
    </insert>

    <update id="updateOrderByAlarmId" parameterType="cn.gwssi.uomp.entity.UompAlarm">
        update
        UOMP_ALARM
        <set>
            <if test="orderId !=null and orderId !=''">
                ORDER_ID = #{orderId},
            </if>
            <if test="orderNo !=null and orderNo !=''">
                ORDER_NO = #{orderNo},
            </if>
            <if test="orderTitle !=null and orderTitle !=''">
                ORDER_TITLE = #{orderTitle},
            </if>
            <if test="instId !=null and instId !=''">
                INST_ID = #{instId},
            </if>
            <if test="alarmStatus !=null and alarmStatus !=''">
                ALARM_STATUS = #{alarmStatus},
            </if>
            <if test="disposeMethod !=null and disposeMethod !=''">
                DISPOSE_METHOD = #{disposeMethod},
            </if>
            <if test="disposePerson !=null and disposePerson !=''">
                DISPOSE_REASON = #{disposePerson},
            </if>
        </set>
        where ALARM_ID = #{alarmId} and DEL_FLAG = '0'
    </update>

    <update id="updateHandleTimeByAlarmId">
        update
        UOMP_ALARM
        <set>
            <if test="handleTime !=null">
                HANDLE_TIME = #{handleTime},
            </if>
        </set>
        where ALARM_ID = #{alarmId} and DEL_FLAG = '0'
    </update>

    <select id="getUnCheckList" parameterType="cn.gwssi.uomp.entity.UompAlarm" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_ALARM
        where
         alarm_status = '0'
         and ID IN(select max(ID) from UOMP_ALARM where DEL_FLAG ='0'  group by ALARM_ID)
        <if test="alarmLevel != null">
          and   alarm_level  = #{alarmLevel}
        </if>
    </select>

    <select id="selectInfoByAlarmId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_ALARM
        where
        ALARM_ID = #{alarmId} and DEL_FLAG = '0'
        order by ALARM_TIME desc
        LIMIT 0,1
    </select>

    <select id="getSilence" resultType="java.util.Map">
        select
        ID as id,
        SILENT_WEEK as silentWeek,
        SILENT_TIME as silentTime
        from UOMP_ALARM_SILENT_STRATEGY
        where IS_START = '0' and SILENT_TYPE = '1' and DEL_FLAG = '0'
    </select>
</mapper>
