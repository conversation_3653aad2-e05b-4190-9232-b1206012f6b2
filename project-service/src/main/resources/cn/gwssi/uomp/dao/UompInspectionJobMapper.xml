<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompInspectionJobMapper">

    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.UompInspectionJob">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="jobId" column="JOB_ID" jdbcType="VARCHAR"/>
            <result property="inspType" column="INSP_TYPE" jdbcType="VARCHAR"/>
            <result property="planId" column="PLAN_ID" jdbcType="VARCHAR"/>
            <result property="jobName" column="JOB_NAME" jdbcType="VARCHAR"/>
            <result property="inspPlanTime" column="INSP_PLAN_TIME" jdbcType="TIMESTAMP"/>
            <result property="completeTime" column="COMPLETE_TIME" jdbcType="TIMESTAMP"/>
            <result property="inspResult" column="INSP_RESULT" jdbcType="VARCHAR"/>
            <result property="completeState" column="COMPLETE_STATE" jdbcType="VARCHAR"/>
            <result property="inspState" column="INSP_STATE" jdbcType="VARCHAR"/>
            <result property="templateId" column="TEMPLATE_ID" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
            <result property="orderFlag" column="ORDER_FLAG" jdbcType="VARCHAR"/>
            <result property="alarmFlag" column="ALARM_FLAG" jdbcType="VARCHAR"/>
            <result property="jobCron" column="JOB_CRON" jdbcType="VARCHAR"/>
            <result property="receiverId" column="RECEIVER_ID" jdbcType="VARCHAR"/>
            <result property="executor" column="EXECUTOR" jdbcType="VARCHAR"/>
            <result property="executorid" column="EXECUTORID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,JOB_ID,INSP_TYPE,
        PLAN_ID,JOB_NAME,INSP_PLAN_TIME,
        COMPLETE_TIME,INSP_RESULT,COMPLETE_STATE,
        INSP_STATE,TEMPLATE_ID,CREATE_BY,
        CREATE_TIME,UPDATE_BY,UPDATE_TIME,
        DEL_FLAG,ORDER_FLAG,ALARM_FLAG,JOB_CRON,RECEIVER_ID,EXECUTOR,EXECUTORID
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from UOMP_INSPECTION_JOB
        where  ID = #{id,jdbcType=VARCHAR} 
    </select>
    <select id="findJobByCronAndPlanId" resultType="java.lang.Integer">
        select count(0) from UOMP_INSPECTION_JOB where PLAN_ID = #{planid} and to_char(insp_plan_time,'yyyy-MM-dd hh24:mi:ss') = #{datestr} and del_flag = '0'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from UOMP_INSPECTION_JOB
        where  ID = #{id,jdbcType=VARCHAR} 
    </delete>
    <insert id="create" keyColumn="id" parameterType="cn.gwssi.uomp.entity.UompInspectionJob">
        insert into UOMP_INSPECTION_JOB
        ( ID,JOB_ID,INSP_TYPE
        ,PLAN_ID,JOB_NAME,INSP_PLAN_TIME
        ,COMPLETE_TIME,INSP_RESULT,COMPLETE_STATE
        ,INSP_STATE,TEMPLATE_ID,CREATE_BY
        ,CREATE_TIME,UPDATE_BY,UPDATE_TIME
        ,DEL_FLAG,ORDER_FLAG,ALARM_FLAG,JOB_CRON,RECEIVER_ID)
        values (#{id,jdbcType=VARCHAR},#{jobId,jdbcType=VARCHAR},#{inspType,jdbcType=VARCHAR}
        ,#{planId,jdbcType=VARCHAR},#{jobName,jdbcType=VARCHAR},#{inspPlanTime,jdbcType=TIMESTAMP}
        ,#{completeTime,jdbcType=TIMESTAMP},#{inspResult,jdbcType=VARCHAR},#{completeState,jdbcType=VARCHAR}
        ,#{inspState,jdbcType=VARCHAR},#{templateId,jdbcType=VARCHAR},#{createBy,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{delFlag,jdbcType=VARCHAR},#{orderFlag,jdbcType=VARCHAR},#{alarmFlag,jdbcType=VARCHAR},#{jobCron,jdbcType=VARCHAR},#{receiverId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.gwssi.uomp.entity.UompInspectionJob">
        insert into UOMP_INSPECTION_JOB
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">ID,</if>
                <if test="jobId != null">JOB_ID,</if>
                <if test="inspType != null">INSP_TYPE,</if>
                <if test="planId != null">PLAN_ID,</if>
                <if test="jobName != null">JOB_NAME,</if>
                <if test="inspPlanTime != null">INSP_PLAN_TIME,</if>
                <if test="completeTime != null">COMPLETE_TIME,</if>
                <if test="inspResult != null">INSP_RESULT,</if>
                <if test="completeState != null">COMPLETE_STATE,</if>
                <if test="inspState != null">INSP_STATE,</if>
                <if test="templateId != null">TEMPLATE_ID,</if>
                <if test="createBy != null">CREATE_BY,</if>
                <if test="createTime != null">CREATE_TIME,</if>
                <if test="updateBy != null">UPDATE_BY,</if>
                <if test="updateTime != null">UPDATE_TIME,</if>
                <if test="delFlag != null">DEL_FLAG,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null"> #{id,jdbcType=VARCHAR},</if>
                <if test="jobId != null"> #{jobId,jdbcType=VARCHAR},</if>
                <if test="inspType != null"> #{inspType,jdbcType=VARCHAR},</if>
                <if test="planId != null"> #{planId,jdbcType=VARCHAR},</if>
                <if test="jobName != null"> #{jobName,jdbcType=VARCHAR},</if>
                <if test="inspPlanTime != null"> #{inspPlanTime,jdbcType=TIMESTAMP},</if>
                <if test="completeTime != null"> #{completeTime,jdbcType=TIMESTAMP},</if>
                <if test="inspResult != null"> #{inspResult,jdbcType=VARCHAR},</if>
                <if test="completeState != null"> #{completeState,jdbcType=VARCHAR},</if>
                <if test="inspState != null"> #{inspState,jdbcType=VARCHAR},</if>
                <if test="templateId != null"> #{templateId,jdbcType=VARCHAR},</if>
                <if test="createBy != null"> #{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null"> #{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null"> #{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null"> #{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="delFlag != null"> #{delFlag,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.gwssi.uomp.entity.UompInspectionJob">
        update UOMP_INSPECTION_JOB
        <set>
                <if test="jobId != null">
                    JOB_ID = #{jobId,jdbcType=VARCHAR},
                </if>
                <if test="inspType != null">
                    INSP_TYPE = #{inspType,jdbcType=VARCHAR},
                </if>
                <if test="planId != null">
                    PLAN_ID = #{planId,jdbcType=VARCHAR},
                </if>
                <if test="jobName != null">
                    JOB_NAME = #{jobName,jdbcType=VARCHAR},
                </if>
                <if test="inspPlanTime != null">
                    INSP_PLAN_TIME = #{inspPlanTime,jdbcType=TIMESTAMP},
                </if>
                <if test="completeTime != null">
                    COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
                </if>
                <if test="inspResult != null">
                    INSP_RESULT = #{inspResult,jdbcType=VARCHAR},
                </if>
                <if test="completeState != null">
                    COMPLETE_STATE = #{completeState,jdbcType=VARCHAR},
                </if>
                <if test="inspState != null">
                    INSP_STATE = #{inspState,jdbcType=VARCHAR},
                </if>
                <if test="templateId != null">
                    TEMPLATE_ID = #{templateId,jdbcType=VARCHAR},
                </if>
                <if test="createBy != null">
                    CREATE_BY = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="delFlag != null">
                    DEL_FLAG = #{delFlag,jdbcType=VARCHAR},
                </if>
                <if test="orderFlag != null">
                    ORDER_FLAG = #{orderFlag,jdbcType=VARCHAR},
                </if>
                <if test="alarmFlag != null">
                    ALARM_FLAG = #{alarmFlag,jdbcType=VARCHAR},
                </if>
                <if test="jobCron != null">
                    JOB_CRON = #{jobCron,jdbcType=VARCHAR},
                </if>
        </set>
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.gwssi.uomp.entity.UompInspectionJob">
        update UOMP_INSPECTION_JOB
        set 
            JOB_ID =  #{jobId,jdbcType=VARCHAR},
            INSP_TYPE =  #{inspType,jdbcType=VARCHAR},
            PLAN_ID =  #{planId,jdbcType=VARCHAR},
            JOB_NAME =  #{jobName,jdbcType=VARCHAR},
            INSP_PLAN_TIME =  #{inspPlanTime,jdbcType=TIMESTAMP},
            COMPLETE_TIME =  #{completeTime,jdbcType=TIMESTAMP},
            INSP_RESULT =  #{inspResult,jdbcType=VARCHAR},
            COMPLETE_STATE =  #{completeState,jdbcType=VARCHAR},
            INSP_STATE =  #{inspState,jdbcType=VARCHAR},
            TEMPLATE_ID =  #{templateId,jdbcType=VARCHAR},
            CREATE_BY =  #{createBy,jdbcType=VARCHAR},
            CREATE_TIME =  #{createTime,jdbcType=TIMESTAMP},
            UPDATE_BY =  #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME =  #{updateTime,jdbcType=TIMESTAMP},
            DEL_FLAG =  #{delFlag,jdbcType=VARCHAR}
        where   ID = #{id,jdbcType=VARCHAR} 
    </update>

    <select id="getDisposalMethodById" resultType="java.lang.String" parameterType="java.lang.String">
        select disposal_method from uomp_inspection_template_manag where id = #{templateId}
    </select>
</mapper>
