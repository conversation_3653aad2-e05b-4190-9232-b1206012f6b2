<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.dao.UompResourceMapper">
    <resultMap id="BaseResultMap" type="cn.gwssi.uomp.entity.CmdbCommResource">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="instId" column="INST_ID" jdbcType="VARCHAR"/>
        <result property="baselineId" column="BASELINE_ID" jdbcType="VARCHAR"/>
        <result property="groupId" column="GROUP_ID" jdbcType="VARCHAR"/>
        <result property="modelId" column="MODEL_ID" jdbcType="VARCHAR"/>
        <result property="resourceNo" column="RESOURCE_NO" jdbcType="VARCHAR"/>
        <result property="resourceName" column="RESOURCE_NAME" jdbcType="VARCHAR"/>
        <result property="deviceModel" column="DEVICE_MODEL" jdbcType="VARCHAR"/>
        <result property="resourceState" column="RESOURCE_STATE" jdbcType="VARCHAR"/>
        <result property="producer" column="PRODUCER" jdbcType="VARCHAR"/>
        <result property="deviceDutyor" column="DEVICE_DUTYOR" jdbcType="VARCHAR"/>
        <result property="tendDateStart" column="TEND_DATE_START" jdbcType="DATE"/>
        <result property="tendDateEnd" column="TEND_DATE_END" jdbcType="DATE"/>
        <result property="deviceUnitName" column="DEVICE_UNIT_NAME" jdbcType="VARCHAR"/>
        <result property="deviceUnitId" column="DEVICE_UNIT_ID" jdbcType="VARCHAR"/>
        <result property="supplierId" column="SUPPLIER_ID" jdbcType="VARCHAR"/>
        <result property="supplierName" column="SUPPLIER_NAME" jdbcType="VARCHAR"/>
        <result property="contractId" column="CONTRACT_ID" jdbcType="VARCHAR"/>
        <result property="contractName" column="CONTRACT_NAME" jdbcType="VARCHAR"/>
        <result property="projectId" column="PROJECT_ID" jdbcType="VARCHAR"/>
        <result property="projectName" column="PROJECT_NAME" jdbcType="VARCHAR"/>
        <result property="deviceState" column="DEVICE_STATE" jdbcType="VARCHAR"/>
        <result property="ipAddr" column="IP_ADDR" jdbcType="VARCHAR"/>
        <result property="submitTime" column="SUBMIT_TIME" jdbcType="TIMESTAMP"/>
        <result property="auditState" column="AUDIT_STATE" jdbcType="VARCHAR"/>
        <result property="auditResult" column="AUDIT_RESULT" jdbcType="VARCHAR"/>
        <result property="auditEndTime" column="AUDIT_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="auditer" column="AUDITER" jdbcType="VARCHAR"/>
        <result property="auditerid" column="AUDITERID" jdbcType="VARCHAR"/>
        <result property="ifNotice" column="IF_NOTICE" jdbcType="VARCHAR"/>
        <result property="noticeTime" column="NOTICE_TIME" jdbcType="TIMESTAMP"/>
        <result property="fileStr" column="FILE_STR" jdbcType="VARCHAR"/>
        <result property="auditOpin" column="AUDIT_OPIN" jdbcType="VARCHAR"/>
        <result property="devicePrice" column="DEVICE_PRICE" jdbcType="DECIMAL"/>
        <result property="metadata1" column="METADATA1" jdbcType="VARCHAR"/>
        <result property="metadata2" column="METADATA2" jdbcType="VARCHAR"/>
        <result property="metadata3" column="METADATA3" jdbcType="VARCHAR"/>
        <result property="metadata4" column="METADATA4" jdbcType="VARCHAR"/>
        <result property="metadata5" column="METADATA5" jdbcType="VARCHAR"/>
        <result property="extends1" column="EXTENDS1" jdbcType="VARCHAR"/>
        <result property="extends2" column="EXTENDS2" jdbcType="VARCHAR"/>
        <result property="extends3" column="EXTENDS3" jdbcType="VARCHAR"/>
        <result property="extends4" column="EXTENDS4" jdbcType="VARCHAR"/>
        <result property="extends5" column="EXTENDS5" jdbcType="VARCHAR"/>
        <result property="extends6" column="EXTENDS6" jdbcType="VARCHAR"/>
        <result property="extends7" column="EXTENDS7" jdbcType="VARCHAR"/>
        <result property="extends8" column="EXTENDS8" jdbcType="VARCHAR"/>
        <result property="extends9" column="EXTENDS9" jdbcType="VARCHAR"/>
        <result property="extends10" column="EXTENDS10" jdbcType="VARCHAR"/>
        <result property="extends11" column="EXTENDS11" jdbcType="VARCHAR"/>
        <result property="extends12" column="EXTENDS12" jdbcType="VARCHAR"/>
        <result property="extends13" column="EXTENDS13" jdbcType="VARCHAR"/>
        <result property="extends14" column="EXTENDS14" jdbcType="VARCHAR"/>
        <result property="extends15" column="EXTENDS15" jdbcType="VARCHAR"/>
        <result property="extends16" column="EXTENDS16" jdbcType="VARCHAR"/>
        <result property="extends17" column="EXTENDS17" jdbcType="VARCHAR"/>
        <result property="extends18" column="EXTENDS18" jdbcType="VARCHAR"/>
        <result property="extends19" column="EXTENDS19" jdbcType="VARCHAR"/>
        <result property="extends20" column="EXTENDS20" jdbcType="VARCHAR"/>
        <result property="extendsDate1" column="EXTENDS_DATE1" jdbcType="DATE"/>
        <result property="extendsDate2" column="EXTENDS_DATE2" jdbcType="DATE"/>
        <result property="extendsDate3" column="EXTENDS_DATE3" jdbcType="DATE"/>
        <result property="extendsDate4" column="EXTENDS_DATE4" jdbcType="DATE"/>
        <result property="extendsDate5" column="EXTENDS_DATE5" jdbcType="DATE"/>
        <result property="extendsInt1" column="EXTENDS_INT1" jdbcType="DECIMAL"/>
        <result property="extendsInt2" column="EXTENDS_INT2" jdbcType="DECIMAL"/>
        <result property="extendsInt3" column="EXTENDS_INT3" jdbcType="DECIMAL"/>
        <result property="extendsInt4" column="EXTENDS_INT4" jdbcType="DECIMAL"/>
        <result property="extendsInt5" column="EXTENDS_INT5" jdbcType="DECIMAL"/>
        <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
        <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="DEL_FLAG" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,INST_ID,BASELINE_ID,
        GROUP_ID,MODEL_ID,RESOURCE_NO,
        RESOURCE_NAME,DEVICE_MODEL,RESOURCE_STATE,
        PRODUCER,DEVICE_DUTYOR,TEND_DATE_START,
        TEND_DATE_END,DEVICE_UNIT_NAME,DEVICE_UNIT_ID,
        SUPPLIER_ID,SUPPLIER_NAME,CONTRACT_ID,
        CONTRACT_NAME,PROJECT_ID,PROJECT_NAME,
        DEVICE_STATE,IP_ADDR,SUBMIT_TIME,
        AUDIT_STATE,AUDIT_RESULT,AUDIT_END_TIME,
        AUDITER,AUDITERID,IF_NOTICE,
        NOTICE_TIME,FILE_STR,AUDIT_OPIN,
        DEVICE_PRICE,METADATA1,METADATA2,
        METADATA3,METADATA4,METADATA5,
        EXTENDS1,EXTENDS2,EXTENDS3,
        EXTENDS4,EXTENDS5,EXTENDS6,
        EXTENDS7,EXTENDS8,EXTENDS9,
        EXTENDS10,EXTENDS11,EXTENDS12,
        EXTENDS13,EXTENDS14,EXTENDS15,
        EXTENDS16,EXTENDS17,EXTENDS18,
        EXTENDS19,EXTENDS20,EXTENDS_DATE1,
        EXTENDS_DATE2,EXTENDS_DATE3,EXTENDS_DATE4,
        EXTENDS_DATE5,EXTENDS_INT1,EXTENDS_INT2,
        EXTENDS_INT3,EXTENDS_INT4,EXTENDS_INT5,
        CREATE_BY,UPDATE_BY,CREATE_TIME,
        UPDATE_TIME,DEL_FLAG,OTHER_COLS
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from CMDB_COMM_RESOURCE
        where  ID = #{id,jdbcType=VARCHAR}
    </select>
    <insert id="insertResource" parameterType="java.util.HashMap">
          insert into CMDB_COMM_RESOURCE(
                ID, INST_ID, BASELINE_ID, GROUP_ID, MODEL_ID, RESOURCE_NO, RESOURCE_NAME, DEVICE_MODEL,
                RESOURCE_STATE, TEND_DATE_START, TEND_DATE_END, DEVICE_UNIT_NAME, DEVICE_UNIT_ID, DEVICE_STATE, IP_ADDR, SUBMIT_TIME,
                AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME, IF_NOTICE, NOTICE_TIME, FILE_STR, AUDIT_OPIN, DEVICE_PRICE, OTHER_COLS,
                METADATA1, METADATA2, METADATA3, METADATA4, METADATA5, EXTENDS1, EXTENDS2, EXTENDS3, EXTENDS4, EXTENDS5, EXTENDS6,
                EXTENDS7, EXTENDS8, EXTENDS9, EXTENDS10, EXTENDS11, EXTENDS12, EXTENDS13, EXTENDS14, EXTENDS15, EXTENDS16, EXTENDS17,
                EXTENDS18, EXTENDS19, EXTENDS20, EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3, EXTENDS_DATE4, EXTENDS_DATE5, EXTENDS_INT1,
                EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4, EXTENDS_INT5, CREATE_BY, CREATE_TIME, DEL_FLAG,
                AUDITER, AUDITERID)
            VALUES
             (#{ID}, #{INST_ID}, #{BASELINE_ID}, #{GROUP_ID}, #{MODEL_ID},
            #{RESOURCE_NO}, #{RESOURCE_NAME}, #{DEVICE_MODEL},
            #{RESOURCE_STATE}, #{TEND_DATE_START}, #{TEND_DATE_END}, #{DEVICE_UNIT_NAME},
            #{DEVICE_UNIT_ID}, #{DEVICE_STATE}, #{IP_ADDR}, #{SUBMIT_TIME},
            '0', #{AUDIT_RESULT}, #{AUDIT_END_TIME},
            #{IF_NOTICE}, #{NOTICE_TIME}, #{FILE_STR}, #{AUDIT_OPIN},
            #{DEVICE_PRICE}, #{OTHER_COLS}, #{METADATA1}, #{METADATA2},
            #{METADATA3}, #{METADATA4}, #{METADATA5}, #{EXTENDS1},
            #{EXTENDS2}, #{EXTENDS3}, #{EXTENDS4}, #{EXTENDS5}, #{EXTENDS6},
            #{EXTENDS7}, #{EXTENDS8}, #{EXTENDS9},#{EXTENDS10}, #{EXTENDS11},
            #{EXTENDS12}, #{EXTENDS13}, #{EXTENDS14}, #{EXTENDS15}, #{EXTENDS16}, #{EXTENDS17},
            #{EXTENDS18}, #{EXTENDS19}, #{EXTENDS20}, #{EXTENDS_DATE1}, #{EXTENDS_DATE2}, #{EXTENDS_DATE3},
            #{EXTENDS_DATE4}, #{EXTENDS_DATE5}, #{EXTENDS_INT1}, #{EXTENDS_INT2},
            #{EXTENDS_INT3}, #{EXTENDS_INT4}, #{EXTENDS_INT5}, #{userId},
            sysdate,  '0', #{AUDITER}, #{AUDITERID})
    </insert>
    <insert id="insertResourceByList" parameterType="java.util.HashMap">
          insert into CMDB_COMM_RESOURCE(
                ID, INST_ID, BASELINE_ID, GROUP_ID, MODEL_ID, RESOURCE_NO, RESOURCE_NAME, DEVICE_MODEL,
                RESOURCE_STATE, TEND_DATE_START, TEND_DATE_END, DEVICE_UNIT_NAME, DEVICE_UNIT_ID, DEVICE_STATE, IP_ADDR, SUBMIT_TIME,
                AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME, IF_NOTICE, NOTICE_TIME, FILE_STR, AUDIT_OPIN, DEVICE_PRICE, OTHER_COLS,
                METADATA1, METADATA2, METADATA3, METADATA4, METADATA5, EXTENDS1, EXTENDS2, EXTENDS3, EXTENDS4, EXTENDS5, EXTENDS6,
                EXTENDS7, EXTENDS8, EXTENDS9, EXTENDS10, EXTENDS11, EXTENDS12, EXTENDS13, EXTENDS14, EXTENDS15, EXTENDS16, EXTENDS17,
                EXTENDS18, EXTENDS19, EXTENDS20, EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3, EXTENDS_DATE4, EXTENDS_DATE5, EXTENDS_INT1,
                EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4, EXTENDS_INT5, CREATE_BY, CREATE_TIME, DEL_FLAG,
                AUDITER, AUDITERID,SUPPLIER_ID, SUPPLIER_NAME, CONTRACT_ID, CONTRACT_NAME, PROJECT_ID, PROJECT_NAME,DEVICE_DUTYOR,PRODUCER,MAC_ADDR,ORDER_NO)
            VALUES
            <foreach collection="paramList" item="it" separator=",">
                (#{it.ID}, #{it.INST_ID}, #{it.BASELINE_ID}, #{it.GROUP_ID}, #{it.MODEL_ID},
                #{it.RESOURCE_NO}, #{it.RESOURCE_NAME}, #{it.DEVICE_MODEL},
                #{it.RESOURCE_STATE}, #{it.TEND_DATE_START,jdbcType=TIMESTAMP}, #{it.TEND_DATE_END,jdbcType=TIMESTAMP}, #{it.DEVICE_UNIT_NAME},
                #{it.DEVICE_UNIT_ID}, #{it.DEVICE_STATE}, #{it.IP_ADDR}, #{it.SUBMIT_TIME},
                '0', #{it.AUDIT_RESULT}, #{it.AUDIT_END_TIME},
                #{it.IF_NOTICE}, #{it.NOTICE_TIME}, #{it.FILE_STR}, #{it.AUDIT_OPIN},
                #{it.DEVICE_PRICE}, #{it.OTHER_COLS}, #{it.METADATA1}, #{it.METADATA2},
                #{it.METADATA3}, #{it.METADATA4}, #{it.METADATA5}, #{it.EXTENDS1},
                #{it.EXTENDS2}, #{it.EXTENDS3}, #{it.EXTENDS4}, #{it.EXTENDS5}, #{it.EXTENDS6},
                #{it.EXTENDS7}, #{it.EXTENDS8}, #{it.EXTENDS9},#{it.EXTENDS10}, #{it.EXTENDS11},
                #{it.EXTENDS12}, #{it.EXTENDS13}, #{it.EXTENDS14}, #{it.EXTENDS15}, #{it.EXTENDS16}, #{it.EXTENDS17},
                #{it.EXTENDS18}, #{it.EXTENDS19}, #{it.EXTENDS20}, #{it.EXTENDS_DATE1}, #{it.EXTENDS_DATE2}, #{it.XTENDS_DATE3},
                #{it.EXTENDS_DATE4}, #{it.EXTENDS_DATE5}, #{it.EXTENDS_INT1}, #{it.EXTENDS_INT2},
                #{it.EXTENDS_INT3}, #{it.EXTENDS_INT4}, #{it.EXTENDS_INT5}, #{it.userId},
                sysdate,  '0', #{it.AUDITER}, #{it.AUDITERID},#{it.SUPPLIER_ID},#{it.SUPPLIER_NAME},#{it.CONTRACT_ID},#{it.CONTRACT_NAME},#{it.PROJECT_ID},
                #{it.PROJECT_NAME}, #{it.DEVICE_DUTYOR}, #{it.PRODUCER}, #{it.MAC_ADDR}, #{it.ORDER_NO}
                )
            </foreach>
    </insert>
    <update id="insertOrUpdateResource" parameterType="java.util.HashMap">
        merge into CMDB_COMM_RESOURCE a
        using (select #{RESOURCE_NO} as RESOURCE_NO,#{MODEL_ID} as MODEL_ID,'0' as DEL_FLAG  from dual ) b
        on (a.RESOURCE_NO = b.RESOURCE_NO and a.MODEL_ID = b.MODEL_ID and a.DEL_FLAG = b.DEL_FLAG)
        when matched then
            update
                set
                a.BASELINE_ID = #{BASELINE_ID},
                a.GROUP_ID = #{GROUP_ID},
                <if test="RESOURCE_NAME != null">
                    a.RESOURCE_NAME = #{RESOURCE_NAME},
                </if>
                <if test="DEVICE_MODEL != null">
                        a.DEVICE_MODEL = #{DEVICE_MODEL},
                </if>
                <if test="RESOURCE_STATE != null">
                        a.RESOURCE_STATE = #{RESOURCE_STATE},
                </if>
                <if test="TEND_DATE_START != null">
                        a.TEND_DATE_START = #{TEND_DATE_START},
                </if>
                <if test="TEND_DATE_END != null">
                        a.TEND_DATE_END = #{TEND_DATE_END},
                </if>
                <if test="DEVICE_STATE != null">
                        a.DEVICE_STATE = #{DEVICE_STATE},
                </if>
                <if test="IP_ADDR != null">
                        a.IP_ADDR = #{IP_ADDR},
                </if>
                <if test="MAC_ADDR != null">
                    a.MAC_ADDR = #{MAC_ADDR},
                </if>
                <if test="DEVICE_PRICE != null">
                        a.DEVICE_PRICE = #{DEVICE_PRICE},
                </if>
                <if test="OTHER_COLS != null">
                        a.OTHER_COLS = #{OTHER_COLS},
                </if>
                <if test="METADATA1 != null">
                        a.METADATA1 = #{METADATA1},
                </if>
                <if test="METADATA2 != null">
                        a.METADATA2 = #{METADATA2},
                </if>
                <if test="METADATA3 != null">
                        a.METADATA3 = #{METADATA3},
                </if>
                <if test="METADATA4 != null">
                        a.METADATA4 = #{METADATA4},
                </if>
                <if test="METADATA5 != null">
                        a.METADATA5 = #{METADATA5},
                </if>
                <if test="EXTENDS1 != null">
                        a.EXTENDS1 = #{EXTENDS1},
                </if>
                <if test="EXTENDS2 != null">
                        a.EXTENDS2 = #{EXTENDS2},
                </if>
                <if test="EXTENDS3 != null">
                        a.EXTENDS3 = #{EXTENDS3},
                </if>
                <if test="EXTENDS4 != null">
                        a.EXTENDS4 = #{EXTENDS4},
                </if>
                <if test="EXTENDS5 != null">
                        a.EXTENDS5 = #{EXTENDS5},
                </if>
                <if test="EXTENDS6 != null">
                        a.EXTENDS6 = #{EXTENDS6},
                </if>
                <if test="EXTENDS7 != null">
                        a.EXTENDS7 = #{EXTENDS7},
                </if>
                <if test="EXTENDS8 != null">
                        a.EXTENDS8 = #{EXTENDS8},
                </if>
                <if test="EXTENDS9 != null">
                        a.EXTENDS9 = #{EXTENDS9},
                </if>
                <if test="EXTENDS10 != null">
                        a.EXTENDS10 = #{EXTENDS10},
                </if>
                <if test="EXTENDS11 != null">
                        a.EXTENDS11 = #{EXTENDS11},
                </if>
                <if test="EXTENDS12 != null">
                        a.EXTENDS12 = #{EXTENDS12},
                </if>
                <if test="EXTENDS13 != null">
                        a.EXTENDS13 = #{EXTENDS13},
                </if>
                <if test="EXTENDS14 != null">
                        a.EXTENDS14 = #{EXTENDS14},
                </if>
                <if test="EXTENDS15 != null">
                        a.EXTENDS15 = #{EXTENDS15},
                </if>
                <if test="EXTENDS16 != null">
                        a.EXTENDS16 = #{EXTENDS16},
                </if>
                <if test="EXTENDS17 != null">
                        a.EXTENDS17 = #{EXTENDS17},
                </if>
                <if test="EXTENDS18 != null">
                        a.EXTENDS18 = #{EXTENDS18},
                </if>
                <if test="EXTENDS19 != null">
                        a.EXTENDS19 = #{EXTENDS19},
                </if>
                <if test="EXTENDS20 != null">
                        a.EXTENDS20 = #{EXTENDS20},
                </if>
                <if test="EXTENDS_DATE1 != null">
                        a.EXTENDS_DATE1 = #{EXTENDS_DATE1},
                </if>
                <if test="EXTENDS_DATE2 != null">
                        a.EXTENDS_DATE2 = #{EXTENDS_DATE2},
                </if>
                <if test="EXTENDS_DATE3 != null">
                        a.EXTENDS_DATE3 = #{EXTENDS_DATE3},
                </if>
                <if test="EXTENDS_DATE4 != null">
                        a.EXTENDS_DATE4 = #{EXTENDS_DATE4},
                </if>
                <if test="EXTENDS_DATE5 != null">
                        a.EXTENDS_DATE5 = #{EXTENDS_DATE5},
                </if>
                <if test="EXTENDS_INT1 != null">
                        a.EXTENDS_INT1 = #{EXTENDS_INT1},
                </if>
                <if test="EXTENDS_INT2 != null">
                        a.EXTENDS_INT2 = #{EXTENDS_INT2},
                </if>
                <if test="EXTENDS_INT3 != null">
                        a.EXTENDS_INT3 = #{EXTENDS_INT3},
                </if>
                <if test="EXTENDS_INT4 != null">
                        a.EXTENDS_INT4 = #{EXTENDS_INT4},
                </if>
                <if test="EXTENDS_INT5 != null">
                        a.EXTENDS_INT5 = #{EXTENDS_INT5},
                </if>
                <if test="PRODUCER != null">
                        a.PRODUCER = #{PRODUCER},
                </if>
                <if test="DEVICE_DUTYOR != null">
                        a.DEVICE_DUTYOR = #{DEVICE_DUTYOR},
                </if>
                <if test="SUPPLIER_ID != null">
                        a.SUPPLIER_ID = #{SUPPLIER_ID},
                </if>
                <if test="SUPPLIER_NAME != null">
                        a.SUPPLIER_NAME = #{SUPPLIER_NAME},
                </if>
                <if test="CONTRACT_ID != null">
                        a.CONTRACT_ID = #{CONTRACT_ID},
                </if>
                <if test="CONTRACT_NAME != null">
                        a.CONTRACT_NAME = #{CONTRACT_NAME},
                </if>
                <if test="PROJECT_ID != null">
                        a.PROJECT_ID = #{PROJECT_ID},
                </if>
                <if test="PROJECT_NAME != null">
                        a.PROJECT_NAME = #{PROJECT_NAME},
                </if>
                <if test="DEVICE_UNIT_NAME != null">
                        a.DEVICE_UNIT_NAME = #{DEVICE_UNIT_NAME},
                </if>
                <if test="DEVICE_UNIT_ID != null">
                        a.DEVICE_UNIT_ID = #{DEVICE_UNIT_ID},
                </if>
                <if test="ORDER_NO != null">
                        a.ORDER_NO = #{ORDER_NO},
                </if>
                a.UPDATE_BY = #{userId},
                a.UPDATE_TIME = sysdate
        when not matched then
          insert (
                ID, INST_ID, BASELINE_ID, GROUP_ID, MODEL_ID, RESOURCE_NO, RESOURCE_NAME, DEVICE_MODEL,
                RESOURCE_STATE, TEND_DATE_START, TEND_DATE_END, DEVICE_UNIT_NAME, DEVICE_UNIT_ID, DEVICE_STATE, IP_ADDR, SUBMIT_TIME,
                AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME, IF_NOTICE, NOTICE_TIME, FILE_STR, AUDIT_OPIN, DEVICE_PRICE, OTHER_COLS,
                METADATA1, METADATA2, METADATA3, METADATA4, METADATA5, EXTENDS1, EXTENDS2, EXTENDS3, EXTENDS4, EXTENDS5, EXTENDS6,
                EXTENDS7, EXTENDS8, EXTENDS9, EXTENDS10, EXTENDS11, EXTENDS12, EXTENDS13, EXTENDS14, EXTENDS15, EXTENDS16, EXTENDS17,
                EXTENDS18, EXTENDS19, EXTENDS20, EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3, EXTENDS_DATE4, EXTENDS_DATE5, EXTENDS_INT1,
                EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4, EXTENDS_INT5, CREATE_BY, CREATE_TIME, DEL_FLAG,
                AUDITER, AUDITERID,SUPPLIER_ID, SUPPLIER_NAME, CONTRACT_ID, CONTRACT_NAME, PROJECT_ID, PROJECT_NAME,DEVICE_DUTYOR,PRODUCER,
                MAC_ADDR,ORDER_NO)
                VALUES
                    (#{ID}, #{INST_ID}, #{BASELINE_ID}, #{GROUP_ID}, #{MODEL_ID},
                    #{RESOURCE_NO}, #{RESOURCE_NAME}, #{DEVICE_MODEL},
                    #{RESOURCE_STATE}, #{TEND_DATE_START,jdbcType=TIMESTAMP}, #{TEND_DATE_END,jdbcType=TIMESTAMP}, #{DEVICE_UNIT_NAME},
                    #{DEVICE_UNIT_ID}, #{DEVICE_STATE}, #{IP_ADDR}, #{SUBMIT_TIME},
                    '0', #{AUDIT_RESULT}, #{AUDIT_END_TIME},
                    #{IF_NOTICE}, #{NOTICE_TIME}, #{FILE_STR}, #{AUDIT_OPIN},
                    #{DEVICE_PRICE}, #{OTHER_COLS}, #{METADATA1}, #{METADATA2},
                    #{METADATA3}, #{METADATA4}, #{METADATA5}, #{EXTENDS1},
                    #{EXTENDS2}, #{EXTENDS3}, #{EXTENDS4}, #{EXTENDS5}, #{EXTENDS6},
                    #{EXTENDS7}, #{EXTENDS8}, #{EXTENDS9},#{EXTENDS10}, #{EXTENDS11},
                    #{EXTENDS12}, #{EXTENDS13}, #{EXTENDS14}, #{EXTENDS15}, #{EXTENDS16}, #{EXTENDS17},
                    #{EXTENDS18}, #{EXTENDS19}, #{EXTENDS20}, #{EXTENDS_DATE1}, #{EXTENDS_DATE2}, #{XTENDS_DATE3},
                    #{EXTENDS_DATE4}, #{EXTENDS_DATE5}, #{EXTENDS_INT1}, #{EXTENDS_INT2},
                    #{EXTENDS_INT3}, #{EXTENDS_INT4}, #{EXTENDS_INT5}, #{userId},
                    sysdate,  '0', #{AUDITER}, #{AUDITERID},#{SUPPLIER_ID},#{SUPPLIER_NAME},#{CONTRACT_ID},#{CONTRACT_NAME},#{PROJECT_ID},
                    #{PROJECT_NAME}, #{DEVICE_DUTYOR}, #{PRODUCER}, #{MAC_ADDR}, #{ORDER_NO}
            )
         
    </update>
    <select id="getListById" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select id,resource_name,resource_no,device_model,to_char(tend_date_start,'yyyy/MM/dd') as tend_date_start,
        to_char(tend_date_end,'yyyy/MM/dd') as tend_date_end,device_unit_name from cmdb_comm_resource
        where del_flag = '0'
        <if test="type != 'all'">
           and id in (#{idlist})
        </if>
    </select>
    <select id="getAllUserRights" parameterType="java.lang.String" resultType="java.lang.String">
     select r.group_id_ || '-' ||case when p.ID is not null then 'team' else replace(r.type_,'User','') end   from org_relation r
      left join UOMP_TEAM p on p.ID = r.GROUP_ID_
      where user_id_ = #{currentUserId}
    </select>
    <select id="getModelData" parameterType="java.lang.String" resultType="java.util.HashMap">
        select  MODEL_ID,GROUP_ID,BASELINE_ID from CMDB_TABLE_BASE_CONFIG where BASELINE_ID = #{baseline_id}
    </select>
    <select id="getColConfigList" parameterType="java.lang.String" resultType="java.util.HashMap">
        select
            model_key, col_name, col_key, col_type, col_length, col_decimal, nvl(col_required,0) as col_required, col_primary,
            col_default_value, metadata_id, ctrl_type, ctrl_config, ctrl_valid_rule, col_comment,
            display_flag, show_flag, search_flag, statis_flag, mapping_col ,mapping_col_id,mapping_col_name,c.sn_
        from
        CMDB_TABLE_BASE_COL c
        where
        c.baseline_id = #{baseline_id}
        order by c.SN_
    </select>

    <select id="selectInfoById" resultType="java.util.Map">
        select
        a.ID as id,
        a.RESOURCE_NO as resourceNo,
        a.RESOURCE_NAME as resourceName,
        a.DEVICE_MODEL as deviceModel,
        b.PATH_ as pathStr
        from CMDB_COMM_RESOURCE a
        left join SYS_TREE_NODE b on a.GROUP_ID = b.ID_
        where a.ID = #{id} and AUDIT_STATE = '2' and DEL_FLAG = '0'
    </select>

    <select id="selectTreeNameById" resultType="java.lang.String">
        select NAME_ as name from SYS_TREE_NODE WHERE ID_ = #{id}
    </select>
    <select id="getResourceNolist" resultType="java.lang.String">
        select resource_no from cmdb_comm_resource where del_flag = '0'
    </select>
    <select id="getUnCheckResourceList" parameterType="cn.gwssi.uomp.entity.CmdbCommResource" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from CMDB_COMM_RESOURCE
        where  DEL_FLAG = '0' and AUDIT_STATE = '2'
        <!--2临期 3超期-->
        <if test='extends1 == "2" '>
            and TEND_DATE_END > sysdate
             <if test='extends2 == "day" '>
                 and ((TEND_DATE_END - sysdate) * 24 * 60) &lt;= #{extendsInt1}
             </if>
<!--            <if test='extends2 == "hour" '>-->
<!--                and (TEND_DATE_END - sysdate)*24 &lt;= #{extendsInt1}-->
<!--            </if>-->
<!--            <if test='extends2 == "minute" '>-->
<!--                and (TEND_DATE_END - sysdate)*24*60 &lt;= #{extendsInt1}-->
<!--            </if>-->
        </if>
        <if test='extends1 == "3"'><!--过保不需要目标时间-->
            and sysdate > TEND_DATE_END
<!--            <if test='extends2 == "day" '>-->
<!--                and (sysdate - TEND_DATE_END) &lt;= #{extendsInt1}-->
<!--            </if>-->
<!--            <if test='extends2 == "hour" '>-->
<!--                and (sysdate - TEND_DATE_END)*24 &lt;= #{extendsInt1}-->
<!--            </if>-->
<!--            <if test='extends2 == "minute" '>-->
<!--                and (sysdate - TEND_DATE_END)*24*60 &lt;= #{extendsInt1}-->
<!--            </if>-->
        </if>
    </select>
    <select id="getTeamList" resultType="java.util.HashMap">
        select id,name from UOMP_TEAM where  NAME in
        <foreach collection="paramList" item="item" open="(" separator="," close=")">
            #{item.RIGHTS_IDENTITY_NAME_}
        </foreach>
    </select>
    <select id="getAllResourceList" resultType="java.util.HashMap">
        select T.KEY AS MODEL_KEY,T.NAME AS MODEL_NAME,C.ID, INST_ID, C.BASELINE_ID, C.GROUP_ID, MODEL_ID, RESOURCE_NO, RESOURCE_NAME, DEVICE_MODEL,
         RESOURCE_STATE, PRODUCER, DEVICE_DUTYOR, TEND_DATE_START, TEND_DATE_END, DEVICE_UNIT_NAME, DEVICE_UNIT_ID, SUPPLIER_ID, SUPPLIER_NAME,
         CONTRACT_ID, CONTRACT_NAME, PROJECT_ID, PROJECT_NAME, DEVICE_STATE, IP_ADDR, SUBMIT_TIME, AUDIT_STATE, AUDIT_RESULT, AUDIT_END_TIME,
         AUDITER, AUDITERID, IF_NOTICE, NOTICE_TIME, FILE_STR, AUDIT_OPIN, DEVICE_PRICE, OTHER_COLS, METADATA1, METADATA2, METADATA3, METADATA4,
          METADATA5, EXTENDS1, EXTENDS2, EXTENDS3, EXTENDS4, EXTENDS5, EXTENDS6, EXTENDS7, EXTENDS8, EXTENDS9, EXTENDS10, EXTENDS11, EXTENDS12,
          EXTENDS13, EXTENDS14, EXTENDS15, EXTENDS16, EXTENDS17, EXTENDS18, EXTENDS19, EXTENDS20, EXTENDS_DATE1, EXTENDS_DATE2, EXTENDS_DATE3,
          EXTENDS_DATE4, EXTENDS_DATE5, EXTENDS_INT1, EXTENDS_INT2, EXTENDS_INT3, EXTENDS_INT4, EXTENDS_INT5, C.CREATE_BY, C.UPDATE_BY,
          C.CREATE_TIME, C.UPDATE_TIME, C.DEL_FLAG, MAC_ADDR from cmdb_comm_resource c left join cmdb_table_config t on t.id = c.model_id
          where c.audit_state = '2' and c.del_flag = '0'
    </select>
    <select id="getColMapList" resultType="java.util.HashMap" parameterType="java.lang.String">
         select
            model_key, col_name, col_key, col_type,
            col_default_value, metadata_id, ctrl_type, ctrl_config, ctrl_valid_rule, col_comment,
           mapping_col,mapping_col_id,mapping_col_name
        from
        CMDB_TABLE_BASE_COL c
        where
        c.baseline_id = #{baseline_id}
    </select>
    <select id="getResourceByNo" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select ID,nvl(AUDIT_STATE,'') AS AUDIT_STATE   from CMDB_COMM_RESOURCE where RESOURCE_NO = #{resourceNo} and MODEL_ID = #{modelId} and DEL_FLAG = '0' limit 1
    </select>
    <select id="judgeIfHasAuthUpdate" resultType="java.lang.Integer" parameterType="java.util.HashMap">
        select count(0) from SYS_AUTHORIZATION d where  d.RIGHTS_TARGET_ = #{RESOURCE_ID}
        and d.RIGHTS_PERMISSION_CODE_ in
        <foreach collection="USER_RIGHT_LIST" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
