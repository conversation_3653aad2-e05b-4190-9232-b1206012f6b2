<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.lanxin.dao.LanxinMapper">


    <select id="getAllOrg" resultType="java.util.Map">
        select
            ID_,NAME_,CODE_,MCODE_
        from ORG_GROUP
        where STATUS_ = '1'
    </select>


    <update id="updateUser">
        update
            ORG_USER_DETAIL
        set EXTEND1_ = #{lanxinUserId}
        where USER_ID_ = (select u.ID_ from
                            ORG_USER u left join ORG_RELATION rel on u.ID_ = rel.USER_ID_
                          where u.MOBILE_ = #{mobile} and rel.GROUP_ID_ = #{orgId} and rel.TYPE_ = 'groupUser'
                          order by u.CREATE_TIME_ limit 1
                          )
    </update>

    <select id="getLxUserIds" parameterType="java.util.List" resultType="java.lang.String">
        select
        EXTEND1_
        from ORG_USER_DETAIL
        where USER_ID_ in
        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <update id="updateUserById">
        update
            ORG_USER_DETAIL
        set EXTEND1_ = #{lxUserId}
        where USER_ID_ = #{userId}
    </update>

    <select id="getAllUser" resultType="java.util.Map">
        select u.ID_ , u.FULLNAME_ ,u.MOBILE_
        from ORG_USER u left join ORG_USER_DETAIL detail on u.ID_ = detail.USER_ID_
        where u.STATUS_ = '1' and u.ACTIVE_STATUS_ = '1' and detail.EXTEND1_ is null
    </select>

    <insert id="insertLanxinError">
        insert into UOMP_LX_ERROR(
            ID,USER_ID,USER_NAME,EROOR_TYPE,ERROE_INFO,CREATE_BY,CREATE_TIME,DEL_FLAG
        )VALUES(
                   #{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{errorType,jdbcType=TIMESTAMP}, #{errorInfo,jdbcType=TIMESTAMP},
                   #{createBy,jdbcType=VARCHAR},sysdate, #{delFlag,jdbcType=VARCHAR}
               )
    </insert>

    <select id="getMobileAndLxUserId" resultType="java.util.Map">
        select u.ID_ , u.FULLNAME_ ,u.MOBILE_ as mobile,detail.EXTEND1_ as extend1
        from ORG_USER u left join ORG_USER_DETAIL detail on u.ID_ = detail.USER_ID_
        where u.ID_ = #{userId}
    </select>

</mapper>
