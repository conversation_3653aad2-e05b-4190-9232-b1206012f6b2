<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.sms.dao.SMSMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="SMS" type="cn.gwssi.uomp.sms.model.SMSVo">
        <id column="ID" property="id"/>
        <result column="URL" property="url"/>
        <result column="SENDER_APP_CODE" property="senderAppCode"/>
        <result column="ADDR_LIST" property="addrlist"/>
        <result column="SENDER" property="sender"/>
        <result column="MESSAGE_BODY" property="messagebody"/>
        <result column="MESSAGE_TYPE" property="messageType"/>
        <result column="TITLE" property="title"/>
        <result column="TYPE" property="type"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID as "id",  URL as "url" ,SENDER_APP_CODE as "senderAppCode",ADDR_LIST as "addrlist",SENDER,MESSAGE_BODY as "messagebody",
            MESSAGE_TYPE as "messageType",TITLE as "title",TYPE as "type",
		CREATE_TIME as "createTime" , CREATE_USER as "createUser",UPDATE_USER as "updateUser" ,UPDATE_TIME as "updateTime"
    </sql>
    <update id="update" parameterType="cn.gwssi.uomp.sms.model.SMSVo">
        update UOMP_SMS_INFO
        <set>
            <if test="url != null and url !=''">
                URL = #{url,jdbcType=VARCHAR},
            </if>
            <if test="senderAppCode != null and senderAppCode !=''">
                SENDER_APP_CODE = #{senderAppCode,jdbcType=VARCHAR},
            </if>
            <if test="addrlist != null and addrlist !=''">
                ADDR_LIST = #{addrList,jdbcType=VARCHAR},
            </if>
            <if test="sender != null and sender !=''">
                SENDER = #{addrList,jdbcType=VARCHAR},
            </if>
            <if test="messagebody != null and messagebody !=''">
                MESSAGE_BODY = #{messagebody,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title !=''">
                TITLE = #{title,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type !=''">
                TYPE = #{type,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>

    <select id="select" parameterType="java.lang.String" resultType="cn.gwssi.uomp.sms.model.SMSVo">
        select <include refid="Base_Column_List"/> from UOMP_SMS_INFO
    </select>
    <insert id="create" parameterType="cn.gwssi.uomp.sms.model.SMSVo">
        insert into UOMP_SMS_INFO (ID, URL, SENDER_APP_CODE,ADDR_LIST,SENDER,MESSAGE_BODY,MESSAGE_TYPE,TITLE,TYPE,
                                      CREATE_USER,CREATE_TIME, UPDATE_USER, UPDATE_TIME)
        values (#{id,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},#{senderAppCode,jdbcType=VARCHAR},
                #{addrlist,jdbcType=VARCHAR},#{sender,jdbcType=VARCHAR},#{messagebody,jdbcType=VARCHAR},
                #{messageType,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},
                #{createUser,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="get" parameterType="java.lang.String" resultType="cn.gwssi.uomp.sms.model.SMSVo">
        select <include refid="Base_Column_List"/> from UOMP_SMS_INFO where ID = #{id}
    </select>
</mapper>
