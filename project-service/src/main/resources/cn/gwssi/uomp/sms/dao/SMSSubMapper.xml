<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gwssi.uomp.sms.dao.SMSSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="SMSSub" type="cn.gwssi.uomp.sms.model.SMSSubVo">
        <id column="ID" property="id"/>
        <result column="SMS_ID" property="smsId"/>
        <result column="IS_CURRENT_USER" property="isCurrentUser"/>
        <result column="MOBILE" property="mobile"/>
        <result column="FUNCTION_CODE" property="functionCode"/>
        <result column="TITLE" property="title"/>
        <result column="IS_ENABLE" property="isEnable"/>
        <result column="MESSAGE_BODY" property="messageBody"/>
        <result column="SENDER" property="sender"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="UPDATE_USER" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID as "id",  SMS_ID as "smsId" ,IS_CURRENT_USER as "isCurrentUser",MOBILE as "mobile",FUNCTION_CODE as "functionCode",
            TITLE as "title",IS_ENABLE as "isEnable",MESSAGE_BODY as "messageBody",SENDER as "sender",
		CREATE_TIME as "createTime" , CREATE_USER as "createUser",UPDATE_USER as "updateUser" ,UPDATE_TIME as "updateTime"
    </sql>
    <update id="update" parameterType="cn.gwssi.uomp.sms.model.SMSSubVo">
        update UOMP_SMS_SUB_INFO
        <set>
            <if test="isCurrentUser != null and isCurrentUser !=''">
                IS_CURRENT_USER = #{isCurrentUser,jdbcType=VARCHAR},
            </if>
            MOBILE = #{mobile,jdbcType=VARCHAR},
            <if test="functionCode != null and functionCode !=''">
                FUNCTION_CODE = #{functionCode,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title !=''">
                TITLE = #{title,jdbcType=VARCHAR},
            </if>
            <if test="isEnable != null and isEnable !=''">
                IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
            </if>
            SENDER = #{sender,jdbcType=VARCHAR},
            MESSAGE_BODY = #{messageBody,jdbcType=VARCHAR},

        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectPage" parameterType="java.lang.String" resultMap="SMSSub">
        select * from UOMP_SMS_SUB_INFO where 1=1
        <if test="title != null and title != ''">
            and (TITLE like concat('%',#{title},'%') or FUNCTION_CODE like  concat('%',#{title},'%'))
        </if>
    </select>

    <select id="selectByCode" parameterType="java.lang.String" resultType="cn.gwssi.uomp.sms.model.SMSSubVo">
        select <include refid="Base_Column_List"/> from UOMP_SMS_SUB_INFO where FUNCTION_CODE = #{code}
    </select>
    <insert id="create" parameterType="cn.gwssi.uomp.sms.model.SMSSubVo">
        insert into UOMP_SMS_SUB_INFO (ID, SMS_ID, IS_CURRENT_USER,MOBILE,FUNCTION_CODE,TITLE,IS_ENABLE,MESSAGE_BODY,SENDER,
                                      CREATE_USER,CREATE_TIME, UPDATE_USER, UPDATE_TIME)
        values (#{id,jdbcType=VARCHAR}, #{smsId,jdbcType=VARCHAR},#{isCurrentUser,jdbcType=VARCHAR},
                #{mobile,jdbcType=VARCHAR},#{functionCode,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR},
                #{isEnable,jdbcType=VARCHAR},#{messageBody,jdbcType=VARCHAR},#{sender,jdbcType=VARCHAR},
                #{createUser,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <delete id="remove" parameterType="java.lang.String">
        delete from UOMP_SMS_SUB_INFO where ID = #{id}
    </delete>
</mapper>
