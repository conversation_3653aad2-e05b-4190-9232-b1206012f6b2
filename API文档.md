# 资源效能管理模块 API 文档

## 概述

资源效能管理模块提供了完整的资源监控、效能规则管理和结果分析功能。模块包含三个主要控制器：

- **ResourceEfficiencyController**: 资源效能规则管理
- **ResourceIndexController**: 资源指标数据管理  
- **ResourceResultController**: 资源能效结果管理

## 1. 资源效能规则管理 (ResourceEfficiencyController)

**基础路径**: `/module/efficiency/rule`

### 1.1 获取规则列表
- **接口**: `POST /module/efficiency/rule/list`
- **描述**: 分页查询资源效能规则列表
- **请求参数**:
  - `pageQuery`: 分页参数
  - `rule`: 规则查询条件 (ResourceEfficiencyDTO)
- **返回**: `PageResult<ResourceEfficiency>`

### 1.2 获取规则详情
- **接口**: `GET /module/efficiency/rule/get/{id}`
- **描述**: 根据ID获取规则详情
- **路径参数**:
  - `id`: 规则ID
- **返回**: `ResultMsg<ResourceEfficiencyDTO>`

### 1.3 保存规则
- **接口**: `POST /module/efficiency/rule/save`
- **描述**: 新增或更新资源效能规则
- **请求体**: `ResourceEfficiencyDTO`
- **返回**: `ResultMsg<String>` (返回规则ID)

### 1.4 修改规则状态
- **接口**: `POST /module/efficiency/rule/status/{id}`
- **描述**: 启用或禁用规则
- **路径参数**:
  - `id`: 规则ID
- **查询参数**:
  - `status`: 状态值 (1-启用, 0-禁用)
- **返回**: `ResultMsg<String>`

### 1.5 删除规则
- **接口**: `POST /module/efficiency/rule/delete`
- **描述**: 删除规则（支持单个和批量删除）
- **请求体**: `List<String>` (规则ID列表)
- **返回**: `ResultMsg<String>`

### 1.6 验证规则名称
- **接口**: `GET /module/efficiency/rule/validate/name`
- **描述**: 验证规则名称是否重复
- **查询参数**:
  - `ruleName`: 规则名称
  - `excludeId`: 排除的规则ID (可选，用于编辑时验证)
- **返回**: `ResultMsg<Boolean>`

## 2. 资源指标数据管理 (ResourceIndexController)

**基础路径**: `/module/efficiency/index`

### 2.1 获取指标数据列表
- **接口**: `POST /module/efficiency/index/list`
- **描述**: 分页查询资源指标数据列表
- **请求参数**:
  - `pageQuery`: 分页参数
  - `index`: 指标查询条件 (ResourceIndexDTO)
- **返回**: `PageResult<ResourceIndexDTO>`

### 2.2 保存指标数据
- **接口**: `POST /module/efficiency/index/save`
- **描述**: 保存单个资源指标数据
- **请求体**: `ResourceIndexDTO`
- **返回**: `ResultMsg<String>` (返回指标数据ID)

### 2.3 批量保存指标数据
- **接口**: `POST /module/efficiency/index/batch/save`
- **描述**: 批量保存资源指标数据
- **请求体**: `List<ResourceIndexDTO>`
- **返回**: `ResultMsg<String>`

### 2.4 清理历史数据
- **接口**: `POST /module/efficiency/index/clean`
- **描述**: 清理指定时间点之前的历史数据
- **查询参数**:
  - `beforeTime`: 时间点 (格式: yyyy-MM-dd HH:mm:ss)
- **返回**: `ResultMsg<String>`

### 2.5 删除指标数据
- **接口**: `DELETE /module/efficiency/index/delete/{id}`
- **描述**: 删除指定的指标数据
- **路径参数**:
  - `id`: 指标数据ID
- **返回**: `ResultMsg<String>`

## 3. 资源能效结果管理 (ResourceResultController)

**基础路径**: `/module/efficiency/result`

### 3.1 获取结果列表
- **接口**: `POST /module/efficiency/result/list`
- **描述**: 分页查询资源能效结果列表
- **请求参数**:
  - `pageQuery`: 分页参数
  - `result`: 结果查询条件 (ResourceResultDTO)
- **返回**: `PageResult<ResourceResultDTO>`

### 3.2 获取能效统计报告
- **接口**: `GET /module/efficiency/result/report`
- **描述**: 获取指定时间范围内的能效统计报告
- **查询参数**:
  - `startDate`: 开始日期 (格式: yyyy-MM-dd)
  - `endDate`: 结束日期 (格式: yyyy-MM-dd)
- **返回**: `ResultMsg<Map<String, Object>>`

### 3.3 获取负荷资源列表
- **接口**: `GET /module/efficiency/result/load-resources`
- **描述**: 获取高负荷和低负荷资源列表
- **查询参数**:
  - `startDate`: 开始日期 (格式: yyyy-MM-dd)
  - `endDate`: 结束日期 (格式: yyyy-MM-dd)
- **返回**: `ResultMsg<Map<String, Object>>`
  ```json
  {
    "highLoadResources": [...],  // 高负荷资源列表
    "lowLoadResources": [...],   // 低负荷资源列表
    "highLoadCount": 10,         // 高负荷资源数量
    "lowLoadCount": 5            // 低负荷资源数量
  }
  ```

### 3.4 清理历史数据
- **接口**: `POST /module/efficiency/result/clean`
- **描述**: 清理指定日期之前的历史数据
- **查询参数**:
  - `beforeDate`: 日期点 (格式: yyyy-MM-dd)
- **返回**: `ResultMsg<String>`

## 数据模型

### ResourceEfficiencyDTO (资源效能规则)
```json
{
  "id": "规则ID",
  "ruleName": "规则名称",
  "cpuLowThreshold": "CPU低阈值",
  "cpuHighThreshold": "CPU高阈值", 
  "memoryLowThreshold": "内存低阈值",
  "memoryHighThreshold": "内存高阈值",
  "diskLowThreshold": "磁盘低阈值",
  "diskHighThreshold": "磁盘高阈值",
  "startTime": "开始时间",
  "endTime": "结束时间",
  "highRate": "高负荷比例",
  "lowRate": "低负荷比例",
  "serverGroups": "服务器组",
  "status": "状态 (1-启用, 0-禁用)"
}
```

### ResourceIndexDTO (资源指标数据)
```json
{
  "id": "指标ID",
  "ruleId": "规则ID",
  "serverGroup": "服务器组",
  "cInstId": "资源实例ID",
  "ciName": "资源名称",
  "ipAddress": "IP地址",
  "cpuRate": "CPU使用率",
  "memoryRate": "内存使用率", 
  "diskRate": "磁盘使用率",
  "receiveTime": "接收时间",
  "sendTime": "发送时间",
  "sameDayStatus": "同日状态",
  "applicationName": "应用名称",
  "collectionFreq": "采集频率",
  "ext1": "扩展字段1"
}
```

### ResourceResultDTO (资源能效结果)
```json
{
  "id": "结果ID",
  "relationId": "关联ID",
  "ruleId": "规则ID",
  "serverGroup": "服务器组",
  "cInstId": "资源实例ID",
  "ciName": "资源名称",
  "ipAddress": "IP地址",
  "applicationName": "应用名称",
  "applicationId": "应用ID",
  "cpuHighThreshold": "CPU高阈值状态",
  "cpuLowThreshold": "CPU低阈值状态",
  "memoryHighThreshold": "内存高阈值状态",
  "memoryLowThreshold": "内存低阈值状态",
  "diskHighThreshold": "磁盘高阈值状态",
  "diskLowThreshold": "磁盘低阈值状态",
  "cpuDays": "CPU天数",
  "memoryDays": "内存天数",
  "diskDays": "磁盘天数",
  "processDate": "处理日期"
}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": "响应数据"
}
```

### 分页响应
```json
{
  "rows": "数据列表",
  "total": "总记录数",
  "page": "当前页码",
  "size": "每页大小"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "code": "错误代码"
}
```

## 注意事项

1. **时间格式**: 
   - 日期时间: `yyyy-MM-dd HH:mm:ss`
   - 日期: `yyyy-MM-dd`

2. **状态值**:
   - 规则状态: 1-启用, 0-禁用
   - 阈值状态: "是"/"否"

3. **批量操作**:
   - 删除规则支持传入单个ID或ID数组
   - 批量保存支持传入对象数组

4. **权限控制**:
   - 所有接口都需要相应的权限验证
   - 删除和状态修改操作需要管理员权限

5. **数据验证**:
   - 规则名称不能重复
   - 阈值范围需要合理设置
   - 时间范围需要有效
