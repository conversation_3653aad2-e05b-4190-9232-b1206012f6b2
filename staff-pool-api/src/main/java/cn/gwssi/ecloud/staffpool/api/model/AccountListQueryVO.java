package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/*
 * 账号申请列表查询
 * <AUTHOR>
 */
@Data
@ApiModel(value = "账号申请列表", description="账号申请列表")
public class AccountListQueryVO implements Serializable {
    @ApiModelProperty(value="标题")
    private String title;
    @ApiModelProperty(value="申请人")
    private String applyPerson;
    @ApiModelProperty(value="申请时间,逗号隔开")
    private String entryDate;
    @ApiModelProperty(value="状态,逗号隔开 0-暂存 1-待审核 2-审核通过 3-审核不通过")
    private String status;
    @ApiModelProperty(value="页码")
    private Integer pageNo;
    @ApiModelProperty(value="每页数量")
    private Integer pageSize;
}
