package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModelProperty;

public class UompContractResourceVO {

    /**
     * 资源ID
     */
    @ApiModelProperty(value="资源ID")
    private String cInstId;

    /**
     * 资源名称
     */
    @ApiModelProperty(value="资源名称")
    private String ciName;

    /**
     * 品牌
     */
    @ApiModelProperty(value="品牌")
    private String brand;

    /**
     * 型号
     */
    @ApiModelProperty(value="型号")
    private String brandModel;

    /**
     * cpu架构
     */
    @ApiModelProperty(value="cpu架构")
    private String cpuFramework;

    /**
     * 用途
     */
    @ApiModelProperty(value="用途")
    private String useds;

    /**
     * 操作系统
     */
    @ApiModelProperty(value="操作系统")
    private String os;

    /**
     * 操作系统版本
     */
    @ApiModelProperty(value="操作系统版本")
    private String osVersion;

    /**
     * 所属机房
     */
    @ApiModelProperty(value="所属机房")
    private String machineRoom;

    /**
     * 所属机柜
     */
    @ApiModelProperty(value="所属机柜")
    private String cabinet;

    public String getcInstId() {
        return cInstId;
    }

    public void setcInstId(String cInstId) {
        this.cInstId = cInstId;
    }

    public String getCiName() {
        return ciName;
    }

    public void setCiName(String ciName) {
        this.ciName = ciName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getBrandModel() {
        return brandModel;
    }

    public void setBrandModel(String brandModel) {
        this.brandModel = brandModel;
    }

    public String getCpuFramework() {
        return cpuFramework;
    }

    public void setCpuFramework(String cpuFramework) {
        this.cpuFramework = cpuFramework;
    }

    public String getUseds() {
        return useds;
    }

    public void setUseds(String useds) {
        this.useds = useds;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getMachineRoom() {
        return machineRoom;
    }

    public void setMachineRoom(String machineRoom) {
        this.machineRoom = machineRoom;
    }

    public String getCabinet() {
        return cabinet;
    }

    public void setCabinet(String cabinet) {
        this.cabinet = cabinet;
    }
}