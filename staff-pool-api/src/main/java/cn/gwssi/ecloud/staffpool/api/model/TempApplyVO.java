package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description="临时入场列表查询接口")
public class TempApplyVO extends PageBaseVO implements Serializable {

    @ApiModelProperty(value = "姓名")
    private String personName;
    @ApiModelProperty(value = "接待人")
    private String acceptName;
    @ApiModelProperty(value = "就职公司")
    private String workingCompany;
    @ApiModelProperty(value = "到访时间段")
    private String realVisitTime;
    @ApiModelProperty(value="参与项目")
    private String involvedProject;
    @ApiModelProperty(value="备案状态 0:未备案, 1:已备案")
    private String filingStatus;
    @ApiModelProperty(value="审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过")
    private String applyStatus;

    @ApiModelProperty(value="申请id-人员信息总览使用")
    private String id;

    // 参与项目
    private List<String> involvedProjectList;
    // 就职公司
    private List<String> workingCompanyList;

    // 入场日期时间段
    private String realVisitTimeBegin;
    private String realVisitTimeEnd;

    // 是否供应商
    private String ifSupplier;
    private String orgId;

    private String supplierId;

    private String createdBy;

    // 过滤暂存状态的数据只有本人可以查看
    private String createdByStatus;
}
