package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
    * 服务报告信息表
    */
@ApiModel(value = "服务报告保存", description = "服务报告保存")
@Data
public class UompReportSave {

    /**
     * id
     */
    @ApiModelProperty(value="id (保存不传，更新必传)")
    private String id;

    /**
    * 报告名称
    */
    @ApiModelProperty(value="报告名称")
    @NotBlank(message = "报告名称不能为空！")
    private String reportName;

    /**
    * 报告编码
    */
    @ApiModelProperty(value="报告编码")
    @NotBlank(message = "报告编码不能为空！")
    private String reportCode;

    /**
    * 报告类型
    */
    @ApiModelProperty(value="报告类型 （1：周报 2：月报 3：年报）")
    @NotBlank(message = "报告类型不能为空！")
    private String reportType;

    /**
    * 报告开始时间
    */
    @ApiModelProperty(value="报告开始时间")
    private Date reportBegin;

    /**
    * 报告结束时间
    */
    @ApiModelProperty(value="报告结束时间")
    private Date reportEnd;

    /**
    * 应用系统id
    */
    @ApiModelProperty(value="应用系统id")
    private String applicationSystemManagementId;

    /**
    * 应用 系统名称
    */
    @ApiModelProperty(value="应用系统名称")
//    @NotBlank(message = "应用系统名称不能为空！")
    private String applicationSystemName;

    private String applicationSystem;

    /**
    * 服务商
    */
    @ApiModelProperty(value="服务商")
    @NotBlank(message = "服务商不能为空！")
    private String supplierName;

    /**
     * 上传文件列表
     */
    @ApiModelProperty(value="上传文件集合")
    private String fileListJson;
}