package cn.gwssi.ecloud.staffpool.api.service;

import cn.gwssi.ecloud.staffpool.api.model.MaskedData;

public interface IDataDesensitization {


    /**
     * 字符串加密
     * @param data
     * @return
     */
    String encrypt(String data);

    /**
     * 字符串解密
     * @param data
     * @return
     */
    String decrypt(String data);

    /**
     * 根据原始字符串和传入的参数截取部分内容加密
     * @param original
     * @param prefixLen
     * @param suffixLen
     * @return
     */
    MaskedData process(String original, int prefixLen, int suffixLen);

    /**
     * 字符串脱敏处理
     * @param original
     * @param prefixLen
     * @param suffixLen
     * @return
     */
    String desensitization(String original, int prefixLen, int suffixLen);




}
