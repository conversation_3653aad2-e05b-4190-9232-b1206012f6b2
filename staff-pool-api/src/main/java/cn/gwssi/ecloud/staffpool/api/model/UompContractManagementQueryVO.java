package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/*
 * 合同管理列表查询
 * <AUTHOR>
 */
@ApiModel(value = "合同管理列表查询", description="合同管理列表查询")
public class UompContractManagementQueryVO implements Serializable {
    @ApiModelProperty(value="编码")
    private String contractCode;
    @ApiModelProperty(value="名称")
    private String contractName;
    @ApiModelProperty(value="签订日期开始时间")
    private Date signingDateBegin;
    @ApiModelProperty(value="签订日期结束时间")
    private Date signingDateEnd;
    @ApiModelProperty(value="甲方")
    private String partyAname;
    @ApiModelProperty(value="乙方")
    private String partyBname;
    @ApiModelProperty(value="类型, 逗号隔开")
    private String contractType;
    @ApiModelProperty(value="状态，逗号隔开")
    private String contractStatus;
    @ApiModelProperty(value="服务商id")
    private String supplierManagementId;
    @ApiModelProperty(value="甲方负责人")
    private String partyAPrincipalName;
    @ApiModelProperty(value="乙方负责人")
    private String partyBPrincipalName;
    //@ApiModelProperty(value="自定义排序(key：排序字段  value:排序方式 desc、asc)")
    //private Map<String, String> sortMap = new HashMap<>();
    @ApiModelProperty(value="页码")
    private Integer pageNo;
    @ApiModelProperty(value="每页数量")
    private Integer pageSize;
    @ApiModelProperty(value="排序字段")
    private String sortField;
    @ApiModelProperty(value="排序方式")
    private String sortType;
    private Date qualityStartBeginDay;

    private Date qualityEndBeginDay;

    private Date qualityStartEndDay;
    private Date qualityEndEndDay;




    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public String getSupplierManagementId() {
        return supplierManagementId;
    }

    public void setSupplierManagementId(String supplierManagementId) {
        this.supplierManagementId = supplierManagementId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getPartyAname() {
        return partyAname;
    }

    public void setPartyAname(String partyAname) {
        this.partyAname = partyAname;
    }

    public String getPartyBname() {
        return partyBname;
    }

    public void setPartyBname(String partyBname) {
        this.partyBname = partyBname;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractStatus() {
        return contractStatus;
    }

    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getSigningDateBegin() {
        return signingDateBegin;
    }

    public void setSigningDateBegin(Date signingDateBegin) {
        this.signingDateBegin = signingDateBegin;
    }

    public Date getSigningDateEnd() {
        return signingDateEnd;
    }

    public void setSigningDateEnd(Date signingDateEnd) {
        this.signingDateEnd = signingDateEnd;
    }

    public String getPartyAPrincipalName() {
        return partyAPrincipalName;
    }

    public void setPartyAPrincipalName(String partyAPrincipalName) {
        this.partyAPrincipalName = partyAPrincipalName;
    }

    public String getPartyBPrincipalName() {
        return partyBPrincipalName;
    }

    public void setPartyBPrincipalName(String partyBPrincipalName) {
        this.partyBPrincipalName = partyBPrincipalName;
    }

    public Date getQualityStartBeginDay() {
        return qualityStartBeginDay;
    }

    public void setQualityStartBeginDay(Date qualityStartBeginDay) {
        this.qualityStartBeginDay = qualityStartBeginDay;
    }

    public Date getQualityEndBeginDay() {
        return qualityEndBeginDay;
    }

    public void setQualityEndBeginDay(Date qualityEndBeginDay) {
        this.qualityEndBeginDay = qualityEndBeginDay;
    }

    public Date getQualityStartEndDay() {
        return qualityStartEndDay;
    }

    public void setQualityStartEndDay(Date qualityStartEndDay) {
        this.qualityStartEndDay = qualityStartEndDay;
    }

    public Date getQualityEndEndDay() {
        return qualityEndEndDay;
    }

    public void setQualityEndEndDay(Date qualityEndEndDay) {
        this.qualityEndEndDay = qualityEndEndDay;
    }
}
