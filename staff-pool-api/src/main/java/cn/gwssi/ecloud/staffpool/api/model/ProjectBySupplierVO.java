package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description="入场申请-根据供应商收敛查询项目列表")
public class ProjectBySupplierVO implements Serializable {

    @ApiModelProperty(value="页数")
    private int pageNo;
    @ApiModelProperty(value="数量")
    private int pageSize;
    @ApiModelProperty(value="系统名称")
    private String projectName;
    @ApiModelProperty(value="供应商id")
    private String supplierId;
}
