package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/*
 * 权限申请列表查询
 * <AUTHOR>
 */
@ApiModel(value = "权限申请", description="权限申请")
public class PermissionListQueryVO implements Serializable {
    @ApiModelProperty(value="标题")
    private String applyTitle;
    @ApiModelProperty(value="申请人")
    private String applyUserName;
    @ApiModelProperty(value="申请时间")
    private String applyTime;
    @ApiModelProperty(value="状态 0-暂存 1-待审核 2-审核通过 3-审核不通过")
    private String applyStatus;
    @ApiModelProperty(value="页码")
    private Integer pageNo;
    @ApiModelProperty(value="每页数量")
    private Integer pageSize;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getApplyTitle() {
        return applyTitle;
    }

    public void setApplyTitle(String applyTitle) {
        this.applyTitle = applyTitle;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public void setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }
}
