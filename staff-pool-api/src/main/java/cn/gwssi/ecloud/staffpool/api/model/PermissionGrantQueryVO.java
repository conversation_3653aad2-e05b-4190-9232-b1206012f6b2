package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

/*
 * 堡垒机授权查询类
 * <AUTHOR>
 */
@ApiModel(description="堡垒机授权参数类")
public class PermissionGrantQueryVO implements Serializable {
    @ApiModelProperty(value="数组")
    private List<PermissionGrantBaseVO> list;

    public List<PermissionGrantBaseVO> getList() {
        return list;
    }

    public void setList(List<PermissionGrantBaseVO> list) {
        this.list = list;
    }
}
