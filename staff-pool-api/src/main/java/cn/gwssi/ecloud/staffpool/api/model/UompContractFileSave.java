package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel(value = "合同文件保存类")
public class UompContractFileSave {

    @ApiModelProperty(value="合同管理主键id")
    @NotBlank(message = "合同管理主键不能为空")
    private String contractManagementId;

    @ApiModelProperty(value="文件信息集合")
    private List<UompContractFileVO> fileInfos;

    public String getContractManagementId() {
        return contractManagementId;
    }

    public void setContractManagementId(String contractManagementId) {
        this.contractManagementId = contractManagementId;
    }

    public List<UompContractFileVO> getFileInfos() {
        return fileInfos;
    }

    public void setFileInfos(List<UompContractFileVO> fileInfos) {
        this.fileInfos = fileInfos;
    }
}