package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*
 * 同步堡垒机-通过监听接口委托同步查询
 * <AUTHOR>
 */
@ApiModel(description="同步堡垒机-通过监听接口委托同步查询")
@Data
public class PermissionRuleQueryVO implements Serializable {
    @ApiModelProperty(value="参数json")
    private String paramJson;
    @ApiModelProperty(value="授权账户")
    private String authAccount;
    @ApiModelProperty(value="授权密码")
    private String authPassword;
    @ApiModelProperty(value="授权url")
    private String authUrl;
    @ApiModelProperty(value="是否批量（1是0否）")
    private String ifBatch;
    @ApiModelProperty(value="服务地址")
    private String serviceUrl;
    @ApiModelProperty(value="匹配规则")
    private String matchRule;
    @ApiModelProperty(value="token")
    private String ssoToken;
    @ApiModelProperty(value="规则集合id")
    private List<String> ruleIdList;
}
