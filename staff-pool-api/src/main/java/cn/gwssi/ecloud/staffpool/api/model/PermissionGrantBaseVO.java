package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/*
 * 堡垒机授权查询类
 * <AUTHOR>
 */
@ApiModel(description="堡垒机授权对象类")
public class PermissionGrantBaseVO implements Serializable {
    @ApiModelProperty(value="授权用户ID，逗号隔开")
    private String userIds;
    @ApiModelProperty(value="申请权限类型：0-申请权限 1-删除权限")
    private String outApplyType;
    @ApiModelProperty(value="堡垒机资产ID，逗号隔开")
    private String empowerResourceIds;
    @ApiModelProperty(value="申请权限类型")
    private String permission;
    @ApiModelProperty(value="授权开始时间")
    private String empowerBeginTime;
    @ApiModelProperty(value="授权结束时间")
    private String empowerEndTime;
    @ApiModelProperty(value="堡垒机ID")
    private String fortId;
    @ApiModelProperty(value="主键id")
    private String id;

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    public String getOutApplyType() {
        return outApplyType;
    }

    public void setOutApplyType(String outApplyType) {
        this.outApplyType = outApplyType;
    }

    public String getEmpowerResourceIds() {
        return empowerResourceIds;
    }

    public void setEmpowerResourceIds(String empowerResourceIds) {
        this.empowerResourceIds = empowerResourceIds;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getEmpowerBeginTime() {
        return empowerBeginTime;
    }

    public void setEmpowerBeginTime(String empowerBeginTime) {
        this.empowerBeginTime = empowerBeginTime;
    }

    public String getEmpowerEndTime() {
        return empowerEndTime;
    }

    public void setEmpowerEndTime(String empowerEndTime) {
        this.empowerEndTime = empowerEndTime;
    }

    public String getFortId() {
        return fortId;
    }

    public void setFortId(String fortId) {
        this.fortId = fortId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
