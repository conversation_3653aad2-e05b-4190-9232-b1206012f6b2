package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description="现场管理驻场管理-驻场人员列表查询")
public class PersonInfoOverviewVO implements Serializable {

    @ApiModelProperty(value="页数")
    private int pageNo;
    @ApiModelProperty(value="数量")
    private int pageSize;
    @ApiModelProperty(value="申请id")
    private String id;
}
