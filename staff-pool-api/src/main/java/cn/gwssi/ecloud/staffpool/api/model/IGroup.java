//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.gwssi.ecloud.staffpool.api.model;

import java.io.Serializable;

public interface IGroup extends Serializable {
    String getGroupId();

    String getGroupName();

    String getGroupCode();

    String getGroupType();

    Integer getGroupLevel();

    String getParentId();

    String getRespName();

    String getOrgGroupId();

    Integer getSn();

    String getPath();

    default Integer getUserNum() {
        return null;
    }

    default String getSimple() {
        return null;
    }

    default String getParentName() {
        return null;
    }
}
