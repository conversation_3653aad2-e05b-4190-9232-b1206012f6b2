package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description="现场管理驻场管理-驻场人员列表查询")
public class PersonListQueryVO extends PageBaseVO implements Serializable {

    @ApiModelProperty(value="姓名")
    private String personName;
    @ApiModelProperty(value="所属运维组")
    private String maintenanceGroupId;
    @ApiModelProperty(value="工作岗位名称")
    private String postName;
    @ApiModelProperty(value="就职公司")
    private String workingCompany;
    @ApiModelProperty(value="参与系统")
    private String involvedProject;
    @ApiModelProperty(value="入场日期时间段")
    private String inTime;
    @ApiModelProperty(value="驻场状态 -1未驻场 0-驻场中 1-已退场")
    private String entryStatus;
    @ApiModelProperty(value="申请id-人员信息总览使用")
    private String id;
    @ApiModelProperty(value="运维组织")
    private String orgGroupId;
    private List<String> orgGroupIdList;

    // 审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过
    private String applyStatus;

    // 所属运维组
    private List<String> maintenanceGroupIdList;

    // 就职公司
    private List<String> workingCompanyList;

    // 入场日期时间段
    private String inTimeBegin;
    private String inTimeEnd;

    // 参与项目
    private List<String> involvedProjectList;

    // 是否供应商
    private String ifSupplier;
    private String supplierId;

    // 身份证号
    private String personCard;

    private String postId;

    private String createdBy;

    // 过滤暂存状态的数据只有本人可以查看
    private String createdByStatus;
}
