package cn.gwssi.ecloud.staffpool.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
    * 应用系统信息表
    */
@ApiModel(value = "应用系统保存", description="应用系统保存")
@Data
public class UompApplicationSystemManagementSave{
    /**
     * id
     */
    @ApiModelProperty(value="主键id （保存不传，更新必传）")
    private String id;

    /**
    * 应用系统名称
    */
    @ApiModelProperty(value="应用系统名称")
    @NotBlank(message = "应用系统名称不能为空！")
    private String applicationSystemName;

    /**
    * 运维部门id
    */
    @ApiModelProperty(value="运维部门id")
    private String departId;

    /**
    * 运维部门名称
    */
    @ApiModelProperty(value="运维部门名称")
    @NotBlank(message = "运维部门名称不能为空！")
    private String departName;

    /**
    * 负责人id
    */
    @ApiModelProperty(value="负责人名称")
    @NotBlank(message = "负责人名称不能为空！")
    private String principalName;

    /**
    * 负责人名称
    */
    @ApiModelProperty(value="负责人id")
    private String principalId;

    /**
    * 是否核心应用（1：是   0：否）
    */
    @ApiModelProperty(value="是否核心应用（1：是   0：否）")
    @NotBlank(message = "核心应用不能为空！")
    private String isHeart;

    /**
    * 上线时间
    */
    @ApiModelProperty(value="上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date onlineTime;

    /**
    * 状态（0：使用中  1已停用）
    */
    @ApiModelProperty(value="状态（0：使用中  1已停用）")
    @NotBlank(message = "状态不能为空！")
    private String systemStatus;

    /**
    * 服务商id
    */
    @ApiModelProperty(value="服务商id")
    private String supplierId;

    /**
    * 服务商名称
    */
    @ApiModelProperty(value="服务商名称")
    @NotBlank(message = "服务商名称不能为空！")
    private String supplierName;

    /**
    * 服务商负责人
    */
    @ApiModelProperty(value="服务商负责人")
    @NotBlank(message = "服务商负责人不能为空！")
    private String supplierDepartName;

    /**
    * 联系电话
    */
    @ApiModelProperty(value="联系电话")
    @NotBlank(message = "联系电话不能为空！")
    private String supplierTel;

    /**
     * 合同集合
     */
    @ApiModelProperty(value="合同id集合")
    private List<String> idList;
}
