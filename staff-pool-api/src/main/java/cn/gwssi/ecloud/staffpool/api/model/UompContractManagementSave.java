package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

@ApiModel(value = "合同管理保存", description = "合同管理保存")
@Data
public class UompContractManagementSave{

    @ApiModelProperty(value="主键id, 保存不传,更新传值")
    private String id;

    @ApiModelProperty(value="合同编码")
    @NotBlank(message = "合同编号不能为空")
    private String contractCode;

    @ApiModelProperty(value="名称")
    @NotBlank(message = "合同名称不能为空")
    private String contractName;

    @ApiModelProperty(value="签订日期")
    private Date signingDate;

    @ApiModelProperty(value="合同类型")
    private String contractType;

    @ApiModelProperty(value="合同状态")
    private String contractStatus;

    @ApiModelProperty(value="甲方名称")
    @NotBlank(message = "甲方名称不能为空")
    private String partyAName;

    @ApiModelProperty(value="甲方负责人名称")
    @NotBlank(message = "甲方负责人不能为空")
    private String partyAPrincipalName;

    @ApiModelProperty(value="乙方名称")
    @NotBlank(message = "乙方名称不能为空")
    private String partyBName;

    @ApiModelProperty(value="乙方id")
    private String partyBId;

    @ApiModelProperty(value="乙方负责人名称")
    @NotBlank(message = "乙方负责人不能为空")
    private String partyBPrincipalName;

    @ApiModelProperty(value="丙方名称")
    private String partyCName;

    @ApiModelProperty(value="丙方负责人名称")
    private String partyCPrincipalName;

    @ApiModelProperty(value="合同内容")
    private String contractContent;

    @ApiModelProperty(value="应用系统id")
    private String projectManagementId;

    @ApiModelProperty(value="应用系统名称")
    private String projectManagementName;

    @ApiModelProperty(value="责任部门id")
    private String dutyDepartId;

    @ApiModelProperty(value="责任部门名称")
    private String dutyDepartName;

    @ApiModelProperty(value="付款日期")
    private Date payTime;

    @ApiModelProperty(value="付款情况")
    private String payStatus;

    @ApiModelProperty(value="服务等级协议id")
    //@NotBlank(message = "服务等级协议id不能为空")
    private String serviceLevelId;

    @ApiModelProperty(value="服务等级协议名称")
    //@NotBlank(message = "服务等级协议名称不能为空")
    private String serviceLevelName;

    @ApiModelProperty(value="质保/维保开始日")
    private Date qualityBeginDay;

    @ApiModelProperty(value="质保/维保结束日")
    private Date qualityEndDay;

    @ApiModelProperty(value="质保内容")
    private String qualityContent;

    @ApiModelProperty(value="甲方负责人电话")
    private String partyAPrincipalTel;

    @ApiModelProperty(value="甲方项目经理")
    private String partyAManager;

    @ApiModelProperty(value="甲方项目经理电话")
    private String partyAManagerTel;

    @ApiModelProperty(value="乙方负责人电话")
    private String partyBPrincipalTel;

    @ApiModelProperty(value="乙方项目经理")
    private String partyBManager;

    @ApiModelProperty(value="乙方项目经理电话")
    private String partyBManagerTel;

    @ApiModelProperty(value="销售经理")
    private String salesManager;

    @ApiModelProperty(value="售前经理")
    private String preSalesManager;

    @ApiModelProperty(value="丙方联系人")
    private String partyCPerson;

    @ApiModelProperty(value="丙方联系人电话")
    private String partyCPersonTel;

    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="是否生效")
    private String isEffect;

    private List<UompPartyInfoVo> partyInfoList;

    private Float contractAmount;
}