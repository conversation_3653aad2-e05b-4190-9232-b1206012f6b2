package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description="入场申请-校验入场人员是否反复参与同一个项目")
public class CheckPersonProjectVO implements Serializable {

    @ApiModelProperty(value="身份证号")
    private String personCard;
    @ApiModelProperty(value="参与系统id")
    private String engagementProjectId;
}
