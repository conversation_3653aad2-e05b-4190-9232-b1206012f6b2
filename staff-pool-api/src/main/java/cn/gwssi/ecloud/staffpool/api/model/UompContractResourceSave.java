package cn.gwssi.ecloud.staffpool.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel(value = "合同关联资源保存类")
public class UompContractResourceSave {

    @ApiModelProperty(value="合同管理主键id")
    @NotBlank(message = "合同管理主键不能为空")
    private String contractManagementId;

    @ApiModelProperty(value="关联资源信息集合")
    private List<UompContractResourceVO> resourceInfos;

    public String getContractManagementId() {
        return contractManagementId;
    }

    public void setContractManagementId(String contractManagementId) {
        this.contractManagementId = contractManagementId;
    }

    public List<UompContractResourceVO> getResourceInfos() {
        return resourceInfos;
    }

    public void setResourceInfos(List<UompContractResourceVO> resourceInfos) {
        this.resourceInfos = resourceInfos;
    }
}