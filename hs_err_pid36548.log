#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 499122176 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=36548, tid=28852
#
# JRE version:  (17.0.9+7) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://code.e.cloud:9080': 

Host: AMD Ryzen 7 7840HS w/ Radeon 780M Graphics     , 16 cores, 29G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
Time: Thu May 23 19:49:16 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.3235) elapsed time: 0.019694 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000282ec30bb80):  JavaThread "Unknown thread" [_thread_in_vm, id=28852, stack(0x000000fd8e400000,0x000000fd8e500000)]

Stack: [0x000000fd8e400000,0x000000fd8e500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6886e9]
V  [jvm.dll+0x841e4a]
V  [jvm.dll+0x843a8e]
V  [jvm.dll+0x8440f3]
V  [jvm.dll+0x24c14f]
V  [jvm.dll+0x685499]
V  [jvm.dll+0x679c2a]
V  [jvm.dll+0x30cf9b]
V  [jvm.dll+0x314446]
V  [jvm.dll+0x36425e]
V  [jvm.dll+0x36448f]
V  [jvm.dll+0x2e2d68]
V  [jvm.dll+0x2e3cd4]
V  [jvm.dll+0x8129f1]
V  [jvm.dll+0x3720c1]
V  [jvm.dll+0x7f152c]
V  [jvm.dll+0x3f5d4f]
V  [jvm.dll+0x3f7981]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffa8c5cef18, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x00000282ec3af9e0 GCTaskThread "GC Thread#0" [stack: 0x000000fd8e500000,0x000000fd8e600000] [id=20708]
  0x00000282ff3e3bf0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000fd8e600000,0x000000fd8e700000] [id=26932]
  0x00000282ec3bbb50 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000fd8e700000,0x000000fd8e800000] [id=36160]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa8bd83937]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000282ec3064b0] Heap_lock - owner thread: 0x00000282ec30bb80

Heap address: 0x0000000624c00000, size: 7604 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000624c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x0000028280ee0000,0x0000028281dc0000] _byte_map_base: 0x000002827ddba000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000282ec3b0000, (CMBitMap*) 0x00000282ec3b0040
 Prev Bits: [0x0000028282ca0000, 0x000002828a370000)
 Next Bits: [0x000002828a370000, 0x0000028291a40000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7c6d10000 - 0x00007ff7c6d1a000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.exe
0x00007ffad1270000 - 0x00007ffad1486000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffacfb30000 - 0x00007ffacfbf4000 	C:\Windows\System32\KERNEL32.DLL
0x00007fface870000 - 0x00007ffacec16000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffacec90000 - 0x00007ffaceda1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffab8bc0000 - 0x00007ffab8bdb000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\VCRUNTIME140.dll
0x00007ffa8cea0000 - 0x00007ffa8ceb7000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jli.dll
0x00007ffad0cf0000 - 0x00007ffad0e9d000 	C:\Windows\System32\USER32.dll
0x00007fface720000 - 0x00007fface746000 	C:\Windows\System32\win32u.dll
0x00007ffab1f10000 - 0x00007ffab21a3000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98\COMCTL32.dll
0x00007ffacf890000 - 0x00007ffacf8b9000 	C:\Windows\System32\GDI32.dll
0x00007ffacf9a0000 - 0x00007ffacfa47000 	C:\Windows\System32\msvcrt.dll
0x00007fface750000 - 0x00007fface868000 	C:\Windows\System32\gdi32full.dll
0x00007ffacedb0000 - 0x00007ffacee4a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffacfd00000 - 0x00007ffacfd31000 	C:\Windows\System32\IMM32.DLL
0x00007ffab8bb0000 - 0x00007ffab8bbc000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\vcruntime140_1.dll
0x00007ffa5fdd0000 - 0x00007ffa5fe5d000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\msvcp140.dll
0x00007ffa8ba90000 - 0x00007ffa8c713000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server\jvm.dll
0x00007ffacf8c0000 - 0x00007ffacf972000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffacfd40000 - 0x00007ffacfde8000 	C:\Windows\System32\sechost.dll
0x00007fface6f0000 - 0x00007fface718000 	C:\Windows\System32\bcrypt.dll
0x00007ffad1060000 - 0x00007ffad1175000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa8a9a0000 - 0x00007ffa8a9a9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffacf800000 - 0x00007ffacf871000 	C:\Windows\System32\WS2_32.dll
0x00007ffac59e0000 - 0x00007ffac59ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffacd400000 - 0x00007ffacd44d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffaca910000 - 0x00007ffaca944000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffacd3e0000 - 0x00007ffacd3f3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffacd6b0000 - 0x00007ffacd6c8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffac51f0000 - 0x00007ffac51fa000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\jimage.dll
0x00007ffacbc00000 - 0x00007ffacbe33000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffacf470000 - 0x00007ffacf7f8000 	C:\Windows\System32\combase.dll
0x00007ffad09d0000 - 0x00007ffad0aa7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffaca8d0000 - 0x00007ffaca902000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fface670000 - 0x00007fface6ea000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffaa47d0000 - 0x00007ffaa47f5000 	C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3235_none_270f74e173860f98;C:\Users\<USER>\AppData\Local\Programs\IntelliJ IDEA Ultimate\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://code.e.cloud:9080': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/Programs/IntelliJ IDEA Ultimate/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 499122176                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 7973371904                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 7973371904                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\app\Kingbase\ES\V8\KESRealPro\V008R006C008B0014\Interface\compress\v8r6_compress_win\kb_x64\release;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\Tailscale\;D:\app\Bandizip\;C:\Program Files\dotnet\;E:\apache-maven-3.8.12\bin;C:\Program Files\Rancher Desktop\resources\resources\win32\bin\;C:\Program Files\Rancher Desktop\resources\resources\linux\bin\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\Java\jre1.8.0_291\bin; ;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\app\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts
USERNAME=hupo
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3235)
OS uptime: 0 days 10:38 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv
Processor Information for processor 0
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 1
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 2
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 3
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 4
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 5
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 6
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 7
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 8
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 9
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 10
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 11
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 12
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 13
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 14
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 15
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801

Memory: 4k page, system-wide physical 30404M (6420M free)
TotalPageFile size 81604M (AvailPageFile size 226M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 71M, peak: 547M

vm_info: OpenJDK 64-Bit Server VM (17.0.9+7-b1000.46) for windows-amd64 JRE (17.0.9+7-b1000.46), built on 2023-10-27 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
