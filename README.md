# 系统能效模块 (Efficiency Module)

## 概述
系统能效模块是基于 Spring Boot 框架开发的资源效能管理系统，用于监控和分析系统资源的使用效率，提供资源优化建议。

## 模块架构

### 项目结构
```
ecloud-module-efficiency/
├── efficiency-api/          # API 模块 - 定义 DTO、常量、枚举
│   └── src/main/java/cn/gwssi/ecloudframework/module/efficiency/api/
│       ├── constant/         # 常量和枚举定义
│       └── model/           # DTO 类
├── efficiency-core/         # 核心模块 - 业务逻辑实现
│   └── src/main/java/cn/gwssi/ecloudframework/module/efficiency/
│       ├── core/
│       │   ├── dao/         # 数据访问层
│       │   ├── manager/     # 业务管理层
│       │   ├── model/       # 实体类
│       │   ├── task/        # 定时任务
│       │   └── util/        # 工具类
│       └── rest/controller/ # REST 控制器
│   └── src/main/resources/
│       ├── cn/gwssi/ecloudframework/module/efficiency/mapping/ # MyBatis 映射文件
│       └── sql/             # 数据库脚本
├── docs/                    # API 文档
└── README.md               # 项目说明
```

### 技术栈
- **框架**: Spring Boot
- **ORM**: MyBatis
- **数据库**: MySQL
- **工具**: Lombok, Jackson
- **分页**: PageHelper

## 核心功能

### 1. 资源效能规则管理
- 创建和管理资源效能监控规则
- 配置 CPU、内存、磁盘的高低阈值
- 设置有效业务时间段
- 定义负荷阈值累计占比

### 2. 资源指标数据管理
- 收集和存储资源使用指标数据
- 支持批量数据导入
- 提供数据查询和统计功能
- 自动清理历史数据

### 3. 资源能效结果分析
- 基于规则自动分析资源使用效率
- 生成高负荷、低负荷、正常状态的分析结果
- 提供优化建议和操作推荐
- 支持能效报告生成

### 4. 规则服务器关系管理
- 管理效能规则与服务器资源的关联关系
- 支持按服务器群组批量关联
- 提供关系同步功能

## 数据库表结构

### 1. s_resource_efficiency (资源效能规则表)
存储资源效能监控规则的配置信息。

### 2. s_resource_index (资源指标数据表)
存储系统资源的实时使用指标数据。

### 3. s_resource_result (资源能效结果表)
存储基于规则分析得出的资源能效结果。

### 4. s_rule_server_relation (规则服务器关系表)
存储效能规则与服务器资源的关联关系。

## API 接口

### 主要控制器
1. **ResourceEfficiencyController** - 资源效能规则管理
2. **ResourceIndexController** - 资源指标数据管理
3. **ResourceResultController** - 资源能效结果管理
4. **RuleServerRelationController** - 规则服务器关系管理

详细的 API 文档请参考 `docs/` 目录下的各个文档文件。

## 定时任务

### EfficiencyTaskScheduler
提供以下定时任务功能（需要配置定时任务调度）：

1. **processEfficiencyData()** - 处理资源能效数据
   - 建议每日凌晨执行
   - 分析前一天的资源使用数据
   - 生成能效分析结果

2. **cleanHistoryData()** - 清理历史数据
   - 建议每周执行一次
   - 清理30天前的指标数据
   - 清理90天前的结果数据

3. **checkRuleStatus()** - 检查规则状态
   - 建议每小时执行一次
   - 验证规则配置有效性
   - 检查规则关联的服务器

## 使用说明

### 1. 创建效能规则
```http
POST /module/efficiency/rule/save
Content-Type: application/json

{
  "ruleName": "生产环境规则",
  "cpuLowThreshold": 10,
  "cpuHighThreshold": 80,
  "memoryLowThreshold": 10,
  "memoryHighThreshold": 85,
  "diskLowThreshold": 10,
  "diskHighThreshold": 90,
  "startTime": "09:00",
  "endTime": "18:00",
  "highRate": 30,
  "lowRate": 50,
  "serverGroupList": ["group1", "group2"]
}
```

### 2. 关联服务器到规则
```http
POST /module/efficiency/relation/associate/{ruleId}
Content-Type: application/json

["server001", "server002", "server003"]
```

### 3. 批量导入指标数据
```http
POST /module/efficiency/index/batch/save
Content-Type: application/json

[
  {
    "ruleId": "rule001",
    "cInstId": "server001",
    "ciName": "服务器01",
    "cpuRate": 75.5,
    "memoryRate": 68.2,
    "diskRate": 45.8,
    "receiveTime": "2024-01-01 10:00:00"
  }
]
```

### 4. 查看能效分析结果
```http
GET /module/efficiency/result/report/{ruleId}?startDate=2024-01-01&endDate=2024-01-31
```

## 配置说明

### 1. 数据库配置
确保数据库中已创建相关表结构，执行 `efficiency-core/src/main/resources/sql/系统能效.sql` 脚本。

### 2. 定时任务配置
需要配置定时任务来调用 `EfficiencyTaskScheduler` 中的方法：
- 使用 Spring 的 `@Scheduled` 注解
- 或集成 XXL-Job 等定时任务框架

### 3. 依赖配置
在主项目的 `pom.xml` 中添加模块依赖：
```xml
<dependency>
    <groupId>cn.gwssi.ecloud</groupId>
    <artifactId>efficiency-api</artifactId>
    <version>0.1-SNAPSHOT</version>
</dependency>
<dependency>
    <groupId>cn.gwssi.ecloud</groupId>
    <artifactId>efficiency-core</artifactId>
    <version>0.1-SNAPSHOT</version>
</dependency>
```

## 注意事项

1. **数据量管理**: 指标数据表可能增长很快，建议定期清理历史数据
2. **性能优化**: 对于大量数据的查询，建议添加适当的数据库索引
3. **定时任务**: 确保定时任务的执行时间不会与业务高峰期冲突
4. **异常处理**: 所有 Controller 方法都使用了 `@CatchErr` 注解进行异常处理
5. **批量操作**: 优先使用批量操作方法，避免在循环中操作数据库

## 扩展开发

### 添加新的分析维度
1. 在 `EfficiencyConstants` 中添加新的枚举类型
2. 在相关 DTO 和 Model 中添加新字段
3. 更新 MyBatis 映射文件
4. 在 Manager 实现类中添加相应的业务逻辑

### 集成外部监控系统
1. 创建新的数据采集接口
2. 实现数据格式转换逻辑
3. 调用批量保存接口导入数据

## 版本信息
- 版本: 0.1-SNAPSHOT
- 开发框架: Spring Boot
- 参考模块: inspection 模块
