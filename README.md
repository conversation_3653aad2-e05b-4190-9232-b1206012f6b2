[staffpool-api](staff-pool-api)
用于对外暴露 service 服务接口. 用于其他 module 其他模块依赖, 如果不存在需要对其他 module 提供 service 服务时,则不需要在该模块下编写相关功能.

依赖方式为:
<dependency>
    <groupId>cn.gwssi.ecloud</groupId>
    <artifactId>staffpool-api</artifactId>
</dependency>




[staffpool-core](staff-pool-core)
用于提供用户库功能.  用于其他项目或者服务需要使用 用户库功能时 集成该依赖,从而实现对应功能.
<dependency>
    <groupId>cn.gwssi.ecloud</groupId>
    <artifactId>staffpool-core</artifactId>
</dependency>


具体使用方式参考: 代码结构,层级,逻辑 参考智能手环项目
http://code.e.cloud:9080/ecloud/module/ecloud-module-bracelet


## 2024-07-04 驻场申请添加性别字段信息:
```sql
ALTER TABLE gwuomp_dev.uomp_admission_person ADD SEX varchar(100) CHARACTER SET gbk COLLATE gbk_chinese_ci DEFAULT '0' NULL COMMENT '0: 男,  1:女';
```
