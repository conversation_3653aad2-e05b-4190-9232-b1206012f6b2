# Staff-Pool 运维人员画像管理模块接口设计

## 概述

Staff-Pool模块是一个运维场景中的人员画像需求系统，主要用于运维团队风险分析、人员画像管理和风险模型配置。系统支持甲方运维负责人快速掌握团队现状、精确的风险管理和数据驱动的决策制定。

## 基础路径
```
/module/staff-pool
```

## 1. 运维团队风险分析 (TeamRiskController)

**基础路径**: `/module/staff-pool/team-risk`

### 1.1 团队风险分布
- **接口**: `GET /module/staff-pool/team-risk/distribution`
- **描述**: 获取团队风险分布统计，以饼图形式展示高中低风险占比
- **请求参数**: 无
- **返回**: `ResultMsg<RiskDistributionVO>`
- **实现**: 调用TeamRiskManager.getRiskDistribution()方法，统计高中低风险人员数量和占比

### 1.3 风险等级统计分布
- **接口**: `GET /module/staff-pool/team-risk/risk-level-statistics`
- **描述**: 获取指定风险等级的详细统计分布，包含环形图和柱状图数据
- **请求参数**: `riskLevel` - 风险等级（HIGH/MEDIUM/LOW）
- **返回**: `ResultMsg<RiskLevelStatisticsVO>`
- **实现**: 调用TeamRiskManager.getRiskLevelStatistics()方法，根据风险等级返回对应的统计数据

### 1.4 风险等级人员清单
- **接口**: `POST /module/staff-pool/team-risk/risk-level-personnel`
- **描述**: 获取指定风险等级的人员清单列表
- **请求参数**:
  - `PageQuery` - 分页参数
  - `riskLevel` - 风险等级（HIGH/MEDIUM/LOW）
- **返回**: `PageResult<RiskPersonnelVO>`
- **实现**: 调用TeamRiskManager.getRiskLevelPersonnel()方法，根据风险等级返回对应人员清单

## 2. 风险判定处置 (RiskAssessmentController)

**基础路径**: `/module/staff-pool/risk-assessment`

### 2.1 获取人员风险详情
- **接口**: `GET /module/staff-pool/risk-assessment/personnel/{personnelId}`
- **描述**: 获取指定人员的风险判定处置详情
- **路径参数**: `personnelId` - 人员ID
- **返回**: `ResultMsg<PersonnelRiskDetailVO>`
- **实现**: 调用RiskAssessmentManager.getPersonnelRiskDetail()方法

### 2.2 风险判定处置
- **接口**: `POST /module/staff-pool/risk-assessment/dispose`
- **描述**: 对人员风险进行判定和处置
- **请求参数**: `RiskDisposalDTO` - 风险处置信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用RiskAssessmentManager.disposeRisk()方法，支持风险调整、处置理由、附件上传

### 2.3 获取历史判定记录
- **接口**: `POST /module/staff-pool/risk-assessment/history/{personnelId}`
- **描述**: 获取人员历史风险判定记录
- **路径参数**: `personnelId` - 人员ID
- **请求参数**: `PageQuery` - 分页参数
- **返回**: `PageResult<RiskHistoryVO>`
- **实现**: 调用RiskAssessmentManager.getRiskHistory()方法

## 3. 风险模型管理 (RiskModelController)

**基础路径**: `/module/staff-pool/risk-model`

### 3.1 风险指标列表
- **接口**: `POST /module/staff-pool/risk-model/indicators/list`
- **描述**: 分页查询风险指标配置列表
- **请求参数**: `RiskIndicatorQueryDTO` - 查询条件（风险指标、风险等级、风险类型）
- **返回**: `PageResult<RiskIndicatorVO>`
- **实现**: 调用RiskModelManager.getRiskIndicatorList()方法

### 3.2 创建风险指标
- **接口**: `POST /module/staff-pool/risk-model/indicators/create`
- **描述**: 新建风险指标配置
- **请求参数**: `RiskIndicatorDTO` - 风险指标信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用RiskModelManager.createRiskIndicator()方法

### 3.3 编辑风险指标
- **接口**: `PUT /module/staff-pool/risk-model/indicators/{indicatorId}`
- **描述**: 编辑风险指标配置
- **路径参数**: `indicatorId` - 指标ID
- **请求参数**: `RiskIndicatorDTO` - 风险指标信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用RiskModelManager.updateRiskIndicator()方法

### 3.4 删除风险指标
- **接口**: `DELETE /module/staff-pool/risk-model/indicators`
- **描述**: 批量删除风险指标（仅禁用状态可删除）
- **请求参数**: `List<String>` - 指标ID列表
- **返回**: `ResultMsg<String>`
- **实现**: 调用RiskModelManager.deleteRiskIndicators()方法

### 3.5 启用/禁用风险指标
- **接口**: `PUT /module/staff-pool/risk-model/indicators/{indicatorId}/status`
- **描述**: 启用或禁用风险指标
- **路径参数**: `indicatorId` - 指标ID
- **请求参数**: `StatusUpdateDTO` - 状态信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用RiskModelManager.updateIndicatorStatus()方法

## 4. 运维岗位职责管理 (PositionController)

**基础路径**: `/module/staff-pool/position`

### 4.1 岗位列表
- **接口**: `POST /module/staff-pool/position/list`
- **描述**: 分页查询运维岗位列表
- **请求参数**: `PositionQueryDTO` - 查询条件
- **返回**: `PageResult<PositionVO>`
- **实现**: 调用PositionManager.getPositionList()方法

### 4.2 新建岗位
- **接口**: `POST /module/staff-pool/position/create`
- **描述**: 新建运维岗位
- **请求参数**: `PositionDTO` - 岗位信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用PositionManager.createPosition()方法

### 4.3 编辑岗位
- **接口**: `PUT /module/staff-pool/position/{positionId}`
- **描述**: 编辑运维岗位信息
- **路径参数**: `positionId` - 岗位ID
- **请求参数**: `PositionDTO` - 岗位信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用PositionManager.updatePosition()方法

### 4.4 删除岗位
- **接口**: `DELETE /module/staff-pool/position`
- **描述**: 批量删除运维岗位
- **请求参数**: `List<String>` - 岗位ID列表
- **返回**: `ResultMsg<String>`
- **实现**: 调用PositionManager.deletePositions()方法

## 数据模型说明

### 核心实体
1. **RiskIndicator** - 风险指标配置
2. **RiskAssessment** - 风险评估记录
3. **Position** - 运维岗位信息
4. **TeamRisk** - 团队风险统计

### 数据库表结构
1. **risk_indicator** - 风险指标配置表
2. **risk_assessment** - 风险评估记录表
3. **position_responsibility** - 岗位职责表
4. **team_risk_statistics** - 团队风险统计表

## 权限控制

### 角色权限
1. **甲方运维负责人**:
   - 查看运维组织视图、团队风险分析（风险分析总览、团队风险清单）
   - 操作风险判定/处置、更新人员信息

2. **服务商运维负责人**:
   - 查看团队风险分析（团队风险清单）
   - 操作更新人员信息

3. **总包商运维经理**:
   - 查看运维组织视图、团队风险分析（风险分析总览、风险模型配置、团队风险清单）
   - 操作风险模型配置

## 实现要点

1. **风险分析模型**: 基于人员多维度信息进行风险评估，支持动态配置风险指标
2. **人员画像构建**: 结构化展示人员特征，支持综合评价和风险识别
3. **数据实时更新**: 风险数据变化时实时刷新，无需手动刷新页面
4. **权限分级管理**: 不同角色具有不同的查看和操作权限
5. **模型配置灵活性**: 支持风险指标的动态配置和调整
