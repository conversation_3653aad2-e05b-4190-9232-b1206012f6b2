# Staff-Pool 运维人员画像管理模块接口设计

## 概述

Staff-Pool模块是一个运维场景中的人员画像需求系统，主要用于运维团队风险分析、人员画像管理和风险模型配置。系统支持甲方运维负责人快速掌握团队现状、精确的风险管理和数据驱动的决策制定。

## 基础路径
```
/module/staff-pool
```

## 1. 运维团队风险分析 (TeamRiskController)

**基础路径**: `/module/staff-pool/team-risk`

### 1.1 团队风险分布
- **接口**: `GET /module/staff-pool/team-risk/distribution`
- **描述**: 获取团队风险分布统计，以饼图形式展示高中低风险占比
- **请求参数**: 无
- **返回**: `ResultMsg<RiskDistributionVO>`
- **实现**: 调用TeamRiskManager.getRiskDistribution()方法，统计高中低风险人员数量和占比

### 1.2 团队风险分析总览
- **接口**: `GET /module/staff-pool/team-risk/overview`
- **描述**: 获取团队风险概况和主要风险领域总结
- **请求参数**: 无
- **返回**: `ResultMsg<RiskOverviewVO>`
- **实现**: 调用TeamRiskManager.getRiskOverview()方法，提供风险概况和主要风险分布

### 1.3 高风险统计分布
- **接口**: `GET /module/staff-pool/team-risk/high-risk-statistics`
- **描述**: 获取高风险详细统计，包含环形图和柱状图数据
- **请求参数**: 无
- **返回**: `ResultMsg<HighRiskStatisticsVO>`
- **实现**: 调用TeamRiskManager.getHighRiskStatistics()方法

### 1.4 高风险人员清单
- **接口**: `POST /module/staff-pool/team-risk/high-risk-personnel`
- **描述**: 获取高风险人员清单列表
- **请求参数**: `PageQuery` - 分页参数
- **返回**: `PageResult<HighRiskPersonnelVO>`
- **实现**: 调用TeamRiskManager.getHighRiskPersonnel()方法

### 1.5 中风险统计分布
- **接口**: `GET /module/staff-pool/team-risk/medium-risk-statistics`
- **描述**: 获取中风险详细统计分布
- **请求参数**: 无
- **返回**: `ResultMsg<MediumRiskStatisticsVO>`
- **实现**: 调用TeamRiskManager.getMediumRiskStatistics()方法

### 1.6 中风险人员清单
- **接口**: `POST /module/staff-pool/team-risk/medium-risk-personnel`
- **描述**: 获取中风险人员清单列表
- **请求参数**: `PageQuery` - 分页参数
- **返回**: `PageResult<MediumRiskPersonnelVO>`
- **实现**: 调用TeamRiskManager.getMediumRiskPersonnel()方法

## 2. 人员技能管理 (StaffSkillController)

**基础路径**: `/module/staff-pool/skill`

### 2.1 获取技能列表
- **接口**: `POST /module/staff-pool/skill/list`
- **描述**: 分页查询人员技能信息
- **请求参数**:
  - `pageQuery`: 分页参数
  - `skillQuery`: 技能查询条件 (StaffSkillDTO)
- **返回**: `PageResult<StaffSkill>`
- **实现**: 调用StaffSkillManager.list()方法

### 2.2 保存人员技能
- **接口**: `POST /module/staff-pool/skill/save`
- **描述**: 保存人员技能信息
- **请求参数**: `StaffSkillDTO` - 技能信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用StaffSkillManager.saveOrUpdate()方法

### 2.3 获取技能标签字典
- **接口**: `GET /module/staff-pool/skill/tags`
- **描述**: 获取所有可用的技能标签
- **返回**: `ResultMsg<List<SkillTag>>`
- **实现**: 调用SkillTagManager.getAllTags()方法

## 3. 人员可用性管理 (StaffAvailabilityController)

**基础路径**: `/module/staff-pool/availability`

### 3.1 获取人员可用性
- **接口**: `POST /module/staff-pool/availability/list`
- **描述**: 查询人员可用性状态
- **请求参数**:
  - `pageQuery`: 分页参数
  - `availabilityQuery`: 可用性查询条件
- **返回**: `PageResult<StaffAvailability>`
- **实现**: 调用StaffAvailabilityManager.list()方法

### 3.2 更新人员状态
- **接口**: `POST /module/staff-pool/availability/updateStatus`
- **描述**: 更新人员工作状态
- **请求参数**: `StaffStatusDTO` - 状态信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用StaffAvailabilityManager.updateStatus()方法

### 3.3 获取可用人员
- **接口**: `POST /module/staff-pool/availability/available`
- **描述**: 根据条件查询可用人员
- **请求参数**: `AvailabilityQueryDTO` - 查询条件
- **返回**: `ResultMsg<List<StaffInfo>>`
- **实现**: 调用StaffAvailabilityManager.getAvailableStaff()方法

## 4. 人员分配管理 (StaffAllocationController)

**基础路径**: `/module/staff-pool/allocation`

### 4.1 智能人员匹配
- **接口**: `POST /module/staff-pool/allocation/match`
- **描述**: 根据项目需求智能匹配合适人员
- **请求参数**: `ProjectRequirementDTO` - 项目需求
- **返回**: `ResultMsg<List<StaffMatchResult>>`
- **实现**: 调用StaffAllocationManager.matchStaff()方法，基于技能、可用性、历史表现等因素进行匹配

### 4.2 创建人员分配
- **接口**: `POST /module/staff-pool/allocation/create`
- **描述**: 创建人员分配记录
- **请求参数**: `StaffAllocationDTO` - 分配信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用StaffAllocationManager.createAllocation()方法

### 4.3 获取分配历史
- **接口**: `POST /module/staff-pool/allocation/history`
- **描述**: 查询人员分配历史
- **请求参数**:
  - `pageQuery`: 分页参数
  - `allocationQuery`: 分配查询条件
- **返回**: `PageResult<StaffAllocation>`
- **实现**: 调用StaffAllocationManager.getAllocationHistory()方法

### 4.4 结束分配
- **接口**: `POST /module/staff-pool/allocation/finish`
- **描述**: 结束人员分配
- **请求参数**: `AllocationFinishDTO` - 结束分配信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用StaffAllocationManager.finishAllocation()方法

## 5. 人员绩效管理 (StaffPerformanceController)

**基础路径**: `/module/staff-pool/performance`

### 5.1 获取绩效列表
- **接口**: `POST /module/staff-pool/performance/list`
- **描述**: 查询人员绩效记录
- **请求参数**:
  - `pageQuery`: 分页参数
  - `performanceQuery`: 绩效查询条件
- **返回**: `PageResult<StaffPerformance>`
- **实现**: 调用StaffPerformanceManager.list()方法

### 5.2 保存绩效评价
- **接口**: `POST /module/staff-pool/performance/save`
- **描述**: 保存人员绩效评价
- **请求参数**: `StaffPerformanceDTO` - 绩效信息
- **返回**: `ResultMsg<String>`
- **实现**: 调用StaffPerformanceManager.savePerformance()方法

### 5.3 获取绩效统计
- **接口**: `GET /module/staff-pool/performance/statistics/{staffId}`
- **描述**: 获取人员绩效统计信息
- **路径参数**: `staffId` - 人员ID
- **返回**: `ResultMsg<PerformanceStatistics>`
- **实现**: 调用StaffPerformanceManager.getStatistics()方法

## 6. 人员资源统计 (StaffStatisticsController)

**基础路径**: `/module/staff-pool/statistics`

### 6.1 获取人员概览统计
- **接口**: `GET /module/staff-pool/statistics/overview`
- **描述**: 获取人员资源概览统计
- **返回**: `ResultMsg<StaffOverviewStatistics>`
- **实现**: 调用StaffStatisticsManager.getOverviewStatistics()方法

### 6.2 获取技能分布统计
- **接口**: `GET /module/staff-pool/statistics/skillDistribution`
- **描述**: 获取技能分布统计
- **返回**: `ResultMsg<List<SkillDistribution>>`
- **实现**: 调用StaffStatisticsManager.getSkillDistribution()方法

### 6.3 获取工作负荷统计
- **接口**: `POST /module/staff-pool/statistics/workload`
- **描述**: 获取人员工作负荷统计
- **请求参数**: `WorkloadQueryDTO` - 查询条件
- **返回**: `ResultMsg<List<WorkloadStatistics>>`
- **实现**: 调用StaffStatisticsManager.getWorkloadStatistics()方法

## 数据模型说明

### 核心实体
1. **StaffInfo** - 人员基础信息
2. **StaffSkill** - 人员技能信息
3. **StaffAvailability** - 人员可用性状态
4. **StaffAllocation** - 人员分配记录
5. **StaffPerformance** - 人员绩效记录

### 数据库表结构
1. **s_staff_info** - 人员基础信息表
2. **s_staff_skill** - 人员技能表
3. **s_staff_availability** - 人员可用性表
4. **s_staff_allocation** - 人员分配表
5. **s_staff_performance** - 人员绩效表
6. **s_skill_tag** - 技能标签字典表

## 实现要点

1. **技能匹配算法**: 基于技能标签、技能等级、经验值等进行智能匹配
2. **可用性管理**: 实时跟踪人员状态，支持请假、出差、项目占用等状态
3. **绩效评价**: 支持多维度评价，包括技术能力、沟通能力、项目贡献等
4. **统计分析**: 提供丰富的统计图表，支持决策分析
5. **权限控制**: 不同角色具有不同的查看和操作权限
