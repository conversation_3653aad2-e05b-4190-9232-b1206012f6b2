-- newgwuomp_dev.uomp_account_apply definition

CREATE TABLE `uomp_account_apply` (
  `ID` varchar(64) NOT NULL,
  `TITLE` varchar(255) DEFAULT NULL COMMENT '标题',
  `APPLY_TIME` timestamp NULL DEFAULT NULL COMMENT '申请时间',
  `APPLY_PERSON` varchar(64) DEFAULT NULL COMMENT '申请人',
  `STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态 1:待审核 2审核通过 3审核不通过',
  `APPLY_ITEM` varchar(1200) DEFAULT NULL COMMENT '申请事项',
  `DEL_FLAG` varchar(10) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员账号申请';


-- newgwuomp_dev.uomp_account_apply_person definition

CREATE TABLE `uomp_account_apply_person` (
  `ID` varchar(64) NOT NULL,
  `APPLY_ID` varchar(64) DEFAULT NULL COMMENT '申请表ID',
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员ID',
  `PERSON_NAME` varchar(200) DEFAULT NULL COMMENT '人员名称',
  `ROLE` varchar(6000) DEFAULT NULL COMMENT '角色',
  `PERMISSION` varchar(64) DEFAULT NULL COMMENT '权限',
  `POSITION` varchar(6000) DEFAULT NULL COMMENT '岗位',
  `ACCOUNT_NUM` varchar(100) DEFAULT NULL COMMENT '账号',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新人组织',
  `ENTRY_DATE` varchar(20) DEFAULT NULL COMMENT '入职时间',
  `PERSON_JSON` longtext,
  `AUTHORIZATION_STATUS` varchar(64) DEFAULT NULL COMMENT '授权状态 0-未授权 1-已授权',
  `ORG_USER_ID` varchar(64) DEFAULT NULL COMMENT '系统账号id',
  `MAINTENANCE_GROUP_ID` varchar(600) DEFAULT NULL COMMENT '进驻运维组id',
  `MAINTENANCE_GROUP_JSON` longtext COMMENT '进驻运维组json',
  `ENGAGEMENT_PROJECT_ID` varchar(600) DEFAULT NULL COMMENT '参与项目id',
  `ENGAGEMENT_PROJECT_JSON` longtext COMMENT '参与项目json',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='账号申请人员表';


-- newgwuomp_dev.uomp_admission_application definition

CREATE TABLE `uomp_admission_application` (
  `ID` varchar(64) NOT NULL,
  `APPLY_CODE` varchar(64) DEFAULT NULL COMMENT '编号',
  `APPLY_TITLE` varchar(200) DEFAULT NULL COMMENT '标题',
  `APPLY_USER_ID` varchar(64) DEFAULT NULL COMMENT '申请人id',
  `APPLY_USER_NAME` varchar(200) DEFAULT NULL COMMENT '申请人名称',
  `APPLY_TIME` timestamp NULL DEFAULT NULL COMMENT '申请时间',
  `APPLY_MATTER` varchar(300) DEFAULT NULL COMMENT '申请事项',
  `MANAGER_COMMENT` varchar(900) DEFAULT NULL COMMENT '运维经理意见',
  `LEADER_COMMENT` varchar(900) DEFAULT NULL COMMENT '甲方运维领导小组意见',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='入场申请基础信息表';


-- newgwuomp_dev.uomp_admission_person definition

CREATE TABLE `uomp_admission_person` (
  `ID` varchar(64) NOT NULL COMMENT '主键',
  `APPLY_ID` varchar(64) DEFAULT NULL COMMENT '入场申请id',
  `PERSON_NAME` varchar(200) DEFAULT NULL COMMENT '姓名',
  `PERSON_CARD` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '身份证号',
  `ENTRY_DATE` varchar(20) DEFAULT NULL COMMENT '入职时间',
  `EDUCATION` varchar(30) DEFAULT NULL COMMENT '学历',
  `TECHNICAL_POST` varchar(60) DEFAULT NULL COMMENT '职称',
  `WORKING_COMPANY` varchar(300) DEFAULT NULL COMMENT '所在公司',
  `TEL` varchar(15) DEFAULT NULL COMMENT '联系电话',
  `ENGAGEMENT_PROJECT` varchar(3000) DEFAULT NULL COMMENT '应用系统',
  `SCORE` varchar(5) DEFAULT NULL COMMENT '笔试成绩',
  `INTERVIEW_RESULT` varchar(100) DEFAULT NULL COMMENT '面谈结果',
  `MAINTENANCE_GROUP_ID` varchar(1000) DEFAULT NULL COMMENT '进驻运维组id',
  `MAINTENANCE_GROUP_NAME` varchar(3000) DEFAULT NULL COMMENT '进驻运维组',
  `POST_ID` varchar(1000) DEFAULT NULL COMMENT '申请岗位id',
  `POST_NAME` varchar(1000) DEFAULT NULL COMMENT '申请岗位',
  `JOB_CONTENT` varchar(600) DEFAULT NULL COMMENT '工作内容',
  `IS_EXPORT` varchar(1) DEFAULT NULL COMMENT '是否导入 0-是 1-不是',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `IN_TIME` timestamp NULL DEFAULT NULL COMMENT '入场时间',
  `OUT_TIME` timestamp NULL DEFAULT NULL COMMENT '退场时间',
  `OUT_APPLY_STATUS` varchar(1) DEFAULT NULL COMMENT '退场申请状态 0-无申请 1-有申请',
  `FILE_INFO` longtext COMMENT '笔试面试相关附件',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `POST_FILE_INFO` text COMMENT '附件',
  `SERVIC_TYPE` varchar(2) DEFAULT NULL COMMENT '服务类型',
  `SERVICE_LOCATION` varchar(50) DEFAULT NULL COMMENT '驻场服务地点',
  `IS_CLASSIFIED_POSITION` varchar(1) DEFAULT NULL COMMENT '是否涉密岗位',
  `TECHNICAL_DIRECTION` varchar(10) DEFAULT NULL COMMENT '技术方向',
  `ENGAGEMENT_PROJECT_ID` varchar(1000) DEFAULT NULL COMMENT '应用系统id',
  `ENGAGEMENT_PROJECT_JSON` longtext COMMENT '应用系统json',
  `MAINTENANCE_GROUP_JSON` longtext COMMENT '进驻运维组json',
  `PLAN_VISIT_TIME` timestamp NULL DEFAULT NULL COMMENT '预计到访时间',
  `PLAN_OUT_TIME` timestamp NULL DEFAULT NULL COMMENT '计划退出时间',
  `PERSON_ROLE` varchar(100) DEFAULT NULL COMMENT '角色',
  `PERSON_ROLE_ID` varchar(100) DEFAULT NULL COMMENT '角色 id',
  `PERSON_ROLE_JSON` varchar(2000) DEFAULT NULL COMMENT '角色 JSON',
  `DEPART_ID` varchar(100) DEFAULT NULL COMMENT '主管部门 id',
  `DEPART_NAME` varchar(100) DEFAULT NULL COMMENT '主管部门',
  `DEPART_JSON` varchar(2000) DEFAULT NULL COMMENT '主管部门 JSON',
  `POSITION_TYPE` varchar(100) DEFAULT NULL COMMENT '岗位类型',
  `CLASSIFIED_POSITION` varchar(100) DEFAULT NULL COMMENT '涉密岗位性质',
  `POST_JSON` varchar(2000) DEFAULT NULL COMMENT '申请岗位 JSON',
  `WORKING_COMPANY_ID` varchar(300) DEFAULT NULL COMMENT '所在公司id',
  `WORKING_COMPANY_JSON` varchar(300) DEFAULT NULL COMMENT '所在公司json',
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员ID',
  `SEX` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='入场申请关联人员信息表';


-- newgwuomp_dev.uomp_application_system_management definition

CREATE TABLE `uomp_application_system_management` (
  `ID` varchar(64) NOT NULL,
  `APPLICATION_SYSTEM_NAME` varchar(300) DEFAULT NULL COMMENT '应用系统名称',
  `DEPART_ID` varchar(64) DEFAULT NULL COMMENT '运维部门id',
  `DEPART_NAME` varchar(200) DEFAULT NULL COMMENT '运维部门名称',
  `PRINCIPAL_NAME` varchar(100) DEFAULT NULL COMMENT '负责人id',
  `PRINCIPAL_ID` varchar(64) DEFAULT NULL COMMENT '负责人名称',
  `IS_HEART` varchar(200) DEFAULT NULL COMMENT '是否核心应用（1：是   0：否）',
  `ONLINE_TIME` timestamp NULL DEFAULT NULL COMMENT '上线时间',
  `SYSTEM_STATUS` varchar(2) DEFAULT NULL COMMENT '状态（0：使用中  1已停用）',
  `SUPPLIER_ID` varchar(64) DEFAULT NULL COMMENT '服务商id',
  `SUPPLIER_NAME` varchar(200) DEFAULT NULL COMMENT '服务商名称',
  `SUPPLIER_DEPART_NAME` varchar(100) DEFAULT NULL COMMENT '服务商负责人',
  `SUPPLIER_TEL` varchar(12) DEFAULT NULL COMMENT '联系电话',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建机构',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新机构',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识（0:正常  1：已删除）1',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='应用系统信息表';


-- newgwuomp_dev.uomp_application_system_relation definition

CREATE TABLE `uomp_application_system_relation` (
  `ID` varchar(64) NOT NULL,
  `APPLICATION_SYSTEM_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '应用系统id',
  `RELATION_ID` varchar(64) DEFAULT NULL COMMENT '关联id',
  `RELATION_TYPE` varchar(10) DEFAULT NULL COMMENT '关联类型（contract:合同）',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建机构',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新机构',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识（0:正常  1：已删除）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='应用系统关联表';


-- newgwuomp_dev.uomp_contract_file definition

CREATE TABLE `uomp_contract_file` (
  `ID` varchar(64) NOT NULL,
  `CONTRACT_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '合同id',
  `FILE_NAME` varchar(300) DEFAULT NULL COMMENT '附件名称',
  `FILE_ID` varchar(300) DEFAULT NULL COMMENT '文件服务id',
  `UPLOAD_TIME` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `UPLOADER_ID` varchar(64) DEFAULT NULL COMMENT '上传人id',
  `UPLOADER_NAME` varchar(30) DEFAULT NULL COMMENT '上传人名称',
  `CREATE_BY` varchar(64) DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL,
  `UPDATE_BY` varchar(64) DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL,
  `DEL_FLAG` varchar(1) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='合同附件表';


-- newgwuomp_dev.uomp_contract_management definition

CREATE TABLE `uomp_contract_management` (
  `ID` varchar(64) NOT NULL,
  `CONTRACT_CODE` varchar(100) DEFAULT NULL COMMENT '合同编号',
  `CONTRACT_NAME` varchar(300) DEFAULT NULL COMMENT '合同名称',
  `SIGNING_DATE` timestamp NULL DEFAULT NULL COMMENT '签订日期',
  `CONTRACT_TYPE` varchar(10) DEFAULT NULL COMMENT '合同类型',
  `CONTRACT_STATUS` varchar(10) DEFAULT NULL COMMENT '合同状态',
  `PARTY_A_NAME` varchar(300) DEFAULT NULL COMMENT '甲方名称',
  `PARTY_A_PRINCIPAL_NAME` varchar(200) DEFAULT NULL COMMENT '甲方负责人名称',
  `PARTY_B_NAME` varchar(300) DEFAULT NULL COMMENT '乙方名称',
  `PARTY_B_PRINCIPAL_NAME` varchar(200) DEFAULT NULL COMMENT '乙方负责人名称',
  `PARTY_C_NAME` varchar(300) DEFAULT NULL COMMENT '丙方名称',
  `PARTY_C_PRINCIPAL_NAME` varchar(200) DEFAULT NULL COMMENT '丙方负责人名称',
  `CONTRACT_CONTENT` varchar(6000) DEFAULT NULL,
  `PROJECT_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '合同应用系统id',
  `PROJECT_MANAGEMENT_NAME` varchar(300) DEFAULT NULL COMMENT '合同应用系统名称',
  `DUTY_DEPART_ID` varchar(64) DEFAULT NULL COMMENT '责任部门id',
  `DUTY_DEPART_NAME` varchar(300) DEFAULT NULL COMMENT '责任部门名称',
  `PAY_TIME` timestamp NULL DEFAULT NULL COMMENT '付款日期',
  `PAY_STATUS` varchar(10) DEFAULT NULL COMMENT '付款情况',
  `SERVICE_LEVEL_ID` varchar(64) DEFAULT NULL COMMENT '服务等级协议id',
  `SERVICE_LEVEL_NAME` varchar(300) DEFAULT NULL COMMENT '服务等级协议名称',
  `QUALITY_BEGIN_DAY` timestamp NULL DEFAULT NULL COMMENT '质保/维保开始日',
  `QUALITY_END_DAY` timestamp NULL DEFAULT NULL COMMENT '质保/维保结束日',
  `QUALITY_CONTENT` varchar(4000) DEFAULT NULL COMMENT '质保内容',
  `PARTY_A_PRINCIPAL_TEL` varchar(20) DEFAULT NULL COMMENT '甲方负责人电话',
  `PARTY_A_MANAGER` varchar(200) DEFAULT NULL COMMENT '甲方项目经理',
  `PARTY_A_MANAGER_TEL` varchar(20) DEFAULT NULL COMMENT '甲方项目经理电话',
  `PARTY_B_PRINCIPAL_TEL` varchar(20) DEFAULT NULL COMMENT '乙方负责人电话',
  `PARTY_B_MANAGER` varchar(200) DEFAULT NULL COMMENT '乙方项目经理',
  `PARTY_B_MANAGER_TEL` varchar(20) DEFAULT NULL COMMENT '乙方项目经理电话',
  `SALES_MANAGER` varchar(200) DEFAULT NULL COMMENT '销售经理',
  `PRE_SALES_MANAGER` varchar(200) DEFAULT NULL COMMENT '售前经理',
  `PARTY_C_PERSON` varchar(200) DEFAULT NULL COMMENT '丙方联系人',
  `PARTY_C_PERSON_TEL` varchar(20) DEFAULT NULL COMMENT '丙方联系人电话',
  `REMARK` varchar(4000) DEFAULT NULL COMMENT '备注',
  `ENTRY_ID` varchar(64) DEFAULT NULL COMMENT '录入人id',
  `ENTRY_NAME` varchar(200) DEFAULT NULL COMMENT '录入人名称',
  `CREATE_BY` varchar(64) DEFAULT NULL,
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL,
  `UPDATE_BY` varchar(64) DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL,
  `DEL_FLAG` varchar(1) DEFAULT NULL,
  `PARTY_B_ID` varchar(64) DEFAULT NULL COMMENT '服务商id',
  `contract_amount` float(10,2) DEFAULT NULL COMMENT '合同金额',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='合同管理基本信息表';


-- newgwuomp_dev.uomp_contract_notice definition

CREATE TABLE `uomp_contract_notice` (
  `ID` varchar(64) NOT NULL,
  `CONTRACT_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '合同id',
  `NOTICE_NUM` int DEFAULT '0' COMMENT '提醒次数',
  `QUALITY_BEGIN_DAY` timestamp NULL DEFAULT NULL COMMENT '质保/维保开始日',
  `QUALITY_END_DAY` timestamp NULL DEFAULT NULL COMMENT '质保/维保开始日',
  `CREATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `DEL_FLAG` varchar(2) DEFAULT '0' COMMENT '删除标识（0 正常 1删除）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='合同到期提醒表';


-- newgwuomp_dev.uomp_contract_resource definition

CREATE TABLE `uomp_contract_resource` (
  `ID` varchar(64) NOT NULL,
  `CONTRACT_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '合同id',
  `C_INST_ID` varchar(64) DEFAULT NULL COMMENT '资源ID',
  `CI_NAME` varchar(256) DEFAULT NULL COMMENT '资源名称',
  `BRAND` varchar(256) DEFAULT NULL COMMENT '品牌',
  `BRAND_MODEL` varchar(256) DEFAULT NULL COMMENT '型号',
  `CPU_FRAMEWORK` varchar(256) DEFAULT NULL COMMENT 'cpu架构',
  `USEDS` varchar(256) DEFAULT NULL COMMENT '用途',
  `OS` varchar(256) DEFAULT NULL COMMENT '操作系统',
  `OS_VERSION` varchar(256) DEFAULT NULL COMMENT '操作系统版本',
  `MACHINE_ROOM` varchar(1024) DEFAULT NULL COMMENT '所属机房',
  `CABINET` varchar(1024) DEFAULT NULL COMMENT '所属机柜',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='合同关联资源表';


-- newgwuomp_dev.uomp_desensitization definition

CREATE TABLE `uomp_desensitization` (
  `ID` varchar(64) NOT NULL,
  `DES_OBJ_CODE` varchar(64) DEFAULT NULL COMMENT '脱敏对象code',
  `DES_FIELD_CODE` varchar(64) DEFAULT NULL COMMENT '脱敏字段code',
  `DES_RULE_MODE` varchar(2) DEFAULT NULL COMMENT '脱敏规则方式 0-普通 1-正则表达式',
  `DES_RULE_JSON` longtext COMMENT '脱敏规则json',
  `DES_RULE_REGX` varchar(128) DEFAULT NULL COMMENT '脱敏规则正则表达式',
  `SENSITIVE_WORDS` varchar(256) DEFAULT NULL COMMENT '敏感词',
  `SENSITIVE_REPLACE_WORDS` varchar(32) DEFAULT NULL COMMENT '敏感词替换词',
  `PLAINTEXT_ROLE_JSON` longtext COMMENT '明文展示角色json',
  `PLAINTEXT_POST_JSON` longtext COMMENT '明文展示岗位json',
  `PLAINTEXT_USER_JSON` longtext COMMENT '经办人是否可见 0-是 1-否',
  `IS_CREATER` varchar(2) DEFAULT NULL COMMENT '数据录入人是否可见 0-是 1-否',
  `IS_OPERATOR` varchar(2) DEFAULT NULL COMMENT '脱敏规则方式 0-普通 1-正则表达式',
  `OPERATOR_MODE` varchar(16) DEFAULT NULL COMMENT '经办人过滤方式 0-sql',
  `OPERATOR_SCRIPT` longtext COMMENT '经办人过滤sql',
  `IS_ENABLE` varchar(2) DEFAULT NULL COMMENT '是否启用 0-是 1-否',
  `IS_OVERALL_ENABLE` varchar(2) DEFAULT NULL COMMENT '全局是否启用 0-是 1-否',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `DEL_FLAG` varchar(2) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='脱敏配置表';


-- newgwuomp_dev.uomp_exit_accept_info definition

CREATE TABLE `uomp_exit_accept_info` (
  `ID` varchar(64) NOT NULL,
  `EXIT_APPLY_ID` varchar(64) DEFAULT NULL COMMENT '退场申请id',
  `ENGAGEMENT_PROJECT_ID` varchar(64) DEFAULT NULL COMMENT '参与项目id',
  `ENGAGEMENT_PROJECT_NAME` varchar(300) DEFAULT NULL COMMENT '参与项目名称',
  `ACCEPT_USER_ID` varchar(64) DEFAULT NULL COMMENT '工作交接人id',
  `ACCEPT_USER_NAME` varchar(200) DEFAULT NULL COMMENT '工作交接人名称',
  `ACCEPT_CONTEXT` varchar(1500) DEFAULT NULL COMMENT '工作交接内容',
  `ACCEPT_CONTEXT_FILE` text COMMENT '工作交接附件',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退场申请接收人数据表';


-- newgwuomp_dev.uomp_exit_admission_relation definition

CREATE TABLE `uomp_exit_admission_relation` (
  `ID` varchar(64) NOT NULL,
  `APPLY_PERSON_ID` varchar(64) DEFAULT NULL COMMENT '入场申请人员id',
  `EXIT_APPLY_ID` varchar(64) DEFAULT NULL COMMENT '退场申请id',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退场申请入场申请关系表';


-- newgwuomp_dev.uomp_exit_application definition

CREATE TABLE `uomp_exit_application` (
  `ID` varchar(64) NOT NULL,
  `APPLY_PERSON_ID` varchar(64) DEFAULT NULL COMMENT '退场申请人员id(原-入场申请人员id)',
  `OUT_APPLY_CODE` varchar(64) DEFAULT NULL COMMENT '编号',
  `PERSON_NAME` varchar(200) DEFAULT NULL COMMENT '姓名',
  `TEL` varchar(15) DEFAULT NULL COMMENT '联系电话',
  `ONLINE_TIME` varchar(20) DEFAULT NULL COMMENT '在岗年限',
  `MAINTENANCE_GROUP_ID` varchar(64) DEFAULT NULL COMMENT '进驻运维组id(已废弃)',
  `MAINTENANCE_GROUP_NAME` varchar(100) DEFAULT NULL COMMENT '进驻运维组(已废弃)',
  `POST_ID` varchar(1000) DEFAULT NULL COMMENT '技术方向code(原-申请岗位id)',
  `POST_NAME` varchar(1000) DEFAULT NULL COMMENT '技术方向(原-申请岗位名称)',
  `WORKING_COMPANY` varchar(300) DEFAULT NULL COMMENT '所在公司',
  `JOB_ACCEPT_ID` varchar(64) DEFAULT NULL COMMENT '工作接收人id(已废弃)',
  `JOB_ACCEPT_NAME` varchar(500) DEFAULT NULL COMMENT '工作接收人(已废弃)',
  `PLAN_OUT_TIME` timestamp NULL DEFAULT NULL COMMENT '计划退出时间',
  `OUT_REASON` varchar(1500) DEFAULT NULL COMMENT '退出原因',
  `OUT_REASON_FILE` text COMMENT '退场原因附件信息',
  `JOB_HANDOVER_SITUATION` varchar(1500) DEFAULT NULL COMMENT '工作交接情况(已废弃)',
  `JOB_HANDOVER_SITUATION_FILE` text COMMENT '工作交接附件信息(已废弃)',
  `MANAGER_COMMENT` varchar(1500) DEFAULT NULL COMMENT '运维经理意见(已废弃)',
  `LEADER_COMMENT` varchar(1500) DEFAULT NULL COMMENT '甲方运维小组领导意见(已废弃)',
  `IS_DELETE` varchar(1) DEFAULT NULL COMMENT '是否删除相关权限 0-是 1-否',
  `OUT_TIME` timestamp NULL DEFAULT NULL COMMENT '实际退场时间',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `OUT_APPLY_TITLE` varchar(60) DEFAULT NULL COMMENT '标题',
  `CREATE_USER` varchar(200) DEFAULT NULL COMMENT '创建人名称',
  `PERSON_CARD` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '身份证号',
  `WORKING_COMPANY_ID` varchar(300) DEFAULT NULL COMMENT '所在公司id',
  `WORKING_COMPANY_JSON` varchar(300) DEFAULT NULL COMMENT '所在公司json',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='退场申请信息表';


-- newgwuomp_dev.uomp_history_record definition

CREATE TABLE `uomp_history_record` (
  `ID` varchar(64) NOT NULL,
  `BIZ_ID` varchar(64) DEFAULT NULL COMMENT '业务主键',
  `BIZ_TYPE` varchar(64) DEFAULT NULL COMMENT '业务类型 0-黑名单',
  `OPERATOR_ID` varchar(64) DEFAULT NULL COMMENT '操作人ID',
  `OPERATOR_NAME` varchar(256) DEFAULT NULL COMMENT '操作人',
  `OPERATOR_MESSAGE` varchar(1024) DEFAULT NULL COMMENT '操作信息',
  `OPERATOR_REASON` varchar(1024) DEFAULT NULL COMMENT '操作原因说明',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `DEL_FLAG` varchar(2) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `OPERATOR_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='操作记录表';


-- newgwuomp_dev.uomp_org_group definition

CREATE TABLE `uomp_org_group` (
  `ID_` varchar(64) NOT NULL,
  `NAME_` varchar(300) NOT NULL COMMENT '名称',
  `PARENT_ID_` varchar(64) DEFAULT NULL COMMENT '父ID',
  `SN_` int DEFAULT '100' COMMENT '排序',
  `CODE_` varchar(300) NOT NULL COMMENT '编号',
  `TYPE_` varchar(192) DEFAULT NULL COMMENT '类型：0集团，1公司，3部门',
  `DESC_` varchar(1500) DEFAULT NULL COMMENT '描述',
  `CREATE_TIME_` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_BY_` varchar(192) DEFAULT NULL COMMENT '创建人',
  `UPDATE_TIME_` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_BY_` varchar(192) DEFAULT NULL COMMENT '更新人',
  `PATH_` varchar(4000) DEFAULT NULL COMMENT '机构路径',
  `SIMPLE_` varchar(60) DEFAULT NULL COMMENT '简称',
  `STATUS_` int DEFAULT '1' COMMENT '状态：0禁用，1正常',
  `SHOW_NAME_` varchar(1000) DEFAULT NULL COMMENT '显示名称',
  `VIRTUAL_` int DEFAULT NULL COMMENT '是否虚拟:0否,1是',
  `HISTORY_NAME_` varchar(2000) DEFAULT NULL COMMENT '曾用名',
  `PATH_NAME_` varchar(4000) DEFAULT NULL COMMENT '机构路径',
  `MCODE_` varchar(300) DEFAULT NULL COMMENT '主编号',
  `RESP_NAME` varchar(300) DEFAULT NULL COMMENT '负责人',
  `ORG_GROUP_ID` varchar(300) DEFAULT NULL COMMENT '组织 id',
  `RESP_ID` varchar(100) DEFAULT NULL COMMENT '负责人 id',
  PRIMARY KEY (`ID_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员库组织机构表';


-- newgwuomp_dev.uomp_party_info definition

CREATE TABLE `uomp_party_info` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `contract_management_id` varchar(64) DEFAULT NULL COMMENT '合同基本信息表ID',
  `party_info` varchar(256) DEFAULT NULL COMMENT '乙方联合体信息',
  `party_name` varchar(256) DEFAULT NULL COMMENT '乙方联合体负责人',
  `party_tel` varchar(20) DEFAULT NULL COMMENT '乙方联合电话',
  `party_manager_name` varchar(256) DEFAULT NULL COMMENT '项目经理',
  `party_manager_tel` varchar(20) DEFAULT NULL COMMENT '项目经理电话',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人id',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='乙方联合体';


-- newgwuomp_dev.uomp_permission_application definition

CREATE TABLE `uomp_permission_application` (
  `ID` varchar(64) NOT NULL,
  `APPLY_CODE` varchar(64) DEFAULT NULL COMMENT '编号',
  `APPLY_TITLE` varchar(200) DEFAULT NULL COMMENT '标题',
  `APPLY_USER_ID` varchar(64) DEFAULT NULL COMMENT '申请人id',
  `APPLY_USER_NAME` varchar(200) DEFAULT NULL COMMENT '申请人名称',
  `APPLY_TIME` timestamp NULL DEFAULT NULL COMMENT '申请时间',
  `APPLY_MATTER` varchar(300) DEFAULT NULL COMMENT '申请事项',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员权限申请基础表';


-- newgwuomp_dev.uomp_permission_in definition

CREATE TABLE `uomp_permission_in` (
  `ID` varchar(64) NOT NULL,
  `APPLY_ID` varchar(64) DEFAULT NULL COMMENT '申请表id',
  `USER_ID` varchar(64) DEFAULT NULL COMMENT '用户id',
  `USER_NAME` varchar(300) DEFAULT NULL COMMENT '用户名称',
  `USER_JSON` text COMMENT '用户json',
  `EXISTING_ROLES` varchar(1000) DEFAULT NULL COMMENT '已有角色',
  `EXISTING_POSITIONS` varchar(1000) DEFAULT NULL COMMENT '已有岗位',
  `MAINTENANCE_GROUP_NAME` varchar(1000) DEFAULT NULL COMMENT '所属业务组名称',
  `APPLY_TYPE` varchar(10) DEFAULT NULL COMMENT '申请类型 0-申请分配权限 1-申请删除权限 2-申请启用权限',
  `ROLE` text COMMENT '角色',
  `POSITION` text COMMENT '岗位',
  `AUTHORIZATION_STATUS` varchar(10) DEFAULT NULL COMMENT '授权状态 0-未授权 1-已授权',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识 0-未删除 1-已删除',
  `EXISTING_SYSTEM` varchar(1000) DEFAULT NULL COMMENT '已有系统',
  `SYSTEM_NAME` text COMMENT '系统',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员权限申请内部权限表';


-- newgwuomp_dev.uomp_permission_out_base definition

CREATE TABLE `uomp_permission_out_base` (
  `ID` varchar(64) NOT NULL,
  `APPLY_ID` varchar(64) DEFAULT NULL COMMENT '申请表id',
  `OUT_APPLY_SYS_TYPE` varchar(10) DEFAULT NULL COMMENT '外部申请系统类型',
  `OUT_APPLY_SYS_ID` varchar(64) DEFAULT NULL COMMENT '外部申请系统id',
  `OUT_APPLY_SYS_NAME` varchar(500) DEFAULT NULL COMMENT '外部申请系统名称',
  `OUT_APPLY_SYS_JSON` varchar(2000) DEFAULT NULL COMMENT '外部申请系统json',
  `OUT_APPLY_TITLE` varchar(500) DEFAULT NULL COMMENT '外部申请标题',
  `OUT_APPLY_USER_ID` varchar(64) DEFAULT NULL COMMENT '外部申请人id',
  `OUT_APPLY_USER_NAME` varchar(200) DEFAULT NULL COMMENT '外部申请人名称',
  `OUT_APPLY_TIME` timestamp NULL DEFAULT NULL COMMENT '外部申请时间',
  `OUT_APPLY_MATTER` varchar(300) DEFAULT NULL COMMENT '外部申请事项',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员权限申请外部权限基础表';


-- newgwuomp_dev.uomp_permission_out_details definition

CREATE TABLE `uomp_permission_out_details` (
  `ID` varchar(64) NOT NULL,
  `OUT_USER_ID` varchar(64) DEFAULT NULL COMMENT '用户表id',
  `OUT_APPLY_TYPE` varchar(10) DEFAULT NULL COMMENT '外部申请类型 0-申请分配权限 1-申请删除权限',
  `EMPOWER_RESOURCE_IDS` varchar(1000) DEFAULT NULL COMMENT '授权资源id集合',
  `EMPOWER_RESOURCE_JSON` text COMMENT '授权资源json',
  `PERMISSION` varchar(200) DEFAULT NULL COMMENT '权限',
  `EMPOWER_BEGIN_TIME` varchar(30) DEFAULT NULL COMMENT '授权开始日期',
  `EMPOWER_END_TIME` varchar(30) DEFAULT NULL COMMENT '授权结束日期',
  `AUTHORIZATION_STATUS` varchar(10) DEFAULT NULL COMMENT '授权状态 0-未授权 1-已授权',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员权限申请外部权限详情表';


-- newgwuomp_dev.uomp_permission_out_user definition

CREATE TABLE `uomp_permission_out_user` (
  `ID` varchar(64) NOT NULL,
  `OUT_BASE_ID` varchar(64) DEFAULT NULL COMMENT '外部申请表id',
  `EMPOWER_USER_IDS` varchar(1000) DEFAULT NULL COMMENT '授权用户id集合',
  `EMPOWER_USER_JSON` text COMMENT '授权用户json',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员权限申请外部用户表';


-- newgwuomp_dev.uomp_person_abroad definition

CREATE TABLE `uomp_person_abroad` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `CERTIFICATE_NAME` varchar(300) DEFAULT NULL COMMENT '证件名称',
  `CERTIFICATE_NUM` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `ISSUE_AT` varchar(300) DEFAULT NULL COMMENT '签发地',
  `START_TIME` varchar(50) DEFAULT NULL COMMENT '证件有效起始时间',
  `END_TIME` varchar(50) DEFAULT NULL COMMENT '证件有效终止时间',
  `FILE_INFO` text COMMENT '附件信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='出国证件信息表';


-- newgwuomp_dev.uomp_person_all_info_temp definition

CREATE TABLE `uomp_person_all_info_temp` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `PERSON_INFO` longtext COMMENT '人员全部信息',
  `EDUCATIONAL_INFO` longtext COMMENT '教育背景信息',
  `JOB_INFO` longtext COMMENT '工作背景信息',
  `TECH_INFO` longtext COMMENT '技术资质信息',
  `SOCIAL_INFO` longtext COMMENT '社会关系信息',
  `NO_CRIME_INFO` longtext COMMENT '无犯罪记录信息',
  `ABROAD_INFO` longtext COMMENT '出国证件信息',
  `ENRTY_EXIT_INFO` longtext COMMENT '出入境记录信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `DEL_FLAG` varchar(2) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员信息子表';


-- newgwuomp_dev.uomp_person_config definition

CREATE TABLE `uomp_person_config` (
  `ID` varchar(64) NOT NULL,
  `CONFIG_INFO` varchar(3000) DEFAULT NULL COMMENT '配置信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识',
  `CONFIG_TYPE` varchar(64) DEFAULT NULL COMMENT '配置类型， 1-人员页签配置 ， 2-供应商页签配置',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员信息标签配置表';


-- newgwuomp_dev.uomp_person_duty definition

CREATE TABLE `uomp_person_duty` (
  `ID` varchar(64) NOT NULL,
  `DATE` varchar(32) DEFAULT NULL COMMENT '值班日期',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT '0' COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员值班表';


-- newgwuomp_dev.uomp_person_duty_detail definition

CREATE TABLE `uomp_person_duty_detail` (
  `ID` varchar(64) NOT NULL,
  `DUTY_ID` varchar(64) DEFAULT NULL COMMENT '值班id',
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `DATE` varchar(32) DEFAULT NULL COMMENT '值班日期',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` varchar(1) DEFAULT '0' COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员值班详情表';


-- newgwuomp_dev.uomp_person_educational definition

CREATE TABLE `uomp_person_educational` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `EDUCATION_BEGIN_TIME` varchar(15) DEFAULT NULL COMMENT '教育开始时间',
  `EDUCATION_END_TIME` varchar(15) DEFAULT NULL COMMENT '教育结束时间',
  `SCHOOL` varchar(120) DEFAULT NULL COMMENT '就读院校',
  `MAJOR` varchar(120) DEFAULT NULL COMMENT '就读专业',
  `EDUCATION_BACKGROUND` varchar(120) DEFAULT NULL COMMENT '学历',
  `FILE_INFO` text COMMENT '附件信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `EDUCATION_FORM` varchar(10) DEFAULT NULL COMMENT '教育形式(是否统招)',
  `CERTIFICATE_NUM` varchar(50) DEFAULT NULL COMMENT '证书编号',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教育背景信息表';


-- newgwuomp_dev.uomp_person_entry_exit definition

CREATE TABLE `uomp_person_entry_exit` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `ENTRY_EXIT` varchar(10) DEFAULT NULL COMMENT '出境/入境',
  `ENTRY_EXIT_TIME` varchar(50) DEFAULT NULL COMMENT '出入境时间',
  `CERTIFICATE_NAME` varchar(300) DEFAULT NULL COMMENT '证件名称',
  `CERTIFICATE_NUM` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `ENTRY_EXIT_PORTS` varchar(300) DEFAULT NULL COMMENT '出入境口岸',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='出入境情况表';


-- newgwuomp_dev.uomp_person_info definition

CREATE TABLE `uomp_person_info` (
  `ID` varchar(64) NOT NULL,
  `PERSON_NAME` varchar(200) DEFAULT NULL COMMENT '姓名',
  `PERSON_CARD` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '身份证号',
  `PERSON_SEX` varchar(2) DEFAULT NULL COMMENT '性别',
  `PERSON_BIRTHDAY` varchar(15) DEFAULT NULL COMMENT '出生年月',
  `TEL` varchar(15) DEFAULT NULL COMMENT '联系电话',
  `ADDRESS` varchar(300) DEFAULT NULL COMMENT '居住地址',
  `EDUCATION` varchar(10) DEFAULT NULL COMMENT '学历',
  `POST` varchar(100) DEFAULT NULL COMMENT '工作职位',
  `MAJOR` varchar(120) DEFAULT NULL COMMENT '专业',
  `WORKING_COMPANY` varchar(300) DEFAULT NULL COMMENT '就职公司',
  `REMARK` varchar(3000) DEFAULT NULL COMMENT '备注',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `BLACKLIST` varchar(10) DEFAULT '0' COMMENT '黑名单标识 0-非黑名单 1-黑名单',
  `BLACKLIST_REASON` varchar(300) DEFAULT NULL COMMENT '加入黑名单原因',
  `ENTRY_DATE` varchar(20) DEFAULT NULL COMMENT '入职日期',
  `FILE_INFO` text COMMENT '附件信息',
  `TRIAL_STATUS` varchar(10) DEFAULT '0' COMMENT '初审状态 0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `BACKGROUND_STATUS` varchar(10) DEFAULT '0' COMMENT '背景调查状态 0-待审核 1-合格 2-不合格',
  `ID_TYPE` varchar(10) DEFAULT NULL COMMENT '证件类型(暂时冗余)',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT '0' COMMENT '逻辑删除标记 0-有效 1-无效',
  `IS_ACCOUNT` varchar(10) DEFAULT '0' COMMENT '是否分配账号 0-未分配 1-已分配',
  `ACCOUNT` varchar(50) DEFAULT NULL COMMENT '账号',
  `WORKING_COMPANY_ID` varchar(64) DEFAULT NULL COMMENT '就职公司id',
  `TECHNICAL_DIRECTION` varchar(10) DEFAULT NULL COMMENT '技术方向',
  `REG_PERMANENT_RESIDENCE` varchar(1000) DEFAULT NULL COMMENT '户口所在地',
  `NATIONALITY` varchar(300) DEFAULT NULL COMMENT '国籍',
  `POLITICS_STATUS` varchar(300) DEFAULT NULL COMMENT '政治面貌',
  `NATION` varchar(200) DEFAULT NULL COMMENT '民族',
  `REG_PROVINCE` varchar(20) DEFAULT NULL COMMENT '户口所在地-省',
  `REG_CITY` varchar(20) DEFAULT NULL COMMENT '户口所在地-市',
  `REG_REGION` varchar(20) DEFAULT NULL COMMENT '户口所在地-区/县',
  `AUDIT_DATE` timestamp NULL DEFAULT NULL COMMENT '审核日期',
  `ORG_GROUP_ID` varchar(100) DEFAULT NULL COMMENT '运维组织Id',
  `ORG_GROUP_NAME` varchar(100) DEFAULT NULL COMMENT '运维组织名称',
  `UPDATING` varchar(10) DEFAULT '0' COMMENT '是否更新中（1是 0 否）',
  `ORG_USER_ID` varchar(64) DEFAULT NULL COMMENT '系统用户Id',
  `ENTRY_STATUS` varchar(10) DEFAULT '-1' COMMENT '驻场服务状态',
  `DIMISSION` varchar(10) DEFAULT '0' COMMENT '是否离职',
  `EMAIL` varchar(64) DEFAULT NULL COMMENT '邮箱',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人员管理基础信息表';


-- newgwuomp_dev.uomp_person_job definition

CREATE TABLE `uomp_person_job` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `JOB_BEGIN_TIME` varchar(15) DEFAULT NULL COMMENT '工作开始时间',
  `JOB_END_TIME` varchar(15) DEFAULT NULL COMMENT '工作结束时间',
  `COMPANY_NAME` varchar(120) DEFAULT NULL COMMENT '公司名称',
  `JOB_POSITION` varchar(120) DEFAULT NULL COMMENT '工作职位',
  `JOB_DESCRIBE` varchar(900) DEFAULT NULL COMMENT '工作描述',
  `FILE_INFO` text COMMENT '附件信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='工作背景信息表';


-- newgwuomp_dev.uomp_person_no_crime definition

CREATE TABLE `uomp_person_no_crime` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `PROOF_NUMBER` varchar(64) DEFAULT NULL COMMENT '证明编号',
  `QUERY_BEGIN_TIME` varchar(15) DEFAULT NULL COMMENT '查询周期开始时间',
  `QUERY_END_TIME` varchar(15) DEFAULT NULL COMMENT '查询周期结束时间',
  `PROVIDE_UNIT` varchar(120) DEFAULT NULL COMMENT '出具单位',
  `INDATE` varchar(30) DEFAULT NULL COMMENT '有效期',
  `FILE_INFO` text COMMENT '附件信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='无犯罪记录信息表';


-- newgwuomp_dev.uomp_person_social definition

CREATE TABLE `uomp_person_social` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `RELA_NAME` varchar(200) DEFAULT NULL COMMENT '姓名',
  `RELA_AGE` varchar(5) DEFAULT NULL COMMENT '年龄',
  `NATIONAL` varchar(100) DEFAULT NULL COMMENT '国籍',
  `POLITICS_STATUS` varchar(30) DEFAULT NULL COMMENT '政治面貌',
  `RELATION_WITH_MYSELF` varchar(30) DEFAULT NULL COMMENT '与本人关系',
  `RELA_TEL` varchar(15) DEFAULT NULL COMMENT '联系方式',
  `RELA_ADDRESS` varchar(300) DEFAULT NULL COMMENT '居住地',
  `RELA_POST` varchar(300) DEFAULT NULL COMMENT '工作单位及职务',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='社会关系信息表';


-- newgwuomp_dev.uomp_person_technology definition

CREATE TABLE `uomp_person_technology` (
  `ID` varchar(64) NOT NULL,
  `PERSON_ID` varchar(64) DEFAULT NULL COMMENT '人员id',
  `GET_TIME` varchar(15) DEFAULT NULL COMMENT '获取时间',
  `QUALIFTY_NAME` varchar(60) DEFAULT NULL COMMENT '资质名称',
  `QUALIFTY_TYPE` varchar(10) DEFAULT NULL COMMENT '资质类型',
  `CERTIFICATION_BODY` varchar(120) DEFAULT NULL COMMENT '颁证机构',
  `FILE_INFO` text COMMENT '附件信息',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `START_TIME` varchar(50) DEFAULT NULL COMMENT '证书起始时间',
  `END_TIME` varchar(50) DEFAULT NULL COMMENT '证书终止时间',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='技术资质信息表';


-- newgwuomp_dev.uomp_report definition

CREATE TABLE `uomp_report` (
  `ID` varchar(64) NOT NULL,
  `REPORT_NAME` varchar(300) DEFAULT NULL COMMENT '报告名称',
  `REPORT_CODE` varchar(100) DEFAULT NULL COMMENT '报告编码',
  `REPORT_TYPE` varchar(1) DEFAULT NULL COMMENT '报告类型',
  `REPORT_BEGIN` timestamp NULL DEFAULT NULL COMMENT '报告开始时间',
  `REPORT_END` timestamp NULL DEFAULT NULL COMMENT '报告结束时间',
  `APPLICATION_SYSTEM_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '应用系统id',
  `APPLICATION_SYSTEM_NAME` varchar(300) DEFAULT NULL COMMENT '应用 系统名称',
  `SUPPLIER_NAME` varchar(200) DEFAULT NULL COMMENT '服务商',
  `UPLOAD_TIME` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `UPLOADER_ID` varchar(64) DEFAULT NULL COMMENT '上传人id',
  `UPLOADER_NAME` varchar(200) DEFAULT NULL COMMENT '上传人',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建机构',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新机构',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识（0:正常  1：已删除）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='服务报告信息表';


-- newgwuomp_dev.uomp_report_file definition

CREATE TABLE `uomp_report_file` (
  `ID` varchar(64) NOT NULL,
  `REPORT_ID` varchar(64) DEFAULT NULL COMMENT '应用系统id',
  `FILE_ID` varchar(64) DEFAULT NULL COMMENT '文件id',
  `FILE_NAME` varchar(300) DEFAULT NULL COMMENT '文件名称',
  `FILE_SIZE` varchar(100) DEFAULT NULL COMMENT '文件大小',
  `UPLOAD_TIME` varchar(64) DEFAULT NULL COMMENT '上传时间',
  `UPLOADER_ID` varchar(64) DEFAULT NULL COMMENT '上传人id',
  `UPLOADER_NAME` varchar(200) DEFAULT NULL COMMENT '上传人',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建机构',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新机构',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '删除标识（0:正常  1：已删除）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='服务报告文件表';


-- newgwuomp_dev.uomp_supplier_file definition

CREATE TABLE `uomp_supplier_file` (
  `ID` varchar(64) NOT NULL,
  `SUPPLIER_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '供应商管理主键',
  `FILE_NAME` varchar(300) DEFAULT NULL COMMENT '附件名称',
  `FILE_ID` varchar(300) DEFAULT NULL COMMENT '文档服务id',
  `UPLOADER_ID` varchar(64) DEFAULT NULL COMMENT '上传人id',
  `UPLOADER_NAME` varchar(200) DEFAULT NULL COMMENT '上传人姓名',
  `UPLOAD_TIME` timestamp NULL DEFAULT NULL COMMENT '上传时间',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL,
  `UPDATE_BY` varchar(64) DEFAULT NULL,
  `UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL,
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `FILE_TYPE` varchar(64) DEFAULT NULL COMMENT '附件类型',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='供应商附件信息表';


-- newgwuomp_dev.uomp_supplier_management definition

CREATE TABLE `uomp_supplier_management` (
  `ID` varchar(64) NOT NULL,
  `SUPPLIER_NAME` varchar(300) DEFAULT NULL COMMENT '供应商名称',
  `CREDIT_CODE` varchar(20) DEFAULT NULL COMMENT '统一社会信用代码',
  `SHORT_NAME` varchar(200) DEFAULT NULL COMMENT '简称',
  `TEL` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `RESP_NAME` varchar(200) DEFAULT NULL COMMENT '负责人',
  `RESP_TEL` varchar(20) DEFAULT NULL COMMENT '负责人电话',
  `SUPPLIER_TYPE` varchar(40) DEFAULT NULL COMMENT '类型',
  `SUPPLIER_STATUS` varchar(10) DEFAULT NULL COMMENT '状态',
  `REGISTER_PROVINCE` varchar(10) DEFAULT NULL,
  `REGISTER_CITY` varchar(10) DEFAULT NULL,
  `REGISTER_REGIN` varchar(10) DEFAULT NULL,
  `REGISTER_ADDRESS` varchar(2000) DEFAULT NULL COMMENT '注册地址',
  `CONTACT_ADDRESS` varchar(2000) DEFAULT NULL COMMENT '联系地址',
  `REMARK` varchar(4000) DEFAULT NULL COMMENT '备注',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人机构ID',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新人机构ID',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `ENTRY_TIME` timestamp NULL DEFAULT NULL COMMENT '录入时间',
  `ENTRY_ID` varchar(64) DEFAULT NULL COMMENT '录入人id',
  `ENTRY_NAME` varchar(200) DEFAULT NULL COMMENT '录入人名称',
  `GROUP_ID` varchar(64) DEFAULT NULL COMMENT '机构id',
  `USED_NAME` varchar(2000) DEFAULT NULL COMMENT '曾用名',
  `START_COOPERATION_TIME` timestamp NULL DEFAULT NULL COMMENT '开始合作日期',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='供应商管理基础信息表';


-- newgwuomp_dev.uomp_supplier_qualification definition

CREATE TABLE `uomp_supplier_qualification` (
  `ID` varchar(64) NOT NULL,
  `SUPPLIER_MANAGEMENT_ID` varchar(64) DEFAULT NULL COMMENT '供应商管理主键',
  `CERTIFICATE_NAME` varchar(300) DEFAULT NULL COMMENT '证书名称',
  `CERTIFICATE_NUM` varchar(100) DEFAULT NULL COMMENT '证书编号',
  `ISSUING_AUTHORITY` varchar(300) DEFAULT NULL COMMENT '发证机构',
  `ISSUING_DATE` varchar(100) DEFAULT NULL COMMENT '发证日期',
  `END_TIME` varchar(100) DEFAULT NULL COMMENT '有效期至',
  `FILES` text COMMENT '证书附件',
  `STANDARDS` varchar(300) DEFAULT NULL COMMENT '符合标准',
  `STATEMENT` varchar(300) DEFAULT NULL COMMENT '适用性声明',
  `BUSINESS_AREA` varchar(600) DEFAULT NULL COMMENT '业务领域',
  `ASSESSMENT_LEVEL` varchar(30) DEFAULT NULL COMMENT '评估等级',
  `BUSINESS_LINE` varchar(300) DEFAULT NULL COMMENT '业务种类',
  `QUALIFICATION_LEVEL` varchar(30) DEFAULT NULL COMMENT '资质等级',
  `TRIAL_AREA` varchar(300) DEFAULT NULL COMMENT '试用地域',
  `CERTIFICATION_SUB_ITEM` varchar(600) DEFAULT NULL COMMENT '认证分项',
  `OTHER_NAME` varchar(300) DEFAULT NULL COMMENT '其他证书名称',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人机构ID',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '更新人机构ID',
  `DEL_FLAG` varchar(2) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='供应商资质证书表';


-- newgwuomp_dev.uomp_team definition

CREATE TABLE `uomp_team` (
  `ID` varchar(64) NOT NULL,
  `NAME` varchar(512) DEFAULT NULL COMMENT '业务组名称',
  `ORGID` varchar(64) DEFAULT NULL COMMENT '归属单位',
  `TEAM_TYPE` varchar(32) DEFAULT NULL COMMENT '组类型',
  `DESCRIBE` varchar(2048) DEFAULT NULL COMMENT '描述',
  `STATUS` varchar(2) DEFAULT NULL COMMENT '状态',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `UPDATE_BY` varchar(32) DEFAULT NULL COMMENT '修改人员ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `DEL_FLAG` varchar(1) DEFAULT '0' COMMENT '删除标志(0:无效，1:有效)',
  `ORG_NAME` varchar(256) DEFAULT NULL COMMENT '归属单位名称',
  `TEAM_LEADER` varchar(256) DEFAULT NULL COMMENT '组领导',
  `TEAM_LEADER_ID` varchar(32) DEFAULT NULL COMMENT '组领导ID',
  `TEAM_RELATIONS` varchar(4000) DEFAULT NULL COMMENT '授权组',
  `GROUP_LEADERS` varchar(1024) DEFAULT NULL COMMENT '组长',
  `GROUP_LEADER_IDS` varchar(2048) DEFAULT NULL COMMENT '组长ids',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='业务组';


-- newgwuomp_dev.uomp_team_risk definition

CREATE TABLE `uomp_team_risk` (
  `id` varchar(64) NOT NULL,
  `user_id` varchar(64) DEFAULT NULL,
  `config_id` varchar(64) DEFAULT NULL,
  `risk_level` varchar(8) DEFAULT NULL,
  `change_level` varchar(8) DEFAULT NULL,
  `reason_` varchar(1024) DEFAULT NULL,
  `attach_` text,
  `update_by` varchar(64) DEFAULT NULL,
  `update_user` varchar(255) DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `config_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


-- newgwuomp_dev.uomp_team_risk_config definition

CREATE TABLE `uomp_team_risk_config` (
  `id` varchar(64) NOT NULL,
  `name_` varchar(255) DEFAULT NULL COMMENT '风险指标名称',
  `type_` varchar(8) DEFAULT NULL COMMENT '风险类型',
  `factor_` varchar(255) DEFAULT NULL COMMENT '风险因素',
  `level_` varchar(8) DEFAULT NULL COMMENT '风险等级',
  `rule_` varchar(1024) DEFAULT NULL COMMENT '风险判断规则',
  `status_` char(2) DEFAULT '1' COMMENT '状态',
  `del_flag` char(1) DEFAULT '0' COMMENT '逻辑删除标记 0-有效 1-无效',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人id',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人id',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


-- newgwuomp_dev.uomp_temp_admission definition

CREATE TABLE `uomp_temp_admission` (
  `ID` varchar(64) NOT NULL,
  `PERSON_NAME` varchar(200) DEFAULT NULL COMMENT '姓名',
  `PERSON_CARD` varchar(256) DEFAULT NULL COMMENT '身份证号',
  `TEL` varchar(15) DEFAULT NULL COMMENT '联系方式',
  `WORKING_COMPANY` varchar(300) DEFAULT NULL COMMENT '就职公司',
  `PLAN_VISIT_TIME` timestamp NULL DEFAULT NULL COMMENT '预计到访时间',
  `REAL_VISIT_TIME` timestamp NULL DEFAULT NULL COMMENT '实际到访时间',
  `DEST_CLERK_ID` varchar(64) DEFAULT NULL COMMENT '申请人id（原-接待人id）',
  `DEST_CLERK_NAME` varchar(200) DEFAULT NULL COMMENT '申请人（原-接待人）',
  `EXIT_TIME` timestamp NULL DEFAULT NULL COMMENT '离场时间',
  `JOB_CONTENT` varchar(3000) DEFAULT NULL COMMENT '工作内容',
  `MANAGER_COMMENT` varchar(1500) DEFAULT NULL COMMENT '运维经理意见',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态 0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `APPLY_CODE` varchar(64) DEFAULT NULL COMMENT '编号',
  `APPLY_TITLE` varchar(60) DEFAULT NULL COMMENT '标题',
  `BLACKLIST` varchar(10) DEFAULT NULL COMMENT '黑名单 0-非黑名单 1-是黑名单',
  `BLACKLIST_REASON` varchar(300) DEFAULT NULL COMMENT '加入黑名单原因',
  `BASE_ID` varchar(64) DEFAULT NULL COMMENT '申请基础表id',
  `ACCEPT_NAME` varchar(200) DEFAULT NULL COMMENT '接待人',
  `SEX` varchar(100) DEFAULT '0' COMMENT '0: 男,  1:女',
  `APPLICAT_DUTY` varchar(100) DEFAULT NULL COMMENT '职务',
  `WORKING_COMPANY_JSON` varchar(300) DEFAULT NULL COMMENT '就职公司 json',
  `ENGAGEMENT_PROJECT_ID` varchar(1000) DEFAULT NULL COMMENT '应用系统id',
  `ENGAGEMENT_PROJECT_JSON` varchar(4000) DEFAULT NULL COMMENT '应用系统json',
  `FILING_STATUS` varchar(100) DEFAULT NULL COMMENT '备案状态 0:未备案, 1:已备案',
  `ENGAGEMENT_PROJECT` varchar(3000) DEFAULT NULL COMMENT '应用系统',
  `WORKING_COMPANY_ID` varchar(300) DEFAULT NULL COMMENT '就职公司 id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='临时入场申请表';


-- newgwuomp_dev.uomp_temp_admission_base definition

CREATE TABLE `uomp_temp_admission_base` (
  `ID` varchar(64) NOT NULL,
  `APPLY_CODE` varchar(64) DEFAULT NULL COMMENT '编号',
  `APPLY_TITLE` varchar(200) DEFAULT NULL COMMENT '标题',
  `DEST_CLERK_ID` varchar(64) DEFAULT NULL COMMENT '申请人id（原-接待人id）',
  `DEST_CLERK_NAME` varchar(200) DEFAULT NULL COMMENT '申请人（原-接待人）',
  `PLAN_VISIT_TIME` timestamp NULL DEFAULT NULL COMMENT '预计到访时间',
  `JOB_CONTENT` varchar(3000) DEFAULT NULL COMMENT '工作内容',
  `INST_ID` varchar(64) DEFAULT NULL COMMENT '流程实例id',
  `APPLY_STATUS` varchar(10) DEFAULT NULL COMMENT '审核状态0-暂存 1-审核中 2-审核通过 3-审核不通过',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(1) DEFAULT NULL COMMENT '逻辑删除标记 0-有效 1-无效',
  `ACCEPT_NAME` varchar(200) DEFAULT NULL COMMENT '接待人',
  `APPLY_TIME` timestamp NULL DEFAULT NULL COMMENT '申请时间',
  `ENGAGEMENT_PROJECT_ID` varchar(1000) DEFAULT NULL COMMENT '应用系统id',
  `ENGAGEMENT_PROJECT_JSON` varchar(4000) DEFAULT NULL COMMENT '应用系统json',
  `FILING_STATUS` varchar(100) DEFAULT NULL COMMENT '备案状态 0:未备案, 1:已备案',
  `ENGAGEMENT_PROJECT` varchar(3000) DEFAULT NULL COMMENT '应用系统',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='临时入场申请基础信息表';


-- newgwuomp_dev.uomp_training_record definition

CREATE TABLE `uomp_training_record` (
  `ID` varchar(64) NOT NULL,
  `TRAINING_NAME` varchar(256) DEFAULT NULL COMMENT '培训名称',
  `PROJECT_NAME` varchar(512) DEFAULT NULL COMMENT '项目名称',
  `TRAINING_MODE` varchar(256) DEFAULT NULL COMMENT '培训方式',
  `CONFERENCE_NUM` varchar(16) DEFAULT NULL COMMENT '会议号',
  `TRAINING_BEGIN_TIME` timestamp NULL DEFAULT NULL COMMENT '培训开始日期',
  `TRAINING_END_TIME` timestamp NULL DEFAULT NULL COMMENT '培训结束日期',
  `TRAINING_TEACHER` varchar(256) DEFAULT NULL COMMENT '培训讲师',
  `TRAINING_PLAN_ID` varchar(64) DEFAULT NULL COMMENT '培训计划id',
  `TRAINING_CONTENT` varchar(3000) DEFAULT NULL COMMENT '培训内容',
  `TRAINING_SITE` varchar(256) DEFAULT NULL COMMENT '培训地点',
  `SIGN_IN_NUM` varchar(8) DEFAULT NULL COMMENT '签到人数',
  `FILE_INFO` longtext COMMENT '附件信息json',
  `CREATE_BY` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `CREATE_TIME` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `CREATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '创建人组织',
  `UPDATE_BY` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `UPDATE_TIME` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `UPDATE_ORG_ID` varchar(64) DEFAULT NULL COMMENT '修改人组织',
  `DEL_FLAG` varchar(2) DEFAULT '0' COMMENT '删除标识（0 正常 1删除）',
  `TRAINING_PLAN_NAME` varchar(300) DEFAULT NULL COMMENT '培训计划名称',
  `TRAINING_DURATION` varchar(16) DEFAULT NULL COMMENT '培训时长',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='培训记录表';


-- newgwuomp_dev.uomp_training_record_person definition

CREATE TABLE `uomp_training_record_person` (
  `ID` varchar(64) NOT NULL,
  `TRAINING_RECORD_ID` varchar(64) DEFAULT NULL COMMENT '培训记录id',
  `TRAINING_PERSON_ID` varchar(64) DEFAULT NULL COMMENT '参会人员id',
  `TRAINING_PERSON_NAME` varchar(300) DEFAULT NULL COMMENT '参会人员名称',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='培训记录参会人员信息表';