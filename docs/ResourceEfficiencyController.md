# 资源效能规则管理 API 文档

## 概述
资源效能规则管理模块提供了对系统资源效能规则的完整管理功能，包括规则的创建、查询、修改、删除等操作。

## 基础路径
```
/module/efficiency/rule
```

## API 接口

### 1. 获取规则列表
**接口地址：** `POST /module/efficiency/rule/list`

**请求参数：**
- PageQuery: 分页参数
- ResourceEfficiencyDTO: 查询条件

**请求示例：**
```json
{
  "pageQuery": {
    "page": 1,
    "limit": 10
  },
  "rule": {
    "ruleName": "测试规则",
    "startTime": "09:00",
    "endTime": "18:00"
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": "rule001",
        "ruleName": "测试规则",
        "cpuLowThreshold": 10,
        "cpuHighThreshold": 80,
        "memoryLowThreshold": 10,
        "memoryHighThreshold": 85,
        "diskLowThreshold": 10,
        "diskHighThreshold": 90,
        "startTime": "09:00",
        "endTime": "18:00",
        "highRate": 30,
        "lowRate": 50,
        "serverGroups": "[\"group1\",\"group2\"]"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取规则详情
**接口地址：** `GET /module/efficiency/rule/get/{id}`

**路径参数：**
- id: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": "rule001",
    "ruleName": "测试规则",
    "cpuLowThreshold": 10,
    "cpuHighThreshold": 80,
    "memoryLowThreshold": 10,
    "memoryHighThreshold": 85,
    "diskLowThreshold": 10,
    "diskHighThreshold": 90,
    "startTime": "09:00",
    "endTime": "18:00",
    "highRate": 30,
    "lowRate": 50,
    "serverGroups": "[\"group1\",\"group2\"]",
    "serverGroupList": ["group1", "group2"]
  }
}
```

### 3. 保存规则
**接口地址：** `POST /module/efficiency/rule/save`

**请求参数：**
```json
{
  "ruleName": "新规则",
  "cpuLowThreshold": 10,
  "cpuHighThreshold": 80,
  "memoryLowThreshold": 10,
  "memoryHighThreshold": 85,
  "diskLowThreshold": 10,
  "diskHighThreshold": 90,
  "startTime": "09:00",
  "endTime": "18:00",
  "highRate": 30,
  "lowRate": 50,
  "serverGroupList": ["group1", "group2"]
}
```

**响应结果：**
```json
{
  "success": true,
  "data": "rule002",
  "msg": "保存成功"
}
```

### 4. 删除规则
**接口地址：** `DELETE /module/efficiency/rule/delete/{id}`

**路径参数：**
- id: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功"
}
```

### 5. 启用规则
**接口地址：** `POST /module/efficiency/rule/enable/{id}`

**路径参数：**
- id: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": "启用成功"
}
```

### 6. 停用规则
**接口地址：** `POST /module/efficiency/rule/disable/{id}`

**路径参数：**
- id: 规则ID

**响应结果：**
```json
{
  "success": true,
  "data": "停用成功"
}
```

### 7. 复制规则
**接口地址：** `POST /module/efficiency/rule/copy`

**请求参数：**
- sourceId: 源规则ID
- newName: 新规则名称

**响应结果：**
```json
{
  "success": true,
  "data": "rule003",
  "msg": "复制成功"
}
```

### 8. 获取启用的规则列表
**接口地址：** `GET /module/efficiency/rule/enabled`

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "rule001",
      "ruleName": "测试规则",
      "cpuLowThreshold": 10,
      "cpuHighThreshold": 80
    }
  ]
}
```

### 9. 验证规则名称
**接口地址：** `GET /module/efficiency/rule/validate/name`

**请求参数：**
- ruleName: 规则名称
- excludeId: 排除的规则ID（可选）

**响应结果：**
```json
{
  "success": true,
  "data": true
}
```

### 10. 批量删除规则
**接口地址：** `POST /module/efficiency/rule/batch/delete`

**请求参数：**
```json
["rule001", "rule002", "rule003"]
```

**响应结果：**
```json
{
  "success": true,
  "data": "成功删除 3 条记录"
}
```

## 数据模型

### ResourceEfficiencyDTO
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 规则ID |
| ruleName | String | 规则名称 |
| cpuLowThreshold | Integer | CPU低阈值 |
| cpuHighThreshold | Integer | CPU高阈值 |
| memoryLowThreshold | Integer | 内存低阈值 |
| memoryHighThreshold | Integer | 内存高阈值 |
| diskLowThreshold | Integer | 磁盘低阈值 |
| diskHighThreshold | Integer | 磁盘高阈值 |
| startTime | String | 有效业务段开始时间 |
| endTime | String | 有效业务段结束时间 |
| highRate | Integer | 任意高负荷阈值累计占比 |
| lowRate | Integer | 任意低负荷阈值累计占比 |
| serverGroups | String | 服务器群组（JSON格式） |
| serverGroupList | List<String> | 服务器群组列表 |

## 错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 规则不存在 |
| 500 | 服务器内部错误 |
