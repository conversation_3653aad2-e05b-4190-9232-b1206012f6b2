# 资源能效结果管理 API 文档

## 概述
资源能效结果管理模块提供了对系统资源能效分析结果的管理功能，包括结果的查询、统计分析、报告生成等操作。

## 基础路径
```
/module/efficiency/result
```

## API 接口

### 1. 获取能效结果列表
**接口地址：** `POST /module/efficiency/result/list`

**请求参数：**
- PageQuery: 分页参数
- ResourceResultDTO: 查询条件

**请求示例：**
```json
{
  "pageQuery": {
    "page": 1,
    "limit": 10
  },
  "result": {
    "ruleId": "rule001",
    "ciName": "服务器01",
    "startTime": "2024-01-01",
    "endTime": "2024-01-31"
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": "result001",
        "ruleId": "rule001",
        "cInstId": "server001",
        "ciName": "服务器01",
        "ipAddress": "*************",
        "applicationName": "应用A",
        "cpuHighThreshold": "否",
        "cpuLowThreshold": "否",
        "memoryHighThreshold": "是",
        "memoryLowThreshold": "否",
        "diskHighThreshold": "否",
        "diskLowThreshold": "否",
        "cpuDays": 0,
        "memoryDays": 5,
        "diskDays": 0,
        "processDate": "2024-01-01",
        "overallStatus": "高负荷",
        "recommendation": "内存使用率过高，建议优化内存使用或增加内存资源"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取结果详情
**接口地址：** `GET /module/efficiency/result/get/{id}`

**路径参数：**
- id: 结果ID

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": "result001",
    "ruleId": "rule001",
    "cInstId": "server001",
    "ciName": "服务器01",
    "ipAddress": "*************",
    "applicationName": "应用A",
    "cpuHighThreshold": "否",
    "cpuLowThreshold": "否",
    "memoryHighThreshold": "是",
    "memoryLowThreshold": "否",
    "diskHighThreshold": "否",
    "diskLowThreshold": "否",
    "cpuDays": 0,
    "memoryDays": 5,
    "diskDays": 0,
    "processDate": "2024-01-01",
    "overallStatus": "高负荷",
    "recommendation": "内存使用率过高，建议优化内存使用或增加内存资源"
  }
}
```

### 3. 保存结果数据
**接口地址：** `POST /module/efficiency/result/save`

**请求参数：**
```json
{
  "ruleId": "rule001",
  "cInstId": "server001",
  "ciName": "服务器01",
  "ipAddress": "*************",
  "applicationName": "应用A",
  "cpuHighThreshold": "否",
  "cpuLowThreshold": "否",
  "memoryHighThreshold": "是",
  "memoryLowThreshold": "否",
  "diskHighThreshold": "否",
  "diskLowThreshold": "否",
  "cpuDays": 0,
  "memoryDays": 5,
  "diskDays": 0,
  "processDate": "2024-01-01"
}
```

**响应结果：**
```json
{
  "success": true,
  "data": "result002",
  "msg": "保存成功"
}
```

### 4. 批量保存结果数据
**接口地址：** `POST /module/efficiency/result/batch/save`

**请求参数：**
```json
[
  {
    "ruleId": "rule001",
    "cInstId": "server001",
    "ciName": "服务器01",
    "cpuHighThreshold": "否",
    "memoryHighThreshold": "是",
    "diskHighThreshold": "否",
    "processDate": "2024-01-01"
  },
  {
    "ruleId": "rule001",
    "cInstId": "server002",
    "ciName": "服务器02",
    "cpuHighThreshold": "是",
    "memoryHighThreshold": "否",
    "diskHighThreshold": "否",
    "processDate": "2024-01-01"
  }
]
```

**响应结果：**
```json
{
  "success": true,
  "data": "批量保存成功，共 2 条记录"
}
```

### 5. 根据规则ID和处理日期查询
**接口地址：** `GET /module/efficiency/result/rule/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- processDate: 处理日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "result001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "cpuHighThreshold": "否",
      "memoryHighThreshold": "是",
      "diskHighThreshold": "否",
      "processDate": "2024-01-01"
    }
  ]
}
```

### 6. 根据资源ID和时间范围查询
**接口地址：** `GET /module/efficiency/result/resource/{cInstId}`

**路径参数：**
- cInstId: 资源ID

**请求参数：**
- startDate: 开始日期（yyyy-MM-dd）
- endDate: 结束日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "result001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "cpuHighThreshold": "否",
      "memoryHighThreshold": "是",
      "diskHighThreshold": "否",
      "processDate": "2024-01-01"
    }
  ]
}
```

### 7. 获取能效统计报告
**接口地址：** `GET /module/efficiency/result/report/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- startDate: 开始日期（yyyy-MM-dd）
- endDate: 结束日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": {
    "totalCount": 100,
    "highLoadCount": 15,
    "lowLoadCount": 25,
    "normalCount": 60,
    "highLoadRate": 0.15,
    "lowLoadRate": 0.25,
    "normalRate": 0.60,
    "avgCpuDays": 2.5,
    "avgMemoryDays": 3.2,
    "avgDiskDays": 1.8
  }
}
```

### 8. 获取异常资源统计
**接口地址：** `GET /module/efficiency/result/abnormal/count/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- processDate: 处理日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": 15
}
```

### 9. 获取高负荷资源列表
**接口地址：** `GET /module/efficiency/result/high-load/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- processDate: 处理日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "result001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "cpuHighThreshold": "是",
      "memoryHighThreshold": "是",
      "diskHighThreshold": "否",
      "processDate": "2024-01-01"
    }
  ]
}
```

### 10. 获取低负荷资源列表
**接口地址：** `GET /module/efficiency/result/low-load/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- processDate: 处理日期（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "result002",
      "cInstId": "server002",
      "ciName": "服务器02",
      "cpuLowThreshold": "是",
      "memoryLowThreshold": "是",
      "diskLowThreshold": "是",
      "processDate": "2024-01-01"
    }
  ]
}
```

### 11. 清理历史数据
**接口地址：** `POST /module/efficiency/result/clean`

**请求参数：**
- beforeDate: 日期点（yyyy-MM-dd）

**响应结果：**
```json
{
  "success": true,
  "data": "清理完成，共删除 500 条记录"
}
```

### 12. 删除结果数据
**接口地址：** `DELETE /module/efficiency/result/delete/{id}`

**路径参数：**
- id: 结果ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功"
}
```

## 数据模型

### ResourceResultDTO
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 结果ID |
| relationId | String | 关系表ID |
| ruleId | String | 规则ID |
| serverGroup | String | 服务器群组 |
| cInstId | String | 资源ID |
| ciName | String | 资源名称 |
| ipAddress | String | 资源IP |
| applicationName | String | 关联应用名称 |
| applicationId | String | 关联应用ID |
| cpuHighThreshold | String | CPU是否是高负荷 |
| cpuLowThreshold | String | CPU是否是低负荷 |
| memoryHighThreshold | String | 内存是否是高负荷 |
| memoryLowThreshold | String | 内存是否是低负荷 |
| diskHighThreshold | String | 磁盘是否是高负荷 |
| diskLowThreshold | String | 磁盘是否是低负荷 |
| cpuDays | Integer | CPU高负荷累计天数 |
| memoryDays | Integer | 内存高负荷累计天数 |
| diskDays | Integer | 磁盘高负荷累计天数 |
| processDate | Date | 数据处理时间 |
| overallStatus | String | 综合负荷状态 |
| recommendation | String | 建议操作 |

## 错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 数据不存在 |
| 500 | 服务器内部错误 |
