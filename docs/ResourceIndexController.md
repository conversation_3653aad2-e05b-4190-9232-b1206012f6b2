# 资源指标数据管理 API 文档

## 概述
资源指标数据管理模块提供了对系统资源指标数据的管理功能，包括数据的查询、保存、统计分析等操作。

## 基础路径
```
/module/efficiency/index
```

## API 接口

### 1. 获取指标数据列表
**接口地址：** `POST /module/efficiency/index/list`

**请求参数：**
- PageQuery: 分页参数
- ResourceIndexDTO: 查询条件

**请求示例：**
```json
{
  "pageQuery": {
    "page": 1,
    "limit": 10
  },
  "index": {
    "ruleId": "rule001",
    "ciName": "服务器01",
    "startTime": "2024-01-01 00:00:00",
    "endTime": "2024-01-01 23:59:59"
  }
}
```

**响应结果：**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": "index001",
        "ruleId": "rule001",
        "cInstId": "server001",
        "ciName": "服务器01",
        "ipAddress": "*************",
        "cpuRate": 75.5,
        "memoryRate": 68.2,
        "diskRate": 45.8,
        "receiveTime": "2024-01-01 10:00:00",
        "sameDayStatus": "正常",
        "applicationName": "应用A"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取指标数据详情
**接口地址：** `GET /module/efficiency/index/get/{id}`

**路径参数：**
- id: 指标数据ID

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": "index001",
    "ruleId": "rule001",
    "cInstId": "server001",
    "ciName": "服务器01",
    "ipAddress": "*************",
    "cpuRate": 75.5,
    "memoryRate": 68.2,
    "diskRate": 45.8,
    "receiveTime": "2024-01-01 10:00:00",
    "sameDayStatus": "正常",
    "applicationName": "应用A",
    "collectionFreq": 5
  }
}
```

### 3. 保存指标数据
**接口地址：** `POST /module/efficiency/index/save`

**请求参数：**
```json
{
  "ruleId": "rule001",
  "cInstId": "server001",
  "ciName": "服务器01",
  "ipAddress": "*************",
  "cpuRate": 75.5,
  "memoryRate": 68.2,
  "diskRate": 45.8,
  "receiveTime": "2024-01-01 10:00:00",
  "sameDayStatus": "正常",
  "applicationName": "应用A",
  "collectionFreq": 5
}
```

**响应结果：**
```json
{
  "success": true,
  "data": "index002",
  "msg": "保存成功"
}
```

### 4. 批量保存指标数据
**接口地址：** `POST /module/efficiency/index/batch/save`

**请求参数：**
```json
[
  {
    "ruleId": "rule001",
    "cInstId": "server001",
    "ciName": "服务器01",
    "cpuRate": 75.5,
    "memoryRate": 68.2,
    "diskRate": 45.8,
    "receiveTime": "2024-01-01 10:00:00"
  },
  {
    "ruleId": "rule001",
    "cInstId": "server002",
    "ciName": "服务器02",
    "cpuRate": 65.3,
    "memoryRate": 72.1,
    "diskRate": 38.9,
    "receiveTime": "2024-01-01 10:00:00"
  }
]
```

**响应结果：**
```json
{
  "success": true,
  "data": "批量保存成功，共 2 条记录"
}
```

### 5. 根据规则ID和时间范围查询
**接口地址：** `GET /module/efficiency/index/rule/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- startTime: 开始时间（yyyy-MM-dd HH:mm:ss）
- endTime: 结束时间（yyyy-MM-dd HH:mm:ss）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "index001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "cpuRate": 75.5,
      "memoryRate": 68.2,
      "diskRate": 45.8,
      "receiveTime": "2024-01-01 10:00:00"
    }
  ]
}
```

### 6. 根据资源ID和时间范围查询
**接口地址：** `GET /module/efficiency/index/resource/{cInstId}`

**路径参数：**
- cInstId: 资源ID

**请求参数：**
- startTime: 开始时间（yyyy-MM-dd HH:mm:ss）
- endTime: 结束时间（yyyy-MM-dd HH:mm:ss）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "index001",
      "ruleId": "rule001",
      "cInstId": "server001",
      "cpuRate": 75.5,
      "memoryRate": 68.2,
      "diskRate": 45.8,
      "receiveTime": "2024-01-01 10:00:00"
    }
  ]
}
```

### 7. 获取最新指标数据
**接口地址：** `GET /module/efficiency/index/latest/{cInstId}`

**路径参数：**
- cInstId: 资源ID

**响应结果：**
```json
{
  "success": true,
  "data": {
    "id": "index001",
    "ruleId": "rule001",
    "cInstId": "server001",
    "cpuRate": 75.5,
    "memoryRate": 68.2,
    "diskRate": 45.8,
    "receiveTime": "2024-01-01 10:00:00"
  }
}
```

### 8. 获取资源使用率统计
**接口地址：** `GET /module/efficiency/index/statistics/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- startTime: 开始时间（yyyy-MM-dd HH:mm:ss）
- endTime: 结束时间（yyyy-MM-dd HH:mm:ss）

**响应结果：**
```json
{
  "success": true,
  "data": {
    "avgCpuRate": 72.5,
    "avgMemoryRate": 65.8,
    "avgDiskRate": 42.3,
    "maxCpuRate": 95.2,
    "maxMemoryRate": 88.7,
    "maxDiskRate": 78.9,
    "minCpuRate": 15.3,
    "minMemoryRate": 22.1,
    "minDiskRate": 8.5,
    "totalCount": 1440
  }
}
```

### 9. 获取异常资源列表
**接口地址：** `GET /module/efficiency/index/abnormal/{ruleId}`

**路径参数：**
- ruleId: 规则ID

**请求参数：**
- startTime: 开始时间（yyyy-MM-dd HH:mm:ss）
- endTime: 结束时间（yyyy-MM-dd HH:mm:ss）

**响应结果：**
```json
{
  "success": true,
  "data": [
    {
      "id": "index001",
      "cInstId": "server001",
      "ciName": "服务器01",
      "cpuRate": 95.5,
      "memoryRate": 88.2,
      "diskRate": 85.8,
      "receiveTime": "2024-01-01 10:00:00",
      "sameDayStatus": "异常"
    }
  ]
}
```

### 10. 清理历史数据
**接口地址：** `POST /module/efficiency/index/clean`

**请求参数：**
- beforeTime: 时间点（yyyy-MM-dd HH:mm:ss）

**响应结果：**
```json
{
  "success": true,
  "data": "清理完成，共删除 1000 条记录"
}
```

### 11. 删除指标数据
**接口地址：** `DELETE /module/efficiency/index/delete/{id}`

**路径参数：**
- id: 指标数据ID

**响应结果：**
```json
{
  "success": true,
  "data": "删除成功"
}
```

## 数据模型

### ResourceIndexDTO
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 指标数据ID |
| ruleId | String | 规则ID |
| serverGroup | String | 服务器群组 |
| cInstId | String | 资源ID |
| ciName | String | 资源名称 |
| ipAddress | String | 资源IP |
| cpuRate | Float | CPU使用率 |
| memoryRate | Float | 内存使用率 |
| diskRate | Float | 磁盘使用率 |
| receiveTime | Date | 接收时间 |
| sendTime | Date | 发送时间 |
| sameDayStatus | String | 当日状态 |
| applicationName | String | 应用名称 |
| collectionFreq | Integer | 采集频率 |

## 错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 数据不存在 |
| 500 | 服务器内部错误 |
